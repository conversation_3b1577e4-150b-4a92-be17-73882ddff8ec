"""
搬砖数据访问对象

提供CS2饰品搬砖数据的数据库操作，支持多条件查询和批量导入。
"""

from typing import List, Optional, Dict, Any, Tuple
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, or_, desc, asc, func
from loguru import logger

from .base_dao import BaseDAO
from ..models.arbitrage_item import ArbitrageItem
from ..config.database import get_db_session


class ArbitrageItemDAO(BaseDAO[ArbitrageItem]):
    """搬砖数据DAO"""
    
    def __init__(self):
        super().__init__(ArbitrageItem)
    
    def get_by_item_id(self, item_id: str) -> List[ArbitrageItem]:
        """根据关联的item_id获取所有搬砖记录"""
        try:
            with get_db_session() as session:
                results = session.query(ArbitrageItem).filter(
                    ArbitrageItem.item_id == item_id
                ).all()

                # 分离所有对象避免Session绑定问题
                for result in results:
                    session.expunge(result)

                return results
        except SQLAlchemyError as e:
            self.logger.error(f"根据item_id获取记录失败: {e}")
            raise
    
    def batch_import(self, items_data: List[Dict[str, Any]],
                    replace_existing: bool = True) -> int:
        """批量导入搬砖数据 - 实现upsert功能（有就更新，没有就新增）"""
        try:
            with get_db_session() as session:
                imported_count = 0
                updated_count = 0

                for item_data in items_data:
                    # 使用market_hash_name作为唯一标识来查找现有记录
                    market_hash_name = item_data.get('market_hash_name')
                    if not market_hash_name:
                        # 如果没有market_hash_name，跳过这条记录
                        self.logger.warning(f"记录缺少market_hash_name，跳过: {item_data.get('market_name', 'Unknown')}")
                        continue

                    # 查找现有记录
                    existing_item = session.query(ArbitrageItem).filter(
                        ArbitrageItem.market_hash_name == market_hash_name
                    ).first()

                    if existing_item:
                        # 更新现有记录
                        for key, value in item_data.items():
                            if hasattr(existing_item, key):
                                setattr(existing_item, key, value)
                        updated_count += 1
                        self.logger.debug(f"更新记录: {market_hash_name}")
                    else:
                        # 创建新记录
                        new_item = ArbitrageItem(**item_data)
                        session.add(new_item)
                        imported_count += 1
                        self.logger.debug(f"新增记录: {market_hash_name}")

                session.flush()
                self.logger.info(f"批量导入完成: 新增{imported_count}条记录, 更新{updated_count}条记录")
                return imported_count + updated_count

        except SQLAlchemyError as e:
            self.logger.error(f"批量导入失败: {e}")
            raise
    
    def find_by_conditions(self,
                          market_name: Optional[str] = None,
                          goods_type: Optional[str] = None,
                          goods_level: Optional[str] = None,
                          steam_price_min: Optional[Decimal] = None,
                          steam_price_max: Optional[Decimal] = None,
                          steam_buy_price_min: Optional[Decimal] = None,
                          steam_buy_price_max: Optional[Decimal] = None,
                          steam_sell_count_min: Optional[int] = None,
                          steam_sell_count_max: Optional[int] = None,
                          steam_buy_count_min: Optional[int] = None,
                          steam_buy_count_max: Optional[int] = None,
                          youpin_price_min: Optional[Decimal] = None,
                          youpin_price_max: Optional[Decimal] = None,
                          youpin_purchase_price_min: Optional[Decimal] = None,
                          youpin_purchase_price_max: Optional[Decimal] = None,
                          youpin_sell_count_min: Optional[int] = None,
                          youpin_sell_count_max: Optional[int] = None,
                          youpin_purchase_count_min: Optional[int] = None,
                          youpin_purchase_count_max: Optional[int] = None,
                          profit_rate_min: Optional[Decimal] = None,
                          profit_rate_max: Optional[Decimal] = None,
                          platform: Optional[str] = None,
                          limit: int = 500000,  # 参考其他页面，设置合理的默认限制
                          offset: int = 0,
                          order_by: str = 'youpin_profit_rate',
                          order_desc: bool = True) -> Tuple[List[ArbitrageItem], int]:
        """多条件查询搬砖数据"""
        try:
            with get_db_session() as session:
                query = session.query(ArbitrageItem)
                
                # 构建查询条件
                conditions = []
                
                # 饰品名称模糊搜索
                if market_name:
                    conditions.append(ArbitrageItem.market_name.like(f"%{market_name}%"))
                
                # 武器类型
                if goods_type:
                    conditions.append(ArbitrageItem.goods_type01_name == goods_type)
                
                # 品质等级
                if goods_level:
                    conditions.append(ArbitrageItem.goods_level_name == goods_level)
                
                # Steam价格范围
                if steam_price_min is not None:
                    conditions.append(ArbitrageItem.steam_price >= steam_price_min)
                if steam_price_max is not None:
                    conditions.append(ArbitrageItem.steam_price <= steam_price_max)

                # Steam求购价范围
                if steam_buy_price_min is not None:
                    conditions.append(ArbitrageItem.steam_buy_price >= steam_buy_price_min)
                if steam_buy_price_max is not None:
                    conditions.append(ArbitrageItem.steam_buy_price <= steam_buy_price_max)

                # Steam在售量范围
                if steam_sell_count_min is not None:
                    conditions.append(ArbitrageItem.steam_sell_count >= steam_sell_count_min)
                if steam_sell_count_max is not None:
                    conditions.append(ArbitrageItem.steam_sell_count <= steam_sell_count_max)

                # Steam求购数范围
                if steam_buy_count_min is not None:
                    conditions.append(ArbitrageItem.steam_buy_count >= steam_buy_count_min)
                if steam_buy_count_max is not None:
                    conditions.append(ArbitrageItem.steam_buy_count <= steam_buy_count_max)

                # Youpin价格范围
                if youpin_price_min is not None:
                    conditions.append(ArbitrageItem.youpin_price >= youpin_price_min)
                if youpin_price_max is not None:
                    conditions.append(ArbitrageItem.youpin_price <= youpin_price_max)

                # Youpin求购价范围
                if youpin_purchase_price_min is not None:
                    conditions.append(ArbitrageItem.youpin_purchase_price >= youpin_purchase_price_min)
                if youpin_purchase_price_max is not None:
                    conditions.append(ArbitrageItem.youpin_purchase_price <= youpin_purchase_price_max)

                # Youpin在售数范围
                if youpin_sell_count_min is not None:
                    conditions.append(ArbitrageItem.youpin_sell_count >= youpin_sell_count_min)
                if youpin_sell_count_max is not None:
                    conditions.append(ArbitrageItem.youpin_sell_count <= youpin_sell_count_max)

                # Youpin求购数范围
                if youpin_purchase_count_min is not None:
                    conditions.append(ArbitrageItem.youpin_purchase_count >= youpin_purchase_count_min)
                if youpin_purchase_count_max is not None:
                    conditions.append(ArbitrageItem.youpin_purchase_count <= youpin_purchase_count_max)
                
                # 利润率范围
                if profit_rate_min is not None:
                    conditions.append(ArbitrageItem.youpin_profit_rate >= profit_rate_min)
                if profit_rate_max is not None:
                    conditions.append(ArbitrageItem.youpin_profit_rate <= profit_rate_max)
                
                # 平台筛选
                if platform == 'steam':
                    conditions.append(ArbitrageItem.steam_price.isnot(None))
                elif platform == 'youpin':
                    conditions.append(ArbitrageItem.youpin_price.isnot(None))
                elif platform == 'both':
                    conditions.append(and_(
                        ArbitrageItem.steam_price.isnot(None),
                        ArbitrageItem.youpin_price.isnot(None)
                    ))
                
                # 应用条件
                if conditions:
                    query = query.filter(and_(*conditions))
                
                # 获取总数
                total_count = query.count()
                
                # 排序
                if hasattr(ArbitrageItem, order_by):
                    order_column = getattr(ArbitrageItem, order_by)
                    if order_desc:
                        query = query.order_by(desc(order_column))
                    else:
                        query = query.order_by(asc(order_column))
                
                # 分页
                query = query.offset(offset).limit(limit)

                results = query.all()

                # 分离所有对象避免Session绑定问题
                for result in results:
                    session.expunge(result)

                self.logger.info(f"多条件查询完成: 找到{total_count}条记录，返回{len(results)}条")
                return results, total_count
                
        except SQLAlchemyError as e:
            self.logger.error(f"多条件查询失败: {e}")
            raise
    
    def find_arbitrage_opportunities(self, 
                                   min_profit_rate: Decimal = Decimal('0.1'),
                                   min_profit_amount: Optional[Decimal] = None,
                                   limit: int = 50) -> List[ArbitrageItem]:
        """查询搬砖机会"""
        try:
            with get_db_session() as session:
                query = session.query(ArbitrageItem).filter(
                    and_(
                        ArbitrageItem.steam_price.isnot(None),
                        ArbitrageItem.youpin_price.isnot(None),
                        ArbitrageItem.youpin_profit_rate >= min_profit_rate
                    )
                )
                
                if min_profit_amount is not None:
                    query = query.filter(ArbitrageItem.youpin_profit >= min_profit_amount)
                
                # 按利润率降序排列
                query = query.order_by(desc(ArbitrageItem.youpin_profit_rate))
                
                results = query.limit(limit).all()

                # 分离所有对象避免Session绑定问题
                for result in results:
                    session.expunge(result)

                self.logger.info(f"搬砖机会查询完成: 找到{len(results)}个机会")
                return results
                
        except SQLAlchemyError as e:
            self.logger.error(f"搬砖机会查询失败: {e}")
            raise
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取搬砖数据统计信息"""
        try:
            with get_db_session() as session:
                # 基础统计
                total_items = session.query(ArbitrageItem).count()
                
                # 有Steam价格的数量
                steam_items = session.query(ArbitrageItem).filter(
                    ArbitrageItem.steam_price.isnot(None)
                ).count()
                
                # 有Youpin价格的数量
                youpin_items = session.query(ArbitrageItem).filter(
                    ArbitrageItem.youpin_price.isnot(None)
                ).count()
                
                # 有搬砖机会的数量（利润率>10%）
                profitable_items = session.query(ArbitrageItem).filter(
                    ArbitrageItem.youpin_profit_rate >= 0.1
                ).count()
                
                # 平均利润率
                avg_profit_rate = session.query(
                    func.avg(ArbitrageItem.youpin_profit_rate)
                ).filter(
                    ArbitrageItem.youpin_profit_rate.isnot(None)
                ).scalar() or 0
                
                return {
                    'total_items': total_items,
                    'steam_items': steam_items,
                    'youpin_items': youpin_items,
                    'profitable_items': profitable_items,
                    'avg_profit_rate': float(avg_profit_rate),
                    'steam_coverage': steam_items / total_items if total_items > 0 else 0,
                    'youpin_coverage': youpin_items / total_items if total_items > 0 else 0,
                    'profitable_rate': profitable_items / total_items if total_items > 0 else 0
                }
                
        except SQLAlchemyError as e:
            self.logger.error(f"获取统计信息失败: {e}")
            raise
    
    def get_distinct_values(self, column_name: str) -> List[str]:
        """获取指定列的不重复值"""
        try:
            with get_db_session() as session:
                if not hasattr(ArbitrageItem, column_name):
                    raise ValueError(f"列 {column_name} 不存在")
                
                column = getattr(ArbitrageItem, column_name)
                results = session.query(column).distinct().filter(
                    column.isnot(None)
                ).all()
                
                return [result[0] for result in results if result[0]]
                
        except SQLAlchemyError as e:
            self.logger.error(f"获取不重复值失败: {e}")
            raise
    
    def delete_by_item_id(self, item_id: str) -> int:
        """删除指定item_id的所有搬砖数据"""
        try:
            with get_db_session() as session:
                deleted_count = session.query(ArbitrageItem).filter(
                    ArbitrageItem.item_id == item_id
                ).delete()

                self.logger.info(f"删除item_id={item_id}的数据: {deleted_count}条")
                return deleted_count

        except SQLAlchemyError as e:
            self.logger.error(f"删除item_id数据失败: {e}")
            raise
