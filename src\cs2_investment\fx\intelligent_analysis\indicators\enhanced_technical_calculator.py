"""
增强技术指标计算器

基于现有TechnicalIndicatorCalculator扩展，补充KDJ指标计算，
增加供需关系分析功能，实现多时间框架指标的统一计算。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import warnings
from loguru import logger

from ...syncps.technical_indicator_calculator import TechnicalIndicatorCalculator

warnings.filterwarnings('ignore')


class EnhancedTechnicalCalculator(TechnicalIndicatorCalculator):
    """增强技术指标计算器"""
    
    def __init__(self, daily_data: pd.DataFrame, weekly_data: pd.DataFrame = None, 
                 hourly_data: pd.DataFrame = None, supply_demand_data: Dict = None):
        """
        初始化增强技术指标计算器
        
        Args:
            daily_data: 日K数据
            weekly_data: 周K数据
            hourly_data: 时K数据
            supply_demand_data: 供需数据 {'3m': data, '6m': data}
        """
        # 调用父类初始化
        super().__init__(daily_data, weekly_data)
        
        # 扩展数据源
        self.hourly_data = hourly_data.copy() if hourly_data is not None else None
        self.supply_demand_data = supply_demand_data or {}
        
        # 缓存机制
        self.cache = {}
        self.cache_enabled = True
        
        # 日志记录
        self.logger = logger.bind(calculator=self.__class__.__name__)
        
        # 验证扩展数据
        self._validate_extended_data()
    
    def _validate_extended_data(self):
        """验证扩展数据完整性"""
        if self.hourly_data is not None:
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in self.hourly_data.columns:
                    self.logger.warning(f"时K数据缺少字段: {col}")
        
        if self.supply_demand_data:
            for source, data in self.supply_demand_data.items():
                if not isinstance(data, (list, pd.DataFrame)):
                    self.logger.warning(f"供需数据格式异常: {source}")
    
    def calculate_enhanced_indicators(self) -> Dict:
        """计算增强技术指标"""
        self.logger.info("开始计算增强技术指标...")
        
        # 首先计算基础指标
        base_indicators = self.calculate_all_indicators()
        
        # 计算KDJ指标
        kdj_data = self.calculate_kdj()
        base_indicators.update(kdj_data)
        
        # 计算多时间框架协调指标
        if self.hourly_data is not None:
            hourly_indicators = self._calculate_hourly_indicators()
            base_indicators.update(hourly_indicators)
        
        # 计算供需关系分析
        if self.supply_demand_data:
            supply_demand_analysis = self.analyze_supply_demand()
            base_indicators.update(supply_demand_analysis)
        
        # 计算增强波动率指标
        volatility_indicators = self._calculate_enhanced_volatility()
        base_indicators.update(volatility_indicators)
        
        self.logger.info("增强技术指标计算完成")
        return base_indicators
    
    def calculate_kdj(self, k_period: int = 9, d_period: int = 3, j_multiplier: int = 3) -> Dict:
        """
        计算KDJ指标
        
        KDJ计算公式：
        RSV = (收盘价 - N日内最低价) / (N日内最高价 - N日内最低价) * 100
        K = RSV的M日移动平均
        D = K的P日移动平均  
        J = 3K - 2D
        
        Args:
            k_period: K值计算周期，默认9
            d_period: D值计算周期，默认3
            j_multiplier: J值计算倍数，默认3
        """
        cache_key = f"kdj_{k_period}_{d_period}_{j_multiplier}"
        if self.cache_enabled and cache_key in self.cache:
            return self.cache[cache_key]
        
        try:
            prices = self.daily_data['close']
            highs = self.daily_data['high']
            lows = self.daily_data['low']
            
            # 计算RSV (Raw Stochastic Value)
            lowest_low = lows.rolling(window=k_period).min()
            highest_high = highs.rolling(window=k_period).max()
            
            # 避免除零错误
            rsv = np.where(
                (highest_high - lowest_low) != 0,
                (prices - lowest_low) / (highest_high - lowest_low) * 100,
                50  # 默认中位值
            )
            rsv = pd.Series(rsv, index=prices.index)
            
            # 计算K值 (RSV的移动平均)
            k_values = rsv.ewm(span=d_period).mean()
            
            # 计算D值 (K值的移动平均)
            d_values = k_values.ewm(span=d_period).mean()
            
            # 计算J值
            j_values = j_multiplier * k_values - (j_multiplier - 1) * d_values
            
            # 限制J值范围 (通常在-100到200之间)
            j_values = np.clip(j_values, -100, 200)
            
            kdj_result = {
                'kdj_k': k_values,
                'kdj_d': d_values,
                'kdj_j': j_values,
                'kdj_rsv': rsv
            }
            
            # 缓存结果
            if self.cache_enabled:
                self.cache[cache_key] = kdj_result
            
            return kdj_result
            
        except Exception as e:
            self.logger.error(f"KDJ指标计算失败: {e}")
            return {
                'kdj_k': pd.Series([np.nan] * len(self.daily_data)),
                'kdj_d': pd.Series([np.nan] * len(self.daily_data)),
                'kdj_j': pd.Series([np.nan] * len(self.daily_data)),
                'kdj_rsv': pd.Series([np.nan] * len(self.daily_data))
            }
    
    def get_kdj_signals(self) -> Dict:
        """获取KDJ信号"""
        kdj_data = self.calculate_kdj()
        
        if kdj_data['kdj_k'].empty:
            return {'kdj_signal': 'UNKNOWN', 'kdj_strength': 0}
        
        # 获取最新值
        latest_k = kdj_data['kdj_k'].iloc[-1]
        latest_d = kdj_data['kdj_d'].iloc[-1]
        latest_j = kdj_data['kdj_j'].iloc[-1]
        
        # 信号判断
        signal = 'NEUTRAL'
        strength = 0
        
        # 超买超卖判断
        if latest_k > 80 and latest_d > 80:
            signal = 'OVERBOUGHT'
            strength = min((latest_k + latest_d) / 2 - 80, 20) / 20 * 100
        elif latest_k < 20 and latest_d < 20:
            signal = 'OVERSOLD'
            strength = min(20 - (latest_k + latest_d) / 2, 20) / 20 * 100
        
        # 金叉死叉判断
        if len(kdj_data['kdj_k']) >= 2:
            prev_k = kdj_data['kdj_k'].iloc[-2]
            prev_d = kdj_data['kdj_d'].iloc[-2]
            
            if prev_k <= prev_d and latest_k > latest_d and latest_k < 80:
                signal = 'GOLDEN_CROSS'
                strength = 75
            elif prev_k >= prev_d and latest_k < latest_d and latest_k > 20:
                signal = 'DEATH_CROSS'
                strength = 75
        
        return {
            'kdj_signal': signal,
            'kdj_strength': strength,
            'kdj_k': latest_k,
            'kdj_d': latest_d,
            'kdj_j': latest_j
        }
    
    def _calculate_hourly_indicators(self) -> Dict:
        """计算时K指标"""
        if self.hourly_data is None or len(self.hourly_data) < 20:
            return {'hourly_trend': 'UNKNOWN'}
        
        try:
            hourly_prices = self.hourly_data['close']
            
            # 短期EMA
            hourly_ema_5 = hourly_prices.ewm(span=5).mean()
            hourly_ema_10 = hourly_prices.ewm(span=10).mean()
            
            # 短期趋势
            if len(hourly_ema_5) >= 2:
                if hourly_ema_5.iloc[-1] > hourly_ema_10.iloc[-1]:
                    hourly_trend = 'UP'
                else:
                    hourly_trend = 'DOWN'
            else:
                hourly_trend = 'UNKNOWN'
            
            # 短期波动率
            hourly_volatility = hourly_prices.pct_change().rolling(window=24).std() * 100
            
            return {
                'hourly_trend': hourly_trend,
                'hourly_ema_5': hourly_ema_5,
                'hourly_ema_10': hourly_ema_10,
                'hourly_volatility': hourly_volatility
            }
            
        except Exception as e:
            self.logger.error(f"时K指标计算失败: {e}")
            return {'hourly_trend': 'ERROR'}
    
    def _calculate_enhanced_volatility(self) -> Dict:
        """计算增强波动率指标"""
        try:
            prices = self.daily_data['close']
            
            # 历史波动率 (不同周期)
            volatility_5d = prices.pct_change().rolling(window=5).std() * np.sqrt(252) * 100
            volatility_20d = prices.pct_change().rolling(window=20).std() * np.sqrt(252) * 100
            volatility_60d = prices.pct_change().rolling(window=60).std() * np.sqrt(252) * 100
            
            # 波动率等级判断
            latest_vol_20d = volatility_20d.iloc[-1] if not volatility_20d.empty else 0
            
            if latest_vol_20d > 30:
                volatility_level = 'HIGH'
            elif latest_vol_20d > 15:
                volatility_level = 'MEDIUM'
            else:
                volatility_level = 'LOW'
            
            return {
                'volatility_5d': volatility_5d,
                'volatility_20d': volatility_20d,
                'volatility_60d': volatility_60d,
                'volatility_level': volatility_level
            }
            
        except Exception as e:
            self.logger.error(f"增强波动率计算失败: {e}")
            return {'volatility_level': 'UNKNOWN'}

    def analyze_supply_demand(self) -> Dict:
        """分析供需关系"""
        if not self.supply_demand_data:
            return {'supply_demand_status': 'NO_DATA'}

        try:
            analysis_result = {}

            for source, data in self.supply_demand_data.items():
                source_analysis = self._analyze_single_source_supply_demand(data, source)
                analysis_result[f'supply_demand_{source}'] = source_analysis

            # 综合供需分析
            combined_analysis = self._combine_supply_demand_analysis(analysis_result)
            analysis_result.update(combined_analysis)

            return analysis_result

        except Exception as e:
            self.logger.error(f"供需关系分析失败: {e}")
            return {'supply_demand_status': 'ERROR'}

    def _analyze_single_source_supply_demand(self, data: Any, source: str) -> Dict:
        """分析单一数据源的供需关系"""
        try:
            if isinstance(data, list) and len(data) > 0:
                # 假设数据格式为列表，包含价格和数量信息
                latest_data = data[-1] if data else {}

                supply_quantity = latest_data.get('supply_quantity', 0)
                demand_quantity = latest_data.get('demand_quantity', 0)
                current_price = latest_data.get('price', 0)

                # 供需比率
                if demand_quantity > 0:
                    supply_demand_ratio = supply_quantity / demand_quantity
                else:
                    supply_demand_ratio = float('inf') if supply_quantity > 0 else 1.0

                # 供需状态判断
                if supply_demand_ratio > 1.5:
                    status = 'SUPPLY_EXCESS'
                elif supply_demand_ratio < 0.67:
                    status = 'DEMAND_EXCESS'
                else:
                    status = 'BALANCED'

                return {
                    'supply_quantity': supply_quantity,
                    'demand_quantity': demand_quantity,
                    'supply_demand_ratio': supply_demand_ratio,
                    'status': status,
                    'current_price': current_price,
                    'data_source': source
                }

            elif isinstance(data, pd.DataFrame) and not data.empty:
                # DataFrame格式的供需数据
                latest_row = data.iloc[-1]

                supply_quantity = latest_row.get('supply', 0)
                demand_quantity = latest_row.get('demand', 0)
                current_price = latest_row.get('price', 0)

                if demand_quantity > 0:
                    supply_demand_ratio = supply_quantity / demand_quantity
                else:
                    supply_demand_ratio = float('inf') if supply_quantity > 0 else 1.0

                if supply_demand_ratio > 1.5:
                    status = 'SUPPLY_EXCESS'
                elif supply_demand_ratio < 0.67:
                    status = 'DEMAND_EXCESS'
                else:
                    status = 'BALANCED'

                return {
                    'supply_quantity': supply_quantity,
                    'demand_quantity': demand_quantity,
                    'supply_demand_ratio': supply_demand_ratio,
                    'status': status,
                    'current_price': current_price,
                    'data_source': source
                }

            else:
                return {
                    'status': 'NO_VALID_DATA',
                    'data_source': source
                }

        except Exception as e:
            self.logger.error(f"单一数据源供需分析失败 ({source}): {e}")
            return {
                'status': 'ERROR',
                'data_source': source
            }

    def _combine_supply_demand_analysis(self, source_analyses: Dict) -> Dict:
        """综合多个数据源的供需分析"""
        try:
            valid_analyses = []

            for key, analysis in source_analyses.items():
                if analysis.get('status') not in ['NO_VALID_DATA', 'ERROR']:
                    valid_analyses.append(analysis)

            if not valid_analyses:
                return {
                    'combined_supply_demand_status': 'NO_VALID_DATA',
                    'supply_demand_confidence': 0
                }

            # 计算平均供需比率
            ratios = [a['supply_demand_ratio'] for a in valid_analyses if a['supply_demand_ratio'] != float('inf')]
            avg_ratio = np.mean(ratios) if ratios else 1.0

            # 状态投票
            status_votes = {}
            for analysis in valid_analyses:
                status = analysis['status']
                status_votes[status] = status_votes.get(status, 0) + 1

            # 确定最终状态
            if status_votes:
                combined_status = max(status_votes.items(), key=lambda x: x[1])[0]
                confidence = max(status_votes.values()) / len(valid_analyses) * 100
            else:
                combined_status = 'UNKNOWN'
                confidence = 0

            return {
                'combined_supply_demand_status': combined_status,
                'combined_supply_demand_ratio': avg_ratio,
                'supply_demand_confidence': confidence,
                'data_sources_count': len(valid_analyses)
            }

        except Exception as e:
            self.logger.error(f"综合供需分析失败: {e}")
            return {
                'combined_supply_demand_status': 'ERROR',
                'supply_demand_confidence': 0
            }

    def calculate_multi_timeframe_coordination(self) -> Dict:
        """计算多时间框架协调指标"""
        try:
            coordination_result = {}

            # 获取各时间框架的趋势
            daily_trend = self._get_daily_trend()
            weekly_trend = self._get_weekly_trend()
            hourly_trend = self._get_hourly_trend()

            coordination_result.update({
                'daily_trend': daily_trend,
                'weekly_trend': weekly_trend,
                'hourly_trend': hourly_trend
            })

            # 趋势一致性分析
            trends = [daily_trend, weekly_trend, hourly_trend]
            valid_trends = [t for t in trends if t not in ['UNKNOWN', 'ERROR']]

            if len(valid_trends) >= 2:
                up_count = valid_trends.count('UP')
                down_count = valid_trends.count('DOWN')

                if up_count > down_count:
                    overall_trend = 'UP'
                    trend_consistency = up_count / len(valid_trends) * 100
                elif down_count > up_count:
                    overall_trend = 'DOWN'
                    trend_consistency = down_count / len(valid_trends) * 100
                else:
                    overall_trend = 'MIXED'
                    trend_consistency = 50
            else:
                overall_trend = 'UNKNOWN'
                trend_consistency = 0

            coordination_result.update({
                'overall_trend': overall_trend,
                'trend_consistency': trend_consistency,
                'timeframes_analyzed': len(valid_trends)
            })

            return coordination_result

        except Exception as e:
            self.logger.error(f"多时间框架协调计算失败: {e}")
            return {
                'overall_trend': 'ERROR',
                'trend_consistency': 0
            }

    def _get_daily_trend(self) -> str:
        """获取日线趋势"""
        try:
            if 'ema_12' in self.indicators and 'ema_26' in self.indicators:
                latest_ema_12 = self.indicators['ema_12'].iloc[-1]
                latest_ema_26 = self.indicators['ema_26'].iloc[-1]
                return 'UP' if latest_ema_12 > latest_ema_26 else 'DOWN'
            else:
                # 如果指标未计算，先计算
                self.calculate_all_indicators()
                return self._get_daily_trend()
        except:
            return 'UNKNOWN'

    def _get_weekly_trend(self) -> str:
        """获取周线趋势"""
        try:
            if self.weekly_data is not None and len(self.weekly_data) >= 12:
                weekly_prices = self.weekly_data['close']
                weekly_ema = weekly_prices.ewm(span=12).mean()

                if len(weekly_ema) >= 2:
                    return 'UP' if weekly_ema.iloc[-1] > weekly_ema.iloc[-2] else 'DOWN'
            return 'UNKNOWN'
        except:
            return 'UNKNOWN'

    def _get_hourly_trend(self) -> str:
        """获取时线趋势"""
        try:
            if self.hourly_data is not None and len(self.hourly_data) >= 10:
                hourly_prices = self.hourly_data['close']
                hourly_ema_5 = hourly_prices.ewm(span=5).mean()
                hourly_ema_10 = hourly_prices.ewm(span=10).mean()

                if len(hourly_ema_5) >= 1 and len(hourly_ema_10) >= 1:
                    return 'UP' if hourly_ema_5.iloc[-1] > hourly_ema_10.iloc[-1] else 'DOWN'
            return 'UNKNOWN'
        except:
            return 'UNKNOWN'

    def validate_with_legacy(self, legacy_calculator: TechnicalIndicatorCalculator) -> Dict:
        """与现有计算器结果对比验证"""
        try:
            # 获取两个计算器的结果
            enhanced_indicators = self.calculate_enhanced_indicators()
            legacy_indicators = legacy_calculator.calculate_all_indicators()

            validation_result = {
                'validation_timestamp': datetime.now(),
                'comparisons': {},
                'overall_consistency': True
            }

            # 对比共同指标
            common_indicators = ['rsi', 'macd', 'ema_12', 'ema_26', 'sma_20']

            for indicator in common_indicators:
                if indicator in enhanced_indicators and indicator in legacy_indicators:
                    enhanced_value = enhanced_indicators[indicator].iloc[-1] if hasattr(enhanced_indicators[indicator], 'iloc') else enhanced_indicators[indicator]
                    legacy_value = legacy_indicators[indicator].iloc[-1] if hasattr(legacy_indicators[indicator], 'iloc') else legacy_indicators[indicator]

                    # 计算差异百分比
                    if legacy_value != 0:
                        diff_percentage = abs(enhanced_value - legacy_value) / abs(legacy_value) * 100
                    else:
                        diff_percentage = 0 if enhanced_value == 0 else 100

                    is_consistent = diff_percentage < 1.0  # 1%以内认为一致

                    validation_result['comparisons'][indicator] = {
                        'enhanced_value': float(enhanced_value),
                        'legacy_value': float(legacy_value),
                        'difference_percentage': diff_percentage,
                        'is_consistent': is_consistent
                    }

                    if not is_consistent:
                        validation_result['overall_consistency'] = False

            # 计算总体一致性分数
            consistent_count = sum(1 for comp in validation_result['comparisons'].values() if comp['is_consistent'])
            total_count = len(validation_result['comparisons'])
            consistency_score = (consistent_count / total_count * 100) if total_count > 0 else 0

            validation_result['consistency_score'] = consistency_score

            self.logger.info(f"与现有计算器验证完成，一致性评分: {consistency_score:.2f}%")

            return validation_result

        except Exception as e:
            self.logger.error(f"与现有计算器对比验证失败: {e}")
            return {
                'validation_timestamp': datetime.now(),
                'overall_consistency': False,
                'consistency_score': 0,
                'error': str(e)
            }

    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        self.logger.info("指标计算缓存已清空")

    def enable_cache(self):
        """启用缓存"""
        self.cache_enabled = True
        self.logger.info("指标计算缓存已启用")

    def disable_cache(self):
        """禁用缓存"""
        self.cache_enabled = False
        self.logger.info("指标计算缓存已禁用")

    def get_cache_info(self) -> Dict:
        """获取缓存信息"""
        return {
            'cache_enabled': self.cache_enabled,
            'cached_items': list(self.cache.keys()),
            'cache_size': len(self.cache)
        }
