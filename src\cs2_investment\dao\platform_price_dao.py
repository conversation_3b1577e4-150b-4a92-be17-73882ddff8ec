"""
平台价格数据访问对象

提供平台价格数据的数据库操作功能。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from contextlib import contextmanager
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.models.platform_price import PlatformPrice
from src.cs2_investment.dao.base_dao import BaseDAO


class PlatformPriceDAO(BaseDAO[PlatformPrice]):
    """平台价格数据访问对象"""

    def __init__(self, session: Optional[Session] = None):
        super().__init__(PlatformPrice)
        self.session = session  # 可选的外部session

    @contextmanager
    def _get_session(self):
        """获取session的上下文管理器"""
        if self.session:
            # 使用外部提供的session
            yield self.session
        else:
            # 使用BaseDAO的session管理
            from src.cs2_investment.config.database import get_db_session
            with get_db_session() as session:
                yield session
    
    def save_platform_price(self,
                           item_id: str,
                           market_hash_name: str,
                           platform: str,
                           platform_item_id: str,
                           sell_price: float,
                           sell_count: int,
                           bidding_price: float,
                           bidding_count: int,
                           steamdt_update_time: int,
                           query_time: datetime,
                           data_source: str = 'steamdt') -> PlatformPrice:
        """
        保存平台价格数据

        Args:
            item_id: 饰品ID
            market_hash_name: 市场哈希名称
            platform: 平台名称
            platform_item_id: 平台商品ID
            sell_price: 在售价格
            sell_count: 在售数量
            bidding_price: 求购价格
            bidding_count: 求购数量
            steamdt_update_time: SteamDT更新时间戳
            query_time: 查询时间
            data_source: 数据源，默认'steamdt'，Steam直接API使用'steam_direct'

        Returns:
            PlatformPrice: 保存的平台价格对象
        """
        # 简化日志：只在DEBUG级别显示详细信息
        self.logger.debug(f"💾 [平台价格保存] {platform} - {market_hash_name}: 在售{sell_price}, 求购{bidding_price}")

        try:
            with self._get_session() as session:
                # 检查是否已存在该饰品在该平台的记录（考虑数据源以支持同一平台的多种数据源）
                existing = session.query(PlatformPrice).filter(
                    and_(
                        PlatformPrice.item_id == item_id,
                        PlatformPrice.platform_name == platform,
                        PlatformPrice.data_source == data_source  # 关键：加入数据源条件
                    )
                ).first()

                if existing:
                    # 更新现有记录为最新价格信息
                    # 只有价格发生变化时才记录日志
                    # 转换为float类型避免Decimal和float运算错误
                    old_sell_price = float(existing.sell_price) if existing.sell_price else 0.0
                    old_bidding_price = float(existing.bidding_price) if existing.bidding_price else 0.0

                    price_changed = (
                        abs(old_sell_price - sell_price) > 0.001 or
                        abs(old_bidding_price - bidding_price) > 0.001
                    )

                    if price_changed:
                        self.logger.info(f"💰 [价格更新] {platform} - {market_hash_name}: "
                                       f"在售 {old_sell_price}→{sell_price}, "
                                       f"求购 {old_bidding_price}→{bidding_price}")

                    existing.platform_item_id = platform_item_id
                    existing.sell_price = sell_price
                    existing.sell_count = sell_count
                    existing.bidding_price = bidding_price
                    existing.bidding_count = bidding_count
                    existing.price = sell_price  # 兼容性字段
                    existing.steamdt_update_time = steamdt_update_time  # 更新为最新的SteamDT时间
                    existing.update_time = datetime.now()
                    existing.query_time = query_time
                    existing.is_active = True
                    existing.data_source = data_source  # 更新数据源

                    session.commit()
                    session.refresh(existing)  # 刷新对象以获取最新数据
                    return existing
                else:
                    # 创建新记录
                    self.logger.info(f"🆕 [新增价格] {platform} - {market_hash_name}: "
                                   f"在售 {sell_price}, 求购 {bidding_price}")

                    platform_price = PlatformPrice(
                        item_id=item_id,
                        platform_enum=platform.lower().replace(' ', '_'),
                        platform_name=platform,
                        platform_item_id=platform_item_id,
                        market_hash_name=market_hash_name,
                        sell_price=sell_price,
                        sell_count=sell_count,
                        bidding_price=bidding_price,
                        bidding_count=bidding_count,
                        price=sell_price,  # 兼容性字段
                        steamdt_update_time=steamdt_update_time,
                        update_time=datetime.now(),
                        query_time=query_time,
                        is_active=True,
                        data_source=data_source
                    )

                    session.add(platform_price)
                    session.commit()
                    session.refresh(platform_price)  # 刷新对象以获取最新数据
                    return platform_price

        except Exception as e:
            self.logger.error(f"❌ [平台价格保存] 保存失败: {market_hash_name} - {platform}")
            self.logger.error(f"   💥 错误详情: {str(e)}")
            self.logger.error(f"   📊 请求参数: item_id={item_id}, platform={platform}, sell_price={sell_price}, bidding_price={bidding_price}")
            raise

    def get_steam_item_id(self, item_id: str) -> Optional[str]:
        """
        获取饰品在Steam平台的item_nameid（用于优化Steam价格抓取）

        Args:
            item_id: 饰品ID

        Returns:
            Steam平台的platform_item_id，如果没有找到返回None
        """
        try:
            with self._get_session() as session:
                # 查询data_source为steam_direct的最新记录
                steam_price = session.query(PlatformPrice).filter(
                    and_(
                        PlatformPrice.item_id == item_id,
                        PlatformPrice.data_source == 'steam_direct',
                        PlatformPrice.is_active == True,
                        PlatformPrice.platform_item_id.isnot(None),
                        PlatformPrice.platform_item_id != ''
                    )
                ).order_by(PlatformPrice.update_time.desc()).first()

                if steam_price:
                    self.logger.info(f"🔍 [Steam ID缓存] 找到已缓存的Steam ID: {steam_price.platform_item_id} (饰品: {item_id})")
                    return steam_price.platform_item_id
                else:
                    self.logger.debug(f"🔍 [Steam ID缓存] 未找到缓存的Steam ID: {item_id}")
                    return None

        except Exception as e:
            self.logger.error(f"❌ [Steam ID缓存] 查询失败: {item_id} - {e}")
            return None

    def get_latest_prices_by_item(self, item_id: str) -> List[PlatformPrice]:
        """
        获取指定饰品的最新平台价格
        
        Args:
            item_id: 饰品ID
            
        Returns:
            List[PlatformPrice]: 最新平台价格列表
        """
        with self._get_session() as session:
            # 子查询：获取每个平台的最新更新时间
            subquery = session.query(
                PlatformPrice.platform_name,
                func.max(PlatformPrice.steamdt_update_time).label('max_update_time')
            ).filter(
                and_(
                    PlatformPrice.item_id == item_id,
                    PlatformPrice.is_active == True
                )
            ).group_by(PlatformPrice.platform_name).subquery()

            # 主查询：获取最新价格记录
            return session.query(PlatformPrice).join(
                subquery,
                and_(
                    PlatformPrice.platform_name == subquery.c.platform_name,
                    PlatformPrice.steamdt_update_time == subquery.c.max_update_time
                )
            ).filter(
                PlatformPrice.item_id == item_id
            ).all()
    
    def get_price_history_by_item_platform(self, 
                                         item_id: str, 
                                         platform: str, 
                                         days: int = 30) -> List[PlatformPrice]:
        """
        获取指定饰品在指定平台的价格历史
        
        Args:
            item_id: 饰品ID
            platform: 平台名称
            days: 历史天数
            
        Returns:
            List[PlatformPrice]: 价格历史列表
        """
        start_time = datetime.now() - timedelta(days=days)

        with self._get_session() as session:
            return session.query(PlatformPrice).filter(
                and_(
                    PlatformPrice.item_id == item_id,
                    PlatformPrice.platform_name == platform,
                    PlatformPrice.query_time >= start_time,
                    PlatformPrice.is_active == True
                )
            ).order_by(desc(PlatformPrice.query_time)).all()
    
    def get_platform_statistics(self, platform: str, days: int = 7) -> Dict[str, Any]:
        """
        获取平台统计信息
        
        Args:
            platform: 平台名称
            days: 统计天数
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        start_time = datetime.now() - timedelta(days=days)

        with self._get_session() as session:
            # 基础统计
            total_items = session.query(func.count(func.distinct(PlatformPrice.item_id))).filter(
                and_(
                    PlatformPrice.platform_name == platform,
                    PlatformPrice.query_time >= start_time,
                    PlatformPrice.is_active == True
                )
            ).scalar()

            # 价格统计
            price_stats = session.query(
                func.avg(PlatformPrice.sell_price).label('avg_price'),
                func.min(PlatformPrice.sell_price).label('min_price'),
                func.max(PlatformPrice.sell_price).label('max_price'),
                func.sum(PlatformPrice.sell_count).label('total_sell_count'),
                func.sum(PlatformPrice.bidding_count).label('total_bidding_count')
            ).filter(
                and_(
                    PlatformPrice.platform_name == platform,
                    PlatformPrice.query_time >= start_time,
                    PlatformPrice.is_active == True,
                    PlatformPrice.sell_price > 0
                )
            ).first()

            return {
                'platform': platform,
                'days': days,
                'total_items': total_items or 0,
                'avg_price': float(price_stats.avg_price) if price_stats.avg_price else 0,
                'min_price': float(price_stats.min_price) if price_stats.min_price else 0,
                'max_price': float(price_stats.max_price) if price_stats.max_price else 0,
                'total_sell_count': price_stats.total_sell_count or 0,
                'total_bidding_count': price_stats.total_bidding_count or 0
            }
    
    def get_items_by_market_hash_name(self, market_hash_names: List[str]) -> Dict[str, str]:
        """
        根据市场哈希名称获取饰品ID映射
        
        Args:
            market_hash_names: 市场哈希名称列表
            
        Returns:
            Dict[str, str]: 市场哈希名称到饰品ID的映射
        """
        from src.cs2_investment.models.item import Item

        with self._get_session() as session:
            results = session.query(
                Item.market_hash_name,
                Item.item_id
            ).filter(
                Item.market_hash_name.in_(market_hash_names)
            ).all()

            return {result.market_hash_name: result.item_id for result in results}
    
    def cleanup_old_data(self, days: int = 90) -> int:
        """
        清理旧数据
        
        Args:
            days: 保留天数
            
        Returns:
            int: 删除的记录数
        """
        cutoff_time = datetime.now() - timedelta(days=days)

        with self._get_session() as session:
            deleted_count = session.query(PlatformPrice).filter(
                PlatformPrice.query_time < cutoff_time
            ).delete()

            session.commit()
            return deleted_count
    
    def deactivate_old_prices(self, hours: int = 24) -> int:
        """
        将过期价格标记为无效
        
        Args:
            hours: 过期小时数
            
        Returns:
            int: 更新的记录数
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)

        with self._get_session() as session:
            updated_count = session.query(PlatformPrice).filter(
                and_(
                    PlatformPrice.query_time < cutoff_time,
                    PlatformPrice.is_active == True
                )
            ).update({'is_active': False})

            session.commit()
            return updated_count
    
    def get_all_platforms(self) -> List[str]:
        """
        获取所有平台列表
        
        Returns:
            List[str]: 平台名称列表
        """
        with self._get_session() as session:
            results = session.query(
                func.distinct(PlatformPrice.platform_name)
            ).filter(
                PlatformPrice.is_active == True
            ).all()

            return [result[0] for result in results if result[0]]
    
    def batch_save_platform_prices(self, price_data_list: List[Dict[str, Any]]) -> int:
        """
        批量保存平台价格数据

        Args:
            price_data_list: 价格数据列表

        Returns:
            int: 保存的记录数
        """
        saved_count = 0

        for price_data in price_data_list:
            try:
                self.save_platform_price(**price_data)
                saved_count += 1
            except Exception as e:
                # 记录错误但继续处理其他数据
                print(f"保存价格数据失败: {e}, 数据: {price_data}")
                continue

        return saved_count

    def get_items_steam_direct_status(self, item_ids: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        获取饰品的steam_direct数据源状态

        Args:
            item_ids: 饰品ID列表

        Returns:
            Dict[str, Dict[str, Any]]: 饰品ID -> steam_direct状态信息
        """
        try:
            with self._get_session() as session:
                # 查询steam_direct数据源的价格记录
                steam_direct_prices = session.query(PlatformPrice).filter(
                    and_(
                        PlatformPrice.item_id.in_(item_ids),
                        PlatformPrice.data_source == 'steam_direct',
                        PlatformPrice.is_active == True
                    )
                ).all()

                # 构建结果字典
                result = {}
                for item_id in item_ids:
                    result[item_id] = {
                        'has_steam_direct': False,
                        'last_update_time': None,
                        'query_time': None
                    }

                # 填充steam_direct数据
                for price in steam_direct_prices:
                    item_id = price.item_id
                    if item_id in result:
                        current_time = result[item_id]['query_time']
                        if current_time is None or (price.query_time and price.query_time > current_time):
                            result[item_id] = {
                                'has_steam_direct': True,
                                'last_update_time': price.update_time,
                                'query_time': price.query_time
                            }

                return result

        except SQLAlchemyError as e:
            self.logger.error(f"获取steam_direct状态失败: {e}")
            raise
