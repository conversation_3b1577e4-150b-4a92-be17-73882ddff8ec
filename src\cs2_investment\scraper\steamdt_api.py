#!/usr/bin/env python3
"""
SteamDT API 直接调用脚本
基于Playwright分析的API接口实现直接HTTP请求
"""

import asyncio
import aiohttp
import json
import random
import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from urllib.parse import quote

from .data_models import (
    ScrapingResult, ItemInfo, KlineData, TrendData,
    TrendDataPoint, KlineDataPoint
)


@dataclass
class APIResponse:
    """API响应数据类"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    status_code: Optional[int] = None


class SteamDTAPI:
    """SteamDT API 直接调用类"""
    
    def __init__(self, proxy_enabled: bool = None, proxy_url: str = None,
                 api_delay_min: int = None, api_delay_max: int = None):
        self.base_url = "https://api.steamdt.com"

        # 初始化logger
        self.logger = logging.getLogger(__name__)

        # 从配置文件读取默认值
        from ..config.timer_config import get_timer_config
        config = get_timer_config()

        # 代理配置 - 如果没有传入参数，从配置文件读取
        self.proxy_enabled = proxy_enabled if proxy_enabled is not None else config.scraping.proxy_enabled
        self.proxy_url = proxy_url if proxy_url is not None else config.scraping.proxy_url

        # API延迟配置 - 如果没有传入参数，从配置文件读取
        self.api_delay_min = api_delay_min if api_delay_min is not None else config.scraping.api_delay_min_seconds
        self.api_delay_max = api_delay_max if api_delay_max is not None else config.scraping.api_delay_max_seconds

        # 移除session保持，每次请求使用全新session
        self.base_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'close',  # 改为close，不保持连接
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'Referer': 'https://steamdt.com/',
            'Origin': 'https://steamdt.com'
        }
    
    def _get_fresh_headers(self) -> Dict[str, str]:
        """获取全新的请求头，每次请求都不同"""
        import random

        # 随机化User-Agent（使用更真实的浏览器版本）
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.67 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7258.67 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7258.67 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.67 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.67 Safari/537.36'
        ]

        headers = self.base_headers.copy()
        headers['User-Agent'] = random.choice(user_agents)

        # 添加更多真实的浏览器头部
        headers['sec-ch-ua'] = '"Not A(Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"'
        headers['sec-ch-ua-mobile'] = '?0'
        headers['sec-ch-ua-platform'] = '"Windows"'
        headers['sec-fetch-user'] = '?1'
        headers['upgrade-insecure-requests'] = '1'

        # 随机添加一些可选头部
        if random.random() > 0.5:
            headers['Cache-Control'] = 'no-cache'
        if random.random() > 0.3:
            headers['Pragma'] = 'no-cache'

        return headers

    async def _random_delay(self, min_seconds: int = None, max_seconds: int = None) -> None:
        """随机等待，防止频繁请求

        Args:
            min_seconds: 最小等待秒数，如果为None则使用配置值
            max_seconds: 最大等待秒数，如果为None则使用配置值
        """
        min_sec = min_seconds if min_seconds is not None else self.api_delay_min
        max_sec = max_seconds if max_seconds is not None else self.api_delay_max
        delay = random.uniform(min_sec, max_sec)
        print(f"⏳ 随机等待 {delay:.1f} 秒，防止频繁请求...")
        await asyncio.sleep(delay)

    def _get_timestamp(self) -> int:
        """获取当前时间戳（毫秒）"""
        return int(time.time() * 1000)
    
    def _extract_item_id_from_url(self, url: str) -> Optional[str]:
        """从SteamDT URL中提取饰品名称用于API调用"""
        try:
            # 从URL中提取饰品名称
            # 例如: https://steamdt.com/cs2/Desert%20Eagle%20%7C%20Ocean%20Drive%20%28Well-Worn%29
            parts = url.split('/cs2/')
            if len(parts) > 1:
                item_name = parts[1]
                # URL解码
                from urllib.parse import unquote
                return unquote(item_name)
            return None
        except Exception as e:
            print(f"提取饰品名称失败: {e}")
            return None
    
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> APIResponse:
        """发起API请求 - 每次使用全新session"""
        url = f"{self.base_url}{endpoint}"

        # 获取全新的请求头
        fresh_headers = self._get_fresh_headers()

        # 创建全新的session，每次请求都不同
        timeout = aiohttp.ClientTimeout(total=30)
        connector = aiohttp.TCPConnector(
            limit=1,  # 限制连接数
            limit_per_host=1,  # 每个主机限制连接数
            ttl_dns_cache=0,  # 禁用DNS缓存
            use_dns_cache=False,  # 不使用DNS缓存
            keepalive_timeout=0,  # 不保持连接
            enable_cleanup_closed=True
        )

        # 配置代理
        proxy_config = None
        if self.proxy_enabled and self.proxy_url:
            proxy_config = self.proxy_url

        try:
            # 每次请求都创建全新的session
            async with aiohttp.ClientSession(
                headers=fresh_headers,
                timeout=timeout,
                connector=connector
            ) as session:
                # 简化日志：只在DEBUG级别显示详细信息
                self.logger.debug(f"🔄 创建全新session请求: {method} {url}")

                # 使用代理发送请求
                async with session.request(method, url, proxy=proxy_config, **kwargs) as response:
                    status_code = response.status

                    if status_code == 200:
                        try:
                            data = await response.json()
                            self.logger.debug(f"✅ 请求成功: {status_code}")
                            return APIResponse(True, data=data, status_code=status_code)
                        except json.JSONDecodeError:
                            text = await response.text()
                            self.logger.error(f"❌ JSON解析失败: {text[:100]}")
                            return APIResponse(False, error_message=f"JSON解析失败: {text[:200]}", status_code=status_code)
                    else:
                        text = await response.text()
                        self.logger.error(f"❌ HTTP错误: {status_code} - {text[:100]}")
                        return APIResponse(False, error_message=f"HTTP {status_code}: {text[:200]}", status_code=status_code)

        except asyncio.TimeoutError:
            print(f"⏰ 请求超时: {url}")
            return APIResponse(False, error_message="API请求超时（30秒）")
        except Exception as e:
            print(f"❌ 请求异常: {url} - {e}")
            return APIResponse(False, error_message=f"请求异常: {str(e)}")
    
    async def get_item_info(self, item_name: str) -> APIResponse:
        """获取饰品基本信息"""
        timestamp = self._get_timestamp()
        endpoint = f"/user/skin/v1/item?timestamp={timestamp}"
        
        # 构建请求数据
        data = {
            "marketHashName": item_name,
            "appId": "730"  # CS2的Steam应用ID
        }
        
        return await self._make_request("POST", endpoint, json=data)
    
    async def get_trend_data_3m(self, item_id: str) -> APIResponse:
        """获取3个月总览趋势数据"""
        timestamp = self._get_timestamp()
        endpoint = f"/user/steam/type-trend/v2/item/details?timestamp={timestamp}"

        data = {
            "dateType": 3,
            "itemId": item_id,
            "platform": "ALL",
            "specialStyle": "",
            "typeDay": "2"  # 3个月
        }

        return await self._make_request("POST", endpoint, json=data)

    async def get_trend_data_6m(self, item_id: str) -> APIResponse:
        """获取6个月总览趋势数据"""
        timestamp = self._get_timestamp()
        endpoint = f"/user/steam/type-trend/v2/item/details?timestamp={timestamp}"

        data = {
            "dateType": 3,
            "itemId": item_id,
            "platform": "ALL",
            "specialStyle": "",
            "typeDay": "3"  # 6个月
        }

        return await self._make_request("POST", endpoint, json=data)
    
    async def get_kline_data(self, item_id: str, kline_type: int, max_time: Optional[int] = None) -> APIResponse:
        """
        获取K线数据

        Args:
            item_id: 饰品ID
            kline_type: K线类型 (1=时K, 2=日K, 3=周K)
            max_time: 最大时间戳（可选）
        """
        timestamp = self._get_timestamp()

        params = {
            "timestamp": timestamp,
            "type": kline_type,
            "typeVal": item_id,
            "platform": "ALL",  # 使用ALL获取总览数据
            "specialStyle": ""
        }

        if max_time:
            params["maxTime"] = max_time
        else:
            params["maxTime"] = ""

        # 构建查询字符串
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        endpoint = f"/user/steam/category/v1/kline?{query_string}"

        return await self._make_request("GET", endpoint)

    def _extract_min_timestamp_from_kline(self, kline_response_data: Dict[str, Any]) -> Optional[int]:
        """从K线响应数据中提取最小时间戳，用于分页"""
        try:
            if not kline_response_data.get("success"):
                return None

            # K线数据直接在data字段中，是一个数组
            data = kline_response_data.get("data", [])

            if not isinstance(data, list) or not data:
                return None

            # 找到最小的时间戳
            min_timestamp = None
            for item in data:
                if isinstance(item, list) and len(item) > 0:
                    timestamp_str = item[0]
                    try:
                        timestamp = int(timestamp_str)
                        if min_timestamp is None or timestamp < min_timestamp:
                            min_timestamp = timestamp
                    except (ValueError, TypeError):
                        continue

            return min_timestamp
        except Exception as e:
            print(f"提取最小时间戳失败: {e}")
            return None
    


    def _parse_item_info(self, api_data: Dict[str, Any]) -> Optional[ItemInfo]:
        """解析饰品基本信息"""
        try:
            self.logger.debug(f"🔍 开始解析饰品信息")

            if not api_data.get("success") or not api_data.get("data"):
                self.logger.error(f"❌ API响应无效: success={api_data.get('success')}, data存在={bool(api_data.get('data'))}")
                self.logger.error(f"❌ 错误信息: {api_data.get('errorMsg')}")
                return None

            data = api_data["data"]

            # 检查必需的字段
            item_id = data.get("itemId") or data.get("id") or data.get("item_id")

            if not item_id:
                self.logger.error(f"❌ 未找到item_id，可用字段: {list(data.keys()) if isinstance(data, dict) else 'data不是字典'}")
                return None

            # 计算平均价格作为总览价格（排除价格为0的平台）
            selling_prices = data.get("sellingPriceList", [])
            valid_prices = [float(p.get("price", 0)) for p in selling_prices if p.get("price", 0) > 0]
            average_price = sum(valid_prices) / len(valid_prices) if valid_prices else 0.0

            item_info = ItemInfo(
                item_id=str(item_id),
                name=data.get("name", ""),
                market_hash_name=data.get("marketHashName", ""),
                wear=data.get("exteriorName", ""),
                rarity=data.get("rarityName", ""),
                current_price=average_price,  # 使用各平台平均价格作为总览价格
                price_change=float(data.get("diff1DayPrice") or 0),
                price_change_percent=float(data.get("diff1Day") or 0),
                volume_24h=int(data.get("transactionCount") or 0),
                inventory_count=int(data.get("surviveNum") or 0)
            )

            # 保存原始API响应数据，供后续使用
            item_info.raw_data = data

            return item_info
        except Exception as e:
            print(f"解析饰品信息失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _parse_kline_data(self, api_data: Dict[str, Any], data_type: str) -> Optional[KlineData]:
        """解析K线数据"""
        try:
            if not api_data.get("success"):
                return None

            # K线数据直接在data字段中，是一个数组
            kline_list = api_data.get("data", [])

            if not kline_list:
                return None

            # 转换K线数据点
            kline_points = []
            for item in kline_list:
                try:
                    # K线数据是数组格式：[timestamp, open, high, low, close, volume, amount]
                    if isinstance(item, list) and len(item) >= 6:
                        point = KlineDataPoint(
                            timestamp=int(item[0]),
                            open_price=float(item[1]) if item[1] is not None else 0.0,
                            high_price=float(item[2]) if item[2] is not None else 0.0,
                            low_price=float(item[3]) if item[3] is not None else 0.0,
                            close_price=float(item[4]) if item[4] is not None else 0.0,
                            volume=int(item[5]) if item[5] is not None else 0,
                            date_str=""  # 添加必需的字段
                        )
                        kline_points.append(point)
                except (ValueError, TypeError, IndexError) as e:
                    print(f"解析K线数据点失败: {e}")
                    continue

            from datetime import datetime
            return KlineData(
                item_id="",  # 将在调用时设置
                kline_type=data_type,
                platform="ALL",  # K线数据使用ALL平台（总览数据）
                data_points=kline_points,
                collected_at=datetime.now(),
                raw_data=kline_list
            )
        except Exception as e:
            print(f"解析K线数据失败: {e}")
            return None

    def _parse_trend_data(self, api_data: Dict[str, Any], period: str) -> Optional[TrendData]:
        """解析趋势数据"""
        try:
            if not api_data.get("success"):
                return None

            # 趋势数据直接在data字段中，是一个数组
            trend_list = api_data.get("data", [])

            if not trend_list:
                return None

            # 转换趋势数据点
            data_points = []
            for item in trend_list:
                try:
                    # 趋势数据是数组格式：[timestamp, price, inventory_count, buy_price, buy_count, trade_price, trade_count, total_inventory]
                    if isinstance(item, list) and len(item) >= 8:
                        point = TrendDataPoint(
                            timestamp=int(item[0]),
                            price=float(item[1]) if item[1] is not None else 0.0,
                            inventory_count=int(item[2]) if item[2] is not None else 0,
                            volume=int(item[6]) if len(item) > 6 and item[6] is not None else 0,
                            date_str=""  # 添加必需的字段
                        )
                        data_points.append(point)
                except (ValueError, TypeError, IndexError) as e:
                    print(f"解析趋势数据点失败: {e}")
                    continue

            from datetime import datetime
            return TrendData(
                item_id="",  # 将在调用时设置
                platform="ALL",  # 趋势数据使用ALL平台（总览数据）
                time_range=period,
                data_points=data_points,
                collected_at=datetime.now(),
                raw_data=trend_list
            )
        except Exception as e:
            print(f"解析趋势数据失败: {e}")
            return None

    def _get_steamdt_id_from_db(self, market_hash_name: str) -> Optional[str]:
        """从数据库获取SteamDT ID"""
        try:
            from ..dao.item_dao import ItemDAO
            item_dao = ItemDAO()

            # 通过market_hash_name查询
            items = item_dao.get_by_filter(market_hash_name=market_hash_name)
            if items and hasattr(items[0], 'steamdt_item_id') and items[0].steamdt_item_id:
                print(f"💾 从数据库获取到SteamDT ID: {items[0].steamdt_item_id}")
                return items[0].steamdt_item_id

            print("💾 数据库中未找到SteamDT ID")
            return None

        except Exception as e:
            print(f"⚠️ 从数据库获取SteamDT ID失败: {e}")
            return None

    def _update_steamdt_id_to_db(self, market_hash_name: str, steamdt_item_id: str) -> None:
        """更新数据库中的SteamDT ID"""
        try:
            from ..dao.item_dao import ItemDAO
            item_dao = ItemDAO()

            # 通过market_hash_name查询item_id
            items = item_dao.get_by_filter(market_hash_name=market_hash_name)
            if items:
                item_dao.update_steamdt_id(items[0].item_id, steamdt_item_id)
                print(f"💾 已更新数据库中的SteamDT ID: {steamdt_item_id}")

        except Exception as e:
            print(f"⚠️ 更新数据库SteamDT ID失败: {e}")

    async def scrape_item_data_optimized(self, market_hash_name: str, steamdt_item_id: Optional[str] = None, data_requirements: Dict[str, bool] = None) -> ScrapingResult:
        """
        优化的API抓取方法（推荐使用）

        Args:
            market_hash_name: 饰品的market_hash_name
            steamdt_item_id: SteamDT饰品ID（可选，如果提供则跳过饰品信息接口）
            data_requirements: 数据需求配置

        Returns:
            ScrapingResult: 抓取结果
        """
        self.logger.debug(f"🚀 开始优化API抓取: {market_hash_name}")

        if not data_requirements:
            data_requirements = {
                'trend_data_3m': True,
                'trend_data_6m': True,
                'hourly_kline': True,
                'daily_kline_1': True,
                'daily_kline_2': True,
                'weekly_kline': True
            }

        try:
            # 1. 如果没有提供steamdt_item_id，先尝试从数据库获取
            if not steamdt_item_id:
                steamdt_item_id = self._get_steamdt_id_from_db(market_hash_name)

            # 2. 如果数据库也没有，才调用饰品信息接口
            item_info = None
            if not steamdt_item_id:
                self.logger.debug("📋 数据库中无SteamDT ID，调用饰品信息接口...")
                item_response = await self.get_item_info(market_hash_name)
                if not item_response.success:
                    return ScrapingResult(
                        success=False,
                        error_message=f"获取饰品信息失败: {item_response.error_message}"
                    )

                # 解析饰品信息
                item_info = self._parse_item_info(item_response.data)
                if not item_info or not item_info.item_id:
                    return ScrapingResult(
                        success=False,
                        error_message="解析饰品信息失败或未获取到饰品ID"
                    )

                steamdt_item_id = item_info.item_id
                self.logger.debug(f"✅ 从API获取到SteamDT ID: {steamdt_item_id}")

                # 更新数据库
                self._update_steamdt_id_to_db(market_hash_name, steamdt_item_id)

            # 3. 使用steamdt_item_id调用其他接口
            return await self._fetch_data_with_steamdt_id(steamdt_item_id, data_requirements, item_info)

        except Exception as e:
            return ScrapingResult(
                success=False,
                error_message=f"优化API抓取异常: {str(e)}"
            )

    async def scrape_item_data_by_name(self, item_name: str, data_requirements: Dict[str, bool]) -> ScrapingResult:
        """
        直接使用饰品名称抓取数据（兼容性方法）

        Args:
            item_name: 饰品名称，如 "AK-47 | Gold Arabesque (Battle-Scarred)"
            data_requirements: 数据需求配置

        Returns:
            ScrapingResult: 抓取结果
        """
        # 使用优化方法
        return await self.scrape_item_data_optimized(item_name, None, data_requirements)

    async def _fetch_all_data(self, item_info: ItemInfo, data_requirements: Dict[str, bool]) -> ScrapingResult:
        """获取所有需要的数据（兼容性方法）"""
        return await self._fetch_data_with_steamdt_id(item_info.item_id, data_requirements, item_info)

    async def scrape_item_data_api(self, url: str, data_requirements: Dict[str, bool]) -> ScrapingResult:
        """
        使用API方式抓取饰品数据（兼容性方法）

        Args:
            url: SteamDT饰品页面URL（为了兼容性，从中提取饰品名称）
            data_requirements: 数据需求配置

        Returns:
            ScrapingResult: 抓取结果
        """
        print(f"🚀 开始API方式抓取饰品数据")
        print(f"📍 URL: {url}")
        print(f"📊 数据需求: {list(data_requirements.keys())}")

        try:
            # 1. 从URL提取饰品名称
            item_name = self._extract_item_id_from_url(url)
            if not item_name:
                return ScrapingResult(
                    success=False,
                    error_message="无法从URL提取饰品名称"
                )

            print(f"🔍 饰品名称: {item_name}")

            # 使用饰品名称进行抓取
            return await self.scrape_item_data_by_name(item_name, data_requirements)

        except Exception as e:
            return ScrapingResult(
                success=False,
                error_message=f"API抓取异常: {str(e)}"
            )

    async def _fetch_data_with_steamdt_id(self, steamdt_item_id: str, data_requirements: Dict[str, bool], item_info: Optional[ItemInfo] = None) -> ScrapingResult:
        """使用SteamDT ID获取数据（核心优化方法）"""
        try:
            # 初始化结果
            result = ScrapingResult(
                success=True,
                item_info=item_info  # 可能为None，如果是从数据库获取的ID
            )

            # 使用传入的steamdt_item_id获取各种数据
            item_id = steamdt_item_id
            print(f"🔧 使用SteamDT ID进行数据获取: {item_id}")

            # 获取3个月趋势数据
            if data_requirements.get('trend_data_3m'):
                print("📈 获取3个月总览趋势数据...")
                trend_3m_response = await self.get_trend_data_3m(item_id)
                if trend_3m_response.success:
                    result.trend_data_3m = self._parse_trend_data(trend_3m_response.data, "3m")
                    if result.trend_data_3m:
                        result.trend_data_3m.item_id = item_id
                await self._random_delay()  # 随机等待

            # 获取6个月趋势数据
            if data_requirements.get('trend_data_6m'):
                print("📈 获取6个月总览趋势数据...")
                trend_6m_response = await self.get_trend_data_6m(item_id)
                if trend_6m_response.success:
                    result.trend_data_6m = self._parse_trend_data(trend_6m_response.data, "6m")
                    if result.trend_data_6m:
                        result.trend_data_6m.item_id = item_id
                await self._random_delay()  # 随机等待

            # 获取K线数据
            if data_requirements.get('hourly_kline'):
                # logger.debug("📊 获取时K数据...")
                hourly_response = await self.get_kline_data(item_id, 1)  # type=1为时K
                if hourly_response.success:
                    result.hourly_kline = self._parse_kline_data(hourly_response.data, "hourly")
                    if result.hourly_kline:
                        result.hourly_kline.item_id = item_id
                await self._random_delay()  # 随机等待

            if data_requirements.get('daily_kline_1'):
                # logger.debug("📊 获取日K数据(第1页)...")
                daily_response = await self.get_kline_data(item_id, 2)  # type=2为日K
                if daily_response.success:
                    result.daily_kline_1 = self._parse_kline_data(daily_response.data, "daily_1")
                    if result.daily_kline_1:
                        result.daily_kline_1.item_id = item_id
                await self._random_delay()  # 随机等待

                # 如果需要第二页数据，从第一页响应中提取最小时间戳
                if data_requirements.get('daily_kline_2'):
                    min_timestamp = self._extract_min_timestamp_from_kline(daily_response.data)
                    if min_timestamp:
                        # logger.debug(f"📊 获取日K数据(第2页)，使用时间戳: {min_timestamp}...")
                        daily_response_2 = await self.get_kline_data(item_id, 2, max_time=min_timestamp)
                        if daily_response_2.success:
                            result.daily_kline_2 = self._parse_kline_data(daily_response_2.data, "daily_2")
                            if result.daily_kline_2:
                                result.daily_kline_2.item_id = item_id
                        await self._random_delay()  # 随机等待
                    else:
                        self.logger.warning("⚠️ 无法从第一页日K数据中提取时间戳，跳过第二页")
            elif data_requirements.get('daily_kline_2'):
                # 如果只需要第二页，使用固定的时间戳（从之前观察到的）
                # logger.debug("📊 获取日K数据(第2页，使用固定时间戳)...")
                daily_response_2 = await self.get_kline_data(item_id, 2, max_time=1747152000)
                if daily_response_2.success:
                    result.daily_kline_2 = self._parse_kline_data(daily_response_2.data, "daily_2")
                    if result.daily_kline_2:
                        result.daily_kline_2.item_id = item_id
                await self._random_delay()  # 随机等待

            if data_requirements.get('weekly_kline'):
                # logger.debug("📊 获取周K数据...")
                weekly_response = await self.get_kline_data(item_id, 3)  # type=3为周K
                if weekly_response.success:
                    result.weekly_kline = self._parse_kline_data(weekly_response.data, "weekly")
                    if result.weekly_kline:
                        result.weekly_kline.item_id = item_id
                await self._random_delay()  # 随机等待

            self.logger.debug("✅ API数据抓取完成")
            return result

        except Exception as e:
            self.logger.error(f"❌ API抓取过程中发生异常: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return ScrapingResult(
                success=False,
                error_message=f"API抓取异常: {str(e)}"
            )
