#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品分析系统 - 技术分析报告生成器测试脚本
验证技术分析报告生成器的各项功能，包括数据提取、指标计算、评估逻辑、格式化输出的完整测试
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

def test_technical_report_generation():
    """测试技术分析报告生成"""
    print("🧪 测试技术分析报告生成...")
    
    try:
        from main_analysis_system import CS2AnalysisSystemV2
        
        # 使用测试饰品
        analyzer = CS2AnalysisSystemV2('AK-47 传承 (久经沙场)')
        
        print("📊 运行完整分析...")
        
        # 运行完整分析
        result = analyzer.run_complete_analysis()
        
        if 'error' in result:
            print(f"⚠️ 分析过程中出现错误: {result['error']}")
            print("🔄 使用模拟数据继续测试...")
            return test_with_mock_data()
        
        print("✅ 完整分析完成")
        
        # 生成技术分析报告
        print("📋 生成技术分析报告...")
        report = analyzer.generate_technical_report('test_technical_report.md')
        
        # 验证报告内容
        assert '# CS2饰品技术分析报告' in report, "报告标题缺失"
        assert '## 📈 **一、饰品基础数据**' in report, "基础数据部分缺失"
        assert '## 🔍 **二、技术指标分析**' in report, "技术指标部分缺失"
        assert '## ⚖️ **三、综合技术评估**' in report, "技术评估部分缺失"
        assert '## ⚠️ **四、风险评估**' in report, "风险评估部分缺失"
        assert '## 📋 **五、技术面总结**' in report, "技术总结部分缺失"
        
        print('✅ 技术分析报告生成测试通过')
        print(f"📄 报告长度: {len(report)} 字符")
        
        # 验证文件保存
        assert os.path.exists('test_technical_report.md'), "报告文件未保存"
        print('✅ 报告文件保存成功')
        
        return True
        
    except Exception as e:
        print(f"❌ 技术分析报告生成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_with_mock_data():
    """使用模拟数据测试"""
    print("🧪 使用模拟数据测试技术分析报告生成器...")
    
    try:
        from technical_analysis_report_generator import TechnicalAnalysisReportGenerator
        
        # 创建模拟分析结果
        mock_analysis_results = create_mock_analysis_results()
        
        # 创建模拟分析系统
        mock_system = create_mock_analysis_system()
        
        # 创建报告生成器
        generator = TechnicalAnalysisReportGenerator(
            item_name="AK-47 传承 (久经沙场)",
            analysis_results=mock_analysis_results,
            analysis_system=mock_system
        )
        
        # 生成报告
        report = generator.generate_technical_report('mock_test_report.md')
        
        # 验证报告结构
        assert '# CS2饰品技术分析报告' in report, "报告标题缺失"
        assert 'AK-47 传承 (久经沙场)' in report, "饰品名称缺失"
        assert '## 📈 **一、饰品基础数据**' in report, "基础数据部分缺失"
        assert '## 🔍 **二、技术指标分析**' in report, "技术指标部分缺失"
        assert '## ⚖️ **三、综合技术评估**' in report, "技术评估部分缺失"
        assert '## ⚠️ **四、风险评估**' in report, "风险评估部分缺失"
        assert '## 📋 **五、技术面总结**' in report, "技术总结部分缺失"
        assert '免责声明' in report, "免责声明缺失"
        
        print('✅ 模拟数据测试通过')
        print(f"📄 报告长度: {len(report)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟数据测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_components():
    """测试各个组件功能"""
    print("\n🧪 测试各个组件功能...")
    
    try:
        from technical_analysis_report_generator import TechnicalAnalysisReportGenerator
        
        # 创建测试数据
        mock_analysis_results = create_mock_analysis_results()
        mock_system = create_mock_analysis_system()
        
        generator = TechnicalAnalysisReportGenerator(
            item_name="测试饰品",
            analysis_results=mock_analysis_results,
            analysis_system=mock_system
        )
        
        # 测试基础数据提取
        print("  📊 测试基础数据提取...")
        basic_data = generator._extract_basic_data()
        assert 'current_price' in basic_data, "当前价格缺失"
        assert 'price_changes' in basic_data, "价格变化缺失"
        assert 'volume_data' in basic_data, "成交量数据缺失"
        print("  ✅ 基础数据提取测试通过")
        
        # 测试技术指标分析
        print("  📈 测试技术指标分析...")
        technical_data = generator._analyze_technical_indicators()
        assert 'price_ma' in technical_data, "价格移动平均线缺失"
        assert 'macd' in technical_data, "MACD指标缺失"
        assert 'rsi' in technical_data, "RSI指标缺失"
        assert 'kdj' in technical_data, "KDJ指标缺失"
        assert 'atr' in technical_data, "ATR指标缺失"
        print("  ✅ 技术指标分析测试通过")
        
        # 测试评估系统
        print("  ⚖️ 测试评估系统...")
        evaluation_data = generator._evaluate_trading_signals()
        assert 'strong_buy_signals' in evaluation_data, "强买入信号缺失"
        assert 'strong_sell_signals' in evaluation_data, "强卖出信号缺失"
        assert 'watch_signals' in evaluation_data, "观望信号缺失"
        assert 'main_signal' in evaluation_data, "主要信号缺失"
        print("  ✅ 评估系统测试通过")
        
        # 测试风险评估
        print("  ⚠️ 测试风险评估...")
        risk_data = generator._assess_risk_factors()
        assert 'volatility_analysis' in risk_data, "波动率分析缺失"
        assert 'stop_loss_levels' in risk_data, "止损水平缺失"
        assert 'key_levels' in risk_data, "关键价位缺失"
        print("  ✅ 风险评估测试通过")
        
        print("✅ 所有组件功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 组件功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理...")
    
    try:
        from technical_analysis_report_generator import TechnicalAnalysisReportGenerator
        
        # 测试空数据处理
        print("  📝 测试空数据处理...")
        generator = TechnicalAnalysisReportGenerator(
            item_name="测试饰品",
            analysis_results={}
        )
        
        # 应该能够处理空数据而不崩溃
        report = generator.generate_technical_report()
        assert len(report) > 0, "空数据时应该生成默认报告"
        print("  ✅ 空数据处理测试通过")
        
        # 测试异常数据处理
        print("  📝 测试异常数据处理...")
        invalid_data = {
            'current_signals': {'price': 'invalid'},  # 无效价格
            'technical_indicators': None,  # 空指标
            'support_resistance': {}  # 空支撑阻力
        }
        
        generator = TechnicalAnalysisReportGenerator(
            item_name="测试饰品",
            analysis_results=invalid_data
        )
        
        report = generator.generate_technical_report()
        assert len(report) > 0, "异常数据时应该生成默认报告"
        print("  ✅ 异常数据处理测试通过")
        
        print("✅ 错误处理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {str(e)}")
        return False

def create_mock_analysis_results():
    """创建模拟分析结果"""
    return {
        'current_signals': {
            'price': 1245.67,
            'rsi': 65.4,
            'macd_trend': 'BULLISH',
            'ema_trend': 'UP',
            'overall_signal': 'BULLISH'
        },
        'technical_indicators': {
            'ema_12': pd.Series([1200, 1210, 1220, 1230, 1240]),
            'ema_26': pd.Series([1190, 1200, 1210, 1220, 1230]),
            'sma_20': pd.Series([1195, 1205, 1215, 1225, 1235]),
            'rsi': pd.Series([60, 62, 64, 66, 65.4]),
            'macd': pd.Series([5, 8, 12, 15, 18]),
            'macd_signal': pd.Series([3, 6, 9, 12, 15]),
            'macd_histogram': pd.Series([2, 2, 3, 3, 3]),
            'bb_upper': pd.Series([1300, 1310, 1320, 1330, 1340]),
            'bb_middle': pd.Series([1250, 1260, 1270, 1280, 1290]),
            'bb_lower': pd.Series([1200, 1210, 1220, 1230, 1240]),
            'bb_position': pd.Series([45, 50, 55, 60, 65])
        },
        'support_resistance': {
            'current_price': 1245.67,
            'recent_support': 1180.45,
            'recent_resistance': 1320.89,
            'support_distance': 5.2,
            'resistance_distance': 6.0
        },
        'market_characteristics': {
            'avg_daily_volume': 134.0,
            'market_type': '活跃市场',
            'daily_volatility': 3.67
        }
    }

class MockDataManager:
    """模拟数据管理器"""
    def get_daily_data(self):
        # 创建模拟的日K数据
        dates = pd.date_range('2024-01-01', periods=50, freq='D')
        np.random.seed(42)  # 确保可重复性

        # 生成模拟价格数据
        base_price = 1200
        price_changes = np.random.normal(0, 0.02, 50)  # 2%的日波动
        prices = [base_price]

        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)

        # 生成OHLC数据
        data = {
            'datetime': dates,
            'open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.randint(50, 200, 50)
        }

        return pd.DataFrame(data)

class MockAnalysisSystem:
    """模拟分析系统"""
    def __init__(self):
        self.data_manager = MockDataManager()

def create_mock_analysis_system():
    """创建模拟分析系统"""
    return MockAnalysisSystem()

def test_performance():
    """测试性能"""
    print("\n🧪 测试性能...")

    try:
        from technical_analysis_report_generator import TechnicalAnalysisReportGenerator

        # 创建测试数据
        mock_analysis_results = create_mock_analysis_results()
        mock_system = create_mock_analysis_system()

        generator = TechnicalAnalysisReportGenerator(
            item_name="性能测试饰品",
            analysis_results=mock_analysis_results,
            analysis_system=mock_system
        )

        # 测试报告生成时间
        start_time = datetime.now()
        report = generator.generate_technical_report()
        end_time = datetime.now()

        generation_time = (end_time - start_time).total_seconds()

        print(f"  ⏱️ 报告生成时间: {generation_time:.2f} 秒")
        print(f"  📄 报告长度: {len(report)} 字符")

        # 性能要求：报告生成应在5秒内完成
        assert generation_time < 5.0, f"报告生成时间过长: {generation_time:.2f}秒"

        print("✅ 性能测试通过")
        return True

    except Exception as e:
        print(f"❌ 性能测试失败: {str(e)}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行技术分析报告生成器完整测试套件\n")

    tests = [
        ("技术分析报告生成测试", test_technical_report_generation),
        ("各个组件功能测试", test_individual_components),
        ("错误处理测试", test_error_handling),
        ("性能测试", test_performance)
    ]

    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name}")
        print('='*60)

        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {str(e)}")
            results.append((test_name, False))

    # 输出测试结果总结
    print(f"\n{'='*60}")
    print("📊 测试结果总结")
    print('='*60)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:30}: {status}")
        if result:
            passed += 1

    print('='*60)
    print(f"总计: {passed}/{total} 个测试通过")

    if passed == total:
        print("\n🎉 所有测试通过！技术分析报告生成器功能完整且稳定。")
        print("✅ 系统已准备好投入使用。")
    elif passed >= total * 0.8:  # 80%以上通过
        print(f"\n✅ 大部分测试通过 ({passed}/{total})")
        print("✅ 核心功能正常，系统基本可用。")
        print("⚠️ 部分非关键功能可能需要优化。")
    else:
        print(f"\n❌ 测试失败过多 ({total - passed}/{total})")
        print("❌ 系统存在严重问题，需要修复后再使用。")

    return passed == total

if __name__ == '__main__':
    """主测试入口"""
    success = run_all_tests()

    # 清理测试文件
    test_files = ['test_technical_report.md', 'mock_test_report.md']
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"🗑️ 已清理测试文件: {file}")
            except:
                pass

    # 退出码
    sys.exit(0 if success else 1)
