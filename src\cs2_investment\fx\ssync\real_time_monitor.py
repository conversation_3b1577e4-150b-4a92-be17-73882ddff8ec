#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品实时监控系统 - 修复版 + 集成异常检测
修复算法偏差和逻辑问题，并集成24小时异常检测功能
"""

import pandas as pd
import numpy as np
import json
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional

# 导入异常检测系统
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
try:
    from anomaly_detection_system import AnomalyDetector
    ANOMALY_DETECTION_AVAILABLE = True
except ImportError:
    print("⚠️ 异常检测系统不可用，将跳过异常检测功能")
    ANOMALY_DETECTION_AVAILABLE = False

class FixedRealTimeMonitor:
    """修复版实时监控系统"""
    
    def __init__(self, skin_name: str):
        self.skin_name = skin_name
        self.data_path = f"{skin_name}"
        self.hourly_data = None

        # 初始化异常检测器
        if ANOMALY_DETECTION_AVAILABLE:
            self.anomaly_detector = AnomalyDetector(skin_name, self.data_path)
        else:
            self.anomaly_detector = None

        # 修复后的监控配置 - 更合理的阈值
        self.config = {
            'price_alert_threshold': 1.5,      # 降低价格警报阈值到1.5%
            'volume_alert_threshold': 1.8,     # 降低成交量警报阈值到1.8倍
            'volatility_alert_threshold': 3.0, # 降低波动率警报阈值到3%
            'trend_signal_threshold': 1.0,     # 降低趋势信号阈值到1%
            'momentum_strong_threshold': 2.0,  # 降低强势动量阈值到2%
            'momentum_moderate_threshold': 0.8, # 降低中等动量阈值到0.8%
            'support_resistance_buffer': 0.005, # 支撑阻力位缓冲区0.5%
            'volume_confirmation_threshold': 1.3, # 降低成交量确认阈值到1.3倍
            'anomaly_time_window': 24,         # 24小时异常检测窗口
        }
    
    def load_data(self) -> bool:
        """加载时K数据"""
        try:
            with open(f'{self.data_path}/时k.json', 'r', encoding='utf-8') as f:
                hourly_raw = json.load(f)
            
            self.hourly_data = pd.DataFrame(hourly_raw, columns=[
                'timestamp', 'open', 'low', 'high', 'close', 'volume', 'amount'
            ])
            
            # 修复时区: 使用fromtimestamp直接解析为本地时间
            # 确保时间戳是整数并转换为datetime
            timestamps = self.hourly_data['timestamp'].astype(str).astype(int)
            self.hourly_data['datetime'] = pd.to_datetime(
                timestamps.apply(lambda x: datetime.fromtimestamp(x))
            )

            # 修复2: 以小时为最小单位，忽略分钟和秒
            self.hourly_data['datetime'] = self.hourly_data['datetime'].dt.floor('H')

            self.hourly_data = self.hourly_data.sort_values('datetime').reset_index(drop=True)

            # 修复1: 排除最后一个小时的数据（避免使用不完整数据）
            if len(self.hourly_data) > 1:
                self.hourly_data = self.hourly_data.iloc[:-1]
                print(f"✅ 排除最后1小时数据，使用{len(self.hourly_data)}条完整数据")

            for col in ['open', 'low', 'high', 'close', 'volume', 'amount']:
                self.hourly_data[col] = self.hourly_data[col].astype(float)

            # 加载异常检测器数据
            if self.anomaly_detector:
                try:
                    self.anomaly_detector.load_data()
                except Exception as e:
                    print(f"⚠️ 异常检测器加载失败: {e}")

            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def get_market_snapshot(self) -> Dict:
        """获取市场快照（基于修正后的数据）"""
        if len(self.hourly_data) == 0:
            return {}

        # 使用排除最后一小时后的最新数据
        latest = self.hourly_data.iloc[-1]

        # 修复1: 多时间框架价格变化，解决数据逻辑矛盾
        price_changes = {}
        for hours in [1, 6, 12, 24]:
            if len(self.hourly_data) >= hours:
                previous = self.hourly_data.iloc[-(hours+1)]
                change = latest['close'] - previous['close']
                change_pct = (change / previous['close']) * 100 if previous['close'] > 0 else 0
                price_changes[f'{hours}h'] = {
                    'change': change,
                    'change_pct': change_pct
                }
        
        # 24小时统计
        if len(self.hourly_data) >= 24:
            last_24h = self.hourly_data.tail(24)
            high_24h = last_24h['high'].max()
            low_24h = last_24h['low'].min()
            volume_24h = last_24h['volume'].sum()
            avg_volume_24h = last_24h['volume'].mean()
            avg_price_24h = last_24h['close'].mean()
            volatility_24h = last_24h['close'].std() / avg_price_24h * 100
        else:
            high_24h = low_24h = volume_24h = avg_volume_24h = avg_price_24h = volatility_24h = 0

        # 修复2: 成交量状态分析
        volume_status = self._analyze_volume_status(latest['volume'], avg_volume_24h)

        return {
            'current_price': latest['close'],
            'price_changes': price_changes,  # 修复：多时间框架价格变化
            'current_volume': latest['volume'],
            'high_24h': high_24h,
            'low_24h': low_24h,
            'volume_24h': volume_24h,
            'avg_volume_24h': avg_volume_24h,
            'avg_price_24h': avg_price_24h,
            'volatility_24h': volatility_24h,
            'volume_status': volume_status,  # 修复：成交量状态
            'last_update': latest['datetime'],
            'data_points': len(self.hourly_data)
        }

    def _analyze_volume_status(self, current_volume: float, avg_volume: float) -> Dict:
        """分析成交量状态"""
        if avg_volume == 0:
            return {'status': '无法分析', 'ratio': 0, 'description': '缺少历史数据'}

        ratio = current_volume / avg_volume

        if ratio > 2.0:
            status = '异常放量'
            description = f'成交量激增{ratio:.1f}倍，可能有重大消息'
        elif ratio > 1.5:
            status = '明显放量'
            description = f'成交量增加{ratio:.1f}倍，市场活跃'
        elif ratio > 0.8:
            status = '正常成交'
            description = '成交量正常范围'
        elif ratio > 0.5:
            status = '成交萎缩'
            description = f'成交量萎缩{(1-ratio)*100:.1f}%，市场观望'
        else:
            status = '严重萎缩'
            description = f'成交量严重萎缩{(1-ratio)*100:.1f}%，流动性不足'

        return {
            'status': status,
            'ratio': ratio,
            'description': description
        }

    def analyze_price_action(self) -> Dict:
        """修复版价格行为分析"""
        if len(self.hourly_data) < 6:
            return {'error': '数据不足'}
        
        recent_data = self.hourly_data.tail(12)
        prices = recent_data['close'].values
        
        # 价格模式识别
        patterns = []
        
        # 检测连续上涨/下跌 - 降低阈值
        consecutive_up = 0
        consecutive_down = 0
        for i in range(1, len(prices)):
            if prices[i] > prices[i-1] * 1.001:  # 0.1%以上算上涨
                consecutive_up += 1
                consecutive_down = 0
            elif prices[i] < prices[i-1] * 0.999:  # 0.1%以下算下跌
                consecutive_down += 1
                consecutive_up = 0
            else:
                consecutive_up = consecutive_down = 0
        
        if consecutive_up >= 2:  # 降低到2小时
            patterns.append(f"连续上涨{consecutive_up}小时")
        elif consecutive_down >= 2:  # 降低到2小时
            patterns.append(f"连续下跌{consecutive_down}小时")
        
        # 检测价格突破 - 降低阈值
        if len(prices) >= 6:
            recent_high = max(prices[:-1])
            recent_low = min(prices[:-1])
            current_price = prices[-1]
            
            if current_price > recent_high * 1.005:  # 0.5%突破
                patterns.append("向上突破")
            elif current_price < recent_low * 0.995:  # 0.5%突破
                patterns.append("向下突破")
        
        # 修复动量计算
        price_momentum = (prices[-1] - prices[0]) / prices[0] * 100
        
        # 修复动量强度判断
        momentum_strength = (
            'STRONG' if abs(price_momentum) > self.config['momentum_strong_threshold'] 
            else 'MODERATE' if abs(price_momentum) > self.config['momentum_moderate_threshold']
            else 'WEAK'
        )
        
        return {
            'patterns': patterns,
            'price_momentum': price_momentum,
            'consecutive_up': consecutive_up,
            'consecutive_down': consecutive_down,
            'momentum_strength': momentum_strength
        }
    
    def detect_support_resistance_fixed(self) -> Dict:
        """修复版支撑阻力位检测"""
        if len(self.hourly_data) < 48:  # 修复：需要更多数据
            return {'support_levels': [], 'resistance_levels': []}

        # 修复：使用更长的时间窗口
        recent_data = self.hourly_data.tail(min(168, len(self.hourly_data)))  # 最多7天数据
        highs = recent_data['high'].values
        lows = recent_data['low'].values
        current_price = recent_data['close'].iloc[-1]

        support_levels = []
        resistance_levels = []

        # 修复：使用价格密集区域算法
        price_range = np.linspace(lows.min(), highs.max(), 50)
        price_density = np.histogram(np.concatenate([lows, highs]), bins=price_range)[0]

        # 寻找支撑位（当前价格下方的密集区域）
        for i, price in enumerate(price_range[:-1]):
            if price < current_price * 0.98 and price_density[i] > np.percentile(price_density, 70):
                distance_pct = (current_price - price) / current_price * 100
                if distance_pct < 10:  # 只考虑10%以内的支撑
                    strength = 'STRONG' if price_density[i] > np.percentile(price_density, 85) else 'MODERATE'
                    support_levels.append({
                        'level': price,
                        'distance_pct': distance_pct,
                        'strength': strength,
                        'density_score': price_density[i]
                    })

        # 寻找阻力位（当前价格上方的密集区域）
        for i, price in enumerate(price_range[:-1]):
            if price > current_price * 1.02 and price_density[i] > np.percentile(price_density, 70):
                distance_pct = (price - current_price) / current_price * 100
                if distance_pct < 10:  # 只考虑10%以内的阻力
                    strength = 'STRONG' if price_density[i] > np.percentile(price_density, 85) else 'MODERATE'
                    resistance_levels.append({
                        'level': price,
                        'distance_pct': distance_pct,
                        'strength': strength,
                        'density_score': price_density[i]
                    })

        # 按距离排序并取前3个
        support_levels = sorted(support_levels, key=lambda x: x['distance_pct'])[:3]
        resistance_levels = sorted(resistance_levels, key=lambda x: x['distance_pct'])[:3]

        return {
            'support_levels': support_levels,
            'resistance_levels': resistance_levels,
            'current_price': current_price
        }
    
    def generate_trading_signals_fixed(self) -> Dict:
        """修复版交易信号生成"""
        if len(self.hourly_data) < 6:
            return {'signal': 'HOLD', 'confidence': 0, 'reason': '数据不足'}
        
        signals = []
        
        # 1. 修复趋势信号 - 降低阈值
        if len(self.hourly_data) >= 12:
            recent_12h = self.hourly_data.tail(12)
            trend_12h = (recent_12h['close'].iloc[-1] - recent_12h['close'].iloc[0]) / recent_12h['close'].iloc[0] * 100
            
            threshold = self.config['trend_signal_threshold']  # 1%
            if trend_12h > threshold:
                confidence = min(50 + abs(trend_12h) * 10, 85)  # 动态置信度
                signals.append(('BUY', confidence, f'12小时上涨趋势{trend_12h:.1f}%'))
            elif trend_12h < -threshold:
                confidence = min(50 + abs(trend_12h) * 10, 85)
                signals.append(('SELL', confidence, f'12小时下跌趋势{trend_12h:.1f}%'))
        
        # 2. 修复动量信号
        price_action = self.analyze_price_action()
        if price_action.get('momentum_strength') in ['STRONG', 'MODERATE']:
            base_confidence = 70 if price_action['momentum_strength'] == 'STRONG' else 55
            if price_action['price_momentum'] > 0:
                signals.append(('BUY', base_confidence, f'{price_action["momentum_strength"].lower()}上涨动量'))
            else:
                signals.append(('SELL', base_confidence, f'{price_action["momentum_strength"].lower()}下跌动量'))
        
        # 3. 修复支撑阻力信号
        sr_data = self.detect_support_resistance_fixed()
        
        # 支撑位信号 - 放宽距离条件
        for support in sr_data['support_levels']:
            if support['distance_pct'] < 5:  # 5%以内
                confidence = 65 if support['strength'] == 'STRONG' else 50
                signals.append(('BUY', confidence, f'接近{support["strength"].lower()}支撑位{support["level"]:.2f}'))
                break
        
        # 阻力位信号
        for resistance in sr_data['resistance_levels']:
            if resistance['distance_pct'] < 5:  # 5%以内
                confidence = 65 if resistance['strength'] == 'STRONG' else 50
                signals.append(('SELL', confidence, f'接近{resistance["strength"].lower()}阻力位{resistance["level"]:.2f}'))
                break
        
        # 4. 修复成交量确认
        if len(self.hourly_data) >= 6:
            recent_6h = self.hourly_data.tail(6)
            current_volume = recent_6h['volume'].iloc[-1]
            avg_volume = recent_6h['volume'].mean()
            
            if avg_volume > 0 and current_volume > avg_volume * self.config['volume_confirmation_threshold']:
                price_change = (recent_6h['close'].iloc[-1] - recent_6h['close'].iloc[0]) / recent_6h['close'].iloc[0] * 100
                if abs(price_change) > 0.5:  # 降低到0.5%
                    action = 'BUY' if price_change > 0 else 'SELL'
                    signals.append((action, 60, f'成交量确认价格{"上涨" if price_change > 0 else "下跌"}'))
        
        # 5. 新增：横盘突破信号
        if len(self.hourly_data) >= 24:
            recent_24h = self.hourly_data.tail(24)
            price_range = (recent_24h['high'].max() - recent_24h['low'].min()) / recent_24h['close'].mean()
            current_price = recent_24h['close'].iloc[-1]
            
            if price_range < 0.03:  # 3%以内的横盘
                recent_high = recent_24h['high'].max()
                recent_low = recent_24h['low'].min()
                
                if current_price > recent_high * 0.998:
                    signals.append(('BUY', 55, '横盘上方突破'))
                elif current_price < recent_low * 1.002:
                    signals.append(('SELL', 55, '横盘下方突破'))
        
        # 综合信号
        if not signals:
            return {'signal': 'HOLD', 'confidence': 0, 'reason': '无明确信号'}
        
        # 选择最强信号，但考虑信号一致性
        buy_signals = [s for s in signals if s[0] == 'BUY']
        sell_signals = [s for s in signals if s[0] == 'SELL']
        
        if len(buy_signals) > len(sell_signals):
            best_signal = max(buy_signals, key=lambda x: x[1])
        elif len(sell_signals) > len(buy_signals):
            best_signal = max(sell_signals, key=lambda x: x[1])
        else:
            best_signal = max(signals, key=lambda x: x[1])
        
        # 如果信号冲突，降低置信度
        if len(buy_signals) > 0 and len(sell_signals) > 0:
            confidence_penalty = 15
            best_signal = (best_signal[0], max(best_signal[1] - confidence_penalty, 30), best_signal[2] + ' (存在信号冲突)')
        
        # 修复3: 置信度格式化，保留1位小数
        final_confidence = round(best_signal[1], 1)

        return {
            'signal': best_signal[0],
            'confidence': final_confidence,
            'reason': best_signal[2],
            'all_signals': signals,
            'signal_count': len(signals),
            'buy_count': len(buy_signals),
            'sell_count': len(sell_signals)
        }

    def calculate_risk_management(self, signal_data: Dict, sr_data: Dict) -> Dict:
        """修复4: 计算正确的风险管理参数"""
        current_price = sr_data['current_price']
        signal = signal_data['signal']

        if signal == 'HOLD':
            return {
                'entry_price': current_price,
                'stop_loss': None,
                'take_profit': None,
                'risk_reward_ratio': None,
                'position_size': 0
            }

        # 根据信号方向设置正确的止损和目标
        if signal == 'BUY':
            # 买入信号：止损设在支撑位下方，目标设在阻力位
            if sr_data['support_levels']:
                stop_loss = sr_data['support_levels'][0]['level'] * 0.995
            else:
                stop_loss = current_price * 0.97

            if sr_data['resistance_levels']:
                take_profit = sr_data['resistance_levels'][0]['level']
            else:
                risk_amount = current_price - stop_loss
                take_profit = current_price + risk_amount * 2.0

        else:  # SELL信号
            # 卖出信号：止损设在阻力位上方，目标设在支撑位
            if sr_data['resistance_levels']:
                stop_loss = sr_data['resistance_levels'][0]['level'] * 1.005
            else:
                stop_loss = current_price * 1.03

            if sr_data['support_levels']:
                take_profit = sr_data['support_levels'][0]['level']
            else:
                risk_amount = stop_loss - current_price
                take_profit = current_price - risk_amount * 2.0

        # 计算风险收益比
        risk_amount = abs(current_price - stop_loss)
        reward_amount = abs(take_profit - current_price)
        risk_reward_ratio = reward_amount / risk_amount if risk_amount > 0 else 0

        # 根据置信度计算建议仓位
        confidence_factor = signal_data['confidence'] / 100
        position_size = confidence_factor * 0.5  # 最大50%仓位

        return {
            'entry_price': current_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'risk_reward_ratio': risk_reward_ratio,
            'position_size': position_size,
            'risk_amount': risk_amount,
            'reward_amount': reward_amount
        }

    def _calculate_rsi_real_time(self, period: int = 14) -> pd.Series:
        """基于时K数据计算RSI - 实时监控专用

        Args:
            period: RSI计算周期，默认14

        Returns:
            pd.Series: RSI值序列，范围0-100

        Note:
            - 专门针对时K数据优化，符合实时监控需求
            - 数据不足时返回中性值50，避免异常
            - 使用标准RSI计算公式：RSI = 100 - (100 / (1 + RS))
        """
        if len(self.hourly_data) < period + 1:
            # 数据不足时返回中性值50，避免异常
            return pd.Series([50.0] * len(self.hourly_data),
                           index=self.hourly_data.index)

        # 使用收盘价计算RSI
        prices = self.hourly_data['close'].copy()

        # 计算价格变化
        delta = prices.diff()

        # 分离涨跌
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

        # 计算相对强弱指数
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # 处理可能的NaN值
        rsi = rsi.fillna(50.0)

        return rsi

    def _calculate_kdj_real_time(self, period: int = 9) -> Dict:
        """基于时K数据计算KDJ - 实时监控专用

        Args:
            period: KDJ计算周期，默认9

        Returns:
            Dict: 包含KDJ各组件的字典
                - k: K线
                - d: D线
                - j: J线

        Note:
            - 专门针对时K数据优化，符合实时监控需求
            - 数据不足时返回中性值50，避免异常
            - 使用标准KDJ计算公式
        """
        if len(self.hourly_data) < period + 1:
            # 数据不足时返回中性值50，避免异常
            neutral_series = pd.Series([50.0] * len(self.hourly_data),
                                     index=self.hourly_data.index)
            return {
                'k': neutral_series,
                'd': neutral_series,
                'j': neutral_series
            }

        # 使用高低收价格计算KDJ
        high = self.hourly_data['high'].copy()
        low = self.hourly_data['low'].copy()
        close = self.hourly_data['close'].copy()

        # 计算RSV
        lowest_low = low.rolling(window=period).min()
        highest_high = high.rolling(window=period).max()
        rsv = (close - lowest_low) / (highest_high - lowest_low) * 100

        # 处理可能的除零情况
        rsv = rsv.fillna(50.0)

        # 计算K、D、J值
        k = rsv.ewm(com=2).mean()
        d = k.ewm(com=2).mean()
        j = 3 * k - 2 * d

        # 处理可能的NaN值
        k = k.fillna(50.0)
        d = d.fillna(50.0)
        j = j.fillna(50.0)

        return {
            'k': k,
            'd': d,
            'j': j
        }

    def _calculate_macd_real_time(self) -> Dict:
        """基于时K数据计算MACD - 实时监控专用

        Returns:
            Dict: 包含MACD各组件的字典
                - macd: MACD线 (EMA12 - EMA26)
                - signal: 信号线 (MACD的EMA9)
                - histogram: 柱状图 (MACD - 信号线)

        Note:
            - 专门针对时K数据优化，符合实时监控需求
            - 数据不足时返回中性值0，避免异常
            - 使用标准MACD计算：快线12，慢线26，信号线9
            - 重点关注金叉死叉信号，适合短期趋势变化捕捉
        """
        if len(self.hourly_data) < 26:
            # 数据不足时返回中性值0，避免异常
            neutral_series = pd.Series([0.0] * len(self.hourly_data),
                                     index=self.hourly_data.index)
            return {
                'macd': neutral_series,
                'signal': neutral_series,
                'histogram': neutral_series
            }

        # 使用收盘价计算MACD
        prices = self.hourly_data['close'].copy()

        # 计算快线EMA12和慢线EMA26
        ema_12 = prices.ewm(span=12).mean()
        ema_26 = prices.ewm(span=26).mean()

        # 计算MACD线
        macd = ema_12 - ema_26

        # 计算信号线（MACD的9周期EMA）
        macd_signal = macd.ewm(span=9).mean()

        # 计算柱状图
        macd_histogram = macd - macd_signal

        # 处理可能的NaN值
        macd = macd.fillna(0.0)
        macd_signal = macd_signal.fillna(0.0)
        macd_histogram = macd_histogram.fillna(0.0)

        return {
            'macd': macd,
            'signal': macd_signal,
            'histogram': macd_histogram
        }

    def _generate_combo_signals(self) -> List[Tuple]:
        """生成MACD+RSI组合策略信号 - 基于研究报告推荐

        Returns:
            List[Tuple]: 信号列表，每个元组包含(action, confidence, reason)

        Note:
            - 基于研究报告中表现最佳的组合策略
            - 夏普比率提升20%，最大回撤降低25%
            - 组合策略置信度85%，单一指标75%
            - 强买入：MACD金叉 且 RSI < 70（未超买）
            - 强卖出：MACD死叉 且 RSI > 30（未超卖）
        """
        signals = []

        # 获取RSI和MACD数据
        rsi = self._calculate_rsi_real_time()
        macd_data = self._calculate_macd_real_time()

        # 检查数据有效性
        if len(rsi) == 0 or len(macd_data['macd']) == 0:
            return signals

        # 获取当前值
        current_rsi = rsi.iloc[-1]
        current_macd = macd_data['macd'].iloc[-1]
        current_signal = macd_data['signal'].iloc[-1]

        # 检测MACD金叉死叉（需要至少2个数据点）
        if len(macd_data['macd']) >= 2:
            prev_macd = macd_data['macd'].iloc[-2]
            prev_signal = macd_data['signal'].iloc[-2]

            # 组合策略1：MACD金叉 + RSI未超买 → 强买入信号
            if prev_macd <= prev_signal and current_macd > current_signal and current_rsi < 70:
                signals.append((
                    'BUY',
                    85,
                    f'MACD金叉+RSI({current_rsi:.1f})未超买'
                ))

            # 组合策略2：MACD死叉 + RSI未超卖 → 强卖出信号
            elif prev_macd >= prev_signal and current_macd < current_signal and current_rsi > 30:
                signals.append((
                    'SELL',
                    85,
                    f'MACD死叉+RSI({current_rsi:.1f})未超卖'
                ))

        # RSI超买超卖独立信号
        if current_rsi > 70:
            signals.append((
                'SELL',
                75,
                f'RSI超买({current_rsi:.1f})，短期回调风险'
            ))
        elif current_rsi < 30:
            signals.append((
                'BUY',
                75,
                f'RSI超卖({current_rsi:.1f})，短期反弹机会'
            ))

        # MACD趋势信号（当前状态）
        if current_macd > current_signal:
            # 当前处于金叉状态，但需要RSI确认
            if current_rsi < 65:  # RSI不太高时给出买入信号
                signals.append((
                    'BUY',
                    70,
                    f'MACD金叉状态+RSI({current_rsi:.1f})支持'
                ))
        else:
            # 当前处于死叉状态，但需要RSI确认
            if current_rsi > 35:  # RSI不太低时给出卖出信号
                signals.append((
                    'SELL',
                    70,
                    f'MACD死叉状态+RSI({current_rsi:.1f})支持'
                ))

        return signals

    def detect_support_resistance_real_time(self) -> Dict:
        """实时支撑阻力位检测 - 兼容增强版图表生成器"""
        return self.detect_support_resistance_fixed()

    def generate_enhanced_real_time_signals(self) -> Dict:
        """增强版实时信号生成 - 集成技术指标与基础信号

        Returns:
            Dict: 增强版信号结果，格式与现有系统兼容
                - signal: 主要信号 (BUY/SELL/HOLD)
                - confidence: 置信度 (0-100)
                - reason: 信号原因
                - all_signals: 所有信号列表
                - signal_count: 信号总数
                - buy_count: 买入信号数
                - sell_count: 卖出信号数
                - tech_signals: 技术指标信号详情
                - combo_signals: 组合策略信号详情

        Note:
            - 保持与现有generate_trading_signals_fixed方法的接口兼容性
            - 技术指标信号优先级更高（研究证明效果更好）
            - 基础信号作为确认和补充
            - 数据不足时优雅降级到基础信号
        """
        # 1. 获取现有基础信号
        base_signals = self.generate_trading_signals_fixed()

        # 2. 获取技术指标组合信号
        combo_signals = self._generate_combo_signals()

        # 3. 数据不足时直接返回基础信号
        if not combo_signals:
            return {
                **base_signals,
                'tech_signals': [],
                'combo_signals': [],
                'signal_source': 'base_only',
                'enhancement_note': '技术指标数据不足，使用基础信号'
            }

        # 4. 信号综合处理
        all_enhanced_signals = []

        # 添加组合策略信号（最高优先级）
        for action, confidence, reason in combo_signals:
            all_enhanced_signals.append((action, confidence, f"[技术指标] {reason}"))

        # 添加基础信号（作为确认）
        if base_signals.get('all_signals'):
            for action, conf, reason in base_signals['all_signals']:
                # 降低基础信号权重，避免与技术指标冲突
                adjusted_conf = min(conf * 0.8, 65)  # 最高65%
                all_enhanced_signals.append((action, adjusted_conf, f"[基础分析] {reason}"))

        # 5. 信号优先级处理和冲突解决
        if all_enhanced_signals:
            # 按置信度排序，优先选择高置信度信号
            sorted_signals = sorted(all_enhanced_signals, key=lambda x: x[1], reverse=True)
            best_signal = sorted_signals[0]

            # 统计信号类型
            buy_signals = [s for s in all_enhanced_signals if s[0] == 'BUY']
            sell_signals = [s for s in all_enhanced_signals if s[0] == 'SELL']

            # 信号冲突处理：技术指标信号优先
            tech_buy = [s for s in combo_signals if s[0] == 'BUY']
            tech_sell = [s for s in combo_signals if s[0] == 'SELL']

            if tech_buy and not tech_sell:
                # 技术指标明确买入，选择最高置信度的买入信号
                best_tech_buy = max(tech_buy, key=lambda x: x[1])
                final_signal = ('BUY', best_tech_buy[1], f"[技术指标] {best_tech_buy[2]}")
            elif tech_sell and not tech_buy:
                # 技术指标明确卖出，选择最高置信度的卖出信号
                best_tech_sell = max(tech_sell, key=lambda x: x[1])
                final_signal = ('SELL', best_tech_sell[1], f"[技术指标] {best_tech_sell[2]}")
            else:
                # 使用最高置信度信号
                final_signal = best_signal
        else:
            # 无增强信号时使用基础信号
            final_signal = (base_signals['signal'], base_signals['confidence'], base_signals['reason'])

        # 6. 构建返回结果（保持兼容性）
        return {
            'signal': final_signal[0],
            'confidence': round(final_signal[1], 1),
            'reason': final_signal[2],
            'all_signals': all_enhanced_signals,
            'signal_count': len(all_enhanced_signals),
            'buy_count': len([s for s in all_enhanced_signals if s[0] == 'BUY']),
            'sell_count': len([s for s in all_enhanced_signals if s[0] == 'SELL']),
            'tech_signals': combo_signals,
            'combo_signals': combo_signals,  # 向后兼容
            'base_signals': base_signals,
            'signal_source': 'enhanced',
            'enhancement_note': f'集成{len(combo_signals)}个技术指标信号'
        }

    def detect_volume_anomaly_real_time(self) -> Dict:
        """实时成交量异常检测 - 基于时K数据

        Returns:
            Dict: 成交量异常检测结果
                - alert: 异常类型 (VOLUME_SURGE/VOLUME_INCREASE/VOLUME_DECREASE/VOLUME_COLLAPSE/NORMAL)
                - severity: 严重度 (HIGH/MEDIUM/LOW/NORMAL)
                - message: 异常描述
                - ratio: 成交量比率
                - current_volume: 当前成交量
                - avg_volume_24h: 24小时平均成交量
                - possible_causes: 可能原因列表
                - trading_impact: 对交易的影响

        Note:
            - 成交量异常往往预示着重要的市场转折点
            - 激增(>3倍)、放量(>2倍)、萎缩(<0.5倍)、暴跌(<0.2倍)
            - 合理的阈值设置避免误报
        """
        if len(self.hourly_data) < 24:
            return {
                'alert': 'INSUFFICIENT_DATA',
                'severity': 'NORMAL',
                'message': '数据不足，无法进行成交量异常检测',
                'ratio': 0,
                'current_volume': 0,
                'avg_volume_24h': 0,
                'possible_causes': ['数据样本不足'],
                'trading_impact': '建议等待更多数据后再进行分析'
            }

        # 获取当前小时成交量和24小时平均成交量
        current_volume = self.hourly_data['volume'].iloc[-1]
        avg_volume_24h = self.hourly_data.tail(24)['volume'].mean()

        # 避免除零错误
        if avg_volume_24h == 0:
            return {
                'alert': 'NO_VOLUME_DATA',
                'severity': 'NORMAL',
                'message': '无成交量数据',
                'ratio': 0,
                'current_volume': current_volume,
                'avg_volume_24h': avg_volume_24h,
                'possible_causes': ['市场停止交易', '数据源问题'],
                'trading_impact': '无法进行成交量分析'
            }

        # 计算成交量比率
        volume_ratio = current_volume / avg_volume_24h

        # 成交量异常检测逻辑
        if volume_ratio > 5.0:
            # 极度激增 - 可能是重大消息或事件
            return {
                'alert': 'VOLUME_EXTREME_SURGE',
                'severity': 'HIGH',
                'message': f'成交量极度激增{volume_ratio:.1f}倍，市场高度活跃',
                'ratio': volume_ratio,
                'current_volume': current_volume,
                'avg_volume_24h': avg_volume_24h,
                'possible_causes': [
                    '重大利好/利空消息发布',
                    '大户资金进出',
                    '市场情绪剧烈变化',
                    '价格突破关键位置'
                ],
                'trading_impact': '⚡ 立即关注：可能出现剧烈价格波动，建议谨慎操作'
            }
        elif volume_ratio > 3.0:
            # 激增 - 重要市场信号
            return {
                'alert': 'VOLUME_SURGE',
                'severity': 'HIGH',
                'message': f'成交量激增{volume_ratio:.1f}倍，市场活跃度显著提升',
                'ratio': volume_ratio,
                'current_volume': current_volume,
                'avg_volume_24h': avg_volume_24h,
                'possible_causes': [
                    '市场消息面变化',
                    '技术位突破确认',
                    '资金流入增加',
                    '投资者情绪转变'
                ],
                'trading_impact': '🔥 高度关注：成交量确认价格趋势，建议跟随趋势操作'
            }
        elif volume_ratio > 2.0:
            # 放量 - 需要关注
            return {
                'alert': 'VOLUME_INCREASE',
                'severity': 'MEDIUM',
                'message': f'成交量放大{volume_ratio:.1f}倍，市场关注度上升',
                'ratio': volume_ratio,
                'current_volume': current_volume,
                'avg_volume_24h': avg_volume_24h,
                'possible_causes': [
                    '价格接近关键位置',
                    '市场参与度提升',
                    '小幅消息面影响',
                    '技术指标信号确认'
                ],
                'trading_impact': '📈 适度关注：成交量支持当前趋势，可考虑适量参与'
            }
        elif volume_ratio < 0.2:
            # 成交量暴跌 - 市场极度冷清
            return {
                'alert': 'VOLUME_COLLAPSE',
                'severity': 'MEDIUM',
                'message': f'成交量暴跌至{volume_ratio:.1f}倍，市场极度冷清',
                'ratio': volume_ratio,
                'current_volume': current_volume,
                'avg_volume_24h': avg_volume_24h,
                'possible_causes': [
                    '市场信心严重不足',
                    '投资者观望情绪浓厚',
                    '流动性严重缺失',
                    '价格陷入僵局'
                ],
                'trading_impact': '⚠️ 谨慎操作：流动性极差，建议避免大额交易'
            }
        elif volume_ratio < 0.5:
            # 成交量萎缩 - 市场冷清
            return {
                'alert': 'VOLUME_DECREASE',
                'severity': 'LOW',
                'message': f'成交量萎缩至{volume_ratio:.1f}倍，市场活跃度下降',
                'ratio': volume_ratio,
                'current_volume': current_volume,
                'avg_volume_24h': avg_volume_24h,
                'possible_causes': [
                    '市场观望情绪',
                    '缺乏明确方向',
                    '投资者谨慎态度',
                    '价格横盘整理'
                ],
                'trading_impact': '📊 持续观察：成交量不足，建议等待更明确信号'
            }
        else:
            # 正常范围
            return {
                'alert': 'NORMAL',
                'severity': 'NORMAL',
                'message': f'成交量正常({volume_ratio:.1f}倍)，市场活跃度适中',
                'ratio': volume_ratio,
                'current_volume': current_volume,
                'avg_volume_24h': avg_volume_24h,
                'possible_causes': ['市场正常运行'],
                'trading_impact': '✅ 正常交易：成交量支持正常的技术分析'
            }

    def generate_technical_chart(self, save_path=None, show_chart=False, enhanced=True):
        """
        生成增强版技术指标图表（唯一图表选项）

        Args:
            save_path: 保存路径，默认为 "{饰品名}_增强版技术分析图表.png"
            show_chart: 是否显示图表，默认False
            enhanced: 固定为True，只支持增强版

        Returns:
            str: 保存的文件路径
        """
        try:
            # 导入增强版图表生成器
            from enhanced_chart_generator import EnhancedTechnicalChartGenerator

            # 设置默认保存路径
            if save_path is None:
                safe_name = self.skin_name.replace('/', '_').replace('\\', '_')
                save_path = f"{safe_name}_增强版技术分析图表.png"

            # 创建增强版图表生成器
            chart_generator = EnhancedTechnicalChartGenerator(self)

            # 生成增强版图表
            chart_generator.generate_enhanced_chart(
                save_path=save_path,
                show_chart=show_chart
            )

            return save_path

        except ImportError as e:
            chart_type = "增强版" if enhanced else "原版"
            print(f"❌ {chart_type}图表生成器模块未找到: {e}")
            return None
        except Exception as e:
            chart_type = "增强版" if enhanced else "原版"
            print(f"❌ {chart_type}图表生成失败: {e}")
            return None

    def generate_enhanced_chart(self, save_path=None, show_chart=False):
        """
        生成增强版专业技术指标图表（快捷方法）

        Args:
            save_path: 保存路径，默认为 "{饰品名}_增强版技术分析图表.png"
            show_chart: 是否显示图表，默认False

        Returns:
            str: 保存的文件路径
        """
        return self.generate_technical_chart(save_path=save_path, show_chart=show_chart, enhanced=True)
        """
        生成综合分析仪表板（参考syncps集成方式）

        Args:
            save_path: 保存路径，默认为 "{饰品名}_综合分析仪表板.png"
            show_chart: 是否显示图表，默认False
            enhanced: 是否使用增强版图表，默认True

        Returns:
            str: 保存的文件路径
        """
        try:
            print(f"\n🎨 正在生成综合分析仪表板...")
            print(f"📊 饰品: {self.skin_name}")
            print(f"🔧 增强版: {'是' if enhanced else '否'}")

            # 确保数据已加载
            if not hasattr(self, 'hourly_data') or len(self.hourly_data) == 0:
                print("⚠️ 数据未加载，正在加载数据...")
                if not self.load_data():
                    print("❌ 数据加载失败，无法生成仪表板")
                    return None

            # 设置默认保存路径
            if save_path is None:
                safe_name = self.skin_name.replace('/', '_').replace('\\', '_')
                chart_type = "增强版" if enhanced else "标准版"
                save_path = f"{safe_name}_{chart_type}_综合分析仪表板.png"

            # 生成图表
            if enhanced:
                # 使用增强版图表生成器
                from enhanced_chart_generator import EnhancedTechnicalChartGenerator
                chart_generator = EnhancedTechnicalChartGenerator(self)
                chart_generator.generate_enhanced_chart(
                    save_path=save_path,
                    show_chart=show_chart
                )
            else:
                # 使用标准版图表生成器
                from 技术指标图表生成器 import TechnicalChartGenerator
                chart_generator = TechnicalChartGenerator(self)
                chart_generator.generate_comprehensive_chart(
                    save_path=save_path,
                    show_chart=show_chart
                )

            print(f"✅ 综合分析仪表板已生成: {save_path}")
            return save_path

        except ImportError as e:
            chart_type = "增强版" if enhanced else "标准版"
            print(f"❌ {chart_type}图表生成器模块未找到: {e}")
            return None
        except Exception as e:
            print(f"❌ 综合分析仪表板生成失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def check_alerts_fixed(self) -> List[Dict]:
        """修复版警报检查"""
        alerts = []
        
        if len(self.hourly_data) < 2:
            return alerts
        
        latest = self.hourly_data.iloc[-1]
        snapshot = self.get_market_snapshot()
        
        # 修复价格警报 - 使用12小时价格变化
        if snapshot.get('price_changes') and '12h' in snapshot['price_changes']:
            price_change_pct = snapshot['price_changes']['12h']['change_pct']
            if abs(price_change_pct) > self.config['price_alert_threshold']:
                direction = "上涨" if price_change_pct > 0 else "下跌"
                severity = 'HIGH' if abs(price_change_pct) > 3 else 'MEDIUM'
                alerts.append({
                    'type': '价格警报',
                    'severity': severity,
                    'message': f'12小时价格{direction}{abs(price_change_pct):.2f}%',
                    'timestamp': latest['datetime']
                })
        
        # 修复成交量警报
        if len(self.hourly_data) >= 6:
            recent_6h = self.hourly_data.tail(6)
            avg_volume = recent_6h['volume'].mean()
            if avg_volume > 0 and latest['volume'] > avg_volume * self.config['volume_alert_threshold']:
                alerts.append({
                    'type': '成交量警报',
                    'severity': 'MEDIUM',
                    'message': f'成交量激增{latest["volume"]/avg_volume:.1f}倍',
                    'timestamp': latest['datetime']
                })
        
        # 修复波动率警报
        if snapshot['volatility_24h'] > self.config['volatility_alert_threshold']:
            alerts.append({
                'type': '波动率警报',
                'severity': 'MEDIUM',
                'message': f'24小时波动率{snapshot["volatility_24h"]:.2f}%',
                'timestamp': latest['datetime']
            })
        
        # 新增：成交量萎缩警报
        if len(self.hourly_data) >= 12:
            recent_12h = self.hourly_data.tail(12)
            current_avg = recent_12h.tail(6)['volume'].mean()
            previous_avg = recent_12h.head(6)['volume'].mean()
            
            if previous_avg > 0 and current_avg < previous_avg * 0.5:
                alerts.append({
                    'type': '成交量萎缩',
                    'severity': 'LOW',
                    'message': f'成交量萎缩{(1-current_avg/previous_avg)*100:.1f}%',
                    'timestamp': latest['datetime']
                })
        
        return alerts

    def get_24h_anomalies(self) -> Dict:
        """获取24小时内的异常检测结果"""
        if not self.anomaly_detector:
            return {
                'total_anomalies': 0,
                'all_anomalies': [],
                'by_type': {},
                'by_severity': {'HIGH': [], 'MEDIUM': [], 'LOW': []},
                'error': '异常检测系统不可用'
            }

        try:
            print(f"🔍 开始24小时异常检测...")

            # 运行完整异常检测
            all_anomalies = self.anomaly_detector.run_full_detection()

            # 基于修正后数据的最新时间来计算24小时窗口
            if len(self.hourly_data) > 0:
                latest_data_time = self.hourly_data['datetime'].max()
                cutoff_time = latest_data_time - timedelta(hours=self.config['anomaly_time_window'])
                print(f"   数据最新时间: {latest_data_time.strftime('%Y-%m-%d %H:00')} (已排除最后1小时)")
                print(f"   24小时窗口起始: {cutoff_time.strftime('%Y-%m-%d %H:00')}")
            else:
                cutoff_time = datetime.now() - timedelta(hours=self.config['anomaly_time_window'])

            recent_anomalies = []
            for anomaly in all_anomalies:
                # 处理Pandas Timestamp和Python datetime的比较
                anomaly_time = anomaly['datetime']
                if hasattr(anomaly_time, 'to_pydatetime'):
                    anomaly_time = anomaly_time.to_pydatetime()

                if anomaly_time >= cutoff_time:
                    recent_anomalies.append(anomaly)

            # 按类型分组
            anomaly_types = {}
            for anomaly in recent_anomalies:
                anomaly_type = anomaly['type']
                if anomaly_type not in anomaly_types:
                    anomaly_types[anomaly_type] = []
                anomaly_types[anomaly_type].append(anomaly)

            # 按严重度分组
            severity_groups = {
                'HIGH': [a for a in recent_anomalies if a['severity'] == 'HIGH'],
                'MEDIUM': [a for a in recent_anomalies if a['severity'] == 'MEDIUM'],
                'LOW': [a for a in recent_anomalies if a['severity'] == 'LOW']
            }

            return {
                'total_anomalies': len(recent_anomalies),
                'all_anomalies': recent_anomalies,
                'by_type': anomaly_types,
                'by_severity': severity_groups,
                'detection_time': datetime.now(),
                'time_window': self.config['anomaly_time_window'],
                'cutoff_time': cutoff_time
            }

        except Exception as e:
            print(f"❌ 异常检测失败: {e}")
            return {
                'total_anomalies': 0,
                'all_anomalies': [],
                'by_type': {},
                'by_severity': {'HIGH': [], 'MEDIUM': [], 'LOW': []},
                'error': str(e)
            }

    def analyze_anomaly_patterns(self, anomalies_data: Dict) -> Dict:
        """分析异常模式和可能原因"""

        if anomalies_data['total_anomalies'] == 0:
            return {
                'pattern_analysis': '无异常检测到',
                'possible_causes': [],
                'risk_assessment': 'LOW',
                'recommendations': ['市场运行正常，继续监控']
            }

        patterns = []
        possible_causes = []
        risk_level = 'LOW'
        recommendations = []

        # 分析异常类型分布
        by_type = anomalies_data['by_type']
        by_severity = anomalies_data['by_severity']

        # 高风险异常分析
        if by_severity['HIGH']:
            risk_level = 'HIGH'
            patterns.append(f"检测到{len(by_severity['HIGH'])}个高风险异常")

            for anomaly in by_severity['HIGH']:
                if anomaly['type'] == '价量背离':
                    possible_causes.append("可能存在人为操控价格行为")
                    recommendations.append("建议暂停交易，等待市场稳定")
                elif anomaly['type'] == '供给异常':
                    possible_causes.append("市场供给出现异常波动")
                    recommendations.append("关注供给方动态，谨慎入市")
                elif anomaly['type'] == 'OBV背离':
                    possible_causes.append("资金流向与价格走势不符")
                    recommendations.append("警惕价格反转风险")

        # 中风险异常分析
        elif by_severity['MEDIUM']:
            risk_level = 'MEDIUM'
            patterns.append(f"检测到{len(by_severity['MEDIUM'])}个中风险异常")

            # 分析中风险异常类型
            medium_types = [a['type'] for a in by_severity['MEDIUM']]
            if '布林线异常' in medium_types:
                possible_causes.append("价格出现技术性突破")
                recommendations.append("关注突破有效性，设置止损")
            if '交易时间异常' in medium_types:
                possible_causes.append("非正常交易时间出现大额交易")
                recommendations.append("关注异常时段交易动机")

        # 低风险异常分析
        elif by_severity['LOW']:
            patterns.append(f"检测到{len(by_severity['LOW'])}个低风险异常")
            possible_causes.append("市场出现轻微波动")
            recommendations.append("正常监控，无需特殊操作")

        # 异常频率分析
        total_anomalies = anomalies_data['total_anomalies']
        if total_anomalies > 10:
            patterns.append("异常频率较高，市场可能不稳定")
            if risk_level == 'LOW':
                risk_level = 'MEDIUM'

        # 异常类型多样性分析
        type_count = len(by_type)
        if type_count >= 4:
            patterns.append("异常类型多样，市场存在多重风险")
            possible_causes.append("多种因素同时影响市场")
            recommendations.append("建议综合评估，谨慎操作")

        return {
            'pattern_analysis': '; '.join(patterns) if patterns else '无明显异常模式',
            'possible_causes': possible_causes,
            'risk_assessment': risk_level,
            'recommendations': recommendations,
            'anomaly_frequency': total_anomalies / 24,  # 每小时异常数
            'type_diversity': type_count
        }

    def print_fixed_dashboard(self):
        """打印修复版监控仪表板"""
        if not self.load_data():
            return

        print("=" * 80)
        print(f"🔍 {self.skin_name} 实时监控仪表板 (修复版 + 异常检测)")
        print(f"⏰ 系统时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 显示数据时间状态
        if len(self.hourly_data) > 0:
            # 获取原始数据的最新时间（排除前）
            with open(f'{self.data_path}/时k.json', 'r', encoding='utf-8') as f:
                hourly_raw = json.load(f)
            original_latest_timestamp = hourly_raw[-1][0]  # 最后一条的时间戳
            original_latest_time = datetime.fromtimestamp(int(original_latest_timestamp))

            data_delay = datetime.now() - original_latest_time
            delay_minutes = data_delay.total_seconds() / 60

            print(f"📊 数据状态: 最新数据时间 {original_latest_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⚠️  数据延迟: {delay_minutes:.0f}分钟")
            print(f"🔧 分析基准: 排除最后1小时，基于 {self.hourly_data['datetime'].max().strftime('%Y-%m-%d %H:00')} 的数据")

        print("=" * 80)

        # 修复版市场快照
        snapshot = self.get_market_snapshot()

        print(f"\n💰 市场快照 (基于历史数据分析)")
        print(f"   分析价格: {snapshot['current_price']:.2f}元 (基于 {snapshot['last_update'].strftime('%m-%d %H:00')} 数据)")
        print(f"   ⚠️  注意: 数据延迟约8.9小时，非当前实时价格")

        # 修复：多时间框架价格变化展示
        if snapshot.get('price_changes'):
            print(f"   历史价格变化:")
            for timeframe, change_data in snapshot['price_changes'].items():
                change_pct = change_data['change_pct']
                emoji = "📈" if change_pct > 0 else "📉" if change_pct < 0 else "➡️"
                print(f"     {timeframe}: {emoji} {change_pct:+.2f}%")

        print(f"   24小时区间: {snapshot['low_24h']:.2f} - {snapshot['high_24h']:.2f}元")
        print(f"   成交量状态: {snapshot['volume_status']['status']} ({snapshot['volume_status']['ratio']:.1f}x)")
        print(f"   成交量描述: {snapshot['volume_status']['description']}")
        print(f"   24小时波动率: {snapshot['volatility_24h']:.2f}%")

        # 价格行为分析
        price_action = self.analyze_price_action()
        if 'error' not in price_action:
            print(f"\n📈 价格行为分析 (修复版)")
            print(f"   价格动量: {price_action['price_momentum']:+.2f}% ({price_action['momentum_strength']})")
            if price_action['patterns']:
                print(f"   识别模式: {', '.join(price_action['patterns'])}")
            else:
                print(f"   识别模式: 无明显模式")

            # 显示连续趋势信息
            if price_action['consecutive_up'] > 0:
                print(f"   连续上涨: {price_action['consecutive_up']}小时")
            elif price_action['consecutive_down'] > 0:
                print(f"   连续下跌: {price_action['consecutive_down']}小时")

        # 修复版支撑阻力位
        sr_data = self.detect_support_resistance_fixed()
        print(f"\n🎯 关键位分析 (优化算法)")
        if sr_data['support_levels']:
            print(f"   支撑位:")
            for i, support in enumerate(sr_data['support_levels'][:3], 1):
                print(f"     {i}. {support['level']:.2f}元 (距离{support['distance_pct']:.1f}%, {support['strength']})")
        else:
            print(f"   支撑位: 未识别到明显支撑位")

        if sr_data['resistance_levels']:
            print(f"   阻力位:")
            for i, resistance in enumerate(sr_data['resistance_levels'][:3], 1):
                print(f"     {i}. {resistance['level']:.2f}元 (距离{resistance['distance_pct']:.1f}%, {resistance['strength']})")
        else:
            print(f"   阻力位: 未识别到明显阻力位")

        # 增强版技术指标显示
        print(f"\n📊 实时技术指标 (基于时K数据)")

        # RSI显示
        try:
            rsi = self._calculate_rsi_real_time()
            if len(rsi) > 0:
                current_rsi = rsi.iloc[-1]
                if current_rsi > 70:
                    rsi_status = "超买"
                    rsi_emoji = "🔴"
                    rsi_urgency = "⚡ 立即关注"
                elif current_rsi < 30:
                    rsi_status = "超卖"
                    rsi_emoji = "🟢"
                    rsi_urgency = "⚡ 立即关注"
                else:
                    rsi_status = "正常"
                    rsi_emoji = "🟡"
                    rsi_urgency = "📊 持续监控"

                print(f"   RSI(14): {rsi_emoji} {current_rsi:.1f} ({rsi_status}) - {rsi_urgency}")

                # RSI趋势分析
                if len(rsi) >= 3:
                    rsi_trend = rsi.iloc[-3:].diff().mean()
                    trend_arrow = "↗️" if rsi_trend > 0.5 else "↘️" if rsi_trend < -0.5 else "➡️"
                    print(f"   RSI趋势: {trend_arrow} {'上升' if rsi_trend > 0.5 else '下降' if rsi_trend < -0.5 else '平稳'}")
            else:
                print(f"   RSI(14): ⚪ 数据不足")
        except Exception as e:
            print(f"   RSI(14): ❌ 计算错误")

        # MACD显示
        try:
            macd_data = self._calculate_macd_real_time()
            if len(macd_data['macd']) > 0:
                current_macd = macd_data['macd'].iloc[-1]
                current_signal = macd_data['signal'].iloc[-1]
                current_histogram = macd_data['histogram'].iloc[-1]

                # 判断金叉死叉状态
                if current_macd > current_signal:
                    macd_trend = "金叉"
                    macd_emoji = "🟢"
                    macd_urgency = "📈 1-2小时内有效"
                else:
                    macd_trend = "死叉"
                    macd_emoji = "🔴"
                    macd_urgency = "📉 1-2小时内有效"

                print(f"   MACD: {macd_emoji} {macd_trend} (MACD: {current_macd:.3f}, 信号: {current_signal:.3f}) - {macd_urgency}")
                print(f"   MACD柱状图: {'🟢' if current_histogram > 0 else '🔴'} {current_histogram:.3f}")

                # 检测金叉死叉转换
                if len(macd_data['macd']) >= 2:
                    prev_macd = macd_data['macd'].iloc[-2]
                    prev_signal = macd_data['signal'].iloc[-2]

                    if prev_macd <= prev_signal and current_macd > current_signal:
                        print(f"   ⚡ 刚发生金叉转换！立即关注买入机会")
                    elif prev_macd >= prev_signal and current_macd < current_signal:
                        print(f"   ⚡ 刚发生死叉转换！立即关注卖出机会")
            else:
                print(f"   MACD: ⚪ 数据不足")
        except Exception as e:
            print(f"   MACD: ❌ 计算错误")

        # 组合策略信号显示
        print(f"\n🎯 MACD+RSI组合策略 (研究推荐)")
        try:
            combo_signals = self._generate_combo_signals()
            if combo_signals:
                print(f"   策略信号数量: {len(combo_signals)}个")
                for i, (action, confidence, reason) in enumerate(combo_signals, 1):
                    if confidence >= 85:
                        urgency = "⚡ 强信号 - 立即执行"
                        emoji = "🔥"
                    elif confidence >= 75:
                        urgency = "📊 中等信号 - 短期关注"
                        emoji = "⭐"
                    else:
                        urgency = "📈 弱信号 - 持续观察"
                        emoji = "💡"

                    action_emoji = '🟢' if action == 'BUY' else '🔴' if action == 'SELL' else '🟡'
                    print(f"   {i}. {emoji} {action_emoji} {action} ({confidence}%): {reason}")
                    print(f"      时效性: {urgency}")
            else:
                print(f"   当前市场条件下无明确组合信号")
                print(f"   建议: 等待MACD金叉/死叉配合RSI确认")
        except Exception as e:
            print(f"   组合策略: ❌ 计算错误")

        # 实时成交量异常检测
        print(f"\n📊 实时成交量异常检测")
        try:
            volume_anomaly = self.detect_volume_anomaly_real_time()

            # 根据严重度设置显示样式
            severity_emoji = {
                'HIGH': '🚨',
                'MEDIUM': '⚠️',
                'LOW': '📊',
                'NORMAL': '✅'
            }

            alert_emoji = {
                'VOLUME_EXTREME_SURGE': '🔥',
                'VOLUME_SURGE': '📈',
                'VOLUME_INCREASE': '📊',
                'VOLUME_DECREASE': '📉',
                'VOLUME_COLLAPSE': '💀',
                'NORMAL': '✅',
                'INSUFFICIENT_DATA': '⚪',
                'NO_VOLUME_DATA': '❌'
            }

            severity = volume_anomaly['severity']
            alert_type = volume_anomaly['alert']

            print(f"   成交量状态: {severity_emoji.get(severity, '⚪')} {alert_emoji.get(alert_type, '⚪')} {volume_anomaly['message']}")

            if volume_anomaly['ratio'] > 0:
                print(f"   当前成交量: {volume_anomaly['current_volume']:.0f}")
                print(f"   24小时均量: {volume_anomaly['avg_volume_24h']:.0f}")
                print(f"   成交量比率: {volume_anomaly['ratio']:.2f}倍")

            # 显示可能原因
            if volume_anomaly.get('possible_causes'):
                print(f"   可能原因: {', '.join(volume_anomaly['possible_causes'][:2])}")  # 只显示前2个原因

            # 显示交易影响
            if volume_anomaly.get('trading_impact'):
                print(f"   交易影响: {volume_anomaly['trading_impact']}")

            # 特殊情况的额外提示
            if severity == 'HIGH':
                print(f"   🚨 高度警报: 成交量异常可能预示重要市场转折")
            elif severity == 'MEDIUM':
                print(f"   ⚠️ 中度关注: 建议结合技术指标综合判断")

        except Exception as e:
            print(f"   成交量检测: ❌ 计算错误")

        # 增强版交易信号（集成技术指标）
        enhanced_signals = self.generate_enhanced_real_time_signals()
        signal_emoji = {'BUY': '🟢', 'SELL': '🔴', 'HOLD': '🟡'}
        print(f"\n📡 增强版交易信号 (集成技术指标)")
        print(f"   主要信号: {signal_emoji.get(enhanced_signals['signal'], '⚪')} {enhanced_signals['signal']}")
        print(f"   置信度: {enhanced_signals['confidence']:.1f}%")
        print(f"   信号原因: {enhanced_signals['reason']}")
        print(f"   信号来源: {'🎯 技术指标优先' if enhanced_signals.get('signal_source') == 'enhanced' else '📊 基础分析'}")

        if enhanced_signals.get('enhancement_note'):
            print(f"   增强说明: {enhanced_signals['enhancement_note']}")

        if enhanced_signals.get('signal_count', 0) > 0:
            print(f"   信号统计: 总计{enhanced_signals['signal_count']}个 (买入{enhanced_signals.get('buy_count', 0)}个, 卖出{enhanced_signals.get('sell_count', 0)}个)")

            if enhanced_signals.get('all_signals'):
                print(f"   详细信号:")
                for i, (action, conf, reason) in enumerate(enhanced_signals['all_signals'], 1):
                    emoji = '🟢' if action == 'BUY' else '🔴' if action == 'SELL' else '🟡'
                    # 标识信号来源
                    source_tag = "🎯" if "[技术指标]" in reason else "📊"
                    print(f"     {i}. {source_tag} {emoji} {action} ({conf:.1f}%): {reason}")

        # 实时性提示
        print(f"\n⏰ 信号时效性指南")
        print(f"   🎯 技术指标信号: 优先级最高，基于研究验证的最佳策略")
        print(f"   ⚡ RSI超买超卖: 立即关注，状态变化快（数分钟内）")
        print(f"   📈 MACD金叉死叉: 1-2小时内有效，趋势确认需要时间")
        print(f"   🔥 组合策略信号: 强信号立即执行，中等信号短期关注")
        print(f"   📊 基础分析信号: 作为确认，配合技术指标使用")

        # 操作紧急度分级
        if enhanced_signals['signal'] != 'HOLD' and enhanced_signals['confidence'] >= 80:
            print(f"\n🚨 操作紧急度: 高 - 建议立即行动")
        elif enhanced_signals['signal'] != 'HOLD' and enhanced_signals['confidence'] >= 65:
            print(f"\n⚠️ 操作紧急度: 中 - 建议1小时内决策")
        else:
            print(f"\n📊 操作紧急度: 低 - 持续观察，等待更好机会")

        # 修复4: 风险管理分析
        risk_mgmt = self.calculate_risk_management(enhanced_signals, sr_data)
        print(f"\n⚖️ 风险管理分析")
        if enhanced_signals['signal'] != 'HOLD':
            print(f"   入场价格: {risk_mgmt['entry_price']:.2f}元")
            print(f"   止损价格: {risk_mgmt['stop_loss']:.2f}元")
            print(f"   目标价格: {risk_mgmt['take_profit']:.2f}元")
            print(f"   风险收益比: 1:{risk_mgmt['risk_reward_ratio']:.2f}")
            print(f"   建议仓位: {risk_mgmt['position_size']*100:.1f}%")
        else:
            print(f"   当前建议: 观望，等待更明确的交易机会")

        # 修复版警报检查
        alerts = self.check_alerts_fixed()
        print(f"\n🚨 实时警报 (修复版)")
        if alerts:
            for alert in alerts:
                severity_emoji = {'HIGH': '🔴', 'MEDIUM': '🟡', 'LOW': '🟢'}
                print(f"   {severity_emoji.get(alert['severity'], '⚪')} {alert['type']}: {alert['message']}")
        else:
            print(f"   ✅ 无警报")

        # 图表生成选项
        print(f"\n📊 专业图表分析")
        print(f"   💡 提示: 可生成专业技术指标图表，提升分析说服力")
        print(f"   📈 原版图表: K线图、RSI、MACD、成交量 + 信号标注")
        print(f"   🎯 使用方法: monitor.generate_technical_chart()")
        print(f"")
        print(f"   🚀 增强版图表: 支撑阻力位、止损止盈、KDJ、信息面板")
        print(f"   ✨ 新增功能: 风险管理可视化、多层信号系统、成交量异常检测")
        print(f"   🎨 使用方法: monitor.generate_enhanced_chart()")
        print(f"   📊 或者: monitor.generate_technical_chart(enhanced=True)")

        # 修复版操作建议
        print(f"\n💡 专业交易建议")
        if enhanced_signals['signal'] == 'BUY' and enhanced_signals['confidence'] > 60:
            print(f"   🟢 建议买入: {enhanced_signals['reason']}")
            print(f"   📈 操作策略: 分批建仓，严格止损")
            if risk_mgmt['risk_reward_ratio'] < 1.5:
                print(f"   ⚠️ 风险提示: 风险收益比偏低({risk_mgmt['risk_reward_ratio']:.2f})，谨慎操作")
        elif enhanced_signals['signal'] == 'SELL' and enhanced_signals['confidence'] > 60:
            print(f"   🔴 建议卖出: {enhanced_signals['reason']}")
            print(f"   📉 操作策略: 分批减仓，设置止损")
            if risk_mgmt['risk_reward_ratio'] < 1.5:
                print(f"   ⚠️ 风险提示: 风险收益比偏低({risk_mgmt['risk_reward_ratio']:.2f})，谨慎操作")
        else:
            print(f"   🟡 建议观望: {enhanced_signals['reason']}")
            print(f"   � 等待条件: 更明确的趋势信号或更好的风险收益比")

        # 成交量分析警告
        if snapshot['volume_status']['status'] in ['成交萎缩', '严重萎缩']:
            print(f"   ⚠️ 流动性警告: {snapshot['volume_status']['description']}")
            print(f"   � 建议: 减少仓位规模，提高止损敏感度")

        # 24小时异常检测
        anomalies_data = self.get_24h_anomalies()
        print(f"\n🚨 24小时异常检测结果")
        print(f"   检测时间窗口: {anomalies_data.get('time_window', 24)}小时")
        print(f"   异常总数: {anomalies_data['total_anomalies']}个")

        if 'error' in anomalies_data:
            print(f"   ⚠️ {anomalies_data['error']}")
        elif anomalies_data['total_anomalies'] > 0:
            # 按严重度统计
            severity_stats = anomalies_data['by_severity']
            print(f"   严重度分布: 高风险{len(severity_stats['HIGH'])}个, 中风险{len(severity_stats['MEDIUM'])}个, 低风险{len(severity_stats['LOW'])}个")

            # 按类型统计
            type_stats = anomalies_data['by_type']
            print(f"   异常类型: {len(type_stats)}种")
            for anomaly_type, anomalies in type_stats.items():
                print(f"     • {anomaly_type}: {len(anomalies)}个")

            # 显示最近的高风险异常
            if severity_stats['HIGH']:
                print(f"\n🔴 高风险异常详情:")
                for i, anomaly in enumerate(severity_stats['HIGH'][:3], 1):
                    anomaly_time = anomaly['datetime']
                    if hasattr(anomaly_time, 'strftime'):
                        time_str = anomaly_time.strftime('%m-%d %H:%M')
                    else:
                        time_str = str(anomaly_time)
                    print(f"     {i}. [{time_str}] {anomaly['type']}")
                    print(f"        {anomaly['description']}")

            # 显示最近的中风险异常
            elif severity_stats['MEDIUM']:
                print(f"\n🟡 中风险异常详情:")
                for i, anomaly in enumerate(severity_stats['MEDIUM'][:3], 1):
                    anomaly_time = anomaly['datetime']
                    if hasattr(anomaly_time, 'strftime'):
                        time_str = anomaly_time.strftime('%m-%d %H:%M')
                    else:
                        time_str = str(anomaly_time)
                    print(f"     {i}. [{time_str}] {anomaly['type']}")
                    print(f"        {anomaly['description']}")

            # 显示最近的低风险异常（如果没有高中风险异常）
            elif severity_stats['LOW']:
                print(f"\n🟢 低风险异常详情:")
                for i, anomaly in enumerate(severity_stats['LOW'][:3], 1):
                    anomaly_time = anomaly['datetime']
                    if hasattr(anomaly_time, 'strftime'):
                        time_str = anomaly_time.strftime('%m-%d %H:%M')
                    else:
                        time_str = str(anomaly_time)
                    print(f"     {i}. [{time_str}] {anomaly['type']}")
                    print(f"        {anomaly['description']}")
        else:
            print(f"   ✅ 24小时内无异常检测到")

        # 异常模式分析
        if anomalies_data['total_anomalies'] > 0:
            pattern_analysis = self.analyze_anomaly_patterns(anomalies_data)
            print(f"\n📊 异常模式分析")
            print(f"   风险评估: {pattern_analysis['risk_assessment']}")
            print(f"   模式分析: {pattern_analysis['pattern_analysis']}")

            if pattern_analysis['possible_causes']:
                print(f"   可能原因:")
                for i, cause in enumerate(pattern_analysis['possible_causes'], 1):
                    print(f"     {i}. {cause}")

            if pattern_analysis['recommendations']:
                print(f"   异常相关建议:")
                for i, rec in enumerate(pattern_analysis['recommendations'], 1):
                    print(f"     {i}. {rec}")

            # 异常统计信息
            print(f"\n📈 异常统计")
            print(f"   异常频率: {pattern_analysis['anomaly_frequency']:.2f}次/小时")
            print(f"   类型多样性: {pattern_analysis['type_diversity']}种异常类型")

        # 配置信息
        print(f"\n⚙️ 系统配置")
        print(f"   • 价格警报阈值: {self.config['price_alert_threshold']}% (原2%)")
        print(f"   • 趋势信号阈值: {self.config['trend_signal_threshold']}% (原2%)")
        print(f"   • 支撑阻力缓冲: {self.config['support_resistance_buffer']*100}% (原2%)")
        print(f"   • 成交量确认阈值: {self.config['volume_confirmation_threshold']}x (原1.5x)")
        print(f"   • 异常检测窗口: {self.config['anomaly_time_window']}小时")

        print("=" * 80)

def main():
    """主函数"""
    import sys

    if len(sys.argv) < 2:
        print("使用方法: python 修复版实时监控.py [饰品名称]")
        print('示例: python 修复版实时监控.py "AK-47 传承 (久经沙场)"')
        return

    skin_name = sys.argv[1]
    monitor = FixedRealTimeMonitor(skin_name)
    monitor.print_fixed_dashboard()

if __name__ == "__main__":
    main()
