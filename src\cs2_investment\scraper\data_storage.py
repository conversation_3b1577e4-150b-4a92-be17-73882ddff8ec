"""
数据存储模块

负责将抓取的数据保存到数据库和文件
"""

import json
from pathlib import Path
from datetime import datetime
from typing import List, Optional
import logging

from .data_models import ScrapingResult, TrendData, KlineData

logger = logging.getLogger(__name__)


class DataStorage:
    """数据存储管理器"""
    
    def __init__(self, output_dir: str = None):
        if output_dir is None:
            # 使用项目根目录下的data/raw_data作为默认目录
            project_root = Path(__file__).parent.parent.parent.parent
            output_dir = project_root / "data" / "raw_data"

        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 创建子目录
        self.trend_dir = self.output_dir / "trend_data"
        self.kline_dir = self.output_dir / "kline_data"

        for dir_path in [self.trend_dir, self.kline_dir]:
            dir_path.mkdir(exist_ok=True)
    
    def save_scraping_result(self, result: ScrapingResult) -> bool:
        """保存完整的抓取结果"""
        try:
            if not result.success:
                logger.warning("抓取结果无效，跳过保存")
                return False

            # 从趋势数据或K线数据中获取item_id
            item_id = None
            if result.trend_data_3m:
                item_id = result.trend_data_3m.item_id
            elif result.trend_data_6m:
                item_id = result.trend_data_6m.item_id
            elif result.hourly_kline:
                item_id = result.hourly_kline.item_id
            elif result.daily_kline:
                item_id = result.daily_kline.item_id
            elif result.weekly_kline:
                item_id = result.weekly_kline.item_id

            if not item_id:
                logger.warning("无法获取item_id，跳过保存")
                return False
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 不再保存饰品基本信息
            
            # 只保存3个月和6个月趋势数据
            if result.trend_data_3m:
                self._save_trend_data(result.trend_data_3m, timestamp, suffix="_3m")

            if result.trend_data_6m:
                self._save_trend_data(result.trend_data_6m, timestamp, suffix="_6m")
            
            # 保存K线数据
            for kline_data, kline_type in [
                (result.hourly_kline, "hourly"),
                (result.daily_kline_1, "daily_1"),  # 日K第一次响应
                (result.daily_kline_2, "daily_2"),  # 日K第二次响应
                (result.weekly_kline, "weekly")
            ]:
                if kline_data:
                    self._save_kline_data(kline_data, kline_type, timestamp)
            
            # 保存完整结果的JSON
            self._save_complete_result(result, timestamp)
            
            logger.info(f"数据保存成功: {item_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
            return False
    
    # 已删除饰品信息保存方法
    
    def _save_trend_data(self, trend_data: TrendData, timestamp: str, suffix: str = ""):
        """保存走势数据 - 直接保存原始数据，不做处理"""
        # 只保存JSON格式，直接保存原始data节点数据
        json_filename = f"trend_{trend_data.item_id}_{timestamp}{suffix}.json"
        json_filepath = self.trend_dir / json_filename

        # 直接保存原始数据，不做任何处理
        data = {
            "item_id": trend_data.item_id,
            "platform": trend_data.platform,
            "time_range": trend_data.time_range,
            "collected_at": trend_data.collected_at.isoformat(),
            "raw_data": trend_data.raw_data if hasattr(trend_data, 'raw_data') else [],
            "data_count": len(trend_data.data_points) if trend_data.data_points else 0
        }

        with open(json_filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def _save_kline_data(self, kline_data: KlineData, kline_type: str, timestamp: str):
        """保存K线数据 - 直接保存原始数据，不做处理"""
        # 只保存JSON格式，直接保存原始data节点数据
        json_filename = f"kline_{kline_type}_{kline_data.item_id}_{timestamp}.json"
        json_filepath = self.kline_dir / json_filename

        # 直接保存原始数据，不做任何时间转换处理
        data = {
            "item_id": kline_data.item_id,
            "kline_type": kline_data.kline_type,
            "platform": kline_data.platform,
            "collected_at": kline_data.collected_at.isoformat(),
            "raw_data": kline_data.raw_data if hasattr(kline_data, 'raw_data') else [],
            "data_count": len(kline_data.data_points) if kline_data.data_points else 0
        }

        with open(json_filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def _save_complete_result(self, result: ScrapingResult, timestamp: str):
        """保存完整的抓取结果"""
        # 获取item_id
        item_id = None
        if result.trend_data_3m:
            item_id = result.trend_data_3m.item_id
        elif result.trend_data_6m:
            item_id = result.trend_data_6m.item_id
        elif result.hourly_kline:
            item_id = result.hourly_kline.item_id
        elif result.daily_kline:
            item_id = result.daily_kline.item_id
        elif result.weekly_kline:
            item_id = result.weekly_kline.item_id

        if not item_id:
            return

        filename = f"complete_{item_id}_{timestamp}.json"
        filepath = self.output_dir / filename

        # 构建完整数据结构（不包含饰品信息）
        data = {
            "success": result.success,
            "collected_at": result.collected_at.isoformat() if result.collected_at else None,
            "error_message": result.error_message,
            "item_id": item_id,
            "data_summary": {
                "trend_data_points": len(result.trend_data.raw_data) if result.trend_data and result.trend_data.raw_data else 0,
                "trend_data_3m_points": len(result.trend_data_3m.raw_data) if result.trend_data_3m and result.trend_data_3m.raw_data else 0,
                "trend_data_6m_points": len(result.trend_data_6m.raw_data) if result.trend_data_6m and result.trend_data_6m.raw_data else 0,
                "hourly_kline_points": len(result.hourly_kline.raw_data) if result.hourly_kline and result.hourly_kline.raw_data else 0,
                "daily_kline_1_points": len(result.daily_kline_1.raw_data) if result.daily_kline_1 and result.daily_kline_1.raw_data else 0,
                "daily_kline_2_points": len(result.daily_kline_2.raw_data) if result.daily_kline_2 and result.daily_kline_2.raw_data else 0,
                "weekly_kline_points": len(result.weekly_kline.raw_data) if result.weekly_kline and result.weekly_kline.raw_data else 0
            }
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def save_batch_results(self, results: List[ScrapingResult]) -> int:
        """批量保存抓取结果"""
        success_count = 0
        
        for result in results:
            if self.save_scraping_result(result):
                success_count += 1
        
        logger.info(f"批量保存完成: {success_count}/{len(results)} 成功")
        return success_count
