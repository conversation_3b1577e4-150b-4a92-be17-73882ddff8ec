"""
CS2饰品数据抓取模块

提供SteamDT网站的饰品交易数据抓取功能，包括：
- 饰品走势数据抓取
- K线数据抓取（时K、日K、周K）
- 数据存储和管理
"""

from .steamdt_scraper import SteamDTScraper
from .steamdt_scraper_adapter import SteamDTScraperAdapter
from .api_scraper import APIScraper
from .abstract_data_scraper import AbstractDataScraper
from .scraper_factory import ScraperFactory, get_scraper_factory, create_scraper, get_recommended_method
from .data_models import TrendData, KlineData, ItemInfo, ScrapingResult
from .data_storage import DataStorage
from .data_validator import (
    DataFormatValidator,
    validate_scraping_result,
    compare_scraping_results,
    print_validation_summary
)
from .config import ScraperConfig, DEFAULT_CONFIG

__all__ = [
    'SteamDTScraper',
    'SteamDTScraperAdapter',
    'APIScraper',
    'AbstractDataScraper',
    'ScraperFactory',
    'get_scraper_factory',
    'create_scraper',
    'get_recommended_method',
    'TrendData',
    'KlineData',
    'ItemInfo',
    'ScrapingResult',
    'DataStorage',
    'DataFormatValidator',
    'validate_scraping_result',
    'compare_scraping_results',
    'print_validation_summary',
    'ScraperConfig',
    'DEFAULT_CONFIG'
]
