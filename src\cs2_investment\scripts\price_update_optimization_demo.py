#!/usr/bin/env python3
"""
价格更新优化演示脚本

演示优化后的价格同步逻辑，解决全零价格饰品重复查询的问题。
"""

import sys
import asyncio
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.item_dao import ItemDAO
from src.cs2_investment.services.simple_price_updater import SimplePriceUpdater
from src.cs2_investment.config.timer_config import get_timer_config
from src.cs2_investment.utils.logger import get_logger

logger = get_logger(__name__)


def analyze_items_status():
    """分析饰品状态"""
    logger.info("🔍 分析饰品状态...")
    
    item_dao = ItemDAO()
    
    try:
        # 获取饰品统计信息
        stats = item_dao.get_item_statistics()
        logger.info(f"📊 饰品总数: {stats['total_count']}")
        logger.info(f"📊 类型分布: {stats['type_distribution']}")
        logger.info(f"📊 稀有度分布: {stats['rarity_distribution']}")
        
        # 分析更新状态
        from src.cs2_investment.config.database import get_db_session
        from src.cs2_investment.models.item import Item
        from sqlalchemy import func
        
        with get_db_session() as session:
            # 统计从未更新过的饰品
            never_updated = session.query(Item).filter(Item.last_price_update.is_(None)).count()
            
            # 统计最近24小时内更新过的饰品
            cutoff_time = datetime.now() - timedelta(hours=24)
            recently_updated = session.query(Item).filter(Item.last_price_update >= cutoff_time).count()
            
            # 统计需要更新的饰品（超过24小时未更新）
            need_update = session.query(Item).filter(
                Item.last_price_update < cutoff_time
            ).count()
            
            logger.info(f"📊 从未更新: {never_updated}")
            logger.info(f"📊 最近24小时更新: {recently_updated}")
            logger.info(f"📊 需要更新: {need_update}")
            
            return {
                'total': stats['total_count'],
                'never_updated': never_updated,
                'recently_updated': recently_updated,
                'need_update': need_update
            }
            
    except Exception as e:
        logger.error(f"❌ 分析饰品状态失败: {e}")
        return None


def test_item_selection_strategies():
    """测试不同的饰品选择策略"""
    logger.info("🧪 测试饰品选择策略...")
    
    item_dao = ItemDAO()
    
    try:
        # 策略1：传统方式（按最后更新时间排序）
        logger.info("📋 策略1: 传统方式")
        traditional_items = item_dao.get_items_for_price_update(
            limit=10, 
            skip_zero_price_items=False
        )
        logger.info(f"   获取到 {len(traditional_items)} 个饰品")
        for i, item in enumerate(traditional_items[:5], 1):
            last_update = item.get('last_price_update', 'Never')
            logger.info(f"   {i}. {item['name']} (最后更新: {last_update})")
        
        # 策略2：跳过全零价格饰品
        logger.info("📋 策略2: 跳过全零价格饰品")
        optimized_items = item_dao.get_items_for_price_update(
            limit=10, 
            skip_zero_price_items=True,
            zero_price_update_interval_hours=24
        )
        logger.info(f"   获取到 {len(optimized_items)} 个饰品")
        for i, item in enumerate(optimized_items[:5], 1):
            last_update = item.get('last_price_update', 'Never')
            logger.info(f"   {i}. {item['name']} (最后更新: {last_update})")
            
        return {
            'traditional_count': len(traditional_items),
            'optimized_count': len(optimized_items)
        }
        
    except Exception as e:
        logger.error(f"❌ 测试饰品选择策略失败: {e}")
        return None


async def test_price_updater_optimization():
    """测试价格更新器优化"""
    logger.info("🚀 测试价格更新器优化...")
    
    try:
        # 获取配置
        config = get_timer_config()
        
        # 显示当前配置
        logger.info("⚙️ 当前配置:")
        logger.info(f"   批量大小: {config.simple_price_update.batch_size}")
        logger.info(f"   单个大小: {config.simple_price_update.single_size}")
        logger.info(f"   饰品限制: {config.simple_price_update.items_limit}")
        logger.info(f"   跳过零价格: {config.simple_price_update.skip_zero_price_items}")
        logger.info(f"   零价格间隔: {config.simple_price_update.zero_price_update_interval_hours}小时")
        
        # 注意：这里只是演示，不会真正调用API
        logger.info("💡 优化建议:")
        logger.info("   1. 启用 SIMPLE_PRICE_SKIP_ZERO_ITEMS=true")
        logger.info("   2. 设置 SIMPLE_PRICE_ZERO_UPDATE_INTERVAL=24 (24小时)")
        logger.info("   3. 这样可以避免重复查询全零价格的饰品")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试价格更新器优化失败: {e}")
        return False


def calculate_optimization_impact():
    """计算优化影响"""
    logger.info("📈 计算优化影响...")
    
    try:
        # 分析当前状态
        status = analyze_items_status()
        if not status:
            return
        
        total_items = status['total']
        current_limit = 150  # 每分钟处理的饰品数量
        
        # 计算完整轮询时间
        full_cycle_minutes = total_items / current_limit
        full_cycle_hours = full_cycle_minutes / 60
        
        logger.info(f"📊 优化影响分析:")
        logger.info(f"   总饰品数: {total_items}")
        logger.info(f"   每分钟处理: {current_limit}")
        logger.info(f"   完整轮询时间: {full_cycle_hours:.1f} 小时")
        
        # 假设有30%的饰品价格全为0
        zero_price_ratio = 0.3
        zero_price_items = int(total_items * zero_price_ratio)
        effective_items = total_items - zero_price_items
        
        optimized_cycle_minutes = effective_items / current_limit
        optimized_cycle_hours = optimized_cycle_minutes / 60
        
        time_saved_hours = full_cycle_hours - optimized_cycle_hours
        efficiency_improvement = (time_saved_hours / full_cycle_hours) * 100
        
        logger.info(f"📊 优化后预期:")
        logger.info(f"   假设零价格饰品: {zero_price_items} ({zero_price_ratio*100}%)")
        logger.info(f"   有效饰品数: {effective_items}")
        logger.info(f"   优化后轮询时间: {optimized_cycle_hours:.1f} 小时")
        logger.info(f"   节省时间: {time_saved_hours:.1f} 小时")
        logger.info(f"   效率提升: {efficiency_improvement:.1f}%")
        
        return {
            'original_hours': full_cycle_hours,
            'optimized_hours': optimized_cycle_hours,
            'time_saved': time_saved_hours,
            'efficiency_improvement': efficiency_improvement
        }
        
    except Exception as e:
        logger.error(f"❌ 计算优化影响失败: {e}")
        return None


async def main():
    """主函数"""
    logger.info("🎯 价格更新优化演示")
    logger.info("=" * 50)
    
    # 1. 分析当前状态
    analyze_items_status()
    
    print()
    
    # 2. 测试选择策略
    test_item_selection_strategies()
    
    print()
    
    # 3. 测试更新器优化
    await test_price_updater_optimization()
    
    print()
    
    # 4. 计算优化影响
    calculate_optimization_impact()
    
    print()
    logger.info("✅ 演示完成")


if __name__ == "__main__":
    asyncio.run(main())
