"""
CS2饰品智能投资决策系统 - 核心分析引擎模块

本模块包含智能分析系统的核心组件：
- UnifiedDataManager: 统一数据管理器
- MultiTimeframeEngine: 多时间框架分析引擎  
- DecisionFusionEngine: 决策融合引擎

版本: 1.0.0
"""

from loguru import logger

# 核心模块导入
try:
    from .unified_data_manager import UnifiedDataManager
    from .multi_timeframe_engine import MultiTimeframeEngine
    from .decision_fusion_engine import DecisionFusionEngine
    
    logger.info("智能分析核心模块加载成功")
    
except ImportError as e:
    logger.warning(f"核心模块部分组件未实现: {e}")
    # 在开发阶段，部分模块可能尚未实现
    pass

# 导出的公共接口
__all__ = [
    'UnifiedDataManager',
    'MultiTimeframeEngine', 
    'DecisionFusionEngine'
]
