"""
交易分析图表组件

提供饰品交易数据的可视化图表。
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, Any
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.app.utils.data_formatter import format_price, format_number


def render_trading_analysis_chart(snapshot: Dict[str, Any]) -> None:
    """渲染交易分析图表"""
    if not snapshot:
        st.info("📊 暂无交易数据")
        return

    st.subheader("📊 交易数据分析")

    # 添加数据说明
    st.caption("💡 成交量反映市场活跃度，成交额体现资金流动规模。数据基于实际交易记录统计。")

    # 创建两列布局
    col1, col2 = st.columns(2)

    with col1:
        render_volume_trend_chart(snapshot)

    with col2:
        render_amount_distribution_chart(snapshot)


def render_volume_trend_chart(snapshot: Dict[str, Any]) -> None:
    """渲染成交量趋势图"""
    st.write("**📈 成交量趋势**")
    
    # 准备成交量数据
    volume_data = [
        {"period": "24小时", "volume": snapshot.get('trans_count_24h', 0)},
        {"period": "1天", "volume": snapshot.get('trans_count_1d', 0)},
        {"period": "3天", "volume": snapshot.get('trans_count_3d', 0)},
        {"period": "7天", "volume": snapshot.get('trans_count_7d', 0)},
        {"period": "1月", "volume": snapshot.get('trans_count_1m', 0)},
        {"period": "3月", "volume": snapshot.get('trans_count_3m', 0)}
    ]
    
    # 过滤掉无数据的项
    volume_data = [item for item in volume_data if item["volume"] > 0]
    
    if not volume_data:
        st.info("暂无成交量数据")
        return
    
    df = pd.DataFrame(volume_data)
    
    # 创建柱状图
    fig = px.bar(
        df,
        x='period',
        y='volume',
        title='成交量趋势',
        labels={'period': '时间段', 'volume': '成交量'},
        color='volume',
        color_continuous_scale='Blues'
    )
    
    fig.update_layout(
        height=300,
        showlegend=False,
        margin=dict(l=20, r=20, t=40, b=20)
    )
    
    fig.update_traces(
        hovertemplate='<b>%{x}</b><br>成交量: %{y}笔<extra></extra>'
    )
    
    st.plotly_chart(fig, use_container_width=True)


def render_amount_distribution_chart(snapshot: Dict[str, Any]) -> None:
    """渲染成交额分布图"""
    st.write("**💰 成交额分布**")
    
    # 准备成交额数据
    amount_data = [
        {"period": "24小时", "amount": snapshot.get('trans_amount_24h', 0)},
        {"period": "1天", "amount": snapshot.get('trans_amount_1d', 0)},
        {"period": "3天", "amount": snapshot.get('trans_amount_3d', 0)},
        {"period": "7天", "amount": snapshot.get('trans_amount_7d', 0)},
        {"period": "1月", "amount": snapshot.get('trans_amount_1m', 0)},
        {"period": "3月", "amount": snapshot.get('trans_amount_3m', 0)}
    ]
    
    # 过滤掉无数据的项
    amount_data = [item for item in amount_data if item["amount"] > 0]
    
    if not amount_data:
        st.info("暂无成交额数据")
        return
    
    df = pd.DataFrame(amount_data)
    
    # 创建面积图
    fig = px.area(
        df,
        x='period',
        y='amount',
        title='成交额分布',
        labels={'period': '时间段', 'amount': '成交额'},
        color_discrete_sequence=['#FF6B6B']
    )
    
    fig.update_layout(
        height=300,
        showlegend=False,
        margin=dict(l=20, r=20, t=40, b=20)
    )
    
    fig.update_traces(
        hovertemplate='<b>%{x}</b><br>成交额: ¥%{y:.2f}<extra></extra>'
    )
    
    st.plotly_chart(fig, use_container_width=True)


def render_trading_summary_metrics(snapshot: Dict[str, Any]) -> None:
    """渲染交易摘要指标"""
    st.subheader("📋 交易摘要")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        trans_count_7d = snapshot.get('trans_count_7d', 0)
        trans_count_1d = snapshot.get('trans_count_1d', 0)
        change = trans_count_7d - trans_count_1d if trans_count_1d > 0 else 0
        
        st.metric(
            "7天成交量",
            format_number(trans_count_7d, "笔"),
            delta=f"{change:+d}笔" if change != 0 else None
        )
    
    with col2:
        trans_amount_7d = snapshot.get('trans_amount_7d', 0)
        trans_amount_1d = snapshot.get('trans_amount_1d', 0)
        amount_change = trans_amount_7d - trans_amount_1d if trans_amount_1d > 0 else 0
        
        st.metric(
            "7天成交额",
            format_price(trans_amount_7d),
            delta=format_price(amount_change) if amount_change != 0 else None
        )
    
    with col3:
        # 计算平均成交价
        avg_price = 0
        if trans_count_7d > 0 and trans_amount_7d > 0:
            avg_price = trans_amount_7d / trans_count_7d
        
        st.metric(
            "平均成交价",
            format_price(avg_price)
        )
    
    with col4:
        # 交易活跃度
        activity_level = "低"
        if trans_count_7d > 100:
            activity_level = "高"
        elif trans_count_7d > 20:
            activity_level = "中"
        
        activity_color = {
            "高": "🟢",
            "中": "🟡", 
            "低": "🔴"
        }.get(activity_level, "⚪")
        
        st.metric(
            "交易活跃度",
            f"{activity_color} {activity_level}"
        )
