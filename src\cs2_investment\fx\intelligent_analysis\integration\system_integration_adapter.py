"""
系统集成适配器

负责将智能分析系统与现有系统(UnifiedAnalysisSystem、syncps、ssync)进行无缝集成。
提供统一的接口，确保新旧系统的兼容性和数据流转。
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from ..core.decision_fusion_engine import DecisionFusionEngine
from ..core.multi_timeframe_engine import MultiTimeframeEngine
from ..scheduler.intelligent_monitoring_scheduler import IntelligentMonitoringScheduler
from ...services.investment_recommendation_service import InvestmentRecommendationService
from ...services.analysis_data_service import AnalysisDataService


class SystemIntegrationAdapter:
    """系统集成适配器"""
    
    def __init__(self, data_base_path: str = "data/scraped_data"):
        """
        初始化系统集成适配器
        
        Args:
            data_base_path: 数据基础路径
        """
        self.data_base_path = data_base_path
        self.logger = logger.bind(adapter=self.__class__.__name__)
        
        # 智能分析组件
        self.decision_fusion_engines: Dict[str, DecisionFusionEngine] = {}
        self.multi_timeframe_engines: Dict[str, MultiTimeframeEngine] = {}
        self.monitoring_scheduler: Optional[IntelligentMonitoringScheduler] = None
        
        # 现有系统组件
        self.investment_recommendation_service = InvestmentRecommendationService()
        self.analysis_data_service = AnalysisDataService()
        
        # 集成配置
        self.integration_config = {
            'enable_intelligent_analysis': True,
            'fallback_to_legacy': True,
            'comparison_mode': False,
            'cache_results': True,
            'max_cache_age_hours': 1
        }
        
        # 结果缓存
        self.analysis_cache: Dict[str, Dict] = {}
        
        self.logger.info("系统集成适配器初始化完成")
    
    def initialize_intelligent_components(self):
        """初始化智能分析组件"""
        try:
            self.logger.info("初始化智能分析组件...")
            
            # 初始化监控调度器
            if not self.monitoring_scheduler:
                self.monitoring_scheduler = IntelligentMonitoringScheduler(self.data_base_path)
                self.logger.info("智能监控调度器初始化完成")
            
            self.logger.info("智能分析组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"智能分析组件初始化失败: {e}")
            raise
    
    async def analyze_item_intelligent(self, item_id: str, analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """
        使用智能分析系统分析饰品
        
        Args:
            item_id: 饰品ID
            analysis_type: 分析类型 (comprehensive, strategic, tactical, execution)
            
        Returns:
            Dict: 智能分析结果
        """
        try:
            self.logger.info(f"开始智能分析: {item_id} (类型: {analysis_type})")
            
            # 检查缓存
            cache_key = f"{item_id}_{analysis_type}"
            if self._is_cache_valid(cache_key):
                self.logger.info(f"使用缓存结果: {item_id}")
                return self.analysis_cache[cache_key]
            
            # 获取或创建分析引擎
            if analysis_type == "comprehensive":
                result = await self._run_comprehensive_analysis(item_id)
            elif analysis_type == "strategic":
                result = await self._run_strategic_analysis(item_id)
            elif analysis_type == "tactical":
                result = await self._run_tactical_analysis(item_id)
            elif analysis_type == "execution":
                result = await self._run_execution_analysis(item_id)
            else:
                raise ValueError(f"不支持的分析类型: {analysis_type}")
            
            # 缓存结果
            if self.integration_config['cache_results']:
                self.analysis_cache[cache_key] = result
            
            self.logger.info(f"智能分析完成: {item_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"智能分析失败 ({item_id}): {e}")
            
            # 如果启用了回退机制，使用传统分析
            if self.integration_config['fallback_to_legacy']:
                self.logger.info(f"回退到传统分析: {item_id}")
                return await self._fallback_to_legacy_analysis(item_id)
            else:
                raise
    
    async def analyze_item_legacy(self, item_id: str) -> Dict[str, Any]:
        """
        使用传统系统分析饰品
        
        Args:
            item_id: 饰品ID
            
        Returns:
            Dict: 传统分析结果
        """
        try:
            self.logger.info(f"开始传统分析: {item_id}")
            
            # 使用现有的投资推荐服务
            recommendation = self.investment_recommendation_service.generate_recommendation(item_id)
            
            # 获取分析数据
            analysis_data = self.analysis_data_service.get_analysis_data(item_id)
            
            result = {
                'item_id': item_id,
                'analysis_type': 'legacy',
                'analysis_timestamp': datetime.now(),
                'recommendation': recommendation,
                'analysis_data': analysis_data,
                'success': True
            }
            
            self.logger.info(f"传统分析完成: {item_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"传统分析失败 ({item_id}): {e}")
            return {
                'item_id': item_id,
                'analysis_type': 'legacy',
                'analysis_timestamp': datetime.now(),
                'success': False,
                'error': str(e)
            }
    
    async def compare_analysis_results(self, item_id: str) -> Dict[str, Any]:
        """
        对比智能分析和传统分析结果
        
        Args:
            item_id: 饰品ID
            
        Returns:
            Dict: 对比分析结果
        """
        try:
            self.logger.info(f"开始对比分析: {item_id}")
            
            # 并行执行两种分析
            intelligent_task = self.analyze_item_intelligent(item_id)
            legacy_task = self.analyze_item_legacy(item_id)
            
            intelligent_result, legacy_result = await asyncio.gather(
                intelligent_task, legacy_task, return_exceptions=True
            )
            
            # 处理异常结果
            if isinstance(intelligent_result, Exception):
                intelligent_result = {
                    'success': False,
                    'error': str(intelligent_result)
                }
            
            if isinstance(legacy_result, Exception):
                legacy_result = {
                    'success': False,
                    'error': str(legacy_result)
                }
            
            # 生成对比报告
            comparison_result = self._generate_comparison_report(
                item_id, intelligent_result, legacy_result
            )
            
            self.logger.info(f"对比分析完成: {item_id}")
            return comparison_result
            
        except Exception as e:
            self.logger.error(f"对比分析失败 ({item_id}): {e}")
            return {
                'item_id': item_id,
                'comparison_timestamp': datetime.now(),
                'success': False,
                'error': str(e)
            }
    
    async def _run_comprehensive_analysis(self, item_id: str) -> Dict[str, Any]:
        """运行综合分析"""
        try:
            # 获取决策融合引擎
            fusion_engine = self._get_decision_fusion_engine(item_id)
            
            # 执行智能决策融合
            fusion_result = fusion_engine.execute_intelligent_fusion()
            
            return {
                'item_id': item_id,
                'analysis_type': 'comprehensive',
                'analysis_timestamp': datetime.now(),
                'fusion_result': fusion_result,
                'success': fusion_result.get('success', False)
            }
            
        except Exception as e:
            self.logger.error(f"综合分析失败 ({item_id}): {e}")
            raise
    
    async def _run_strategic_analysis(self, item_id: str) -> Dict[str, Any]:
        """运行战略分析"""
        try:
            # 获取多时间框架引擎
            mtf_engine = self._get_multi_timeframe_engine(item_id)
            
            # 初始化分析器
            if not mtf_engine.initialize_analyzers():
                raise Exception("多时间框架分析器初始化失败")
            
            # 执行战略分析
            if mtf_engine.strategic_analyzer:
                strategic_result = mtf_engine.strategic_analyzer.generate_strategic_analysis_report()
            else:
                raise Exception("战略分析器未初始化")
            
            return {
                'item_id': item_id,
                'analysis_type': 'strategic',
                'analysis_timestamp': datetime.now(),
                'strategic_result': strategic_result,
                'success': True
            }
            
        except Exception as e:
            self.logger.error(f"战略分析失败 ({item_id}): {e}")
            raise
    
    async def _run_tactical_analysis(self, item_id: str) -> Dict[str, Any]:
        """运行战术分析"""
        try:
            # 获取多时间框架引擎
            mtf_engine = self._get_multi_timeframe_engine(item_id)
            
            # 初始化分析器
            if not mtf_engine.initialize_analyzers():
                raise Exception("多时间框架分析器初始化失败")
            
            # 执行战术分析
            if mtf_engine.tactical_analyzer:
                tactical_result = mtf_engine.tactical_analyzer.generate_tactical_analysis_report()
            else:
                raise Exception("战术分析器未初始化")
            
            return {
                'item_id': item_id,
                'analysis_type': 'tactical',
                'analysis_timestamp': datetime.now(),
                'tactical_result': tactical_result,
                'success': True
            }
            
        except Exception as e:
            self.logger.error(f"战术分析失败 ({item_id}): {e}")
            raise
    
    async def _run_execution_analysis(self, item_id: str) -> Dict[str, Any]:
        """运行执行分析"""
        try:
            # 获取多时间框架引擎
            mtf_engine = self._get_multi_timeframe_engine(item_id)
            
            # 初始化分析器
            if not mtf_engine.initialize_analyzers():
                raise Exception("多时间框架分析器初始化失败")
            
            # 执行执行分析
            if mtf_engine.execution_analyzer:
                execution_result = mtf_engine.execution_analyzer.generate_execution_analysis_report()
            else:
                raise Exception("执行分析器未初始化")
            
            return {
                'item_id': item_id,
                'analysis_type': 'execution',
                'analysis_timestamp': datetime.now(),
                'execution_result': execution_result,
                'success': True
            }
            
        except Exception as e:
            self.logger.error(f"执行分析失败 ({item_id}): {e}")
            raise

    async def _fallback_to_legacy_analysis(self, item_id: str) -> Dict[str, Any]:
        """回退到传统分析"""
        try:
            self.logger.warning(f"智能分析失败，回退到传统分析: {item_id}")

            legacy_result = await self.analyze_item_legacy(item_id)

            # 标记为回退结果
            legacy_result['is_fallback'] = True
            legacy_result['fallback_reason'] = '智能分析失败'

            return legacy_result

        except Exception as e:
            self.logger.error(f"传统分析回退也失败 ({item_id}): {e}")
            return {
                'item_id': item_id,
                'analysis_type': 'fallback_failed',
                'analysis_timestamp': datetime.now(),
                'success': False,
                'error': str(e),
                'is_fallback': True
            }

    def _generate_comparison_report(self, item_id: str, intelligent_result: Dict, legacy_result: Dict) -> Dict[str, Any]:
        """生成对比报告"""
        try:
            comparison_report = {
                'item_id': item_id,
                'comparison_timestamp': datetime.now(),
                'intelligent_analysis': intelligent_result,
                'legacy_analysis': legacy_result,
                'comparison_summary': {},
                'recommendations': {}
            }

            # 提取推荐结果进行对比
            intelligent_success = intelligent_result.get('success', False)
            legacy_success = legacy_result.get('success', False)

            if intelligent_success and legacy_success:
                # 对比推荐类型
                intelligent_recommendation = self._extract_recommendation_from_intelligent(intelligent_result)
                legacy_recommendation = self._extract_recommendation_from_legacy(legacy_result)

                comparison_report['comparison_summary'] = {
                    'both_successful': True,
                    'recommendations_match': intelligent_recommendation.get('type') == legacy_recommendation.get('type'),
                    'intelligent_recommendation': intelligent_recommendation,
                    'legacy_recommendation': legacy_recommendation,
                    'confidence_comparison': {
                        'intelligent_confidence': intelligent_recommendation.get('confidence', 0),
                        'legacy_confidence': legacy_recommendation.get('confidence', 0)
                    }
                }

                # 生成综合推荐
                comparison_report['recommendations'] = self._generate_combined_recommendation(
                    intelligent_recommendation, legacy_recommendation
                )

            elif intelligent_success:
                comparison_report['comparison_summary'] = {
                    'both_successful': False,
                    'intelligent_only': True,
                    'recommendation_source': 'intelligent'
                }
                comparison_report['recommendations'] = self._extract_recommendation_from_intelligent(intelligent_result)

            elif legacy_success:
                comparison_report['comparison_summary'] = {
                    'both_successful': False,
                    'legacy_only': True,
                    'recommendation_source': 'legacy'
                }
                comparison_report['recommendations'] = self._extract_recommendation_from_legacy(legacy_result)

            else:
                comparison_report['comparison_summary'] = {
                    'both_successful': False,
                    'both_failed': True
                }
                comparison_report['recommendations'] = {
                    'type': 'HOLD',
                    'confidence': 0,
                    'reason': '两种分析方法均失败'
                }

            return comparison_report

        except Exception as e:
            self.logger.error(f"生成对比报告失败 ({item_id}): {e}")
            return {
                'item_id': item_id,
                'comparison_timestamp': datetime.now(),
                'success': False,
                'error': str(e)
            }

    def _extract_recommendation_from_intelligent(self, intelligent_result: Dict) -> Dict[str, Any]:
        """从智能分析结果中提取推荐"""
        try:
            fusion_result = intelligent_result.get('fusion_result', {})
            final_decision = fusion_result.get('final_decision', {})

            return {
                'type': final_decision.get('recommendation_type', 'HOLD'),
                'confidence': final_decision.get('confidence_level', 0),
                'score': final_decision.get('total_score', 50),
                'risk_level': final_decision.get('risk_level', 'MEDIUM'),
                'investment_horizon': final_decision.get('investment_horizon', 'MEDIUM_TERM'),
                'source': 'intelligent'
            }

        except Exception as e:
            self.logger.error(f"提取智能分析推荐失败: {e}")
            return {
                'type': 'HOLD',
                'confidence': 0,
                'source': 'intelligent',
                'error': str(e)
            }

    def _extract_recommendation_from_legacy(self, legacy_result: Dict) -> Dict[str, Any]:
        """从传统分析结果中提取推荐"""
        try:
            recommendation = legacy_result.get('recommendation', {})

            return {
                'type': recommendation.get('recommendation_type', 'HOLD'),
                'confidence': recommendation.get('confidence_level', 0),
                'score': recommendation.get('total_score', 50),
                'risk_level': recommendation.get('risk_level', 'MEDIUM'),
                'source': 'legacy'
            }

        except Exception as e:
            self.logger.error(f"提取传统分析推荐失败: {e}")
            return {
                'type': 'HOLD',
                'confidence': 0,
                'source': 'legacy',
                'error': str(e)
            }

    def _generate_combined_recommendation(self, intelligent_rec: Dict, legacy_rec: Dict) -> Dict[str, Any]:
        """生成综合推荐"""
        try:
            # 如果两个推荐一致，直接返回
            if intelligent_rec['type'] == legacy_rec['type']:
                return {
                    'type': intelligent_rec['type'],
                    'confidence': max(intelligent_rec['confidence'], legacy_rec['confidence']),
                    'consensus': True,
                    'source': 'combined',
                    'reasoning': '智能分析和传统分析结果一致'
                }

            # 如果推荐不一致，基于置信度选择
            if intelligent_rec['confidence'] > legacy_rec['confidence']:
                primary_rec = intelligent_rec
                secondary_rec = legacy_rec
                primary_source = 'intelligent'
            else:
                primary_rec = legacy_rec
                secondary_rec = intelligent_rec
                primary_source = 'legacy'

            return {
                'type': primary_rec['type'],
                'confidence': primary_rec['confidence'] * 0.8,  # 降低置信度因为存在分歧
                'consensus': False,
                'source': 'combined',
                'primary_source': primary_source,
                'reasoning': f'基于{primary_source}分析的更高置信度选择',
                'alternative_recommendation': secondary_rec['type']
            }

        except Exception as e:
            self.logger.error(f"生成综合推荐失败: {e}")
            return {
                'type': 'HOLD',
                'confidence': 30,
                'consensus': False,
                'source': 'combined',
                'error': str(e)
            }

    def _get_decision_fusion_engine(self, item_id: str) -> DecisionFusionEngine:
        """获取或创建决策融合引擎"""
        if item_id not in self.decision_fusion_engines:
            self.decision_fusion_engines[item_id] = DecisionFusionEngine(item_id, self.data_base_path)

        return self.decision_fusion_engines[item_id]

    def _get_multi_timeframe_engine(self, item_id: str) -> MultiTimeframeEngine:
        """获取或创建多时间框架引擎"""
        if item_id not in self.multi_timeframe_engines:
            self.multi_timeframe_engines[item_id] = MultiTimeframeEngine(item_id, self.data_base_path)

        return self.multi_timeframe_engines[item_id]

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if not self.integration_config['cache_results']:
            return False

        if cache_key not in self.analysis_cache:
            return False

        cached_result = self.analysis_cache[cache_key]
        cache_time = cached_result.get('analysis_timestamp')

        if not cache_time:
            return False

        # 检查缓存是否过期
        if isinstance(cache_time, str):
            cache_time = datetime.fromisoformat(cache_time.replace('Z', '+00:00'))

        max_age = timedelta(hours=self.integration_config['max_cache_age_hours'])
        return datetime.now() - cache_time < max_age

    def clear_cache(self, item_id: str = None):
        """清理缓存"""
        if item_id:
            # 清理特定饰品的缓存
            keys_to_remove = [key for key in self.analysis_cache.keys() if key.startswith(item_id)]
            for key in keys_to_remove:
                self.analysis_cache.pop(key, None)
            self.logger.info(f"已清理饰品 {item_id} 的缓存")
        else:
            # 清理所有缓存
            self.analysis_cache.clear()
            self.logger.info("已清理所有缓存")

    def get_integration_status(self) -> Dict[str, Any]:
        """获取集成状态"""
        return {
            'integration_config': self.integration_config,
            'cache_size': len(self.analysis_cache),
            'active_engines': {
                'decision_fusion_engines': len(self.decision_fusion_engines),
                'multi_timeframe_engines': len(self.multi_timeframe_engines)
            },
            'monitoring_scheduler_status': {
                'initialized': self.monitoring_scheduler is not None,
                'running': self.monitoring_scheduler.is_running if self.monitoring_scheduler else False
            },
            'timestamp': datetime.now()
        }

    def update_integration_config(self, config_updates: Dict[str, Any]):
        """更新集成配置"""
        try:
            for key, value in config_updates.items():
                if key in self.integration_config:
                    old_value = self.integration_config[key]
                    self.integration_config[key] = value
                    self.logger.info(f"配置更新: {key} = {value} (原值: {old_value})")
                else:
                    self.logger.warning(f"未知配置项: {key}")

            # 如果禁用缓存，清理现有缓存
            if not self.integration_config['cache_results']:
                self.clear_cache()

        except Exception as e:
            self.logger.error(f"更新集成配置失败: {e}")
            raise
