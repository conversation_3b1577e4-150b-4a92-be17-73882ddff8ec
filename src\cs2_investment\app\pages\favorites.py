"""
收藏列表页面

提供用户收藏饰品的管理功能。
"""

import streamlit as st
import pandas as pd
from typing import List, Dict, Any, Optional
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.app.services.favorite_service import FavoriteService
from src.cs2_investment.app.utils.data_formatter import format_price, format_percentage, format_item_type, format_quality
from src.cs2_investment.app.components import item_analysis_component
from src.cs2_investment.app.components.unified_item_card import render_item_card
from src.cs2_investment.app.components.unified_item_filter import render_item_filter
from src.cs2_investment.app.services.item_service import ItemService


def handle_search_callback(query_params: Dict[str, Any]):
    """
    处理统一过滤组件的搜索回调

    Args:
        query_params: 标准化的查询参数字典
    """
    if query_params.get('button_clicked'):
        # 保存完整的查询参数到session_state
        st.session_state.favorite_query_params = query_params
        st.session_state.favorite_search_executed = True
        st.session_state.favorite_page = 1  # 重置到第一页

        # 为了向后兼容，仍然保存名称查询
        name_query = query_params.get('name_query')
        if name_query and name_query.strip():
            st.session_state.favorite_search_query = name_query.strip()
        else:
            st.session_state.favorite_search_query = ""


def show_page():
    """显示收藏列表页面"""
    st.title("⭐ 收藏列表")

    try:
        # 初始化服务
        if 'favorite_service' not in st.session_state:
            st.session_state.favorite_service = FavoriteService()

        # 获取收藏统计
        with st.spinner("加载收藏数据..."):
            stats = st.session_state.favorite_service.get_favorite_statistics()

        if stats.get('total_count', 0) == 0:
            show_empty_favorites()
            return



        # 不再需要复杂的筛选选项，统一收藏表已经简化了筛选

        # 显示收藏列表
        show_favorites_list()

    except Exception as e:
        st.error(f"加载收藏页面失败: {str(e)}")
        st.info("请检查数据库连接配置")
        show_empty_favorites()


def show_empty_favorites():
    """显示空收藏列表"""
    st.info("您还没有收藏任何饰品")
    
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        st.markdown("""
        ### 如何添加收藏？
        
        1. 前往 **饰品查询** 页面
        2. 搜索您感兴趣的饰品
        3. 点击饰品卡片上的 ⭐ 按钮
        4. 收藏的饰品将出现在这里
        """)
        
        if st.button("🔍 去查询饰品", type="primary", use_container_width=True):
            st.switch_page("pages/item_query.py")





# show_filter_options 函数已删除，使用统一收藏表的简化筛选


def show_favorites_list():
    """显示收藏列表 - 统一饰品收藏"""
    st.subheader("我的收藏")

    # 搜索筛选条件（使用统一过滤组件）
    with st.expander("筛选条件", expanded=True):
        render_item_filter(
            filter_type='query',
            key_suffix='favorite_page',
            on_filter_change=handle_search_callback
        )

    # 分页参数
    items_per_page = 50
    if 'favorite_page' not in st.session_state:
        st.session_state.favorite_page = 1

    offset = (st.session_state.favorite_page - 1) * items_per_page

    # 获取收藏列表
    with st.spinner("加载收藏列表..."):
        # 获取查询参数
        search_executed = getattr(st.session_state, 'favorite_search_executed', False)
        query_params = getattr(st.session_state, 'favorite_query_params', {})

        if search_executed and query_params:
            # 过滤模式 - 使用完整的查询参数
            favorites = st.session_state.favorite_service.filter_user_favorites(
                user_id="default_user",
                query_params=query_params,
                limit=items_per_page,
                offset=offset
            )

            # 获取过滤结果总数
            total_count = st.session_state.favorite_service.count_filtered_favorites(
                user_id="default_user",
                query_params=query_params
            )
        else:
            # 普通模式 - 显示所有收藏
            favorites = st.session_state.favorite_service.get_user_favorites(
                user_id="default_user",
                limit=items_per_page,
                offset=offset
            )

            # 获取总数
            total_count = st.session_state.favorite_service.get_favorite_count(
                user_id="default_user"
            )

    if not favorites:
        if search_executed and query_params:
            # 构建过滤条件描述
            filter_desc = []
            if query_params.get('name_query'):
                filter_desc.append(f"名称包含'{query_params['name_query']}'")
            if query_params.get('item_types'):
                filter_desc.append(f"类型为{query_params['item_types']}")
            if query_params.get('qualities'):
                filter_desc.append(f"品质为{query_params['qualities']}")
            if query_params.get('rarities'):
                filter_desc.append(f"稀有度为{query_params['rarities']}")
            if query_params.get('arbitrage_threshold'):
                filter_desc.append(f"搬砖率≥{query_params['arbitrage_threshold']:.3f}")

            desc_text = "、".join(filter_desc) if filter_desc else "指定条件"
            st.info(f"未找到符合 {desc_text} 的收藏饰品")
        else:
            st.info("暂无收藏饰品")
        return

    # 显示统计信息
    if search_executed and query_params:
        # 构建过滤条件描述
        filter_desc = []
        if query_params.get('name_query'):
            filter_desc.append(f"名称包含'{query_params['name_query']}'")
        if query_params.get('item_types'):
            filter_desc.append(f"类型为{query_params['item_types']}")
        if query_params.get('qualities'):
            filter_desc.append(f"品质为{query_params['qualities']}")
        if query_params.get('rarities'):
            filter_desc.append(f"稀有度为{query_params['rarities']}")
        if query_params.get('arbitrage_threshold'):
            filter_desc.append(f"搬砖率≥{query_params['arbitrage_threshold']:.3f}")

        desc_text = "、".join(filter_desc) if filter_desc else "指定条件"
        st.write(f"符合 {desc_text} 的收藏：{total_count} 项")
    else:
        st.write(f"共有 {total_count} 项收藏")

    # 分页控制
    total_pages = (total_count + items_per_page - 1) // items_per_page
    if total_pages > 1:
        col1, col2, col3 = st.columns([1, 2, 1])
        with col1:
            if st.button("上一页", disabled=st.session_state.favorite_page <= 1):
                st.session_state.favorite_page -= 1
                st.rerun()

        with col2:
            st.write(f"第 {st.session_state.favorite_page} 页，共 {total_pages} 页")

        with col3:
            if st.button("下一页", disabled=st.session_state.favorite_page >= total_pages):
                st.session_state.favorite_page += 1
                st.rerun()

    # 🚀 性能优化：批量获取所有收藏饰品的完整信息
    if favorites:
        print(f"🚀 [性能优化] 批量获取 {len(favorites)} 个收藏饰品的完整信息")

        # 提取所有饰品ID
        item_ids = [item.get('item_id') for item in favorites if item.get('item_id')]

        # 初始化ItemService
        if 'item_service' not in st.session_state:
            st.session_state.item_service = ItemService()

        # 批量获取完整信息
        full_items_data = st.session_state.item_service.search_items_with_prices(
            item_ids=item_ids,
            limit=len(item_ids)
        )

        # 创建ID到完整数据的映射
        full_items_dict = {item['item_id']: item for item in full_items_data}

        print(f"✅ [性能优化] 批量获取完成，成功获取 {len(full_items_dict)} 个饰品的完整信息")

    # 显示收藏项
    for item in favorites:
        show_favorite_item_optimized(item, full_items_dict.get(item.get('item_id')))


def show_favorite_item(item: Dict):
    """显示单个收藏项 - 使用统一饰品卡片组件"""
    # 初始化ItemService
    if 'item_service' not in st.session_state:
        st.session_state.item_service = ItemService()

    # 根据item_id获取完整的饰品信息（包含搬砖率和平台价格）
    item_id = item.get('item_id')
    if item_id:
        # 使用新的get_item_with_prices方法获取完整信息（包含arbitrage_ratio和platform_prices）
        full_item_data = st.session_state.item_service.get_item_with_prices(item_id)

        if full_item_data:
            # 合并收藏表的数据和饰品详细数据
            merged_data = {
                **full_item_data,  # 饰品的完整信息（name, arbitrage_ratio, platform_prices等）
                **item,  # 收藏表的信息（id, created_at, notes等）
                'item_id': item_id  # 确保item_id正确
            }
        else:
            # 如果获取不到完整信息，使用收藏表的基本信息
            merged_data = item
    else:
        merged_data = item

    # 使用query模式，但是操作按钮改为取消收藏
    custom_actions = [
        {'type': 'delete', 'icon': '🗑️', 'help': '取消收藏'},
        {'type': 'analysis', 'icon': '📊', 'help': '查看分析结果'}
    ]

    render_item_card(
        item_data=merged_data,
        card_type='query',
        actions=custom_actions,
        key_suffix='favorites_page'
    )


def show_favorite_item_optimized(item: Dict, full_item_data: Optional[Dict] = None):
    """显示单个收藏项 - 优化版本，使用预获取的完整数据"""

    if full_item_data:
        # 合并收藏表的数据和饰品详细数据
        merged_data = {
            **full_item_data,  # 饰品的完整信息（包含arbitrage_ratio和platform_prices）
            **item,  # 收藏表的信息（id, created_at, notes等）
            'item_id': item.get('item_id')  # 确保item_id正确
        }
    else:
        # 降级到原有方法（如果批量获取失败）
        print(f"⚠️ [降级] 单独获取饰品信息: {item.get('item_id')}")

        # 初始化ItemService
        if 'item_service' not in st.session_state:
            st.session_state.item_service = ItemService()

        item_id = item.get('item_id')
        if item_id:
            full_item_data = st.session_state.item_service.get_item_with_prices(item_id)
            if full_item_data:
                merged_data = {
                    **full_item_data,
                    **item,
                    'item_id': item_id
                }
            else:
                merged_data = item
        else:
            merged_data = item

    # 使用query模式，但是操作按钮改为取消收藏
    custom_actions = [
        {'type': 'delete', 'icon': '🗑️', 'help': '取消收藏'},
        {'type': 'analysis', 'icon': '📊', 'help': '查看分析结果'}
    ]

    render_item_card(
        item_data=merged_data,
        card_type='query',
        actions=custom_actions,
        key_suffix='favorites_page'
    )


# remove_from_favorites 函数已删除，直接使用 FavoriteService 的方法
