"""
高增长潜力资产筛选算法

识别具有强劲上涨动力的资产。
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from .base_algorithm import BaseScreeningAlgorithm
from ..models.market_snapshot import MarketSnapshot


class GrowthPotentialAlgorithm(BaseScreeningAlgorithm):
    """高增长潜力资产筛选算法"""
    
    def __init__(self):
        super().__init__(
            algorithm_name="GrowthPotentialAlgorithm",
            investment_type="高增长潜力资产",
            version="1.0"
        )
    
    def get_screening_query(self, session: Session):
        """获取高增长潜力资产筛选查询"""
        return self.get_latest_snapshots_query(session)\
            .filter(
                and_(
                    MarketSnapshot.diff_1m > 15,                # 1个月涨幅 > 15%
                    MarketSnapshot.trans_amount_1m > 1000000,   # 1个月成交额 > 100万
                    MarketSnapshot.sell_nums_1m_rate < -5,      # 在售数量减少 > 5%
                    MarketSnapshot.trans_count_1m > 100         # 1个月成交量 > 100
                )
            )\
            .order_by(desc(MarketSnapshot.diff_1m))
    
    def calculate_score(self, snapshot: MarketSnapshot) -> float:
        """计算高增长潜力评分"""
        score = 0.0
        
        # 价格增长评分 (35分)
        if snapshot.diff_1m:
            growth_rate = float(snapshot.diff_1m)
            if growth_rate >= 50:
                score += 35
            elif growth_rate >= 30:
                score += 30
            elif growth_rate >= 20:
                score += 25
            elif growth_rate >= 15:
                score += 20
            else:
                score += 10
        
        # 成交额支撑评分 (25分)
        if snapshot.trans_amount_1m:
            amount_score = min(float(snapshot.trans_amount_1m) / 10000000 * 25, 25)
            score += amount_score
        
        # 供需关系评分 (20分)
        if snapshot.sell_nums_1m_rate:
            supply_decrease = abs(float(snapshot.sell_nums_1m_rate))
            if supply_decrease >= 20:
                score += 20
            elif supply_decrease >= 15:
                score += 16
            elif supply_decrease >= 10:
                score += 12
            elif supply_decrease >= 5:
                score += 8
            else:
                score += 4
        
        # 成交量活跃度评分 (15分)
        if snapshot.trans_count_1m:
            if snapshot.trans_count_1m >= 1000:
                score += 15
            elif snapshot.trans_count_1m >= 500:
                score += 12
            elif snapshot.trans_count_1m >= 200:
                score += 9
            elif snapshot.trans_count_1m >= 100:
                score += 6
            else:
                score += 3
        
        # 短期动量评分 (5分)
        if snapshot.diff_7d and float(snapshot.diff_7d) > 0:
            momentum_score = min(float(snapshot.diff_7d) / 20 * 5, 5)
            score += momentum_score
        
        return min(score, 100.0)
    
    def determine_risk_level(self, snapshot: MarketSnapshot, score: float) -> str:
        """确定高增长资产风险等级"""
        # 高增长资产通常风险较高
        risk_factors = 1  # 基础风险
        
        # 价格波动风险
        if snapshot.diff_1m and float(snapshot.diff_1m) > 100:
            risk_factors += 2  # 涨幅过大风险
        elif snapshot.diff_1m and float(snapshot.diff_1m) > 50:
            risk_factors += 1
        
        # 流动性风险
        if not snapshot.trans_count_1m or snapshot.trans_count_1m < 100:
            risk_factors += 1
        
        # 供应风险
        if not snapshot.sell_nums or snapshot.sell_nums < 10:
            risk_factors += 1
        
        # 热度风险
        if snapshot.hot_rank and snapshot.hot_rank > 500:
            risk_factors += 1
        
        if risk_factors <= 2:
            return "MEDIUM"
        elif risk_factors <= 4:
            return "HIGH"
        else:
            return "HIGH"
    
    def generate_recommendation(self, score: float, risk_level: str) -> str:
        """生成高增长资产投资建议"""
        if score >= 80:
            return "BUY"  # 高分高增长，建议买入
        elif score >= 60:
            return "BUY" if risk_level != "HIGH" else "HOLD"
        elif score >= 40:
            return "HOLD"
        else:
            return "AVOID"
    
    def generate_analysis_summary(self, snapshot: MarketSnapshot, score: float) -> str:
        """生成高增长资产分析摘要"""
        summary_parts = []
        
        # 基础信息
        if snapshot.current_price:
            summary_parts.append(f"价格: ¥{snapshot.current_price}")
        
        # 增长信息
        if snapshot.diff_1m:
            summary_parts.append(f"1月涨幅: +{snapshot.diff_1m:.1f}%")
        
        if snapshot.diff_7d:
            change_desc = "涨" if float(snapshot.diff_7d) > 0 else "跌"
            summary_parts.append(f"7天{change_desc}: {abs(float(snapshot.diff_7d)):.1f}%")
        
        # 成交数据
        if snapshot.trans_amount_1m:
            summary_parts.append(f"1月成交额: ¥{snapshot.trans_amount_1m:,.0f}")
        
        if snapshot.trans_count_1m:
            summary_parts.append(f"1月成交量: {snapshot.trans_count_1m}")
        
        # 供需信息
        if snapshot.sell_nums_1m_rate:
            summary_parts.append(f"在售减少: {abs(float(snapshot.sell_nums_1m_rate)):.1f}%")
        
        # 投资建议
        if score >= 80:
            summary_parts.append("高增长优质标的，上涨动力强劲")
        elif score >= 60:
            summary_parts.append("增长潜力良好，适度关注")
        else:
            summary_parts.append("增长动力一般，谨慎观察")
        
        return "; ".join(summary_parts)
