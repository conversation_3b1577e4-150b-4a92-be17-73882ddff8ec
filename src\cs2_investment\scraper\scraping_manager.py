#!/usr/bin/env python3
"""
爬取管理器 - 实现续爬功能
"""

import sys
import os
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Tuple
from pathlib import Path

# 添加项目根路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from src.cs2_investment.models.scraping_record import ScrapingRecord
from src.cs2_investment.config.database import get_db_session

# 使用统一日志组件
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)


class ScrapingManager:
    """爬取管理器"""
    
    def __init__(self, db_url: str = None):
        """
        初始化爬取管理器

        Args:
            db_url: 数据库连接URL，如果为None则使用系统配置
        """
        if db_url is None:
            # 使用系统统一的数据库配置
            from src.cs2_investment.config.database import create_database_engine, create_session_factory
            self.engine = create_database_engine()
            self.SessionLocal = create_session_factory()
        else:
            # 使用自定义数据库配置
            self.engine = create_engine(db_url, echo=False)
            self.SessionLocal = sessionmaker(bind=self.engine)

        logger.info("爬取管理器初始化完成")
    
    def get_session(self):
        """获取数据库会话"""
        return self.SessionLocal()
    
    def record_scraping_start(self, item_id: str, item_name: str, scraping_type: str = "regular") -> int:
        """
        记录爬取开始
        
        Args:
            item_id: 饰品ID
            item_name: 饰品名称
            scraping_type: 爬取类型 (regular, priority, manual)
            
        Returns:
            记录ID
        """
        try:
            with get_db_session() as session:
                record = ScrapingRecord(
                    item_id=item_id,
                    item_name=item_name,
                    scraping_type=scraping_type,
                    status='running',
                    start_time=datetime.now()
                )
                
                session.add(record)
                session.commit()
                
                logger.info(f"记录爬取开始: {item_name} (ID: {record.id})")
                return record.id
                
        except Exception as e:
            logger.error(f"记录爬取开始失败: {e}")
            return None
    
    def record_scraping_success(self, record_id: int, result_data: Dict = None):
        """
        记录爬取成功
        
        Args:
            record_id: 记录ID
            result_data: 结果数据
        """
        try:
            with get_db_session() as session:
                record = session.query(ScrapingRecord).filter_by(id=record_id).first()
                if record:
                    record.status = 'success'
                    record.end_time = datetime.now()
                    record.duration_seconds = (record.end_time - record.start_time).total_seconds()
                    
                    if result_data:
                        record.result_data = str(result_data)
                    
                    session.commit()
                    logger.info(f"记录爬取成功: {record.item_name} (耗时: {record.duration_seconds:.2f}秒)")
                else:
                    logger.warning(f"未找到记录ID: {record_id}")
                    
        except Exception as e:
            logger.error(f"记录爬取成功失败: {e}")
    
    def record_scraping_error(self, record_id: int, error_message: str):
        """
        记录爬取失败
        
        Args:
            record_id: 记录ID
            error_message: 错误信息
        """
        try:
            with get_db_session() as session:
                record = session.query(ScrapingRecord).filter_by(id=record_id).first()
                if record:
                    record.status = 'failed'
                    record.end_time = datetime.now()
                    record.duration_seconds = (record.end_time - record.start_time).total_seconds()
                    record.error_message = error_message
                    
                    session.commit()
                    logger.warning(f"记录爬取失败: {record.item_name} - {error_message}")
                else:
                    logger.warning(f"未找到记录ID: {record_id}")
                    
        except Exception as e:
            logger.error(f"记录爬取失败失败: {e}")
    
    def get_items_for_daily_scan(self, limit: int = 1000) -> List[Dict]:
        """
        获取需要进行每日扫描的饰品列表
        
        Args:
            limit: 最大返回数量
            
        Returns:
            饰品列表
        """
        try:
            with get_db_session() as session:
                # 这里应该根据实际的业务逻辑来获取需要扫描的饰品
                # 例如：最近更新时间较早的饰品、热门饰品等
                
                # 示例查询（需要根据实际表结构调整）
                query = """
                SELECT item_id, item_name 
                FROM items 
                WHERE last_update_time < DATE_SUB(NOW(), INTERVAL 1 DAY)
                ORDER BY last_update_time ASC
                LIMIT :limit
                """
                
                result = session.execute(query, {'limit': limit})
                items = [{'item_id': row[0], 'item_name': row[1]} for row in result.fetchall()]
                
                logger.info(f"获取到 {len(items)} 个饰品需要每日扫描")
                return items
                
        except Exception as e:
            logger.error(f"获取每日扫描饰品列表失败: {e}")
            return []
    
    def get_items_for_hourly_check(self, limit: int = 100) -> List[Dict]:
        """
        获取需要进行每小时检查的饰品列表
        
        Args:
            limit: 最大返回数量
            
        Returns:
            饰品列表
        """
        try:
            with get_db_session() as session:
                # 获取热门饰品和最近有价格变动的饰品
                query = """
                SELECT item_id, item_name 
                FROM items 
                WHERE is_popular = 1 OR last_price_change > DATE_SUB(NOW(), INTERVAL 6 HOUR)
                ORDER BY popularity_score DESC, last_price_change DESC
                LIMIT :limit
                """
                
                result = session.execute(query, {'limit': limit})
                items = [{'item_id': row[0], 'item_name': row[1]} for row in result.fetchall()]
                
                logger.info(f"获取到 {len(items)} 个饰品需要每小时检查")
                return items
                
        except Exception as e:
            logger.error(f"获取每小时检查饰品列表失败: {e}")
            return []
    
    def get_priority_items(self, limit: int = 20) -> List[Dict]:
        """
        获取高优先级饰品列表
        
        Args:
            limit: 最大返回数量
            
        Returns:
            饰品列表
        """
        try:
            with get_db_session() as session:
                # 获取收藏的饰品或标记为高优先级的饰品
                query = """
                SELECT item_id, item_name 
                FROM items 
                WHERE priority_level = 'high' OR is_favorited = 1
                ORDER BY priority_score DESC
                LIMIT :limit
                """
                
                result = session.execute(query, {'limit': limit})
                items = [{'item_id': row[0], 'item_name': row[1]} for row in result.fetchall()]
                
                logger.info(f"获取到 {len(items)} 个高优先级饰品")
                return items
                
        except Exception as e:
            logger.error(f"获取高优先级饰品列表失败: {e}")
            return []
    
    def record_daily_scan_completion(self):
        """记录每日扫描完成"""
        try:
            with get_db_session() as session:
                # 记录每日扫描完成时间
                query = """
                INSERT INTO daily_scan_log (scan_date, completion_time, status)
                VALUES (:scan_date, :completion_time, 'completed')
                ON DUPLICATE KEY UPDATE 
                completion_time = :completion_time, status = 'completed'
                """
                
                session.execute(query, {
                    'scan_date': date.today(),
                    'completion_time': datetime.now()
                })
                session.commit()
                
                logger.info("记录每日扫描完成")
                
        except Exception as e:
            logger.error(f"记录每日扫描完成失败: {e}")
    
    def get_daily_statistics(self) -> Dict:
        """
        获取每日统计信息
        
        Returns:
            统计信息字典
        """
        try:
            with get_db_session() as session:
                today = date.today()
                
                # 获取今日爬取统计
                query = """
                SELECT 
                    COUNT(*) as total_count,
                    SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_count,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count,
                    AVG(duration_seconds) as avg_duration
                FROM scraping_records 
                WHERE DATE(start_time) = :today
                """
                
                result = session.execute(query, {'today': today}).fetchone()
                
                total = result[0] or 0
                success = result[1] or 0
                failed = result[2] or 0
                avg_duration = result[3] or 0
                
                success_rate = (success / total * 100) if total > 0 else 0
                
                stats = {
                    'processed_items': total,
                    'successful_items': success,
                    'failed_items': failed,
                    'success_rate': success_rate,
                    'average_duration': avg_duration
                }
                
                logger.info(f"今日统计: {stats}")
                return stats
                
        except Exception as e:
            logger.error(f"获取每日统计信息失败: {e}")
            return {}
    
    def cleanup_old_records(self, days: int = 30):
        """
        清理旧记录
        
        Args:
            days: 保留天数
        """
        try:
            with get_db_session() as session:
                cutoff_date = datetime.now() - timedelta(days=days)
                
                deleted_count = session.query(ScrapingRecord).filter(
                    ScrapingRecord.start_time < cutoff_date
                ).delete()
                
                session.commit()

                logger.info(f"清理了 {deleted_count} 条旧记录")

        except Exception as e:
            logger.error(f"清理旧记录失败: {e}")

    def get_failed_items_for_retry(self, max_attempts: int = 10) -> List[Dict]:
        """
        获取需要重试的失败饰品

        Args:
            max_attempts: 最大重试次数

        Returns:
            需要重试的饰品列表
        """
        try:
            with get_db_session() as session:
                # 查询失败次数未超过最大重试次数的排行榜任务
                query = text("""
                SELECT
                    ranking_key,
                    sub_option,
                    time_period,
                    ranking_name,
                    retry_count,
                    last_retry_time
                FROM scraping_records
                WHERE status = 'failed'
                    AND scraping_date = CURDATE()
                    AND retry_count < :max_attempts
                ORDER BY last_retry_time ASC
                LIMIT 100
                """)

                results = session.execute(query, {'max_attempts': max_attempts}).fetchall()

                failed_items = []
                for row in results:
                    failed_items.append({
                        'ranking_key': row[0],
                        'sub_option': row[1],
                        'time_period': row[2],
                        'ranking_name': row[3],
                        'retry_count': row[4],
                        'last_retry_time': row[5]
                    })

                logger.info(f"找到 {len(failed_items)} 个需要重试的失败饰品")
                return failed_items

        except Exception as e:
            logger.error(f"获取失败饰品失败: {e}")
            return []

    def check_and_create_daily_tasks(self) -> bool:
        """
        检查今天是否有爬取任务，如果没有则创建

        Returns:
            是否成功创建或已存在任务
        """
        try:
            with get_db_session() as session:
                today = date.today()

                # 检查今天是否已有爬取记录
                query = text("""
                SELECT COUNT(*) as count
                FROM scraping_records
                WHERE scraping_date = :today
                """)

                result = session.execute(query, {'today': today}).fetchone()
                today_count = result[0] if result else 0

                if today_count > 0:
                    logger.info(f"今天已有 {today_count} 个爬取任务记录")
                    return True

                # 如果今天没有任务，创建排行榜爬取任务
                logger.info("今天没有爬取任务，开始创建排行榜爬取任务...")

                # 定义需要爬取的排行榜类型和时间范围
                ranking_tasks = self._generate_ranking_tasks()

                # 批量插入任务记录
                for task in ranking_tasks:
                    record = ScrapingRecord(
                        ranking_key=task['ranking_key'],
                        sub_option=task['sub_option'],
                        time_period=task['time_period'],
                        ranking_name=task['ranking_name'],
                        scraping_date=today,
                        status='pending',
                        start_time=datetime.now(),
                        total_expected=500  # 每个排行榜预期爬取500条
                    )
                    session.add(record)

                session.commit()
                logger.info(f"成功创建 {len(ranking_tasks)} 个排行榜爬取任务")
                return True

        except Exception as e:
            logger.error(f"检查和创建每日任务失败: {e}")
            return False

    def _generate_ranking_tasks(self) -> List[Dict]:
        """
        生成排行榜爬取任务列表

        基于steamdt_scraper_final.py中的rankings配置

        Returns:
            任务列表
        """
        tasks = []

        # 使用与steamdt_scraper_final.py相同的排行榜配置
        rankings = {
            "price_up": {
                "name": "价格榜-上涨榜",
                "sub_options": ["涨跌榜(百分比)"],
                "time_periods": ["近1天","近3天","近7天", "近15天", "近一个月", "近三个月", "近六个月", "近一年"]
            },
            "price_down": {
                "name": "价格榜-下跌榜",
                "sub_options": ["涨跌榜(百分比)"],
                "time_periods": ["近1天","近3天","近7天", "近15天", "近一个月", "近三个月", "近六个月", "近一年"]
            },
            "inventory": {
                "name": "在售数榜",
                "sub_options": ["在售数变化(百分比)"],
                "time_periods": ["近1天","近3天","近7天", "近15天", "近一个月", "近三个月", "近六个月", "近一年"]
            },
            "transaction": {
                "name": "成交榜",
                "sub_options": ["成交量", "成交额"],
                "time_periods": ["近1天","近3天","近7天", "近15天", "近一个月", "近三个月"]
            },
            "popularity": {
                "name": "饰品热度榜",
                "sub_options": ["热度榜单", "热度上升榜单"],
                "time_periods": ["近1天"]  # 热度榜单只有近1天
            }
        }

        # 生成所有组合的任务
        for ranking_key, config in rankings.items():
            for sub_option in config['sub_options']:
                for time_period in config['time_periods']:
                    task = {
                        'ranking_key': ranking_key,
                        'sub_option': sub_option,
                        'time_period': time_period,
                        'ranking_name': config['name']
                    }
                    tasks.append(task)

        logger.info(f"生成了 {len(tasks)} 个排行榜爬取任务")
        return tasks

    def get_items_for_daily_scraping(self, limit: int = 1000, include_failed: bool = True) -> List[Dict]:
        """
        获取需要进行每日爬取的饰品

        从scraping_records表中获取今天状态为pending或failed的任务

        Args:
            limit: 最大饰品数量
            include_failed: 是否包含失败的任务

        Returns:
            需要爬取的饰品列表
        """
        try:
            with get_db_session() as session:
                today = date.today()

                # 构建查询条件
                if include_failed:
                    # 查询今天状态为pending或failed（重试次数未超限）的爬取任务
                    query = text("""
                    SELECT ranking_key, sub_option, time_period, ranking_name, status, retry_count
                    FROM scraping_records
                    WHERE scraping_date = :today
                        AND (status = 'pending' OR (status = 'failed' AND retry_count < 10))
                    ORDER BY
                        CASE WHEN status = 'failed' THEN 0 ELSE 1 END,  -- 失败任务优先
                        id ASC
                    LIMIT :limit
                    """)
                else:
                    # 只查询今天状态为pending的爬取任务
                    query = text("""
                    SELECT ranking_key, sub_option, time_period, ranking_name, status, retry_count
                    FROM scraping_records
                    WHERE scraping_date = :today
                        AND status = 'pending'
                    ORDER BY id ASC
                    LIMIT :limit
                    """)

                results = session.execute(query, {'today': today, 'limit': limit}).fetchall()

                items = []
                pending_count = 0
                failed_count = 0

                for row in results:
                    items.append({
                        'ranking_key': row[0],
                        'sub_option': row[1],
                        'time_period': row[2],
                        'ranking_name': row[3],
                        'status': row[4],
                        'retry_count': row[5] or 0
                    })

                    if row[4] == 'pending':
                        pending_count += 1
                    elif row[4] == 'failed':
                        failed_count += 1

                logger.info(f"获取到 {len(items)} 个待处理任务 (pending: {pending_count}, failed: {failed_count})")
                return items

        except Exception as e:
            logger.error(f"获取每日爬取饰品失败: {e}")
            return []

    def record_daily_scraping_completion(self, scraping_date: date):
        """
        记录每日爬取完成状态

        Args:
            scraping_date: 爬取日期
        """
        try:
            with get_db_session() as session:
                # 可以在这里记录每日爬取完成的状态
                # 例如插入到一个专门的完成记录表中
                logger.info(f"记录每日爬取完成: {scraping_date}")

        except Exception as e:
            logger.error(f"记录每日爬取完成失败: {e}")

    def create_or_update_record(self, ranking_key: str, sub_option: str,
                               time_period: str, ranking_name: str,
                               expected_count: int = 500) -> int:
        """
        创建或更新爬取记录

        Args:
            ranking_key: 排行榜键
            sub_option: 子选项
            time_period: 时间周期
            ranking_name: 排行榜名称
            expected_count: 预期数量

        Returns:
            记录ID
        """
        try:
            with get_db_session() as session:
                # 使用模型的类方法创建或获取记录
                record = ScrapingRecord.get_or_create_record(
                    session=session,
                    ranking_key=ranking_key,
                    sub_option=sub_option,
                    time_period=time_period,
                    ranking_name=ranking_name,
                    expected_count=expected_count
                )

                logger.info(f"创建或获取记录: {ranking_name} - {sub_option} - {time_period} (ID: {record.id})")
                return record.id

        except Exception as e:
            logger.error(f"创建或更新记录失败: {e}")
            return None

    def start_scraping(self, record_id: int):
        """
        开始爬取

        Args:
            record_id: 记录ID
        """
        try:
            with get_db_session() as session:
                record = session.query(ScrapingRecord).filter_by(id=record_id).first()
                if record:
                    record.start_scraping()
                    session.commit()
                    logger.info(f"开始爬取: {record.ranking_name} (ID: {record_id})")
                else:
                    logger.warning(f"未找到记录ID: {record_id}")

        except Exception as e:
            logger.error(f"开始爬取失败: {e}")

    def complete_scraping(self, record_id: int, total_scraped: int, total_saved: int):
        """
        完成爬取

        Args:
            record_id: 记录ID
            total_scraped: 爬取总数
            total_saved: 保存总数
        """
        try:
            with get_db_session() as session:
                record = session.query(ScrapingRecord).filter_by(id=record_id).first()
                if record:
                    record.complete_scraping(total_scraped, total_saved)
                    session.commit()
                    logger.info(f"完成爬取: {record.ranking_name} - 爬取{total_scraped}条，保存{total_saved}条")
                else:
                    logger.warning(f"未找到记录ID: {record_id}")

        except Exception as e:
            logger.error(f"完成爬取失败: {e}")

    def fail_scraping(self, record_id: int, error_message: str):
        """
        爬取失败

        Args:
            record_id: 记录ID
            error_message: 错误信息
        """
        try:
            with self.get_session() as session:
                record = session.query(ScrapingRecord).filter_by(id=record_id).first()
                if record:
                    record.fail_scraping(error_message)
                    session.commit()
                    logger.warning(f"爬取失败: {record.ranking_name} - {error_message}")
                else:
                    logger.warning(f"未找到记录ID: {record_id}")

        except Exception as e:
            logger.error(f"记录爬取失败失败: {e}")

    def update_progress(self, record_id: int, total_scraped: int, total_saved: int):
        """
        更新爬取进度

        Args:
            record_id: 记录ID
            total_scraped: 爬取总数
            total_saved: 保存总数
        """
        try:
            with self.get_session() as session:
                record = session.query(ScrapingRecord).filter_by(id=record_id).first()
                if record:
                    record.total_scraped = total_scraped
                    record.total_saved = total_saved
                    session.commit()
                    logger.debug(f"更新进度: {record.ranking_name} - 爬取{total_scraped}条，保存{total_saved}条")
                else:
                    logger.warning(f"未找到记录ID: {record_id}")

        except Exception as e:
            logger.error(f"更新进度失败: {e}")

    def get_daily_summary(self) -> Dict:
        """
        获取每日汇总

        Returns:
            汇总信息
        """
        try:
            with self.get_session() as session:
                today = date.today()

                # 统计各种状态的记录数
                query = text("""
                SELECT
                    status,
                    COUNT(*) as count,
                    SUM(total_scraped) as total_scraped,
                    SUM(total_saved) as total_saved
                FROM scraping_records
                WHERE scraping_date = :today
                GROUP BY status
                """)

                results = session.execute(query, {'today': today}).fetchall()

                summary = {
                    'date': today.strftime('%Y-%m-%d'),
                    'total_rankings': 0,
                    'completed': 0,
                    'running': 0,
                    'failed': 0,
                    'pending': 0,
                    'partial': 0,
                    'total_scraped': 0,
                    'total_saved': 0,
                    'completion_rate': 0.0
                }

                for row in results:
                    status, count, scraped, saved = row
                    summary['total_rankings'] += count
                    summary[status] = count
                    summary['total_scraped'] += scraped or 0
                    summary['total_saved'] += saved or 0

                # 计算完成率
                if summary['total_rankings'] > 0:
                    summary['completion_rate'] = (summary['completed'] / summary['total_rankings']) * 100

                return summary

        except Exception as e:
            logger.error(f"获取每日汇总失败: {e}")
            # 返回默认的汇总信息，避免KeyError
            today = date.today()
            return {
                'date': today.strftime('%Y-%m-%d'),
                'total_rankings': 0,
                'completed': 0,
                'running': 0,
                'failed': 0,
                'pending': 0,
                'partial': 0,
                'total_scraped': 0,
                'total_saved': 0,
                'completion_rate': 0.0
            }

    def get_pending_rankings(self) -> List[Dict]:
        """
        获取待处理的排行榜

        Returns:
            待处理排行榜列表
        """
        try:
            with self.get_session() as session:
                today = date.today()

                query = text("""
                SELECT
                    id, ranking_key, sub_option, time_period, ranking_name,
                    status, retry_count, total_expected, total_scraped, total_saved
                FROM scraping_records
                WHERE scraping_date = :today
                    AND status IN ('pending', 'running', 'failed', 'partial')
                ORDER BY status, retry_count ASC
                """)

                results = session.execute(query, {'today': today}).fetchall()

                pending_rankings = []
                for row in results:
                    completion_rate = 0.0
                    if row[7] and row[7] > 0:  # total_expected
                        completion_rate = (row[8] or 0) / row[7] * 100  # total_scraped / total_expected

                    pending_rankings.append({
                        'id': row[0],  # 修改为id以匹配SteamDTScraperFinal的期望
                        'ranking_key': row[1],
                        'sub_option': row[2],
                        'time_period': row[3],
                        'ranking_name': row[4],
                        'status': row[5],
                        'retry_count': row[6],
                        'total_expected': row[7],
                        'total_scraped': row[8],
                        'total_saved': row[9],
                        'completion_rate': completion_rate
                    })

                return pending_rankings

        except Exception as e:
            logger.error(f"获取待处理排行榜失败: {e}")
            return []

def main():
    """主函数 - 用于测试和管理"""
    import argparse
    
    parser = argparse.ArgumentParser(description='爬取管理器')
    parser.add_argument('action', choices=['stats', 'cleanup'], help='操作类型')
    parser.add_argument('--days', type=int, default=30, help='清理天数')
    
    args = parser.parse_args()
    
    manager = ScrapingManager()
    
    if args.action == 'stats':
        stats = manager.get_daily_statistics()
        print("📊 今日爬取统计:")
        print(f"  处理饰品数: {stats.get('processed_items', 0)}")
        print(f"  成功数: {stats.get('successful_items', 0)}")
        print(f"  失败数: {stats.get('failed_items', 0)}")
        print(f"  成功率: {stats.get('success_rate', 0):.1f}%")
        print(f"  平均耗时: {stats.get('average_duration', 0):.2f}秒")
    
    elif args.action == 'cleanup':
        manager.cleanup_old_records(args.days)
        print(f"✅ 清理完成，保留最近 {args.days} 天的记录")

if __name__ == "__main__":
    main()
