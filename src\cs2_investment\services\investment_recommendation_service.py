"""
投资推荐服务

负责生成最终的投资推荐结果。
"""

import uuid
from typing import List, Dict, Optional, Any
from datetime import datetime, date, timedelta
from decimal import Decimal

from ..models.investment_recommendation import InvestmentRecommendation
from ..dao.investment_recommendation_dao import investment_recommendation_dao
from ..utils.logger import get_logger

logger = get_logger(__name__)


class InvestmentRecommendationService:
    """投资推荐服务"""
    
    def __init__(self):
        self.dao = investment_recommendation_dao
    
    def generate_final_recommendation(
        self, 
        item_id: str, 
        screening_results: List[Dict], 
        analysis_result: Dict
    ) -> Optional[InvestmentRecommendation]:
        """生成最终投资推荐"""
        try:
            logger.info(f"🧠 开始生成最终投资推荐: {item_id}")
            
            if not screening_results:
                logger.warning(f"⚠️ 没有筛选结果，无法生成推荐: {item_id}")
                return None
            
            # 1. 计算综合评分
            total_score = self._calculate_total_score(screening_results)
            
            # 2. 确定推荐类型
            recommendation_type = self._determine_recommendation_type(screening_results, total_score)
            
            # 3. 确定风险等级
            risk_level = self._determine_risk_level(screening_results, total_score)
            
            # 4. 计算置信度
            confidence_level = self._calculate_confidence_level(screening_results)
            
            # 5. 生成推荐理由
            recommendation_reason = self._generate_recommendation_reason(
                screening_results, total_score, recommendation_type, risk_level
            )
            
            # 6. 提取市场数据
            market_data = self._extract_market_data(analysis_result)
            
            # 7. 生成会话ID
            session_id = f"rec_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
            
            # 8. 序列化算法详情（处理datetime对象）
            serialized_algorithm_details = self._serialize_algorithm_details(screening_results)

            # 9. 转换为JSON字符串（确保完全序列化）
            import json
            try:
                algorithm_details_json = json.dumps(serialized_algorithm_details, ensure_ascii=False, default=str)
            except Exception as e:
                logger.error(f"JSON序列化失败: {e}")
                algorithm_details_json = json.dumps([
                    {
                        'item_id': result.get('item_id', ''),
                        'investment_type': result.get('investment_type', ''),
                        'score': float(result.get('score', 0)),
                        'recommendation': result.get('recommendation', ''),
                        'risk_level': result.get('risk_level', '')
                    }
                    for result in screening_results
                ], ensure_ascii=False)

            # 10. 创建推荐对象
            recommendation = InvestmentRecommendation(
                item_id=item_id,
                recommendation_date=date.today(),
                recommendation_time=datetime.now(),
                recommendation_session_id=session_id,
                recommendation_type=recommendation_type,
                total_score=Decimal(str(total_score)),
                algorithm_count=len(screening_results),
                algorithm_details=algorithm_details_json,  # 使用JSON字符串
                recommendation_reason=recommendation_reason,
                risk_level=risk_level,
                confidence_level=Decimal(str(confidence_level)),
                current_price=market_data.get('current_price'),
                price_change_7d=market_data.get('price_change_7d'),
                price_change_30d=market_data.get('price_change_30d'),
                volume_30d=market_data.get('volume_30d'),
                amount_30d=market_data.get('amount_30d'),
                hot_rank=market_data.get('hot_rank'),
                valid_until=datetime.now() + timedelta(days=7),  # 7天有效期
                status='ACTIVE'
            )
            
            logger.info(f"✅ 最终投资推荐生成成功: {item_id} - {recommendation_type} - {total_score}")
            return recommendation
            
        except Exception as e:
            logger.error(f"❌ 生成最终投资推荐失败: {item_id}, 错误: {e}")
            return None

    def _serialize_algorithm_details(self, screening_results: List[Dict]) -> List[Dict]:
        """序列化算法详情，处理datetime等不可JSON序列化的对象"""
        try:
            import json
            from datetime import datetime, date

            def json_serializer(obj):
                """JSON序列化器"""
                if isinstance(obj, (datetime, date)):
                    return obj.isoformat()
                elif isinstance(obj, Decimal):
                    return float(obj)
                else:
                    return str(obj)

            # 深度复制并序列化
            serialized_results = []
            for result in screening_results:
                serialized_result = {}
                for key, value in result.items():
                    try:
                        # 尝试JSON序列化测试
                        json.dumps(value)
                        serialized_result[key] = value
                    except (TypeError, ValueError):
                        # 如果不能序列化，使用自定义序列化器
                        serialized_result[key] = json_serializer(value)

                serialized_results.append(serialized_result)

            return serialized_results

        except Exception as e:
            logger.error(f"序列化算法详情失败: {e}")
            # 返回简化版本
            return [
                {
                    'item_id': result.get('item_id', ''),
                    'investment_type': result.get('investment_type', ''),
                    'score': float(result.get('score', 0)),
                    'risk_level': result.get('risk_level', ''),
                    'recommendation': result.get('recommendation', ''),
                    'reason': str(result.get('reason', ''))[:500]  # 限制长度
                }
                for result in screening_results
            ]

    def _calculate_total_score(self, screening_results: List[Dict]) -> float:
        """计算综合评分"""
        if not screening_results:
            return 0.0
        
        # 简单平均算法（可以后续优化为加权平均）
        total = sum(float(result.get('score', 0)) for result in screening_results)
        return round(total / len(screening_results), 2)
    
    def _determine_recommendation_type(self, screening_results: List[Dict], total_score: float) -> str:
        """确定推荐类型"""
        # 统计各种推荐类型的数量
        recommendations = [result.get('recommendation', 'AVOID') for result in screening_results]
        
        buy_count = recommendations.count('BUY')
        hold_count = recommendations.count('HOLD')
        sell_count = recommendations.count('SELL')
        avoid_count = recommendations.count('AVOID')
        
        total_count = len(recommendations)
        
        # 决策逻辑
        if buy_count >= total_count * 0.6:  # 60%以上推荐买入
            return 'BUY'
        elif (buy_count + hold_count) >= total_count * 0.6:  # 60%以上推荐买入或持有
            return 'HOLD'
        elif sell_count >= total_count * 0.5:  # 50%以上推荐卖出
            return 'SELL'
        else:
            return 'AVOID'
    
    def _determine_risk_level(self, screening_results: List[Dict], total_score: float) -> str:
        """确定风险等级"""
        # 统计风险等级
        risk_levels = [result.get('risk_level', 'HIGH') for result in screening_results]
        
        high_count = risk_levels.count('HIGH')
        medium_count = risk_levels.count('MEDIUM')
        low_count = risk_levels.count('LOW')
        
        total_count = len(risk_levels)
        
        # 风险等级决策（偏向保守）
        if high_count >= total_count * 0.3:  # 30%以上高风险
            return 'HIGH'
        elif medium_count >= total_count * 0.5:  # 50%以上中等风险
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def _calculate_confidence_level(self, screening_results: List[Dict]) -> float:
        """计算置信度"""
        if not screening_results:
            return 0.0
        
        # 基于算法数量和评分一致性计算置信度
        algorithm_count = len(screening_results)
        scores = [float(result.get('score', 0)) for result in screening_results]
        
        # 算法数量加分（更多算法 = 更高置信度）
        count_bonus = min(algorithm_count * 10, 30)  # 最多30分
        
        # 评分一致性加分（评分越接近 = 更高置信度）
        if len(scores) > 1:
            score_variance = sum((s - sum(scores)/len(scores))**2 for s in scores) / len(scores)
            consistency_bonus = max(0, 40 - score_variance)  # 最多40分
        else:
            consistency_bonus = 20
        
        # 基础置信度
        base_confidence = 30
        
        total_confidence = min(base_confidence + count_bonus + consistency_bonus, 100)
        return round(total_confidence, 2)
    
    def _generate_recommendation_reason(
        self, 
        screening_results: List[Dict], 
        total_score: float, 
        recommendation_type: str, 
        risk_level: str
    ) -> str:
        """生成推荐理由"""
        try:
            # 统计算法类型
            algorithm_types = [result.get('investment_type', 'Unknown') for result in screening_results]
            algorithm_summary = f"{len(algorithm_types)}种算法分析"
            
            # 评分描述
            if total_score >= 80:
                score_desc = "评分优秀"
            elif total_score >= 60:
                score_desc = "评分良好"
            elif total_score >= 40:
                score_desc = "评分一般"
            else:
                score_desc = "评分较低"
            
            # 推荐类型描述
            type_desc_map = {
                'BUY': '建议买入',
                'HOLD': '建议持有',
                'SELL': '建议卖出',
                'AVOID': '建议避免'
            }
            type_desc = type_desc_map.get(recommendation_type, '无明确建议')
            
            # 风险描述
            risk_desc_map = {
                'LOW': '风险较低',
                'MEDIUM': '风险适中',
                'HIGH': '风险较高'
            }
            risk_desc = risk_desc_map.get(risk_level, '风险未知')
            
            # 组合推荐理由
            reason_parts = [
                f"经过{algorithm_summary}，{score_desc}（{total_score}分）",
                f"{type_desc}，{risk_desc}"
            ]
            
            # 添加具体算法建议（取前3个）
            if screening_results:
                algo_details = []
                for i, result in enumerate(screening_results[:3]):
                    algo_type = result.get('investment_type', 'Unknown')
                    algo_score = result.get('score', 0)
                    algo_details.append(f"{algo_type}({algo_score}分)")
                
                if algo_details:
                    reason_parts.append(f"主要依据: {', '.join(algo_details)}")
            
            return "；".join(reason_parts) + "。"
            
        except Exception as e:
            logger.error(f"生成推荐理由失败: {e}")
            return f"基于{len(screening_results)}种算法分析，综合评分{total_score}分，{type_desc}。"
    
    def _extract_market_data(self, analysis_result: Dict) -> Dict:
        """提取市场数据"""
        try:
            result_data = analysis_result.get('result', {})
            
            return {
                'current_price': self._safe_decimal(result_data.get('current_price')),
                'price_change_7d': self._safe_decimal(result_data.get('price_change_7d')),
                'price_change_30d': self._safe_decimal(result_data.get('price_change_30d')),
                'volume_30d': self._safe_int(result_data.get('volume_30d')),
                'amount_30d': self._safe_decimal(result_data.get('amount_30d')),
                'hot_rank': self._safe_int(result_data.get('hot_rank'))
            }
        except Exception as e:
            logger.error(f"提取市场数据失败: {e}")
            return {}
    
    def _safe_decimal(self, value) -> Optional[Decimal]:
        """安全转换为Decimal"""
        try:
            if value is not None:
                return Decimal(str(value))
        except:
            pass
        return None
    
    def _safe_int(self, value) -> Optional[int]:
        """安全转换为int"""
        try:
            if value is not None:
                return int(value)
        except:
            pass
        return None
    
    def save_recommendation(self, recommendation: InvestmentRecommendation) -> bool:
        """保存推荐"""
        return self.dao.save_recommendation(recommendation)
    
    def get_latest_recommendations(self, limit: int = 50) -> List[InvestmentRecommendation]:
        """获取最新推荐"""
        return self.dao.get_latest_recommendations(limit)
    
    def get_recommendations_by_date(self, target_date: date, limit: int = 100) -> List[InvestmentRecommendation]:
        """获取指定日期的推荐"""
        return self.dao.get_recommendations_by_date(target_date, limit)
