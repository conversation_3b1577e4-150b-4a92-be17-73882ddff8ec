"""
饰品详情服务层

提供饰品详细信息查询和投资评级分析功能。
"""

import streamlit as st
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.item_dao import ItemDAO
from src.cs2_investment.dao.market_snapshot_dao import MarketSnapshotDAO
from src.cs2_investment.config.database import get_db_session
from src.cs2_investment.models.market_snapshot import MarketSnapshot
from sqlalchemy import desc, and_


class ItemDetailService:
    """饰品详情服务类"""
    
    def __init__(self):
        self.item_dao = ItemDAO()
        self.snapshot_dao = MarketSnapshotDAO()
    
    @st.cache_data(ttl=300)  # 5分钟缓存
    def get_comprehensive_data(_self, item_id: str) -> Optional[Dict[str, Any]]:
        """获取饰品的完整详情数据
        
        Args:
            item_id: 饰品ID
            
        Returns:
            包含饰品基础信息、最新快照、投资评级等的完整数据字典
        """
        try:
            # 获取饰品基础信息
            item_data = _self._get_item_basic_info(item_id)
            if not item_data:
                return None
            
            # 获取最新完整快照数据（76个字段）
            latest_snapshot = _self._get_latest_complete_snapshot(item_id)
            
            # 获取价格趋势数据
            price_trend = _self._get_extended_price_trend(item_id, days=365)
            
            # 计算投资评级
            investment_rating = _self._calculate_investment_rating(latest_snapshot)
            
            # 获取关键指标
            key_metrics = _self._calculate_key_metrics(latest_snapshot, price_trend)
            
            return {
                'item': item_data,
                'latest_snapshot': latest_snapshot,
                'price_trend': price_trend,
                'investment_rating': investment_rating,
                'key_metrics': key_metrics,
                'last_updated': datetime.now()
            }
            
        except Exception as e:
            print(f"获取饰品完整数据失败: {e}")
            return None
    
    def _get_item_basic_info(self, item_id: str) -> Optional[Dict[str, Any]]:
        """获取饰品基础信息"""
        try:
            with get_db_session() as session:
                from src.cs2_investment.models.item import Item
                
                item = session.query(Item).filter(Item.item_id == item_id).first()
                if not item:
                    return None
                
                return {
                    'item_id': item.item_id,
                    'name': item.name,
                    'item_type': item.item_type,
                    'quality': item.quality,
                    'rarity': item.rarity,
                    'exterior': getattr(item, 'exterior', None),
                    'image_url': getattr(item, 'image_url', None),
                    'market_hash_name': getattr(item, 'market_hash_name', None),
                    'def_index_name': getattr(item, 'def_index_name', None),
                    'created_at': getattr(item, 'created_at', None),
                    'updated_at': getattr(item, 'updated_at', None)
                }
                
        except Exception as e:
            print(f"获取饰品基础信息失败: {e}")
            return None
    
    def _get_latest_complete_snapshot(self, item_id: str) -> Optional[Dict[str, Any]]:
        """获取最新的完整快照数据（包含所有76个字段）"""
        try:
            with get_db_session() as session:
                snapshot = session.query(MarketSnapshot)\
                    .filter(MarketSnapshot.item_id == item_id)\
                    .order_by(desc(MarketSnapshot.snapshot_time))\
                    .first()
                
                if not snapshot:
                    return None
                
                # 返回完整的快照数据字典
                return {
                    'item_id': snapshot.item_id,
                    'snapshot_date': snapshot.snapshot_date,
                    'snapshot_time': snapshot.snapshot_time,
                    'data_source': snapshot.data_source,
                    
                    # 价格信息
                    'current_price': float(snapshot.current_price) if snapshot.current_price else 0,
                    'diff_1d': float(snapshot.diff_1d) if snapshot.diff_1d else 0,
                    'diff_3d': float(snapshot.diff_3d) if snapshot.diff_3d else 0,
                    'diff_7d': float(snapshot.diff_7d) if snapshot.diff_7d else 0,
                    'diff_15d': float(snapshot.diff_15d) if snapshot.diff_15d else 0,
                    'diff_1m': float(snapshot.diff_1m) if snapshot.diff_1m else 0,
                    'diff_3m': float(snapshot.diff_3m) if snapshot.diff_3m else 0,
                    'diff_6m': float(snapshot.diff_6m) if snapshot.diff_6m else 0,
                    'diff_1y': float(snapshot.diff_1y) if snapshot.diff_1y else 0,
                    
                    # 价格变化金额
                    'diff_1d_price': float(snapshot.diff_1d_price) if snapshot.diff_1d_price else 0,
                    'diff_3d_price': float(snapshot.diff_3d_price) if snapshot.diff_3d_price else 0,
                    'diff_7d_price': float(snapshot.diff_7d_price) if snapshot.diff_7d_price else 0,
                    'diff_15d_price': float(snapshot.diff_15d_price) if snapshot.diff_15d_price else 0,
                    'diff_1m_price': float(snapshot.diff_1m_price) if snapshot.diff_1m_price else 0,
                    'diff_3m_price': float(snapshot.diff_3m_price) if snapshot.diff_3m_price else 0,
                    'diff_6m_price': float(snapshot.diff_6m_price) if snapshot.diff_6m_price else 0,
                    'diff_1y_price': float(snapshot.diff_1y_price) if snapshot.diff_1y_price else 0,
                    
                    # 历史价格
                    'before_1d_price': float(snapshot.before_1d_price) if snapshot.before_1d_price else 0,
                    'before_3d_price': float(snapshot.before_3d_price) if snapshot.before_3d_price else 0,
                    'before_7d_price': float(snapshot.before_7d_price) if snapshot.before_7d_price else 0,
                    'before_15d_price': float(snapshot.before_15d_price) if snapshot.before_15d_price else 0,
                    'before_1m_price': float(snapshot.before_1m_price) if snapshot.before_1m_price else 0,
                    'before_3m_price': float(snapshot.before_3m_price) if snapshot.before_3m_price else 0,
                    'before_6m_price': float(snapshot.before_6m_price) if snapshot.before_6m_price else 0,
                    'before_1y_price': float(snapshot.before_1y_price) if snapshot.before_1y_price else 0,
                    
                    # 成交量信息
                    'trans_count_1d': snapshot.trans_count_1d or 0,
                    'trans_count_3d': snapshot.trans_count_3d or 0,
                    'trans_count_7d': snapshot.trans_count_7d or 0,
                    'trans_count_15d': snapshot.trans_count_15d or 0,
                    'trans_count_1m': snapshot.trans_count_1m or 0,
                    'trans_count_3m': snapshot.trans_count_3m or 0,
                    'trans_count_24h': snapshot.trans_count_24h or 0,
                    'trans_count_48h': snapshot.trans_count_48h or 0,
                    'trans_count_diff_48h': snapshot.trans_count_diff_48h or 0,
                    'trans_count_rate_48h': float(snapshot.trans_count_rate_48h) if snapshot.trans_count_rate_48h else 0,
                    
                    # 成交额信息
                    'trans_amount_1d': float(snapshot.trans_amount_1d) if snapshot.trans_amount_1d else 0,
                    'trans_amount_3d': float(snapshot.trans_amount_3d) if snapshot.trans_amount_3d else 0,
                    'trans_amount_7d': float(snapshot.trans_amount_7d) if snapshot.trans_amount_7d else 0,
                    'trans_amount_15d': float(snapshot.trans_amount_15d) if snapshot.trans_amount_15d else 0,
                    'trans_amount_1m': float(snapshot.trans_amount_1m) if snapshot.trans_amount_1m else 0,
                    'trans_amount_3m': float(snapshot.trans_amount_3m) if snapshot.trans_amount_3m else 0,
                    'trans_amount_24h': float(snapshot.trans_amount_24h) if snapshot.trans_amount_24h else 0,
                    'trans_amount_48h': float(snapshot.trans_amount_48h) if snapshot.trans_amount_48h else 0,
                    
                    # 热度信息
                    'hot_rank': snapshot.hot_rank,
                    'hot_count': snapshot.hot_count,
                    'hot_keep_days': snapshot.hot_keep_days,
                    'hot_rank_change': snapshot.hot_rank_change,
                    
                    # 在售数量信息
                    'sell_nums': snapshot.sell_nums or 0,
                    'sell_nums_1d': snapshot.sell_nums_1d or 0,
                    'sell_nums_3d': snapshot.sell_nums_3d or 0,
                    'sell_nums_7d': snapshot.sell_nums_7d or 0,
                    'sell_nums_15d': snapshot.sell_nums_15d or 0,
                    'sell_nums_1m': snapshot.sell_nums_1m or 0,
                    'sell_nums_3m': snapshot.sell_nums_3m or 0,
                    
                    # 在售数量变化率
                    'sell_nums_1d_rate': float(snapshot.sell_nums_1d_rate) if snapshot.sell_nums_1d_rate else 0,
                    'sell_nums_3d_rate': float(snapshot.sell_nums_3d_rate) if snapshot.sell_nums_3d_rate else 0,
                    'sell_nums_7d_rate': float(snapshot.sell_nums_7d_rate) if snapshot.sell_nums_7d_rate else 0,
                    'sell_nums_15d_rate': float(snapshot.sell_nums_15d_rate) if snapshot.sell_nums_15d_rate else 0,
                    'sell_nums_1m_rate': float(snapshot.sell_nums_1m_rate) if snapshot.sell_nums_1m_rate else 0,
                    'sell_nums_3m_rate': float(snapshot.sell_nums_3m_rate) if snapshot.sell_nums_3m_rate else 0,
                    
                    # 在售数量变化差值
                    'sell_nums_1d_diff': snapshot.sell_nums_1d_diff or 0,
                    'sell_nums_3d_diff': snapshot.sell_nums_3d_diff or 0,
                    'sell_nums_7d_diff': snapshot.sell_nums_7d_diff or 0,
                    'sell_nums_15d_diff': snapshot.sell_nums_15d_diff or 0,
                    'sell_nums_1m_diff': snapshot.sell_nums_1m_diff or 0,
                    'sell_nums_3m_diff': snapshot.sell_nums_3m_diff or 0,
                    
                    # 其他信息
                    'survive_num': snapshot.survive_num or 0,
                    'update_time': snapshot.update_time
                }
                
        except Exception as e:
            print(f"获取完整快照数据失败: {e}")
            return None

    def _get_extended_price_trend(self, item_id: str, days: int = 365) -> List[Dict[str, Any]]:
        """获取扩展的价格趋势数据"""
        try:
            with get_db_session() as session:
                start_time = datetime.now() - timedelta(days=days)
                snapshots = session.query(MarketSnapshot)\
                    .filter(
                        and_(
                            MarketSnapshot.item_id == item_id,
                            MarketSnapshot.snapshot_time >= start_time,
                            MarketSnapshot.current_price.isnot(None)
                        )
                    )\
                    .order_by(MarketSnapshot.snapshot_time.asc()).all()

                return [
                    {
                        'snapshot_time': snapshot.snapshot_time,
                        'snapshot_date': snapshot.snapshot_date,
                        'current_price': float(snapshot.current_price) if snapshot.current_price else 0,
                        'diff_1d': float(snapshot.diff_1d) if snapshot.diff_1d else 0,
                        'diff_7d': float(snapshot.diff_7d) if snapshot.diff_7d else 0,
                        'diff_1m': float(snapshot.diff_1m) if snapshot.diff_1m else 0,
                        'trans_count_1d': snapshot.trans_count_1d or 0,
                        'trans_count_7d': snapshot.trans_count_7d or 0,
                        'trans_amount_1d': float(snapshot.trans_amount_1d) if snapshot.trans_amount_1d else 0,
                        'trans_amount_7d': float(snapshot.trans_amount_7d) if snapshot.trans_amount_7d else 0,
                        'sell_nums': snapshot.sell_nums or 0,
                        'hot_rank': snapshot.hot_rank,
                        'hot_count': snapshot.hot_count
                    }
                    for snapshot in snapshots
                ]

        except Exception as e:
            print(f"获取价格趋势数据失败: {e}")
            return []

    def _calculate_investment_rating(self, snapshot_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """计算投资评级

        基于价格变化、交易量、热度等指标计算A-F等级的投资评级
        """
        if not snapshot_data:
            return {
                'rating': 'F',
                'score': 0,
                'factors': {
                    'price_trend': 0,
                    'liquidity': 0,
                    'popularity': 0,
                    'volatility': 0
                },
                'description': '无数据'
            }

        try:
            # 价格趋势评分 (0-25分)
            price_trend_score = self._calculate_price_trend_score(snapshot_data)

            # 流动性评分 (0-25分)
            liquidity_score = self._calculate_liquidity_score(snapshot_data)

            # 热度评分 (0-25分)
            popularity_score = self._calculate_popularity_score(snapshot_data)

            # 波动性评分 (0-25分)
            volatility_score = self._calculate_volatility_score(snapshot_data)

            # 总分
            total_score = price_trend_score + liquidity_score + popularity_score + volatility_score

            # 评级映射
            if total_score >= 85:
                rating = 'A'
            elif total_score >= 70:
                rating = 'B'
            elif total_score >= 55:
                rating = 'C'
            elif total_score >= 40:
                rating = 'D'
            elif total_score >= 25:
                rating = 'E'
            else:
                rating = 'F'

            return {
                'rating': rating,
                'score': total_score,
                'factors': {
                    'price_trend': price_trend_score,
                    'liquidity': liquidity_score,
                    'popularity': popularity_score,
                    'volatility': volatility_score
                },
                'description': self._get_rating_description(rating, total_score)
            }

        except Exception as e:
            print(f"计算投资评级失败: {e}")
            return {
                'rating': 'F',
                'score': 0,
                'factors': {
                    'price_trend': 0,
                    'liquidity': 0,
                    'popularity': 0,
                    'volatility': 0
                },
                'description': '计算失败'
            }

    def _calculate_price_trend_score(self, snapshot_data: Dict[str, Any]) -> float:
        """计算价格趋势评分"""
        try:
            # 基于多时间段价格变化计算趋势评分
            diff_1d = snapshot_data.get('diff_1d', 0)
            diff_7d = snapshot_data.get('diff_7d', 0)
            diff_1m = snapshot_data.get('diff_1m', 0)
            diff_3m = snapshot_data.get('diff_3m', 0)

            # 修正权重分配：短期20%，中期30%，长期50%（更注重长期趋势）
            weighted_change = (diff_1d * 0.2 + diff_7d * 0.3 + diff_1m * 0.3 + diff_3m * 0.2)

            # 修正评分阈值，更加保守和合理
            if weighted_change > 15:  # 降低满分阈值
                return 25
            elif weighted_change > 8:  # 调整各档位
                return 22
            elif weighted_change > 3:
                return 18
            elif weighted_change > 0:
                return 15
            elif weighted_change > -3:
                return 12
            elif weighted_change > -8:
                return 8
            elif weighted_change > -15:
                return 5
            else:
                return 0

        except Exception:
            return 0

    def _calculate_liquidity_score(self, snapshot_data: Dict[str, Any]) -> float:
        """计算流动性评分"""
        try:
            trans_count_1d = snapshot_data.get('trans_count_1d', 0)
            trans_count_7d = snapshot_data.get('trans_count_7d', 0)
            trans_count_1m = snapshot_data.get('trans_count_1m', 0)

            # 修正计算逻辑：使用7天数据作为主要参考（更稳定）
            daily_volume_7d = trans_count_7d / 7 if trans_count_7d > 0 else 0
            daily_volume_1m = trans_count_1m / 30 if trans_count_1m > 0 else 0

            # 综合评估：7天数据权重70%，1月数据权重30%
            avg_daily_volume = daily_volume_7d * 0.7 + daily_volume_1m * 0.3

            # 修正评分阈值，更加合理
            if avg_daily_volume > 50:  # 降低满分阈值
                return 25
            elif avg_daily_volume > 25:
                return 22
            elif avg_daily_volume > 10:
                return 18
            elif avg_daily_volume > 5:
                return 15
            elif avg_daily_volume > 2:
                return 12
            elif avg_daily_volume > 0.5:
                return 8
            elif avg_daily_volume > 0:
                return 5
            else:
                return 0

        except Exception:
            return 0

    def _calculate_popularity_score(self, snapshot_data: Dict[str, Any]) -> float:
        """计算热度评分"""
        try:
            hot_rank = snapshot_data.get('hot_rank')
            hot_count = snapshot_data.get('hot_count', 0)

            # 热度排名评分
            rank_score = 0
            if hot_rank:
                if hot_rank <= 10:
                    rank_score = 15
                elif hot_rank <= 50:
                    rank_score = 12
                elif hot_rank <= 100:
                    rank_score = 10
                elif hot_rank <= 500:
                    rank_score = 8
                elif hot_rank <= 1000:
                    rank_score = 5
                else:
                    rank_score = 2

            # 热度计数评分
            count_score = 0
            if hot_count > 10000:
                count_score = 10
            elif hot_count > 5000:
                count_score = 8
            elif hot_count > 1000:
                count_score = 6
            elif hot_count > 500:
                count_score = 4
            elif hot_count > 100:
                count_score = 2

            return min(rank_score + count_score, 25)

        except Exception:
            return 0

    def _calculate_volatility_score(self, snapshot_data: Dict[str, Any]) -> float:
        """计算稳定性评分（适度波动得高分，过高或过低波动都不好）"""
        try:
            diff_1d = abs(snapshot_data.get('diff_1d', 0))
            diff_7d = abs(snapshot_data.get('diff_7d', 0))
            diff_1m = abs(snapshot_data.get('diff_1m', 0))

            # 计算加权平均波动率（更重视中长期稳定性）
            avg_volatility = (diff_1d * 0.2 + diff_7d * 0.3 + diff_1m * 0.5)

            # 修正评分逻辑：适度波动（2-8%）得高分
            if 2 <= avg_volatility <= 8:  # 适度波动区间
                return 25
            elif 1 <= avg_volatility < 2 or 8 < avg_volatility <= 12:  # 次优区间
                return 20
            elif 0.5 <= avg_volatility < 1 or 12 < avg_volatility <= 18:  # 一般区间
                return 15
            elif avg_volatility < 0.5:  # 过低波动（可能缺乏流动性）
                return 10
            elif 18 < avg_volatility <= 25:  # 较高波动
                return 8
            elif 25 < avg_volatility <= 35:  # 高波动
                return 5
            else:  # 极高波动
                return 0

        except Exception:
            return 0

    def _get_rating_description(self, rating: str, score: float) -> str:
        """获取评级描述（包含风险提示）"""
        descriptions = {
            'A': f'综合表现优秀 (评分: {score:.1f}) - 各项指标表现良好，但投资有风险，请谨慎决策',
            'B': f'综合表现良好 (评分: {score:.1f}) - 整体指标较好，建议结合个人风险承受能力考虑',
            'C': f'综合表现一般 (评分: {score:.1f}) - 指标表现平平，投资前请充分了解市场风险',
            'D': f'综合表现较差 (评分: {score:.1f}) - 多项指标偏弱，存在较大投资风险',
            'E': f'综合表现差 (评分: {score:.1f}) - 指标表现不佳，投资风险很高',
            'F': f'综合表现极差 (评分: {score:.1f}) - 各项指标都很差，投资风险极高'
        }
        base_desc = descriptions.get(rating, f'评级未知 (评分: {score:.1f})')
        risk_warning = "⚠️ 本评级仅供参考，不构成投资建议。饰品投资存在价格波动风险，请根据自身情况谨慎决策。"
        return f"{base_desc}\n\n{risk_warning}"

    def _calculate_key_metrics(self, snapshot_data: Optional[Dict[str, Any]],
                              price_trend: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算关键指标"""
        if not snapshot_data:
            return {}

        try:
            current_price = snapshot_data.get('current_price', 0)
            survive_num = snapshot_data.get('survive_num', 0)

            # 市值估算（当前价格 * 存世量）
            market_cap = current_price * survive_num if survive_num > 0 else 0

            # 流动性比率（在售数量 / 存世量）
            sell_nums = snapshot_data.get('sell_nums', 0)
            liquidity_ratio = (sell_nums / survive_num * 100) if survive_num > 0 else 0

            # 价格波动率（基于历史数据）
            price_volatility = self._calculate_price_volatility(price_trend)

            # 交易活跃度
            trans_count_7d = snapshot_data.get('trans_count_7d', 0)
            trading_activity = 'high' if trans_count_7d > 100 else 'medium' if trans_count_7d > 20 else 'low'

            return {
                'market_cap': market_cap,
                'liquidity_ratio': liquidity_ratio,
                'price_volatility': price_volatility,
                'trading_activity': trading_activity,
                'rarity_level': self._get_rarity_level(survive_num),
                'trend_direction': self._get_trend_direction(snapshot_data)
            }

        except Exception as e:
            print(f"计算关键指标失败: {e}")
            return {}

    def _calculate_price_volatility(self, price_trend: List[Dict[str, Any]]) -> float:
        """计算价格波动率"""
        if len(price_trend) < 2:
            return 0

        try:
            prices = [item['current_price'] for item in price_trend if item['current_price'] > 0]
            if len(prices) < 2:
                return 0

            # 计算价格变化的标准差
            import statistics
            return statistics.stdev(prices) / statistics.mean(prices) * 100

        except Exception:
            return 0

    def _get_rarity_level(self, survive_num: int) -> str:
        """获取稀有度等级"""
        if survive_num == 0:
            return 'unknown'
        elif survive_num < 100:
            return 'extremely_rare'
        elif survive_num < 500:
            return 'very_rare'
        elif survive_num < 1000:
            return 'rare'
        elif survive_num < 5000:
            return 'uncommon'
        else:
            return 'common'

    def _get_trend_direction(self, snapshot_data: Dict[str, Any]) -> str:
        """获取趋势方向"""
        diff_7d = snapshot_data.get('diff_7d', 0)
        diff_1m = snapshot_data.get('diff_1m', 0)

        if diff_7d > 5 and diff_1m > 5:
            return 'strong_upward'
        elif diff_7d > 0 and diff_1m > 0:
            return 'upward'
        elif diff_7d < -5 and diff_1m < -5:
            return 'strong_downward'
        elif diff_7d < 0 and diff_1m < 0:
            return 'downward'
        else:
            return 'sideways'
