"""
SteamDT ID更新调度器

持续运行的调度器，用于自动获取并更新饰品的SteamDT ID。
"""

import asyncio
import logging
import random
from datetime import datetime
from typing import Dict, Any, Optional, List
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.scraper.steamdt_api import SteamDTAPI
from src.cs2_investment.dao.item_dao import ItemDAO
from src.cs2_investment.config.timer_config import SteamDTIdUpdateSettings
from src.cs2_investment.utils.logger import get_timer_logger


class SteamDTIdUpdateScheduler:
    """SteamDT ID更新调度器"""
    
    def __init__(self, config: SteamDTIdUpdateSettings = None):
        """
        初始化SteamDT ID更新调度器
        
        Args:
            config: 调度器配置
        """
        self.config = config or SteamDTIdUpdateSettings()
        self.logger = get_timer_logger(__name__, "steamdt_id_update")
        
        # 初始化DAO和API
        self.item_dao = ItemDAO()
        self.steamdt_api = SteamDTAPI()
        
        # 任务状态
        self.is_running = False
        self.task_status = {
            'start_time': None,
            'last_cycle_time': None,
            'total_cycles': 0,
            'total_items_processed': 0,
            'total_items_updated': 0,
            'total_errors': 0,
            'current_batch_size': 0,
            'current_batch_progress': 0,
            'last_error': None,
            'success_rate': 0.0
        }
        
        # 运行控制
        self._stop_event = asyncio.Event()
        self._main_task = None
        
    async def start(self):
        """启动调度器"""
        if self.is_running:
            self.logger.warning("SteamDT ID更新调度器已经在运行")
            return
        
        try:
            self.logger.info("🚀 启动SteamDT ID更新调度器")
            self.is_running = True
            self.task_status['start_time'] = datetime.now()
            self._stop_event.clear()
            
            # 启动主循环任务
            self._main_task = asyncio.create_task(self._main_loop())
            
            self.logger.info("✅ SteamDT ID更新调度器启动成功")
            
        except Exception as e:
            self.logger.error(f"❌ 启动SteamDT ID更新调度器失败: {e}")
            self.is_running = False
            raise
    
    async def stop(self):
        """停止调度器"""
        if not self.is_running:
            return
        
        try:
            self.logger.info("⏹️ 停止SteamDT ID更新调度器")
            
            # 设置停止事件
            self._stop_event.set()
            
            # 等待主任务完成
            if self._main_task:
                try:
                    await asyncio.wait_for(self._main_task, timeout=30.0)
                except asyncio.TimeoutError:
                    self.logger.warning("主任务停止超时，强制取消")
                    self._main_task.cancel()
                    try:
                        await self._main_task
                    except asyncio.CancelledError:
                        pass
            
            self.is_running = False
            self.logger.info("✅ SteamDT ID更新调度器已停止")
            
        except Exception as e:
            self.logger.error(f"❌ 停止SteamDT ID更新调度器失败: {e}")
    
    async def _main_loop(self):
        """主循环"""
        try:
            self.logger.info("🚀 SteamDT ID更新调度器主循环开始运行")
            self.logger.info(f"📋 配置信息: 持续模式={self.config.continuous_mode}, 周期间隔={self.config.cycle_interval_minutes}分钟, 批量大小={self.config.batch_size}")

            while not self._stop_event.is_set():
                cycle_start_time = datetime.now()
                self.logger.info("🔄 开始新的SteamDT ID更新周期")
                
                try:
                    # 执行一次完整的更新周期
                    await self._update_cycle()
                    
                    # 更新统计信息
                    self.task_status['last_cycle_time'] = cycle_start_time
                    self.task_status['total_cycles'] += 1
                    
                    # 计算成功率
                    if self.task_status['total_items_processed'] > 0:
                        self.task_status['success_rate'] = (
                            (self.task_status['total_items_processed'] - self.task_status['total_errors']) /
                            self.task_status['total_items_processed'] * 100
                        )
                    
                    self.logger.info(f"✅ 更新周期完成，成功率: {self.task_status['success_rate']:.1f}%")
                    
                except Exception as e:
                    self.logger.error(f"❌ 更新周期执行失败: {e}")
                    self.task_status['total_errors'] += 1
                    self.task_status['last_error'] = str(e)
                
                # 等待下一个周期
                if self.config.continuous_mode and not self._stop_event.is_set():
                    wait_minutes = self.config.cycle_interval_minutes
                    self.logger.info(f"⏰ 等待 {wait_minutes} 分钟后开始下一个周期")
                    
                    try:
                        await asyncio.wait_for(
                            self._stop_event.wait(),
                            timeout=wait_minutes * 60
                        )
                        # 如果等待被中断，说明收到停止信号
                        break
                    except asyncio.TimeoutError:
                        # 超时是正常的，继续下一个周期
                        continue
                else:
                    # 非持续模式，执行一次后退出
                    break
                    
        except asyncio.CancelledError:
            self.logger.info("主循环被取消")
            raise
        except Exception as e:
            self.logger.error(f"❌ 主循环异常: {e}")
            raise
    
    async def _update_cycle(self):
        """执行一次更新周期"""
        self.logger.info("🔍 开始查询需要更新SteamDT ID的饰品...")

        # 查询需要更新的饰品
        items = self._get_items_without_steamdt_id()

        if not items:
            self.logger.info("📭 没有需要更新SteamDT ID的饰品")
            # 获取统计信息
            try:
                stats = self.item_dao.get_steamdt_id_statistics()
                self.logger.info(f"📊 数据库统计: 总饰品={stats.get('total_items', 0)}, 已有SteamDT ID={stats.get('with_steamdt_id', 0)}, 待更新={stats.get('without_steamdt_id', 0)}, 无market_hash_name={stats.get('no_market_hash_name', 0)}")
            except Exception as e:
                self.logger.error(f"❌ 获取统计信息失败: {e}")
            return
        
        self.logger.info(f"📦 找到 {len(items)} 个需要更新SteamDT ID的饰品")
        self.task_status['current_batch_size'] = len(items)
        self.task_status['current_batch_progress'] = 0
        
        # 逐个处理饰品
        for i, item in enumerate(items):
            if self._stop_event.is_set():
                self.logger.info("收到停止信号，中断处理")
                break
            
            try:
                await self._process_single_item(item)
                self.task_status['current_batch_progress'] = i + 1
                
                # 随机等待
                if i < len(items) - 1:  # 最后一个不需要等待
                    await self._random_wait()
                    
            except Exception as e:
                self.logger.error(f"❌ 处理饰品失败 {item.get('item_id', 'unknown')}: {e}")
                self.task_status['total_errors'] += 1
                self.task_status['last_error'] = str(e)
    
    def _get_items_without_steamdt_id(self) -> List[Dict[str, Any]]:
        """获取没有SteamDT ID的饰品"""
        try:
            self.logger.info(f"🔍 正在查询最多 {self.config.batch_size} 个需要更新SteamDT ID的饰品...")
            items = self.item_dao.get_items_without_steamdt_id(self.config.batch_size)
            self.logger.info(f"📋 查询完成，找到 {len(items)} 个饰品")
            return items
        except Exception as e:
            self.logger.error(f"❌ 查询饰品失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return []
    
    async def _process_single_item(self, item: Dict[str, Any]):
        """处理单个饰品"""
        item_id = item.get('item_id')
        market_hash_name = item.get('market_hash_name')
        
        if not market_hash_name:
            self.logger.warning(f"饰品 {item_id} 缺少market_hash_name，跳过")
            return
        
        self.logger.info(f"🔍 处理饰品: {market_hash_name}")
        
        # 重试机制
        for attempt in range(self.config.max_retry_attempts):
            try:
                # 调用SteamDT API获取饰品信息（不再使用异步上下文管理器）
                response = await self.steamdt_api.get_item_info(market_hash_name)

                if response.success:
                    # 解析饰品信息获取ID
                    item_info = self.steamdt_api._parse_item_info(response.data)
                    if item_info and item_info.item_id:
                        # 更新数据库 - 不仅更新SteamDT ID，还要更新其他相关信息
                        self._update_item_with_steamdt_data(item_id, item_info)
                        self.task_status['total_items_updated'] += 1
                        self.logger.info(f"✅ 成功更新饰品 {item_id} 的完整信息，SteamDT ID: {item_info.item_id}")
                        break
                    else:
                        self.logger.warning(f"⚠️ 无法从API响应中解析饰品ID: {market_hash_name}")
                else:
                    self.logger.warning(f"⚠️ API请求失败: {response.error_message}")
                        
            except Exception as e:
                self.logger.error(f"❌ API调用失败 (尝试 {attempt + 1}/{self.config.max_retry_attempts}): {e}")
                if attempt < self.config.max_retry_attempts - 1:
                    # 等待后重试
                    await asyncio.sleep(2 ** attempt)  # 指数退避
                else:
                    # 所有重试都失败
                    raise
        
        self.task_status['total_items_processed'] += 1
    
    def _update_item_with_steamdt_data(self, item_id: str, item_info):
        """更新饰品的SteamDT相关数据"""
        try:
            # 从API原始响应中提取更多信息
            raw_data = getattr(item_info, 'raw_data', {})

            # 提取更新频率信息
            update_level = self._extract_update_level(raw_data)

            # 准备更新数据 - 只更新items表中的相关字段
            item_update_data = {
                'steamdt_item_id': item_info.item_id,
                'image_url': raw_data.get('imageUrl', ''),
                'quality': raw_data.get('qualityName', ''),
                'rarity': item_info.rarity,
                'exterior': item_info.wear,
                'item_type': raw_data.get('itemType', ''),
                'update_level': update_level
            }

            # 使用DAO更新饰品基础信息
            self.item_dao.upsert_item({
                'item_id': item_id,
                **item_update_data
            })

            self.logger.info(f"✅ 成功更新饰品 {item_id} 的items表字段: SteamDT ID={item_info.item_id}, 品质={item_update_data['quality']}, 稀有度={item_update_data['rarity']}")

        except Exception as e:
            self.logger.error(f"❌ 更新饰品items表字段失败 {item_id}: {e}")
            raise

    def _extract_update_level(self, raw_data: dict) -> str:
        """从API响应中提取更新频率信息"""
        try:
            analysis_tags = raw_data.get('analysisTags', [])
            for tag in analysis_tags:
                if tag.get('type') == 'Update_Level':
                    key = tag.get('key', '')
                    # 将英文key转换为中文描述
                    if 'Ten_Minute' in key:
                        return '10分钟级更新'
                    elif 'Hour' in key:
                        return '小时级更新'
                    elif 'Day' in key:
                        return '日级更新'
                    else:
                        return key
            return ''
        except Exception as e:
            self.logger.warning(f"提取更新频率失败: {e}")
            return ''



    async def _random_wait(self):
        """随机等待"""
        wait_seconds = random.randint(
            self.config.min_wait_seconds,
            self.config.max_wait_seconds
        )
        self.logger.debug(f"⏳ 随机等待 {wait_seconds} 秒")
        await asyncio.sleep(wait_seconds)
    
    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        return {
            'is_running': self.is_running,
            'config': {
                'enabled': self.config.enabled,
                'continuous_mode': self.config.continuous_mode,
                'cycle_interval_minutes': self.config.cycle_interval_minutes,
                'batch_size': self.config.batch_size,
                'wait_range': f"{self.config.min_wait_seconds}-{self.config.max_wait_seconds}s",
                'max_retry_attempts': self.config.max_retry_attempts
            },
            'status': self.task_status.copy()
        }
    
    async def trigger_update_now(self) -> Dict[str, Any]:
        """立即触发一次更新（用于测试）"""
        if not self.is_running:
            return {
                'success': False,
                'error': 'Scheduler is not running'
            }
        
        try:
            self.logger.info("🔄 手动触发SteamDT ID更新")
            await self._update_cycle()
            return {
                'success': True,
                'message': 'Update cycle completed successfully'
            }
        except Exception as e:
            self.logger.error(f"❌ 手动更新失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
