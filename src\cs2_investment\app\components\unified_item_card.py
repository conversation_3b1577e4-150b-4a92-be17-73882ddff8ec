"""
统一饰品卡片组件

提供统一的饰品卡片显示功能，支持不同页面的操作按钮差异化。
通过配置驱动的设计模式，整合现有的饰品显示逻辑。
"""

import streamlit as st
from typing import Dict, Any, List, Optional
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.app.utils.data_formatter import (
    format_price, format_percentage, format_item_type,
    format_quality, format_rarity, get_rarity_color, format_number
)
from src.cs2_investment.app.components import item_analysis_component


# 预设配置字典
CARD_TYPE_CONFIGS = {
    'query': {
        'layout_columns': [4, 1.5, 2],
        'show_platform_prices': True,
        'actions': [
            {'type': 'favorite', 'icon': '💖', 'help': '收藏/取消收藏'},
            {'type': 'analysis', 'icon': '📊', 'help': '查看饰品分析结果'}
        ]
    },
    'favorite': {
        'layout_columns': [4, 2, 2, 1, 1],
        'show_platform_prices': False,
        'actions': [
            {'type': 'analysis', 'icon': '📊', 'help': '查看收藏饰品分析结果'},
            {'type': 'delete', 'icon': '🗑️', 'help': '取消收藏'}
        ]
    },
    'holding': {
        'layout_columns': [3, 2, 2, 2, 2, 1, 1, 1],
        'show_platform_prices': False,
        'actions': [
            {'type': 'analysis', 'icon': '📊', 'help': '查看持仓饰品分析结果'},
            {'type': 'trade', 'icon': '💰', 'help': '交易操作'}
        ]
    }
}


def render_item_card(
    item_data: Dict[str, Any],
    card_type: str = 'default',
    actions: Optional[List[Dict]] = None,
    show_platform_prices: Optional[bool] = None,
    layout_columns: Optional[List[float]] = None,
    key_suffix: str = ""
):
    """
    渲染统一的饰品卡片

    Args:
        item_data: 饰品数据字典，必须包含item_id和name字段
        card_type: 卡片类型，可选值：'query', 'favorite', 'holding', 'custom'
        actions: 自定义操作按钮配置列表，格式：[{'type': 'favorite', 'icon': '💖', 'help': '收藏'}]
        show_platform_prices: 是否显示平台价格表格，None时使用预设配置
        layout_columns: 列布局配置，None时使用预设配置
        key_suffix: 唯一标识后缀，用于避免不同页面间的状态冲突
    """

    # 类型检查：确保item_data是字典
    if not isinstance(item_data, dict):
        st.error(f"数据类型错误: 期望字典，实际得到 {type(item_data)}")
        return

    # 获取配置
    config = CARD_TYPE_CONFIGS.get(card_type, {})
    
    # 使用传入参数或预设配置
    final_layout = layout_columns or config.get('layout_columns', [4, 1.5, 2])
    final_actions = actions or config.get('actions', [])
    final_show_prices = show_platform_prices if show_platform_prices is not None else config.get('show_platform_prices', False)
    
    # 基本信息
    item_id = item_data.get('item_id')
    item_name = item_data.get('name') or item_data.get('item_name', '未知饰品')
    
    with st.container():
        # 根据卡片类型渲染不同的内容
        if card_type == 'query':
            _render_query_mode(item_data, final_layout, final_actions, key_suffix)
        elif card_type == 'favorite':
            _render_favorite_mode(item_data, final_layout, final_actions, key_suffix)
        elif card_type == 'holding':
            _render_holding_mode(item_data, final_layout, final_actions, key_suffix)
        else:
            # 自定义模式
            _render_custom_mode(item_data, final_layout, final_actions, key_suffix)
        
        # 显示平台价格表格（如果需要）
        if final_show_prices and card_type == 'query':
            _render_platform_prices_table(item_data)
        
        # 渲染分析对话框（如果有分析按钮）
        if any(action.get('type') == 'analysis' for action in final_actions):
            _render_analysis_dialog(item_data, key_suffix)
        
        # 添加分隔线
        st.divider()


def _render_query_mode(item_data: Dict[str, Any], layout_columns: List[float], actions: List[Dict], key_suffix: str):
    """渲染查询模式的卡片内容"""
    col1, col2, col3 = st.columns(layout_columns)
    
    item_id = item_data.get('item_id')
    item_name = item_data.get('name', '未知饰品')
    
    with col1:
        # 饰品名称（可点击跳转到SteamDT）
        market_hash_name = item_data.get('market_hash_name', '')
        
        if market_hash_name:
            import urllib.parse
            encoded_hash_name = urllib.parse.quote(market_hash_name)
            steamdt_url = f"https://steamdt.com/cs2/{encoded_hash_name}"
            st.markdown(f"**[{item_name}]({steamdt_url})**")
        else:
            st.markdown(f"**{item_name}**")
        
        # ID和hashname显示
        item_id_display = item_data.get('item_id', '未知')
        if market_hash_name:
            id_caption = f"ID: {item_id_display} | {market_hash_name}"
        else:
            id_caption = f"ID: {item_id_display}"
        st.caption(id_caption)
    
    with col2:
        # 搬砖率和求购利润（水平排列）
        arbitrage_col, profit_col = st.columns(2)

        # 搬砖率
        with arbitrage_col:
            arbitrage_ratio = item_data.get('arbitrage_ratio')
            if arbitrage_ratio is not None:
                if arbitrage_ratio >= 0.8:
                    color = "🔴"  # 高搬砖率，红色
                elif arbitrage_ratio >= 0.6:
                    color = "🟡"  # 中等搬砖率，黄色
                else:
                    color = "🟢"  # 低搬砖率，绿色
                st.write(f"{color} {arbitrage_ratio:.4f}")
                st.caption("搬砖率")
            else:
                st.write("📊 无数据")
                st.caption("搬砖率")

        # 求购利润
        with profit_col:
            # 优先使用后端计算的求购利润
            purchase_profit = item_data.get('purchase_profit')
            purchase_profit_rate = item_data.get('purchase_profit_rate')

            if purchase_profit is not None and purchase_profit_rate is not None:
                # 使用后端计算的结果
                if purchase_profit >= 0:
                    display_text = f"¥{purchase_profit:.2f} ({purchase_profit_rate:.1f}%)"
                    color = "🟢" if purchase_profit_rate >= 10 else "🟡"
                else:
                    display_text = f"¥{purchase_profit:.2f} ({purchase_profit_rate:.1f}%)"
                    color = "🔴"
                st.write(f"{color} {display_text}")
                st.caption("求购利润")
            else:
                # 后备方案：使用前端计算
                purchase_profit_info = _calculate_purchase_profit(item_data)
                if purchase_profit_info:
                    profit_text = purchase_profit_info['display_text']
                    profit_color = purchase_profit_info['color']
                    st.write(f"{profit_color} {profit_text}")
                    st.caption("求购利润")
                else:
                    st.write("📊 无法计算")
                    st.caption("求购利润")
    
    with col3:
        # 操作按钮
        if item_id and actions:
            _render_action_buttons(item_data, actions, key_suffix)


def _render_favorite_mode(item_data: Dict[str, Any], layout_columns: List[float], actions: List[Dict], key_suffix: str):
    """渲染收藏模式的卡片内容"""
    col1, col2, col3, col4, col5 = st.columns(layout_columns)
    
    item_id = item_data.get('item_id')
    item_name = item_data.get('item_name') or item_data.get('item_id', '未知饰品')
    
    with col1:
        # 显示饰品名称和ID
        st.markdown(f"**🎮 {item_name}**")
        st.caption(f"ID: {item_data.get('item_id', '未知')}")
    
    with col2:
        # 显示收藏时间
        created_at = item_data.get('created_at')
        if created_at:
            st.write(f"📅 {created_at.strftime('%Y-%m-%d')}")
            st.caption(f"{created_at.strftime('%H:%M:%S')}")
        else:
            st.write("📅 未知时间")
    
    with col3:
        # 显示备注
        notes = item_data.get('notes')
        if notes:
            st.write(f"📝 {notes[:30]}{'...' if len(notes) > 30 else ''}")
        else:
            st.write("📝 无备注")
    
    with col4:
        # 分析按钮
        analysis_action = next((action for action in actions if action.get('type') == 'analysis'), None)
        if analysis_action and item_id:
            _render_analysis_button(item_data, analysis_action, key_suffix)
    
    with col5:
        # 删除收藏按钮
        delete_action = next((action for action in actions if action.get('type') == 'delete'), None)
        if delete_action and item_id:
            _render_delete_button(item_data, delete_action, key_suffix)


def _render_holding_mode(item_data: Dict[str, Any], layout_columns: List[float], actions: List[Dict], key_suffix: str):
    """渲染持仓模式的卡片内容（预留接口）"""
    # 这里可以根据需要实现持仓模式的显示逻辑
    # 暂时使用简化版本
    col1, col2 = st.columns([6, 2])
    
    with col1:
        item_name = item_data.get('item_name') or item_data.get('name', '未知饰品')
        st.markdown(f"**🎮 {item_name}**")
        st.caption(f"ID: {item_data.get('item_id', '未知')}")
    
    with col2:
        if actions:
            _render_action_buttons(item_data, actions, key_suffix)


def _render_custom_mode(item_data: Dict[str, Any], layout_columns: List[float], actions: List[Dict], key_suffix: str):
    """渲染自定义模式的卡片内容"""
    # 基础的两列布局
    col1, col2 = st.columns([6, 2])

    with col1:
        item_name = item_data.get('name') or item_data.get('item_name', '未知饰品')
        st.markdown(f"**{item_name}**")
        st.caption(f"ID: {item_data.get('item_id', '未知')}")

    with col2:
        if actions:
            _render_action_buttons(item_data, actions, key_suffix)


def _render_action_buttons(item_data: Dict[str, Any], actions: List[Dict], key_suffix: str):
    """渲染操作按钮"""
    item_id = item_data.get('item_id')
    if not item_id:
        return

    # 根据按钮数量决定布局
    if len(actions) == 1:
        _render_single_action(item_data, actions[0], key_suffix)
    elif len(actions) == 2:
        action_col1, action_col2 = st.columns(2)
        with action_col1:
            _render_single_action(item_data, actions[0], key_suffix)
        with action_col2:
            _render_single_action(item_data, actions[1], key_suffix)
    else:
        # 多个按钮时使用垂直布局
        for action in actions:
            _render_single_action(item_data, action, key_suffix)


def _render_single_action(item_data: Dict[str, Any], action: Dict, key_suffix: str):
    """渲染单个操作按钮"""
    action_type = action.get('type')
    icon = action.get('icon', '🔘')
    help_text = action.get('help', '')
    item_id = item_data.get('item_id')

    if action_type == 'favorite':
        _render_favorite_button(item_data, action, key_suffix)
    elif action_type == 'analysis':
        _render_analysis_button(item_data, action, key_suffix)
    elif action_type == 'delete':
        _render_delete_button(item_data, action, key_suffix)
    elif action_type == 'trade':
        _render_trade_button(item_data, action, key_suffix)
    elif action_type == 'custom':
        # 自定义按钮
        callback = action.get('callback')
        if st.button(icon, key=f"custom_{item_id}_{key_suffix}", help=help_text):
            if callback:
                callback(item_data)


def _render_favorite_button(item_data: Dict[str, Any], action: Dict, key_suffix: str):
    """渲染收藏按钮"""
    item_id = item_data.get('item_id')
    item_name = item_data.get('name') or item_data.get('item_name', '未知饰品')

    # 初始化收藏服务
    if 'favorite_service' not in st.session_state:
        from src.cs2_investment.app.services.favorite_service import FavoriteService
        st.session_state.favorite_service = FavoriteService()

    # 检查收藏状态 - 优先使用缓存
    if 'favorite_status_cache' in st.session_state and item_id in st.session_state.favorite_status_cache:
        # 使用缓存的收藏状态（性能优化）
        is_favorited = st.session_state.favorite_status_cache[item_id]
    else:
        # 降级到单独查询（兼容性）
        is_favorited = st.session_state.favorite_service.is_favorited(
            user_id="default_user",
            item_id=item_id
        )

    button_text = "💖" if is_favorited else "🤍"
    if st.button(button_text, key=f"fav_{item_id}_{key_suffix}", help="收藏/取消收藏"):
        if is_favorited:
            success = st.session_state.favorite_service.remove_favorite(
                user_id="default_user",
                item_id=item_id
            )
            if success:
                # 更新缓存
                if 'favorite_status_cache' in st.session_state:
                    st.session_state.favorite_status_cache[item_id] = False
                st.success("已取消收藏")
                st.rerun()
        else:
            success = st.session_state.favorite_service.add_favorite(
                user_id="default_user",
                item_id=item_id,
                item_name=item_name
            )
            if success:
                # 更新缓存
                if 'favorite_status_cache' in st.session_state:
                    st.session_state.favorite_status_cache[item_id] = True
                st.success("已添加收藏")
                st.rerun()


def _render_analysis_button(item_data: Dict[str, Any], action: Dict, key_suffix: str):
    """渲染分析按钮"""
    item_id = item_data.get('item_id')
    help_text = action.get('help', '查看分析结果')

    # 直接使用导入的item_analysis_component
    item_analysis_component.render_analysis_button(
        item_id=item_id,
        button_key=f"analysis_{item_id}_{key_suffix}",
        help_text=help_text,
        dialog_key_suffix=f"_{key_suffix}"
    )


def _render_delete_button(item_data: Dict[str, Any], action: Dict, key_suffix: str):
    """渲染删除按钮"""
    item_id = item_data.get('item_id')

    if st.button("🗑️", key=f"remove_{item_data.get('id', item_id)}_{key_suffix}", help="取消收藏"):
        # 初始化收藏服务
        if 'favorite_service' not in st.session_state:
            from src.cs2_investment.app.services.favorite_service import FavoriteService
            st.session_state.favorite_service = FavoriteService()

        success = st.session_state.favorite_service.remove_favorite(
            user_id="default_user",
            item_id=item_id
        )
        if success:
            st.success("已取消收藏")
            st.rerun()
        else:
            st.error("取消收藏失败")


def _render_trade_button(item_data: Dict[str, Any], action: Dict, key_suffix: str):
    """渲染交易按钮（预留接口）"""
    item_id = item_data.get('item_id')
    help_text = action.get('help', '交易操作')

    if st.button("💰", key=f"trade_{item_id}_{key_suffix}", help=help_text):
        st.info("交易功能开发中...")


def _render_analysis_dialog(item_data: Dict[str, Any], key_suffix: str):
    """渲染分析对话框"""
    item_id = item_data.get('item_id')
    item_name = item_data.get('name') or item_data.get('item_name', '未知饰品')

    if item_id:
        # 直接使用导入的item_analysis_component
        item_analysis_component.render_analysis_dialog(
            item_data={
                'item_id': item_id,
                'item_name': item_name
            },
            dialog_key_suffix=f"_{key_suffix}"
        )


def _render_platform_prices_table(item_data: Dict[str, Any]):
    """渲染平台价格信息表格"""

    # 类型检查：确保item_data是字典
    if not isinstance(item_data, dict):
        st.error(f"数据类型错误: 期望字典，实际得到 {type(item_data)}")
        return

    st.markdown("**平台价格信息:**")
    platform_prices = item_data.get('platform_prices', {})
    platform_mappings_raw = item_data.get('platform_mappings', {})
    market_hash_name = item_data.get('market_hash_name', '')

    # 修复platform_mappings的类型问题
    if isinstance(platform_mappings_raw, str):
        try:
            import json
            parsed_mappings = json.loads(platform_mappings_raw) if platform_mappings_raw else []

            # 转换列表格式为字典格式
            if isinstance(parsed_mappings, list):
                platform_mappings = {}
                for mapping in parsed_mappings:
                    if isinstance(mapping, dict) and 'name' in mapping and 'itemId' in mapping:
                        platform_mappings[mapping['name']] = mapping['itemId']
            elif isinstance(parsed_mappings, dict):
                platform_mappings = parsed_mappings
            else:
                platform_mappings = {}
        except (json.JSONDecodeError, TypeError):
            platform_mappings = {}
    else:
        platform_mappings = platform_mappings_raw if isinstance(platform_mappings_raw, dict) else {}

    # 构建表格数据
    import pandas as pd
    import urllib.parse
    from datetime import datetime
    from src.cs2_investment.config.streamlit_config import get_streamlit_config

    # 获取美元汇率配置
    try:
        streamlit_config = get_streamlit_config()
        usd_exchange_rate = streamlit_config.real_exchange_rate
    except Exception as e:
        print(f"获取汇率配置失败: {e}")
        usd_exchange_rate = 7.21  # 默认汇率



    # 定义平台信息
    platforms = [
        ('buff', '🟡 BUFF'),
        ('youpin', '🔵 YOUPIN'),
        ('steam', '🟢 Steam (SteamDT)'),
        ('steam_direct', '🔴 Steam (Direct)')
    ]

    def create_platform_url(platform_key: str, platform_mappings: dict, market_hash_name: str) -> str:
        """创建平台链接URL"""
        if platform_key == 'buff':
            platform_id = platform_mappings.get('BUFF', '')
            if platform_id:
                return f"https://buff.163.com/goods/{platform_id}?from=market#tab=selling"
        elif platform_key == 'youpin':
            platform_id = platform_mappings.get('YOUPIN', '')
            if platform_id:
                return f"https://www.youpin898.com/market/goods-list?listType=10&templateId={platform_id}&gameId=730"
        elif platform_key in ['steam', 'steam_direct']:
            if market_hash_name:
                encoded_hash_name = urllib.parse.quote(market_hash_name)
                return f"https://steamcommunity.com/market/listings/730/{encoded_hash_name}"
        return ""

    # 构建表格数据
    import pandas as pd

    table_data = []

    for platform_key, platform_name in platforms:
        data = platform_prices.get(platform_key, {})
        platform_url = create_platform_url(platform_key, platform_mappings, market_hash_name)

        if data:
            sell_price = data.get('sell_price', 0)
            sell_count = data.get('sell_count', 0)
            bidding_price = data.get('bidding_price', 0)
            bidding_count = data.get('bidding_count', 0)
            update_time = data.get('update_time')
            data_source = data.get('data_source', 'steamdt')

            # 格式化价格显示
            if platform_key == 'steam_direct' or data_source == 'steam_direct':
                # Steam Direct API 使用美元，显示格式：$10.50 (¥75.60)
                sell_price_cny = sell_price * usd_exchange_rate
                bidding_price_cny = bidding_price * usd_exchange_rate

                sell_price_display = f"${sell_price:.2f} (¥{sell_price_cny:.2f})" if sell_price > 0 else "—"
                bidding_price_display = f"${bidding_price:.2f} (¥{bidding_price_cny:.2f})" if bidding_price > 0 else "—"
            else:
                # BUFF、YOUPIN、Steam(steamdt) 都使用人民币
                sell_price_display = f"¥{sell_price:.2f}" if sell_price > 0 else "—"
                bidding_price_display = f"¥{bidding_price:.2f}" if bidding_price > 0 else "—"

            # 格式化更新时间
            if update_time:
                if isinstance(update_time, str):
                    time_str = update_time[:19]  # 截取到秒
                else:
                    time_str = update_time.strftime('%Y-%m-%d %H:%M:%S')
            else:
                time_str = '—'

            table_data.append({
                '链接': platform_url if platform_url else "",
                '平台': platform_name,
                '在售价格': sell_price_display,
                '在售量': str(sell_count) if sell_count > 0 else "—",
                '求购价格': bidding_price_display,
                '求购量': str(bidding_count) if bidding_count > 0 else "—",
                '更新时间': time_str
            })
        else:
            table_data.append({
                '链接': platform_url if platform_url else "",
                '平台': platform_name,
                '在售价格': '—',
                '在售量': '—',
                '求购价格': '—',
                '求购量': '—',
                '更新时间': '—'
            })

    # 显示表格
    if table_data:
        df = pd.DataFrame(table_data)

        # 配置列显示
        column_config = {
            "链接": st.column_config.LinkColumn(
                "链接",
                help="点击跳转到对应平台页面",
                display_text=":material/open_in_new:",
                width="small"
            ),
            "平台": st.column_config.TextColumn("平台", width="medium"),
            "在售价格": st.column_config.TextColumn("在售价格", width="small"),
            "在售量": st.column_config.TextColumn("在售量", width="small"),
            "求购价格": st.column_config.TextColumn("求购价格", width="small"),
            "求购量": st.column_config.TextColumn("求购量", width="small"),
            "更新时间": st.column_config.TextColumn("更新时间", width="large")
        }

        st.dataframe(
            df,
            column_config=column_config,
            hide_index=True,
            use_container_width=True
        )
    else:
        st.info("暂无平台价格数据")





def _calculate_purchase_profit(item_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    计算求购利润

    Args:
        item_data: 饰品数据，包含platform_prices等信息

    Returns:
        Dict包含display_text和color，如果无法计算则返回None
    """
    try:
        # 类型检查：确保item_data是字典
        if not isinstance(item_data, dict):
            return None

        from src.cs2_investment.config.streamlit_config import get_streamlit_config

        platform_prices = item_data.get('platform_prices', {})
        if not platform_prices:
            return None

        # 获取steam_direct的求购价
        steam_direct_data = platform_prices.get('steam_direct', {})
        if not steam_direct_data or not steam_direct_data.get('bidding_price'):
            return None

        steam_bidding_price = float(steam_direct_data.get('bidding_price', 0))
        if steam_bidding_price <= 0:
            return None

        # 获取buff和youpin的最低在售价
        buff_data = platform_prices.get('buff', {})
        youpin_data = platform_prices.get('youpin', {})

        buff_sell_price = float(buff_data.get('sell_price', 0)) if buff_data else 0
        youpin_sell_price = float(youpin_data.get('sell_price', 0)) if youpin_data else 0

        # 找到最低在售价
        valid_prices = [price for price in [buff_sell_price, youpin_sell_price] if price > 0]
        if not valid_prices:
            return None

        min_sell_price = min(valid_prices)

        # 获取卡价（优先使用查询条件中的搬砖卡价，否则使用汇率配置）
        card_price = _get_card_price()
        if card_price is None:
            return None

        # 计算求购利润
        # 利润 = 最低在售价 - (卡价 × steam_direct求购价)
        profit = min_sell_price - (card_price * steam_bidding_price)

        # 计算利润率
        cost = card_price * steam_bidding_price
        profit_rate = (profit / cost * 100) if cost > 0 else 0

        # 格式化显示文本
        if profit >= 0:
            display_text = f"¥{profit:.2f} ({profit_rate:.1f}%)"
            color = "🟢" if profit_rate >= 10 else "🟡"
        else:
            display_text = f"¥{profit:.2f} ({profit_rate:.1f}%)"
            color = "🔴"

        return {
            'display_text': display_text,
            'color': color,
            'profit': profit,
            'profit_rate': profit_rate
        }

    except Exception as e:
        print(f"计算求购利润失败: {e}")
        return None


def _get_card_price() -> Optional[float]:
    """
    获取卡价
    优先使用查询条件中的搬砖卡价，否则使用streamlit配置的汇率

    Returns:
        卡价，如果无法获取则返回None
    """
    try:
        import streamlit as st
        from src.cs2_investment.config.streamlit_config import get_streamlit_config

        # 尝试从session_state获取查询条件中的搬砖卡价
        if hasattr(st, 'session_state') and hasattr(st.session_state, 'query_params'):
            query_params = st.session_state.query_params
            arbitrage_card_price = query_params.get('arbitrage_card_price')
            if arbitrage_card_price is not None and arbitrage_card_price > 0:
                print(f"使用查询条件中的搬砖卡价: {arbitrage_card_price}")
                return float(arbitrage_card_price)

        # 如果没有查询条件中的卡价，使用汇率配置
        streamlit_config = get_streamlit_config()
        print(f"使用配置汇率作为卡价: {streamlit_config.real_exchange_rate}")
        return streamlit_config.real_exchange_rate

    except Exception as e:
        print(f"获取卡价失败: {e}")
        return None
