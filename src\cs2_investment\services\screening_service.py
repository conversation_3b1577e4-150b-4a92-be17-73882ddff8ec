"""
筛选服务

提供投资筛选的完整服务，包括算法执行、结果存储和查询。
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from loguru import logger

from ..algorithms.algorithm_manager import AlgorithmManager
from ..dao.screening_result_dao import ScreeningResultDAO
from ..dao.item_dao import ItemDAO
from ..models.screening_result import ScreeningResult


class ScreeningService:
    """筛选服务"""
    
    def __init__(self):
        self.algorithm_manager = AlgorithmManager()
        self.screening_dao = ScreeningResultDAO()
        self.item_dao = ItemDAO()
        self.logger = logger.bind(service="ScreeningService")
    
    def run_screening(self, investment_types: Optional[List[str]] = None, 
                     limit_per_type: int = 50) -> Dict[str, Any]:
        """运行投资筛选
        
        Args:
            investment_types: 指定要运行的投资类型，None表示运行所有
            limit_per_type: 每种类型的结果数量限制
            
        Returns:
            Dict[str, Any]: 筛选执行结果
        """
        self.logger.info("开始运行投资筛选")
        
        try:
            # 运行筛选算法并保存结果
            result = self.algorithm_manager.run_and_save_screening(
                investment_types=investment_types,
                limit_per_type=limit_per_type
            )
            
            self.logger.info(f"筛选完成: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"运行筛选失败: {e}")
            raise
    
    def get_latest_screening_results(self, investment_type: Optional[str] = None, 
                                   limit: int = 100) -> List[ScreeningResult]:
        """获取最新的筛选结果
        
        Args:
            investment_type: 投资类型过滤
            limit: 结果数量限制
            
        Returns:
            List[ScreeningResult]: 筛选结果列表
        """
        try:
            results = self.screening_dao.get_latest_results(investment_type, limit)
            self.logger.info(f"获取最新筛选结果: {len(results)} 条")
            return results
        except Exception as e:
            self.logger.error(f"获取最新筛选结果失败: {e}")
            raise
    
    def get_top_scored_results(self, investment_type: Optional[str] = None, 
                              limit: int = 20) -> List[ScreeningResult]:
        """获取高评分的筛选结果
        
        Args:
            investment_type: 投资类型过滤
            limit: 结果数量限制
            
        Returns:
            List[ScreeningResult]: 高评分筛选结果列表
        """
        try:
            results = self.screening_dao.get_top_scored_results(investment_type, limit)
            self.logger.info(f"获取高评分筛选结果: {len(results)} 条")
            return results
        except Exception as e:
            self.logger.error(f"获取高评分筛选结果失败: {e}")
            raise
    
    def get_screening_summary(self, days: int = 7) -> Dict[str, Any]:
        """获取筛选结果摘要
        
        Args:
            days: 统计天数
            
        Returns:
            Dict[str, Any]: 筛选结果摘要
        """
        try:
            summary = self.screening_dao.get_investment_summary(days)
            self.logger.info(f"获取筛选摘要: {days} 天")
            return summary
        except Exception as e:
            self.logger.error(f"获取筛选摘要失败: {e}")
            raise
    
    def get_item_screening_history(self, item_id: str, days: int = 30) -> List[ScreeningResult]:
        """获取指定饰品的筛选历史
        
        Args:
            item_id: 饰品ID
            days: 历史天数
            
        Returns:
            List[ScreeningResult]: 筛选历史列表
        """
        try:
            results = self.screening_dao.get_results_by_item(item_id, days)
            self.logger.info(f"获取饰品筛选历史: {item_id}, {len(results)} 条")
            return results
        except Exception as e:
            self.logger.error(f"获取饰品筛选历史失败: {e}")
            raise
    
    def get_investment_opportunities(self, risk_level: Optional[str] = None,
                                   recommendation: Optional[str] = None,
                                   min_score: float = 60.0,
                                   limit: int = 50) -> List[Dict[str, Any]]:
        """获取投资机会
        
        Args:
            risk_level: 风险等级过滤 (LOW/MEDIUM/HIGH)
            recommendation: 推荐等级过滤 (BUY/HOLD/SELL/AVOID)
            min_score: 最低评分
            limit: 结果数量限制
            
        Returns:
            List[Dict[str, Any]]: 投资机会列表（包含饰品信息）
        """
        try:
            # 获取筛选结果
            results = self.screening_dao.get_latest_results(limit=limit * 2)
            
            # 过滤条件
            filtered_results = []
            for result in results:
                if result.score and result.score < min_score:
                    continue
                if risk_level and result.risk_level != risk_level:
                    continue
                if recommendation and result.recommendation != recommendation:
                    continue
                
                filtered_results.append(result)
            
            # 限制数量
            filtered_results = filtered_results[:limit]
            
            # 获取饰品信息并组合
            opportunities = []
            for result in filtered_results:
                item = self.item_dao.get_by_item_id(result.item_id)
                
                opportunity = {
                    'screening_result': result,
                    'item_info': item,
                    'item_id': result.item_id,
                    'item_name': item.name if item else 'Unknown',
                    'investment_type': result.investment_type,
                    'score': result.score,
                    'rank': result.rank,
                    'confidence': result.confidence,
                    'current_price': result.current_price,
                    'price_change_7d': result.price_change_7d,
                    'risk_level': result.risk_level,
                    'recommendation': result.recommendation,
                    'analysis_summary': result.analysis_summary,
                    'screening_time': result.screening_time
                }
                opportunities.append(opportunity)
            
            self.logger.info(f"获取投资机会: {len(opportunities)} 个")
            return opportunities
            
        except Exception as e:
            self.logger.error(f"获取投资机会失败: {e}")
            raise
    
    def cleanup_old_results(self, days: int = 90) -> int:
        """清理旧的筛选结果
        
        Args:
            days: 保留天数
            
        Returns:
            int: 清理的记录数
        """
        try:
            deleted_count = self.screening_dao.cleanup_old_results(days)
            self.logger.info(f"清理旧筛选结果: {deleted_count} 条")
            return deleted_count
        except Exception as e:
            self.logger.error(f"清理旧筛选结果失败: {e}")
            raise
    
    def get_available_investment_types(self) -> List[str]:
        """获取可用的投资类型"""
        return self.algorithm_manager.get_available_algorithms()
    
    def get_algorithm_info(self, investment_type: str) -> Dict[str, Any]:
        """获取算法信息"""
        return self.algorithm_manager.get_algorithm_info(investment_type)
    
    def get_all_algorithms_info(self) -> List[Dict[str, Any]]:
        """获取所有算法信息"""
        return self.algorithm_manager.get_all_algorithms_info()
    
    def update_screening_ranks(self, screening_time: datetime, investment_type: str):
        """更新筛选结果排名
        
        Args:
            screening_time: 筛选时间
            investment_type: 投资类型
        """
        try:
            self.screening_dao.update_result_rank(screening_time, investment_type)
            self.logger.info(f"更新排名完成: {investment_type}")
        except Exception as e:
            self.logger.error(f"更新排名失败: {e}")
            raise
