# 饰品分析通用组件使用指南

## 概述

`ItemAnalysisComponent` 是一个通用的饰品分析组件，从持仓管理页面的分析功能中抽取而来。该组件提供了完整的饰品分析功能，包括：

- 📈 实时分析结果展示
- 📊 常规分析结果展示  
- 🚀 启动新分析功能
- 📋 分析进度跟踪
- 📈 图表展示

## 主要功能

### 1. 分析按钮渲染
提供统一的分析按钮样式和交互逻辑

### 2. 分析对话框
完整的分析结果展示对话框，包含三个标签页：
- 实时分析结果
- 常规分析结果
- 启动新分析

### 3. 任务进度跟踪
实时显示分析任务的执行状态和进度

## 使用方法

### 基本用法

```python
from src.cs2_investment.app.components.item_analysis_component import item_analysis_component

# 1. 在列表中渲染分析按钮
def show_item_card(item_data):
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        st.write(item_data['name'])
    
    with col2:
        # 其他按钮...
        pass
    
    with col3:
        # 渲染分析按钮
        item_analysis_component.render_analysis_button(
            item_id=item_data['item_id'],
            button_key=f"analysis_btn_{item_data['item_id']}",
            help_text="查看分析结果"
        )
    
    # 2. 渲染分析对话框
    item_analysis_component.render_analysis_dialog(
        item_data=item_data,
        dialog_key_suffix="_favorites"  # 用于区分不同页面
    )
```

### 在收藏列表中使用

```python
# favorites.py
from src.cs2_investment.app.components.item_analysis_component import item_analysis_component

def show_favorite_item(favorite_item):
    # ... 其他UI代码 ...
    
    # 分析按钮
    item_analysis_component.render_analysis_button(
        item_id=favorite_item['item_id'],
        button_key=f"fav_analysis_{favorite_item['item_id']}",
        help_text="查看收藏饰品分析结果"
    )
    
    # 分析对话框
    item_analysis_component.render_analysis_dialog(
        item_data={
            'item_id': favorite_item['item_id'],
            'item_name': favorite_item['item_name']
        },
        dialog_key_suffix="_favorites"
    )
```

### 在饰品列表中使用

```python
# items.py  
from src.cs2_investment.app.components.item_analysis_component import item_analysis_component

def show_item_list():
    for item in items:
        # ... 其他UI代码 ...
        
        # 分析按钮
        item_analysis_component.render_analysis_button(
            item_id=item['item_id'],
            button_key=f"item_analysis_{item['item_id']}",
            help_text="查看饰品分析结果"
        )
        
        # 分析对话框
        item_analysis_component.render_analysis_dialog(
            item_data=item,
            dialog_key_suffix="_items"
        )
```

## 参数说明

### render_analysis_button()

- `item_id`: 饰品ID（必需）
- `button_key`: 按钮的唯一key（必需）
- `help_text`: 按钮提示文本（可选，默认："查看分析结果"）

### render_analysis_dialog()

- `item_data`: 饰品数据字典（必需），必须包含：
  - `item_id`: 饰品ID
  - `item_name`: 饰品名称
- `dialog_key_suffix`: 对话框key的后缀（可选），用于区分不同页面的对话框

## 注意事项

### 1. 唯一性保证
- 每个页面使用不同的 `dialog_key_suffix` 来避免key冲突
- 按钮的 `button_key` 必须在页面内唯一

### 2. 数据格式
- `item_data` 必须包含 `item_id` 和 `item_name` 字段
- 其他字段可选，组件会自动处理

### 3. 状态管理
- 组件使用 `st.session_state` 管理对话框显示状态
- 任务进度状态也存储在 `st.session_state` 中

### 4. 服务依赖
- 组件会自动初始化所需的分析服务
- 确保相关的DAO和Service类可以正常导入

## 组件优势

### 1. 代码复用
- 一次编写，多处使用
- 统一的UI风格和交互逻辑

### 2. 维护性
- 集中管理分析功能的代码
- 修改一处，所有使用的地方都会更新

### 3. 扩展性
- 易于添加新的分析类型
- 支持自定义样式和行为

### 4. 一致性
- 所有页面的分析功能保持一致
- 统一的用户体验

## 文件结构

```
src/cs2_investment/app/components/
├── __init__.py
├── item_analysis_component.py  # 主组件文件
└── README.md                   # 使用文档
```

## 依赖关系

组件依赖以下模块：
- `streamlit`: UI框架
- `src.cs2_investment.dao.analysis_result_dao`: 分析结果数据访问
- `src.cs2_investment.app.services.holding_service`: 持仓服务

## 后续扩展

可以考虑添加的功能：
- 分析结果缓存
- 自定义分析参数
- 批量分析功能
- 分析结果导出
- 更多图表类型支持

---

# 统一饰品卡片组件使用指南

## 概述

`unified_item_card` 是一个统一的饰品卡片显示组件，通过配置驱动的设计模式整合了现有的饰品显示逻辑。该组件支持不同页面的操作按钮差异化，提供了灵活的预设配置和自定义选项。

### 🎯 核心特性

- 🔧 **配置驱动**：通过预设配置支持不同的显示模式
- 🎨 **灵活布局**：支持自定义列布局和操作按钮
- 🔄 **功能复用**：整合现有的display_item_row和show_favorite_item逻辑
- 📱 **响应式设计**：适配不同屏幕尺寸和使用场景
- 🛡️ **状态隔离**：通过key_suffix避免不同页面间的状态冲突

### 🚀 主要优势

1. **代码复用**：一次编写，多处使用，减少重复代码
2. **维护性强**：集中管理饰品卡片逻辑，修改一处全局生效
3. **扩展性好**：易于添加新的显示模式和操作按钮
4. **一致性保证**：所有页面的饰品卡片保持统一的视觉效果

## 预设模式

### 1. Query模式 (`card_type='query'`)
- **用途**：饰品查询页面的饰品展示
- **布局**：[4, 1.5, 2] 三列布局
- **功能**：SteamDT链接、搬砖率显示、收藏按钮、分析按钮、平台价格表格

### 2. Favorite模式 (`card_type='favorite'`)
- **用途**：收藏页面的饰品展示
- **布局**：[4, 2, 2, 1, 1] 五列布局
- **功能**：收藏时间、备注信息、分析按钮、删除收藏按钮

### 3. Holding模式 (`card_type='holding'`)
- **用途**：持仓页面的饰品展示（预留接口）
- **布局**：可自定义
- **功能**：持仓信息、交易按钮、分析按钮

### 4. Custom模式 (`card_type='custom'`)
- **用途**：完全自定义的饰品展示
- **布局**：可自定义
- **功能**：完全由actions参数控制

## 使用方法

### 基本用法

```python
from src.cs2_investment.app.components.unified_item_card import render_item_card

# 基本使用 - Query模式
render_item_card(
    item_data=item_data,
    card_type='query',
    key_suffix='page_query'
)
```

### Query模式示例

```python
# 饰品查询页面使用
def show_item_list():
    for item in items:
        render_item_card(
            item_data=item,
            card_type='query',
            key_suffix='query_list'
        )
```

### Favorite模式示例

```python
# 收藏页面使用
def show_favorite_list():
    for favorite_item in favorites:
        render_item_card(
            item_data=favorite_item,
            card_type='favorite',
            key_suffix='favorite_list'
        )
```

### 自定义配置示例

```python
# 自定义操作按钮
custom_actions = [
    {'type': 'favorite', 'icon': '💖', 'help': '收藏/取消收藏'},
    {'type': 'analysis', 'icon': '📊', 'help': '查看分析'},
    {'type': 'custom', 'icon': '⚙️', 'help': '自定义操作', 'callback': my_custom_function}
]

render_item_card(
    item_data=item_data,
    card_type='custom',
    actions=custom_actions,
    layout_columns=[3, 2, 2, 1],
    show_platform_prices=True,
    key_suffix='custom_page'
)
```

## 参数说明

### render_item_card()

```python
def render_item_card(
    item_data: Dict[str, Any],
    card_type: str = 'default',
    actions: Optional[List[Dict]] = None,
    show_platform_prices: Optional[bool] = None,
    layout_columns: Optional[List[float]] = None,
    key_suffix: str = ""
):
```

#### 参数详解

- **item_data** (Dict[str, Any], 必需)
  - 饰品数据字典，必须包含以下字段：
    - `item_id`: 饰品唯一标识符
    - `name` 或 `item_name`: 饰品名称
  - 可选字段（根据模式需要）：
    - `market_hash_name`: Steam市场哈希名称（用于SteamDT链接）
    - `arbitrage_ratio`: 搬砖率（Query模式）
    - `platform_prices`: 平台价格数据（Query模式）
    - `platform_mappings`: 平台映射数据（Query模式）
    - `created_at`: 收藏时间（Favorite模式）
    - `notes`: 备注信息（Favorite模式）

- **card_type** (str, 可选, 默认: 'default')
  - 卡片类型，可选值：
    - `'query'`: 查询模式，适用于饰品查询页面
    - `'favorite'`: 收藏模式，适用于收藏页面
    - `'holding'`: 持仓模式，适用于持仓页面
    - `'custom'`: 自定义模式，完全自定义配置

- **actions** (List[Dict], 可选)
  - 自定义操作按钮配置列表
  - 格式：`[{'type': 'favorite', 'icon': '💖', 'help': '收藏'}]`
  - 支持的操作类型：
    - `'favorite'`: 收藏/取消收藏按钮
    - `'analysis'`: 分析按钮
    - `'delete'`: 删除按钮
    - `'trade'`: 交易按钮
    - `'custom'`: 自定义按钮（需提供callback函数）

- **show_platform_prices** (bool, 可选)
  - 是否显示平台价格表格
  - None时使用预设配置
  - 仅在Query模式下有效

- **layout_columns** (List[float], 可选)
  - 列布局配置，如 [4, 1.5, 2]
  - None时使用预设配置

- **key_suffix** (str, 可选, 默认: "")
  - 唯一标识后缀，用于避免不同页面间的状态冲突
  - 建议使用页面名称，如 "query_page", "favorite_page"

## 操作按钮配置

### 按钮类型说明

```python
# 收藏按钮
{'type': 'favorite', 'icon': '💖', 'help': '收藏/取消收藏'}

# 分析按钮
{'type': 'analysis', 'icon': '📊', 'help': '查看分析结果'}

# 删除按钮
{'type': 'delete', 'icon': '🗑️', 'help': '删除'}

# 交易按钮
{'type': 'trade', 'icon': '💰', 'help': '交易操作'}

# 自定义按钮
{'type': 'custom', 'icon': '⚙️', 'help': '自定义操作', 'callback': my_function}
```

### 自定义回调函数

```python
def my_custom_function(item_data):
    """自定义按钮回调函数"""
    item_name = item_data.get('name', '未知饰品')
    st.info(f"执行自定义操作: {item_name}")

# 使用自定义按钮
actions = [
    {'type': 'custom', 'icon': '⚙️', 'help': '自定义操作', 'callback': my_custom_function}
]
```

## 最佳实践

### 1. 唯一性保证
```python
# ✅ 正确：使用不同的key_suffix避免冲突
render_item_card(item_data, card_type='query', key_suffix='query_page')
render_item_card(item_data, card_type='favorite', key_suffix='favorite_page')

# ❌ 错误：相同的key_suffix可能导致状态冲突
render_item_card(item_data, card_type='query', key_suffix='same')
render_item_card(item_data, card_type='favorite', key_suffix='same')
```

### 2. 数据格式规范
```python
# ✅ 正确：提供完整的必需字段
item_data = {
    'item_id': 'ak47_inheritance_ft',
    'name': 'AK-47 | 传承 (久经沙场)',
    'market_hash_name': 'AK-47 | Inheritance (Field-Tested)',
    # 其他可选字段...
}

# ❌ 错误：缺少必需字段
item_data = {
    'name': 'AK-47 | 传承 (久经沙场)'
    # 缺少item_id字段
}
```

### 3. 预设模式优先
```python
# ✅ 推荐：优先使用预设模式
render_item_card(item_data, card_type='query')

# ⚠️ 谨慎：只在预设模式不满足需求时使用自定义
render_item_card(item_data, card_type='custom', actions=custom_actions)
```

### 4. 性能考虑
```python
# ✅ 正确：在循环外初始化服务
if 'favorite_service' not in st.session_state:
    st.session_state.favorite_service = FavoriteService()

for item in items:
    render_item_card(item, card_type='query', key_suffix=f'query_{item["item_id"]}')
```

## 注意事项

### 1. 状态管理
- 每个页面使用不同的 `key_suffix` 避免状态冲突
- 组件会自动初始化所需的服务（FavoriteService、item_analysis_component）
- 状态存储在 `st.session_state` 中，页面刷新后会重置

### 2. 数据要求
- `item_data` 必须包含 `item_id` 字段
- `name` 或 `item_name` 字段用于显示饰品名称
- 不同模式需要不同的可选字段，详见参数说明

### 3. 平台价格表格
- 仅在Query模式下显示
- 需要 `platform_prices` 和 `platform_mappings` 字段
- 支持BUFF、YOUPIN、Steam等多个平台

### 4. 操作按钮限制
- 最多支持同时显示多个按钮
- 自定义按钮需要提供callback函数
- 按钮key会自动生成，包含item_id和key_suffix

## 依赖关系

组件依赖以下模块：
- `streamlit`: UI框架
- `src.cs2_investment.app.services.favorite_service`: 收藏功能服务
- `src.cs2_investment.app.components.item_analysis_component`: 分析功能组件
- `src.cs2_investment.app.utils.data_formatter`: 数据格式化工具

## 文件结构

```
src/cs2_investment/app/components/
├── __init__.py
├── unified_item_card.py           # 统一饰品卡片组件
├── item_analysis_component.py     # 分析功能组件
├── item_card.py                   # 原始卡片组件（保留）
└── README.md                      # 组件使用文档
```

## 测试页面

可以通过测试页面验证组件功能：
```
src/cs2_investment/app/pages/test_unified_item_card.py
```

测试页面包含：
- 预设模式测试
- 功能对比测试
- 交互功能测试
- 性能测试
- 边界情况测试

## 后续扩展

可以考虑添加的功能：
- 更多预设模式（如推荐模式、搜索结果模式）
- 主题切换支持
- 动画效果
- 批量操作支持
- 导出功能

---

# 统一过滤组件使用指南

## 概述

`unified_item_filter` 是一个统一的饰品过滤组件，采用配置驱动的设计模式，整合了查询页面和收藏页面的过滤功能。该组件支持多种预设模式，提供了灵活的配置选项和标准化的回调机制。

### 🎯 核心特性

- 🔧 **配置驱动**：通过预设配置支持不同的过滤模式
- 🎨 **模块化设计**：7个独立的过滤项函数，易于维护和扩展
- 🔄 **代码复用**：统一的过滤逻辑，减少重复代码
- 📱 **响应式布局**：支持1-6列的灵活布局配置
- 🛡️ **状态隔离**：通过key_suffix避免不同页面间的状态冲突
- 🚀 **标准化回调**：统一的查询参数格式和回调机制

### 🚀 主要优势

1. **代码复用**：一次编写，多处使用，减少重复的过滤逻辑
2. **维护性强**：集中管理过滤功能，修改一处全局生效
3. **扩展性好**：易于添加新的过滤类型和预设模式
4. **一致性保证**：所有页面的过滤功能保持统一的交互体验

## 预设模式

### 1. Query模式 (`filter_type='query'`)
- **用途**：饰品查询页面的完整过滤功能
- **过滤项**：饰品名称、饰品类型、品质、稀有度、价格范围、搬砖卡价、排序方式
- **布局**：3列布局 [1, 1, 1]
- **特性**：包含搬砖卡价计算和提示信息

### 2. Custom模式 (`filter_type='custom'`)
- **用途**：完全自定义的过滤功能
- **过滤项**：由config参数完全控制
- **布局**：可自定义
- **特性**：最大的灵活性，支持任意组合的过滤项

## 使用方法

### 基本用法

```python
from src.cs2_investment.app.components.unified_item_filter import render_item_filter

# 基本使用 - Query模式
render_item_filter(
    filter_type='query',
    key_suffix='query_page',
    on_filter_change=handle_query_callback
)
```

### Query模式示例

```python
# 饰品查询页面使用
def handle_query_callback(query_params: Dict[str, Any]):
    """处理查询回调"""
    if query_params.get('button_clicked'):
        # 保存查询参数
        st.session_state.query_params = query_params
        st.session_state.query_executed = True
        st.session_state.current_page = 1

def show_query_filters():
    render_item_filter(
        filter_type='query',
        key_suffix='query_page',
        on_filter_change=handle_query_callback
    )
```



### 自定义配置示例

```python
# 自定义过滤配置
custom_config = {
    'filters': ['name_query', 'item_type', 'price_range'],
    'layout_columns': [1, 1, 1],
    'show_arbitrage_threshold': False,
    'show_arbitrage_info': False
}

def handle_custom_callback(query_params: Dict[str, Any]):
    """处理自定义回调"""
    if query_params.get('button_clicked'):
        # 处理自定义查询逻辑
        process_custom_query(query_params)

render_item_filter(
    filter_type='custom',
    config=custom_config,
    key_suffix='custom_page',
    on_filter_change=handle_custom_callback
)
```

## 参数说明

### render_item_filter()

```python
def render_item_filter(
    filter_type: str = 'query',
    config: Optional[Dict[str, Any]] = None,
    key_suffix: str = "",
    on_filter_change: Optional[Callable[[Dict[str, Any]], None]] = None
):
```

#### 参数详解

- **filter_type** (str, 可选, 默认: 'query')
  - 过滤器类型，可选值：
    - `'query'`: 查询模式，包含完整的过滤功能
    - `'favorite'`: 收藏模式，仅包含名称搜索
    - `'custom'`: 自定义模式，由config参数控制

- **config** (Dict[str, Any], 可选)
  - 自定义配置字典，用于覆盖预设配置
  - 主要配置项：
    - `filters`: 要显示的过滤项列表
    - `layout_columns`: 列布局配置
    - `show_arbitrage_threshold`: 是否显示搬砖卡价
    - `show_arbitrage_info`: 是否显示搬砖计算信息
    - `button_text`: 查询按钮文本
    - `button_type`: 查询按钮类型

- **key_suffix** (str, 可选, 默认: "")
  - 唯一标识后缀，用于避免不同页面间的状态冲突
  - 建议使用页面名称，如 "query_page", "favorite_page"

- **on_filter_change** (Callable, 可选)
  - 过滤条件变化时的回调函数
  - 函数签名：`(query_params: Dict[str, Any]) -> None`
  - 回调参数包含所有过滤条件和button_clicked标志

## 过滤项说明

### 支持的过滤项

1. **name_query** - 饰品名称搜索
   - 类型：文本输入框
   - 返回：字符串

2. **item_type** - 饰品类型选择
   - 类型：下拉选择框
   - 选项：武器、刀具、手套、印花、挂件等
   - 返回：字符串或None

3. **quality** - 品质选择
   - 类型：下拉选择框
   - 选项：崭新出厂、略有磨损、久经沙场等
   - 返回：字符串或None

4. **rarity** - 稀有度选择
   - 类型：下拉选择框
   - 选项：消费级、工业级、军规级等
   - 返回：字符串或None

5. **price_range** - 价格范围
   - 类型：双滑块
   - 范围：0-100000
   - 返回：元组 (min_price, max_price)

6. **arbitrage_threshold** - 搬砖卡价
   - 类型：数字输入框
   - 返回：浮点数或None

7. **sort_by** - 排序方式
   - 类型：下拉选择框
   - 选项：搬砖率、价格、名称等
   - 返回：字符串

## 回调参数格式

### 标准回调参数

```python
query_params = {
    'button_clicked': True,           # 是否点击了查询按钮
    'name_query': 'AK-47',           # 饰品名称搜索
    'item_type': 'Rifle',            # 饰品类型
    'quality': 'Field-Tested',       # 品质
    'rarity': 'Classified',          # 稀有度
    'price_range': (100, 1000),      # 价格范围
    'arbitrage_threshold': 0.8,      # 搬砖卡价
    'sort_by': 'arbitrage_ratio_desc' # 排序方式
}
```

### 回调处理示例

```python
def handle_filter_callback(query_params: Dict[str, Any]):
    """处理过滤回调的标准模式"""
    if not query_params.get('button_clicked'):
        return

    # 提取查询参数
    name_query = query_params.get('name_query', '').strip()
    item_type = query_params.get('item_type')
    quality = query_params.get('quality')
    rarity = query_params.get('rarity')
    price_range = query_params.get('price_range', (0, 100000))
    arbitrage_threshold = query_params.get('arbitrage_threshold')
    sort_by = query_params.get('sort_by', 'arbitrage_ratio_desc')

    # 构建查询条件
    search_params = {}
    if name_query:
        search_params['name_query'] = name_query
    if item_type:
        search_params['item_type'] = item_type
    if quality:
        search_params['quality'] = quality
    if rarity:
        search_params['rarity'] = rarity
    if price_range != (0, 100000):
        search_params['min_price'] = price_range[0]
        search_params['max_price'] = price_range[1]
    if arbitrage_threshold:
        search_params['arbitrage_threshold'] = arbitrage_threshold
    if sort_by:
        search_params['sort_by'] = sort_by

    # 执行查询
    execute_search(search_params)
```

## 配置说明

### 预设配置

#### Query模式配置
```python
QUERY_CONFIG = {
    'filters': [
        'name_query', 'item_type', 'quality',
        'rarity', 'price_range', 'arbitrage_threshold', 'sort_by'
    ],
    'layout_columns': [1, 1, 1],
    'show_arbitrage_threshold': True,
    'show_arbitrage_info': True,
    'button_text': '🔍 查询',
    'button_type': 'primary'
}
```



### 自定义配置

```python
# 完全自定义配置示例
custom_config = {
    'filters': ['name_query', 'item_type', 'price_range'],
    'layout_columns': [2, 1, 1],
    'show_arbitrage_threshold': False,
    'show_arbitrage_info': False,
    'button_text': '🔍 筛选',
    'button_type': 'secondary',
    'price_range_min': 0,
    'price_range_max': 50000,
    'sort_options': [
        ('price_asc', '价格 (低到高)'),
        ('price_desc', '价格 (高到低)'),
        ('name_asc', '名称 (A-Z)')
    ]
}
```

### 配置项详解

- **filters**: 要显示的过滤项列表，顺序决定显示顺序
- **layout_columns**: 列布局权重，如[1,1,1]表示三等分
- **show_arbitrage_threshold**: 是否显示搬砖卡价输入框
- **show_arbitrage_info**: 是否显示搬砖计算信息
- **button_text**: 查询按钮显示文本
- **button_type**: 查询按钮类型（primary/secondary）
- **price_range_min/max**: 价格范围的最小/最大值
- **sort_options**: 自定义排序选项列表

## 最佳实践

### 1. 唯一性保证
```python
# ✅ 正确：使用不同的key_suffix避免冲突
render_item_filter(filter_type='query', key_suffix='query_page')
render_item_filter(filter_type='favorite', key_suffix='favorite_page')

# ❌ 错误：相同的key_suffix可能导致状态冲突
render_item_filter(filter_type='query', key_suffix='same')
render_item_filter(filter_type='favorite', key_suffix='same')
```

### 2. 回调函数设计
```python
# ✅ 正确：检查button_clicked标志
def handle_callback(query_params):
    if query_params.get('button_clicked'):
        # 处理查询逻辑
        process_query(query_params)

# ❌ 错误：不检查button_clicked可能导致意外触发
def handle_callback(query_params):
    # 直接处理，可能在组件初始化时误触发
    process_query(query_params)
```

### 3. 预设模式优先
```python
# ✅ 推荐：优先使用预设模式
render_item_filter(filter_type='query')

# ⚠️ 谨慎：只在预设模式不满足需求时使用自定义
render_item_filter(filter_type='custom', config=custom_config)
```

### 4. 服务初始化
```python
# ✅ 正确：在回调外初始化服务
if 'item_service' not in st.session_state:
    st.session_state.item_service = ItemService()

def handle_callback(query_params):
    if query_params.get('button_clicked'):
        service = st.session_state.item_service
        results = service.search_items(**query_params)
```

## 注意事项

### 1. 状态管理
- 每个页面使用不同的 `key_suffix` 避免状态冲突
- 组件会自动初始化所需的服务（ItemService）
- 状态存储在 `st.session_state` 中，页面刷新后会重置

### 2. 回调时机
- 回调函数只在用户点击查询按钮时触发
- 通过 `button_clicked` 标志判断是否为用户主动触发
- 避免在组件初始化时执行查询逻辑

### 3. 配置合并
- 自定义配置会覆盖预设配置的对应项
- 未指定的配置项使用预设值
- 配置验证确保必需的配置项存在

### 4. 性能考虑
- 过滤项的状态使用 `key_suffix` 隔离
- 避免在回调函数中执行耗时操作
- 大量数据查询建议使用分页和缓存

## 依赖关系

组件依赖以下模块：
- `streamlit`: UI框架
- `src.cs2_investment.app.services.item_service`: 饰品查询服务
- `src.cs2_investment.app.utils.data_formatter`: 数据格式化工具
- `typing`: 类型注解支持

## 文件结构

```
src/cs2_investment/app/components/
├── __init__.py
├── unified_item_filter.py         # 统一过滤组件
├── unified_item_card.py           # 统一饰品卡片组件
├── item_analysis_component.py     # 分析功能组件
└── README.md                      # 组件使用文档
```

## 测试页面

可以通过测试页面验证组件功能：
```
src/cs2_investment/app/pages/test_unified_item_filter.py
```

测试页面包含：
- 预设模式测试
- 自定义配置测试
- 回调功能测试
- 状态隔离测试
- 性能测试
- 边界情况测试

## 扩展指南

### 添加新的过滤类型

1. **创建过滤项函数**
```python
def _render_new_filter(key_suffix: str = "") -> Any:
    """渲染新的过滤项"""
    return st.text_input(
        "新过滤项",
        key=f"new_filter_{key_suffix}",
        help="新过滤项的帮助信息"
    )
```

2. **更新过滤项映射**
```python
FILTER_FUNCTIONS = {
    'name_query': _render_name_query,
    'item_type': _render_item_type,
    # ... 其他现有过滤项
    'new_filter': _render_new_filter,  # 添加新过滤项
}
```

3. **更新查询参数构建**
```python
def _build_query_params(filters: List[str], key_suffix: str = "") -> Dict[str, Any]:
    # ... 现有逻辑
    if 'new_filter' in filters:
        new_filter_value = st.session_state.get(f"new_filter_{key_suffix}")
        if new_filter_value:
            params['new_filter'] = new_filter_value
```

### 添加新的预设模式

```python
# 在PRESET_CONFIGS中添加新模式
PRESET_CONFIGS = {
    'query': QUERY_CONFIG,
    'favorite': FAVORITE_CONFIG,
    'new_mode': {  # 新的预设模式
        'filters': ['name_query', 'new_filter'],
        'layout_columns': [1, 1],
        'show_arbitrage_threshold': False,
        'show_arbitrage_info': False,
        'button_text': '🔍 新搜索',
        'button_type': 'primary'
    }
}
```

### 自定义布局算法

```python
def _create_custom_layout(columns: List[float]) -> List:
    """创建自定义布局"""
    # 实现自定义布局逻辑
    return st.columns(columns)
```

## 性能优化建议

### 1. 状态缓存
```python
# 缓存服务实例
@st.cache_resource
def get_item_service():
    return ItemService()
```

### 2. 查询优化
```python
# 使用防抖避免频繁查询
import time

def debounced_callback(query_params):
    if 'last_query_time' not in st.session_state:
        st.session_state.last_query_time = 0

    current_time = time.time()
    if current_time - st.session_state.last_query_time < 0.5:
        return  # 防抖：500ms内不重复查询

    st.session_state.last_query_time = current_time
    # 执行查询逻辑
```

### 3. 数据分页
```python
def paginated_search(query_params, page=1, page_size=20):
    """分页查询"""
    offset = (page - 1) * page_size
    return item_service.search_items(
        **query_params,
        limit=page_size,
        offset=offset
    )
```

## 故障排除

### 常见问题

1. **状态冲突**
   - 问题：不同页面的过滤状态相互影响
   - 解决：确保每个页面使用不同的 `key_suffix`

2. **回调不触发**
   - 问题：过滤条件变化但回调函数不执行
   - 解决：检查 `button_clicked` 标志，确保用户点击了查询按钮

3. **配置不生效**
   - 问题：自定义配置没有覆盖预设配置
   - 解决：检查配置字典的键名是否正确

4. **性能问题**
   - 问题：过滤操作响应缓慢
   - 解决：使用缓存、分页和防抖技术

### 调试技巧

```python
# 启用调试模式
def debug_callback(query_params):
    st.write("调试信息：", query_params)
    # 正常的回调逻辑
    handle_normal_callback(query_params)

# 使用调试回调
render_item_filter(
    filter_type='query',
    key_suffix='debug',
    on_filter_change=debug_callback
)
```

## 后续扩展

可以考虑添加的功能：
- 过滤条件保存和加载
- 预设查询模板
- 高级过滤语法支持
- 过滤历史记录
- 导出过滤结果
- 多语言支持
- 主题自定义
- 移动端适配优化
