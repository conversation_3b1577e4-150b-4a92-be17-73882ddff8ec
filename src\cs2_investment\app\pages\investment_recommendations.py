"""
投资推荐页面

显示系统生成的投资推荐，支持多维度筛选和详细分析。
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.investment_recommendation_dao import investment_recommendation_dao
from src.cs2_investment.dao.item_dao import item_dao
from src.cs2_investment.models.investment_recommendation import InvestmentRecommendation
from src.cs2_investment.app.components.recommendation_card import show_recommendation_card, show_recommendation_summary_card


def show_page():
    """显示投资推荐页面"""
    st.title("🎯 投资推荐")
    st.markdown("基于AI算法分析的专业投资建议")

    # 添加数据类型选择
    st.markdown("### 📋 数据类型")
    data_type = st.radio(
        "选择要查看的数据类型",
        options=["推荐记录", "不推荐记录", "全部记录"],
        horizontal=True,
        help="推荐记录：评分>0的投资建议；不推荐记录：评分=0的分析结果；全部记录：包含所有分析结果"
    )

    # 根据选择设置过滤条件
    if data_type == "推荐记录":
        score_filter = "positive"  # total_score > 0
        st.info("📈 显示有投资价值的推荐记录（评分 > 0）")
    elif data_type == "不推荐记录":
        score_filter = "zero"      # total_score = 0
        st.info("📉 显示不建议投资的分析记录（评分 = 0）")
    else:
        score_filter = "all"       # 全部记录
        st.info("📊 显示所有分析记录（包含推荐和不推荐）")

    # 将过滤条件存储到session state
    st.session_state.score_filter = score_filter

    # 页面布局
    tab1, tab2, tab3, tab4 = st.tabs([
        "📊 推荐概览",
        "🔍 详细筛选",
        "📈 趋势分析",
        "⚙️ 推荐设置"
    ])

    with tab1:
        show_recommendations_overview()

    with tab2:
        show_detailed_filtering()

    with tab3:
        show_trend_analysis()

    with tab4:
        show_recommendation_settings()


def show_recommendations_overview():
    """显示推荐概览"""
    st.subheader("📊 推荐概览")
    
    # 获取统计数据
    stats = get_recommendation_statistics()
    
    # 根据数据类型显示不同的指标
    score_filter = getattr(st.session_state, 'score_filter', 'all')

    if score_filter == "positive":
        # 推荐记录的指标
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric(
                "今日推荐",
                stats.get('today_count', 0),
                delta=stats.get('today_delta', 0)
            )

        with col2:
            st.metric(
                "BUY推荐",
                stats.get('buy_count', 0),
                delta=f"{stats.get('buy_percentage', 0):.1f}%"
            )

        with col3:
            st.metric(
                "平均评分",
                f"{stats.get('avg_score', 0):.1f}",
                delta=f"{stats.get('score_trend', 0):+.1f}"
            )

        with col4:
            st.metric(
                "高置信度",
                stats.get('high_confidence_count', 0),
                delta=f"{stats.get('confidence_percentage', 0):.1f}%"
            )

    elif score_filter == "zero":
        # 不推荐记录的指标
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric(
                "今日分析",
                stats.get('today_count', 0),
                delta=stats.get('today_delta', 0)
            )

        with col2:
            st.metric(
                "AVOID记录",
                stats.get('today_count', 0),  # 大部分应该是AVOID
                delta="不推荐投资"
            )

        with col3:
            st.metric(
                "评分",
                "0.0",
                delta="未通过筛选"
            )

        with col4:
            st.metric(
                "算法数量",
                "0-7",
                delta="分析完成"
            )

    else:
        # 全部记录的指标
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric(
                "今日分析",
                stats.get('today_count', 0),
                delta=stats.get('today_delta', 0)
            )

        with col2:
            st.metric(
                "推荐记录",
                stats.get('positive_count', 0),
                delta=f"不推荐: {stats.get('zero_count', 0)}"
            )

        with col3:
            st.metric(
                "平均评分",
                f"{stats.get('avg_score', 0):.1f}",
                delta=f"推荐率: {(stats.get('positive_count', 0) / max(stats.get('today_count', 1), 1) * 100):.1f}%"
            )

        with col4:
            st.metric(
                "高置信度",
                stats.get('high_confidence_count', 0),
                delta=f"{stats.get('confidence_percentage', 0):.1f}%"
            )
    
    # 推荐类型分布图
    col1, col2 = st.columns(2)
    
    with col1:
        show_recommendation_type_chart(stats)
    
    with col2:
        show_risk_level_chart(stats)
    
    # 最新推荐列表
    st.subheader("🆕 最新推荐")
    show_latest_recommendations()


def show_detailed_filtering():
    """显示详细筛选"""
    st.subheader("🔍 详细筛选")
    
    # 筛选条件
    col1, col2, col3 = st.columns(3)
    
    # 根据数据类型调整默认筛选条件
    score_filter = getattr(st.session_state, 'score_filter', 'all')

    with col1:
        # 根据数据类型设置默认推荐类型
        if score_filter == "positive":
            default_types = ['BUY', 'HOLD', 'SELL']
        elif score_filter == "zero":
            default_types = ['AVOID']
        else:
            default_types = ['BUY', 'HOLD', 'SELL', 'AVOID']

        recommendation_types = st.multiselect(
            "推荐类型",
            options=['BUY', 'HOLD', 'SELL', 'AVOID'],
            default=default_types,
            help="选择要查看的推荐类型"
        )

    with col2:
        risk_levels = st.multiselect(
            "风险等级",
            options=['LOW', 'MEDIUM', 'HIGH'],
            default=['LOW', 'MEDIUM', 'HIGH'],  # 默认包含所有风险等级
            help="选择风险等级"
        )

    with col3:
        date_range = st.date_input(
            "日期范围",
            value=(date.today() - timedelta(days=30), date.today()),  # 扩大到30天
            help="选择推荐日期范围"
        )
    
    # 高级筛选
    with st.expander("高级筛选", expanded=False):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            # 根据数据类型调整默认评分
            if score_filter == "zero":
                min_score = st.slider("最低评分", 0.0, 100.0, 0.0, 5.0)
                st.caption("💡 不推荐记录的评分都是0")
            else:
                min_score = st.slider("最低评分", 0.0, 100.0, 0.0, 5.0)  # 降低默认值

            min_confidence = st.slider("最低置信度", 0.0, 100.0, 0.0, 5.0)  # 降低默认值

        with col2:
            min_algorithms = st.number_input("最少算法数", 0, 10, 0)  # 降低默认值
            price_range = st.slider("价格范围 (¥)", 0, 50000, (0, 50000), 100)  # 扩大范围
        
        with col3:
            sort_by = st.selectbox(
                "排序方式",
                options=['total_score', 'confidence_level', 'recommendation_time', 'current_price'],
                format_func=lambda x: {
                    'total_score': '综合评分',
                    'confidence_level': '置信度',
                    'recommendation_time': '推荐时间',
                    'current_price': '当前价格'
                }[x]
            )
            sort_order = st.radio("排序顺序", ['降序', '升序'], horizontal=True)
    
    # 应用筛选并显示结果
    col1, col2 = st.columns([1, 3])

    with col1:
        apply_filter = st.button("🔍 应用筛选", type="primary")

    with col2:
        # 显示当前筛选条件摘要
        filter_summary = []
        if recommendation_types:
            filter_summary.append(f"类型: {', '.join(recommendation_types)}")
        if risk_levels:
            filter_summary.append(f"风险: {', '.join(risk_levels)}")
        if min_score > 0:
            filter_summary.append(f"评分≥{min_score}")
        if min_confidence > 0:
            filter_summary.append(f"置信度≥{min_confidence}%")

        if filter_summary:
            st.caption(f"筛选条件: {' | '.join(filter_summary)}")

    if apply_filter:
        with st.spinner("正在筛选数据..."):
            # 显示筛选参数用于调试
            with st.expander("🔧 筛选参数（调试信息）", expanded=False):
                st.write("推荐类型:", recommendation_types)
                st.write("风险等级:", risk_levels)
                st.write("日期范围:", date_range)
                st.write("最低评分:", min_score)
                st.write("最低置信度:", min_confidence)
                st.write("最少算法数:", min_algorithms)
                st.write("价格范围:", price_range)
                st.write("排序方式:", sort_by, sort_order)
                st.write("数据类型过滤:", getattr(st.session_state, 'score_filter', 'all'))

            filtered_recommendations = apply_filters(
                recommendation_types, risk_levels, date_range,
                min_score, min_confidence, min_algorithms, price_range,
                sort_by, sort_order
            )

            if filtered_recommendations:
                show_filtered_recommendations(filtered_recommendations)
            else:
                st.warning("没有找到符合条件的推荐")

                # 提供一些建议
                st.markdown("### 💡 建议")
                st.markdown("""
                - 尝试放宽筛选条件（降低评分要求、扩大日期范围等）
                - 检查是否选择了正确的数据类型（推荐记录/不推荐记录）
                - 确认数据库中有相应的推荐记录
                """)

                # 显示数据库中的基本统计
                try:
                    from src.cs2_investment.config.database import get_db_session
                    from sqlalchemy import func

                    with get_db_session() as session:
                        total_count = session.query(func.count(InvestmentRecommendation.id)).scalar()
                        today_count = session.query(func.count(InvestmentRecommendation.id))\
                            .filter(InvestmentRecommendation.recommendation_date == date.today()).scalar()

                        st.info(f"数据库统计: 总记录数 {total_count}, 今日记录数 {today_count}")
                except Exception as e:
                    st.error(f"获取统计信息失败: {e}")


def show_trend_analysis():
    """显示趋势分析"""
    st.subheader("📈 趋势分析")
    
    # 时间范围选择
    col1, col2 = st.columns(2)
    
    with col1:
        analysis_period = st.selectbox(
            "分析周期",
            options=[7, 14, 30, 60, 90],
            format_func=lambda x: f"最近{x}天",
            index=2
        )
    
    with col2:
        chart_type = st.selectbox(
            "图表类型",
            options=['推荐数量趋势', '评分分布', '风险分析', '算法效果']
        )
    
    # 生成趋势图表
    if chart_type == '推荐数量趋势':
        show_recommendation_count_trend(analysis_period)
    elif chart_type == '评分分布':
        show_score_distribution(analysis_period)
    elif chart_type == '风险分析':
        show_risk_analysis(analysis_period)
    elif chart_type == '算法效果':
        show_algorithm_effectiveness(analysis_period)


def show_recommendation_settings():
    """显示推荐设置"""
    st.subheader("⚙️ 推荐设置")
    
    # 个人偏好设置
    st.markdown("### 个人偏好")
    
    col1, col2 = st.columns(2)
    
    with col1:
        preferred_risk = st.selectbox(
            "偏好风险等级",
            options=['LOW', 'MEDIUM', 'HIGH'],
            format_func=lambda x: {'LOW': '低风险', 'MEDIUM': '中等风险', 'HIGH': '高风险'}[x]
        )
        
        min_investment = st.number_input("最小投资金额 (¥)", 100, 50000, 1000, 100)
    
    with col2:
        preferred_types = st.multiselect(
            "关注推荐类型",
            options=['BUY', 'HOLD', 'SELL'],
            default=['BUY'],
            format_func=lambda x: {'BUY': '买入', 'HOLD': '持有', 'SELL': '卖出'}[x]
        )
        
        max_investment = st.number_input("最大投资金额 (¥)", 1000, 100000, 10000, 500)
    
    # 通知设置
    st.markdown("### 通知设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        enable_notifications = st.checkbox("启用推荐通知", value=True)
        notification_threshold = st.slider("通知评分阈值", 60.0, 95.0, 80.0, 5.0)
    
    with col2:
        notification_frequency = st.selectbox(
            "通知频率",
            options=['实时', '每小时', '每日', '每周'],
            index=1
        )
    
    # 保存设置
    if st.button("💾 保存设置", type="primary"):
        save_user_preferences({
            'preferred_risk': preferred_risk,
            'min_investment': min_investment,
            'max_investment': max_investment,
            'preferred_types': preferred_types,
            'enable_notifications': enable_notifications,
            'notification_threshold': notification_threshold,
            'notification_frequency': notification_frequency
        })
        st.success("设置已保存！")


def get_recommendation_statistics() -> Dict[str, Any]:
    """获取推荐统计数据"""
    try:
        from src.cs2_investment.config.database import get_db_session
        from sqlalchemy import func, and_

        # 获取评分过滤条件
        score_filter = getattr(st.session_state, 'score_filter', 'all')

        with get_db_session() as session:
            today = date.today()
            yesterday = today - timedelta(days=1)

            # 构建查询条件
            base_conditions = [InvestmentRecommendation.recommendation_date == today]
            yesterday_conditions = [InvestmentRecommendation.recommendation_date == yesterday]

            if score_filter == "positive":
                base_conditions.append(InvestmentRecommendation.total_score > 0)
                yesterday_conditions.append(InvestmentRecommendation.total_score > 0)
            elif score_filter == "zero":
                base_conditions.append(InvestmentRecommendation.total_score == 0)
                yesterday_conditions.append(InvestmentRecommendation.total_score == 0)
            # "all" 不添加额外条件

            # 今日统计
            today_query = session.query(
                func.count(InvestmentRecommendation.id).label('total_count'),
                func.avg(InvestmentRecommendation.total_score).label('avg_score'),
                func.count(func.nullif(InvestmentRecommendation.total_score > 0, False)).label('positive_count')
            ).filter(and_(*base_conditions))

            today_result = today_query.first()
            today_count = today_result.total_count or 0
            avg_score = float(today_result.avg_score or 0)
            positive_count = today_result.positive_count or 0

            # 昨日统计
            yesterday_query = session.query(
                func.count(InvestmentRecommendation.id).label('total_count')
            ).filter(and_(*yesterday_conditions))

            yesterday_result = yesterday_query.first()
            yesterday_count = yesterday_result.total_count or 0

            # BUY推荐统计
            buy_conditions = base_conditions + [InvestmentRecommendation.recommendation_type == 'BUY']
            buy_count = session.query(func.count(InvestmentRecommendation.id))\
                .filter(and_(*buy_conditions)).scalar() or 0

            # 高置信度推荐统计（置信度>80%）
            high_confidence_conditions = base_conditions + [InvestmentRecommendation.confidence_level > 80]
            high_confidence_count = session.query(func.count(InvestmentRecommendation.id))\
                .filter(and_(*high_confidence_conditions)).scalar() or 0

            return {
                'today_count': today_count,
                'today_delta': today_count - yesterday_count,
                'buy_count': buy_count,
                'buy_percentage': (buy_count / today_count * 100) if today_count > 0 else 0,
                'avg_score': avg_score,
                'score_trend': 0,  # 可以后续实现评分趋势计算
                'high_confidence_count': high_confidence_count,
                'confidence_percentage': (high_confidence_count / today_count * 100) if today_count > 0 else 0,
                'positive_count': positive_count,
                'zero_count': today_count - positive_count
            }
    except Exception as e:
        st.error(f"获取统计数据失败: {e}")
        return {}


def show_recommendation_type_chart(stats: Dict[str, Any]):
    """显示推荐类型分布图"""
    st.markdown("#### 推荐类型分布")

    try:
        from src.cs2_investment.config.database import get_db_session
        from sqlalchemy import func, and_

        # 获取评分过滤条件
        score_filter = getattr(st.session_state, 'score_filter', 'all')

        with get_db_session() as session:
            today = date.today()

            # 构建基础查询条件
            base_conditions = [InvestmentRecommendation.recommendation_date == today]

            if score_filter == "positive":
                base_conditions.append(InvestmentRecommendation.total_score > 0)
            elif score_filter == "zero":
                base_conditions.append(InvestmentRecommendation.total_score == 0)

            # 获取各类型推荐数量
            type_stats = session.query(
                InvestmentRecommendation.recommendation_type,
                func.count(InvestmentRecommendation.id).label('count')
            ).filter(and_(*base_conditions))\
             .group_by(InvestmentRecommendation.recommendation_type)\
             .all()

            if type_stats:
                # 转换为DataFrame
                type_data = [{'类型': stat[0], '数量': stat[1]} for stat in type_stats]
                df = pd.DataFrame(type_data)

                # 根据数据类型调整颜色和标题
                if score_filter == "positive":
                    title = "推荐类型分布（评分 > 0）"
                    # 对于推荐记录，AVOID类型应该很少或没有
                    color_map = {
                        'BUY': '#00C851',
                        'HOLD': '#ffbb33',
                        'SELL': '#ff4444',
                        'AVOID': '#9E9E9E'
                    }
                elif score_filter == "zero":
                    title = "分析结果分布（评分 = 0）"
                    # 对于不推荐记录，主要是AVOID类型
                    color_map = {
                        'BUY': '#E0E0E0',    # 灰色，因为评分为0的BUY很少见
                        'HOLD': '#E0E0E0',   # 灰色
                        'SELL': '#E0E0E0',   # 灰色
                        'AVOID': '#ff4444'   # 红色，主要类型
                    }
                else:
                    title = "推荐类型分布（全部记录）"
                    color_map = {
                        'BUY': '#00C851',
                        'HOLD': '#ffbb33',
                        'SELL': '#ff4444',
                        'AVOID': '#9E9E9E'
                    }

                fig = px.pie(df, values='数量', names='类型',
                            title=title,
                            color_discrete_map=color_map)
                st.plotly_chart(fig, use_container_width=True)

                # 显示详细统计
                total = sum(df['数量'])
                st.markdown("**详细统计:**")
                for _, row in df.iterrows():
                    percentage = (row['数量'] / total * 100) if total > 0 else 0
                    st.write(f"- {row['类型']}: {row['数量']} 条 ({percentage:.1f}%)")
            else:
                st.info("暂无数据")
    except Exception as e:
        st.error(f"生成图表失败: {e}")


def show_risk_level_chart(stats: Dict[str, Any]):
    """显示风险等级分布图"""
    st.markdown("#### 风险等级分布")
    
    try:
        # 这里需要实现获取风险等级分布的逻辑
        risk_data = [
            {'风险等级': 'LOW', '数量': 10},
            {'风险等级': 'MEDIUM', '数量': 15},
            {'风险等级': 'HIGH', '数量': 5}
        ]
        
        df = pd.DataFrame(risk_data)
        fig = px.bar(df, x='风险等级', y='数量',
                    color='风险等级',
                    color_discrete_map={
                        'LOW': '#00C851',
                        'MEDIUM': '#ffbb33',
                        'HIGH': '#ff4444'
                    })
        st.plotly_chart(fig, use_container_width=True)
    except Exception as e:
        st.error(f"生成图表失败: {e}")


def show_latest_recommendations():
    """显示最新推荐列表"""
    try:
        # 获取评分过滤条件
        score_filter = getattr(st.session_state, 'score_filter', 'all')

        # 根据过滤条件获取数据
        from src.cs2_investment.config.database import get_db_session
        from sqlalchemy import and_, desc

        with get_db_session() as session:
            # 构建查询条件
            conditions = [InvestmentRecommendation.status == 'ACTIVE']

            if score_filter == "positive":
                conditions.append(InvestmentRecommendation.total_score > 0)
            elif score_filter == "zero":
                conditions.append(InvestmentRecommendation.total_score == 0)

            # 查询数据
            query_results = session.query(InvestmentRecommendation)\
                .filter(and_(*conditions))\
                .order_by(desc(InvestmentRecommendation.recommendation_time))\
                .limit(10)\
                .all()

            # 转换为脱离Session的对象
            recommendations = [investment_recommendation_dao._detach_recommendation(rec) for rec in query_results]

        if recommendations:
            # 根据数据类型显示不同的标题
            if score_filter == "positive":
                st.markdown("#### 🟢 最新投资推荐（评分 > 0）")
            elif score_filter == "zero":
                st.markdown("#### 🔴 最新不推荐记录（评分 = 0）")
            else:
                st.markdown("#### 📋 最新分析记录（全部）")

            # 显示模式选择
            view_mode = st.radio(
                "显示模式",
                ["卡片视图", "表格视图"],
                horizontal=True,
                key="latest_view_mode"
            )

            if view_mode == "卡片视图":
                # 使用推荐卡片组件
                for rec in recommendations:
                    show_recommendation_summary_card(rec)
            else:
                # 转换为DataFrame显示
                data = []
                for rec in recommendations:
                    # 获取饰品信息
                    item = item_dao.get_by_item_id(rec.item_id)
                    item_name = item.get('name') if item else rec.item_id

                    data.append({
                        '饰品名称': item_name,
                        '推荐类型': rec.recommendation_type,
                        '综合评分': f"{rec.total_score:.1f}",
                        '风险等级': rec.risk_level,
                        '置信度': f"{rec.confidence_level:.1f}%" if rec.confidence_level else "N/A",
                        '当前价格': f"¥{rec.current_price:.2f}" if rec.current_price else "N/A",
                        '推荐时间': rec.recommendation_time.strftime('%m-%d %H:%M')
                    })

                df = pd.DataFrame(data)

                # 添加点击事件处理
                event = st.dataframe(
                    df,
                    use_container_width=True,
                    on_select="rerun",
                    selection_mode="single-row"
                )

                # 处理行选择事件
                if event.selection.rows:
                    selected_idx = event.selection.rows[0]
                    selected_rec = recommendations[selected_idx]

                    st.markdown("### 选中推荐详情")
                    show_recommendation_card(selected_rec, expanded=True)
        else:
            st.info("暂无推荐数据")
    except Exception as e:
        st.error(f"获取推荐数据失败: {e}")


def apply_filters(recommendation_types, risk_levels, date_range, min_score,
                 min_confidence, min_algorithms, price_range, sort_by, sort_order):
    """应用筛选条件"""
    try:
        from src.cs2_investment.config.database import get_db_session
        from sqlalchemy import and_, or_, desc, asc

        with get_db_session() as session:
            # 构建查询
            query = session.query(InvestmentRecommendation)

            # 应用筛选条件
            conditions = []

            # 添加数据类型过滤（基于页面选择）
            score_filter = getattr(st.session_state, 'score_filter', 'all')
            if score_filter == "positive":
                conditions.append(InvestmentRecommendation.total_score > 0)
            elif score_filter == "zero":
                conditions.append(InvestmentRecommendation.total_score == 0)

            # 推荐类型筛选
            if recommendation_types:
                conditions.append(InvestmentRecommendation.recommendation_type.in_(recommendation_types))

            # 风险等级筛选
            if risk_levels:
                conditions.append(InvestmentRecommendation.risk_level.in_(risk_levels))

            # 日期范围筛选
            if isinstance(date_range, (list, tuple)) and len(date_range) == 2:
                start_date, end_date = date_range
                conditions.append(InvestmentRecommendation.recommendation_date >= start_date)
                conditions.append(InvestmentRecommendation.recommendation_date <= end_date)
            elif hasattr(date_range, '__iter__') and len(list(date_range)) == 2:
                # 处理streamlit date_input返回的格式
                date_list = list(date_range)
                start_date, end_date = date_list[0], date_list[1]
                conditions.append(InvestmentRecommendation.recommendation_date >= start_date)
                conditions.append(InvestmentRecommendation.recommendation_date <= end_date)

            # 评分筛选 - 只对推荐记录应用最低评分要求
            if score_filter == "positive":
                conditions.append(InvestmentRecommendation.total_score >= min_score)
            elif score_filter == "zero":
                # 对于不推荐记录，评分都是0，不需要额外筛选
                pass
            else:
                # 全部记录时，应用评分筛选
                conditions.append(InvestmentRecommendation.total_score >= min_score)

            # 置信度筛选 - 只对有置信度的记录应用
            if min_confidence > 0:
                conditions.append(
                    or_(
                        InvestmentRecommendation.confidence_level >= min_confidence,
                        InvestmentRecommendation.confidence_level.is_(None)  # 包含没有置信度的记录
                    )
                )

            # 算法数量筛选
            if min_algorithms > 0:
                conditions.append(InvestmentRecommendation.algorithm_count >= min_algorithms)

            # 价格范围筛选 - 只对有价格的记录应用
            if price_range[0] > 0 or price_range[1] < 10000:
                conditions.append(
                    or_(
                        and_(
                            InvestmentRecommendation.current_price >= price_range[0],
                            InvestmentRecommendation.current_price <= price_range[1]
                        ),
                        InvestmentRecommendation.current_price.is_(None)  # 包含没有价格的记录
                    )
                )

            # 应用所有条件
            if conditions:
                query = query.filter(and_(*conditions))

            # 排序
            sort_column = getattr(InvestmentRecommendation, sort_by, InvestmentRecommendation.total_score)
            if sort_order == "降序":
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(asc(sort_column))

            # 限制结果数量并获取结果
            results = query.limit(100).all()

            # 转换为脱离Session的对象
            detached_results = []
            for rec in results:
                detached_results.append(investment_recommendation_dao._detach_recommendation(rec))

            return detached_results

    except Exception as e:
        st.error(f"筛选失败: {e}")
        import traceback
        st.error(f"详细错误: {traceback.format_exc()}")
        return []


def show_filtered_recommendations(recommendations):
    """显示筛选后的推荐"""
    st.success(f"找到 {len(recommendations)} 条符合条件的推荐")

    if not recommendations:
        return

    # 显示模式选择
    view_mode = st.radio(
        "显示模式",
        ["摘要视图", "详细视图"],
        horizontal=True,
        key="filtered_view_mode"
    )

    if view_mode == "摘要视图":
        # 摘要卡片视图
        for rec in recommendations:
            show_recommendation_summary_card(rec)
    else:
        # 详细卡片视图
        for i, rec in enumerate(recommendations):
            with st.expander(f"推荐 {i+1}: {rec.item_id}", expanded=False):
                show_recommendation_card(rec, expanded=True)


def show_recommendation_count_trend(days):
    """显示推荐数量趋势"""
    st.markdown("#### 推荐数量趋势")

    try:
        from src.cs2_investment.config.database import get_db_session
        from sqlalchemy import func

        with get_db_session() as session:
            # 获取指定天数内的每日推荐数量
            end_date = date.today()
            start_date = end_date - timedelta(days=days)

            daily_counts = session.query(
                InvestmentRecommendation.recommendation_date,
                func.count(InvestmentRecommendation.id).label('count')
            ).filter(
                InvestmentRecommendation.recommendation_date >= start_date
            ).group_by(
                InvestmentRecommendation.recommendation_date
            ).order_by(
                InvestmentRecommendation.recommendation_date
            ).all()

            if daily_counts:
                # 创建DataFrame
                df = pd.DataFrame(daily_counts, columns=['日期', '推荐数量'])

                # 创建趋势图
                fig = px.line(df, x='日期', y='推荐数量',
                             title=f'最近{days}天推荐数量趋势',
                             markers=True)
                fig.update_layout(xaxis_title="日期", yaxis_title="推荐数量")
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("暂无趋势数据")

    except Exception as e:
        st.error(f"生成趋势图失败: {e}")


def show_score_distribution(days):
    """显示评分分布"""
    st.markdown("#### 评分分布")

    try:
        from src.cs2_investment.config.database import get_db_session

        with get_db_session() as session:
            end_date = date.today()
            start_date = end_date - timedelta(days=days)

            # 获取评分数据
            scores = session.query(InvestmentRecommendation.total_score)\
                .filter(InvestmentRecommendation.recommendation_date >= start_date)\
                .all()

            if scores:
                score_values = [float(score[0]) for score in scores]

                # 创建直方图
                fig = px.histogram(
                    x=score_values,
                    nbins=20,
                    title=f'最近{days}天评分分布',
                    labels={'x': '评分', 'y': '数量'}
                )
                fig.update_layout(xaxis_title="评分", yaxis_title="数量")
                st.plotly_chart(fig, use_container_width=True)

                # 显示统计信息
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("平均评分", f"{sum(score_values)/len(score_values):.1f}")
                with col2:
                    st.metric("最高评分", f"{max(score_values):.1f}")
                with col3:
                    st.metric("最低评分", f"{min(score_values):.1f}")
            else:
                st.info("暂无评分数据")

    except Exception as e:
        st.error(f"生成评分分布图失败: {e}")


def show_risk_analysis(days):
    """显示风险分析"""
    st.markdown("#### 风险分析")

    try:
        from src.cs2_investment.config.database import get_db_session
        from sqlalchemy import func

        with get_db_session() as session:
            end_date = date.today()
            start_date = end_date - timedelta(days=days)

            # 按风险等级和推荐类型统计
            risk_stats = session.query(
                InvestmentRecommendation.risk_level,
                InvestmentRecommendation.recommendation_type,
                func.count(InvestmentRecommendation.id).label('count'),
                func.avg(InvestmentRecommendation.total_score).label('avg_score')
            ).filter(
                InvestmentRecommendation.recommendation_date >= start_date
            ).group_by(
                InvestmentRecommendation.risk_level,
                InvestmentRecommendation.recommendation_type
            ).all()

            if risk_stats:
                # 转换为DataFrame
                df = pd.DataFrame(risk_stats, columns=['风险等级', '推荐类型', '数量', '平均评分'])

                # 创建堆叠柱状图
                fig = px.bar(df, x='风险等级', y='数量', color='推荐类型',
                           title=f'最近{days}天风险等级分析',
                           color_discrete_map={
                               'BUY': '#00C851',
                               'HOLD': '#ffbb33',
                               'SELL': '#ff4444',
                               'AVOID': '#9E9E9E'
                           })
                st.plotly_chart(fig, use_container_width=True)

                # 显示详细统计表
                st.dataframe(df, use_container_width=True)
            else:
                st.info("暂无风险分析数据")

    except Exception as e:
        st.error(f"生成风险分析图失败: {e}")


def show_algorithm_effectiveness(days):
    """显示算法效果分析"""
    st.markdown("#### 算法效果分析")

    try:
        from src.cs2_investment.config.database import get_db_session
        from sqlalchemy import func

        with get_db_session() as session:
            end_date = date.today()
            start_date = end_date - timedelta(days=days)

            # 按算法数量统计
            algo_stats = session.query(
                InvestmentRecommendation.algorithm_count,
                func.count(InvestmentRecommendation.id).label('count'),
                func.avg(InvestmentRecommendation.total_score).label('avg_score'),
                func.avg(InvestmentRecommendation.confidence_level).label('avg_confidence')
            ).filter(
                InvestmentRecommendation.recommendation_date >= start_date
            ).group_by(
                InvestmentRecommendation.algorithm_count
            ).order_by(
                InvestmentRecommendation.algorithm_count
            ).all()

            if algo_stats:
                # 转换为DataFrame
                df = pd.DataFrame(algo_stats, columns=['算法数量', '推荐数量', '平均评分', '平均置信度'])

                # 创建散点图
                fig = go.Figure()

                fig.add_trace(go.Scatter(
                    x=df['算法数量'],
                    y=df['平均评分'],
                    mode='markers+lines',
                    name='平均评分',
                    marker=dict(size=df['推荐数量']*2, color='blue'),
                    yaxis='y'
                ))

                fig.add_trace(go.Scatter(
                    x=df['算法数量'],
                    y=df['平均置信度'],
                    mode='markers+lines',
                    name='平均置信度',
                    marker=dict(size=df['推荐数量']*2, color='red'),
                    yaxis='y2'
                ))

                fig.update_layout(
                    title=f'最近{days}天算法效果分析',
                    xaxis_title='算法数量',
                    yaxis=dict(title='平均评分', side='left'),
                    yaxis2=dict(title='平均置信度', side='right', overlaying='y'),
                    legend=dict(x=0.7, y=1)
                )

                st.plotly_chart(fig, use_container_width=True)

                # 显示详细统计表
                st.dataframe(df, use_container_width=True)
            else:
                st.info("暂无算法效果数据")

    except Exception as e:
        st.error(f"生成算法效果分析图失败: {e}")


def save_user_preferences(preferences):
    """保存用户偏好设置"""
    # 实现用户偏好保存逻辑
    pass


if __name__ == "__main__":
    show_page()
