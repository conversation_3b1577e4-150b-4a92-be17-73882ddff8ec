#!/usr/bin/env python3
"""
SteamDT-ssync集成分析系统

完整流程：
1. 抓取饰品趋势和K线数据
2. 转换数据格式为ssync系统所需格式
3. 调用ssync实时监控和技术分析系统
4. 生成增强版技术分析图表
5. 输出完整的实时监控报告
"""

import asyncio
import sys
import json
import os
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入抓取系统
from src.cs2_investment.scraper.steamdt_scraper import SteamDTScraper
from src.cs2_investment.scraper.data_storage import DataStorage

# 导入数据服务
from src.cs2_investment.services.analysis_data_service import AnalysisDataService

# 使用统一日志组件
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)


class IntegratedSSyncSystem:
    """ssync集成分析系统"""
    
    def __init__(self, ssync_system_path: str = None):
        """
        初始化ssync集成分析系统
        
        Args:
            ssync_system_path: ssync系统路径，默认为相对路径
        """
        if ssync_system_path is None:
            # 使用正确的相对路径，指向ssync目录
            current_dir = Path(__file__).parent
            self.ssync_system_path = current_dir / "ssync"
        else:
            self.ssync_system_path = Path(ssync_system_path)
        
        # 确保ssync系统路径存在
        if not self.ssync_system_path.exists():
            raise FileNotFoundError(f"ssync系统路径不存在: {self.ssync_system_path}")

        # 初始化数据服务
        self.data_service = AnalysisDataService()
    
    async def run_complete_analysis(self, item_url: str, item_name: str = None) -> Dict:
        """
        运行完整的ssync集成分析流程
        
        Args:
            item_url: 饰品URL
            item_name: 饰品名称（用于ssync系统）
        
        Returns:
            Dict: 分析结果
        """
        print("=" * 80)
        print("🎯 SteamDT-ssync集成分析系统")
        print("📊 抓取 → 转换 → 实时监控 → 技术分析 → 图表生成")
        print("=" * 80)
        
        try:
            # 第一步：抓取数据
            print(f"\n🚀 第一步：抓取饰品数据")
            print(f"📍 URL: {item_url}")
            
            scraping_result = await self._scrape_data(item_url)
            if not scraping_result['success']:
                return {'success': False, 'error': f"数据抓取失败: {scraping_result['error']}"}
            
            # 第二步：转换数据格式
            print(f"\n🔄 第二步：转换数据格式（使用3个月趋势数据）")
            
            if not item_name:
                item_name = self._extract_item_name_from_url(item_url)
            
            conversion_result = self._convert_data_format_for_ssync(scraping_result['data'], item_name)
            if not conversion_result['success']:
                return {'success': False, 'error': f"数据转换失败: {conversion_result['error']}"}
            
            # 第三步：运行ssync实时监控系统
            print(f"\n📊 第三步：运行ssync实时监控和技术分析")
            
            ssync_result = self._run_ssync_system(conversion_result['data_path'])
            if not ssync_result['success']:
                return {'success': False, 'error': f"ssync分析失败: {ssync_result['error']}"}
            
            # 第四步：生成增强版技术图表
            print(f"\n📈 第四步：生成增强版技术图表")

            chart_result = self._generate_enhanced_charts(
                ssync_result['monitor'],
                item_name,
                conversion_result['data_path']  # 传递data_path用于提取item_id
            )
            if not chart_result['success']:
                print(f"⚠️ 图表生成失败: {chart_result['error']}")
                # 图表生成失败不影响整体流程

            # 第五步：保存分析结果到数据库
            print(f"\n💾 第五步：保存分析结果到数据库")

            database_result = self._save_realtime_analysis_to_database(
                ssync_result.get('result', {}),
                item_name,
                conversion_result.get('data_path', ''),
                chart_result.get('chart_path') if chart_result['success'] else None,
                None,  # 不再生成报告
                ssync_result.get('duration', 0)
            )

            # 第六步：整合结果
            print(f"\n📋 第六步：整合分析结果")

            final_result = {
                'success': True,
                'item_name': item_name,
                'item_url': item_url,
                'scraping_data': scraping_result['data'],
                'ssync_result': ssync_result['result'],
                'chart_result': chart_result if chart_result['success'] else None,
                'database_result': database_result,
                'data_path': conversion_result['data_path'],
                'chart_path': chart_result.get('chart_path') if chart_result['success'] else None,
                'completed_at': datetime.now().isoformat()
            }
            
            print(f"\n🎉 ssync集成分析完成！")
            print(f"✅ 数据抓取: 成功")
            print(f"✅ 数据转换: 成功")
            print(f"✅ 实时监控: 成功")
            print(f"✅ 图表生成: {'成功' if chart_result['success'] else '失败'}")
            print(f"✅ 数据库保存: {'成功' if database_result['success'] else '失败'}")
            print(f"📁 数据路径: {conversion_result['data_path']}")
            if chart_result['success']:
                print(f"📈 图表路径: {chart_result.get('chart_path', '未知')}")
            
            return final_result
            
        except Exception as e:
            logger.error(f"ssync集成分析失败: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _scrape_data(self, item_url: str) -> Dict:
        """抓取数据（ssync系统按需抓取）"""
        try:
            # ssync系统数据需求：时K、3个月趋势数据
            data_requirements = {
                'hourly_kline': True,
                'trend_data_3m': True,
                'daily_kline_1': True,  # 可选，异常检测用
                'daily_kline_2': True,  # 可选，异常检测用
                'weekly_kline': True,   # 可选，异常检测用
                'trend_data_6m': True   # 不需要
            }

            async with SteamDTScraper() as scraper:
                result = await scraper.scrape_item_data(item_url, data_requirements)

                if result.success:
                    return {
                        'success': True,
                        'data': result
                    }
                else:
                    return {
                        'success': False,
                        'error': result.error_message
                    }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _extract_item_name_from_url(self, item_url: str) -> str:
        """从URL提取饰品名称"""
        try:
            # 从URL中提取饰品名称
            import urllib.parse
            
            url_parts = item_url.split('/')
            if len(url_parts) >= 2:
                encoded_name = url_parts[-1]
                decoded_name = urllib.parse.unquote(encoded_name)
                # 替换特殊字符
                decoded_name = decoded_name.replace('%7C', '|').replace('%20', ' ')
                return decoded_name
            else:
                return f"Item_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
        except Exception as e:
            logger.warning(f"提取饰品名称失败: {e}")
            return f"Item_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    def _generate_realtime_report_content(self, analysis_result: dict, item_name: str) -> str:
        """生成实时分析报告内容"""
        try:
            lines = []

            # 标题和时间
            lines.append("=" * 60)
            lines.append(f"📋 {item_name} - 实时分析报告")
            lines.append(f"📅 生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}")
            lines.append("=" * 60)

            # 基本信息
            lines.append("")
            lines.append("📊 实时监控信息")
            current_price = analysis_result.get('current_price')
            if current_price:
                lines.append(f"   当前价格：{current_price}元")

            price_change = analysis_result.get('price_change')
            if price_change:
                lines.append(f"   价格变化：{price_change}")

            volume_status = analysis_result.get('volume_status', 'UNKNOWN')
            lines.append(f"   成交量状态：{volume_status}")

            # 交易信号
            trading_signals = analysis_result.get('trading_signals', {})
            if trading_signals:
                lines.append("")
                lines.append("🚨 交易信号")
                signal = trading_signals.get('signal', '无信号')
                confidence = trading_signals.get('confidence', 0)
                reason = trading_signals.get('reason', '无详细说明')

                lines.append(f"   信号类型：{signal}")
                lines.append(f"   置信度：{confidence}%")
                lines.append(f"   信号原因：{reason}")

            # 风险评估
            risk_assessment = analysis_result.get('risk_assessment', {})
            if risk_assessment:
                lines.append("")
                lines.append("⚠️ 风险评估")
                risk_level = risk_assessment.get('risk_level', '评估中')
                volatility = risk_assessment.get('volatility', '计算中')
                liquidity = risk_assessment.get('liquidity', '分析中')

                lines.append(f"   风险等级：{risk_level}")
                lines.append(f"   波动性：{volatility}")
                lines.append(f"   流动性：{liquidity}")

                stop_loss = risk_assessment.get('stop_loss')
                take_profit = risk_assessment.get('take_profit')
                if stop_loss:
                    lines.append(f"   建议止损位：{stop_loss}元")
                if take_profit:
                    lines.append(f"   建议止盈位：{take_profit}元")

            # 支撑阻力位
            support_resistance = analysis_result.get('support_resistance', {})
            if support_resistance:
                lines.append("")
                lines.append("📈 支撑阻力位")

                support_levels = support_resistance.get('support_levels', [])
                resistance_levels = support_resistance.get('resistance_levels', [])

                if support_levels:
                    lines.append("   支撑位：")
                    for level in support_levels[:3]:  # 显示前3个
                        lines.append(f"     - {level}元")

                if resistance_levels:
                    lines.append("   阻力位：")
                    for level in resistance_levels[:3]:  # 显示前3个
                        lines.append(f"     - {level}元")

            # 异常检测
            anomaly_results = analysis_result.get('anomaly_results', {})
            if anomaly_results:
                lines.append("")
                lines.append("🔍 异常检测")

                total_anomalies = anomaly_results.get('total_anomalies', 0)
                risk_assessment_anomaly = anomaly_results.get('risk_assessment', '正常')

                lines.append(f"   异常数量：{total_anomalies}")
                lines.append(f"   风险评估：{risk_assessment_anomaly}")

                by_type = anomaly_results.get('by_type', {})
                if by_type:
                    lines.append("   异常类型分布：")
                    for anomaly_type, count in by_type.items():
                        lines.append(f"     - {anomaly_type}: {count}个")

            # 免责声明
            lines.append("")
            lines.append("=" * 60)
            lines.append("📝 免责声明：实时分析仅供参考，投资有风险，入市需谨慎。")
            lines.append("=" * 60)

            return "\n".join(lines)

        except Exception as e:
            logger.error(f"生成实时报告内容失败: {e}")
            return f"实时分析报告生成失败: {str(e)}"

    def _save_realtime_analysis_to_database(self, analysis_result: Dict, item_name: str,
                                           data_path: str, chart_path: str = None,
                                           report_path: str = None, analysis_duration: float = None) -> Dict:
        """保存实时分析结果到数据库"""
        try:
            print(f"🔍 开始保存实时分析结果到数据库...")

            # 保存实时分析结果
            db_result_id = self.data_service.save_realtime_analysis_result(
                analysis_result=analysis_result,
                item_name=item_name,
                data_path=data_path,
                chart_path=chart_path,
                report_path=report_path,
                analysis_duration=analysis_duration
            )

            if db_result_id:
                print(f"✅ 实时分析结果已保存到数据库: ID={db_result_id}")
                return {
                    'success': True,
                    'database_id': db_result_id,
                    'message': '实时分析结果保存成功'
                }
            else:
                print(f"❌ 实时分析结果保存失败")
                return {
                    'success': False,
                    'error': '数据库保存失败'
                }

        except Exception as e:
            print(f"❌ 保存实时分析结果到数据库时发生错误: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _convert_data_format_for_ssync(self, scraping_result, item_name: str) -> Dict:
        """转换数据格式为ssync系统所需格式"""
        try:
            # 获取item_id作为目录名
            item_id = None
            if scraping_result.trend_data_3m:
                item_id = scraping_result.trend_data_3m.item_id
            elif scraping_result.trend_data_6m:
                item_id = scraping_result.trend_data_6m.item_id
            elif scraping_result.hourly_kline:
                item_id = scraping_result.hourly_kline.item_id
            elif scraping_result.daily_kline_1:
                item_id = scraping_result.daily_kline_1.item_id

            if not item_id:
                raise ValueError("无法获取饰品ID")

            # 创建数据目录结构：data/scraped_data/{item_id}/
            project_root = Path(__file__).parent.parent.parent.parent
            data_dir = project_root / "data" / "scraped_data" / str(item_id)
            data_dir.mkdir(parents=True, exist_ok=True)

            print(f"📁 创建ssync数据目录: {data_dir}")
            print(f"🆔 饰品ID: {item_id}")
            print(f"📝 饰品名称: {item_name}")
            
            # 转换并保存各种数据
            conversion_success = 0
            total_conversions = 5
            
            # 1. 转换时K数据
            if scraping_result.hourly_kline and scraping_result.hourly_kline.raw_data:
                hourly_file = data_dir / "时k.json"
                with open(hourly_file, 'w', encoding='utf-8') as f:
                    json.dump(scraping_result.hourly_kline.raw_data, f, ensure_ascii=False, indent=2)
                print(f"✅ 时K数据: {len(scraping_result.hourly_kline.raw_data)} 条")
                conversion_success += 1
            
            # 2. 转换日K数据（第一次响应）
            if scraping_result.daily_kline_1 and scraping_result.daily_kline_1.raw_data:
                daily1_file = data_dir / "日k1.json"
                with open(daily1_file, 'w', encoding='utf-8') as f:
                    json.dump(scraping_result.daily_kline_1.raw_data, f, ensure_ascii=False, indent=2)
                print(f"✅ 日K1数据: {len(scraping_result.daily_kline_1.raw_data)} 条")
                conversion_success += 1
            
            # 3. 转换日K数据（第二次响应）
            if scraping_result.daily_kline_2 and scraping_result.daily_kline_2.raw_data:
                daily2_file = data_dir / "日k2.json"
                with open(daily2_file, 'w', encoding='utf-8') as f:
                    json.dump(scraping_result.daily_kline_2.raw_data, f, ensure_ascii=False, indent=2)
                print(f"✅ 日K2数据: {len(scraping_result.daily_kline_2.raw_data)} 条")
                conversion_success += 1
            
            # 4. 转换周K数据
            if scraping_result.weekly_kline and scraping_result.weekly_kline.raw_data:
                weekly_file = data_dir / "周k.json"
                with open(weekly_file, 'w', encoding='utf-8') as f:
                    json.dump(scraping_result.weekly_kline.raw_data, f, ensure_ascii=False, indent=2)
                print(f"✅ 周K数据: {len(scraping_result.weekly_kline.raw_data)} 条")
                conversion_success += 1
            
            # 5. 转换趋势数据（使用3个月数据作为走势数据）
            if scraping_result.trend_data_3m and scraping_result.trend_data_3m.raw_data:
                trend_file = data_dir / "走势.json"
                with open(trend_file, 'w', encoding='utf-8') as f:
                    json.dump(scraping_result.trend_data_3m.raw_data, f, ensure_ascii=False, indent=2)
                print(f"✅ 走势数据: {len(scraping_result.trend_data_3m.raw_data)} 条（3个月数据）")
                conversion_success += 1
            
            print(f"📊 数据转换完成: {conversion_success}/{total_conversions}")

            # ssync系统只需要2种核心数据：时K和3个月趋势
            if conversion_success >= 2:  # ssync系统至少需要2种核心数据
                return {
                    'success': True,
                    'data_path': str(data_dir),
                    'conversions': conversion_success
                }
            else:
                return {
                    'success': False,
                    'error': f"数据转换不完整: 只成功转换了 {conversion_success}/{total_conversions} 种数据"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _run_ssync_system(self, data_path: str) -> Dict:
        """运行ssync实时监控系统"""
        try:
            # 切换到ssync系统目录
            original_cwd = os.getcwd()
            original_path = sys.path.copy()

            os.chdir(self.ssync_system_path)
            sys.path.insert(0, str(self.ssync_system_path))

            try:
                # 清除可能的模块缓存
                import importlib

                # 导入ssync系统
                if 'real_time_monitor' in sys.modules:
                    importlib.reload(sys.modules['real_time_monitor'])

                from real_time_monitor import FixedRealTimeMonitor

                # 创建实时监控系统实例
                monitor = FixedRealTimeMonitor(data_path)

                # 运行完整监控分析
                print(f"🔄 加载数据并初始化监控系统...")
                monitor.load_data()

                print(f"📊 生成增强版实时信号...")
                trading_signals = monitor.generate_enhanced_real_time_signals()

                print(f"⚠️ 检测支撑阻力位...")
                sr_data = monitor.detect_support_resistance_real_time()

                print(f"📈 计算风险管理参数...")
                risk_assessment = monitor.calculate_risk_management(trading_signals, sr_data)

                print(f"🔍 执行成交量异常检测...")
                anomaly_results = monitor.detect_volume_anomaly_real_time()

                # 整合所有分析结果
                analysis_result = {
                    'risk_assessment': risk_assessment,
                    'trading_signals': trading_signals,
                    'support_resistance': sr_data,
                    'anomaly_results': anomaly_results,
                    'current_price': sr_data.get('current_price', None),
                    'price_change': getattr(monitor, 'price_change', None),
                    'volume_status': anomaly_results.get('alert', 'NORMAL')
                }

                # 生成实时报告内容
                # 从数据路径中提取item_name用于报告标题
                from pathlib import Path
                item_name_for_report = Path(data_path).name
                realtime_report = self._generate_realtime_report_content(
                    analysis_result, item_name_for_report
                )
                analysis_result['realtime_report'] = realtime_report

                return {
                    'success': True,
                    'result': analysis_result,
                    'monitor': monitor  # 返回监控器实例用于图表生成
                }

            finally:
                # 恢复原始工作目录和路径
                os.chdir(original_cwd)
                sys.path = original_path

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除或替换无效字符（与常规分析系统保持一致）"""
        import re

        # 定义无效字符的映射
        char_replacements = {
            '★': 'Star',
            '（': '(',
            '）': ')',
            '|': '_',
            '/': '_',
            '\\': '_',
            ':': '_',
            '*': '_',
            '?': '_',
            '"': '_',
            '<': '_',
            '>': '_',
            '™': 'TM',
            '®': 'R'
        }

        # 替换特殊字符
        safe_filename = filename
        for old_char, new_char in char_replacements.items():
            safe_filename = safe_filename.replace(old_char, new_char)

        # 移除其他可能的无效字符
        safe_filename = re.sub(r'[^\w\s\-_().]', '_', safe_filename)

        # 清理多余的空格和下划线
        safe_filename = re.sub(r'\s+', '_', safe_filename)
        safe_filename = re.sub(r'_+', '_', safe_filename)
        safe_filename = safe_filename.strip('_')

        return safe_filename

    def _generate_enhanced_charts(self, monitor, item_name: str, data_path: str) -> Dict:
        """生成增强版技术图表（使用item_id命名，与常规分析保持一致）"""
        try:
            # 切换到ssync系统目录
            original_cwd = os.getcwd()
            original_path = sys.path.copy()

            os.chdir(self.ssync_system_path)
            sys.path.insert(0, str(self.ssync_system_path))

            try:
                # 生成基于item_id和时间戳的图表保存路径（与常规分析系统保持一致）
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                # 从data_path中提取item_id（与常规分析系统保持一致）
                item_id = Path(data_path).name if data_path else f"unknown_{timestamp}"

                # 构建保存路径：data/analysis_results/{item_id}/（与常规分析保持一致）
                project_root = Path(__file__).parent.parent.parent.parent
                analysis_dir = project_root / "data" / "analysis_results" / str(item_id)
                analysis_dir.mkdir(parents=True, exist_ok=True)

                # 使用与常规分析一致的命名格式：{item_id}_增强版技术分析图表_{timestamp}.png
                chart_filename = f"{item_id}_增强版技术分析图表_{timestamp}.png"
                chart_path = analysis_dir / chart_filename

                print(f"🔍 饰品名称: {item_name}")
                print(f"🔍 饰品ID: {item_id}")
                print(f"🔍 时间戳: {timestamp}")
                print(f"🔍 图表保存路径: {chart_path}")

                # 生成增强版技术分析图表
                monitor.generate_enhanced_chart(
                    save_path=str(chart_path),
                    show_chart=False
                )

                print(f"✅ 增强版技术图表已生成: {chart_filename}")

                return {
                    'success': True,
                    'chart_path': str(chart_path),
                    'chart_filename': chart_filename,
                    'item_id': item_id
                }

            finally:
                # 恢复原始工作目录和路径
                os.chdir(original_cwd)
                sys.path = original_path

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_comprehensive_report(self, scraping_data, ssync_result, chart_result, item_name: str) -> Dict:
        """生成完整的实时监控分析报告（修复文件名特殊字符问题）"""
        try:
            # 清理文件名，移除特殊字符
            safe_item_name = self._sanitize_filename(item_name)

            # 生成基于item_id和时间戳的报告保存路径（参考常规分析系统）
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 构建保存路径：data/analysis_results/{item_id}/
            project_root = Path(__file__).parent.parent.parent.parent

            # 尝试从scraping_data获取item_id，如果没有则使用安全的item_name
            item_id = getattr(scraping_data, 'item_id', None) or safe_item_name

            analysis_dir = project_root / "data" / "analysis_results" / str(item_id)
            analysis_dir.mkdir(parents=True, exist_ok=True)

            report_filename = f"{item_id}_实时监控分析报告_{timestamp}.md"
            report_path = analysis_dir / report_filename

            print(f"🔍 报告保存路径: {report_path}")

            # 生成报告内容
            report_content = self._create_ssync_report_content(scraping_data, ssync_result, chart_result, item_name)

            # 保存报告
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)

            print(f"✅ 实时监控分析报告已生成: {report_filename}")

            return {
                'success': True,
                'report_path': str(report_path),
                'report_filename': report_filename
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _create_ssync_report_content(self, scraping_data, ssync_result, chart_result, item_name: str) -> str:
        """创建ssync报告内容"""
        from datetime import datetime

        # 获取数据统计
        data_stats = {
            'trend_3m_count': len(scraping_data.trend_data_3m.raw_data) if scraping_data.trend_data_3m and scraping_data.trend_data_3m.raw_data else 0,
            'trend_6m_count': len(scraping_data.trend_data_6m.raw_data) if scraping_data.trend_data_6m and scraping_data.trend_data_6m.raw_data else 0,
            'hourly_count': len(scraping_data.hourly_kline.raw_data) if scraping_data.hourly_kline and scraping_data.hourly_kline.raw_data else 0,
            'daily_1_count': len(scraping_data.daily_kline_1.raw_data) if scraping_data.daily_kline_1 and scraping_data.daily_kline_1.raw_data else 0,
            'daily_2_count': len(scraping_data.daily_kline_2.raw_data) if scraping_data.daily_kline_2 and scraping_data.daily_kline_2.raw_data else 0,
            'weekly_count': len(scraping_data.weekly_kline.raw_data) if scraping_data.weekly_kline and scraping_data.weekly_kline.raw_data else 0
        }

        # 获取ssync分析结果
        trading_signals = ssync_result.get('trading_signals', {})
        risk_assessment = ssync_result.get('risk_assessment', {})
        support_resistance = ssync_result.get('support_resistance', {})
        anomaly_results = ssync_result.get('anomaly_results', {})

        report_content = f"""# {item_name} - 实时监控分析报告

## 📊 报告概览

**生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
**分析系统**: SteamDT-ssync集成分析系统
**数据来源**: SteamDT官方API
**分析周期**: 3个月高频实时监控分析
**分析类型**: 短期技术分析 + 实时监控

---

## 📈 数据统计

### 原始数据获取情况
- **3个月趋势数据**: {data_stats['trend_3m_count']:,} 条记录 ⭐ (主要分析数据)
- **6个月趋势数据**: {data_stats['trend_6m_count']:,} 条记录
- **时K线数据**: {data_stats['hourly_count']:,} 条记录
- **日K线数据1**: {data_stats['daily_1_count']:,} 条记录
- **日K线数据2**: {data_stats['daily_2_count']:,} 条记录
- **周K线数据**: {data_stats['weekly_count']:,} 条记录

### 数据质量评估
- ✅ **数据完整性**: 所有关键数据类型均已获取
- ✅ **时间跨度**: 覆盖3个月高频历史数据，适合短期技术分析
- ✅ **数据密度**: 小时级别高频数据，适合实时监控和技术分析

---

## 🚨 实时监控信号

### 当前交易信号
**信号类型**: {trading_signals.get('signal', '监控中')}
**信号强度**: {trading_signals.get('strength', '计算中')}
**建议价位**: {trading_signals.get('price', '分析中')}
**置信度**: {trading_signals.get('confidence', '评估中')}

### 信号详情
{trading_signals.get('description', '正在进行实时信号分析，基于多指标融合算法生成交易建议。')}

---

## 📊 技术分析

### 支撑阻力位分析
**当前价格**: {support_resistance.get('current_price', '获取中')}
**关键支撑位**: {support_resistance.get('support_levels', '计算中')}
**关键阻力位**: {support_resistance.get('resistance_levels', '计算中')}

### 价格突破分析
{support_resistance.get('breakout_analysis', '正在分析价格突破情况，识别关键技术位的突破信号。')}

### 趋势强度
{trading_signals.get('trend_strength', '基于多重技术指标评估当前趋势强度和持续性。')}

---

## ⚠️ 风险管理

### 风险评估
**风险等级**: {risk_assessment.get('risk_level', '评估中')}
**波动性**: {risk_assessment.get('volatility', '计算中')}
**流动性**: {risk_assessment.get('liquidity', '分析中')}

### 风险控制建议
**止损位**: {risk_assessment.get('stop_loss', '计算中')}
**止盈位**: {risk_assessment.get('take_profit', '计算中')}
**仓位建议**: {risk_assessment.get('position_size', '评估中')}

### 详细风险分析
{risk_assessment.get('detailed_analysis', '正在进行全面风险评估，包括市场风险、流动性风险和技术风险。')}

---

## 🔍 异常检测

### 成交量异常
**异常状态**: {anomaly_results.get('alert', 'NORMAL')}
**异常类型**: {anomaly_results.get('anomaly_type', '无异常')}
**异常强度**: {anomaly_results.get('severity', '正常')}

### 异常详情
{anomaly_results.get('description', '实时监控成交量异常情况，及时发现市场异常波动。')}

---

## 📈 增强版技术图表

### 技术分析图表
{f"✅ 已生成增强版技术分析图表" if chart_result else "❌ 图表生成失败"}

图表包含：
- 📊 价格走势图（3个月高频数据）
- 📈 实时交易信号标注
- 📉 支撑阻力位可视化
- 🎯 关键技术指标（MACD、RSI、KDJ等）
- ⚠️ 风险管理位置标注
- 🔍 异常检测结果显示

---

## 📋 数据文件

### 保存的数据文件
- `时k.json` - 时K线数据 ({data_stats['hourly_count']:,} 条)
- `日k1.json` - 日K线数据第1次响应 ({data_stats['daily_1_count']:,} 条)
- `日k2.json` - 日K线数据第2次响应 ({data_stats['daily_2_count']:,} 条)
- `周k.json` - 周K线数据 ({data_stats['weekly_count']:,} 条)
- `走势.json` - 走势数据，基于3个月高频趋势数据 ({data_stats['trend_3m_count']:,} 条)

### 图表文件
- `{item_name}_增强版技术分析图表.png` - 专业技术分析图表

---

## 🎯 交易策略建议

### 短期交易策略
基于3个月高频数据分析，该饰品适合以下交易策略：

1. **技术分析交易**: 基于技术指标进行短期交易
2. **突破交易**: 关注关键支撑阻力位的突破
3. **趋势跟踪**: 跟随短期趋势进行交易
4. **风险控制**: 严格执行止损止盈策略

### 关键交易信号
- **买入信号**: {trading_signals.get('buy_signals', '基于技术分析生成')}
- **卖出信号**: {trading_signals.get('sell_signals', '基于技术分析生成')}
- **观望信号**: {trading_signals.get('hold_signals', '基于市场情况判断')}

### 实时监控要点
1. **价格突破**: 密切关注关键技术位的突破
2. **成交量变化**: 监控异常成交量变化
3. **技术指标**: 关注多重技术指标的共振信号
4. **风险控制**: 及时调整止损止盈位置

---

## ⚡ 免责声明

本报告基于实时数据和技术分析生成，专注于短期技术分析，仅供交易参考，不构成投资建议。CS2饰品市场存在较高风险，交易者应：

1. **充分了解风险**: CS2饰品价格波动极大，短期交易风险更高
2. **理性交易**: 不要投入超过承受能力的资金
3. **严格风控**: 必须设置止损位，控制单次交易风险
4. **实时监控**: 市场情况可能瞬息万变，需要实时关注

**短期交易风险极高，请谨慎操作！**

---

*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*分析系统: SteamDT-ssync集成分析系统 v1.0*
*数据更新频率: 实时*
"""

        return report_content


async def main():
    """主函数 - 示例用法"""
    if len(sys.argv) < 2:
        print("使用方法: python integrated_ssync_system.py <饰品URL> [饰品名称]")
        print("示例: python integrated_ssync_system.py \"https://steamdt.com/cs2/Desert%20Eagle%20%7C%20Printstream%20(Field-Tested)\"")
        return

    item_url = sys.argv[1]
    item_name = sys.argv[2] if len(sys.argv) > 2 else None

    # 创建ssync集成分析系统
    system = IntegratedSSyncSystem()

    # 运行完整分析
    result = await system.run_complete_analysis(item_url, item_name)

    if result['success']:
        print(f"\n🎉 ssync集成分析成功完成！")
        print(f"📊 分析结果已保存")
    else:
        print(f"\n❌ ssync集成分析失败: {result['error']}")


if __name__ == "__main__":
    asyncio.run(main())
