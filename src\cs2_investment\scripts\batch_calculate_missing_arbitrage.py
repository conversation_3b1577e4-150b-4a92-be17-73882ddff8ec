#!/usr/bin/env python3
"""
批量计算缺失的搬砖率

为所有可以计算搬砖率但是没有保存到items表的饰品批量计算搬砖率
"""

import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.config.database import get_db_session
from src.cs2_investment.services.arbitrage_calculator import ArbitrageCalculator
from src.cs2_investment.utils.logger import get_logger
from sqlalchemy import text

logger = get_logger(__name__)


def get_items_need_arbitrage_calculation(session, limit=None):
    """获取需要计算搬砖率的饰品列表"""
    
    query = """
    SELECT 
        i.item_id,
        i.market_hash_name
    FROM items i
    INNER JOIN platform_prices pp ON i.item_id = pp.item_id
    WHERE pp.is_active = 1
      AND i.market_hash_name IS NOT NULL
      AND (i.arbitrage_ratio IS NULL OR i.arbitrage_ratio = 0)
    GROUP BY i.item_id, i.market_hash_name, i.arbitrage_ratio
    HAVING 
        -- 有BUFF或YOUPIN价格
        SUM(CASE WHEN pp.platform_name IN ('BUFF', 'YOUPIN') AND pp.sell_price > 0 THEN 1 ELSE 0 END) > 0
        -- 有Steam价格
        AND SUM(CASE WHEN pp.platform_name = 'STEAM' AND pp.sell_price > 0 THEN 1 ELSE 0 END) > 0
    ORDER BY i.market_hash_name
    """
    
    if limit:
        query += f" LIMIT {limit}"
    
    result = session.execute(text(query))
    return [{"item_id": row[0], "market_hash_name": row[1]} for row in result.fetchall()]


def batch_calculate_arbitrage_ratios(batch_size=100, max_items=None):
    """批量计算搬砖率"""
    
    logger.info("🚀 开始批量计算缺失的搬砖率")
    
    # 初始化计算器
    arbitrage_calculator = ArbitrageCalculator()
    
    # 统计信息
    stats = {
        'total_processed': 0,
        'success_count': 0,
        'failed_count': 0,
        'skipped_count': 0,
        'start_time': datetime.now()
    }
    
    try:
        with get_db_session() as session:
            # 获取需要计算搬砖率的饰品
            logger.info("📦 获取需要计算搬砖率的饰品...")
            items = get_items_need_arbitrage_calculation(session, limit=max_items)
            
            if not items:
                logger.info("✅ 没有需要计算搬砖率的饰品")
                return stats
            
            logger.info(f"找到 {len(items)} 个需要计算搬砖率的饰品")
            
            # 分批处理
            for i in range(0, len(items), batch_size):
                batch = items[i:i + batch_size]
                batch_num = i // batch_size + 1
                total_batches = (len(items) + batch_size - 1) // batch_size
                
                logger.info(f"🔄 处理第 {batch_num}/{total_batches} 批 ({len(batch)} 个饰品)")
                
                for item in batch:
                    item_id = item['item_id']
                    market_hash_name = item['market_hash_name']
                    
                    try:
                        # 计算搬砖率
                        success = arbitrage_calculator.update_item_arbitrage_ratio(item_id)
                        
                        if success:
                            stats['success_count'] += 1
                            logger.debug(f"✅ 搬砖率计算成功: {market_hash_name}")
                        else:
                            stats['skipped_count'] += 1
                            logger.debug(f"⚠️ 搬砖率计算跳过: {market_hash_name}")
                        
                        stats['total_processed'] += 1
                        
                        # 每100个显示一次进度
                        if stats['total_processed'] % 100 == 0:
                            logger.info(f"📊 进度: {stats['total_processed']}/{len(items)} "
                                      f"(成功: {stats['success_count']}, "
                                      f"跳过: {stats['skipped_count']}, "
                                      f"失败: {stats['failed_count']})")
                        
                    except Exception as e:
                        stats['failed_count'] += 1
                        logger.error(f"❌ 搬砖率计算失败: {market_hash_name} - {e}")
                        continue
                
                # 批次完成提示
                logger.info(f"✅ 第 {batch_num} 批处理完成")
        
        # 计算总耗时
        stats['end_time'] = datetime.now()
        stats['duration'] = (stats['end_time'] - stats['start_time']).total_seconds()
        
        # 显示最终统计
        logger.info("🎯 批量搬砖率计算完成")
        logger.info(f"📊 处理统计:")
        logger.info(f"  - 总处理数: {stats['total_processed']}")
        logger.info(f"  - 成功计算: {stats['success_count']}")
        logger.info(f"  - 跳过计算: {stats['skipped_count']}")
        logger.info(f"  - 计算失败: {stats['failed_count']}")
        logger.info(f"  - 总耗时: {stats['duration']:.2f} 秒")
        logger.info(f"  - 平均速度: {stats['total_processed'] / stats['duration']:.2f} 个/秒")
        
        return stats
        
    except Exception as e:
        logger.error(f"❌ 批量计算过程中发生异常: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return stats


def verify_calculation_results():
    """验证计算结果"""
    logger.info("🔍 验证搬砖率计算结果...")
    
    try:
        with get_db_session() as session:
            # 重新查询缺失搬砖率的饰品数量
            query = """
            SELECT COUNT(DISTINCT item_id) as count
            FROM (
                SELECT 
                    i.item_id,
                    i.market_hash_name,
                    i.arbitrage_ratio,
                    SUM(CASE WHEN pp.platform_name IN ('BUFF', 'YOUPIN') AND pp.sell_price > 0 THEN 1 ELSE 0 END) as non_steam_platforms,
                    SUM(CASE WHEN pp.platform_name = 'STEAM' AND pp.sell_price > 0 THEN 1 ELSE 0 END) as steam_platforms
                FROM items i
                INNER JOIN platform_prices pp ON i.item_id = pp.item_id
                WHERE pp.is_active = 1
                  AND i.market_hash_name IS NOT NULL
                  AND (i.arbitrage_ratio IS NULL OR i.arbitrage_ratio = 0)
                GROUP BY i.item_id, i.market_hash_name, i.arbitrage_ratio
                HAVING non_steam_platforms > 0 AND steam_platforms > 0
            ) as calculable_items
            """
            
            result = session.execute(text(query))
            remaining_count = result.scalar()
            
            logger.info(f"📊 验证结果: 还有 {remaining_count} 个饰品需要计算搬砖率")
            
            # 查询已有搬砖率的饰品数量
            query2 = """
            SELECT COUNT(*) as count
            FROM items 
            WHERE arbitrage_ratio IS NOT NULL 
              AND arbitrage_ratio > 0
            """
            
            result2 = session.execute(text(query2))
            total_with_arbitrage = result2.scalar()
            
            logger.info(f"📊 当前已有搬砖率的饰品: {total_with_arbitrage} 个")
            
    except Exception as e:
        logger.error(f"❌ 验证过程中发生异常: {e}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='批量计算缺失的搬砖率')
    parser.add_argument('--batch-size', type=int, default=100, help='批处理大小 (默认: 100)')
    parser.add_argument('--max-items', type=int, help='最大处理饰品数量 (默认: 全部)')
    parser.add_argument('--verify-only', action='store_true', help='仅验证，不执行计算')
    
    args = parser.parse_args()
    
    if args.verify_only:
        verify_calculation_results()
    else:
        # 执行批量计算
        stats = batch_calculate_arbitrage_ratios(
            batch_size=args.batch_size,
            max_items=args.max_items
        )
        
        # 验证结果
        verify_calculation_results()


if __name__ == "__main__":
    main()
