"""
传统系统桥接器

负责与现有系统(UnifiedAnalysisSystem、syncps、ssync)的桥接和数据转换。
确保智能分析系统能够无缝集成到现有的工作流程中。
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from ...unified_analysis_system import UnifiedAnalysisSystem
from ...services.investment_recommendation_service import InvestmentRecommendationService
from ...services.analysis_data_service import AnalysisDataService


class LegacySystemBridge:
    """传统系统桥接器"""
    
    def __init__(self):
        """初始化传统系统桥接器"""
        self.logger = logger.bind(bridge=self.__class__.__name__)
        
        # 初始化传统系统组件
        self.unified_analysis_system = None
        self.investment_recommendation_service = InvestmentRecommendationService()
        self.analysis_data_service = AnalysisDataService()
        
        # 数据转换映射
        self.recommendation_type_mapping = {
            # 智能系统 -> 传统系统
            'STRONG_BUY': 'STRONG_BUY',
            'BUY': 'BUY',
            'HOLD': 'HOLD',
            'SELL': 'SELL',
            'STRONG_SELL': 'STRONG_SELL'
        }
        
        self.risk_level_mapping = {
            # 智能系统 -> 传统系统
            'LOW': 'LOW',
            'MEDIUM': 'MEDIUM',
            'HIGH': 'HIGH'
        }
        
        self.logger.info("传统系统桥接器初始化完成")
    
    def initialize_unified_analysis_system(self, syncps_path: str = None, ssync_path: str = None):
        """初始化统一分析系统"""
        try:
            self.unified_analysis_system = UnifiedAnalysisSystem(syncps_path, ssync_path)
            self.logger.info("统一分析系统初始化完成")
        except Exception as e:
            self.logger.error(f"统一分析系统初始化失败: {e}")
            raise
    
    async def run_unified_analysis(self, item_id: str, market_hash_name: str) -> Dict[str, Any]:
        """运行统一分析系统"""
        try:
            if not self.unified_analysis_system:
                raise Exception("统一分析系统未初始化")
            
            self.logger.info(f"运行统一分析: {item_id}")
            
            # 调用统一分析系统
            result = await asyncio.to_thread(
                self.unified_analysis_system.analyze_item,
                market_hash_name
            )
            
            # 转换结果格式
            converted_result = self._convert_unified_analysis_result(result, item_id)
            
            self.logger.info(f"统一分析完成: {item_id}")
            return converted_result
            
        except Exception as e:
            self.logger.error(f"统一分析失败 ({item_id}): {e}")
            return {
                'item_id': item_id,
                'analysis_type': 'unified',
                'success': False,
                'error': str(e),
                'timestamp': datetime.now()
            }
    
    def generate_legacy_recommendation(self, item_id: str) -> Dict[str, Any]:
        """生成传统推荐"""
        try:
            self.logger.info(f"生成传统推荐: {item_id}")
            
            # 使用投资推荐服务
            recommendation = self.investment_recommendation_service.generate_recommendation(item_id)
            
            # 转换结果格式
            converted_recommendation = self._convert_legacy_recommendation(recommendation, item_id)
            
            self.logger.info(f"传统推荐生成完成: {item_id}")
            return converted_recommendation
            
        except Exception as e:
            self.logger.error(f"传统推荐生成失败 ({item_id}): {e}")
            return {
                'item_id': item_id,
                'recommendation_type': 'HOLD',
                'confidence_level': 0,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now()
            }
    
    def get_analysis_data(self, item_id: str) -> Dict[str, Any]:
        """获取分析数据"""
        try:
            self.logger.info(f"获取分析数据: {item_id}")
            
            # 使用分析数据服务
            analysis_data = self.analysis_data_service.get_analysis_data(item_id)
            
            # 转换数据格式
            converted_data = self._convert_analysis_data(analysis_data, item_id)
            
            self.logger.info(f"分析数据获取完成: {item_id}")
            return converted_data
            
        except Exception as e:
            self.logger.error(f"分析数据获取失败 ({item_id}): {e}")
            return {
                'item_id': item_id,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now()
            }
    
    def convert_intelligent_to_legacy_format(self, intelligent_result: Dict) -> Dict[str, Any]:
        """将智能分析结果转换为传统系统格式"""
        try:
            item_id = intelligent_result.get('item_id', '')
            
            # 提取智能分析的核心结果
            fusion_result = intelligent_result.get('fusion_result', {})
            final_decision = fusion_result.get('final_decision', {})
            
            # 转换为传统格式
            legacy_format = {
                'item_id': item_id,
                'recommendation_type': self.recommendation_type_mapping.get(
                    final_decision.get('recommendation_type', 'HOLD'), 'HOLD'
                ),
                'total_score': final_decision.get('total_score', 50),
                'confidence_level': final_decision.get('confidence_level', 50),
                'risk_level': self.risk_level_mapping.get(
                    final_decision.get('risk_level', 'MEDIUM'), 'MEDIUM'
                ),
                'investment_horizon': final_decision.get('investment_horizon', 'MEDIUM_TERM'),
                'algorithm_count': 4,  # 智能系统使用4个维度分析
                'recommendation_reason': self._generate_legacy_reason(final_decision),
                'recommendation_date': datetime.now().date(),
                'recommendation_time': datetime.now(),
                'source': 'intelligent_analysis',
                'original_result': intelligent_result
            }
            
            return legacy_format
            
        except Exception as e:
            self.logger.error(f"智能结果转换失败: {e}")
            return {
                'item_id': intelligent_result.get('item_id', ''),
                'recommendation_type': 'HOLD',
                'total_score': 50,
                'confidence_level': 30,
                'risk_level': 'MEDIUM',
                'error': str(e)
            }
    
    def convert_legacy_to_intelligent_format(self, legacy_result: Dict) -> Dict[str, Any]:
        """将传统分析结果转换为智能系统格式"""
        try:
            item_id = legacy_result.get('item_id', '')
            
            # 转换为智能系统格式
            intelligent_format = {
                'item_id': item_id,
                'analysis_type': 'legacy_converted',
                'analysis_timestamp': datetime.now(),
                'success': True,
                'fusion_result': {
                    'success': True,
                    'final_decision': {
                        'recommendation_type': legacy_result.get('recommendation_type', 'HOLD'),
                        'total_score': legacy_result.get('total_score', 50),
                        'confidence_level': legacy_result.get('confidence_level', 50),
                        'risk_level': legacy_result.get('risk_level', 'MEDIUM'),
                        'investment_horizon': legacy_result.get('investment_horizon', 'MEDIUM_TERM')
                    },
                    'decision_reasoning': {
                        'key_factors': [legacy_result.get('recommendation_reason', '传统分析结果')],
                        'supporting_evidence': ['基于传统算法分析'],
                        'risk_warnings': [],
                        'reasoning_confidence': legacy_result.get('confidence_level', 50)
                    }
                },
                'original_result': legacy_result
            }
            
            return intelligent_format
            
        except Exception as e:
            self.logger.error(f"传统结果转换失败: {e}")
            return {
                'item_id': legacy_result.get('item_id', ''),
                'analysis_type': 'legacy_converted',
                'analysis_timestamp': datetime.now(),
                'success': False,
                'error': str(e)
            }
    
    def _convert_unified_analysis_result(self, result: Dict, item_id: str) -> Dict[str, Any]:
        """转换统一分析结果"""
        try:
            return {
                'item_id': item_id,
                'analysis_type': 'unified',
                'success': True,
                'unified_result': result,
                'timestamp': datetime.now(),
                'data_quality': result.get('data_quality', 'UNKNOWN'),
                'analysis_components': {
                    'syncps_analysis': result.get('syncps_analysis'),
                    'ssync_analysis': result.get('ssync_analysis')
                }
            }
            
        except Exception as e:
            self.logger.error(f"统一分析结果转换失败: {e}")
            return {
                'item_id': item_id,
                'analysis_type': 'unified',
                'success': False,
                'error': str(e),
                'timestamp': datetime.now()
            }
    
    def _convert_legacy_recommendation(self, recommendation: Dict, item_id: str) -> Dict[str, Any]:
        """转换传统推荐结果"""
        try:
            return {
                'item_id': item_id,
                'recommendation_type': recommendation.get('recommendation_type', 'HOLD'),
                'total_score': recommendation.get('total_score', 50),
                'confidence_level': recommendation.get('confidence_level', 50),
                'risk_level': recommendation.get('risk_level', 'MEDIUM'),
                'algorithm_count': recommendation.get('algorithm_count', 1),
                'recommendation_reason': recommendation.get('recommendation_reason', ''),
                'success': True,
                'timestamp': datetime.now(),
                'source': 'legacy_recommendation'
            }
            
        except Exception as e:
            self.logger.error(f"传统推荐结果转换失败: {e}")
            return {
                'item_id': item_id,
                'recommendation_type': 'HOLD',
                'total_score': 50,
                'confidence_level': 30,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now()
            }
    
    def _convert_analysis_data(self, analysis_data: Dict, item_id: str) -> Dict[str, Any]:
        """转换分析数据"""
        try:
            return {
                'item_id': item_id,
                'analysis_data': analysis_data,
                'success': True,
                'timestamp': datetime.now(),
                'data_sources': analysis_data.get('data_sources', []),
                'data_quality': analysis_data.get('data_quality', 'UNKNOWN')
            }
            
        except Exception as e:
            self.logger.error(f"分析数据转换失败: {e}")
            return {
                'item_id': item_id,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now()
            }
    
    def _generate_legacy_reason(self, final_decision: Dict) -> str:
        """生成传统系统格式的推荐理由"""
        try:
            recommendation_type = final_decision.get('recommendation_type', 'HOLD')
            total_score = final_decision.get('total_score', 50)
            confidence_level = final_decision.get('confidence_level', 50)
            
            type_desc_map = {
                'STRONG_BUY': '强烈建议买入',
                'BUY': '建议买入',
                'HOLD': '建议持有',
                'SELL': '建议卖出',
                'STRONG_SELL': '强烈建议卖出'
            }
            
            type_desc = type_desc_map.get(recommendation_type, '无明确建议')
            
            return f"经过智能多维度分析，综合评分{total_score:.1f}分，置信度{confidence_level:.1f}%，{type_desc}。"
            
        except Exception:
            return "基于智能分析系统的综合评估结果。"
    
    def get_bridge_status(self) -> Dict[str, Any]:
        """获取桥接器状态"""
        return {
            'unified_analysis_system_initialized': self.unified_analysis_system is not None,
            'investment_recommendation_service_available': self.investment_recommendation_service is not None,
            'analysis_data_service_available': self.analysis_data_service is not None,
            'recommendation_type_mappings': len(self.recommendation_type_mapping),
            'risk_level_mappings': len(self.risk_level_mapping),
            'timestamp': datetime.now()
        }
