"""
数据导入服务

提供JSON数据导入到数据库的服务。
"""

import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from loguru import logger

from ..dao.item_dao import ItemDAO
from ..dao.market_snapshot_dao import MarketSnapshotDAO
from ..utils.data_cleaner import DataCleaner
from ..config.settings import get_settings


class DataImportService:
    """数据导入服务"""
    
    def __init__(self):
        self.item_dao = ItemDAO()
        self.snapshot_dao = MarketSnapshotDAO()
        self.data_cleaner = DataCleaner()
        self.settings = get_settings()
        self.logger = logger.bind(service="DataImportService")
    
    def load_json_file(self, file_path: Path) -> Optional[List[Dict[str, Any]]]:
        """加载JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                self.logger.error(f"JSON文件格式错误，期望列表格式: {file_path}")
                return None
            
            self.logger.info(f"成功加载JSON文件: {file_path}, 记录数: {len(data)}")
            return data
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析错误: {file_path}, 错误: {e}")
            return None
        except Exception as e:
            self.logger.error(f"加载JSON文件失败: {file_path}, 错误: {e}")
            return None
    
    def extract_data_source_from_filename(self, filename: str) -> str:
        """从文件名提取数据源"""
        # 移除文件扩展名
        name = Path(filename).stem
        
        # 数据源映射
        source_mapping = {
            '1天成交额榜单': '1天成交额榜单',
            '热度榜单': '热度榜单',
            '1天在售变化榜': '1天在售变化榜',
            '7天价格上涨榜': '7天价格上涨榜',
            '7天价格下跌榜': '7天价格下跌榜',
            '30天成交额榜单': '30天成交额榜单',
            '30天价格上涨榜': '30天价格上涨榜',
            '30天价格下跌榜': '30天价格下跌榜',
            '90天成交额榜单': '90天成交额榜单',
        }
        
        return source_mapping.get(name, name)

    def _parse_ranking_key(self, data_source: str) -> str:
        """解析排行榜标识（复用现有解析模式）"""
        # 从data_source解析：price_gain_上涨_1d -> price_gain_1d
        parts = data_source.split('_')
        if len(parts) >= 3:
            # 对于复合类型如price_gain，需要保持完整
            if len(parts) == 4 and parts[0] in ['price']:
                # price_gain_上涨_1d -> price_gain_1d
                ranking_type = f"{parts[0]}_{parts[1]}"
                time_period = parts[-1]
                return f"{ranking_type}_{time_period}"
            else:
                # 其他情况：volume_成交量_1d -> volume_1d
                ranking_type = parts[0]
                time_period = parts[-1]
                return f"{ranking_type}_{time_period}"

        return data_source

    def process_single_item(self, item_data: Dict[str, Any], data_source: str,
                           snapshot_time: datetime) -> Tuple[Dict[str, Any], Dict[str, Any], List[Dict[str, Any]]]:
        """处理单个饰品数据"""
        # 提取基础信息
        item_info = item_data.get('itemInfoVO', {})
        item_id = item_info.get('itemId')
        
        if not item_id:
            raise ValueError("缺少饰品ID")
        
        # 清洗饰品基础信息
        clean_item_data = self.data_cleaner.clean_item_data(item_info)
        
        # 清洗市场快照数据
        snapshot_data = {
            'item_id': item_id,
            'snapshot_time': snapshot_time,
            'data_source': data_source,
            'survive_num': self.data_cleaner.safe_int(item_data.get('surviveNum')),
            'update_time': self.data_cleaner.safe_int(item_data.get('updateTime')),
        }
        
        # 添加价格信息
        price_info = item_data.get('sellPriceInfoVO', {})
        if price_info:
            snapshot_data.update(self.data_cleaner.clean_price_data(price_info))
        
        # 添加在售数量信息
        sell_nums_info = item_data.get('sellNumsInfoVO', {})
        if sell_nums_info:
            snapshot_data.update(self.data_cleaner.clean_sell_nums_data(sell_nums_info))
        
        # 添加成交量信息
        trans_count_info = item_data.get('transactionCountInfoVO', {})
        if trans_count_info:
            snapshot_data.update(self.data_cleaner.clean_transaction_count_data(trans_count_info))
        
        # 添加成交额信息
        trans_amount_info = item_data.get('transactionAmountInfoVO', {})
        if trans_amount_info:
            snapshot_data.update(self.data_cleaner.clean_transaction_amount_data(trans_amount_info))
        
        # 添加热度信息
        hot_info = item_data.get('hotVO', {})
        if hot_info:
            snapshot_data.update(self.data_cleaner.clean_hot_data(hot_info))
        
        # 添加更新频率信息
        update_freq = item_data.get('updateFrequency', {})
        if update_freq:
            snapshot_data.update({
                'update_frequency_type': self.data_cleaner.safe_str(update_freq.get('type'), 50),
                'update_frequency_key': self.data_cleaner.safe_str(update_freq.get('key'), 50),
            })
        
        # 新增：排名信息处理
        ranking_key = self._parse_ranking_key(data_source)
        ranking_position = item_data.get('_ranking_position', 1)  # 从批量处理中获取位置

        # 初始化排名信息
        ranking_info = {ranking_key: ranking_position}

        # 使用现有的JSON序列化模式
        snapshot_data['ranking_info'] = json.dumps(ranking_info, ensure_ascii=False, default=str)

        # 处理平台价格信息
        platform_prices = []
        platform_list = item_data.get('platformInfoList', [])
        for platform_info in platform_list:
            platform_price = self.data_cleaner.clean_platform_price_data(
                platform_info, item_id, snapshot_time
            )
            if platform_price['platform_enum'] and platform_price['price'] is not None:
                platform_prices.append(platform_price)

        return clean_item_data, snapshot_data, platform_prices
    
    def import_json_data(self, json_data: List[Dict[str, Any]], data_source: str) -> Dict[str, int]:
        """导入JSON数据到数据库"""
        snapshot_time = datetime.now()

        items_data = []
        snapshots_data = []
        platform_prices_data = []

        success_count = 0
        error_count = 0

        # 新增：为整个批次预计算排名信息
        for index, item_data in enumerate(json_data):
            item_data['_ranking_position'] = index + 1  # 临时字段，用于排名计算

        # 处理每个饰品数据
        for item_data in json_data:
            try:
                item_info, snapshot_info, platform_prices = self.process_single_item(
                    item_data, data_source, snapshot_time
                )
                
                # 验证数据
                if not self.data_cleaner.validate_item_data(item_info):
                    self.logger.warning(f"饰品数据验证失败: {item_info.get('item_id')}")
                    error_count += 1
                    continue
                
                if not self.data_cleaner.validate_snapshot_data(snapshot_info):
                    self.logger.warning(f"快照数据验证失败: {snapshot_info.get('item_id')}")
                    error_count += 1
                    continue
                
                items_data.append(item_info)
                snapshots_data.append(snapshot_info)
                platform_prices_data.extend(platform_prices)
                success_count += 1
                
            except Exception as e:
                self.logger.error(f"处理饰品数据失败: {e}")
                error_count += 1
        
        # 批量导入数据库
        try:
            # 导入饰品基础信息
            self.item_dao.batch_upsert_items(items_data)
            
            # 导入市场快照
            self.snapshot_dao.batch_insert_snapshots(snapshots_data)
            
            # TODO: 导入平台价格数据（需要先获取snapshot_id）
            
            self.logger.info(f"数据导入完成: 成功 {success_count}, 失败 {error_count}")
            
            return {
                'success_count': success_count,
                'error_count': error_count,
                'items_count': len(items_data),
                'snapshots_count': len(snapshots_data),
                'platform_prices_count': len(platform_prices_data)
            }
            
        except Exception as e:
            self.logger.error(f"批量导入数据库失败: {e}")
            raise
    
    def import_file(self, file_path: Path, data_source: Optional[str] = None) -> Dict[str, int]:
        """导入单个文件"""
        # 从文件名推断数据源
        if data_source is None:
            data_source = self.extract_data_source_from_filename(file_path.name)
        
        # 加载JSON数据
        json_data = self.load_json_file(file_path)
        if json_data is None:
            return {'success_count': 0, 'error_count': 1}
        
        # 导入数据
        return self.import_json_data(json_data, data_source)
    
    def import_directory(self, directory_path: Optional[Path] = None) -> Dict[str, Any]:
        """导入目录下的所有JSON文件"""
        if directory_path is None:
            directory_path = self.settings.data_path
        
        if not directory_path.exists():
            self.logger.error(f"数据目录不存在: {directory_path}")
            return {'total_files': 0, 'success_files': 0, 'error_files': 0}
        
        # 查找所有JSON文件
        json_files = list(directory_path.glob("*.json"))
        if not json_files:
            self.logger.warning(f"目录中没有找到JSON文件: {directory_path}")
            return {'total_files': 0, 'success_files': 0, 'error_files': 0}
        
        self.logger.info(f"找到 {len(json_files)} 个JSON文件")
        
        total_stats = {
            'total_files': len(json_files),
            'success_files': 0,
            'error_files': 0,
            'total_success_count': 0,
            'total_error_count': 0,
            'file_results': []
        }
        
        # 逐个导入文件
        for json_file in json_files:
            try:
                self.logger.info(f"开始导入文件: {json_file.name}")
                result = self.import_file(json_file)
                
                total_stats['success_files'] += 1
                total_stats['total_success_count'] += result['success_count']
                total_stats['total_error_count'] += result['error_count']
                total_stats['file_results'].append({
                    'file': json_file.name,
                    'status': 'success',
                    'result': result
                })
                
                self.logger.info(f"文件导入成功: {json_file.name}")
                
            except Exception as e:
                total_stats['error_files'] += 1
                total_stats['file_results'].append({
                    'file': json_file.name,
                    'status': 'error',
                    'error': str(e)
                })
                
                self.logger.error(f"文件导入失败: {json_file.name}, 错误: {e}")
        
        self.logger.info(f"目录导入完成: 成功 {total_stats['success_files']} 个文件, 失败 {total_stats['error_files']} 个文件")
        return total_stats
