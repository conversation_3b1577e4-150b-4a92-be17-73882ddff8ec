"""
饰品数据访问对象

提供饰品相关的数据库操作。
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import func, text
from loguru import logger

from .base_dao import BaseDAO
from ..models.item import Item
from ..config.database import get_db_session


class ItemDAO(BaseDAO[Item]):
    """饰品DAO"""
    
    def __init__(self):
        super().__init__(Item)
    
    def get_by_item_id(self, item_id: str) -> Optional[Dict[str, Any]]:
        """根据饰品ID获取记录，返回字典格式避免Session问题"""
        try:
            with get_db_session() as session:
                item = session.query(Item).filter(Item.item_id == item_id).first()
                if item:
                    # 转换为字典格式
                    return {
                        'item_id': item.item_id,
                        'name': item.name,
                        'item_type': item.item_type,
                        'quality': item.quality,
                        'rarity': item.rarity,
                        'exterior': getattr(item, 'exterior', None),
                        'image_url': getattr(item, 'image_url', None),
                        'market_hash_name': getattr(item, 'market_hash_name', None),
                        'def_index_name': getattr(item, 'def_index_name', None),
                        'steamdt_item_id': getattr(item, 'steamdt_item_id', None),  # 添加steamdt_item_id字段
                        'created_at': getattr(item, 'created_at', None),
                        'updated_at': getattr(item, 'updated_at', None)
                    }
                return None
        except SQLAlchemyError as e:
            self.logger.error(f"根据饰品ID获取记录失败: {e}")
            raise
    
    def get_by_market_hash_name(self, market_hash_name: str) -> Optional[Item]:
        """根据市场哈希名称精确查询饰品"""
        try:
            with get_db_session() as session:
                item = session.query(Item).filter(Item.market_hash_name == market_hash_name).first()
                return item
        except SQLAlchemyError as e:
            self.logger.error(f"根据市场哈希名称查询饰品失败: {e}")
            raise

    def get_by_name(self, name: str) -> List[Item]:
        """根据饰品名称搜索"""
        try:
            with get_db_session() as session:
                return session.query(Item).filter(
                    Item.name.like(f"%{name}%")
                ).all()
        except SQLAlchemyError as e:
            self.logger.error(f"根据名称搜索饰品失败: {e}")
            raise

    def search_items_by_name(self, name: str, limit: int = 50) -> List[dict]:
        """根据饰品名称搜索（用于界面选择）"""
        try:
            with get_db_session() as session:
                items = session.query(Item).filter(
                    Item.name.like(f"%{name}%")
                ).limit(limit).all()

                # 在会话内提取数据，避免会话关闭后的访问问题
                result = []
                for item in items:
                    result.append({
                        'item_id': item.item_id,
                        'name': item.name
                    })
                return result
        except SQLAlchemyError as e:
            self.logger.error(f"搜索饰品失败: {e}")
            raise
    
    def get_by_type(self, item_type: str) -> List[Item]:
        """根据饰品类型获取"""
        try:
            with get_db_session() as session:
                return session.query(Item).filter(Item.item_type == item_type).all()
        except SQLAlchemyError as e:
            self.logger.error(f"根据类型获取饰品失败: {e}")
            raise
    
    def get_by_rarity(self, rarity: str) -> List[Item]:
        """根据稀有度获取"""
        try:
            with get_db_session() as session:
                return session.query(Item).filter(Item.rarity == rarity).all()
        except SQLAlchemyError as e:
            self.logger.error(f"根据稀有度获取饰品失败: {e}")
            raise

    def get_all_active_items(self, limit: int = 1000) -> List[Dict[str, Any]]:
        """获取所有活跃饰品（用于价格更新）"""
        try:
            with get_db_session() as session:
                items = session.query(Item).limit(limit).all()
                # 转换为字典格式，避免session绑定问题
                return [
                    {
                        'item_id': item.item_id,
                        'name': item.name,
                        'item_type': item.item_type,
                        'quality': item.quality,
                        'rarity': item.rarity,
                        'exterior': getattr(item, 'exterior', None),
                        'image_url': getattr(item, 'image_url', None),
                        'market_hash_name': getattr(item, 'market_hash_name', None),
                        'def_index_name': getattr(item, 'def_index_name', None)
                    }
                    for item in items
                ]
        except SQLAlchemyError as e:
            self.logger.error(f"获取所有活跃饰品失败: {e}")
            raise

    def get_items_with_arbitrage_ratio(self, limit: int = 1000, randomize: bool = True) -> List[Dict[str, Any]]:
        """
        获取搬砖率不为0或不为null的饰品

        Args:
            limit: 返回数量限制
            randomize: 是否随机排序，避免总是返回相同的饰品

        Returns:
            搬砖率不为0或null的饰品列表
        """
        try:
            with get_db_session() as session:
                query = session.query(Item).filter(
                    Item.arbitrage_ratio.isnot(None),
                    Item.arbitrage_ratio != 0,
                    Item.market_hash_name.isnot(None)  # 确保有market_hash_name
                )

                # 添加随机排序，避免总是返回相同的饰品
                if randomize:
                    from sqlalchemy import func
                    query = query.order_by(func.rand())
                else:
                    # 如果不随机，按搬砖率降序排序
                    query = query.order_by(Item.arbitrage_ratio.desc())

                items = query.limit(limit).all()

                # 转换为字典格式，避免session绑定问题
                return [
                    {
                        'item_id': item.item_id,
                        'name': item.name,
                        'item_type': item.item_type,
                        'quality': item.quality,
                        'rarity': item.rarity,
                        'exterior': getattr(item, 'exterior', None),
                        'image_url': getattr(item, 'image_url', None),
                        'market_hash_name': getattr(item, 'market_hash_name', None),
                        'def_index_name': getattr(item, 'def_index_name', None),
                        'arbitrage_ratio': float(item.arbitrage_ratio) if item.arbitrage_ratio else None
                    }
                    for item in items
                ]
        except SQLAlchemyError as e:
            self.logger.error(f"获取搬砖率饰品失败: {e}")
            raise
    
    def search_items(self, keyword: str, item_type: Optional[str] = None,
                    rarity: Optional[str] = None, limit: int = 1000) -> List[Item]:
        """综合搜索饰品"""
        try:
            with get_db_session() as session:
                query = session.query(Item)

                # 关键词搜索
                if keyword:
                    query = query.filter(
                        Item.name.like(f"%{keyword}%") |
                        Item.market_hash_name.like(f"%{keyword}%")
                    )

                # 类型过滤
                if item_type:
                    query = query.filter(Item.item_type == item_type)

                # 稀有度过滤
                if rarity:
                    query = query.filter(Item.rarity == rarity)

                # 获取结果并强制加载属性
                items = query.limit(limit).all()

                # 强制加载所有属性
                for item in items:
                    _ = item.item_id
                    _ = item.name
                    _ = item.item_type
                    _ = item.quality
                    _ = item.rarity
                    _ = getattr(item, 'exterior', None)
                    _ = getattr(item, 'image_url', None)
                    _ = getattr(item, 'market_hash_name', None)
                    _ = getattr(item, 'created_at', None)
                    _ = getattr(item, 'updated_at', None)

                return items
        except SQLAlchemyError as e:
            self.logger.error(f"综合搜索饰品失败: {e}")
            raise

    def advanced_search(self, name_query: Optional[str] = None,
                       item_types: Optional[List[str]] = None,
                       qualities: Optional[List[str]] = None,
                       rarities: Optional[List[str]] = None,
                       sort_by: str = "updated_desc",
                       limit: int = 1000,
                       offset: int = 0) -> List[Item]:
        """高级搜索饰品"""
        try:
            with get_db_session() as session:
                query = session.query(Item)

                # 名称搜索
                if name_query:
                    query = query.filter(
                        Item.name.like(f"%{name_query}%") |
                        Item.market_hash_name.like(f"%{name_query}%")
                    )

                # 类型过滤
                if item_types:
                    query = query.filter(Item.item_type.in_(item_types))

                # 品质过滤
                if qualities:
                    query = query.filter(Item.quality.in_(qualities))

                # 稀有度过滤
                if rarities:
                    query = query.filter(Item.rarity.in_(rarities))

                # 排序
                if sort_by == "name_asc":
                    query = query.order_by(Item.name.asc())
                elif sort_by == "name_desc":
                    query = query.order_by(Item.name.desc())
                elif sort_by == "updated_desc":
                    query = query.order_by(Item.updated_at.desc())
                elif sort_by == "updated_asc":
                    query = query.order_by(Item.updated_at.asc())
                else:
                    query = query.order_by(Item.updated_at.desc())

                # 获取结果并立即访问所有属性以避免Session问题
                items = query.offset(offset).limit(limit).all()

                # 强制加载所有属性
                for item in items:
                    # 访问所有属性以确保它们被加载
                    _ = item.item_id
                    _ = item.name
                    _ = item.item_type
                    _ = item.quality
                    _ = item.rarity
                    _ = getattr(item, 'exterior', None)
                    _ = getattr(item, 'image_url', None)
                    _ = getattr(item, 'market_hash_name', None)
                    _ = getattr(item, 'created_at', None)
                    _ = getattr(item, 'updated_at', None)

                return items
        except SQLAlchemyError as e:
            self.logger.error(f"高级搜索饰品失败: {e}")
            raise

    def get_distinct_values(self, field: str) -> List[str]:
        """获取指定字段的不重复值"""
        try:
            with get_db_session() as session:
                if not hasattr(Item, field):
                    raise ValueError(f"Item模型没有字段: {field}")

                column = getattr(Item, field)
                results = session.query(column).distinct().filter(column.isnot(None)).all()
                return [result[0] for result in results if result[0]]
        except SQLAlchemyError as e:
            self.logger.error(f"获取字段不重复值失败: {e}")
            raise
    
    def upsert_item(self, item_data: dict) -> Item:
        """插入或更新饰品信息"""
        try:
            with get_db_session() as session:
                item_id = item_data.get('item_id')
                existing_item = session.query(Item).filter(Item.item_id == item_id).first()
                
                if existing_item:
                    # 更新现有记录
                    for key, value in item_data.items():
                        if hasattr(existing_item, key) and key != 'item_id':
                            setattr(existing_item, key, value)
                    session.flush()
                    session.refresh(existing_item)
                    self.logger.info(f"更新饰品信息: {item_id}")
                    return existing_item
                else:
                    # 创建新记录
                    new_item = Item(**item_data)
                    session.add(new_item)
                    session.flush()
                    session.refresh(new_item)
                    self.logger.info(f"创建饰品信息: {item_id}")
                    return new_item
        except SQLAlchemyError as e:
            self.logger.error(f"插入或更新饰品信息失败: {e}")
            raise
    
    def batch_upsert_items(self, items_data: List[dict]) -> List[Item]:
        """批量插入或更新饰品信息"""
        try:
            with get_db_session() as session:
                results = []
                
                for item_data in items_data:
                    item_id = item_data.get('item_id')
                    existing_item = session.query(Item).filter(Item.item_id == item_id).first()
                    
                    if existing_item:
                        # 更新现有记录
                        for key, value in item_data.items():
                            if hasattr(existing_item, key) and key != 'item_id':
                                setattr(existing_item, key, value)
                        results.append(existing_item)
                    else:
                        # 创建新记录
                        new_item = Item(**item_data)
                        session.add(new_item)
                        results.append(new_item)
                
                session.flush()
                for item in results:
                    session.refresh(item)
                
                self.logger.info(f"批量处理饰品信息: {len(results)}条")
                return results
        except SQLAlchemyError as e:
            self.logger.error(f"批量插入或更新饰品信息失败: {e}")
            raise
    
    def get_item_statistics(self) -> dict:
        """获取饰品统计信息"""
        try:
            with get_db_session() as session:
                total_count = session.query(Item).count()
                
                # 按类型统计
                type_stats = session.query(
                    Item.item_type,
                    func.count(Item.item_id).label('count')
                ).group_by(Item.item_type).all()

                # 按稀有度统计
                rarity_stats = session.query(
                    Item.rarity,
                    func.count(Item.item_id).label('count')
                ).group_by(Item.rarity).all()
                
                return {
                    'total_count': total_count,
                    'type_distribution': {stat[0]: stat[1] for stat in type_stats if stat[0]},
                    'rarity_distribution': {stat[0]: stat[1] for stat in rarity_stats if stat[0]}
                }
        except SQLAlchemyError as e:
            self.logger.error(f"获取饰品统计信息失败: {e}")
            raise

    def get_items_without_steamdt_id(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取没有SteamDT ID的饰品记录

        Args:
            limit: 限制返回的记录数量

        Returns:
            List[Dict[str, Any]]: 饰品记录列表
        """
        try:
            with get_db_session() as session:
                # 使用原生SQL查询避免ORM模型问题
                sql = """
                    SELECT item_id, name, market_hash_name, item_type, quality, rarity, exterior, created_at
                    FROM items
                    WHERE steamdt_item_id IS NULL
                    AND market_hash_name IS NOT NULL
                    AND market_hash_name != ''
                    ORDER BY created_at ASC
                    LIMIT :limit
                """

                result = session.execute(text(sql), {'limit': limit})
                items = result.fetchall()

                # 转换为字典格式
                item_list = []
                for item in items:
                    item_list.append({
                        'item_id': item[0],
                        'name': item[1],
                        'market_hash_name': item[2],
                        'item_type': item[3],
                        'quality': item[4],
                        'rarity': item[5],
                        'exterior': item[6],
                        'created_at': item[7]
                    })

                self.logger.info(f"查询到 {len(item_list)} 个需要更新SteamDT ID的饰品")
                return item_list

        except SQLAlchemyError as e:
            self.logger.error(f"查询没有SteamDT ID的饰品失败: {e}")
            raise

    def update_steamdt_id(self, item_id: str, steamdt_id: str) -> bool:
        """
        更新饰品的SteamDT ID

        Args:
            item_id: 饰品ID
            steamdt_id: SteamDT饰品ID

        Returns:
            bool: 是否更新成功
        """
        try:
            with get_db_session() as session:
                # 使用原生SQL更新避免ORM模型问题
                sql = """
                    UPDATE items
                    SET steamdt_item_id = :steamdt_id, updated_at = NOW()
                    WHERE item_id = :item_id
                """

                result = session.execute(text(sql), {
                    'steamdt_id': steamdt_id,
                    'item_id': item_id
                })

                if result.rowcount > 0:
                    self.logger.info(f"成功更新饰品 {item_id} 的SteamDT ID: {steamdt_id}")
                    return True
                else:
                    self.logger.warning(f"未找到饰品 {item_id}，更新失败")
                    return False

        except SQLAlchemyError as e:
            self.logger.error(f"更新饰品SteamDT ID失败: {e}")
            raise

    def update_item_price_update_time(self, item_id: str) -> bool:
        """
        更新饰品的价格更新时间

        Args:
            item_id: 饰品ID

        Returns:
            bool: 是否更新成功
        """
        try:
            with get_db_session() as session:
                # 使用原生SQL更新避免ORM模型问题
                sql = """
                    UPDATE items
                    SET last_price_update = NOW(), updated_at = NOW()
                    WHERE item_id = :item_id
                """

                result = session.execute(text(sql), {
                    'item_id': item_id
                })

                if result.rowcount > 0:
                    self.logger.debug(f"成功更新饰品 {item_id} 的价格更新时间")
                    return True
                else:
                    self.logger.warning(f"未找到饰品 {item_id}，更新价格时间失败")
                    return False

        except SQLAlchemyError as e:
            self.logger.error(f"更新饰品价格更新时间失败: {e}")
            raise

    def get_items_for_price_update(self, limit: int = 100, skip_zero_price_items: bool = False,
                                 zero_price_update_interval_hours: int = 24) -> List[Dict[str, Any]]:
        """
        获取需要价格更新的饰品列表

        Args:
            limit: 返回数量限制
            skip_zero_price_items: 是否跳过全零价格饰品
            zero_price_update_interval_hours: 全零价格饰品重新查询间隔（小时）

        Returns:
            需要更新的饰品列表
        """
        try:
            with get_db_session() as session:
                # 基础查询
                sql = """
                    SELECT item_id, name, market_hash_name, item_type, quality, rarity,
                           exterior, last_price_update, created_at
                    FROM items
                    WHERE market_hash_name IS NOT NULL
                    AND market_hash_name != ''
                """

                # 如果跳过全零价格饰品
                if skip_zero_price_items:
                    sql += f"""
                    AND (
                        last_price_update IS NULL
                        OR last_price_update < DATE_SUB(NOW(), INTERVAL {zero_price_update_interval_hours} HOUR)
                        OR EXISTS (
                            SELECT 1 FROM platform_prices pp
                            WHERE pp.item_id = items.item_id
                            AND pp.is_active = 1
                            AND (pp.sell_price > 0 OR pp.bidding_price > 0)
                        )
                    )
                    """

                # 按最后更新时间排序，NULL值优先
                sql += """
                    ORDER BY
                        CASE WHEN last_price_update IS NULL THEN 0 ELSE 1 END,
                        last_price_update ASC
                    LIMIT :limit
                """

                result = session.execute(text(sql), {'limit': limit})
                items = result.fetchall()

                # 转换为字典格式
                item_list = []
                for item in items:
                    item_list.append({
                        'item_id': item[0],
                        'name': item[1],
                        'market_hash_name': item[2],
                        'item_type': item[3],
                        'quality': item[4],
                        'rarity': item[5],
                        'exterior': item[6],
                        'last_price_update': item[7],
                        'created_at': item[8]
                    })

                self.logger.debug(f"查询到 {len(item_list)} 个需要价格更新的饰品")
                return item_list

        except SQLAlchemyError as e:
            self.logger.error(f"获取需要价格更新的饰品失败: {e}")
            raise

    def get_steamdt_id_statistics(self) -> Dict[str, Any]:
        """
        获取SteamDT ID更新统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            with get_db_session() as session:
                # 使用原生SQL查询避免ORM模型问题

                # 总饰品数
                result = session.execute(text("SELECT COUNT(*) FROM items"))
                total_count = result.scalar()

                # 已有SteamDT ID的饰品数
                result = session.execute(text("""
                    SELECT COUNT(*) FROM items
                    WHERE steamdt_item_id IS NOT NULL AND steamdt_item_id != ''
                """))
                with_steamdt_id = result.scalar()

                # 没有SteamDT ID但有market_hash_name的饰品数
                result = session.execute(text("""
                    SELECT COUNT(*) FROM items
                    WHERE steamdt_item_id IS NULL
                    AND market_hash_name IS NOT NULL
                    AND market_hash_name != ''
                """))
                without_steamdt_id = result.scalar()

                # 没有market_hash_name的饰品数（无法更新）
                result = session.execute(text("""
                    SELECT COUNT(*) FROM items
                    WHERE market_hash_name IS NULL OR market_hash_name = ''
                """))
                no_market_hash_name = result.scalar()

                completion_rate = (with_steamdt_id / total_count * 100) if total_count > 0 else 0

                return {
                    'total_items': total_count,
                    'with_steamdt_id': with_steamdt_id,
                    'without_steamdt_id': without_steamdt_id,
                    'no_market_hash_name': no_market_hash_name,
                    'completion_rate': round(completion_rate, 2)
                }

        except SQLAlchemyError as e:
            self.logger.error(f"获取SteamDT ID统计信息失败: {e}")
            raise


# 创建全局实例
item_dao = ItemDAO()
