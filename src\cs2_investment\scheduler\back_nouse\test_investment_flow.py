"""
完整业务流程测试脚本

测试自动化投资分析的端到端流程：
1. 排行榜数据获取
2. 数据更新检查
3. 常规分析任务提交
4. 投资筛选算法运行
5. 结果保存验证
6. 性能和错误处理测试
"""

import sys
import asyncio
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.scheduler.back_nouse.investment_analysis_scheduler import (
    InvestmentAnalysisScheduler, 
    InvestmentAnalysisConfig
)
from src.cs2_investment.dao.market_snapshot_dao import MarketSnapshotDAO
from src.cs2_investment.algorithms.algorithm_manager import AlgorithmManager
from src.cs2_investment.api.services.task_engine import task_engine, TaskInfo, TaskType

# 使用统一日志系统
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)


class InvestmentFlowTester:
    """投资分析流程测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.test_results = {}
        self.performance_metrics = {}
        self.error_log = []
        self.start_time = None
        
        # 测试配置
        self.test_config = InvestmentAnalysisConfig(
            analysis_interval_hours=1,  # 测试用较短间隔
            batch_size=10,              # 测试用较小批次
            max_ranking_items=50,       # 测试用较少数量
            algorithm_limit_per_type=5, # 测试用较少结果
            enable_detailed_logging=True,
            enable_performance_monitoring=True
        )
        
        # 初始化组件
        self.scheduler = None
        self.market_dao = MarketSnapshotDAO()
        self.algorithm_manager = AlgorithmManager()
        
        logger.info("🧪 投资分析流程测试器初始化完成")
    
    async def run_full_test_suite(self) -> Dict[str, Any]:
        """运行完整测试套件"""
        self.start_time = datetime.now()
        logger.info("🚀 开始完整业务流程测试")
        
        test_functions = [
            ("环境准备", self._test_environment_setup),
            ("排行榜数据获取", self._test_ranking_data_retrieval),
            ("数据更新检查", self._test_data_update_check),
            ("调度器初始化", self._test_scheduler_initialization),
            ("常规分析流程", self._test_regular_analysis_flow),
            ("投资筛选算法", self._test_investment_screening),
            ("结果保存验证", self._test_result_persistence),
            ("性能测试", self._test_performance),
            ("错误处理", self._test_error_handling),
            ("完整流程集成", self._test_end_to_end_integration)
        ]
        
        for test_name, test_func in test_functions:
            try:
                logger.info(f"🔍 执行测试: {test_name}")
                start_time = time.time()
                
                result = await test_func()
                
                duration = time.time() - start_time
                self.test_results[test_name] = {
                    'success': result,
                    'duration': duration,
                    'timestamp': datetime.now()
                }
                
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"{status} {test_name} (耗时: {duration:.2f}秒)")
                
            except Exception as e:
                error_msg = f"{test_name}测试异常: {e}"
                logger.error(error_msg)
                self.error_log.append(error_msg)
                self.test_results[test_name] = {
                    'success': False,
                    'duration': 0,
                    'error': str(e),
                    'timestamp': datetime.now()
                }
        
        # 生成测试报告
        return self._generate_test_report()
    
    async def _test_environment_setup(self) -> bool:
        """测试环境准备"""
        try:
            # 检查数据库连接
            from src.cs2_investment.config.database import get_db_session
            with get_db_session() as session:
                logger.info("✅ 数据库连接正常")
            
            # 检查TaskEngine
            if task_engine:
                logger.info("✅ TaskEngine可用")
            else:
                logger.warning("⚠️ TaskEngine不可用")
            
            # 检查算法管理器
            algorithms = self.algorithm_manager.get_available_algorithms()
            logger.info(f"✅ 算法管理器可用: {len(algorithms)} 种算法")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 环境准备失败: {e}")
            return False
    
    async def _test_ranking_data_retrieval(self) -> bool:
        """测试排行榜数据获取"""
        try:
            # 测试获取热度排行榜
            ranking_items = self.market_dao.get_hot_ranking_items(limit=self.test_config.max_ranking_items)
            
            if not ranking_items:
                logger.warning("⚠️ 没有获取到排行榜数据")
                return False
            
            logger.info(f"✅ 成功获取 {len(ranking_items)} 个排行榜饰品")
            
            # 验证数据结构
            first_item = ranking_items[0]
            required_fields = ['item_id', 'hot_rank', 'current_price']
            
            for field in required_fields:
                if not hasattr(first_item, field):
                    logger.error(f"❌ 排行榜数据缺少字段: {field}")
                    return False
            
            logger.info("✅ 排行榜数据结构验证通过")
            
            # 记录性能指标
            self.performance_metrics['ranking_items_count'] = len(ranking_items)
            self.performance_metrics['first_item_rank'] = getattr(first_item, 'hot_rank', 0)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 排行榜数据获取失败: {e}")
            return False
    
    async def _test_data_update_check(self) -> bool:
        """测试数据更新检查"""
        try:
            # 创建临时调度器用于测试
            temp_scheduler = InvestmentAnalysisScheduler(self.test_config)
            
            # 测试首次运行检查
            has_update = await temp_scheduler._check_ranking_data_update()
            logger.info(f"✅ 数据更新检查完成: {'有更新' if has_update else '无更新'}")
            
            # 测试时间容差机制
            temp_scheduler.last_processed_time = datetime.now() - timedelta(seconds=30)
            no_update = await temp_scheduler._check_ranking_data_update()
            
            if not no_update:
                logger.info("✅ 时间容差机制正常工作")
            else:
                logger.warning("⚠️ 时间容差机制可能有问题")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据更新检查失败: {e}")
            return False
    
    async def _test_scheduler_initialization(self) -> bool:
        """测试调度器初始化"""
        try:
            # 创建调度器
            self.scheduler = InvestmentAnalysisScheduler(self.test_config)
            
            # 验证配置应用
            if self.scheduler.batch_size != self.test_config.batch_size:
                logger.error("❌ 配置应用失败")
                return False
            
            # 验证组件初始化
            if not self.scheduler.algorithm_manager:
                logger.error("❌ 算法管理器未初始化")
                return False
            
            if not self.scheduler.market_dao:
                logger.error("❌ 市场数据DAO未初始化")
                return False
            
            logger.info("✅ 调度器初始化成功")
            
            # 获取初始状态
            status = self.scheduler.get_status()
            logger.info(f"✅ 调度器状态获取成功: 运行状态 {status.get('is_running', False)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 调度器初始化失败: {e}")
            return False
    
    async def _test_regular_analysis_flow(self) -> bool:
        """测试常规分析流程"""
        try:
            if not self.scheduler:
                logger.error("❌ 调度器未初始化")
                return False
            
            # 设置调度器为运行状态
            self.scheduler.is_running = True
            
            # 获取测试数据
            ranking_items = self.market_dao.get_hot_ranking_items(limit=10)
            if not ranking_items:
                logger.warning("⚠️ 没有测试数据")
                return False
            
            # 测试批量分析
            success = await self.scheduler._run_regular_analysis_batch(ranking_items)
            
            if success:
                logger.info("✅ 常规分析流程测试成功")
                
                # 检查统计信息
                stats = self.scheduler.get_stats()
                analyzed_count = stats.get('total_items_analyzed', 0)
                logger.info(f"✅ 分析饰品数量: {analyzed_count}")
                
                self.performance_metrics['analyzed_items'] = analyzed_count
                return True
            else:
                logger.error("❌ 常规分析流程测试失败")
                return False
            
        except Exception as e:
            logger.error(f"❌ 常规分析流程测试异常: {e}")
            return False

    async def _test_investment_screening(self) -> bool:
        """测试投资筛选算法"""
        try:
            if not self.scheduler:
                logger.error("❌ 调度器未初始化")
                return False

            # 测试投资筛选
            success = await self.scheduler._run_investment_screening()

            if success:
                logger.info("✅ 投资筛选算法测试成功")

                # 检查筛选统计
                stats = self.scheduler.get_stats()
                screening_stats = stats.get('last_screening_stats', {})
                total_results = stats.get('last_screening_total_results', 0)

                logger.info(f"✅ 筛选结果: {total_results} 个推荐")
                for algo_type, count in screening_stats.items():
                    logger.info(f"   {algo_type}: {count} 个")

                self.performance_metrics['screening_results'] = total_results
                self.performance_metrics['screening_algorithms'] = len(screening_stats)

                return total_results > 0
            else:
                logger.warning("⚠️ 投资筛选算法未产生结果")
                return False

        except Exception as e:
            logger.error(f"❌ 投资筛选算法测试异常: {e}")
            return False

    async def _test_result_persistence(self) -> bool:
        """测试结果保存验证"""
        try:
            # 测试算法结果生成
            test_results = self.algorithm_manager.run_all_algorithms(limit_per_type=3)

            if not test_results:
                logger.warning("⚠️ 没有算法结果可供测试")
                return True  # 不算失败，因为可能是数据问题

            # 合并结果
            all_results = []
            for investment_type, results in test_results.items():
                all_results.extend(results)

            if all_results:
                # 测试保存功能（注意：这可能会因为数据库约束失败）
                try:
                    save_success = self.algorithm_manager.save_screening_results(all_results)
                    if save_success:
                        logger.info(f"✅ 结果保存成功: {len(all_results)} 条")
                        return True
                    else:
                        logger.warning("⚠️ 结果保存失败（可能是重复数据）")
                        return True  # 不算失败，因为可能是重复数据
                except Exception as save_e:
                    logger.warning(f"⚠️ 结果保存异常: {save_e}")
                    return True  # 不算失败，因为可能是数据库约束问题
            else:
                logger.warning("⚠️ 没有结果需要保存")
                return True

        except Exception as e:
            logger.error(f"❌ 结果保存验证异常: {e}")
            return False

    async def _test_performance(self) -> bool:
        """测试性能"""
        try:
            if not self.scheduler:
                logger.error("❌ 调度器未初始化")
                return False

            # 性能测试：获取排行榜数据
            start_time = time.time()
            ranking_items = self.market_dao.get_hot_ranking_items(limit=100)
            ranking_duration = time.time() - start_time

            # 性能测试：算法执行
            start_time = time.time()
            algorithm_results = self.algorithm_manager.run_all_algorithms(limit_per_type=5)
            algorithm_duration = time.time() - start_time

            # 记录性能指标
            self.performance_metrics.update({
                'ranking_fetch_duration': ranking_duration,
                'algorithm_execution_duration': algorithm_duration,
                'items_per_second': len(ranking_items) / ranking_duration if ranking_duration > 0 else 0
            })

            logger.info(f"✅ 性能测试完成:")
            logger.info(f"   排行榜获取: {ranking_duration:.3f}秒 ({len(ranking_items)} 个饰品)")
            logger.info(f"   算法执行: {algorithm_duration:.3f}秒 ({len(algorithm_results)} 种算法)")
            logger.info(f"   处理速度: {self.performance_metrics['items_per_second']:.1f} 饰品/秒")

            # 性能阈值检查
            if ranking_duration > 10:  # 超过10秒认为性能有问题
                logger.warning("⚠️ 排行榜获取性能较慢")

            if algorithm_duration > 30:  # 超过30秒认为性能有问题
                logger.warning("⚠️ 算法执行性能较慢")

            return True

        except Exception as e:
            logger.error(f"❌ 性能测试异常: {e}")
            return False

    async def _test_error_handling(self) -> bool:
        """测试错误处理"""
        try:
            if not self.scheduler:
                logger.error("❌ 调度器未初始化")
                return False

            # 测试错误记录功能
            test_error = "测试错误处理"
            self.scheduler._record_error(test_error)

            stats = self.scheduler.get_stats()
            if stats.get('last_error') == test_error:
                logger.info("✅ 错误记录功能正常")
            else:
                logger.error("❌ 错误记录功能异常")
                return False

            # 测试健康状态检查
            health = self.scheduler.get_health_status()
            logger.info(f"✅ 健康状态检查: {'健康' if health.get('is_healthy') else '不健康'}")

            # 测试配置验证
            try:
                invalid_config = InvestmentAnalysisConfig(analysis_interval_hours=-1)
                logger.warning("⚠️ 配置验证可能需要加强")
            except:
                logger.info("✅ 配置验证正常")

            return True

        except Exception as e:
            logger.error(f"❌ 错误处理测试异常: {e}")
            return False

    async def _test_end_to_end_integration(self) -> bool:
        """测试端到端集成"""
        try:
            if not self.scheduler:
                logger.error("❌ 调度器未初始化")
                return False

            logger.info("🔄 开始端到端集成测试...")

            # 设置调度器为运行状态
            self.scheduler.is_running = True

            # 模拟完整的分析周期
            integration_start = time.time()

            # 1. 检查数据更新
            has_update = await self.scheduler._check_ranking_data_update()
            logger.info(f"   数据更新检查: {'有更新' if has_update else '无更新'}")

            if has_update:
                # 2. 运行完整分析周期
                success = await self.scheduler._run_investment_analysis_cycle()

                if success:
                    integration_duration = time.time() - integration_start
                    logger.info(f"✅ 端到端集成测试成功 (耗时: {integration_duration:.2f}秒)")

                    # 记录集成测试指标
                    self.performance_metrics['integration_duration'] = integration_duration

                    # 检查最终状态
                    final_stats = self.scheduler.get_stats()
                    logger.info(f"   最终统计: 分析 {final_stats.get('total_items_analyzed', 0)} 个饰品")
                    logger.info(f"   算法运行: {final_stats.get('total_algorithms_run', 0)} 次")

                    return True
                else:
                    logger.error("❌ 完整分析周期失败")
                    return False
            else:
                logger.info("✅ 端到端集成测试完成（无数据更新）")
                return True

        except Exception as e:
            logger.error(f"❌ 端到端集成测试异常: {e}")
            return False

    def _generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        total_duration = (datetime.now() - self.start_time).total_seconds()

        # 统计测试结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        failed_tests = total_tests - passed_tests

        # 计算成功率
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        # 生成报告
        report = {
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'success_rate': success_rate,
                'total_duration': total_duration,
                'test_start_time': self.start_time,
                'test_end_time': datetime.now()
            },
            'test_results': self.test_results,
            'performance_metrics': self.performance_metrics,
            'error_log': self.error_log,
            'recommendations': self._generate_recommendations()
        }

        return report

    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []

        # 基于测试结果生成建议
        failed_tests = [name for name, result in self.test_results.items() if not result['success']]

        if failed_tests:
            recommendations.append(f"需要修复失败的测试: {', '.join(failed_tests)}")

        # 基于性能指标生成建议
        if 'ranking_fetch_duration' in self.performance_metrics:
            if self.performance_metrics['ranking_fetch_duration'] > 5:
                recommendations.append("排行榜数据获取性能需要优化")

        if 'algorithm_execution_duration' in self.performance_metrics:
            if self.performance_metrics['algorithm_execution_duration'] > 20:
                recommendations.append("算法执行性能需要优化")

        if 'items_per_second' in self.performance_metrics:
            if self.performance_metrics['items_per_second'] < 10:
                recommendations.append("数据处理速度需要提升")

        # 基于错误日志生成建议
        if self.error_log:
            recommendations.append(f"需要处理 {len(self.error_log)} 个错误")

        if not recommendations:
            recommendations.append("所有测试通过，系统运行良好")

        return recommendations

    def print_test_report(self, report: Dict[str, Any]):
        """打印测试报告"""
        print("\n" + "=" * 80)
        print("📊 完整业务流程测试报告")
        print("=" * 80)

        # 摘要信息
        summary = report['summary']
        print(f"\n📋 测试摘要:")
        print(f"   测试开始时间: {summary['test_start_time']}")
        print(f"   测试结束时间: {summary['test_end_time']}")
        print(f"   总测试时间: {summary['total_duration']:.2f} 秒")
        print(f"   总测试数量: {summary['total_tests']}")
        print(f"   通过测试: {summary['passed_tests']}")
        print(f"   失败测试: {summary['failed_tests']}")
        print(f"   成功率: {summary['success_rate']:.1f}%")

        # 详细测试结果
        print(f"\n📝 详细测试结果:")
        for test_name, result in report['test_results'].items():
            status = "✅ 通过" if result['success'] else "❌ 失败"
            duration = result['duration']
            print(f"   {test_name}: {status} ({duration:.2f}秒)")
            if not result['success'] and 'error' in result:
                print(f"      错误: {result['error']}")

        # 性能指标
        if report['performance_metrics']:
            print(f"\n⚡ 性能指标:")
            for metric, value in report['performance_metrics'].items():
                if isinstance(value, float):
                    print(f"   {metric}: {value:.3f}")
                else:
                    print(f"   {metric}: {value}")

        # 错误日志
        if report['error_log']:
            print(f"\n❌ 错误日志:")
            for error in report['error_log']:
                print(f"   - {error}")

        # 改进建议
        print(f"\n💡 改进建议:")
        for recommendation in report['recommendations']:
            print(f"   - {recommendation}")

        print("\n" + "=" * 80)


async def main():
    """主函数"""
    print("🚀 开始完整业务流程测试")
    print("=" * 80)

    # 创建测试器
    tester = InvestmentFlowTester()

    try:
        # 运行完整测试套件
        report = await tester.run_full_test_suite()

        # 打印测试报告
        tester.print_test_report(report)

        # 判断整体测试结果
        success_rate = report['summary']['success_rate']

        if success_rate >= 90:
            print("🎉 测试结果优秀！系统运行状态良好。")
            return True
        elif success_rate >= 70:
            print("✅ 测试结果良好，但有改进空间。")
            return True
        elif success_rate >= 50:
            print("⚠️ 测试结果一般，需要关注失败的测试。")
            return False
        else:
            print("❌ 测试结果较差，需要重点修复问题。")
            return False

    except Exception as e:
        logger.error(f"❌ 测试执行异常: {e}")
        print(f"❌ 测试执行异常: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
