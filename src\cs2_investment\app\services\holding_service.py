"""
持仓服务层

提供持仓管理的业务逻辑。
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from decimal import Decimal
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.holding_dao import HoldingDAO
from src.cs2_investment.dao.holding_transaction_dao import HoldingTransactionDAO
from src.cs2_investment.dao.market_snapshot_dao import MarketSnapshotDAO


class HoldingService:
    """持仓服务类"""
    
    def __init__(self):
        self.holding_dao = HoldingDAO()
        self.transaction_dao = HoldingTransactionDAO()
        self.market_snapshot_dao = MarketSnapshotDAO()
        self.default_user_id = "default_user"  # 简化实现，使用默认用户
    
    def add_buy_transaction(self, user_id: str, item_id: str, item_name: str,
                           quantity: int, price: float, transaction_date: datetime = None,
                           fee: float = 0, notes: str = None) -> Dict[str, Any]:
        """添加买入交易"""
        try:
            if transaction_date is None:
                transaction_date = datetime.now()

            price_decimal = Decimal(str(price))
            fee_decimal = Decimal(str(fee)) if fee else Decimal('0')

            # 添加交易记录
            transaction = self.transaction_dao.add_transaction(
                user_id=user_id,
                item_id=item_id,
                item_name=item_name,
                transaction_type='BUY',
                quantity=quantity,
                price=price_decimal,
                transaction_date=transaction_date,
                fee=fee_decimal,
                notes=notes
            )

            # 更新持仓汇总
            holding = self.holding_dao.update_holding_after_transaction(
                user_id=user_id,
                item_id=item_id,
                item_name=item_name,
                transaction_type='BUY',
                quantity=quantity,
                price=price_decimal
            )

            return {
                'success': True,
                'transaction_id': transaction.get('id') if transaction else None,
                'holding_id': holding.get('id') if holding else None,
                'message': f'成功买入 {quantity} 个 {item_name}'
            }
            
        except Exception as e:
            error_msg = str(e)
            print(f"添加买入交易失败: {error_msg}")
            return {
                'success': False,
                'error': error_msg,
                'message': f'买入交易失败: {error_msg}'
            }
    
    def add_sell_transaction(self, user_id: str, item_id: str, item_name: str,
                            quantity: int, price: float, transaction_date: datetime = None,
                            fee: float = 0, notes: str = None) -> Dict[str, Any]:
        """添加卖出交易"""
        try:
            if transaction_date is None:
                transaction_date = datetime.now()
            
            price_decimal = Decimal(str(price))
            fee_decimal = Decimal(str(fee)) if fee else Decimal('0')
            
            # 检查持仓是否足够
            holding = self.holding_dao.get_holding_by_user_item(user_id, item_id)
            if not holding or holding.get('available_quantity', 0) < quantity:
                available = holding.get('available_quantity', 0) if holding else 0
                return {
                    'success': False,
                    'error': 'insufficient_quantity',
                    'message': f'可用数量不足，当前可用: {available}, 尝试卖出: {quantity}'
                }
            
            # 添加交易记录
            transaction = self.transaction_dao.add_transaction(
                user_id=user_id,
                item_id=item_id,
                item_name=item_name,
                transaction_type='SELL',
                quantity=quantity,
                price=price_decimal,
                transaction_date=transaction_date,
                fee=fee_decimal,
                notes=notes
            )
            
            # 更新持仓汇总
            holding = self.holding_dao.update_holding_after_transaction(
                user_id=user_id,
                item_id=item_id,
                item_name=item_name,
                transaction_type='SELL',
                quantity=quantity,
                price=price_decimal
            )
            
            return {
                'success': True,
                'transaction_id': transaction.get('id') if transaction else None,
                'holding_id': holding.get('id') if holding else None,
                'message': f'成功卖出 {quantity} 个 {item_name}'
            }
            
        except Exception as e:
            error_msg = str(e)
            print(f"添加卖出交易失败: {error_msg}")
            return {
                'success': False,
                'error': error_msg,
                'message': f'卖出交易失败: {error_msg}'
            }
    
    def calculate_pnl(self, user_id: str, item_id: str = None) -> Dict[str, Any]:
        """计算盈亏"""
        try:
            if item_id:
                # 计算单个饰品的盈亏
                return self._calculate_single_item_pnl(user_id, item_id)
            else:
                # 计算所有持仓的盈亏
                return self._calculate_portfolio_pnl(user_id)
                
        except Exception as e:
            print(f"计算盈亏失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_cost': 0.0,
                'current_value': 0.0,
                'pnl_amount': 0.0,
                'pnl_percentage': 0.0
            }
    
    def _calculate_single_item_pnl(self, user_id: str, item_id: str) -> Dict[str, Any]:
        """计算单个饰品的盈亏"""
        holding = self.holding_dao.get_holding_by_user_item(user_id, item_id)
        if not holding or holding.get('total_quantity', 0) <= 0:
            return {
                'success': False,
                'error': 'no_holding',
                'total_cost': 0.0,
                'current_value': 0.0,
                'pnl_amount': 0.0,
                'pnl_percentage': 0.0
            }

        # 获取当前价格
        current_price = self._get_current_price(item_id)
        if current_price is None:
            return {
                'success': False,
                'error': 'no_price_data',
                'total_cost': float(holding.get('total_cost', 0)),
                'current_value': 0.0,
                'pnl_amount': 0.0,
                'pnl_percentage': 0.0
            }

        # 计算盈亏
        current_value = current_price * holding.get('total_quantity', 0)
        total_cost = float(holding.get('total_cost', 0))
        pnl_amount = current_value - total_cost
        pnl_percentage = (pnl_amount / total_cost * 100) if total_cost > 0 else 0

        return {
            'success': True,
            'item_id': item_id,
            'item_name': holding.get('item_name', ''),
            'quantity': holding.get('total_quantity', 0),
            'average_cost': float(holding.get('average_cost', 0)),
            'current_price': current_price,
            'total_cost': total_cost,
            'current_value': current_value,
            'pnl_amount': pnl_amount,
            'pnl_percentage': pnl_percentage
        }
    
    def _calculate_portfolio_pnl(self, user_id: str) -> Dict[str, Any]:
        """计算投资组合的盈亏"""
        holdings = self.holding_dao.get_user_holdings(user_id)
        
        total_cost = 0.0
        total_current_value = 0.0
        successful_items = 0
        failed_items = 0
        
        for holding in holdings:
            item_pnl = self._calculate_single_item_pnl(user_id, holding['item_id'])
            if item_pnl['success']:
                total_cost += item_pnl['total_cost']
                total_current_value += item_pnl['current_value']
                successful_items += 1
            else:
                failed_items += 1
                total_cost += holding['total_cost']  # 至少加上成本
        
        pnl_amount = total_current_value - total_cost
        pnl_percentage = (pnl_amount / total_cost * 100) if total_cost > 0 else 0
        
        return {
            'success': True,
            'total_holdings': len(holdings),
            'successful_items': successful_items,
            'failed_items': failed_items,
            'total_cost': total_cost,
            'current_value': total_current_value,
            'pnl_amount': pnl_amount,
            'pnl_percentage': pnl_percentage
        }
    
    def _get_current_price(self, item_id: str) -> Optional[float]:
        """获取当前价格"""
        try:
            snapshot = self.market_snapshot_dao.get_latest_snapshot_by_item(item_id)
            if snapshot and snapshot.get('current_price'):
                return float(snapshot['current_price'])
            return None
        except Exception as e:
            print(f"获取价格失败: {e}")
            return None
    
    def get_holdings_with_current_prices(self, user_id: str, limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """获取带实时价格的持仓列表"""
        try:
            holdings = self.holding_dao.get_user_holdings(user_id, limit=limit, offset=offset)

            # 为每个持仓添加当前价格和盈亏信息
            enriched_holdings = []
            for holding in holdings:
                pnl_data = self._calculate_single_item_pnl(user_id, holding['item_id'])

                enriched_holding = {
                    **holding,
                    'current_price': pnl_data.get('current_price', 0),
                    'current_value': pnl_data.get('current_value', 0),
                    'pnl_amount': pnl_data.get('pnl_amount', 0),
                    'pnl_percentage': pnl_data.get('pnl_percentage', 0),
                    'price_available': pnl_data['success']
                }
                enriched_holdings.append(enriched_holding)

            return enriched_holdings

        except Exception as e:
            print(f"获取持仓列表失败: {e}")
            return []

    def get_holdings_count(self, user_id: str) -> int:
        """获取用户持仓总数"""
        try:
            return self.holding_dao.get_holdings_count(user_id)
        except Exception as e:
            print(f"获取持仓总数失败: {e}")
            return 0
    
    def get_user_transactions(self, user_id: str, **kwargs) -> List[Dict[str, Any]]:
        """获取用户交易记录"""
        try:
            return self.transaction_dao.get_user_transactions(user_id, **kwargs)
        except Exception as e:
            print(f"获取交易记录失败: {e}")
            return []

    def delete_holding(self, user_id: str, item_id: str) -> Dict[str, Any]:
        """删除持仓记录及其所有相关交易"""
        try:
            # 首先检查持仓是否存在
            holding = self.holding_dao.get_holding_by_user_item(user_id, item_id)
            if not holding:
                return {
                    'success': False,
                    'message': '持仓记录不存在'
                }

            item_name = holding.get('item_name', '未知饰品')

            # 删除所有相关交易记录
            deleted_transactions = self.transaction_dao.delete_transactions_by_user_item(user_id, item_id)

            # 删除持仓记录
            deleted_holding = self.holding_dao.delete_holding(user_id, item_id)

            if deleted_holding:
                return {
                    'success': True,
                    'message': f'成功删除持仓 "{item_name}" 及其 {deleted_transactions} 条交易记录'
                }
            else:
                return {
                    'success': False,
                    'message': '删除持仓记录失败'
                }

        except Exception as e:
            print(f"删除持仓失败: {e}")
            return {
                'success': False,
                'message': f'删除失败: {str(e)}'
            }

    def start_realtime_analysis(self, user_id: str, item_id: str) -> Dict[str, Any]:
        """启动饰品实时分析（直接调用与定时任务相同的分析系统）"""
        try:
            # 获取饰品信息
            from src.cs2_investment.dao.item_dao import ItemDAO
            item_dao = ItemDAO()
            item_info = item_dao.get_by_item_id(item_id)

            if not item_info:
                return {
                    'success': False,
                    'message': '饰品信息不存在'
                }

            # 构造SteamDT URL
            market_hash_name = item_info.get('market_hash_name')
            if not market_hash_name:
                return {
                    'success': False,
                    'message': '饰品缺少市场哈希名称，无法进行实时分析'
                }

            import urllib.parse
            encoded_name = urllib.parse.quote(market_hash_name, safe='')
            item_url = f"https://steamdt.com/cs2/{encoded_name}"
            item_name = item_info.get('name', '未知饰品')

            # 直接调用与定时任务相同的分析系统
            import asyncio
            from src.cs2_investment.fx.unified_analysis_system import UnifiedAnalysisSystem

            # 创建分析系统实例
            unified_system = UnifiedAnalysisSystem()

            # 运行分析（与定时任务相同的调用方式）
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    unified_system.run_complete_analysis(item_url, item_name)
                )
            finally:
                loop.close()

            if not api_result:
                return {
                    'success': False,
                    'message': 'API调用失败，请稍后重试'
                }

            return {
                'success': True,
                'message': '实时分析任务已提交到后台执行',
                'task_id': api_result.get('task_id'),
                'item_id': item_id,
                'item_name': item_name,
                'item_url': item_url,
                'analysis_type': 'realtime',
                'status': api_result.get('status', 'pending')
            }

        except Exception as e:
            print(f"启动实时分析失败: {e}")
            return {
                'success': False,
                'message': f'启动分析失败: {str(e)}'
            }

    def start_regular_analysis(self, user_id: str, item_id: str) -> Dict[str, Any]:
        """启动饰品常规分析"""
        try:
            # 获取饰品信息
            from src.cs2_investment.dao.item_dao import ItemDAO
            item_dao = ItemDAO()
            item_info = item_dao.get_by_item_id(item_id)

            if not item_info:
                return {
                    'success': False,
                    'message': '饰品信息不存在'
                }

            # 构造SteamDT URL
            market_hash_name = item_info.get('market_hash_name')
            if not market_hash_name:
                return {
                    'success': False,
                    'message': '饰品缺少市场哈希名称，无法进行常规分析'
                }

            import urllib.parse
            encoded_name = urllib.parse.quote(market_hash_name, safe='')
            item_url = f"https://steamdt.com/cs2/{encoded_name}"
            item_name = item_info.get('name', '未知饰品')

            # 确定用于分析的item_id（优先使用steamdt_item_id）
            analysis_item_id = item_info.get('steamdt_item_id') or item_id

            # 通过API客户端启动常规分析
            from src.cs2_investment.api.clients.api_client import api_client

            # 检查API服务状态
            if not api_client.check_service_health():
                return {
                    'success': False,
                    'message': 'API服务不可用，请确保分析服务已启动'
                }

            # 调用API启动常规分析（使用analysis_item_id）
            api_result = api_client.start_regular_analysis(analysis_item_id, item_name, item_url)

            if not api_result:
                return {
                    'success': False,
                    'message': 'API调用失败，请稍后重试'
                }

            return {
                'success': True,
                'message': '常规分析任务已提交到后台执行',
                'task_id': api_result.get('task_id'),
                'item_id': item_id,
                'item_name': item_name,
                'item_url': item_url,
                'analysis_type': 'regular',
                'status': api_result.get('status', 'pending')
            }

        except Exception as e:
            print(f"启动常规分析失败: {e}")
            return {
                'success': False,
                'message': f'启动分析失败: {str(e)}'
            }

    def get_analysis_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取分析任务状态"""
        try:
            from src.cs2_investment.api.clients.api_client import api_client

            # 检查API服务状态
            if not api_client.check_service_health():
                return {
                    'success': False,
                    'message': 'API服务不可用'
                }

            # 获取任务状态
            status_info = api_client.get_task_status(task_id)

            if not status_info:
                return {
                    'success': False,
                    'message': '无法获取任务状态'
                }

            return {
                'success': True,
                'status': status_info.get('status'),
                'message': status_info.get('message', ''),
                'progress': status_info.get('progress', 0),
                'result': status_info.get('result')
            }

        except Exception as e:
            print(f"获取任务状态失败: {e}")
            return {
                'success': False,
                'message': f'获取状态失败: {str(e)}'
            }
    
    def get_holding_summary(self, user_id: str) -> Dict[str, Any]:
        """获取持仓汇总信息"""
        try:
            # 获取基础统计
            summary = self.holding_dao.calculate_holding_summary(user_id)
            
            # 获取盈亏信息
            pnl_data = self.calculate_pnl(user_id)
            
            # 获取交易统计
            transaction_stats = self.transaction_dao.get_transaction_statistics(user_id)
            
            return {
                **summary,
                **pnl_data,
                'transaction_stats': transaction_stats
            }
            
        except Exception as e:
            print(f"获取持仓汇总失败: {e}")
            return {
                'total_holdings': 0,
                'total_quantity': 0,
                'total_cost': 0.0,
                'current_value': 0.0,
                'pnl_amount': 0.0,
                'pnl_percentage': 0.0
            }
