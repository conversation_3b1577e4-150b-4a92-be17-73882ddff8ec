"""
调度器Web接口

为智能监控调度系统提供Web API接口，支持状态查询、任务管理、配置调整等功能。
基于现有系统的API风格，提供RESTful接口。
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel
from loguru import logger

from .intelligent_monitoring_scheduler import IntelligentMonitoringScheduler, MonitoringPriority


class SchedulerConfigUpdate(BaseModel):
    """调度器配置更新模型"""
    base_interval_minutes: Optional[int] = None
    max_concurrent_tasks: Optional[int] = None
    task_timeout_minutes: Optional[int] = None
    retry_delay_minutes: Optional[int] = None


class TaskPriorityUpdate(BaseModel):
    """任务优先级更新模型"""
    task_id: str
    priority: str  # CRITICAL, HIGH, MEDIUM, LOW


class SchedulerWebInterface:
    """调度器Web接口"""
    
    def __init__(self):
        """初始化Web接口"""
        self.scheduler: Optional[IntelligentMonitoringScheduler] = None
        self.router = APIRouter(prefix="/api/v1/scheduler", tags=["智能监控调度"])
        self.logger = logger.bind(interface=self.__class__.__name__)
        
        # 注册路由
        self._register_routes()
        
        self.logger.info("调度器Web接口初始化完成")
    
    def set_scheduler(self, scheduler: IntelligentMonitoringScheduler):
        """设置调度器实例"""
        self.scheduler = scheduler
        self.logger.info("调度器实例已设置")
    
    def _register_routes(self):
        """注册API路由"""
        
        @self.router.get("/status", summary="获取调度器状态")
        async def get_scheduler_status():
            """获取调度器状态"""
            try:
                if not self.scheduler:
                    raise HTTPException(status_code=503, detail="调度器未初始化")
                
                status = self.scheduler.get_status()
                return JSONResponse(content={
                    "success": True,
                    "data": status,
                    "timestamp": datetime.now().isoformat()
                })
                
            except Exception as e:
                self.logger.error(f"获取调度器状态失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.router.post("/start", summary="启动调度器")
        async def start_scheduler(background_tasks: BackgroundTasks):
            """启动调度器"""
            try:
                if not self.scheduler:
                    raise HTTPException(status_code=503, detail="调度器未初始化")
                
                if self.scheduler.is_running:
                    return JSONResponse(content={
                        "success": True,
                        "message": "调度器已在运行中",
                        "timestamp": datetime.now().isoformat()
                    })
                
                # 在后台启动调度器
                background_tasks.add_task(self.scheduler.start)
                
                return JSONResponse(content={
                    "success": True,
                    "message": "调度器启动中...",
                    "timestamp": datetime.now().isoformat()
                })
                
            except Exception as e:
                self.logger.error(f"启动调度器失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.router.post("/stop", summary="停止调度器")
        async def stop_scheduler():
            """停止调度器"""
            try:
                if not self.scheduler:
                    raise HTTPException(status_code=503, detail="调度器未初始化")
                
                if not self.scheduler.is_running:
                    return JSONResponse(content={
                        "success": True,
                        "message": "调度器已停止",
                        "timestamp": datetime.now().isoformat()
                    })
                
                self.scheduler.stop()
                
                return JSONResponse(content={
                    "success": True,
                    "message": "调度器已停止",
                    "timestamp": datetime.now().isoformat()
                })
                
            except Exception as e:
                self.logger.error(f"停止调度器失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.router.get("/tasks", summary="获取任务列表")
        async def get_tasks(task_id: Optional[str] = None):
            """获取任务列表或特定任务详情"""
            try:
                if not self.scheduler:
                    raise HTTPException(status_code=503, detail="调度器未初始化")
                
                task_details = self.scheduler.get_task_details(task_id)
                
                if 'error' in task_details:
                    raise HTTPException(status_code=404, detail=task_details['error'])
                
                return JSONResponse(content={
                    "success": True,
                    "data": task_details,
                    "timestamp": datetime.now().isoformat()
                })
                
            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"获取任务列表失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.router.put("/config", summary="更新调度器配置")
        async def update_scheduler_config(config: SchedulerConfigUpdate):
            """更新调度器配置"""
            try:
                if not self.scheduler:
                    raise HTTPException(status_code=503, detail="调度器未初始化")
                
                # 更新配置
                updated_fields = []
                
                if config.base_interval_minutes is not None:
                    self.scheduler.config['base_interval_minutes'] = config.base_interval_minutes
                    updated_fields.append('base_interval_minutes')
                
                if config.max_concurrent_tasks is not None:
                    self.scheduler.config['max_concurrent_tasks'] = config.max_concurrent_tasks
                    updated_fields.append('max_concurrent_tasks')
                
                if config.task_timeout_minutes is not None:
                    self.scheduler.config['task_timeout_minutes'] = config.task_timeout_minutes
                    updated_fields.append('task_timeout_minutes')
                
                if config.retry_delay_minutes is not None:
                    self.scheduler.config['retry_delay_minutes'] = config.retry_delay_minutes
                    updated_fields.append('retry_delay_minutes')
                
                return JSONResponse(content={
                    "success": True,
                    "message": f"配置已更新: {', '.join(updated_fields)}",
                    "updated_config": {field: self.scheduler.config[field] for field in updated_fields},
                    "timestamp": datetime.now().isoformat()
                })
                
            except Exception as e:
                self.logger.error(f"更新调度器配置失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.router.get("/statistics", summary="获取统计信息")
        async def get_statistics():
            """获取调度器统计信息"""
            try:
                if not self.scheduler:
                    raise HTTPException(status_code=503, detail="调度器未初始化")
                
                status = self.scheduler.get_status()
                statistics = status.get('statistics', {})
                queue_status = status.get('queue_status', {})
                
                # 计算成功率
                total_completed = statistics.get('total_tasks_completed', 0)
                total_failed = statistics.get('total_tasks_failed', 0)
                total_processed = total_completed + total_failed
                
                success_rate = (total_completed / total_processed * 100) if total_processed > 0 else 0
                
                enhanced_stats = {
                    **statistics,
                    **queue_status,
                    'success_rate': round(success_rate, 2),
                    'total_processed': total_processed
                }
                
                return JSONResponse(content={
                    "success": True,
                    "data": enhanced_stats,
                    "timestamp": datetime.now().isoformat()
                })
                
            except Exception as e:
                self.logger.error(f"获取统计信息失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.router.get("/health", summary="健康检查")
        async def health_check():
            """健康检查接口"""
            try:
                health_status = {
                    "status": "healthy",
                    "scheduler_initialized": self.scheduler is not None,
                    "scheduler_running": self.scheduler.is_running if self.scheduler else False,
                    "timestamp": datetime.now().isoformat()
                }
                
                if self.scheduler:
                    status = self.scheduler.get_status()
                    health_status.update({
                        "queue_size": status.get('queue_status', {}).get('pending_tasks', 0),
                        "running_tasks": status.get('queue_status', {}).get('running_tasks', 0),
                        "analysis_engines": status.get('analysis_engines', 0)
                    })
                
                return JSONResponse(content={
                    "success": True,
                    "data": health_status
                })
                
            except Exception as e:
                self.logger.error(f"健康检查失败: {e}")
                return JSONResponse(
                    status_code=500,
                    content={
                        "success": False,
                        "status": "unhealthy",
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    }
                )
        
        @self.router.post("/tasks/{task_id}/retry", summary="重试失败任务")
        async def retry_failed_task(task_id: str):
            """重试失败的任务"""
            try:
                if not self.scheduler:
                    raise HTTPException(status_code=503, detail="调度器未初始化")
                
                # 查找失败的任务
                if task_id not in self.scheduler.failed_tasks:
                    raise HTTPException(status_code=404, detail="未找到失败的任务")
                
                failed_task = self.scheduler.failed_tasks[task_id]
                
                # 重置任务状态
                failed_task.status = failed_task.status.PENDING
                failed_task.retry_count = 0
                failed_task.last_error = None
                failed_task.next_run_time = datetime.now()
                
                # 移回任务队列
                self.scheduler.task_queue.append(failed_task)
                self.scheduler.failed_tasks.pop(task_id)
                
                return JSONResponse(content={
                    "success": True,
                    "message": f"任务 {task_id} 已重新加入队列",
                    "timestamp": datetime.now().isoformat()
                })
                
            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"重试任务失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.router.delete("/tasks/{task_id}", summary="删除任务")
        async def delete_task(task_id: str):
            """删除任务"""
            try:
                if not self.scheduler:
                    raise HTTPException(status_code=503, detail="调度器未初始化")
                
                # 从各个队列中查找并删除任务
                deleted = False
                
                # 从待执行队列删除
                self.scheduler.task_queue = [
                    task for task in self.scheduler.task_queue 
                    if task.task_id != task_id
                ]
                
                # 从失败队列删除
                if task_id in self.scheduler.failed_tasks:
                    self.scheduler.failed_tasks.pop(task_id)
                    deleted = True
                
                # 从完成队列删除
                if task_id in self.scheduler.completed_tasks:
                    self.scheduler.completed_tasks.pop(task_id)
                    deleted = True
                
                # 不能删除正在运行的任务
                if task_id in self.scheduler.running_tasks:
                    raise HTTPException(status_code=400, detail="无法删除正在运行的任务")
                
                if not deleted:
                    raise HTTPException(status_code=404, detail="未找到指定任务")
                
                return JSONResponse(content={
                    "success": True,
                    "message": f"任务 {task_id} 已删除",
                    "timestamp": datetime.now().isoformat()
                })
                
            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"删除任务失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))


# 全局实例
scheduler_web_interface = SchedulerWebInterface()
