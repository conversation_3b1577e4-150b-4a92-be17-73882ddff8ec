"""
应用程序配置设置

使用Pydantic进行配置管理，支持环境变量和.env文件。
"""

from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings
from pathlib import Path


class DatabaseSettings(BaseSettings):
    """数据库配置"""

    host: str = Field(default="localhost", alias="DB_HOST")
    port: int = Field(default=3306, alias="DB_PORT")
    user: str = Field(default="root", alias="DB_USER")
    password: str = Field(default="120130", alias="DB_PASSWORD")
    name: str = Field(default="cs2_market", alias="DB_NAME")
    charset: str = Field(default="utf8mb4")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"
    
    @property
    def url(self) -> str:
        """获取数据库连接URL"""
        from urllib.parse import quote_plus
        encoded_password = quote_plus(self.password)
        return f"mysql+pymysql://{self.user}:{encoded_password}@{self.host}:{self.port}/{self.name}?charset={self.charset}"
    
    @property
    def connection_params(self) -> dict:
        """获取数据库连接参数"""
        return {
            "host": self.host,
            "port": self.port,
            "user": self.user,
            "password": self.password,
            "database": self.name,
            "charset": self.charset,
            "autocommit": False,
            "raise_on_warnings": True,
        }


class LoggingSettings(BaseSettings):
    """日志配置"""

    level: str = Field(default="INFO", alias="LOG_LEVEL")
    file: str = Field(default="logs/cs2_investment.log", alias="LOG_FILE")
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    rotation: str = "10 MB"
    retention: str = "30 days"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"


class AppSettings(BaseSettings):
    """应用程序配置"""

    debug: bool = Field(default=False, alias="DEBUG")
    timezone: str = Field(default="Asia/Shanghai", alias="TIMEZONE")
    data_dir: str = Field(default="data", alias="DATA_DIR")
    output_dir: str = Field(default="output", alias="OUTPUT_DIR")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"


class Settings(BaseSettings):
    """主配置类"""

    # 子配置
    database: DatabaseSettings = DatabaseSettings()
    logging: LoggingSettings = LoggingSettings()
    app: AppSettings = AppSettings()

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"
    
    @property
    def project_root(self) -> Path:
        """获取项目根目录"""
        return Path(__file__).parent.parent.parent.parent
    
    @property
    def data_path(self) -> Path:
        """获取数据目录路径"""
        return self.project_root / self.app.data_dir
    
    @property
    def output_path(self) -> Path:
        """获取输出目录路径"""
        return self.project_root / self.app.output_dir
    
    @property
    def logs_path(self) -> Path:
        """获取日志目录路径"""
        return self.project_root / "logs"


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
