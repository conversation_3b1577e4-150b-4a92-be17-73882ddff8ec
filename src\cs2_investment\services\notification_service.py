"""
通知服务

提供统一的通知接口，支持多种通知方式：
- 系统内消息通知
- 邮件通知
- 桌面弹窗通知

为触发条件系统提供通知能力。
"""

import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from dataclasses import dataclass
from loguru import logger

# 可选导入，避免依赖问题
try:
    import smtplib
    from email.mime.text import MIMEText
    from email.mime.multipart import MIMEMultipart
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False

try:
    import plyer
    DESKTOP_NOTIFICATION_AVAILABLE = True
except ImportError:
    DESKTOP_NOTIFICATION_AVAILABLE = False


class NotificationType(Enum):
    """通知类型"""
    SYSTEM_MESSAGE = "system_message"
    EMAIL = "email"
    DESKTOP_POPUP = "desktop_popup"


class NotificationPriority(Enum):
    """通知优先级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class NotificationStatus(Enum):
    """通知状态"""
    PENDING = "pending"
    SENT = "sent"
    FAILED = "failed"
    DELIVERED = "delivered"


@dataclass
class NotificationMessage:
    """通知消息"""
    title: str
    content: str
    notification_type: NotificationType
    priority: NotificationPriority
    recipient: str
    item_id: Optional[str] = None
    item_name: Optional[str] = None
    trigger_type: Optional[str] = None
    analysis_summary: Optional[Dict] = None
    recommended_action: Optional[str] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


class NotificationService:
    """通知服务"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化通知服务
        
        Args:
            config: 通知配置
        """
        self.logger = logger.bind(service=self.__class__.__name__)
        self.config = config or self._get_default_config()
        
        # 通知器实例
        self.notifiers = {}
        self._initialize_notifiers()
        
        # 通知历史
        self.notification_history: List[Dict] = []
        
        # 频率控制
        self.last_notification_time = {}
        
        self.logger.info("通知服务初始化完成")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'system_message': {
                'enabled': True,
                'max_messages': 100,  # 最大消息数
                'retention_hours': 24  # 消息保留时间
            },
            'email': {
                'enabled': EMAIL_AVAILABLE,
                'smtp_server': 'smtp.gmail.com',
                'smtp_port': 587,
                'username': '',
                'password': '',
                'from_email': '',
                'use_tls': True
            },
            'desktop_popup': {
                'enabled': DESKTOP_NOTIFICATION_AVAILABLE,
                'timeout': 10,  # 显示时间（秒）
                'app_name': 'CS2投资助手'
            },
            'frequency_control': {
                'min_interval_seconds': 60,  # 最小通知间隔
                'max_notifications_per_hour': 10  # 每小时最大通知数
            }
        }
    
    def _initialize_notifiers(self):
        """初始化通知器"""
        try:
            # 系统消息通知器
            self.notifiers[NotificationType.SYSTEM_MESSAGE] = SystemMessageNotifier(
                self.config.get('system_message', {})
            )
            
            # 邮件通知器
            if EMAIL_AVAILABLE and self.config.get('email', {}).get('enabled', False):
                self.notifiers[NotificationType.EMAIL] = EmailNotifier(
                    self.config.get('email', {})
                )
            
            # 桌面弹窗通知器
            if DESKTOP_NOTIFICATION_AVAILABLE and self.config.get('desktop_popup', {}).get('enabled', False):
                self.notifiers[NotificationType.DESKTOP_POPUP] = DesktopNotifier(
                    self.config.get('desktop_popup', {})
                )
            
            self.logger.info(f"通知器初始化完成，可用通知器: {list(self.notifiers.keys())}")
            
        except Exception as e:
            self.logger.error(f"通知器初始化失败: {e}")
    
    async def send_notification(self, message: NotificationMessage) -> bool:
        """
        发送通知
        
        Args:
            message: 通知消息
            
        Returns:
            bool: 是否发送成功
        """
        try:
            # 频率控制检查
            if not self._check_frequency_limit(message):
                self.logger.warning(f"通知频率超限，跳过发送: {message.title}")
                return False
            
            # 获取对应的通知器
            notifier = self.notifiers.get(message.notification_type)
            if not notifier:
                self.logger.error(f"未找到通知器: {message.notification_type}")
                return False
            
            # 发送通知
            success = await notifier.send(message)
            
            # 记录通知历史
            self._record_notification(message, success)
            
            # 更新频率控制
            self._update_frequency_control(message)
            
            if success:
                self.logger.info(f"通知发送成功: {message.title} -> {message.recipient}")
            else:
                self.logger.error(f"通知发送失败: {message.title} -> {message.recipient}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"发送通知异常: {e}")
            return False
    
    async def send_trigger_notification(self, trigger_result, user_id: str, 
                                      notification_types: List[NotificationType] = None) -> Dict[str, bool]:
        """
        发送触发条件通知
        
        Args:
            trigger_result: 触发结果
            user_id: 用户ID
            notification_types: 通知类型列表
            
        Returns:
            Dict[str, bool]: 各通知类型的发送结果
        """
        try:
            if notification_types is None:
                notification_types = [NotificationType.SYSTEM_MESSAGE]
            
            # 构建通知内容
            title, content = self._build_trigger_notification_content(trigger_result)
            
            # 确定优先级
            priority = self._determine_priority(trigger_result)
            
            results = {}
            
            # 发送各种类型的通知
            for notification_type in notification_types:
                message = NotificationMessage(
                    title=title,
                    content=content,
                    notification_type=notification_type,
                    priority=priority,
                    recipient=user_id,
                    trigger_type=trigger_result.signal_type.value,
                    analysis_summary=trigger_result.analysis_summary,
                    recommended_action=trigger_result.recommended_action
                )
                
                success = await self.send_notification(message)
                results[notification_type.value] = success
            
            return results
            
        except Exception as e:
            self.logger.error(f"发送触发通知异常: {e}")
            return {}
    
    def _build_trigger_notification_content(self, trigger_result) -> tuple:
        """构建触发通知内容"""
        try:
            signal_type = trigger_result.signal_type.value.upper()
            confidence = trigger_result.confidence
            risk_level = trigger_result.risk_level.value
            
            # 构建标题
            if trigger_result.signal_type.value == 'buy':
                title = f"🟢 买入信号 - 置信度{confidence:.0%}"
            elif trigger_result.signal_type.value == 'sell':
                title = f"🔴 卖出信号 - 置信度{confidence:.0%}"
            elif trigger_result.signal_type.value == 'risk_alert':
                alert_level = trigger_result.alert_level.value if trigger_result.alert_level else 'unknown'
                title = f"⚠️ 风险预警 - {alert_level.upper()}"
            else:
                title = f"📊 {signal_type}信号 - 置信度{confidence:.0%}"
            
            # 构建内容
            content_parts = [
                f"信号类型: {signal_type}",
                f"置信度: {confidence:.1%}",
                f"风险等级: {risk_level.upper()}",
                f"推荐操作: {trigger_result.recommended_action}",
                ""
            ]
            
            # 添加触发原因
            if trigger_result.trigger_reasons:
                content_parts.append("触发原因:")
                for i, reason in enumerate(trigger_result.trigger_reasons[:3], 1):
                    content_parts.append(f"{i}. {reason}")
                content_parts.append("")
            
            # 添加分析摘要
            if trigger_result.analysis_summary:
                content_parts.append("分析摘要:")
                for layer, summary in trigger_result.analysis_summary.items():
                    content_parts.append(f"• {layer}: {summary[:50]}...")
            
            content = "\n".join(content_parts)
            
            return title, content
            
        except Exception as e:
            self.logger.error(f"构建通知内容失败: {e}")
            return "通知", "触发条件通知"
    
    def _determine_priority(self, trigger_result) -> NotificationPriority:
        """确定通知优先级"""
        try:
            # 风险预警优先级最高
            if trigger_result.signal_type.value == 'risk_alert':
                if hasattr(trigger_result, 'alert_level') and trigger_result.alert_level:
                    if trigger_result.alert_level.value == 'level_3':
                        return NotificationPriority.URGENT
                    elif trigger_result.alert_level.value == 'level_2':
                        return NotificationPriority.HIGH
                    else:
                        return NotificationPriority.MEDIUM
                return NotificationPriority.HIGH
            
            # 买卖信号根据置信度确定优先级
            confidence = trigger_result.confidence
            if confidence >= 0.8:
                return NotificationPriority.HIGH
            elif confidence >= 0.6:
                return NotificationPriority.MEDIUM
            else:
                return NotificationPriority.LOW
                
        except Exception:
            return NotificationPriority.MEDIUM

    def _check_frequency_limit(self, message: NotificationMessage) -> bool:
        """检查频率限制"""
        try:
            current_time = datetime.now()
            key = f"{message.recipient}_{message.notification_type.value}"

            # 检查最小间隔
            min_interval = self.config.get('frequency_control', {}).get('min_interval_seconds', 60)
            if key in self.last_notification_time:
                time_diff = (current_time - self.last_notification_time[key]).total_seconds()
                if time_diff < min_interval:
                    return False

            # 检查每小时最大通知数
            max_per_hour = self.config.get('frequency_control', {}).get('max_notifications_per_hour', 10)
            hour_ago = current_time - timedelta(hours=1)
            recent_count = len([
                record for record in self.notification_history
                if (record.get('recipient') == message.recipient and
                    record.get('notification_type') == message.notification_type.value and
                    record.get('timestamp', datetime.min) > hour_ago)
            ])

            if recent_count >= max_per_hour:
                return False

            return True

        except Exception as e:
            self.logger.error(f"频率限制检查失败: {e}")
            return True  # 出错时允许发送

    def _update_frequency_control(self, message: NotificationMessage):
        """更新频率控制"""
        try:
            key = f"{message.recipient}_{message.notification_type.value}"
            self.last_notification_time[key] = datetime.now()
        except Exception as e:
            self.logger.error(f"更新频率控制失败: {e}")

    def _record_notification(self, message: NotificationMessage, success: bool):
        """记录通知历史"""
        try:
            record = {
                'title': message.title,
                'notification_type': message.notification_type.value,
                'priority': message.priority.value,
                'recipient': message.recipient,
                'success': success,
                'timestamp': datetime.now(),
                'trigger_type': message.trigger_type,
                'item_id': message.item_id,
                'item_name': message.item_name
            }

            self.notification_history.append(record)

            # 清理旧记录
            cutoff_time = datetime.now() - timedelta(hours=24)
            self.notification_history = [
                r for r in self.notification_history
                if r.get('timestamp', datetime.min) > cutoff_time
            ]

        except Exception as e:
            self.logger.error(f"记录通知历史失败: {e}")

    def get_notification_history(self, recipient: str = None, hours: int = 24) -> List[Dict]:
        """获取通知历史"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            history = [
                record for record in self.notification_history
                if record.get('timestamp', datetime.min) > cutoff_time
            ]

            if recipient:
                history = [r for r in history if r.get('recipient') == recipient]

            return sorted(history, key=lambda x: x.get('timestamp', datetime.min), reverse=True)

        except Exception as e:
            self.logger.error(f"获取通知历史失败: {e}")
            return []

    def get_notification_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取通知统计"""
        try:
            history = self.get_notification_history(hours=hours)

            total_count = len(history)
            success_count = len([r for r in history if r.get('success', False)])

            # 按类型统计
            type_stats = {}
            for notification_type in NotificationType:
                type_records = [r for r in history if r.get('notification_type') == notification_type.value]
                type_stats[notification_type.value] = {
                    'total': len(type_records),
                    'success': len([r for r in type_records if r.get('success', False)])
                }

            # 按优先级统计
            priority_stats = {}
            for priority in NotificationPriority:
                priority_records = [r for r in history if r.get('priority') == priority.value]
                priority_stats[priority.value] = len(priority_records)

            return {
                'period_hours': hours,
                'total_notifications': total_count,
                'successful_notifications': success_count,
                'success_rate': (success_count / total_count * 100) if total_count > 0 else 0,
                'type_statistics': type_stats,
                'priority_statistics': priority_stats
            }

        except Exception as e:
            self.logger.error(f"获取通知统计失败: {e}")
            return {}

    def update_config(self, new_config: Dict):
        """更新配置"""
        try:
            self.config.update(new_config)
            self._initialize_notifiers()  # 重新初始化通知器
            self.logger.info("通知服务配置已更新")
        except Exception as e:
            self.logger.error(f"更新配置失败: {e}")


class BaseNotifier:
    """通知器基类"""

    def __init__(self, config: Dict):
        self.config = config
        self.logger = logger.bind(notifier=self.__class__.__name__)

    async def send(self, message: NotificationMessage) -> bool:
        """发送通知（子类实现）"""
        raise NotImplementedError


class SystemMessageNotifier(BaseNotifier):
    """系统内消息通知器"""

    def __init__(self, config: Dict):
        super().__init__(config)
        self.messages = []  # 存储系统消息

    async def send(self, message: NotificationMessage) -> bool:
        """发送系统内消息"""
        try:
            system_message = {
                'id': f"msg_{int(datetime.now().timestamp())}",
                'title': message.title,
                'content': message.content,
                'priority': message.priority.value,
                'recipient': message.recipient,
                'created_at': message.created_at,
                'read': False,
                'trigger_type': message.trigger_type,
                'item_id': message.item_id,
                'item_name': message.item_name
            }

            self.messages.append(system_message)

            # 清理旧消息
            max_messages = self.config.get('max_messages', 100)
            if len(self.messages) > max_messages:
                self.messages = self.messages[-max_messages:]

            self.logger.info(f"系统消息发送成功: {message.title}")
            return True

        except Exception as e:
            self.logger.error(f"系统消息发送失败: {e}")
            return False

    def get_messages(self, recipient: str, unread_only: bool = False) -> List[Dict]:
        """获取用户消息"""
        try:
            user_messages = [
                msg for msg in self.messages
                if msg.get('recipient') == recipient
            ]

            if unread_only:
                user_messages = [msg for msg in user_messages if not msg.get('read', False)]

            return sorted(user_messages, key=lambda x: x.get('created_at', datetime.min), reverse=True)

        except Exception as e:
            self.logger.error(f"获取用户消息失败: {e}")
            return []

    def mark_as_read(self, message_id: str) -> bool:
        """标记消息为已读"""
        try:
            for msg in self.messages:
                if msg.get('id') == message_id:
                    msg['read'] = True
                    return True
            return False
        except Exception as e:
            self.logger.error(f"标记消息已读失败: {e}")
            return False


class EmailNotifier(BaseNotifier):
    """邮件通知器"""

    def __init__(self, config: Dict):
        super().__init__(config)
        self.smtp_server = config.get('smtp_server', 'smtp.gmail.com')
        self.smtp_port = config.get('smtp_port', 587)
        self.username = config.get('username', '')
        self.password = config.get('password', '')
        self.from_email = config.get('from_email', '')
        self.use_tls = config.get('use_tls', True)

    async def send(self, message: NotificationMessage) -> bool:
        """发送邮件通知"""
        if not EMAIL_AVAILABLE:
            self.logger.warning("邮件功能不可用，缺少相关依赖")
            return False

        try:
            # 构建邮件内容
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = message.recipient
            msg['Subject'] = message.title

            # 构建HTML内容
            html_content = self._build_html_content(message)
            msg.attach(MIMEText(html_content, 'html', 'utf-8'))

            # 发送邮件
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls()

                if self.username and self.password:
                    server.login(self.username, self.password)

                server.send_message(msg)

            self.logger.info(f"邮件发送成功: {message.title} -> {message.recipient}")
            return True

        except Exception as e:
            self.logger.error(f"邮件发送失败: {e}")
            return False

    def _build_html_content(self, message: NotificationMessage) -> str:
        """构建HTML邮件内容"""
        try:
            # 根据优先级选择颜色
            priority_colors = {
                'urgent': '#ff4444',
                'high': '#ff8800',
                'medium': '#0088ff',
                'low': '#888888'
            }
            color = priority_colors.get(message.priority.value, '#0088ff')

            html_template = f"""
            <html>
            <head>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ background-color: {color}; color: white; padding: 15px; border-radius: 5px; }}
                    .content {{ padding: 20px; border: 1px solid #ddd; border-radius: 5px; margin-top: 10px; }}
                    .footer {{ margin-top: 20px; font-size: 12px; color: #666; }}
                    .highlight {{ background-color: #f0f8ff; padding: 10px; border-left: 4px solid {color}; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>{message.title}</h2>
                    <p>优先级: {message.priority.value.upper()}</p>
                </div>

                <div class="content">
                    <pre style="white-space: pre-wrap; font-family: Arial, sans-serif;">{message.content}</pre>

                    {self._build_analysis_section(message)}

                    {self._build_action_section(message)}
                </div>

                <div class="footer">
                    <p>发送时间: {message.created_at.strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p>CS2饰品智能投资决策系统</p>
                </div>
            </body>
            </html>
            """

            return html_template

        except Exception as e:
            self.logger.error(f"构建HTML内容失败: {e}")
            return f"<html><body><h3>{message.title}</h3><p>{message.content}</p></body></html>"

    def _build_analysis_section(self, message: NotificationMessage) -> str:
        """构建分析部分"""
        if not message.analysis_summary:
            return ""

        try:
            analysis_html = '<div class="highlight"><h4>分析详情:</h4><ul>'
            for layer, summary in message.analysis_summary.items():
                analysis_html += f'<li><strong>{layer}:</strong> {summary}</li>'
            analysis_html += '</ul></div>'
            return analysis_html
        except Exception:
            return ""

    def _build_action_section(self, message: NotificationMessage) -> str:
        """构建操作建议部分"""
        if not message.recommended_action:
            return ""

        return f'<div class="highlight"><h4>推荐操作:</h4><p>{message.recommended_action}</p></div>'


class DesktopNotifier(BaseNotifier):
    """桌面弹窗通知器"""

    def __init__(self, config: Dict):
        super().__init__(config)
        self.app_name = config.get('app_name', 'CS2投资助手')
        self.timeout = config.get('timeout', 10)

    async def send(self, message: NotificationMessage) -> bool:
        """发送桌面弹窗通知"""
        if not DESKTOP_NOTIFICATION_AVAILABLE:
            self.logger.warning("桌面通知功能不可用，缺少plyer依赖")
            return False

        try:
            # 简化内容用于桌面通知
            simplified_content = self._simplify_content(message.content)

            # 选择图标
            icon_path = self._get_icon_path(message.priority)

            # 发送桌面通知
            plyer.notification.notify(
                title=message.title,
                message=simplified_content,
                app_name=self.app_name,
                timeout=self.timeout,
                app_icon=icon_path
            )

            self.logger.info(f"桌面通知发送成功: {message.title}")
            return True

        except Exception as e:
            self.logger.error(f"桌面通知发送失败: {e}")
            return False

    def _simplify_content(self, content: str) -> str:
        """简化内容用于桌面通知"""
        try:
            # 取前100个字符
            simplified = content.replace('\n', ' ').strip()
            if len(simplified) > 100:
                simplified = simplified[:97] + "..."
            return simplified
        except Exception:
            return "触发条件通知"

    def _get_icon_path(self, priority: NotificationPriority) -> Optional[str]:
        """获取图标路径"""
        # 这里可以根据优先级返回不同的图标路径
        # 暂时返回None使用系统默认图标
        return None
