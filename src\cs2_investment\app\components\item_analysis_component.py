"""
饰品分析通用组件

提供饰品分析功能的通用组件，可在持仓管理、收藏列表、饰品列表等页面复用。
包含实时分析、常规分析、启动新分析等功能。
"""

import streamlit as st
import time
from typing import Dict, Any, Optional


class ItemAnalysisComponent:
    """饰品分析通用组件"""
    
    def __init__(self):
        """初始化组件"""
        self._init_services()
    
    def _init_services(self):
        """初始化分析服务"""
        if 'analysis_dao' not in st.session_state:
            from src.cs2_investment.dao.analysis_result_dao import AnalysisResultDAO
            st.session_state.analysis_dao = AnalysisResultDAO()
        
        if 'realtime_dao' not in st.session_state:
            from src.cs2_investment.dao.analysis_result_dao import RealtimeAnalysisResultDAO
            st.session_state.realtime_dao = RealtimeAnalysisResultDAO()
        
        if 'holding_service' not in st.session_state:
            from src.cs2_investment.app.services.holding_service import HoldingService
            st.session_state.holding_service = HoldingService()

        if 'item_dao' not in st.session_state:
            from src.cs2_investment.dao.item_dao import ItemDAO
            st.session_state.item_dao = ItemDAO()

    def _get_analysis_item_id(self, item_id: str) -> str:
        """
        获取用于查询分析数据的ID

        由于分析数据可能是用旧的item_id（现在的steamdt_item_id）保存的，
        需要先查询items表获取steamdt_item_id，如果有则使用steamdt_item_id，
        否则使用当前的item_id

        Args:
            item_id: 当前的饰品ID

        Returns:
            str: 用于查询分析数据的ID
        """
        try:
            # 确保item_dao已初始化
            self._init_services()

            # 查询饰品信息
            item_data = st.session_state.item_dao.get_by_item_id(item_id)

            if item_data and item_data.get('steamdt_item_id'):
                # 如果有steamdt_item_id，优先使用它（这是旧的item_id，分析数据可能用这个保存的）
                return item_data['steamdt_item_id']
            else:
                # 如果没有steamdt_item_id，使用当前的item_id
                return item_id

        except Exception as e:
            # 如果查询失败，使用当前的item_id作为备用
            st.warning(f"获取分析ID失败，使用当前ID: {e}")
            return item_id

    def render_analysis_button(self, item_id: str, button_key: str, help_text: str = "查看分析结果", dialog_key_suffix: str = "") -> bool:
        """
        渲染分析按钮

        Args:
            item_id: 饰品ID
            button_key: 按钮的唯一key
            help_text: 按钮提示文本
            dialog_key_suffix: 对话框key的后缀，用于区分不同页面的对话框

        Returns:
            bool: 是否点击了按钮
        """
        if st.button("📊", key=button_key, help=help_text):
            st.session_state[f'show_analysis_{item_id}{dialog_key_suffix}'] = True
            st.rerun()
            return True
        return False
    
    def render_analysis_dialog(self, item_data: Dict[str, Any], dialog_key_suffix: str = ""):
        """
        渲染分析结果对话框
        
        Args:
            item_data: 饰品数据字典，必须包含 item_id 和 item_name
            dialog_key_suffix: 对话框key的后缀，用于区分不同页面的对话框
        """
        item_id = item_data.get('item_id')
        item_name = item_data.get('item_name', '未知饰品')
        
        if not item_id:
            st.error("❌ 饰品ID不能为空")
            return
        
        # 检查是否需要显示分析对话框
        dialog_key = f'show_analysis_{item_id}{dialog_key_suffix}'
        if not st.session_state.get(dialog_key, False):
            return
        
        with st.container():
            st.markdown(f"### 📊 分析结果: {item_name}")

            # 创建标签页（隐藏实时分析tab）
            # tab1, tab2 = st.tabs(["📈 实时分析", "📊 常规分析"])
            #
            # with tab1:
            #     self._render_realtime_analysis_tab(item_id, item_name, dialog_key_suffix)
            #
            # with tab2:
            #     self._render_regular_analysis_tab(item_id, item_name, dialog_key_suffix)

            # 只显示常规分析
            self._render_regular_analysis_tab(item_id, item_name, dialog_key_suffix)

            # 关闭按钮
            close_key = f"close_analysis_dialog_{item_id}{dialog_key_suffix}"
            if st.button("❌ 关闭", key=close_key):
                st.session_state[dialog_key] = False
                st.rerun()
    
    def _render_realtime_analysis_tab(self, item_id: str, item_name: str, dialog_key_suffix: str = ""):
        """渲染实时分析标签页（包含结果展示和启动分析功能）"""
        try:
            # 确保服务已初始化
            self._init_services()

            # 检查是否有正在进行的实时分析任务
            realtime_task_key = f'realtime_analysis_task_{item_id}{dialog_key_suffix}'

            if realtime_task_key in st.session_state:
                # 显示分析进度
                self._render_analysis_progress(realtime_task_key, item_id, "实时分析", dialog_key_suffix)
                return

            # 获取用于查询分析数据的ID（优先使用steamdt_item_id）
            analysis_item_id = self._get_analysis_item_id(item_id)

            # 获取最新的实时分析结果
            realtime_result = st.session_state.realtime_dao.get_latest_realtime_analysis(analysis_item_id)

            if realtime_result:
                # 显示现有分析结果
                st.success(f"✅ 最新实时分析结果")
                st.caption(f"分析时间: {realtime_result.created_at}")

                # 创建三列布局显示关键指标
                col1, col2, col3 = st.columns(3)

                with col1:
                    # 价格信息
                    if realtime_result.current_price:
                        st.metric("当前价格", f"¥{realtime_result.current_price:,.2f}")
                    if realtime_result.price_change_percent_24h:
                        change_color = "normal" if realtime_result.price_change_percent_24h >= 0 else "inverse"
                        st.metric("24h涨跌", f"{realtime_result.price_change_percent_24h:+.2f}%",
                                 delta_color=change_color)

                with col2:
                    # 趋势和信号
                    if realtime_result.price_trend:
                        trend_emoji = {"上涨": "📈", "下跌": "📉", "震荡": "📊"}.get(realtime_result.price_trend, "📊")
                        st.metric("价格趋势", f"{trend_emoji} {realtime_result.price_trend}")

                    if realtime_result.risk_level:
                        risk_emoji = {"LOW": "🟢", "MEDIUM": "🟡", "HIGH": "🔴"}.get(realtime_result.risk_level, "⚪")
                        st.metric("风险等级", f"{risk_emoji} {realtime_result.risk_level}")

                with col3:
                    # 异常检测
                    if realtime_result.anomaly_count is not None:
                        anomaly_emoji = "⚠️" if realtime_result.anomaly_count > 0 else "✅"
                        st.metric("异常检测", f"{anomaly_emoji} {realtime_result.anomaly_count} 个")

                    if realtime_result.volume_status:
                        volume_emoji = {"高": "🔥", "中": "📊", "低": "❄️"}.get(realtime_result.volume_status, "📊")
                        st.metric("成交量", f"{volume_emoji} {realtime_result.volume_status}")

                # 显示分析报告
                if realtime_result.realtime_report_content:
                    st.markdown("#### 📋 分析报告")
                    st.markdown(realtime_result.realtime_report_content)

                # 显示图表
                if realtime_result.chart_path:
                    self._render_analysis_chart(realtime_result.chart_path, "实时分析图表")

                # 显示详细数据
                with st.expander("📊 详细数据"):
                    col1, col2 = st.columns(2)
                    with col1:
                        if realtime_result.price_24h_high:
                            st.write(f"**24h最高价**: ¥{realtime_result.price_24h_high:,.2f}")
                        if realtime_result.price_24h_low:
                            st.write(f"**24h最低价**: ¥{realtime_result.price_24h_low:,.2f}")
                        if realtime_result.volume_24h:
                            st.write(f"**24h成交量**: {realtime_result.volume_24h} 个")

                    with col2:
                        if realtime_result.trend_strength:
                            st.write(f"**趋势强度**: {realtime_result.trend_strength:.2f}")
                        if realtime_result.signal_confidence:
                            st.write(f"**信号置信度**: {realtime_result.signal_confidence:.2f}%")
                        if realtime_result.analysis_duration:
                            st.write(f"**分析耗时**: {realtime_result.analysis_duration:.1f} 秒")

                # 启动新分析按钮
                st.markdown("---")
                col1, col2 = st.columns([1, 3])
                with col1:
                    start_key = f"start_new_realtime_analysis_{item_id}{dialog_key_suffix}"
                    if st.button("🔄 重新分析", key=start_key, type="secondary"):
                        self._start_realtime_analysis(item_id, item_name, dialog_key_suffix)

                with col2:
                    st.caption("💡 点击重新分析获取最新的实时分析结果")

            else:
                # 没有分析结果，显示启动分析界面
                st.info("📝 暂无实时分析结果")
                st.markdown("🎯 **实时分析功能**：")
                st.markdown("""
                - 📈 **价格趋势分析** - 分析历史价格走势和技术指标
                - 📊 **成交量分析** - 检测异常成交量和市场活跃度
                - ⚠️ **支撑阻力位** - 识别关键价格支撑和阻力位
                - 🎯 **交易信号** - 生成买入/卖出建议
                - 📋 **风险评估** - 评估投资风险和收益预期
                """)

                st.warning("⏰ 实时分析过程需要3-5分钟，请耐心等待")

                # 启动分析按钮
                col1, col2 = st.columns([1, 2])
                with col1:
                    start_key = f"start_realtime_analysis_{item_id}{dialog_key_suffix}"
                    if st.button("🚀 开始实时分析", key=start_key, type="primary"):
                        self._start_realtime_analysis(item_id, item_name, dialog_key_suffix)

        except Exception as e:
            st.error(f"❌ 加载实时分析失败: {e}")
    
    def _render_regular_analysis_tab(self, item_id: str, item_name: str, dialog_key_suffix: str = ""):
        """渲染常规分析标签页（包含结果展示和启动分析功能）"""
        try:
            # 确保服务已初始化
            self._init_services()

            # 检查是否有正在进行的常规分析任务
            regular_task_key = f'regular_analysis_task_{item_id}{dialog_key_suffix}'

            if regular_task_key in st.session_state:
                # 显示分析进度
                self._render_analysis_progress(regular_task_key, item_id, "常规分析", dialog_key_suffix)
                return

            # 获取用于查询分析数据的ID（优先使用steamdt_item_id）
            analysis_item_id = self._get_analysis_item_id(item_id)

            # 获取最新的常规分析结果
            regular_result = st.session_state.analysis_dao.get_latest_analysis(analysis_item_id)

            if regular_result:
                # 显示现有分析结果和操作按钮
                col1, col2, col3 = st.columns([3, 1, 1])

                with col1:
                    st.success(f"✅ 最新常规分析结果")
                    st.caption(f"分析时间: {regular_result.created_at}")

                with col2:
                    # 重新分析按钮
                    if st.button("🔄 重新分析", key=f"reanalyze_{item_id}", help="重新运行分析"):
                        # 清除当前分析结果缓存
                        keys_to_clear = [
                            f"analysis_result_{item_id}",
                            f"trigger_analysis_{item_id}",
                            f"analysis_progress_{item_id}"
                        ]
                        for key in keys_to_clear:
                            if key in st.session_state:
                                del st.session_state[key]

                        # 启动新的分析任务
                        self._start_regular_analysis(item_id, item_name, dialog_key_suffix)
                        st.rerun()

                with col3:
                    # 关闭按钮
                    close_button_key = f"close_{item_id}{dialog_key_suffix}"
                    if st.button("❌ 关闭", key=close_button_key, help="关闭分析结果"):
                        # 清除分析结果显示状态（考虑dialog_key_suffix）
                        dialog_key = f"show_analysis_{item_id}{dialog_key_suffix}"
                        if dialog_key in st.session_state:
                            st.session_state[dialog_key] = False

                        # 清除相关缓存
                        keys_to_remove = [
                            f"analysis_result_{item_id}",
                            f"trigger_analysis_{item_id}",
                            f"analysis_progress_{item_id}"
                        ]
                        for key in keys_to_remove:
                            if key in st.session_state:
                                del st.session_state[key]

                        st.rerun()

                # 顶部指标展示已隐藏
                # col1, col2, col3 = st.columns(3)
                #
                # with col1:
                #     # 投资评分
                #     if regular_result.investment_score:
                #         score_color = "normal" if regular_result.investment_score >= 60 else "inverse"
                #         st.metric("投资评分", f"{regular_result.investment_score:.1f}/100",
                #                  delta_color=score_color)
                #
                #     # 交易信号
                #     if regular_result.trading_signal:
                #         signal_emoji = {"BUY": "🟢", "SELL": "🔴", "HOLD": "🟡", "WAIT": "⚪"}.get(regular_result.trading_signal, "⚪")
                #         st.metric("交易信号", f"{signal_emoji} {regular_result.trading_signal}")
                #
                # with col2:
                #     # 风险评估
                #     if regular_result.risk_level:
                #         risk_emoji = {"LOW": "🟢", "MEDIUM": "🟡", "HIGH": "🔴"}.get(regular_result.risk_level, "⚪")
                #         st.metric("风险等级", f"{risk_emoji} {regular_result.risk_level}")
                #
                #     if regular_result.confidence_level:
                #         st.metric("置信度", f"{regular_result.confidence_level:.1f}%")
                #
                # with col3:
                #     # 价格信息
                #     if regular_result.current_price:
                #         st.metric("分析时价格", f"¥{regular_result.current_price:,.2f}")
                #
                #     if regular_result.price_change_percent_24h:
                #         change_color = "normal" if regular_result.price_change_percent_24h >= 0 else "inverse"
                #         st.metric("24h涨跌", f"{regular_result.price_change_percent_24h:+.2f}%",
                #                  delta_color=change_color)

                # 显示技术分析报告（从report_path读取markdown文件）
                if regular_result.report_path:
                    self._render_markdown_report_from_file(regular_result.report_path)
                elif regular_result.user_report_content:
                    # 备用：如果没有report_path，显示用户报告内容
                    self._render_investment_analysis_report(regular_result.user_report_content)

                # 显示图表
                if regular_result.chart_path:
                    self._render_analysis_chart(regular_result.chart_path, "常规分析图表")

                # 显示技术指标
                with st.expander("📊 技术指标详情"):
                    col1, col2 = st.columns(2)
                    with col1:
                        if regular_result.rsi_value:
                            st.write(f"**RSI指标**: {regular_result.rsi_value:.2f}")
                        if regular_result.macd_value:
                            st.write(f"**MACD指标**: {regular_result.macd_value:.4f}")
                        if regular_result.support_level_1:
                            st.write(f"**支撑位1**: ¥{regular_result.support_level_1:,.2f}")
                        if regular_result.resistance_level_1:
                            st.write(f"**阻力位1**: ¥{regular_result.resistance_level_1:,.2f}")

                    with col2:
                        if regular_result.volatility_score:
                            st.write(f"**波动性评分**: {regular_result.volatility_score:.2f}")
                        if regular_result.liquidity_score:
                            st.write(f"**流动性评分**: {regular_result.liquidity_score:.2f}")
                        if regular_result.sentiment_score:
                            st.write(f"**市场情绪**: {regular_result.sentiment_score:.2f}")
                        if regular_result.analysis_duration:
                            st.write(f"**分析耗时**: {regular_result.analysis_duration:.1f} 秒")

                # 启动新分析按钮
                st.markdown("---")
                col1, col2 = st.columns([1, 3])
                with col1:
                    start_key = f"start_new_regular_analysis_{item_id}{dialog_key_suffix}"
                    if st.button("🔄 重新分析", key=start_key, type="secondary"):
                        self._start_regular_analysis(item_id, item_name, dialog_key_suffix)

                with col2:
                    st.caption("💡 点击重新分析获取最新的常规分析结果")

            else:
                # 没有分析结果，显示启动分析界面
                st.info("📝 暂无常规分析结果")
                st.markdown("🎯 **常规分析功能**：")
                st.markdown("""
                - 📈 **技术指标分析** - RSI、MACD、布林带等技术指标
                - 📊 **价格预测** - 基于历史数据的价格趋势预测
                - 💰 **投资评分** - 综合评估投资价值和风险
                - 🎯 **交易建议** - 明确的买入/卖出/持有建议
                - 📋 **详细报告** - 完整的投资分析报告
                """)

                st.warning("⏰ 常规分析过程需要5-10分钟，请耐心等待")

                # 启动分析按钮
                col1, col2 = st.columns([1, 2])
                with col1:
                    start_key = f"start_regular_analysis_{item_id}{dialog_key_suffix}"
                    if st.button("📊 开始常规分析", key=start_key, type="primary"):
                        self._start_regular_analysis(item_id, item_name, dialog_key_suffix)

        except Exception as e:
            st.error(f"❌ 加载常规分析失败: {e}")

    def _start_realtime_analysis(self, item_id: str, item_name: str, dialog_key_suffix: str = ""):
        """启动实时分析（现在统一调用常规分析接口）"""
        # 实时分析和常规分析现在统一使用同一个接口
        self._start_regular_analysis(item_id, item_name, dialog_key_suffix)

    def _start_regular_analysis(self, item_id: str, item_name: str, dialog_key_suffix: str = ""):
        """启动常规分析（直接调用API接口）"""
        # 确保服务已初始化
        self._init_services()

        with st.spinner("正在启动分析..."):
            # 直接调用API接口进行分析
            result = self._call_analysis_api(item_id, item_name)

        if result['success']:
            st.success(f"✅ 分析完成！")
            st.info(f"📊 分析结果已保存到数据库")
            time.sleep(1)
            st.rerun()
        else:
            st.error(f"❌ {result['message']}")

    def _call_analysis_api(self, item_id: str, item_name: str) -> Dict[str, Any]:
        """直接调用分析API接口"""
        try:
            # 获取饰品信息
            item_info = st.session_state.item_dao.get_by_item_id(item_id)
            if not item_info:
                return {
                    'success': False,
                    'message': '饰品信息不存在'
                }

            # 构造SteamDT URL
            market_hash_name = item_info.get('market_hash_name')
            if not market_hash_name:
                return {
                    'success': False,
                    'message': '饰品缺少市场哈希名称，无法进行分析'
                }

            import urllib.parse
            encoded_name = urllib.parse.quote(market_hash_name, safe='')
            item_url = f"https://steamdt.com/cs2/{encoded_name}"

            # 调用API接口
            import requests
            api_url = "http://localhost:8000/api/analysis/regular"

            payload = {
                "item_id": item_id,
                "item_name": item_name,
                "item_url": item_url
            }

            response = requests.post(api_url, json=payload, timeout=300)  # 5分钟超时

            if response.status_code == 200:
                return {
                    'success': True,
                    'message': '分析完成'
                }
            else:
                error_detail = response.json().get('detail', '未知错误') if response.headers.get('content-type', '').startswith('application/json') else response.text
                return {
                    'success': False,
                    'message': f'API调用失败: {error_detail}'
                }

        except requests.exceptions.Timeout:
            return {
                'success': False,
                'message': '分析超时，请稍后重试'
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'message': 'API服务不可用，请确保分析服务已启动'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'分析失败: {str(e)}'
            }



    def _render_analysis_progress(self, task_key: str, item_id: str, analysis_type: str, dialog_key_suffix: str = ""):
        """显示分析进度和状态"""
        # 确保服务已初始化
        self._init_services()

        task_info = st.session_state[task_key]
        task_id = task_info['task_id']
        start_time = task_info['start_time']
        elapsed_time = time.time() - start_time

        st.info(f"📋 任务ID: {task_id}")
        st.info(f"⏰ 已运行时间: {elapsed_time:.0f} 秒")

        # 获取任务状态
        status_result = st.session_state.holding_service.get_analysis_task_status(task_id)

        if status_result['success']:
            status = status_result['status']
            progress = status_result.get('progress', 0)

            if status == 'pending':
                st.warning("⏳ 任务排队中...")
                st.progress(0.1)
            elif status == 'running':
                st.info("🔄 分析进行中...")
                st.progress(min(0.8, progress / 100))
            elif status == 'success':
                st.success("✅ 分析完成！")
                st.progress(1.0)

                # 显示结果链接或摘要
                result_data = status_result.get('result')
                if result_data:
                    st.json(result_data)

                # 清除任务状态并刷新页面显示新结果
                view_key = f"view_new_result_{analysis_type}_{item_id}{dialog_key_suffix}"
                if st.button("🔄 查看新结果", key=view_key):
                    del st.session_state[task_key]
                    st.rerun()

            elif status == 'failed':
                st.error("❌ 分析失败")
                st.error(status_result.get('message', '未知错误'))

                # 清除任务状态
                retry_key = f"retry_{analysis_type}_analysis_{item_id}{dialog_key_suffix}"
                if st.button("🔄 重试", key=retry_key):
                    del st.session_state[task_key]
                    st.rerun()
            else:
                st.warning(f"⚠️ 未知状态: {status}")
        else:
            st.error(f"❌ 无法获取任务状态: {status_result['message']}")

        # 刷新按钮
        col1, col2 = st.columns([1, 2])
        with col1:
            refresh_key = f"refresh_{analysis_type}_status_{item_id}{dialog_key_suffix}"
            if st.button("🔄 刷新状态", key=refresh_key):
                st.rerun()

    def _render_analysis_chart(self, chart_path: str, chart_title: str):
        """显示分析图表"""
        try:
            import os
            from pathlib import Path

            # 检查图表文件是否存在
            if chart_path and os.path.exists(chart_path):
                st.markdown(f"#### 📈 {chart_title}")

                # 显示图表
                st.image(chart_path, caption=chart_title, use_container_width=True)

                # 显示图表信息
                chart_file = Path(chart_path)
                file_size = chart_file.stat().st_size / 1024  # KB
                st.caption(f"📁 文件大小: {file_size:.1f} KB | 📅 生成时间: {chart_file.name}")

            else:
                st.warning(f"⚠️ 图表文件不存在: {chart_path}")

        except Exception as e:
            st.error(f"❌ 显示图表失败: {e}")

    def _render_markdown_report_from_file(self, report_path: str):
        """从文件路径读取并渲染markdown报告"""
        try:
            import os
            from pathlib import Path

            # 检查文件是否存在
            if not report_path or not os.path.exists(report_path):
                st.warning(f"⚠️ 报告文件不存在: {report_path}")
                return

            # 读取markdown文件内容
            try:
                with open(report_path, 'r', encoding='utf-8') as f:
                    markdown_content = f.read()

                if markdown_content.strip():
                    st.markdown("#### 📋 技术分析报告")

                    # 解析markdown内容，按一级标题分割成tabs
                    self._render_markdown_as_tabs(markdown_content)

                    # 显示文件信息
                    report_file = Path(report_path)
                    file_size = report_file.stat().st_size / 1024  # KB
                    st.caption(f"📁 文件大小: {file_size:.1f} KB | 📅 生成时间: {report_file.name}")
                else:
                    st.warning("⚠️ 报告文件为空")

            except UnicodeDecodeError:
                # 尝试其他编码
                try:
                    with open(report_path, 'r', encoding='gbk') as f:
                        markdown_content = f.read()
                    st.markdown("#### 📋 技术分析报告")

                    # 解析markdown内容，按一级标题分割成tabs
                    self._render_markdown_as_tabs(markdown_content)
                except Exception as e:
                    st.error(f"❌ 读取报告文件失败（编码问题）: {e}")

        except Exception as e:
            st.error(f"❌ 渲染markdown报告失败: {e}")

    def _render_markdown_as_tabs(self, markdown_content: str):
        """将markdown内容按二级标题分割成tabs进行渲染"""
        try:
            import re

            # 按二级标题（## 开头）分割内容
            sections = re.split(r'^## (.+)$', markdown_content, flags=re.MULTILINE)

            # 第一个元素是标题前的内容（如果有的话）
            intro_content = sections[0].strip() if sections[0].strip() else None

            # 提取标题和对应的内容
            tab_data = []
            for i in range(1, len(sections), 2):
                if i + 1 < len(sections):
                    title = sections[i].strip()
                    content = sections[i + 1].strip()
                    if title and content:
                        # 清理标题中的emoji和格式符号，只保留核心文字
                        clean_title = self._clean_tab_title(title)
                        tab_data.append((clean_title, content))

            # 如果没有找到二级标题，直接显示原内容
            if not tab_data:
                st.markdown(markdown_content)
                return

            # 显示介绍内容（如果有）
            if intro_content:
                st.markdown(intro_content)
                st.markdown("---")

            # 创建tabs
            tab_titles = [title for title, _ in tab_data]
            tabs = st.tabs(tab_titles)

            # 在每个tab中显示对应的内容
            for i, (title, content) in enumerate(tab_data):
                with tabs[i]:
                    # 处理内容中的三级及以下标题，调整其级别
                    processed_content = self._process_markdown_headers_for_tabs(content)
                    st.markdown(processed_content)

        except Exception as e:
            st.error(f"❌ 解析markdown tabs失败: {e}")
            # 如果解析失败，回退到直接显示
            st.markdown(markdown_content)

    def _clean_tab_title(self, title: str) -> str:
        """清理tab标题，移除emoji和格式符号"""
        try:
            import re

            # 移除emoji和特殊符号
            title = re.sub(r'[📈🔍⚖️📋⚠️\*\*]', '', title)
            # 移除多余的空格
            title = re.sub(r'\s+', ' ', title).strip()
            # 移除开头的数字编号（如"一、"、"二、"等）
            title = re.sub(r'^[一二三四五六七八九十]+、\s*', '', title)

            return title

        except Exception as e:
            return title

    def _process_markdown_headers_for_tabs(self, content: str) -> str:
        """处理tab内容中的标题，调整级别"""
        try:
            import re

            # 将三级标题(###)转换为二级标题(##)，四级转三级，以此类推
            # 这样在tab内部显示时标题层级更合适
            lines = content.split('\n')
            processed_lines = []

            for line in lines:
                # 匹配标题行
                header_match = re.match(r'^(#{3,6})\s+(.+)$', line)
                if header_match:
                    current_level = len(header_match.group(1))
                    title_text = header_match.group(2)
                    # 减少一个级别（最少保持二级标题）
                    new_level = max(2, current_level - 1)
                    new_line = '#' * new_level + ' ' + title_text
                    processed_lines.append(new_line)
                else:
                    processed_lines.append(line)

            return '\n'.join(processed_lines)

        except Exception as e:
            # 如果处理失败，返回原内容
            return content

    def _render_investment_analysis_report(self, report_content: str):
        """渲染结构化的投资分析报告"""
        try:
            import json
            import ast

            # 尝试解析JSON格式的报告
            try:
                # 处理不同的数据格式
                report_data = None

                # 方法1: 直接JSON解析
                if isinstance(report_content, str) and report_content.strip().startswith('{'):
                    try:
                        report_data = json.loads(report_content)
                    except json.JSONDecodeError:
                        pass

                # 方法2: 如果是Python字典字符串格式（用单引号），使用ast.literal_eval
                if report_data is None and isinstance(report_content, str):
                    try:
                        # 处理Windows路径中的反斜杠问题
                        # 将单个反斜杠替换为双反斜杠，避免Unicode转义错误
                        safe_content = report_content.replace('\\', '\\\\')
                        report_data = ast.literal_eval(safe_content)
                    except (ValueError, SyntaxError, UnicodeDecodeError):
                        pass

                # 方法3: 尝试替换单引号为双引号后再解析JSON
                if report_data is None and isinstance(report_content, str):
                    try:
                        # 简单的单引号到双引号转换（可能不完美，但对大多数情况有效）
                        json_content = report_content.replace("'", '"')
                        report_data = json.loads(json_content)
                    except json.JSONDecodeError:
                        pass

                # 如果成功解析，显示结构化报告
                if report_data and isinstance(report_data, dict):
                    self._render_structured_report(report_data)
                else:
                    # 如果不是字典格式，按原来的方式显示
                    st.markdown("#### 📋 投资分析报告")
                    st.markdown(report_content)

            except Exception as e:
                # 解析失败，显示调试信息并降级显示
                st.warning(f"⚠️ 报告解析失败: {e}")
                st.markdown("#### 📋 投资分析报告")
                st.markdown("**原始内容：**")
                st.text(report_content[:1000] + "..." if len(report_content) > 1000 else report_content)

        except Exception as e:
            st.error(f"❌ 显示投资分析报告失败: {e}")
            # 降级显示原始内容
            st.markdown("#### 📋 投资分析报告")
            st.markdown(report_content)

    def _render_structured_report(self, report_data: dict):
        """渲染结构化的投资分析报告"""
        try:
            # 报告标题和生成时间
            st.markdown("#### 📋 投资分析报告")

            # 分隔线
            st.markdown("=" * 60)

            # 标题和生成时间
            if 'report_title' in report_data:
                st.markdown(f"**📋 {report_data['report_title']}**")
            if 'generation_time' in report_data:
                st.markdown(f"**📅 生成时间：{report_data['generation_time']}**")

            st.markdown("=" * 60)

            # 基本信息
            if 'basic_info' in report_data:
                self._render_basic_info_section(report_data['basic_info'])

            # 投资建议
            if 'investment_advice' in report_data:
                self._render_investment_advice_section(report_data['investment_advice'])

            # 风险评估
            if 'risk_summary' in report_data:
                self._render_risk_assessment_section(report_data['risk_summary'])

            # 市场情况分析
            if 'market_analysis' in report_data:
                self._render_market_analysis_section(report_data['market_analysis'])

            # 异常分析详情
            if 'anomaly_details' in report_data:
                self._render_anomaly_details_section(report_data['anomaly_details'])

            # 免责声明
            st.markdown("=" * 60)
            st.markdown("**📝 免责声明：投资有风险，入市需谨慎。本报告仅供参考，不构成投资建议。**")
            st.markdown("=" * 60)

        except Exception as e:
            st.error(f"❌ 渲染结构化报告失败: {e}")
            # 降级显示原始数据
            st.json(report_data)

    def _render_basic_info_section(self, basic_info: dict):
        """渲染基本信息部分"""
        st.markdown("### 📊 基本信息")

        # 基础信息
        if 'item_name' in basic_info:
            st.markdown(f"   **饰品名称：** {basic_info['item_name']}")
        if 'current_price' in basic_info:
            st.markdown(f"   **当前价格：** {basic_info['current_price']}")
        if 'price_trend' in basic_info:
            st.markdown(f"   **价格趋势：** {basic_info['price_trend']}")
        if 'market_activity' in basic_info:
            st.markdown(f"   **市场活跃度：** {basic_info['market_activity']}")

        # 技术指标
        if 'technical_indicators' in basic_info:
            st.markdown("   **关键技术指标：**")
            tech_indicators = basic_info['technical_indicators']

            for indicator_name, indicator_data in tech_indicators.items():
                if isinstance(indicator_data, dict):
                    if indicator_name == '支撑阻力位':
                        self._render_support_resistance(indicator_data)
                    else:
                        value = indicator_data.get('value', '')
                        evaluation = indicator_data.get('evaluation', '')
                        st.markdown(f"     • **{indicator_name}**: {value} - {evaluation}")

    def _render_support_resistance(self, sr_data: dict):
        """渲染支撑阻力位信息"""
        st.markdown("     • **支撑阻力位分析**:")

        if 'upper' in sr_data and 'lower' in sr_data:
            st.markdown(f"       - 上轨: {sr_data['upper']} / 下轨: {sr_data['lower']}")

        if 'position' in sr_data and 'evaluation' in sr_data:
            st.markdown(f"       - {sr_data['evaluation']}")

        if 'nearest_support' in sr_data:
            support = sr_data['nearest_support']
            if 'price' in support and 'distance_percent' in support:
                st.markdown(f"       - 最近支撑位: {support['price']}元 (距离{support['distance_percent']:.1f}%)")

        if 'nearest_resistance' in sr_data:
            resistance = sr_data['nearest_resistance']
            if 'price' in resistance and 'distance_percent' in resistance:
                st.markdown(f"       - 最近阻力位: {resistance['price']}元 (距离{resistance['distance_percent']:.1f}%)")

        if 'key_levels' in sr_data:
            levels = sr_data['key_levels']
            if 'short_term' in levels:
                short = levels['short_term']
                st.markdown(f"       - 短期关键位: 支撑{short.get('support', 'N/A')}元 / 阻力{short.get('resistance', 'N/A')}元")
            if 'long_term' in levels:
                long_term = levels['long_term']
                st.markdown(f"       - 长期关键位: 支撑{long_term.get('support', 'N/A')}元 / 阻力{long_term.get('resistance', 'N/A')}元")

        if 'evaluation' in sr_data:
            st.markdown(f"       - 位置评估: {sr_data['evaluation']}")

    def _render_investment_advice_section(self, investment_advice: dict):
        """渲染投资建议部分"""
        st.markdown("### 💡 投资建议")

        if 'main_advice' in investment_advice:
            st.markdown(f"   **主要建议：** {investment_advice['main_advice']}")
        if 'confidence_level' in investment_advice:
            st.markdown(f"   **置信度：** {investment_advice['confidence_level']}")
        if 'recommended_strategy' in investment_advice:
            st.markdown(f"   **推荐策略：** {investment_advice['recommended_strategy']}")

        if 'key_reasons' in investment_advice and investment_advice['key_reasons']:
            st.markdown("   **关键原因：**")
            for reason in investment_advice['key_reasons']:
                st.markdown(f"     • {reason}")

        if 'action_plan' in investment_advice and investment_advice['action_plan']:
            st.markdown("   **行动计划：**")
            for i, action in enumerate(investment_advice['action_plan'], 1):
                st.markdown(f"     {i}. {action}")

        if 'timeline_suggestion' in investment_advice:
            st.markdown(f"   **时间建议：** {investment_advice['timeline_suggestion']}")

    def _render_risk_assessment_section(self, risk_summary: dict):
        """渲染风险评估部分"""
        st.markdown("### ⚠️ 风险评估")

        if 'overall_risk_level' in risk_summary:
            st.markdown(f"   **风险等级：** {risk_summary['overall_risk_level']}")
        if 'risk_score' in risk_summary:
            st.markdown(f"   **风险评分：** {risk_summary['risk_score']}")
        if 'risk_score_explanation' in risk_summary:
            st.markdown(f"   **评分说明：** {risk_summary['risk_score_explanation']}")

        if 'main_risk_factors' in risk_summary and risk_summary['main_risk_factors']:
            st.markdown("   **主要风险：**")
            for risk in risk_summary['main_risk_factors']:
                st.markdown(f"     • {risk}")

        if 'risk_control_advice' in risk_summary and risk_summary['risk_control_advice']:
            st.markdown("   **风险控制建议：**")
            for advice in risk_summary['risk_control_advice']:
                st.markdown(f"     {advice}")

        if 'suitable_investors' in risk_summary:
            st.markdown(f"   **适合投资者：** {risk_summary['suitable_investors']}")

    def _render_market_analysis_section(self, market_analysis: dict):
        """渲染市场情况分析部分"""
        st.markdown("### 📈 市场情况分析")

        if 'supply_demand' in market_analysis:
            st.markdown(f"   **供需情况：** {market_analysis['supply_demand']}")
        if 'price_trend' in market_analysis:
            st.markdown(f"   **价格走势：** {market_analysis['price_trend']}")
        if 'market_sentiment' in market_analysis:
            st.markdown(f"   **市场情绪：** {market_analysis['market_sentiment']}")
        if 'market_anomalies' in market_analysis:
            st.markdown(f"   **异常情况：** {market_analysis['market_anomalies']}")
        if 'overall_assessment' in market_analysis:
            st.markdown(f"   **整体评估：** {market_analysis['overall_assessment']}")

    def _render_anomaly_details_section(self, anomaly_details: dict):
        """渲染异常分析详情部分"""
        st.markdown("### 🚨 异常分析详情")

        if 'anomaly_summary' in anomaly_details:
            st.markdown(f"   **异常总数：** {anomaly_details['anomaly_summary']}")

        if 'anomaly_types' in anomaly_details and anomaly_details['anomaly_types']:
            st.markdown("   **异常类型：**")
            for anomaly_type in anomaly_details['anomaly_types']:
                st.markdown(f"     • {anomaly_type}")

        if 'risk_impact' in anomaly_details:
            st.markdown(f"   **风险影响：** {anomaly_details['risk_impact']}")

        if 'recent_anomalies' in anomaly_details and anomaly_details['recent_anomalies']:
            st.markdown("   **最近异常：**")
            for anomaly in anomaly_details['recent_anomalies']:
                st.markdown(f"     • {anomaly}")

        if 'monitoring_advice' in anomaly_details:
            st.markdown(f"   **监控建议：** {anomaly_details['monitoring_advice']}")


# 创建全局组件实例
item_analysis_component = ItemAnalysisComponent()
