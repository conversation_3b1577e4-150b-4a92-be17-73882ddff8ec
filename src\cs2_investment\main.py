"""
CS2饰品投资分析系统主程序

提供命令行接口来执行各种操作。
"""

import click
from pathlib import Path

# 使用统一日志系统
from .utils.logger import get_logger
logger = get_logger(__name__)
from .dao.database_manager import DatabaseManager
from .services.data_import_service import DataImportService
from .services.screening_service import ScreeningService
from .services.report_service import ReportService
from .services.data_scraping_service import DataScrapingService


@click.group()
@click.option('--debug', is_flag=True, help='启用调试模式')
def cli(debug):
    """CS2饰品投资分析系统"""
    if debug:
        logger.info("调试模式已启用")


@cli.command()
def init_db():
    """初始化数据库"""
    click.echo("正在初始化数据库...")
    
    db_manager = DatabaseManager()
    
    if db_manager.setup_database():
        click.echo("✅ 数据库初始化成功")
    else:
        click.echo("❌ 数据库初始化失败")
        exit(1)


@cli.command()
@click.option('--file', '-f', type=click.Path(exists=True), help='指定JSON文件路径')
@click.option('--directory', '-d', type=click.Path(exists=True), help='指定数据目录路径')
@click.option('--source', '-s', help='指定数据源名称')
def import_data(file, directory, source):
    """导入数据"""
    import_service = DataImportService()
    
    if file:
        # 导入单个文件
        click.echo(f"正在导入文件: {file}")
        file_path = Path(file)
        result = import_service.import_file(file_path, source)
        
        click.echo(f"✅ 导入完成: 成功 {result['success_count']} 条, 失败 {result['error_count']} 条")
        
    elif directory:
        # 导入目录
        click.echo(f"正在导入目录: {directory}")
        dir_path = Path(directory)
        result = import_service.import_directory(dir_path)
        
        click.echo(f"✅ 导入完成: 成功 {result['success_files']} 个文件, 失败 {result['error_files']} 个文件")
        click.echo(f"   总计: 成功 {result['total_success_count']} 条记录, 失败 {result['total_error_count']} 条记录")
        
    else:
        # 导入默认数据目录
        click.echo("正在导入默认数据目录...")
        result = import_service.import_directory()
        
        click.echo(f"✅ 导入完成: 成功 {result['success_files']} 个文件, 失败 {result['error_files']} 个文件")
        click.echo(f"   总计: 成功 {result['total_success_count']} 条记录, 失败 {result['total_error_count']} 条记录")


@cli.command()
def db_info():
    """显示数据库信息"""
    db_manager = DatabaseManager()
    info = db_manager.get_database_info()
    
    click.echo("📊 数据库信息:")
    click.echo(f"   数据库大小: {info.get('database_size_mb', 0):.2f} MB")
    
    click.echo("\n📋 表记录统计:")
    tables = info.get('tables', {})
    for table_name, count in tables.items():
        click.echo(f"   {table_name}: {count:,} 条记录")


@cli.command()
@click.option('--types', '-t', multiple=True, help='指定投资类型（可多选）')
@click.option('--limit', '-l', default=50, help='每种类型的结果数量限制')
def run_screening(types, limit):
    """运行投资筛选"""
    screening_service = ScreeningService()

    # 显示可用的投资类型
    available_types = screening_service.get_available_investment_types()
    click.echo(f"📋 可用的投资类型: {', '.join(available_types)}")

    # 验证指定的类型
    investment_types = list(types) if types else None
    if investment_types:
        invalid_types = [t for t in investment_types if t not in available_types]
        if invalid_types:
            click.echo(f"❌ 无效的投资类型: {', '.join(invalid_types)}")
            return

    click.echo("🔍 开始运行投资筛选...")

    try:
        result = screening_service.run_screening(investment_types, limit)

        click.echo("✅ 筛选完成!")
        click.echo(f"   运行算法数: {result['algorithms_run']}")
        click.echo(f"   生成结果数: {result['total_results']}")
        click.echo(f"   耗时: {result['duration_seconds']:.2f} 秒")

        # 显示各类型结果统计
        click.echo("\n📊 各类型结果统计:")
        for investment_type, count in result['results_by_type'].items():
            click.echo(f"   {investment_type}: {count} 个结果")

    except Exception as e:
        click.echo(f"❌ 筛选失败: {e}")
        exit(1)


@cli.command()
@click.option('--type', '-t', help='投资类型过滤')
@click.option('--limit', '-l', default=20, help='显示数量限制')
@click.option('--risk', '-r', help='风险等级过滤 (LOW/MEDIUM/HIGH)')
@click.option('--recommendation', help='推荐等级过滤 (BUY/HOLD/SELL/AVOID)')
def show_results(type, limit, risk, recommendation):
    """显示筛选结果"""
    screening_service = ScreeningService()

    try:
        opportunities = screening_service.get_investment_opportunities(
            risk_level=risk,
            recommendation=recommendation,
            limit=limit
        )

        if not opportunities:
            click.echo("📭 没有找到符合条件的投资机会")
            return

        click.echo(f"🎯 找到 {len(opportunities)} 个投资机会:")
        click.echo()

        for i, opp in enumerate(opportunities, 1):
            click.echo(f"{i}. {opp['item_name']}")
            click.echo(f"   类型: {opp['investment_type']}")
            click.echo(f"   评分: {opp['score']:.1f} | 排名: #{opp['rank']} | 置信度: {opp['confidence']:.1f}%")
            click.echo(f"   价格: ¥{opp['current_price']} | 7天变化: {opp['price_change_7d']:.1f}%")
            click.echo(f"   风险: {opp['risk_level']} | 建议: {opp['recommendation']}")
            click.echo(f"   分析: {opp['analysis_summary']}")
            click.echo()

    except Exception as e:
        click.echo(f"❌ 获取筛选结果失败: {e}")


@cli.command()
def screening_summary():
    """显示筛选摘要"""
    screening_service = ScreeningService()

    try:
        summary = screening_service.get_screening_summary()

        click.echo("📊 筛选结果摘要 (最近7天):")
        click.echo()

        # 投资类型分布
        click.echo("🎯 投资类型分布:")
        for type_info in summary['type_distribution']:
            click.echo(f"   {type_info['investment_type']}: {type_info['count']} 个 "
                      f"(平均分: {type_info['avg_score']:.1f}, 最高分: {type_info['max_score']:.1f})")

        click.echo()

        # 风险分布
        click.echo("⚠️  风险等级分布:")
        for risk_info in summary['risk_distribution']:
            click.echo(f"   {risk_info['risk_level']}: {risk_info['count']} 个")

        click.echo()

        # 推荐分布
        click.echo("💡 推荐等级分布:")
        for rec_info in summary['recommendation_distribution']:
            click.echo(f"   {rec_info['recommendation']}: {rec_info['count']} 个")

    except Exception as e:
        click.echo(f"❌ 获取筛选摘要失败: {e}")


@cli.command()
@click.option('--format', '-f', default='html', type=click.Choice(['html', 'json', 'txt']), help='报告格式')
@click.option('--days', '-d', default=7, help='分析天数')
def generate_report(format, days):
    """生成投资报告"""
    report_service = ReportService()

    click.echo(f"📊 开始生成投资报告 ({format} 格式, {days} 天数据)...")

    try:
        result = report_service.generate_investment_report(format, days)

        click.echo("✅ 报告生成成功!")
        click.echo(f"   文件路径: {result['file_path']}")
        click.echo(f"   格式: {result['format']}")
        click.echo(f"   投资机会数: {result['total_opportunities']}")
        click.echo(f"   算法数: {result['algorithms_count']}")
        click.echo(f"   生成时间: {result['generated_at']}")

    except Exception as e:
        click.echo(f"❌ 报告生成失败: {e}")
        exit(1)


@cli.command()
@click.option('--rankings', '-r', help='指定要抓取的榜单类型，用逗号分隔')
@click.option('--list-rankings', '-l', is_flag=True, help='显示可用的榜单类型')
def scrape_data(rankings, list_rankings):
    """抓取SteamDT数据并直接保存到数据库"""
    import asyncio

    scraping_service = DataScrapingService()

    if list_rankings:
        click.echo("📋 可用的榜单类型:")
        available_rankings = scraping_service.get_available_rankings()
        for key, name in available_rankings.items():
            info = scraping_service.get_ranking_info(key)
            click.echo(f"  🔹 {key}: {name}")
            click.echo(f"     子选项: {', '.join(info['sub_options'])}")
            click.echo(f"     时间周期: {', '.join(info['time_periods'])}")
            click.echo(f"     预计数据量: {info['estimated_items']} 条")
            click.echo()
        return

    selected_rankings = None
    if rankings:
        selected_rankings = [r.strip() for r in rankings.split(',')]
        click.echo(f"📋 选定榜单: {', '.join(selected_rankings)}")
    else:
        click.echo("📋 抓取所有榜单")

    click.echo("🚀 开始数据抓取...")
    click.echo("💡 特性: 12小时智能检查，每榜单1000条数据")

    try:
        # 运行异步抓取
        results = asyncio.run(scraping_service.run_scraping(selected_rankings))

        click.echo("✅ 抓取完成!")
        click.echo(f"   总榜单: {results['total_rankings']}")
        click.echo(f"   成功: {results['successful_rankings']}")
        click.echo(f"   跳过: {results['skipped_rankings']}")
        click.echo(f"   失败: {results['failed_rankings']}")
        click.echo(f"   总数据: {results['total_data_count']:,} 条")

        if results.get('error'):
            click.echo(f"⚠️ 错误: {results['error']}")

    except KeyboardInterrupt:
        click.echo("⚠️ 用户中断抓取")
    except Exception as e:
        click.echo(f"❌ 抓取失败: {e}")
        exit(1)


def main():
    """主函数"""
    cli()


if __name__ == '__main__':
    main()
