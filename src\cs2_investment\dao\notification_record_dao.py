"""
通知记录数据访问对象

提供通知记录的数据库操作功能，包括创建、查询、更新、统计等。
支持通知历史分析和发送状态跟踪。
"""

from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy import and_, or_, desc, asc, func
from sqlalchemy.orm import Session

from .base_dao import BaseDAO
from ..models.notification_record import (
    NotificationRecord, NotificationTypeEnum, 
    NotificationPriorityEnum, NotificationStatusEnum
)
from ..database.database_manager import DatabaseManager


class NotificationRecordDAO(BaseDAO):
    """通知记录数据访问对象"""
    
    def __init__(self):
        super().__init__(NotificationRecord)
        self.db_manager = DatabaseManager()
    
    def create_notification_record(self, record_data: Dict[str, Any]) -> NotificationRecord:
        """
        创建通知记录
        
        Args:
            record_data: 通知记录数据字典
            
        Returns:
            NotificationRecord: 创建的通知记录
        """
        try:
            with self.db_manager.get_session() as session:
                notification_record = NotificationRecord(**record_data)
                session.add(notification_record)
                session.commit()
                session.refresh(notification_record)
                return notification_record
                
        except Exception as e:
            self.logger.error(f"创建通知记录失败: {e}")
            raise
    
    def get_user_notifications(self, user_id: str, limit: int = 50, 
                             notification_type: Optional[str] = None,
                             status: Optional[str] = None) -> List[NotificationRecord]:
        """
        获取用户通知记录
        
        Args:
            user_id: 用户ID
            limit: 限制数量
            notification_type: 通知类型过滤
            status: 状态过滤
            
        Returns:
            List[NotificationRecord]: 通知记录列表
        """
        try:
            with self.db_manager.get_session() as session:
                query = session.query(NotificationRecord).filter(
                    NotificationRecord.user_id == user_id
                )
                
                if notification_type:
                    query = query.filter(NotificationRecord.notification_type == notification_type)
                
                if status:
                    query = query.filter(NotificationRecord.status == status)
                
                records = query.order_by(desc(NotificationRecord.created_at)).limit(limit).all()
                return records
                
        except Exception as e:
            self.logger.error(f"获取用户通知记录失败: {e}")
            return []
    
    def get_pending_notifications(self, notification_type: Optional[str] = None) -> List[NotificationRecord]:
        """
        获取待发送的通知记录
        
        Args:
            notification_type: 通知类型过滤
            
        Returns:
            List[NotificationRecord]: 待发送的通知记录
        """
        try:
            with self.db_manager.get_session() as session:
                query = session.query(NotificationRecord).filter(
                    NotificationRecord.status == NotificationStatusEnum.PENDING
                )
                
                if notification_type:
                    query = query.filter(NotificationRecord.notification_type == notification_type)
                
                records = query.order_by(
                    desc(NotificationRecord.priority),
                    asc(NotificationRecord.created_at)
                ).all()
                
                return records
                
        except Exception as e:
            self.logger.error(f"获取待发送通知记录失败: {e}")
            return []
    
    def get_failed_notifications_for_retry(self) -> List[NotificationRecord]:
        """
        获取可重试的失败通知
        
        Returns:
            List[NotificationRecord]: 可重试的失败通知记录
        """
        try:
            with self.db_manager.get_session() as session:
                current_time = datetime.now()
                
                records = session.query(NotificationRecord).filter(
                    and_(
                        NotificationRecord.status == NotificationStatusEnum.FAILED,
                        NotificationRecord.retry_count < NotificationRecord.max_retries,
                        or_(
                            NotificationRecord.next_retry_at.is_(None),
                            NotificationRecord.next_retry_at <= current_time
                        )
                    )
                ).order_by(
                    desc(NotificationRecord.priority),
                    asc(NotificationRecord.failed_at)
                ).all()
                
                return records
                
        except Exception as e:
            self.logger.error(f"获取可重试失败通知失败: {e}")
            return []
    
    def update_notification_status(self, notification_id: int, status: str, 
                                 error_message: str = None) -> bool:
        """
        更新通知状态
        
        Args:
            notification_id: 通知记录ID
            status: 新状态
            error_message: 错误信息（如果是失败状态）
            
        Returns:
            bool: 是否成功
        """
        try:
            with self.db_manager.get_session() as session:
                notification = session.query(NotificationRecord).filter(
                    NotificationRecord.id == notification_id
                ).first()
                
                if notification:
                    if status == 'sent':
                        notification.mark_as_sent()
                    elif status == 'delivered':
                        notification.mark_as_delivered()
                    elif status == 'read':
                        notification.mark_as_read()
                    elif status == 'failed':
                        notification.mark_as_failed(error_message or "")
                    
                    session.commit()
                    return True
                
                return False
                
        except Exception as e:
            self.logger.error(f"更新通知状态失败: {e}")
            return False
    
    def get_notification_statistics(self, user_id: str = None, days: int = 7) -> Dict[str, Any]:
        """
        获取通知统计
        
        Args:
            user_id: 用户ID（可选）
            days: 统计天数
            
        Returns:
            Dict[str, Any]: 通知统计
        """
        try:
            with self.db_manager.get_session() as session:
                start_date = datetime.now() - timedelta(days=days)
                
                query = session.query(NotificationRecord).filter(
                    NotificationRecord.created_at >= start_date
                )
                
                if user_id:
                    query = query.filter(NotificationRecord.user_id == user_id)
                
                # 总体统计
                total_count = query.count()
                sent_count = query.filter(NotificationRecord.status == NotificationStatusEnum.SENT).count()
                delivered_count = query.filter(NotificationRecord.status == NotificationStatusEnum.DELIVERED).count()
                failed_count = query.filter(NotificationRecord.status == NotificationStatusEnum.FAILED).count()
                read_count = query.filter(NotificationRecord.status == NotificationStatusEnum.READ).count()
                
                # 按类型统计
                type_stats = {}
                for notification_type in NotificationTypeEnum:
                    type_query = query.filter(NotificationRecord.notification_type == notification_type)
                    type_total = type_query.count()
                    type_success = type_query.filter(
                        NotificationRecord.status.in_([
                            NotificationStatusEnum.SENT,
                            NotificationStatusEnum.DELIVERED,
                            NotificationStatusEnum.READ
                        ])
                    ).count()
                    
                    type_stats[notification_type.value] = {
                        'total': type_total,
                        'successful': type_success,
                        'success_rate': (type_success / type_total * 100) if type_total > 0 else 0
                    }
                
                # 按优先级统计
                priority_stats = {}
                for priority in NotificationPriorityEnum:
                    priority_count = query.filter(NotificationRecord.priority == priority).count()
                    priority_stats[priority.value] = priority_count
                
                # 按触发类型统计
                trigger_stats = session.query(
                    NotificationRecord.trigger_type,
                    func.count(NotificationRecord.id).label('count')
                ).filter(
                    NotificationRecord.created_at >= start_date
                ).group_by(NotificationRecord.trigger_type).all()
                
                trigger_type_stats = {trigger: count for trigger, count in trigger_stats if trigger}
                
                return {
                    'period_days': days,
                    'total_notifications': total_count,
                    'sent_notifications': sent_count,
                    'delivered_notifications': delivered_count,
                    'failed_notifications': failed_count,
                    'read_notifications': read_count,
                    'success_rate': ((sent_count + delivered_count + read_count) / total_count * 100) if total_count > 0 else 0,
                    'type_statistics': type_stats,
                    'priority_statistics': priority_stats,
                    'trigger_type_statistics': trigger_type_stats
                }
                
        except Exception as e:
            self.logger.error(f"获取通知统计失败: {e}")
            return {}
    
    def get_unread_system_messages(self, user_id: str) -> List[NotificationRecord]:
        """
        获取未读系统消息
        
        Args:
            user_id: 用户ID
            
        Returns:
            List[NotificationRecord]: 未读系统消息列表
        """
        try:
            with self.db_manager.get_session() as session:
                records = session.query(NotificationRecord).filter(
                    and_(
                        NotificationRecord.user_id == user_id,
                        NotificationRecord.notification_type == NotificationTypeEnum.SYSTEM_MESSAGE,
                        NotificationRecord.status != NotificationStatusEnum.READ
                    )
                ).order_by(desc(NotificationRecord.created_at)).all()
                
                return records
                
        except Exception as e:
            self.logger.error(f"获取未读系统消息失败: {e}")
            return []
    
    def mark_messages_as_read(self, user_id: str, message_ids: List[int] = None) -> int:
        """
        标记消息为已读
        
        Args:
            user_id: 用户ID
            message_ids: 消息ID列表（可选，为空则标记所有未读消息）
            
        Returns:
            int: 标记的消息数量
        """
        try:
            with self.db_manager.get_session() as session:
                query = session.query(NotificationRecord).filter(
                    and_(
                        NotificationRecord.user_id == user_id,
                        NotificationRecord.status != NotificationStatusEnum.READ
                    )
                )
                
                if message_ids:
                    query = query.filter(NotificationRecord.id.in_(message_ids))
                
                count = query.update({
                    'status': NotificationStatusEnum.READ,
                    'read_at': func.now()
                })
                
                session.commit()
                return count
                
        except Exception as e:
            self.logger.error(f"标记消息已读失败: {e}")
            return 0
    
    def cleanup_old_notifications(self, days: int = 30) -> int:
        """
        清理旧通知记录
        
        Args:
            days: 保留天数
            
        Returns:
            int: 删除的记录数
        """
        try:
            with self.db_manager.get_session() as session:
                cutoff_date = datetime.now() - timedelta(days=days)
                
                # 只删除已读或已发送的旧通知，保留失败的通知用于分析
                deleted_count = session.query(NotificationRecord).filter(
                    and_(
                        NotificationRecord.created_at < cutoff_date,
                        NotificationRecord.status.in_([
                            NotificationStatusEnum.READ,
                            NotificationStatusEnum.SENT,
                            NotificationStatusEnum.DELIVERED
                        ])
                    )
                ).delete()
                
                session.commit()
                self.logger.info(f"清理了 {deleted_count} 条旧通知记录")
                return deleted_count
                
        except Exception as e:
            self.logger.error(f"清理旧通知记录失败: {e}")
            return 0
