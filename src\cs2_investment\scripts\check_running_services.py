#!/usr/bin/env python3
"""
检查当前运行的价格更新服务

检查是否有多个价格更新服务同时运行，导致API请求频率过高
"""

import sys
import psutil
import threading
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.utils.logger import get_logger

logger = get_logger(__name__)


def check_python_processes():
    """检查Python进程"""
    logger.info("🔍 检查Python进程...")
    
    python_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = proc.info['cmdline']
                if cmdline and any('cs2_investment' in arg for arg in cmdline):
                    python_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': ' '.join(cmdline),
                        'create_time': datetime.fromtimestamp(proc.info['create_time']),
                        'process': proc
                    })
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    logger.info(f"找到 {len(python_processes)} 个相关Python进程:")
    
    for proc_info in python_processes:
        logger.info(f"  PID: {proc_info['pid']}")
        logger.info(f"  名称: {proc_info['name']}")
        logger.info(f"  启动时间: {proc_info['create_time']}")
        logger.info(f"  命令行: {proc_info['cmdline'][:100]}...")
        
        # 检查内存和CPU使用
        try:
            proc = proc_info['process']
            memory_info = proc.memory_info()
            cpu_percent = proc.cpu_percent()
            
            logger.info(f"  内存使用: {memory_info.rss / 1024 / 1024:.1f} MB")
            logger.info(f"  CPU使用: {cpu_percent:.1f}%")
            
            # 检查线程数
            num_threads = proc.num_threads()
            logger.info(f"  线程数: {num_threads}")
            
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            logger.info("  无法获取进程详细信息")
        
        logger.info("-" * 50)
    
    return python_processes


def check_current_threads():
    """检查当前进程的线程"""
    logger.info("🧵 检查当前进程的线程...")
    
    current_threads = threading.enumerate()
    
    logger.info(f"当前进程有 {len(current_threads)} 个线程:")
    
    for thread in current_threads:
        logger.info(f"  线程名: {thread.name}")
        logger.info(f"  是否存活: {thread.is_alive()}")
        logger.info(f"  是否守护线程: {thread.daemon}")
        
        # 检查是否是价格更新相关的线程
        if any(keyword in thread.name.lower() for keyword in ['price', 'update', 'scheduler', 'timer']):
            logger.warning(f"  ⚠️ 发现价格更新相关线程: {thread.name}")
        
        logger.info("-" * 30)


def check_api_key_usage():
    """检查API密钥使用情况"""
    logger.info("🔑 检查API密钥使用情况...")
    
    try:
        from src.cs2_investment.config.timer_config import get_timer_config
        
        config = get_timer_config()
        api_key = config.api.steamdt_api_key
        
        if api_key:
            logger.info(f"配置的API密钥: {api_key[:10]}...")
            
            # 检查SmartPriceUpdatePool的运行实例
            try:
                from src.cs2_investment.services.smart_price_update_pool import SmartPriceUpdatePool
                
                running_instances = SmartPriceUpdatePool.get_running_instances()
                logger.info(f"SmartPriceUpdatePool运行实例: {len(running_instances)}")
                
                for instance_id in running_instances:
                    logger.info(f"  实例ID: {instance_id}")
                
                if SmartPriceUpdatePool.is_any_instance_running():
                    logger.warning("⚠️ 发现SmartPriceUpdatePool实例在运行")
                
            except Exception as e:
                logger.error(f"检查SmartPriceUpdatePool失败: {e}")
        else:
            logger.warning("⚠️ 未配置API密钥")
            
    except Exception as e:
        logger.error(f"检查API密钥配置失败: {e}")


def check_scheduler_status():
    """检查调度器状态"""
    logger.info("⏰ 检查调度器状态...")
    
    try:
        # 检查IntegratedScheduler
        logger.info("检查IntegratedScheduler...")
        
        # 这里我们无法直接访问运行中的实例，但可以检查配置
        from src.cs2_investment.config.timer_config import get_timer_config
        
        config = get_timer_config()
        
        logger.info("调度器配置状态:")
        logger.info(f"  调度器启用: {config.scheduler.enabled}")
        logger.info(f"  简化价格更新启用: {config.simple_price_update.enabled}")
        logger.info(f"  价格更新启用: {config.price_update.enabled}")
        logger.info(f"  Steam监控启用: {config.steam_monitor.enabled}")
        
        # 检查间隔配置
        logger.info("价格更新间隔配置:")
        logger.info(f"  简化价格更新间隔: {config.simple_price_update.update_interval_minutes} 分钟")
        logger.info(f"  单个接口间隔: {config.simple_price_update.single_interval_seconds} 秒")
        logger.info(f"  批量大小: {config.simple_price_update.batch_size}")
        logger.info(f"  单个大小: {config.simple_price_update.single_size}")
        
    except Exception as e:
        logger.error(f"检查调度器状态失败: {e}")


def analyze_api_request_frequency():
    """分析API请求频率"""
    logger.info("📊 分析API请求频率...")
    
    # 基于配置计算理论请求频率
    try:
        from src.cs2_investment.config.timer_config import get_timer_config
        
        config = get_timer_config()
        
        # 简化价格更新器的请求频率
        single_interval = config.simple_price_update.single_interval_seconds
        single_size = config.simple_price_update.single_size
        update_interval = config.simple_price_update.update_interval_minutes
        
        logger.info("理论API请求频率分析:")
        logger.info(f"  单个接口间隔: {single_interval} 秒")
        logger.info(f"  每次处理饰品数: {single_size}")
        logger.info(f"  更新间隔: {update_interval} 分钟")
        
        # 计算每分钟的理论请求数
        requests_per_cycle = single_size
        cycle_time_seconds = single_size * single_interval
        requests_per_minute = (60 / cycle_time_seconds) * requests_per_cycle
        
        logger.info(f"  每个周期请求数: {requests_per_cycle}")
        logger.info(f"  每个周期耗时: {cycle_time_seconds} 秒")
        logger.info(f"  理论每分钟请求数: {requests_per_minute:.2f}")
        
        # SteamDT API限制
        steamdt_limit = 60  # 每分钟60次
        logger.info(f"  SteamDT API限制: {steamdt_limit} 次/分钟")
        
        if requests_per_minute > steamdt_limit:
            logger.warning(f"⚠️ 理论请求频率({requests_per_minute:.2f})超过API限制({steamdt_limit})")
        else:
            logger.info(f"✅ 理论请求频率({requests_per_minute:.2f})在API限制内")
        
    except Exception as e:
        logger.error(f"分析API请求频率失败: {e}")


def main():
    """主函数"""
    logger.info("🚀 开始检查运行的价格更新服务")
    
    try:
        # 1. 检查Python进程
        python_processes = check_python_processes()
        
        print("-" * 80)
        
        # 2. 检查当前线程
        check_current_threads()
        
        print("-" * 80)
        
        # 3. 检查API密钥使用
        check_api_key_usage()
        
        print("-" * 80)
        
        # 4. 检查调度器状态
        check_scheduler_status()
        
        print("-" * 80)
        
        # 5. 分析API请求频率
        analyze_api_request_frequency()
        
        print("-" * 80)
        
        # 总结
        logger.info("📋 检查总结:")
        logger.info(f"  发现Python进程: {len(python_processes)} 个")
        logger.info(f"  当前线程数: {len(threading.enumerate())} 个")
        
        # 建议
        if len(python_processes) > 1:
            logger.warning("⚠️ 发现多个Python进程，可能导致API请求冲突")
            logger.info("💡 建议: 确保只有一个价格更新服务在运行")
        
        logger.info("✅ 检查完成")
        
    except Exception as e:
        logger.error(f"❌ 检查过程中发生异常: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")


if __name__ == "__main__":
    main()
