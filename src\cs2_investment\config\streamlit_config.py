"""
Streamlit应用配置

从环境变量读取Streamlit应用相关配置
"""

import os
from typing import Optional

try:
    from pydantic_settings import BaseSettings
    from pydantic import Field, validator
except ImportError:
    # 兼容旧版本pydantic
    from pydantic import BaseSettings, Field, validator


class StreamlitSettings(BaseSettings):
    """Streamlit应用配置"""
    
    # 汇率配置
    real_exchange_rate: float = Field(default=7.21, alias="REAL_EXCHANGE_RATE")
    
    # 页面配置
    page_title: str = Field(default="CS2投资助手", alias="STREAMLIT_PAGE_TITLE")
    page_icon: str = Field(default="🎯", alias="STREAMLIT_PAGE_ICON")
    layout: str = Field(default="wide", alias="STREAMLIT_LAYOUT")
    
    # 数据显示配置
    default_items_per_page: int = Field(default=50, alias="STREAMLIT_ITEMS_PER_PAGE")
    max_query_limit: int = Field(default=5000, alias="STREAMLIT_MAX_QUERY_LIMIT")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"
    
    @validator('real_exchange_rate')
    def validate_exchange_rate(cls, v):
        """验证汇率"""
        if v <= 0:
            raise ValueError("real_exchange_rate must be greater than 0")
        if v > 20:  # 合理的汇率上限
            raise ValueError("real_exchange_rate seems too high, please check")
        return v
    
    @validator('default_items_per_page', 'max_query_limit')
    def validate_positive_int(cls, v):
        """验证正整数"""
        if v <= 0:
            raise ValueError("value must be greater than 0")
        return v


# 全局配置实例
_streamlit_config = None


def get_streamlit_config() -> StreamlitSettings:
    """获取Streamlit配置实例"""
    global _streamlit_config
    if _streamlit_config is None:
        _streamlit_config = StreamlitSettings()
    return _streamlit_config


def create_streamlit_env_template() -> str:
    """创建Streamlit环境变量模板"""
    return """
# ===== Streamlit应用配置 =====
# 真实汇率（用于搬砖卡价计算）
REAL_EXCHANGE_RATE=7.21

# 页面配置
STREAMLIT_PAGE_TITLE=CS2投资助手
STREAMLIT_PAGE_ICON=🎯
STREAMLIT_LAYOUT=wide

# 数据显示配置
STREAMLIT_ITEMS_PER_PAGE=50
STREAMLIT_MAX_QUERY_LIMIT=5000
"""


if __name__ == "__main__":
    # 测试配置
    config = get_streamlit_config()
    print("Streamlit配置:")
    print(f"  真实汇率: {config.real_exchange_rate}")
    print(f"  页面标题: {config.page_title}")
    print(f"  页面图标: {config.page_icon}")
    print(f"  布局: {config.layout}")
    print(f"  每页显示: {config.default_items_per_page}")
    print(f"  最大查询限制: {config.max_query_limit}")
    
    print("\n环境变量模板:")
    print(create_streamlit_env_template())
