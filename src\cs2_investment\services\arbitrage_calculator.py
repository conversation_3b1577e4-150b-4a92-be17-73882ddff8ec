"""
搬砖卡价比例计算器

计算BUFF和YOUPIN平台相对于Steam的价格比例
"""

from decimal import Decimal, ROUND_HALF_UP
from typing import Optional, Dict, Any
from datetime import datetime

from ..utils.logger import get_logger
from ..config.database import get_db_session
from ..models.item import Item
from ..models.platform_price import PlatformPrice

logger = get_logger(__name__)


class ArbitrageCalculator:
    """搬砖卡价比例计算器"""
    
    def __init__(self):
        self.logger = logger
        
        # 平台映射
        self.platform_mapping = {
            'BUFF': 'buff',
            'YOUPIN': 'youpin', 
            'STEAM': 'steam'
        }
    
    def calculate_arbitrage_ratio(self, item_id: str) -> Optional[Decimal]:
        """
        计算搬砖卡价比例
        
        Args:
            item_id: 饰品ID
            
        Returns:
            搬砖卡价比例 (最低价/Steam价格)，如果无法计算则返回None
        """
        try:
            with get_db_session() as session:
                # 获取BUFF、YOUPIN、STEAM的最新价格
                prices = self._get_platform_prices(session, item_id)
                
                if not prices:
                    self.logger.debug(f"未找到价格数据: {item_id}")
                    return None
                
                # 获取各平台价格
                buff_price = prices.get('buff')
                youpin_price = prices.get('youpin')
                steam_price = prices.get('steam')

                # 详细日志记录价格情况
                self.logger.debug(f"价格数据 {item_id}: BUFF={buff_price}, YOUPIN={youpin_price}, STEAM={steam_price}")

                # 检查Steam价格是否存在且大于0
                if not steam_price or steam_price <= 0:
                    self.logger.debug(f"Steam价格无效，跳过计算: {item_id}, steam_price={steam_price}")
                    return None

                # 验证BUFF价格有效性
                valid_buff_price = None
                if buff_price is not None and buff_price > 0:
                    valid_buff_price = buff_price
                    self.logger.debug(f"BUFF价格有效: {item_id}, price={buff_price}")
                else:
                    self.logger.debug(f"BUFF价格无效: {item_id}, price={buff_price}")

                # 验证YOUPIN价格有效性
                valid_youpin_price = None
                if youpin_price is not None and youpin_price > 0:
                    valid_youpin_price = youpin_price
                    self.logger.debug(f"YOUPIN价格有效: {item_id}, price={youpin_price}")
                else:
                    self.logger.debug(f"YOUPIN价格无效: {item_id}, price={youpin_price}")

                # 获取BUFF和YOUPIN中的最低价格
                min_price = None
                min_price_source = None

                if valid_buff_price is not None and valid_youpin_price is not None:
                    # 两个平台都有有效价格，选择最低的
                    if valid_buff_price <= valid_youpin_price:
                        min_price = valid_buff_price
                        min_price_source = "BUFF"
                    else:
                        min_price = valid_youpin_price
                        min_price_source = "YOUPIN"
                elif valid_buff_price is not None:
                    # 只有BUFF有有效价格
                    min_price = valid_buff_price
                    min_price_source = "BUFF"
                elif valid_youpin_price is not None:
                    # 只有YOUPIN有有效价格
                    min_price = valid_youpin_price
                    min_price_source = "YOUPIN"

                # 如果BUFF和YOUPIN都没有有效价格，则不计算
                if min_price is None:
                    self.logger.debug(f"BUFF和YOUPIN价格都无效，跳过计算: {item_id}")
                    return None
                
                # 计算比例: 最低价 / Steam价格
                ratio = Decimal(str(min_price)) / Decimal(str(steam_price))

                # 保留6位小数
                ratio = ratio.quantize(Decimal('0.000001'), rounding=ROUND_HALF_UP)

                self.logger.debug(f"计算搬砖比例成功: {item_id}, 最低价={min_price}({min_price_source}), Steam价格={steam_price}, 比例={ratio}")

                return ratio
                
        except Exception as e:
            self.logger.error(f"计算搬砖比例失败: {item_id} - {e}")
            return None
    
    def _get_platform_prices(self, session, item_id: str) -> Dict[str, Optional[Decimal]]:
        """
        获取各平台的最新价格
        
        Args:
            session: 数据库会话
            item_id: 饰品ID
            
        Returns:
            各平台价格字典
        """
        prices = {
            'buff': None,
            'youpin': None,
            'steam': None
        }
        
        try:
            # 查询各平台的最新价格
            for platform_name, platform_key in self.platform_mapping.items():
                price_record = session.query(PlatformPrice).filter(
                    PlatformPrice.item_id == item_id,
                    PlatformPrice.platform_name.ilike(f'%{platform_name}%'),
                    PlatformPrice.is_active == True
                ).order_by(PlatformPrice.update_time.desc()).first()
                
                if price_record:
                    # 优先使用sell_price，如果没有则使用price
                    price_value = price_record.sell_price or price_record.price

                    # 确保价格值有效（不为None且大于0）
                    if price_value is not None and price_value > 0:
                        try:
                            prices[platform_key] = Decimal(str(price_value))
                            self.logger.debug(f"获取{platform_name}有效价格: {item_id} = {price_value}")
                        except (ValueError, TypeError) as e:
                            self.logger.warning(f"价格转换失败: {item_id}, {platform_name}={price_value}, 错误={e}")
                    else:
                        self.logger.debug(f"{platform_name}价格无效: {item_id} = {price_value}")
                else:
                    self.logger.debug(f"未找到{platform_name}价格记录: {item_id}")
            
            return prices
            
        except Exception as e:
            self.logger.error(f"获取平台价格失败: {item_id} - {e}")
            return prices
    
    def update_item_arbitrage_ratio(self, item_id: str) -> bool:
        """
        更新饰品的搬砖卡价比例
        
        Args:
            item_id: 饰品ID
            
        Returns:
            是否更新成功
        """
        try:
            # 计算搬砖比例
            ratio = self.calculate_arbitrage_ratio(item_id)
            
            if ratio is None:
                self.logger.debug(f"无法计算搬砖比例，跳过更新: {item_id}")
                return False
            
            # 更新到数据库
            with get_db_session() as session:
                item = session.query(Item).filter(Item.item_id == item_id).first()
                
                if not item:
                    self.logger.warning(f"未找到饰品: {item_id}")
                    return False
                
                # 更新搬砖比例
                item.arbitrage_ratio = ratio
                session.commit()
                
                self.logger.debug(f"更新搬砖比例成功: {item_id} = {ratio}")
                return True
                
        except Exception as e:
            self.logger.error(f"更新搬砖比例失败: {item_id} - {e}")
            return False
    
    def batch_update_arbitrage_ratios(self, item_ids: list) -> Dict[str, int]:
        """
        批量更新搬砖卡价比例
        
        Args:
            item_ids: 饰品ID列表
            
        Returns:
            更新统计信息
        """
        stats = {
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        self.logger.info(f"开始批量更新搬砖比例: {len(item_ids)}个饰品")
        
        for item_id in item_ids:
            try:
                if self.update_item_arbitrage_ratio(item_id):
                    stats['success'] += 1
                else:
                    stats['skipped'] += 1
                    
            except Exception as e:
                self.logger.error(f"批量更新失败: {item_id} - {e}")
                stats['failed'] += 1
        
        self.logger.info(f"批量更新完成: 成功{stats['success']}, 跳过{stats['skipped']}, 失败{stats['failed']}")
        
        return stats
