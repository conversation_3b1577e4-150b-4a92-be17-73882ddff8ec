"""
SteamDT抓取器使用示例

演示如何使用抓取器获取饰品数据
"""

import asyncio
from pathlib import Path

from .steamdt_scraper import SteamDTScraper
from .data_storage import DataStorage

# 使用统一日志组件
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)


async def scrape_single_item_example():
    """单个饰品抓取示例"""
    # 饰品URL
    item_url = "https://steamdt.com/cs2/Desert%20Eagle%20%7C%20Printstream%20(Field-Tested)"
    
    # 创建抓取器和存储器
    storage = DataStorage()
    
    async with SteamDTScraper() as scraper:
        logger.info("开始抓取单个饰品数据")
        
        # 抓取数据
        result = await scraper.scrape_item_data(item_url)
        
        if result.success:
            logger.info("抓取成功！")
            
            # 打印基本信息
            if result.item_info:
                print(f"饰品名称: {result.item_info.name}")
                print(f"当前价格: ¥{result.item_info.current_price}")
                print(f"价格变化: {result.item_info.price_change_percent}%")
            
            # 打印数据统计
            if result.trend_data:
                print(f"走势数据点数: {len(result.trend_data.data_points)}")
            
            if result.hourly_kline:
                print(f"时K数据点数: {len(result.hourly_kline.data_points)}")
            
            if result.daily_kline:
                print(f"日K数据点数: {len(result.daily_kline.data_points)}")
            
            if result.weekly_kline:
                print(f"周K数据点数: {len(result.weekly_kline.data_points)}")
            
            # 保存数据
            storage.save_scraping_result(result)
            logger.info("数据已保存到文件")
            
        else:
            logger.error(f"抓取失败: {result.error_message}")


async def scrape_multiple_items_example():
    """多个饰品批量抓取示例"""
    # 饰品URL列表
    item_urls = [
        "https://steamdt.com/cs2/Desert%20Eagle%20%7C%20Printstream%20(Field-Tested)",
        "https://steamdt.com/cs2/AK-47%20%7C%20Redline%20(Field-Tested)",
        "https://steamdt.com/cs2/AWP%20%7C%20Dragon%20Lore%20(Field-Tested)"
    ]
    
    # 创建抓取器和存储器
    storage = DataStorage()
    
    async with SteamDTScraper() as scraper:
        logger.info(f"开始批量抓取 {len(item_urls)} 个饰品")
        
        # 批量抓取
        results = await scraper.scrape_multiple_items(item_urls)
        
        # 统计结果
        success_count = sum(1 for r in results if r.success)
        logger.info(f"批量抓取完成: {success_count}/{len(results)} 成功")
        
        # 保存所有结果
        storage.save_batch_results(results)
        
        # 打印详细结果
        for i, result in enumerate(results, 1):
            print(f"\n=== 饰品 {i} ===")
            if result.success and result.item_info:
                print(f"名称: {result.item_info.name}")
                print(f"价格: ¥{result.item_info.current_price}")
                print(f"数据点数: 走势({len(result.trend_data.data_points) if result.trend_data else 0}), "
                      f"时K({len(result.hourly_kline.data_points) if result.hourly_kline else 0}), "
                      f"日K({len(result.daily_kline.data_points) if result.daily_kline else 0}), "
                      f"周K({len(result.weekly_kline.data_points) if result.weekly_kline else 0})")
            else:
                print(f"抓取失败: {result.error_message}")


async def test_data_apis():
    """测试数据API连接"""
    async with SteamDTScraper() as scraper:
        # 测试页面访问
        test_url = "https://steamdt.com/cs2/Desert%20Eagle%20%7C%20Printstream%20(Field-Tested)"
        
        logger.info("测试页面访问...")
        item_info = await scraper.get_item_info_from_page(test_url)
        
        if item_info:
            print(f"页面访问成功: {item_info.name}")
        else:
            print("页面访问失败")
        
        # 测试获取item_id
        logger.info("测试获取饰品ID...")
        item_id = await scraper.get_item_id_from_network(test_url)
        
        if item_id:
            print(f"饰品ID获取成功: {item_id}")
            
            # 测试各种数据API
            logger.info("测试走势数据API...")
            trend_data = await scraper.fetch_trend_data(item_id)
            if trend_data:
                print(f"走势数据获取成功: {len(trend_data.data_points)} 个数据点")
            
            logger.info("测试时K数据API...")
            hourly_kline = await scraper.fetch_kline_data(item_id, 'hourly')
            if hourly_kline:
                print(f"时K数据获取成功: {len(hourly_kline.data_points)} 个数据点")
            
            logger.info("测试日K数据API...")
            daily_kline = await scraper.fetch_kline_data(item_id, 'daily')
            if daily_kline:
                print(f"日K数据获取成功: {len(daily_kline.data_points)} 个数据点")
            
            logger.info("测试周K数据API...")
            weekly_kline = await scraper.fetch_kline_data(item_id, 'weekly')
            if weekly_kline:
                print(f"周K数据获取成功: {len(weekly_kline.data_points)} 个数据点")
        else:
            print("饰品ID获取失败")


def main():
    """主函数"""
    print("SteamDT数据抓取器示例")
    print("1. 单个饰品抓取")
    print("2. 批量饰品抓取")
    print("3. API连接测试")
    
    choice = input("请选择操作 (1-3): ").strip()
    
    if choice == '1':
        asyncio.run(scrape_single_item_example())
    elif choice == '2':
        asyncio.run(scrape_multiple_items_example())
    elif choice == '3':
        asyncio.run(test_data_apis())
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
