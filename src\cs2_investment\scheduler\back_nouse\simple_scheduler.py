#!/usr/bin/env python3
"""
简化版定时任务调度器

使用硬编码的测试数据，避免数据库会话问题
"""

import asyncio
import random
import sys
from datetime import datetime, timedelta
from typing import List, Dict, Any
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.fx.integrated_analysis_system import IntegratedAnalysisSystem
from src.cs2_investment.fx.integrated_ssync_system import IntegratedSSyncSystem

# 使用统一日志组件
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)


class SimpleTaskScheduler:
    """简化版定时任务调度器"""
    
    def __init__(self):
        """初始化调度器"""
        self.is_running = False
        self.regular_analysis_task = None
        self.realtime_monitor_task = None
        
        # 任务配置
        self.regular_interval_hours = 24  # 常规分析每24小时执行一次
        self.realtime_interval_hours = 1  # 实时监控每1小时执行一次
        self.item_delay_seconds = (15, 30)  # 饰品之间间隔15-30秒
        
        # 系统实例
        self.regular_system = IntegratedAnalysisSystem()
        self.realtime_system = IntegratedSSyncSystem()
        
        # 硬编码的测试数据
        self.test_items = [
            {
                'item_id': 'AK-47%20%7C%20Inheritance%20(Field-Tested)',
                'name': 'AK-47 传承 (久经沙场)',
                'url': 'https://steamdt.com/cs2/AK-47%20%7C%20Inheritance%20(Field-Tested)',
                'item_type': '步枪',
                'quality': 'Field-Tested',
                'rarity': 'Covert'
            },
            {
                'item_id': 'Desert%20Eagle%20%7C%20Printstream%20(Field-Tested)',
                'name': '沙漠之鹰 印花集 (久经沙场)',
                'url': 'https://steamdt.com/cs2/Desert%20Eagle%20%7C%20Printstream%20(Field-Tested)',
                'item_type': '手枪',
                'quality': 'Field-Tested',
                'rarity': 'Covert'
            },
            {
                'item_id': 'M4A1-S%20%7C%20Printstream%20(Field-Tested)',
                'name': 'M4A1 消音型印花集 (久经沙场)',
                'url': 'https://steamdt.com/cs2/M4A1-S%20%7C%20Printstream%20(Field-Tested)',
                'item_type': '步枪',
                'quality': 'Field-Tested',
                'rarity': 'Covert'
            }
        ]
        
        self.test_favorites = [
            {
                'id': 1,
                'item_id': 'Desert%20Eagle%20%7C%20Printstream%20(Field-Tested)',
                'name': '沙漠之鹰 印花集 (久经沙场)',
                'url': 'https://steamdt.com/cs2/Desert%20Eagle%20%7C%20Printstream%20(Field-Tested)',
                'user_id': 'test_user',
                'created_at': datetime.now()
            },
            {
                'id': 2,
                'item_id': 'AK-47%20%7C%20Inheritance%20(Field-Tested)',
                'name': 'AK-47 传承 (久经沙场)',
                'url': 'https://steamdt.com/cs2/AK-47%20%7C%20Inheritance%20(Field-Tested)',
                'user_id': 'test_user',
                'created_at': datetime.now()
            }
        ]
    
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已在运行中")
            return
        
        self.is_running = True
        logger.info("🚀 启动简化版定时任务调度器")
        
        # 启动常规分析任务
        self.regular_analysis_task = asyncio.create_task(
            self._regular_analysis_loop()
        )
        
        # 启动实时监控任务
        self.realtime_monitor_task = asyncio.create_task(
            self._realtime_monitor_loop()
        )
        
        logger.info("✅ 定时任务调度器启动完成")
        
        # 等待任务完成（通常不会完成，除非手动停止）
        try:
            await asyncio.gather(
                self.regular_analysis_task,
                self.realtime_monitor_task
            )
        except asyncio.CancelledError:
            logger.info("定时任务被取消")
    
    async def stop(self):
        """停止调度器"""
        if not self.is_running:
            logger.warning("调度器未在运行")
            return
        
        logger.info("🛑 停止定时任务调度器")
        self.is_running = False
        
        # 取消任务
        if self.regular_analysis_task:
            self.regular_analysis_task.cancel()
        
        if self.realtime_monitor_task:
            self.realtime_monitor_task.cancel()
        
        # 等待任务完全停止
        try:
            if self.regular_analysis_task:
                await self.regular_analysis_task
        except asyncio.CancelledError:
            pass
        
        try:
            if self.realtime_monitor_task:
                await self.realtime_monitor_task
        except asyncio.CancelledError:
            pass
        
        logger.info("✅ 定时任务调度器已停止")
    
    async def _regular_analysis_loop(self):
        """常规分析循环任务"""
        logger.info("📊 启动常规分析定时任务")
        
        while self.is_running:
            try:
                items = self.test_items
                
                if not items:
                    logger.warning("没有找到需要分析的饰品")
                    await asyncio.sleep(3600)  # 1小时后重试
                    continue
                
                logger.info(f"📋 开始常规分析，共 {len(items)} 个饰品")
                
                # 逐个分析饰品
                for i, item in enumerate(items, 1):
                    if not self.is_running:
                        break
                    
                    await self._run_regular_analysis(item, i, len(items))
                    
                    # 饰品之间的随机间隔
                    if i < len(items):  # 不是最后一个饰品
                        delay = random.randint(*self.item_delay_seconds)
                        logger.info(f"⏳ 等待 {delay} 秒后分析下一个饰品...")
                        await asyncio.sleep(delay)
                
                logger.info("✅ 本轮常规分析完成")
                
                # 等待下一轮分析
                next_run = datetime.now() + timedelta(hours=self.regular_interval_hours)
                logger.info(f"⏰ 下次常规分析时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
                await asyncio.sleep(self.regular_interval_hours * 3600)
                
            except asyncio.CancelledError:
                logger.info("常规分析任务被取消")
                break
            except Exception as e:
                logger.error(f"常规分析循环出错: {e}")
                await asyncio.sleep(300)  # 5分钟后重试
    
    async def _realtime_monitor_loop(self):
        """实时监控循环任务"""
        logger.info("📈 启动实时监控定时任务")
        
        while self.is_running:
            try:
                favorites = self.test_favorites
                
                if not favorites:
                    logger.warning("没有找到需要监控的收藏饰品")
                    await asyncio.sleep(3600)  # 1小时后重试
                    continue
                
                logger.info(f"📋 开始实时监控，共 {len(favorites)} 个收藏饰品")
                
                # 逐个监控饰品
                for i, favorite in enumerate(favorites, 1):
                    if not self.is_running:
                        break
                    
                    await self._run_realtime_monitor(favorite, i, len(favorites))
                    
                    # 饰品之间的随机间隔
                    if i < len(favorites):  # 不是最后一个饰品
                        delay = random.randint(*self.item_delay_seconds)
                        logger.info(f"⏳ 等待 {delay} 秒后监控下一个饰品...")
                        await asyncio.sleep(delay)
                
                logger.info("✅ 本轮实时监控完成")
                
                # 等待下一轮监控
                next_run = datetime.now() + timedelta(hours=self.realtime_interval_hours)
                logger.info(f"⏰ 下次实时监控时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
                await asyncio.sleep(self.realtime_interval_hours * 3600)
                
            except asyncio.CancelledError:
                logger.info("实时监控任务被取消")
                break
            except Exception as e:
                logger.error(f"实时监控循环出错: {e}")
                await asyncio.sleep(300)  # 5分钟后重试
    
    async def _run_regular_analysis(self, item: Dict[str, Any], current: int, total: int):
        """运行单个饰品的常规分析"""
        start_time = datetime.now()
        
        try:
            logger.info(f"📊 [{current}/{total}] 开始常规分析: {item['name']}")
            
            # 运行常规分析
            result = await self.regular_system.run_complete_analysis(item['url'], item['name'])
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            if result['success']:
                logger.info(f"✅ [{current}/{total}] 常规分析成功: {item['name']} (耗时: {duration:.1f}秒)")
            else:
                error_msg = result.get('error', '未知错误')
                logger.error(f"❌ [{current}/{total}] 常规分析失败: {item['name']} - {error_msg}")
        
        except Exception as e:
            end_time = datetime.now()
            error_msg = str(e)
            logger.error(f"❌ [{current}/{total}] 常规分析异常: {item['name']} - {error_msg}")
    
    async def _run_realtime_monitor(self, favorite: Dict[str, Any], current: int, total: int):
        """运行单个饰品的实时监控"""
        start_time = datetime.now()
        
        try:
            logger.info(f"📈 [{current}/{total}] 开始实时监控: {favorite['name']}")
            
            # 运行实时监控
            result = await self.realtime_system.run_complete_analysis(favorite['url'], favorite['name'])
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            if result['success']:
                logger.info(f"✅ [{current}/{total}] 实时监控成功: {favorite['name']} (耗时: {duration:.1f}秒)")
            else:
                error_msg = result.get('error', '未知错误')
                logger.error(f"❌ [{current}/{total}] 实时监控失败: {favorite['name']} - {error_msg}")
        
        except Exception as e:
            end_time = datetime.now()
            error_msg = str(e)
            logger.error(f"❌ [{current}/{total}] 实时监控异常: {favorite['name']} - {error_msg}")
    
    def get_status(self) -> dict:
        """获取调度器状态"""
        return {
            'is_running': self.is_running,
            'regular_analysis_running': self.regular_analysis_task and not self.regular_analysis_task.done(),
            'realtime_monitor_running': self.realtime_monitor_task and not self.realtime_monitor_task.done(),
            'regular_interval_hours': self.regular_interval_hours,
            'realtime_interval_hours': self.realtime_interval_hours,
            'item_delay_seconds': self.item_delay_seconds,
            'test_items_count': len(self.test_items),
            'test_favorites_count': len(self.test_favorites)
        }


# 全局调度器实例
simple_scheduler = SimpleTaskScheduler()


async def main():
    """主函数 - 用于测试"""
    # 日志已通过统一组件配置
    
    try:
        # 启动调度器
        await simple_scheduler.start()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止...")
        await simple_scheduler.stop()


if __name__ == "__main__":
    asyncio.run(main())
