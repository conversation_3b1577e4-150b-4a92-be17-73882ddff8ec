"""
战术分析器

基于日K数据进行中期交易分析，识别入场出场信号、
日线支撑阻力位、战术风险等级等中期层面的分析。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from loguru import logger

from ...syncps.technical_indicator_calculator import TechnicalIndicatorCalculator
from ..indicators.enhanced_technical_calculator import EnhancedTechnicalCalculator


class TacticalAnalyzer:
    """战术分析器 - 基于日K数据"""
    
    def __init__(self, daily_data: pd.DataFrame, weekly_data: pd.DataFrame = None):
        """
        初始化战术分析器
        
        Args:
            daily_data: 日K数据
            weekly_data: 周K数据（用于趋势确认）
        """
        self.daily_data = daily_data.copy() if daily_data is not None else pd.DataFrame()
        self.weekly_data = weekly_data.copy() if weekly_data is not None else pd.DataFrame()
        self.logger = logger.bind(analyzer=self.__class__.__name__)
        
        # 初始化增强技术指标计算器
        if not self.daily_data.empty:
            self.enhanced_calculator = EnhancedTechnicalCalculator(
                daily_data=self.daily_data,
                weekly_data=self.weekly_data
            )
        else:
            self.enhanced_calculator = None
        
        # 分析结果缓存
        self.analysis_cache = {}
    
    def analyze_tactical_trend(self) -> Dict[str, Any]:
        """
        分析战术趋势 - 重构版本

        Returns:
            Dict包含中期趋势状态的具体描述
        """
        try:
            if self.daily_data.empty:
                return {
                    'trend_status': '数据不足',
                    'trend_description': '缺少日K数据，无法进行中期趋势分析',
                    'trend_reasoning': '战术分析需要充足的日K线历史数据',
                    'error': '缺少日K数据'
                }

            self.logger.info("开始战术趋势分析...")

            prices = self.daily_data['close']

            # 中期移动平均线
            ema_12 = prices.ewm(span=12).mean()
            ema_26 = prices.ewm(span=26).mean()
            sma_50 = prices.rolling(window=50).mean()

            # 当前价格和指标值
            current_price = prices.iloc[-1]
            latest_ema_12 = ema_12.iloc[-1] if not ema_12.empty else None
            latest_ema_26 = ema_26.iloc[-1] if not ema_26.empty else None
            latest_sma_50 = sma_50.iloc[-1] if not sma_50.empty else None

            # 生成详细的趋势分析
            if latest_ema_12 and latest_ema_26:
                trend_analysis = self._generate_tactical_trend_analysis(
                    current_price, latest_ema_12, latest_ema_26, latest_sma_50
                )

                # 趋势确认（与周线趋势对比）
                trend_confirmation = self._confirm_trend_with_weekly_description(trend_analysis['status'])

                result = {
                    'trend_status': trend_analysis['status'],
                    'trend_description': trend_analysis['description'],
                    'trend_reasoning': trend_analysis['reasoning'],
                    'trend_confirmation': trend_confirmation,
                    'current_price': current_price,
                    'ema_12': latest_ema_12,
                    'ema_26': latest_ema_26,
                    'sma_50': latest_sma_50,
                    'analysis_timestamp': datetime.now()
                }
            else:
                result = {
                    'trend_status': '数据不足',
                    'trend_description': '移动平均线数据不完整，无法准确判断中期趋势',
                    'trend_reasoning': '需要更多历史数据来计算可靠的移动平均线',
                    'trend_confirmation': '无法确认',
                    'current_price': current_price,
                    'analysis_timestamp': datetime.now()
                }

            self.logger.info(f"战术趋势分析完成: {result['trend_status']}")
            return result

        except Exception as e:
            self.logger.error(f"战术趋势分析失败: {e}")
            return {
                'trend_status': '分析异常',
                'trend_description': f'战术趋势分析过程中发生异常: {str(e)}',
                'trend_reasoning': '请检查数据质量或联系技术支持',
                'error': str(e)
            }
    
    def analyze_technical_indicators_status(self) -> Dict[str, Any]:
        """
        分析技术指标状态 - 重构版本

        Returns:
            Dict包含各主要技术指标的当前状态和信号
        """
        try:
            if self.enhanced_calculator is None:
                return {
                    'indicators_status': '数据不足',
                    'indicators_description': '缺少数据，无法计算技术指标',
                    'indicators_details': '技术指标分析需要充足的历史数据',
                    'error': '缺少数据'
                }

            self.logger.info("开始分析技术指标状态...")

            # 获取增强技术指标
            indicators = self.enhanced_calculator.calculate_enhanced_indicators()

            # 分析各个技术指标
            macd_analysis = self._analyze_macd_status(indicators)
            rsi_analysis = self._analyze_rsi_status(indicators)
            kdj_analysis = self._analyze_kdj_status()
            bollinger_analysis = self._analyze_bollinger_status(indicators)

            # 生成综合技术指标状态
            status_summary = self._generate_indicators_summary(
                macd_analysis, rsi_analysis, kdj_analysis, bollinger_analysis
            )

            result = {
                'indicators_status': status_summary['status'],
                'indicators_description': status_summary['description'],
                'indicators_details': status_summary['details'],
                'macd_status': macd_analysis,
                'rsi_status': rsi_analysis,
                'kdj_status': kdj_analysis,
                'bollinger_status': bollinger_analysis,
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"技术指标状态分析完成: {status_summary['status']}")
            return result

        except Exception as e:
            self.logger.error(f"技术指标状态分析失败: {e}")
            return {
                'indicators_status': '分析异常',
                'indicators_description': f'技术指标分析过程中发生异常: {str(e)}',
                'indicators_details': '请检查数据质量或联系技术支持',
                'error': str(e)
            }

    def analyze_trading_timing(self) -> Dict[str, Any]:
        """
        分析买卖时机 - 新增方法

        Returns:
            Dict包含具体的时机分析和建议
        """
        try:
            self.logger.info("开始分析买卖时机...")

            # 获取趋势和技术指标分析
            trend_analysis = self.analyze_tactical_trend()
            indicators_analysis = self.analyze_technical_indicators_status()

            # 生成时机分析
            timing_analysis = self._generate_timing_analysis(trend_analysis, indicators_analysis)

            result = {
                'timing_assessment': timing_analysis['assessment'],
                'timing_description': timing_analysis['description'],
                'timing_reasoning': timing_analysis['reasoning'],
                'buy_timing_advice': timing_analysis['buy_advice'],
                'sell_timing_advice': timing_analysis['sell_advice'],
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"买卖时机分析完成: {timing_analysis['assessment']}")
            return result

        except Exception as e:
            self.logger.error(f"买卖时机分析失败: {e}")
            return {
                'timing_assessment': '分析异常',
                'timing_description': f'买卖时机分析过程中发生异常: {str(e)}',
                'timing_reasoning': '请检查数据质量或联系技术支持',
                'buy_timing_advice': '建议等待系统恢复后重新评估',
                'sell_timing_advice': '建议等待系统恢复后重新评估',
                'error': str(e)
            }

    def calculate_daily_key_levels(self) -> Dict[str, Any]:
        """
        计算日线级别关键价位 - 新增方法

        Returns:
            Dict包含日线级别的支撑阻力位
        """
        try:
            if self.daily_data.empty:
                return {
                    'key_levels_description': '数据不足，无法计算日线级别关键价位',
                    'support_analysis': '缺少日K数据，无法识别支撑位',
                    'resistance_analysis': '缺少日K数据，无法识别阻力位',
                    'error': '缺少日K数据'
                }

            self.logger.info("开始计算日线级别关键价位...")

            highs = self.daily_data['high']
            lows = self.daily_data['low']
            closes = self.daily_data['close']
            current_price = closes.iloc[-1]

            # 寻找日线级别的重要高点和低点
            resistance_levels = self._find_daily_resistance_levels(highs, closes)
            support_levels = self._find_daily_support_levels(lows, closes)

            # 生成关键价位分析
            levels_analysis = self._generate_daily_levels_analysis(
                current_price, support_levels, resistance_levels
            )

            result = {
                'key_levels_description': levels_analysis['description'],
                'support_analysis': levels_analysis['support_analysis'],
                'resistance_analysis': levels_analysis['resistance_analysis'],
                'daily_support_1': support_levels[0] if len(support_levels) > 0 else None,
                'daily_support_2': support_levels[1] if len(support_levels) > 1 else None,
                'daily_resistance_1': resistance_levels[0] if len(resistance_levels) > 0 else None,
                'daily_resistance_2': resistance_levels[1] if len(resistance_levels) > 1 else None,
                'current_price': current_price,
                'analysis_timestamp': datetime.now()
            }

            self.logger.info("日线级别关键价位计算完成")
            return result

        except Exception as e:
            self.logger.error(f"日线级别关键价位计算失败: {e}")
            return {
                'key_levels_description': f'关键价位计算异常: {str(e)}',
                'support_analysis': '支撑位分析异常，请检查数据质量',
                'resistance_analysis': '阻力位分析异常，请检查数据质量',
                'error': str(e)
            }

    def generate_tactical_advice(self) -> Dict[str, Any]:
        """
        生成战术操作建议 - 新增方法

        Returns:
            Dict包含基于战术分析的具体操作建议
        """
        try:
            self.logger.info("开始生成战术操作建议...")

            # 获取各项分析结果
            trend_analysis = self.analyze_tactical_trend()
            indicators_analysis = self.analyze_technical_indicators_status()
            timing_analysis = self.analyze_trading_timing()
            levels_analysis = self.calculate_daily_key_levels()

            # 生成综合操作建议
            advice = self._generate_comprehensive_tactical_advice(
                trend_analysis, indicators_analysis, timing_analysis, levels_analysis
            )

            result = {
                'tactical_recommendation': advice['recommendation'],
                'operation_strategy': advice['strategy'],
                'entry_points': advice['entry_points'],
                'exit_points': advice['exit_points'],
                'risk_control': advice['risk_control'],
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"战术操作建议生成完成: {advice['recommendation']}")
            return result

        except Exception as e:
            self.logger.error(f"战术操作建议生成失败: {e}")
            return {
                'tactical_recommendation': '建议生成异常，请谨慎操作',
                'operation_strategy': '建议等待系统恢复后重新评估',
                'entry_points': '无法确定入场点位',
                'exit_points': '无法确定出场点位',
                'risk_control': '严格控制风险，避免损失',
                'error': str(e)
            }

    def generate_exit_signals(self) -> Dict[str, Any]:
        """生成出场信号"""
        try:
            if self.enhanced_calculator is None:
                return {
                    'exit_signal': 'NO_DATA',
                    'signal_strength': 0,
                    'exit_reasons': [],
                    'error': '缺少数据'
                }
            
            self.logger.info("开始生成出场信号...")
            
            indicators = self.enhanced_calculator.calculate_enhanced_indicators()
            
            exit_reasons = []
            exit_scores = []
            
            # 1. 超买超卖出场信号
            if 'rsi' in indicators:
                rsi_value = indicators['rsi'].iloc[-1]
                if rsi_value > 80:
                    exit_reasons.append("RSI超买")
                    exit_scores.append(80)
                elif rsi_value < 20:
                    exit_reasons.append("RSI超卖")
                    exit_scores.append(-80)
            
            # 2. MACD背离出场信号
            if 'macd' in indicators and 'macd_signal' in indicators:
                macd_exit = self._analyze_macd_exit_signal(indicators)
                if macd_exit['should_exit']:
                    exit_reasons.append(macd_exit['reason'])
                    exit_scores.append(macd_exit['score'])
            
            # 3. 支撑阻力位突破出场信号
            support_resistance = self.identify_daily_support_resistance()
            sr_exit = self._analyze_support_resistance_exit(support_resistance)
            if sr_exit['should_exit']:
                exit_reasons.append(sr_exit['reason'])
                exit_scores.append(sr_exit['score'])
            
            # 4. 趋势反转出场信号
            trend_analysis = self.analyze_tactical_trend()
            if not trend_analysis.get('trend_confirmation', True):
                exit_reasons.append("趋势确认失败")
                exit_scores.append(60)
            
            # 综合出场信号判断
            if exit_scores:
                avg_score = np.mean(exit_scores)
                
                if avg_score > 70:
                    exit_signal = 'STRONG_EXIT'
                elif avg_score > 50:
                    exit_signal = 'EXIT'
                elif avg_score < -70:
                    exit_signal = 'STRONG_REVERSE'
                elif avg_score < -50:
                    exit_signal = 'REVERSE'
                else:
                    exit_signal = 'HOLD'
                
                signal_strength = abs(avg_score)
            else:
                exit_signal = 'NO_SIGNAL'
                signal_strength = 0
            
            result = {
                'exit_signal': exit_signal,
                'signal_strength': signal_strength,
                'exit_reasons': exit_reasons,
                'exit_count': len(exit_reasons),
                'analysis_timestamp': datetime.now()
            }
            
            self.logger.info(f"出场信号生成完成: {exit_signal}")
            return result
            
        except Exception as e:
            self.logger.error(f"出场信号生成失败: {e}")
            return {
                'exit_signal': 'ERROR',
                'signal_strength': 0,
                'exit_reasons': [],
                'error': str(e)
            }
    
    def identify_daily_support_resistance(self) -> Dict[str, Any]:
        """识别日线支撑阻力位"""
        try:
            if self.daily_data.empty:
                return {
                    'daily_support': None,
                    'daily_resistance': None,
                    'support_strength': 0,
                    'resistance_strength': 0,
                    'error': '缺少日K数据'
                }
            
            self.logger.info("开始识别日线支撑阻力位...")
            
            highs = self.daily_data['high']
            lows = self.daily_data['low']
            closes = self.daily_data['close']
            current_price = closes.iloc[-1]
            
            # 寻找近期支撑位
            recent_lows = lows.tail(20)  # 最近20个交易日
            support_candidates = []
            
            for i in range(1, len(recent_lows) - 1):
                if (recent_lows.iloc[i] < recent_lows.iloc[i-1] and 
                    recent_lows.iloc[i] < recent_lows.iloc[i+1]):
                    support_candidates.append(recent_lows.iloc[i])
            
            # 选择最强支撑位（最接近当前价格且低于当前价格）
            valid_supports = [s for s in support_candidates if s < current_price]
            daily_support = max(valid_supports) if valid_supports else None
            
            # 寻找近期阻力位
            recent_highs = highs.tail(20)
            resistance_candidates = []
            
            for i in range(1, len(recent_highs) - 1):
                if (recent_highs.iloc[i] > recent_highs.iloc[i-1] and 
                    recent_highs.iloc[i] > recent_highs.iloc[i+1]):
                    resistance_candidates.append(recent_highs.iloc[i])
            
            # 选择最强阻力位（最接近当前价格且高于当前价格）
            valid_resistances = [r for r in resistance_candidates if r > current_price]
            daily_resistance = min(valid_resistances) if valid_resistances else None
            
            # 计算支撑阻力强度
            support_strength = self._calculate_level_strength(daily_support, lows) if daily_support else 0
            resistance_strength = self._calculate_level_strength(daily_resistance, highs) if daily_resistance else 0
            
            result = {
                'daily_support': daily_support,
                'daily_resistance': daily_resistance,
                'support_strength': support_strength,
                'resistance_strength': resistance_strength,
                'current_price': current_price,
                'analysis_timestamp': datetime.now()
            }
            
            self.logger.info("日线支撑阻力位识别完成")
            return result
            
        except Exception as e:
            self.logger.error(f"日线支撑阻力位识别失败: {e}")
            return {
                'daily_support': None,
                'daily_resistance': None,
                'support_strength': 0,
                'resistance_strength': 0,
                'error': str(e)
            }

    def assess_tactical_risk(self) -> Dict[str, Any]:
        """评估战术风险"""
        try:
            if self.daily_data.empty:
                return {
                    'tactical_risk_level': 'UNKNOWN',
                    'risk_factors': {},
                    'error': '缺少日K数据'
                }

            self.logger.info("开始战术风险评估...")

            risk_factors = {}
            risk_scores = []

            # 1. 短期波动率风险
            prices = self.daily_data['close']
            short_volatility = prices.pct_change().tail(10).std()

            if short_volatility > 0.05:
                vol_risk = 'HIGH'
                vol_score = 80
            elif short_volatility > 0.03:
                vol_risk = 'MEDIUM'
                vol_score = 50
            else:
                vol_risk = 'LOW'
                vol_score = 20

            risk_factors['short_volatility'] = {
                'level': vol_risk,
                'score': vol_score,
                'value': short_volatility
            }
            risk_scores.append(vol_score)

            # 2. 技术指标风险
            if self.enhanced_calculator:
                indicators = self.enhanced_calculator.calculate_enhanced_indicators()
                tech_risk_score = self._calculate_technical_risk(indicators)

                if tech_risk_score > 70:
                    tech_risk = 'HIGH'
                elif tech_risk_score > 40:
                    tech_risk = 'MEDIUM'
                else:
                    tech_risk = 'LOW'

                risk_factors['technical_indicators'] = {
                    'level': tech_risk,
                    'score': tech_risk_score
                }
                risk_scores.append(tech_risk_score)

            # 3. 趋势一致性风险
            trend_analysis = self.analyze_tactical_trend()
            if not trend_analysis.get('trend_confirmation', True):
                trend_risk_score = 70
                trend_risk = 'HIGH'
            else:
                # 基于趋势状态判断风险（修正字符串比较错误）
                trend_status = trend_analysis.get('trend_status', '中性')
                if '强势' in trend_status or '强烈' in trend_status:
                    trend_risk_score = 20
                    trend_risk = 'LOW'
                elif '上涨' in trend_status or '下跌' in trend_status:
                    trend_risk_score = 35
                    trend_risk = 'MEDIUM'
                else:
                    trend_risk_score = 50
                    trend_risk = 'MEDIUM'

            risk_factors['trend_consistency'] = {
                'level': trend_risk,
                'score': trend_risk_score
            }
            risk_scores.append(trend_risk_score)

            # 综合风险评估
            avg_risk_score = np.mean(risk_scores) if risk_scores else 50

            if avg_risk_score > 70:
                tactical_risk_level = 'HIGH'
            elif avg_risk_score > 40:
                tactical_risk_level = 'MEDIUM'
            else:
                tactical_risk_level = 'LOW'

            result = {
                'tactical_risk_level': tactical_risk_level,
                'risk_score': avg_risk_score,
                'risk_factors': risk_factors,
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"战术风险评估完成: {tactical_risk_level}")
            return result

        except Exception as e:
            self.logger.error(f"战术风险评估失败: {e}")
            return {
                'tactical_risk_level': 'ERROR',
                'risk_factors': {},
                'error': str(e)
            }

    def generate_entry_signals(self) -> Dict[str, Any]:
        """生成入场信号"""
        try:
            if self.daily_data.empty:
                return {
                    'entry_signals': [],
                    'signal_strength': 'NONE',
                    'description': '缺少日K数据，无法生成入场信号',
                    'error': '缺少数据'
                }

            self.logger.info("开始生成入场信号...")

            # 获取技术指标
            if self.enhanced_calculator:
                indicators = self.enhanced_calculator.calculate_enhanced_indicators()
            else:
                indicators = {}

            signals = []
            signal_strength = 'WEAK'

            # RSI信号
            if 'rsi' in indicators and not indicators['rsi'].empty:
                rsi_value = indicators['rsi'].iloc[-1]
                if rsi_value < 30:
                    signals.append({'type': 'BUY', 'source': 'RSI', 'strength': 'STRONG', 'value': rsi_value})
                    signal_strength = 'STRONG'
                elif rsi_value < 40:
                    signals.append({'type': 'BUY', 'source': 'RSI', 'strength': 'MODERATE', 'value': rsi_value})
                    if signal_strength == 'WEAK':
                        signal_strength = 'MODERATE'

            # MACD信号
            if 'macd' in indicators and not indicators['macd'].empty:
                macd_value = indicators['macd'].iloc[-1]
                macd_signal = indicators.get('macd_signal', pd.Series()).iloc[-1] if 'macd_signal' in indicators else 0
                if macd_value > macd_signal and macd_value > 0:
                    signals.append({'type': 'BUY', 'source': 'MACD', 'strength': 'MODERATE', 'value': macd_value})
                    if signal_strength == 'WEAK':
                        signal_strength = 'MODERATE'

            # 价格趋势信号
            if len(self.daily_data) >= 5:
                recent_prices = self.daily_data['close'].iloc[-5:]
                if recent_prices.iloc[-1] > recent_prices.iloc[-3] > recent_prices.iloc[-5]:
                    signals.append({'type': 'BUY', 'source': 'TREND', 'strength': 'MODERATE', 'value': 'UPTREND'})
                    if signal_strength == 'WEAK':
                        signal_strength = 'MODERATE'

            # 生成综合入场信号
            if signals:
                # 统计信号类型
                buy_signals = [s for s in signals if s['type'] == 'BUY']
                sell_signals = [s for s in signals if s['type'] == 'SELL']

                if len(buy_signals) > len(sell_signals):
                    if signal_strength == 'STRONG':
                        entry_signal = 'STRONG_BUY'
                    elif signal_strength == 'MODERATE':
                        entry_signal = 'BUY'
                    else:
                        entry_signal = 'WEAK_BUY'
                elif len(sell_signals) > len(buy_signals):
                    if signal_strength == 'STRONG':
                        entry_signal = 'STRONG_SELL'
                    elif signal_strength == 'MODERATE':
                        entry_signal = 'SELL'
                    else:
                        entry_signal = 'WEAK_SELL'
                else:
                    entry_signal = 'HOLD'
            else:
                entry_signal = 'NO_SIGNAL'

            return {
                'entry_signal': entry_signal,  # 添加单个信号字段
                'entry_signals': signals,      # 保留信号列表
                'signal_strength': signal_strength,
                'signal_count': len(signals),
                'description': f'检测到 {len(signals)} 个入场信号，整体强度: {signal_strength}',
                'analysis_timestamp': datetime.now()
            }

        except Exception as e:
            self.logger.error(f"生成入场信号失败: {e}")
            return {
                'entry_signals': [],
                'signal_strength': 'ERROR',
                'description': f'入场信号生成失败: {str(e)}',
                'error': str(e)
            }

    def generate_tactical_analysis_report(self) -> Dict[str, Any]:
        """生成战术分析报告"""
        try:
            self.logger.info("生成战术分析报告...")

            # 执行各项分析
            trend_analysis = self.analyze_tactical_trend()
            entry_signals = self.generate_entry_signals()
            exit_signals = self.generate_exit_signals()
            support_resistance = self.identify_daily_support_resistance()
            risk_assessment = self.assess_tactical_risk()

            # 综合报告
            report = {
                'analysis_type': 'TACTICAL',
                'timeframe': 'DAILY',
                'analysis_timestamp': datetime.now(),
                'data_quality': 'GOOD' if not self.daily_data.empty else 'NO_DATA',
                'data_points': len(self.daily_data),

                # 各项分析结果
                'trend_analysis': trend_analysis,
                'entry_signals': entry_signals,
                'exit_signals': exit_signals,
                'support_resistance': support_resistance,
                'risk_assessment': risk_assessment,

                # 综合建议
                'tactical_recommendation': self._generate_tactical_recommendation(
                    entry_signals, exit_signals, risk_assessment
                )
            }

            self.logger.info("战术分析报告生成完成")
            return report

        except Exception as e:
            self.logger.error(f"战术分析报告生成失败: {e}")
            return {
                'analysis_type': 'TACTICAL',
                'error': str(e),
                'analysis_timestamp': datetime.now()
            }

    # 辅助方法
    def _confirm_trend_with_weekly(self, tactical_trend: str) -> bool:
        """与周线趋势确认"""
        try:
            if self.weekly_data.empty:
                return True  # 无周线数据时默认确认

            weekly_prices = self.weekly_data['close']
            weekly_sma = weekly_prices.rolling(window=13).mean()

            if len(weekly_sma) < 1:
                return True

            current_weekly_price = weekly_prices.iloc[-1]
            latest_weekly_sma = weekly_sma.iloc[-1]

            weekly_trend = 'UP' if current_weekly_price > latest_weekly_sma else 'DOWN'

            # 检查战术趋势与战略趋势的一致性
            if tactical_trend in ['STRONG_UPTREND', 'WEAK_UPTREND'] and weekly_trend == 'UP':
                return True
            elif tactical_trend in ['STRONG_DOWNTREND', 'WEAK_DOWNTREND'] and weekly_trend == 'DOWN':
                return True
            elif tactical_trend == 'SIDEWAYS':
                return True
            else:
                return False

        except Exception:
            return True

    def _analyze_macd_entry_signal(self, indicators: Dict) -> Dict:
        """分析MACD入场信号"""
        try:
            macd_line = indicators['macd']
            macd_signal = indicators['macd_signal']

            if len(macd_line) < 2 or len(macd_signal) < 2:
                return {'signal': 'NO_DATA', 'score': 0}

            current_macd = macd_line.iloc[-1]
            current_signal = macd_signal.iloc[-1]
            prev_macd = macd_line.iloc[-2]
            prev_signal = macd_signal.iloc[-2]

            # 金叉死叉判断
            if prev_macd <= prev_signal and current_macd > current_signal:
                return {'signal': 'GOLDEN_CROSS', 'score': 70}
            elif prev_macd >= prev_signal and current_macd < current_signal:
                return {'signal': 'DEATH_CROSS', 'score': -70}
            elif current_macd > current_signal and current_macd > 0:
                return {'signal': 'BULLISH', 'score': 40}
            elif current_macd < current_signal and current_macd < 0:
                return {'signal': 'BEARISH', 'score': -40}
            else:
                return {'signal': 'NEUTRAL', 'score': 0}

        except Exception:
            return {'signal': 'ERROR', 'score': 0}

    def _analyze_rsi_entry_signal(self, indicators: Dict) -> Dict:
        """分析RSI入场信号"""
        try:
            rsi = indicators['rsi']

            if rsi.empty:
                return {'signal': 'NO_DATA', 'score': 0}

            current_rsi = rsi.iloc[-1]

            if current_rsi < 30:
                return {'signal': 'OVERSOLD', 'score': 60}
            elif current_rsi > 70:
                return {'signal': 'OVERBOUGHT', 'score': -60}
            elif 40 <= current_rsi <= 60:
                return {'signal': 'NEUTRAL', 'score': 20}
            else:
                return {'signal': 'TRENDING', 'score': 10}

        except Exception:
            return {'signal': 'ERROR', 'score': 0}

    def _analyze_kdj_entry_signal(self, kdj_signals: Dict) -> Dict:
        """分析KDJ入场信号"""
        try:
            signal = kdj_signals.get('kdj_signal', 'UNKNOWN')
            strength = kdj_signals.get('kdj_strength', 0)

            if signal == 'GOLDEN_CROSS':
                return {'signal': 'BUY', 'score': 75}
            elif signal == 'DEATH_CROSS':
                return {'signal': 'SELL', 'score': -75}
            elif signal == 'OVERSOLD':
                return {'signal': 'BUY', 'score': 60}
            elif signal == 'OVERBOUGHT':
                return {'signal': 'SELL', 'score': -60}
            else:
                return {'signal': 'NEUTRAL', 'score': 0}

        except Exception:
            return {'signal': 'ERROR', 'score': 0}

    def _analyze_bollinger_entry_signal(self, indicators: Dict) -> Dict:
        """分析布林带入场信号"""
        try:
            bb_upper = indicators['bb_upper']
            bb_lower = indicators['bb_lower']
            bb_middle = indicators['bb_middle']

            if bb_upper.empty or bb_lower.empty:
                return {'signal': 'NO_DATA', 'score': 0}

            current_price = self.daily_data['close'].iloc[-1]
            upper = bb_upper.iloc[-1]
            lower = bb_lower.iloc[-1]
            middle = bb_middle.iloc[-1]

            # 布林带位置判断
            if current_price <= lower:
                return {'signal': 'OVERSOLD', 'score': 65}
            elif current_price >= upper:
                return {'signal': 'OVERBOUGHT', 'score': -65}
            elif current_price > middle:
                return {'signal': 'UPPER_HALF', 'score': 20}
            else:
                return {'signal': 'LOWER_HALF', 'score': -20}

        except Exception:
            return {'signal': 'ERROR', 'score': 0}

    def _analyze_macd_exit_signal(self, indicators: Dict) -> Dict:
        """分析MACD出场信号"""
        try:
            macd_line = indicators['macd']
            macd_signal = indicators['macd_signal']

            if len(macd_line) < 3 or len(macd_signal) < 3:
                return {'should_exit': False, 'reason': '', 'score': 0}

            # 检查MACD背离
            prices = self.daily_data['close'].tail(10)
            macd_values = macd_line.tail(10)

            # 简单的背离检测
            if len(prices) >= 5 and len(macd_values) >= 5:
                price_trend = prices.iloc[-1] > prices.iloc[-5]
                macd_trend = macd_values.iloc[-1] > macd_values.iloc[-5]

                if price_trend != macd_trend:
                    return {
                        'should_exit': True,
                        'reason': 'MACD背离',
                        'score': 70
                    }

            return {'should_exit': False, 'reason': '', 'score': 0}

        except Exception:
            return {'should_exit': False, 'reason': 'MACD分析异常', 'score': 0}

    def _analyze_support_resistance_exit(self, sr_data: Dict) -> Dict:
        """分析支撑阻力位出场信号"""
        try:
            current_price = self.daily_data['close'].iloc[-1]
            support = sr_data.get('daily_support')
            resistance = sr_data.get('daily_resistance')

            # 支撑位跌破
            if support and current_price < support * 0.98:  # 2%缓冲
                return {
                    'should_exit': True,
                    'reason': f'跌破支撑位{support:.2f}',
                    'score': 75
                }

            # 阻力位突破失败
            if resistance and current_price > resistance * 1.02:  # 2%缓冲
                # 检查是否是假突破
                recent_highs = self.daily_data['high'].tail(3)
                if all(h < resistance * 1.05 for h in recent_highs):
                    return {
                        'should_exit': True,
                        'reason': f'阻力位{resistance:.2f}假突破',
                        'score': 60
                    }

            return {'should_exit': False, 'reason': '', 'score': 0}

        except Exception:
            return {'should_exit': False, 'reason': '支撑阻力分析异常', 'score': 0}

    def _calculate_level_strength(self, level: float, price_series: pd.Series) -> int:
        """计算支撑阻力位强度"""
        try:
            if level is None:
                return 0

            # 计算价格触及该水平的次数
            tolerance = level * 0.01  # 1%容差
            touches = sum(1 for price in price_series if abs(price - level) <= tolerance)

            # 强度评分
            if touches >= 5:
                return 90
            elif touches >= 3:
                return 70
            elif touches >= 2:
                return 50
            else:
                return 30

        except Exception:
            return 0

    def execute_comprehensive_tactical_analysis(self) -> Dict[str, Any]:
        """
        执行完整的战术分析 - 主要接口方法

        Returns:
            Dict包含符合需求文档要求的完整战术分析结果
        """
        try:
            self.logger.info("开始执行完整的战术分析...")

            # 执行各项分析
            trend_analysis = self.analyze_tactical_trend()
            indicators_analysis = self.analyze_technical_indicators_status()
            timing_analysis = self.analyze_trading_timing()
            levels_analysis = self.calculate_daily_key_levels()
            advice_analysis = self.generate_tactical_advice()

            # 整合分析结果，符合需求文档格式
            comprehensive_result = {
                # 中期趋势状态：当前中期趋势的具体描述
                'medium_term_trend_status': trend_analysis.get('trend_status', '数据不足'),
                'trend_description': trend_analysis.get('trend_description', ''),
                'trend_reasoning': trend_analysis.get('trend_reasoning', ''),
                'trend_confirmation': trend_analysis.get('trend_confirmation', ''),

                # 技术指标状态：各主要技术指标的当前状态和信号
                'technical_indicators_status': indicators_analysis.get('indicators_status', '数据不足'),
                'indicators_description': indicators_analysis.get('indicators_description', ''),
                'indicators_details': indicators_analysis.get('indicators_details', ''),
                'macd_status': indicators_analysis.get('macd_status', ''),
                'rsi_status': indicators_analysis.get('rsi_status', ''),
                'kdj_status': indicators_analysis.get('kdj_status', ''),
                'bollinger_status': indicators_analysis.get('bollinger_status', ''),

                # 买卖时机判断：具体的时机分析和建议
                'trading_timing_assessment': timing_analysis.get('timing_assessment', ''),
                'timing_description': timing_analysis.get('timing_description', ''),
                'timing_reasoning': timing_analysis.get('timing_reasoning', ''),
                'buy_timing_advice': timing_analysis.get('buy_timing_advice', ''),
                'sell_timing_advice': timing_analysis.get('sell_timing_advice', ''),

                # 关键价位：日线级别的支撑阻力位
                'daily_key_levels_description': levels_analysis.get('key_levels_description', ''),
                'daily_support_analysis': levels_analysis.get('support_analysis', ''),
                'daily_resistance_analysis': levels_analysis.get('resistance_analysis', ''),
                'daily_support_1': levels_analysis.get('daily_support_1'),
                'daily_support_2': levels_analysis.get('daily_support_2'),
                'daily_resistance_1': levels_analysis.get('daily_resistance_1'),
                'daily_resistance_2': levels_analysis.get('daily_resistance_2'),

                # 操作建议：基于战术分析的具体操作建议
                'tactical_recommendation': advice_analysis.get('tactical_recommendation', ''),
                'operation_strategy': advice_analysis.get('operation_strategy', ''),
                'entry_points_advice': advice_analysis.get('entry_points', ''),
                'exit_points_advice': advice_analysis.get('exit_points', ''),
                'risk_control_advice': advice_analysis.get('risk_control', ''),

                # 元数据
                'analysis_timestamp': datetime.now(),
                'data_quality': 'GOOD' if not self.daily_data.empty else 'INSUFFICIENT',
                'analysis_success': True
            }

            self.logger.info("完整战术分析执行成功")
            return comprehensive_result

        except Exception as e:
            self.logger.error(f"完整战术分析执行失败: {e}")
            return {
                'medium_term_trend_status': '分析异常',
                'trend_description': f'战术分析过程中发生异常: {str(e)}',
                'trend_reasoning': '请检查数据质量或联系技术支持',
                'technical_indicators_status': '无法评估',
                'indicators_description': '技术指标分析异常',
                'indicators_details': '请检查数据质量',
                'trading_timing_assessment': '无法评估',
                'timing_description': '买卖时机分析异常',
                'timing_reasoning': '请检查数据质量',
                'daily_key_levels_description': '无法计算关键价位',
                'daily_support_analysis': '支撑位分析异常',
                'daily_resistance_analysis': '阻力位分析异常',
                'tactical_recommendation': '建议暂停交易决策',
                'operation_strategy': '等待系统恢复后重新评估',
                'entry_points_advice': '避免新增仓位',
                'exit_points_advice': '谨慎处理现有仓位',
                'risk_control_advice': '严格控制风险，避免损失',
                'analysis_timestamp': datetime.now(),
                'data_quality': 'ERROR',
                'analysis_success': False,
                'error': str(e)
            }

    def _generate_tactical_trend_analysis(self, current_price: float, ema_12: float,
                                        ema_26: float, sma_50: float = None) -> Dict[str, str]:
        """生成详细的战术趋势分析"""
        try:
            # 计算价格相对于移动平均线的位置
            price_vs_ema12_pct = ((current_price - ema_12) / ema_12) * 100
            price_vs_ema26_pct = ((current_price - ema_26) / ema_26) * 100
            ema12_vs_ema26_pct = ((ema_12 - ema_26) / ema_26) * 100

            # 趋势强度判断
            if current_price > ema_12 > ema_26:
                if price_vs_ema26_pct > 10:
                    status = '强势上涨'
                    description = f'当前价格{current_price:.2f}元显著高于12日均线({ema_12:.2f}元)和26日均线({ema_26:.2f}元)，较26日均线高出{price_vs_ema26_pct:.1f}%，中期上涨动能强劲。'
                    reasoning = '短期和中期均线呈现多头排列，价格大幅偏离中期均线，显示强势上涨趋势，适合持有或逢低加仓。'
                elif price_vs_ema26_pct > 3:
                    status = '稳健上涨'
                    description = f'当前价格{current_price:.2f}元位于12日均线({ema_12:.2f}元)和26日均线({ema_26:.2f}元)之上，较26日均线高出{price_vs_ema26_pct:.1f}%，呈现稳健的上涨态势。'
                    reasoning = '均线系统支撑价格上涨，虽然涨幅温和，但趋势稳定，可以考虑分批建仓。'
                else:
                    status = '初步上涨'
                    description = f'当前价格{current_price:.2f}元刚好站上12日均线({ema_12:.2f}元)和26日均线({ema_26:.2f}元)，显示初步的上涨信号。'
                    reasoning = '价格刚刚突破关键均线，上涨趋势初现，需要观察能否持续。'

            elif current_price < ema_12 < ema_26:
                if abs(price_vs_ema26_pct) > 10:
                    status = '强势下跌'
                    description = f'当前价格{current_price:.2f}元大幅低于12日均线({ema_12:.2f}元)和26日均线({ema_26:.2f}元)，较26日均线低{abs(price_vs_ema26_pct):.1f}%，中期下跌压力沉重。'
                    reasoning = '均线系统呈现空头排列，价格深度回调，市场情绪悲观，建议避免介入或减仓。'
                elif abs(price_vs_ema26_pct) > 3:
                    status = '持续下跌'
                    description = f'当前价格{current_price:.2f}元位于12日均线({ema_12:.2f}元)和26日均线({ema_26:.2f}元)下方，较26日均线低{abs(price_vs_ema26_pct):.1f}%，处于下降通道中。'
                    reasoning = '均线对价格形成压制，下跌趋势明确，建议谨慎操作，等待反弹信号。'
                else:
                    status = '初步下跌'
                    description = f'当前价格{current_price:.2f}元跌破12日均线({ema_12:.2f}元)和26日均线({ema_26:.2f}元)，显示初步的下跌信号。'
                    reasoning = '价格失守关键均线支撑，下跌风险增加，需要密切关注后续走势。'

            elif current_price > ema_12 and ema_12 < ema_26:
                status = '震荡偏强'
                description = f'当前价格{current_price:.2f}元高于12日均线({ema_12:.2f}元)但12日均线仍低于26日均线({ema_26:.2f}元)，显示短期反弹但中期趋势尚未确立。'
                reasoning = '短期均线开始上翘，但中期均线仍处劣势，可能是反弹行情，需要观察能否持续突破。'

            elif current_price < ema_12 and ema_12 > ema_26:
                status = '震荡偏弱'
                description = f'当前价格{current_price:.2f}元低于12日均线({ema_12:.2f}元)但12日均线高于26日均线({ema_26:.2f}元)，显示短期调整但中期趋势仍然向上。'
                reasoning = '中期趋势依然向好，当前可能是技术性调整，可以考虑逢低布局的机会。'

            else:
                status = '震荡整理'
                description = f'当前价格{current_price:.2f}元与12日均线({ema_12:.2f}元)和26日均线({ema_26:.2f}元)交织运行，中期方向不明确。'
                reasoning = '均线系统混乱，市场处于震荡整理阶段，建议等待明确的方向性突破。'

            # 如果有50日均线数据，添加长期背景分析
            if sma_50 and not pd.isna(sma_50):
                if current_price > sma_50:
                    description += f' 从更长期视角看，价格仍位于50日均线({sma_50:.2f}元)之上，长期趋势基础相对稳固。'
                else:
                    description += f' 从更长期视角看，价格已跌破50日均线({sma_50:.2f}元)，长期趋势面临考验。'

            return {
                'status': status,
                'description': description,
                'reasoning': reasoning
            }

        except Exception as e:
            return {
                'status': '分析异常',
                'description': f'战术趋势分析过程中发生异常: {str(e)}',
                'reasoning': '请检查数据质量或联系技术支持'
            }

    def _confirm_trend_with_weekly_description(self, tactical_trend: str) -> str:
        """与周线趋势确认的描述版本"""
        try:
            if self.weekly_data.empty:
                return '缺少周线数据，无法进行趋势确认'

            # 这里可以添加与周线趋势的对比逻辑
            # 简化版本，返回基本的确认信息
            if '上涨' in tactical_trend:
                return '与长期趋势方向一致，趋势确认度较高'
            elif '下跌' in tactical_trend:
                return '与长期趋势存在分歧，需要谨慎判断'
            else:
                return '趋势方向不明确，建议结合长期趋势分析'

        except Exception:
            return '趋势确认分析异常'

    def _analyze_macd_status(self, indicators: Dict) -> str:
        """分析MACD指标状态"""
        try:
            if 'macd' not in indicators or 'macd_signal' not in indicators:
                return 'MACD数据不足，无法分析'

            macd = indicators['macd'].iloc[-1]
            signal = indicators['macd_signal'].iloc[-1]
            histogram = macd - signal

            if macd > signal and histogram > 0:
                if macd > 0:
                    return 'MACD金叉且位于零轴上方，显示强烈的买入信号'
                else:
                    return 'MACD金叉但仍在零轴下方，显示初步的反弹信号'
            elif macd < signal and histogram < 0:
                if macd < 0:
                    return 'MACD死叉且位于零轴下方，显示强烈的卖出信号'
                else:
                    return 'MACD死叉但仍在零轴上方，显示初步的调整信号'
            else:
                return 'MACD信号不明确，建议观望等待明确信号'

        except Exception:
            return 'MACD分析异常'

    def _analyze_rsi_status(self, indicators: Dict) -> str:
        """分析RSI指标状态"""
        try:
            if 'rsi' not in indicators:
                return 'RSI数据不足，无法分析'

            rsi = indicators['rsi'].iloc[-1]

            if rsi > 80:
                return f'RSI为{rsi:.1f}，处于严重超买状态，存在回调风险'
            elif rsi > 70:
                return f'RSI为{rsi:.1f}，处于超买状态，需要谨慎追高'
            elif rsi < 20:
                return f'RSI为{rsi:.1f}，处于严重超卖状态，存在反弹机会'
            elif rsi < 30:
                return f'RSI为{rsi:.1f}，处于超卖状态，可以考虑逢低布局'
            elif 45 <= rsi <= 55:
                return f'RSI为{rsi:.1f}，处于中性区域，市场相对平衡'
            elif rsi > 55:
                return f'RSI为{rsi:.1f}，偏向强势，但尚未超买'
            else:
                return f'RSI为{rsi:.1f}，偏向弱势，但尚未超卖'

        except Exception:
            return 'RSI分析异常'

    def _analyze_kdj_status(self) -> str:
        """分析KDJ指标状态"""
        try:
            if not self.enhanced_calculator:
                return 'KDJ计算器未初始化'

            kdj_signals = self.enhanced_calculator.get_kdj_signals()
            if not kdj_signals:
                return 'KDJ数据不足，无法分析'

            # 这里需要根据实际的KDJ信号结构来分析
            # 简化版本
            return 'KDJ指标分析正常，具体信号需要进一步解析'

        except Exception:
            return 'KDJ分析异常'

    def _analyze_bollinger_status(self, indicators: Dict) -> str:
        """分析布林带指标状态"""
        try:
            if not all(k in indicators for k in ['bb_upper', 'bb_lower', 'bb_middle']):
                return '布林带数据不足，无法分析'

            current_price = self.daily_data['close'].iloc[-1]
            bb_upper = indicators['bb_upper'].iloc[-1]
            bb_lower = indicators['bb_lower'].iloc[-1]
            bb_middle = indicators['bb_middle'].iloc[-1]

            if current_price > bb_upper:
                return f'价格{current_price:.2f}突破布林带上轨({bb_upper:.2f})，显示强势突破但需警惕回调'
            elif current_price < bb_lower:
                return f'价格{current_price:.2f}跌破布林带下轨({bb_lower:.2f})，显示超卖状态，存在反弹机会'
            elif current_price > bb_middle:
                return f'价格{current_price:.2f}位于布林带中轨({bb_middle:.2f})上方，显示相对强势'
            else:
                return f'价格{current_price:.2f}位于布林带中轨({bb_middle:.2f})下方，显示相对弱势'

        except Exception:
            return '布林带分析异常'

    def _generate_indicators_summary(self, macd: str, rsi: str, kdj: str, bollinger: str) -> Dict[str, str]:
        """生成技术指标综合状态"""
        try:
            # 统计各指标的信号倾向
            bullish_signals = []
            bearish_signals = []
            neutral_signals = []

            # 分析MACD信号
            if '买入' in macd or '金叉' in macd:
                bullish_signals.append('MACD看涨')
            elif '卖出' in macd or '死叉' in macd:
                bearish_signals.append('MACD看跌')
            else:
                neutral_signals.append('MACD中性')

            # 分析RSI信号
            if '超卖' in rsi or '反弹' in rsi:
                bullish_signals.append('RSI超卖反弹')
            elif '超买' in rsi or '回调' in rsi:
                bearish_signals.append('RSI超买回调')
            else:
                neutral_signals.append('RSI中性')

            # 分析布林带信号
            if '反弹' in bollinger or '强势' in bollinger:
                bullish_signals.append('布林带看涨')
            elif '回调' in bollinger or '弱势' in bollinger:
                bearish_signals.append('布林带看跌')
            else:
                neutral_signals.append('布林带中性')

            # 生成综合状态
            if len(bullish_signals) >= 2:
                status = '技术指标偏多'
                description = f'多项技术指标显示积极信号：{", ".join(bullish_signals)}。'
                details = f'MACD: {macd}; RSI: {rsi}; 布林带: {bollinger}'
            elif len(bearish_signals) >= 2:
                status = '技术指标偏空'
                description = f'多项技术指标显示消极信号：{", ".join(bearish_signals)}。'
                details = f'MACD: {macd}; RSI: {rsi}; 布林带: {bollinger}'
            else:
                status = '技术指标中性'
                description = '各项技术指标信号混合，市场方向不明确。'
                details = f'MACD: {macd}; RSI: {rsi}; 布林带: {bollinger}'

            return {
                'status': status,
                'description': description,
                'details': details
            }

        except Exception:
            return {
                'status': '指标分析异常',
                'description': '技术指标综合分析过程中发生异常',
                'details': '请检查各项指标数据质量'
            }

    def _generate_timing_analysis(self, trend_analysis: Dict, indicators_analysis: Dict) -> Dict[str, str]:
        """生成买卖时机分析"""
        try:
            trend_status = trend_analysis.get('trend_status', '中性')
            indicators_status = indicators_analysis.get('indicators_status', '中性')

            # 基于趋势和指标状态生成时机分析
            if '强势上涨' in trend_status and '偏多' in indicators_status:
                assessment = '买入时机良好'
                description = '中期趋势强势上涨，技术指标支持买入，当前是较好的入场时机。'
                reasoning = '趋势和技术指标双重确认，市场动能充足，适合积极操作。'
                buy_advice = '建议在回调至关键支撑位时分批买入，或在突破阻力位后追涨。'
                sell_advice = '暂不建议卖出，可以持有等待更高价位。'

            elif '下跌' in trend_status and '偏空' in indicators_status:
                assessment = '卖出时机良好'
                description = '中期趋势偏弱，技术指标支持卖出，当前是较好的出场时机。'
                reasoning = '趋势和技术指标双重确认下跌风险，建议及时止损或减仓。'
                buy_advice = '暂不建议买入，等待趋势转强信号。'
                sell_advice = '建议在反弹至关键阻力位时分批卖出，或跌破支撑位后止损。'

            elif '上涨' in trend_status or '偏多' in indicators_status:
                assessment = '偏向买入时机'
                description = '趋势或技术指标显示积极信号，可以考虑适量买入。'
                reasoning = '虽然信号不够强烈，但整体偏向积极，可以小仓位试探。'
                buy_advice = '建议小仓位买入，严格设置止损。'
                sell_advice = '如有持仓可以继续持有，观察后续发展。'

            elif '下跌' in trend_status or '偏空' in indicators_status:
                assessment = '偏向卖出时机'
                description = '趋势或技术指标显示消极信号，建议谨慎操作。'
                reasoning = '存在一定的下跌风险，建议控制仓位或考虑减仓。'
                buy_advice = '暂不建议买入，等待更明确的信号。'
                sell_advice = '建议适量减仓，控制风险敞口。'

            else:
                assessment = '观望等待'
                description = '当前趋势和技术指标信号不明确，建议观望等待。'
                reasoning = '市场方向不清晰，贸然操作风险较大。'
                buy_advice = '等待明确的买入信号再行动。'
                sell_advice = '如有持仓可以继续持有，但要设置止损。'

            return {
                'assessment': assessment,
                'description': description,
                'reasoning': reasoning,
                'buy_advice': buy_advice,
                'sell_advice': sell_advice
            }

        except Exception:
            return {
                'assessment': '分析异常',
                'description': '买卖时机分析过程中发生异常',
                'reasoning': '请检查数据质量或联系技术支持',
                'buy_advice': '建议等待系统恢复',
                'sell_advice': '建议等待系统恢复'
            }

    def _find_daily_support_levels(self, lows: pd.Series, closes: pd.Series) -> List[float]:
        """寻找日线级别支撑位"""
        try:
            if len(lows) < 20:
                return []

            support_levels = []

            # 寻找近期低点
            recent_lows = lows.iloc[-60:] if len(lows) >= 60 else lows

            # 找到局部最低点
            for i in range(2, len(recent_lows) - 2):
                if (recent_lows.iloc[i] <= recent_lows.iloc[i-1] and
                    recent_lows.iloc[i] <= recent_lows.iloc[i-2] and
                    recent_lows.iloc[i] <= recent_lows.iloc[i+1] and
                    recent_lows.iloc[i] <= recent_lows.iloc[i+2]):
                    support_levels.append(recent_lows.iloc[i])

            # 去重并排序
            support_levels = sorted(list(set(support_levels)), reverse=True)

            # 返回最重要的2个支撑位
            return support_levels[:2]

        except Exception:
            return []

    def _find_daily_resistance_levels(self, highs: pd.Series, closes: pd.Series) -> List[float]:
        """寻找日线级别阻力位"""
        try:
            if len(highs) < 20:
                return []

            resistance_levels = []

            # 寻找近期高点
            recent_highs = highs.iloc[-60:] if len(highs) >= 60 else highs

            # 找到局部最高点
            for i in range(2, len(recent_highs) - 2):
                if (recent_highs.iloc[i] >= recent_highs.iloc[i-1] and
                    recent_highs.iloc[i] >= recent_highs.iloc[i-2] and
                    recent_highs.iloc[i] >= recent_highs.iloc[i+1] and
                    recent_highs.iloc[i] >= recent_highs.iloc[i+2]):
                    resistance_levels.append(recent_highs.iloc[i])

            # 去重并排序
            resistance_levels = sorted(list(set(resistance_levels)), reverse=True)

            # 返回最重要的2个阻力位
            return resistance_levels[:2]

        except Exception:
            return []

    def _generate_daily_levels_analysis(self, current_price: float,
                                      support_levels: List[float],
                                      resistance_levels: List[float]) -> Dict[str, str]:
        """生成日线级别关键价位分析"""
        try:
            # 分析支撑位
            if len(support_levels) >= 2:
                support_analysis = f'重要日线支撑位位于{support_levels[0]:.2f}元和{support_levels[1]:.2f}元。'
                if current_price > support_levels[0]:
                    support_analysis += f'当前价格{current_price:.2f}元位于主要支撑位之上，支撑有效。'
                else:
                    support_analysis += f'当前价格{current_price:.2f}元已跌破主要支撑位，需要关注下一支撑位{support_levels[1]:.2f}元。'
            elif len(support_levels) >= 1:
                support_analysis = f'重要日线支撑位位于{support_levels[0]:.2f}元。'
                if current_price > support_levels[0]:
                    support_analysis += f'当前价格{current_price:.2f}元位于支撑位之上。'
                else:
                    support_analysis += f'当前价格{current_price:.2f}元已跌破支撑位，需要谨慎。'
            else:
                support_analysis = '暂未识别到明确的日线支撑位，需要进一步观察价格走势。'

            # 分析阻力位
            if len(resistance_levels) >= 2:
                resistance_analysis = f'重要日线阻力位位于{resistance_levels[0]:.2f}元和{resistance_levels[1]:.2f}元。'
                if current_price < resistance_levels[0]:
                    resistance_analysis += f'当前价格{current_price:.2f}元位于主要阻力位下方，上涨空间受限。'
                else:
                    resistance_analysis += f'当前价格{current_price:.2f}元已突破主要阻力位，下一目标位{resistance_levels[1]:.2f}元。'
            elif len(resistance_levels) >= 1:
                resistance_analysis = f'重要日线阻力位位于{resistance_levels[0]:.2f}元。'
                if current_price < resistance_levels[0]:
                    resistance_analysis += f'当前价格{current_price:.2f}元位于阻力位下方。'
                else:
                    resistance_analysis += f'当前价格{current_price:.2f}元已突破阻力位。'
            else:
                resistance_analysis = '暂未识别到明确的日线阻力位，上涨空间相对开阔。'

            # 综合描述
            description = f'基于日K线数据分析，{support_analysis} {resistance_analysis}'

            return {
                'description': description,
                'support_analysis': support_analysis,
                'resistance_analysis': resistance_analysis
            }

        except Exception:
            return {
                'description': '日线关键价位分析异常，无法准确识别支撑阻力位',
                'support_analysis': '支撑位分析异常',
                'resistance_analysis': '阻力位分析异常'
            }

    def _generate_comprehensive_tactical_advice(self, trend_analysis: Dict, indicators_analysis: Dict,
                                              timing_analysis: Dict, levels_analysis: Dict) -> Dict[str, str]:
        """生成综合战术建议"""
        try:
            trend_status = trend_analysis.get('trend_status', '中性')
            indicators_status = indicators_analysis.get('indicators_status', '中性')
            timing_assessment = timing_analysis.get('timing_assessment', '观望')

            # 基于各项分析生成综合建议
            if '强势上涨' in trend_status and '偏多' in indicators_status and '买入' in timing_assessment:
                recommendation = '积极买入'
                strategy = '趋势跟随策略，在回调时加仓，突破时追涨，充分利用上涨趋势。'
                entry_points = f"建议在{levels_analysis.get('daily_support_1', '支撑位')}附近分批买入，或突破{levels_analysis.get('daily_resistance_1', '阻力位')}后追涨。"
                exit_points = '暂不考虑出场，持有等待更高价位，但要设置移动止损保护利润。'
                risk_control = '设置止损位在买入价下方5-8%，随着价格上涨逐步上移止损位。'

            elif '上涨' in trend_status and ('偏多' in indicators_status or '买入' in timing_assessment):
                recommendation = '谨慎买入'
                strategy = '稳健建仓策略，分批买入降低成本，控制仓位规模。'
                entry_points = f"建议在{levels_analysis.get('daily_support_1', '支撑位')}附近小仓位买入，观察市场反应后决定是否加仓。"
                exit_points = '可以在阻力位附近适量减仓，锁定部分利润。'
                risk_control = '严格控制仓位在总资金的10-15%，设置止损位在买入价下方8-10%。'

            elif '下跌' in trend_status and '偏空' in indicators_status and '卖出' in timing_assessment:
                recommendation = '积极卖出'
                strategy = '止损减仓策略，及时止损避免更大损失，等待趋势转换。'
                entry_points = '暂不建议新增买入，等待明确的趋势转换信号。'
                exit_points = f"建议在{levels_analysis.get('daily_resistance_1', '阻力位')}附近分批卖出，或跌破{levels_analysis.get('daily_support_1', '支撑位')}后止损。"
                risk_control = '严格执行止损，避免情绪化操作，保护资金安全。'

            elif '震荡' in trend_status or '中性' in indicators_status:
                recommendation = '区间操作'
                strategy = '震荡交易策略，在支撑位买入，在阻力位卖出，赚取波段收益。'
                entry_points = f"可以在{levels_analysis.get('daily_support_1', '支撑位')}附近小仓位买入。"
                exit_points = f"在{levels_analysis.get('daily_resistance_1', '阻力位')}附近适量卖出。"
                risk_control = '控制单次操作仓位，设置较紧的止损，快进快出。'

            else:
                recommendation = '观望等待'
                strategy = '等待策略，观察市场变化，等待明确的交易信号出现。'
                entry_points = '暂不建议操作，等待更明确的信号。'
                exit_points = '如有持仓可以继续持有，但要密切关注市场变化。'
                risk_control = '保持现有仓位，设置基础止损保护，避免盲目操作。'

            return {
                'recommendation': recommendation,
                'strategy': strategy,
                'entry_points': entry_points,
                'exit_points': exit_points,
                'risk_control': risk_control
            }

        except Exception:
            return {
                'recommendation': '建议生成异常，请谨慎操作',
                'strategy': '等待系统恢复后重新评估',
                'entry_points': '暂不建议操作',
                'exit_points': '谨慎处理现有仓位',
                'risk_control': '严格控制风险，避免损失'
            }

    def _calculate_technical_risk(self, indicators: Dict) -> int:
        """计算技术指标风险评分"""
        try:
            risk_score = 0

            # RSI风险
            if 'rsi' in indicators and not indicators['rsi'].empty:
                rsi_value = indicators['rsi'].iloc[-1]
                if rsi_value > 80 or rsi_value < 20:
                    risk_score += 30
                elif rsi_value > 70 or rsi_value < 30:
                    risk_score += 20
                else:
                    risk_score += 10

            # MACD风险
            if 'macd' in indicators and not indicators['macd'].empty:
                macd_value = indicators['macd'].iloc[-1]
                if abs(macd_value) > 0.8:
                    risk_score += 25
                else:
                    risk_score += 10

            # 布林带风险
            if all(k in indicators for k in ['bb_upper', 'bb_lower']) and not indicators['bb_upper'].empty:
                current_price = self.daily_data['close'].iloc[-1]
                bb_upper = indicators['bb_upper'].iloc[-1]
                bb_lower = indicators['bb_lower'].iloc[-1]

                bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
                if bb_position > 0.9 or bb_position < 0.1:
                    risk_score += 25
                else:
                    risk_score += 10

            return min(100, risk_score)

        except Exception:
            return 50

    def _generate_tactical_recommendation(self, entry_signals: Dict, exit_signals: Dict, risk_assessment: Dict) -> Dict:
        """生成战术建议"""
        try:
            recommendation = {
                'action': 'HOLD',
                'confidence': 50,
                'reasons': [],
                'warnings': []
            }

            # 基于入场信号
            entry_signal = entry_signals.get('entry_signal', 'NO_SIGNAL')
            entry_strength_raw = entry_signals.get('signal_strength', 0)

            # 确保 entry_strength 是数字（修正字符串比较错误）
            try:
                entry_strength = float(entry_strength_raw) if entry_strength_raw is not None else 0
            except (ValueError, TypeError):
                entry_strength = 0

            if entry_signal in ['STRONG_BUY', 'BUY'] and entry_strength > 60:
                recommendation['action'] = 'BUY'
                recommendation['confidence'] += 20
                recommendation['reasons'].append(f"入场信号: {entry_signal}")
            elif entry_signal in ['STRONG_SELL', 'SELL'] and entry_strength > 60:
                recommendation['action'] = 'SELL'
                recommendation['confidence'] += 20
                recommendation['reasons'].append(f"入场信号: {entry_signal}")

            # 基于出场信号
            exit_signal = exit_signals.get('exit_signal', 'NO_SIGNAL')
            exit_strength_raw = exit_signals.get('signal_strength', 0)

            # 确保 exit_strength 是数字（修正字符串比较错误）
            try:
                exit_strength = float(exit_strength_raw) if exit_strength_raw is not None else 0
            except (ValueError, TypeError):
                exit_strength = 0

            if exit_signal in ['STRONG_EXIT', 'EXIT'] and exit_strength > 60:
                if recommendation['action'] == 'BUY':
                    recommendation['action'] = 'HOLD'
                recommendation['warnings'].append(f"出场信号: {exit_signal}")
                recommendation['confidence'] -= 10

            # 基于风险评估
            risk_level = risk_assessment.get('tactical_risk_level', 'UNKNOWN')
            if risk_level == 'HIGH':
                recommendation['confidence'] -= 20
                recommendation['warnings'].append("战术风险较高")
            elif risk_level == 'LOW':
                recommendation['confidence'] += 10
                recommendation['reasons'].append("战术风险较低")

            # 限制置信度范围
            recommendation['confidence'] = max(0, min(100, recommendation['confidence']))

            return recommendation

        except Exception as e:
            return {
                'action': 'HOLD',
                'confidence': 50,
                'reasons': [],
                'warnings': [f"建议生成异常: {str(e)}"]
            }
