#!/usr/bin/env python3
"""
异步任务执行引擎

管理分析任务的生命周期，包括任务队列、并发控制、错误重试和状态更新
"""

import asyncio
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.analysis_log_dao import analysis_log_dao
from src.cs2_investment.fx.integrated_analysis_system import IntegratedAnalysisSystem
from src.cs2_investment.fx.integrated_ssync_system import IntegratedSSyncSystem

# 使用统一日志组件
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(Enum):
    """任务类型枚举"""
    REGULAR = "regular"
    REALTIME = "realtime"


@dataclass
class TaskInfo:
    """任务信息数据类"""
    task_id: int
    item_id: str
    item_name: str
    item_url: str
    analysis_type: TaskType
    retry_count: int = 0
    max_retries: int = 3
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


class TaskEngine:
    """异步任务执行引擎"""
    
    def __init__(self, max_concurrent_tasks: int = 3, max_retries: int = 3):
        """
        初始化任务执行引擎
        
        Args:
            max_concurrent_tasks: 最大并发任务数
            max_retries: 最大重试次数
        """
        self.max_concurrent_tasks = max_concurrent_tasks
        self.max_retries = max_retries
        
        # 任务队列和状态管理
        self.task_queue = asyncio.Queue()
        self.running_tasks: Dict[int, asyncio.Task] = {}
        self.cancelled_tasks: Set[int] = set()
        
        # 统计信息
        self.stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'cancelled': 0,
            'retried': 0
        }
        
        # 分析系统实例
        self.regular_analysis_system = None
        self.realtime_analysis_system = None
        
        # 引擎状态
        self.is_running = False
        self.worker_tasks = []
        
        logger.info(f"🚀 任务执行引擎初始化完成 - 最大并发数: {max_concurrent_tasks}")
    
    def _initialize_analysis_systems(self):
        """初始化分析系统"""
        try:
            if self.regular_analysis_system is None:
                self.regular_analysis_system = IntegratedAnalysisSystem()
                logger.info("✅ 常规分析系统初始化成功")
            
            if self.realtime_analysis_system is None:
                self.realtime_analysis_system = IntegratedSSyncSystem()
                logger.info("✅ 实时监控系统初始化成功")
                
        except Exception as e:
            logger.error(f"❌ 分析系统初始化失败: {e}")
            raise
    
    async def start(self):
        """启动任务执行引擎"""
        if self.is_running:
            logger.warning("任务执行引擎已在运行中")
            return
        
        self.is_running = True
        logger.info("🚀 启动任务执行引擎")
        
        # 初始化分析系统
        self._initialize_analysis_systems()
        
        # 启动工作线程
        for i in range(self.max_concurrent_tasks):
            worker_task = asyncio.create_task(self._worker(f"worker-{i+1}"))
            self.worker_tasks.append(worker_task)
        
        logger.info(f"✅ 任务执行引擎启动完成 - {len(self.worker_tasks)} 个工作线程")
    
    async def stop(self):
        """停止任务执行引擎"""
        if not self.is_running:
            return
        
        logger.info("🛑 停止任务执行引擎")
        self.is_running = False
        
        # 取消所有工作线程
        for task in self.worker_tasks:
            task.cancel()
        
        # 等待工作线程结束
        await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        self.worker_tasks.clear()
        
        logger.info("✅ 任务执行引擎已停止")
    
    async def submit_task(self, task_info: TaskInfo):
        """提交任务到队列"""
        await self.task_queue.put(task_info)
        logger.info(f"📋 任务已提交到队列: {task_info.task_id} - {task_info.item_name}")
    
    def cancel_task(self, task_id: int):
        """取消任务"""
        self.cancelled_tasks.add(task_id)
        
        # 如果任务正在运行，取消它
        if task_id in self.running_tasks:
            self.running_tasks[task_id].cancel()
            logger.info(f"❌ 正在运行的任务已取消: {task_id}")
        else:
            logger.info(f"❌ 任务已标记为取消: {task_id}")
    
    async def _worker(self, worker_name: str):
        """工作线程"""
        logger.info(f"👷 工作线程启动: {worker_name}")
        
        while self.is_running:
            try:
                # 从队列获取任务
                task_info = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)
                
                # 检查任务是否已被取消
                if task_info.task_id in self.cancelled_tasks:
                    logger.info(f"⏭️ 跳过已取消的任务: {task_info.task_id}")
                    self.cancelled_tasks.discard(task_info.task_id)
                    self.stats['cancelled'] += 1
                    continue
                
                # 执行任务
                execution_task = asyncio.create_task(
                    self._execute_task(task_info, worker_name)
                )
                self.running_tasks[task_info.task_id] = execution_task
                
                try:
                    await execution_task
                finally:
                    # 清理运行中的任务记录
                    self.running_tasks.pop(task_info.task_id, None)
                    self.cancelled_tasks.discard(task_info.task_id)
                
            except asyncio.TimeoutError:
                # 队列为空，继续等待
                continue
            except asyncio.CancelledError:
                logger.info(f"👷 工作线程被取消: {worker_name}")
                break
            except Exception as e:
                logger.error(f"👷 工作线程异常: {worker_name} - {e}")
        
        logger.info(f"👷 工作线程结束: {worker_name}")
    
    async def _execute_task(self, task_info: TaskInfo, worker_name: str):
        """执行单个任务"""
        task_id = task_info.task_id
        start_time = datetime.now()
        
        try:
            logger.info(f"🚀 [{worker_name}] 开始执行任务: {task_id} - {task_info.item_name}")
            
            # 更新任务状态为运行中
            analysis_log_dao.update_analysis_status(
                task_id,
                TaskStatus.RUNNING.value,
                start_time=start_time
            )
            
            # 根据任务类型选择分析系统
            if task_info.analysis_type == TaskType.REGULAR:
                result = await self.regular_analysis_system.run_complete_analysis(
                    task_info.item_url,
                    task_info.item_name
                )
            elif task_info.analysis_type == TaskType.REALTIME:
                result = await self.realtime_analysis_system.run_complete_analysis(
                    task_info.item_url,
                    task_info.item_name
                )
            else:
                raise ValueError(f"不支持的任务类型: {task_info.analysis_type}")
            
            # 任务执行成功
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            analysis_log_dao.update_analysis_status(
                task_id,
                TaskStatus.SUCCESS.value,
                end_time=end_time,
                result_path=result.get('report_path', '')
            )
            
            self.stats['successful'] += 1
            self.stats['total_processed'] += 1
            
            logger.info(f"✅ [{worker_name}] 任务执行成功: {task_id} - 耗时: {duration:.2f}秒")
            
        except asyncio.CancelledError:
            # 任务被取消
            end_time = datetime.now()
            analysis_log_dao.update_analysis_status(
                task_id,
                TaskStatus.CANCELLED.value,
                end_time=end_time,
                error_message="任务被取消"
            )
            
            self.stats['cancelled'] += 1
            self.stats['total_processed'] += 1
            
            logger.info(f"❌ [{worker_name}] 任务被取消: {task_id}")
            raise
            
        except Exception as e:
            # 任务执行失败
            end_time = datetime.now()
            error_message = str(e)
            
            logger.error(f"❌ [{worker_name}] 任务执行失败: {task_id} - {error_message}")
            
            # 检查是否需要重试
            if task_info.retry_count < self.max_retries:
                task_info.retry_count += 1
                self.stats['retried'] += 1
                
                logger.info(f"🔄 [{worker_name}] 任务重试: {task_id} - 第{task_info.retry_count}次重试")
                
                # 延迟后重新提交任务
                await asyncio.sleep(2 ** task_info.retry_count)  # 指数退避
                await self.submit_task(task_info)
                
                # 更新状态为等待重试
                analysis_log_dao.update_analysis_status(
                    task_id,
                    TaskStatus.PENDING.value,
                    error_message=f"重试中 ({task_info.retry_count}/{self.max_retries}): {error_message}"
                )
            else:
                # 重试次数已用完，标记为失败
                analysis_log_dao.update_analysis_status(
                    task_id,
                    TaskStatus.FAILED.value,
                    end_time=end_time,
                    error_message=f"重试失败 ({self.max_retries}次): {error_message}"
                )
                
                self.stats['failed'] += 1
                self.stats['total_processed'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        return {
            'is_running': self.is_running,
            'queue_size': self.task_queue.qsize(),
            'running_tasks_count': len(self.running_tasks),
            'cancelled_tasks_count': len(self.cancelled_tasks),
            'worker_count': len(self.worker_tasks),
            'max_concurrent_tasks': self.max_concurrent_tasks,
            'stats': self.stats.copy()
        }
    
    def get_running_tasks(self) -> Dict[int, str]:
        """获取正在运行的任务列表"""
        return {task_id: f"Task-{task_id}" for task_id in self.running_tasks.keys()}


# 全局任务引擎实例
task_engine = TaskEngine(max_concurrent_tasks=3, max_retries=3)
