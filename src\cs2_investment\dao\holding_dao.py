"""
持仓数据访问对象

提供持仓相关的数据库操作。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import desc, and_, func
from decimal import Decimal

from .base_dao import BaseDAO
from ..models.holding_record import HoldingRecord
from ..models.holding_transaction import HoldingTransaction
from ..config.database import get_db_session


class HoldingDAO(BaseDAO[HoldingRecord]):
    """持仓DAO"""
    
    def __init__(self):
        super().__init__(HoldingRecord)
    
    def get_user_holdings(self, user_id: str, limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """获取用户持仓列表，返回字典格式避免Session问题"""
        try:
            with get_db_session() as session:
                holdings = session.query(HoldingRecord).filter(
                    HoldingRecord.user_id == user_id
                ).filter(
                    HoldingRecord.total_quantity > 0  # 只返回有持仓的记录
                ).order_by(desc(HoldingRecord.updated_at))\
                .offset(offset).limit(limit).all()

                # 立即转换为字典格式
                results = []
                for holding in holdings:
                    holding_dict = {
                        'id': holding.id,
                        'user_id': holding.user_id,
                        'item_id': holding.item_id,
                        'item_name': holding.item_name,
                        'total_quantity': holding.total_quantity,
                        'available_quantity': holding.available_quantity,
                        'average_cost': float(holding.average_cost) if holding.average_cost else 0,
                        'total_cost': float(holding.total_cost) if holding.total_cost else 0,
                        'created_at': holding.created_at,
                        'updated_at': holding.updated_at
                    }
                    results.append(holding_dict)

                return results

        except SQLAlchemyError as e:
            self.logger.error(f"获取用户持仓列表失败: {e}")
            raise
    
    def get_holding_by_user_item(self, user_id: str, item_id: str) -> Optional[dict]:
        """根据用户ID和饰品ID获取持仓记录"""
        try:
            with get_db_session() as session:
                holding = session.query(HoldingRecord).filter(
                    and_(
                        HoldingRecord.user_id == user_id,
                        HoldingRecord.item_id == item_id
                    )
                ).first()

                if holding:
                    # 在会话内提取所有需要的数据
                    result = {
                        'id': holding.id,
                        'user_id': holding.user_id,
                        'item_id': holding.item_id,
                        'item_name': holding.item_name,
                        'total_quantity': holding.total_quantity,
                        'available_quantity': holding.available_quantity,
                        'average_cost': holding.average_cost,
                        'total_cost': holding.total_cost,
                        'created_at': holding.created_at,
                        'updated_at': holding.updated_at
                    }
                    return result

                return None

        except SQLAlchemyError as e:
            self.logger.error(f"获取持仓记录失败: {e}")
            raise
    
    def update_holding_after_transaction(self, user_id: str, item_id: str, item_name: str,
                                       transaction_type: str, quantity: int, price: Decimal) -> dict:
        """交易后更新持仓记录"""
        try:
            with get_db_session() as session:
                # 查找现有持仓记录
                holding = session.query(HoldingRecord).filter(
                    and_(
                        HoldingRecord.user_id == user_id,
                        HoldingRecord.item_id == item_id
                    )
                ).first()

                if transaction_type == 'BUY':
                    if holding:
                        # 更新现有持仓
                        old_total_cost = holding.total_cost
                        old_quantity = holding.total_quantity
                        new_total_cost = old_total_cost + (price * quantity)
                        new_quantity = old_quantity + quantity
                        
                        holding.total_quantity = new_quantity
                        holding.available_quantity = new_quantity
                        holding.total_cost = new_total_cost
                        holding.average_cost = new_total_cost / new_quantity
                        holding.updated_at = datetime.now()
                    else:
                        # 创建新持仓记录
                        total_cost = price * quantity
                        holding = HoldingRecord(
                            user_id=user_id,
                            item_id=item_id,
                            item_name=item_name,
                            total_quantity=quantity,
                            available_quantity=quantity,
                            average_cost=price,
                            total_cost=total_cost
                        )
                        session.add(holding)
                
                elif transaction_type == 'SELL':
                    if not holding or holding.available_quantity < quantity:
                        raise ValueError(f"可用数量不足，当前可用: {holding.available_quantity if holding else 0}, 尝试卖出: {quantity}")
                    
                    # 更新持仓数量
                    holding.total_quantity -= quantity
                    holding.available_quantity -= quantity
                    holding.updated_at = datetime.now()
                    
                    # 如果全部卖出，平均成本保持不变；如果部分卖出，平均成本也保持不变
                    # 总成本按比例减少
                    if holding.total_quantity > 0:
                        holding.total_cost = holding.average_cost * holding.total_quantity
                    else:
                        # 全部卖出，重置成本
                        holding.total_cost = Decimal('0')
                        holding.average_cost = Decimal('0')

                session.flush()
                session.refresh(holding)

                # 在会话内提取所有需要的数据
                result = {
                    'id': holding.id,
                    'user_id': holding.user_id,
                    'item_id': holding.item_id,
                    'item_name': holding.item_name,
                    'total_quantity': holding.total_quantity,
                    'available_quantity': holding.available_quantity,
                    'average_cost': holding.average_cost,
                    'total_cost': holding.total_cost,
                    'created_at': holding.created_at,
                    'updated_at': holding.updated_at
                }

                self.logger.info(f"更新持仓成功: user_id={user_id}, item_id={item_id}, type={transaction_type}, quantity={quantity}")
                return result

        except SQLAlchemyError as e:
            self.logger.error(f"更新持仓失败: {e}")
            raise
        except ValueError as e:
            self.logger.error(f"更新持仓失败: {e}")
            raise
    
    def calculate_holding_summary(self, user_id: str) -> Dict[str, Any]:
        """计算用户持仓汇总信息"""
        try:
            with get_db_session() as session:
                # 统计持仓数量和总成本
                result = session.query(
                    func.count(HoldingRecord.id).label('total_holdings'),
                    func.sum(HoldingRecord.total_quantity).label('total_quantity'),
                    func.sum(HoldingRecord.total_cost).label('total_cost')
                ).filter(
                    and_(
                        HoldingRecord.user_id == user_id,
                        HoldingRecord.total_quantity > 0
                    )
                ).first()

                return {
                    'total_holdings': result.total_holdings or 0,
                    'total_quantity': result.total_quantity or 0,
                    'total_cost': float(result.total_cost) if result.total_cost else 0.0
                }

        except SQLAlchemyError as e:
            self.logger.error(f"计算持仓汇总失败: {e}")
            return {
                'total_holdings': 0,
                'total_quantity': 0,
                'total_cost': 0.0
            }
    
    def get_holding_count(self, user_id: str) -> int:
        """获取用户持仓数量"""
        try:
            with get_db_session() as session:
                count = session.query(HoldingRecord).filter(
                    and_(
                        HoldingRecord.user_id == user_id,
                        HoldingRecord.total_quantity > 0
                    )
                ).count()

                return count

        except SQLAlchemyError as e:
            self.logger.error(f"获取持仓数量失败: {e}")
            return 0

    def delete_holding(self, user_id: str, item_id: str) -> bool:
        """删除持仓记录"""
        try:
            with get_db_session() as session:
                result = session.query(HoldingRecord).filter(
                    HoldingRecord.user_id == user_id,
                    HoldingRecord.item_id == item_id
                ).delete()

                session.commit()
                return result > 0

        except SQLAlchemyError as e:
            self.logger.error(f"删除持仓记录失败: {e}")
            raise

    def get_holdings_count(self, user_id: str) -> int:
        """获取用户持仓总数"""
        try:
            with get_db_session() as session:
                count = session.query(HoldingRecord).filter(
                    HoldingRecord.user_id == user_id,
                    HoldingRecord.total_quantity > 0  # 只统计有持仓的记录
                ).count()

                return count

        except SQLAlchemyError as e:
            self.logger.error(f"获取持仓总数失败: {e}")
            return 0
