"""
智能监控配置数据访问对象

提供智能监控配置的数据库操作功能，包括用户个性化配置管理、
触发条件设置、通知配置等功能。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy import desc, func, and_, or_
from sqlalchemy.exc import SQLAlchemyError
from loguru import logger

from .base_dao import BaseDAO
from ..models.intelligent_monitoring_config import IntelligentMonitoringConfig
from ..config.database import get_db_session


class IntelligentMonitoringConfigDAO(BaseDAO[IntelligentMonitoringConfig]):
    """智能监控配置DAO"""
    
    def __init__(self):
        super().__init__(IntelligentMonitoringConfig)
        self.logger = logger.bind(dao=self.__class__.__name__)
    
    def create_config(self, config_data: Dict[str, Any]) -> Optional[IntelligentMonitoringConfig]:
        """创建监控配置"""
        try:
            with get_db_session() as session:
                config = IntelligentMonitoringConfig(**config_data)
                session.add(config)
                session.flush()
                session.refresh(config)
                self.logger.info(f"创建监控配置成功: {config.id} - {config.user_id}/{config.item_id}")
                
                session.expunge(config)
                return config
        except SQLAlchemyError as e:
            self.logger.error(f"创建监控配置失败: {e}")
            raise
    
    def get_user_config(self, user_id: str, item_id: str) -> Optional[IntelligentMonitoringConfig]:
        """获取用户的监控配置"""
        try:
            with get_db_session() as session:
                config = session.query(IntelligentMonitoringConfig).filter(
                    and_(
                        IntelligentMonitoringConfig.user_id == user_id,
                        IntelligentMonitoringConfig.item_id == item_id
                    )
                ).first()
                
                if config:
                    session.expunge(config)
                
                return config
        except SQLAlchemyError as e:
            self.logger.error(f"获取用户监控配置失败: {e}")
            raise
    
    def get_user_all_configs(self, user_id: str) -> List[IntelligentMonitoringConfig]:
        """获取用户的所有监控配置"""
        try:
            with get_db_session() as session:
                configs = session.query(IntelligentMonitoringConfig).filter(
                    IntelligentMonitoringConfig.user_id == user_id
                ).order_by(desc(IntelligentMonitoringConfig.updated_at)).all()
                
                for config in configs:
                    session.expunge(config)
                
                return configs
        except SQLAlchemyError as e:
            self.logger.error(f"获取用户所有监控配置失败: {e}")
            raise
    
    def get_enabled_configs(self, item_id: str = None) -> List[IntelligentMonitoringConfig]:
        """获取启用的监控配置"""
        try:
            with get_db_session() as session:
                query = session.query(IntelligentMonitoringConfig).filter(
                    IntelligentMonitoringConfig.monitoring_enabled == True
                )
                
                if item_id:
                    query = query.filter(IntelligentMonitoringConfig.item_id == item_id)
                
                configs = query.order_by(IntelligentMonitoringConfig.update_frequency).all()
                
                for config in configs:
                    session.expunge(config)
                
                return configs
        except SQLAlchemyError as e:
            self.logger.error(f"获取启用监控配置失败: {e}")
            raise
    
    def update_config(self, user_id: str, item_id: str, updates: Dict[str, Any]) -> Optional[IntelligentMonitoringConfig]:
        """更新监控配置"""
        try:
            with get_db_session() as session:
                config = session.query(IntelligentMonitoringConfig).filter(
                    and_(
                        IntelligentMonitoringConfig.user_id == user_id,
                        IntelligentMonitoringConfig.item_id == item_id
                    )
                ).first()
                
                if config:
                    for key, value in updates.items():
                        if hasattr(config, key):
                            setattr(config, key, value)
                    
                    session.flush()
                    session.refresh(config)
                    self.logger.info(f"更新监控配置成功: {config.id}")
                    
                    session.expunge(config)
                    return config
                else:
                    self.logger.warning(f"未找到用户{user_id}的饰品{item_id}监控配置")
                    return None
        except SQLAlchemyError as e:
            self.logger.error(f"更新监控配置失败: {e}")
            raise
    
    def create_or_update_config(self, user_id: str, item_id: str, config_data: Dict[str, Any]) -> IntelligentMonitoringConfig:
        """创建或更新监控配置"""
        try:
            existing_config = self.get_user_config(user_id, item_id)
            
            if existing_config:
                # 更新现有配置
                updated_config = self.update_config(user_id, item_id, config_data)
                return updated_config
            else:
                # 创建新配置
                config_data.update({'user_id': user_id, 'item_id': item_id})
                new_config = self.create_config(config_data)
                return new_config
        except SQLAlchemyError as e:
            self.logger.error(f"创建或更新监控配置失败: {e}")
            raise
    
    def enable_monitoring(self, user_id: str, item_id: str) -> bool:
        """启用监控"""
        try:
            updated_config = self.update_config(user_id, item_id, {'monitoring_enabled': True})
            return updated_config is not None
        except SQLAlchemyError as e:
            self.logger.error(f"启用监控失败: {e}")
            raise
    
    def disable_monitoring(self, user_id: str, item_id: str) -> bool:
        """禁用监控"""
        try:
            updated_config = self.update_config(user_id, item_id, {'monitoring_enabled': False})
            return updated_config is not None
        except SQLAlchemyError as e:
            self.logger.error(f"禁用监控失败: {e}")
            raise
    
    def get_configs_by_frequency(self, frequency_range: tuple = None) -> List[IntelligentMonitoringConfig]:
        """根据更新频率获取配置"""
        try:
            with get_db_session() as session:
                query = session.query(IntelligentMonitoringConfig).filter(
                    IntelligentMonitoringConfig.monitoring_enabled == True
                )
                
                if frequency_range:
                    min_freq, max_freq = frequency_range
                    query = query.filter(
                        and_(
                            IntelligentMonitoringConfig.update_frequency >= min_freq,
                            IntelligentMonitoringConfig.update_frequency <= max_freq
                        )
                    )
                
                configs = query.order_by(IntelligentMonitoringConfig.update_frequency).all()
                
                for config in configs:
                    session.expunge(config)
                
                return configs
        except SQLAlchemyError as e:
            self.logger.error(f"根据频率获取监控配置失败: {e}")
            raise
    
    def get_notification_enabled_configs(self, notification_type: str = None) -> List[IntelligentMonitoringConfig]:
        """获取启用通知的配置"""
        try:
            with get_db_session() as session:
                query = session.query(IntelligentMonitoringConfig).filter(
                    IntelligentMonitoringConfig.monitoring_enabled == True
                )
                
                if notification_type == 'email':
                    query = query.filter(IntelligentMonitoringConfig.email_notification == True)
                elif notification_type == 'desktop':
                    query = query.filter(IntelligentMonitoringConfig.desktop_notification == True)
                else:
                    # 任意一种通知方式启用
                    query = query.filter(
                        or_(
                            IntelligentMonitoringConfig.email_notification == True,
                            IntelligentMonitoringConfig.desktop_notification == True
                        )
                    )
                
                configs = query.all()
                
                for config in configs:
                    session.expunge(config)
                
                return configs
        except SQLAlchemyError as e:
            self.logger.error(f"获取通知启用配置失败: {e}")
            raise
    
    def get_config_statistics(self) -> Dict[str, Any]:
        """获取配置统计信息"""
        try:
            with get_db_session() as session:
                # 总配置数
                total_configs = session.query(IntelligentMonitoringConfig).count()
                
                # 启用的配置数
                enabled_configs = session.query(IntelligentMonitoringConfig).filter(
                    IntelligentMonitoringConfig.monitoring_enabled == True
                ).count()
                
                # 通知方式分布
                email_enabled = session.query(IntelligentMonitoringConfig).filter(
                    IntelligentMonitoringConfig.email_notification == True
                ).count()
                
                desktop_enabled = session.query(IntelligentMonitoringConfig).filter(
                    IntelligentMonitoringConfig.desktop_notification == True
                ).count()
                
                # 通知阈值分布
                threshold_dist = session.query(
                    IntelligentMonitoringConfig.notification_threshold,
                    func.count(IntelligentMonitoringConfig.id).label('count')
                ).group_by(IntelligentMonitoringConfig.notification_threshold).all()
                
                # 更新频率统计
                freq_stats = session.query(
                    func.avg(IntelligentMonitoringConfig.update_frequency).label('avg_frequency'),
                    func.max(IntelligentMonitoringConfig.update_frequency).label('max_frequency'),
                    func.min(IntelligentMonitoringConfig.update_frequency).label('min_frequency')
                ).first()
                
                return {
                    'total_configurations': total_configs,
                    'enabled_configurations': enabled_configs,
                    'notification_statistics': {
                        'email_enabled': email_enabled,
                        'desktop_enabled': desktop_enabled
                    },
                    'threshold_distribution': {threshold: count for threshold, count in threshold_dist},
                    'frequency_statistics': {
                        'average': int(freq_stats.avg_frequency) if freq_stats.avg_frequency else None,
                        'maximum': freq_stats.max_frequency,
                        'minimum': freq_stats.min_frequency
                    }
                }
        except SQLAlchemyError as e:
            self.logger.error(f"获取配置统计失败: {e}")
            raise
    
    def delete_user_config(self, user_id: str, item_id: str) -> bool:
        """删除用户配置"""
        try:
            with get_db_session() as session:
                config = session.query(IntelligentMonitoringConfig).filter(
                    and_(
                        IntelligentMonitoringConfig.user_id == user_id,
                        IntelligentMonitoringConfig.item_id == item_id
                    )
                ).first()
                
                if config:
                    session.delete(config)
                    self.logger.info(f"删除监控配置成功: {user_id}/{item_id}")
                    return True
                else:
                    self.logger.warning(f"未找到要删除的监控配置: {user_id}/{item_id}")
                    return False
        except SQLAlchemyError as e:
            self.logger.error(f"删除监控配置失败: {e}")
            raise
