#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品分析系统 - 重构主分析系统
统一数据粒度，规范分析流程
"""

from data_manager import DataManager
from technical_indicator_calculator import TechnicalIndicatorCalculator
from enhanced_risk_assessor import EnhancedRiskAssessment
from market_sentiment_analyzer import MarketSentimentAnalyzer
from strategy_selector import StrategySelector
from simple_report_generator import UserFriendlyReportGenerator

import pandas as pd
import numpy as np
from typing import Dict, List

class CS2AnalysisSystemV2:
    """CS2饰品分析系统 V2.0 - 重构版本"""
    
    def __init__(self, skin_name: str):
        self.skin_name = skin_name
        
        # 初始化数据管理器
        self.data_manager = DataManager(skin_name)
        
        # 初始化技术指标计算器
        self.tech_calculator = None
        
        # 分析结果存储
        self.analysis_results = {}
        
    def run_complete_analysis(self) -> Dict:
        """运行完整分析流程"""
        
        print(f"🚀 开始分析 {self.skin_name} (重构版本)")
        print("="*60)
        
        # 第一步：数据加载与验证
        if not self._load_and_validate_data():
            return {'error': '数据加载失败'}
        
        # 第二步：技术指标计算
        self._calculate_technical_indicators()
        
        # 第三步：分层分析
        self._run_layered_analysis()
        
        # 第四步：生成技术分析报告
        self._generate_technical_analysis_report()

        # 第五步：生成综合报告
        final_report = self._generate_final_report()

        print("="*60)
        print("✅ 分析完成")

        return final_report

    def generate_professional_dashboard(self, save_path=None, show_chart=False):
        """
        生成专业分析仪表板

        Args:
            save_path: 保存路径，默认为 "{饰品名}_专业仪表板.png"
            show_chart: 是否显示图表

        Returns:
            str: 保存的文件路径
        """
        try:
            # 导入专业图表系统
            from professional_chart_system import ProfessionalChartSystem

            # 设置默认保存路径
            if save_path is None:
                safe_name = self.skin_name.replace('/', '_').replace('\\', '_')
                save_path = f"{safe_name}_专业仪表板.png"

            # 创建专业图表系统
            chart_system = ProfessionalChartSystem(self)

            # 生成综合仪表板
            chart_system.generate_comprehensive_dashboard(
                save_path=save_path,
                show_chart=show_chart
            )

            return save_path

        except ImportError:
            print("❌ 专业图表系统模块未找到，请确保 专业图表生成系统.py 文件存在")
            return None
        except Exception as e:
            print(f"❌ 专业仪表板生成失败: {e}")
            return None

    def _load_and_validate_data(self) -> bool:
        """数据加载与验证"""
        print("\n📊 第一步：数据加载与验证")
        
        # 加载所有数据
        if not self.data_manager.load_all_data():
            return False
        
        # 数据一致性验证
        consistency = self.data_manager.validate_data_consistency()
        if 'error' not in consistency:
            print(f"✅ 数据一致性验证: {consistency['consistency_rate']:.1f}%")
            if not consistency['is_consistent']:
                print("⚠️ 警告: 数据一致性较低，分析结果可能存在偏差")
        
        return True
    
    def _calculate_technical_indicators(self):
        """技术指标计算"""
        print("\n📈 第二步：技术指标计算 (基于日K数据)")
        
        # 获取数据
        daily_data = self.data_manager.get_daily_data()
        weekly_data = self.data_manager.get_weekly_data()
        
        # 初始化技术指标计算器
        self.tech_calculator = TechnicalIndicatorCalculator(daily_data, weekly_data)
        
        # 计算所有指标
        indicators = self.tech_calculator.calculate_all_indicators()
        
        # 获取当前信号
        current_signals = self.tech_calculator.get_current_signals()
        
        self.analysis_results['technical_indicators'] = indicators
        self.analysis_results['current_signals'] = current_signals
        
        print(f"✅ 技术指标计算完成 (数据源: {current_signals['data_source']})")
    
    def _run_layered_analysis(self):
        """分层分析"""
        print("\n🔍 第三步：分层分析")
        
        # 第一层：市场特征分析 (基于日K数据)
        self._layer1_market_characteristics()
        
        # 第二层：技术信号分析 (基于日K数据)
        self._layer2_technical_signals()
        
        # 第三层：基本面分析 (基于走势数据)
        self._layer3_fundamental_analysis()
        
        # 第四层：多时间框架分析 (日K + 周K)
        self._layer4_multi_timeframe()
        
        # 第五层：风险评估 (基于日K数据)
        self._layer5_risk_assessment()
        
        # 第六层：交易建议 (综合分析)
        self._layer6_trading_advice()

        # 第七层：市场情绪分析 (迁移原系统功能)
        self._layer7_market_sentiment()

        # 第八层：策略选择 (智能策略匹配)
        self._layer8_strategy_selection()
    
    def _layer1_market_characteristics(self):
        """第一层：市场特征分析"""
        print("   🏷️ 第一层：市场特征分析 (基于日K数据)")
        
        market_chars = self.data_manager.get_market_characteristics()
        
        print(f"      市场类型: {market_chars['market_type']}")
        print(f"      日均成交量: {market_chars['avg_daily_volume']:.1f}件")
        print(f"      日波动率: {market_chars['daily_volatility']:.2f}%")
        print(f"      流动性水平: {market_chars['liquidity_level']}")
        print(f"      数据来源: {market_chars['data_source']}")
        
        self.analysis_results['market_characteristics'] = market_chars
    
    def _layer2_technical_signals(self):
        """第二层：技术信号分析"""
        print("   📊 第二层：技术信号分析 (基于日K数据)")
        
        signals = self.analysis_results['current_signals']
        
        print(f"      当前价格: {signals['price']:.2f}元")
        print(f"      RSI: {signals['rsi']:.1f} ({signals['rsi_signal']})")
        print(f"      MACD趋势: {signals['macd_trend']}")
        print(f"      EMA趋势: {signals['ema_trend']}")
        print(f"      综合信号: {signals['overall_signal']}")
        print(f"      数据来源: {signals['data_source']}")
        
        # 支撑阻力位
        levels = self.tech_calculator.get_support_resistance_levels()
        print(f"      支撑位: {levels['recent_support']:.2f}元")
        print(f"      阻力位: {levels['recent_resistance']:.2f}元")
        
        self.analysis_results['support_resistance'] = levels
    
    def _layer3_fundamental_analysis(self):
        """第三层：基本面分析"""
        print("   🏢 第三层：基本面分析 (基于走势数据)")
        
        snapshot = self.data_manager.get_current_market_snapshot()
        
        print(f"      当前价格: {snapshot['current_price']:.2f}元")
        print(f"      在售数量: {snapshot['supply']:.0f}件")
        print(f"      求购溢价: {snapshot['bid_premium']:+.2f}%")
        print(f"      供给比例: {snapshot['supply_ratio']:.2f}%")
        print(f"      求购深度: {snapshot['bid_depth']:.2f}%")
        print(f"      数据来源: {snapshot['data_source']}")
        
        self.analysis_results['fundamental_snapshot'] = snapshot
    
    def _layer4_multi_timeframe(self):
        """第四层：多时间框架分析"""
        print("   ⏰ 第四层：多时间框架分析 (日K + 周K)")
        
        # 日线趋势
        daily_trend = self.analysis_results['current_signals']['ema_trend']
        
        # 周线趋势
        weekly_trend = self.analysis_results['technical_indicators'].get('weekly_trend', 'UNKNOWN')
        
        print(f"      日线趋势: {daily_trend}")
        print(f"      周线趋势: {weekly_trend}")
        
        # 趋势一致性
        if daily_trend == 'UP' and weekly_trend == 'UP':
            trend_consistency = '强烈上升'
        elif daily_trend == 'DOWN' and weekly_trend == 'DOWN':
            trend_consistency = '强烈下降'
        elif weekly_trend == 'UNKNOWN':
            trend_consistency = '周线数据不足'
        else:
            trend_consistency = '趋势分歧'
        
        print(f"      趋势一致性: {trend_consistency}")
        
        self.analysis_results['multi_timeframe'] = {
            'daily_trend': daily_trend,
            'weekly_trend': weekly_trend,
            'trend_consistency': trend_consistency
        }
    
    def _layer5_risk_assessment(self):
        """第五层：增强风险评估 (长期 + 短期 + 异常)"""
        print("   ⚠️ 第五层：增强风险评估 (长期 + 短期 + 异常)")

        # 获取数据
        daily_data = self.data_manager.get_daily_data()
        hourly_data = self.data_manager.hourly_data  # 直接访问，用于短期风险监控
        trend_data = self.data_manager.get_trend_data()

        # 初始化增强风险评估器
        risk_assessor = EnhancedRiskAssessment(daily_data, hourly_data, trend_data)

        # 运行综合风险评估
        risk_results = risk_assessor.assess_comprehensive_risk()

        # 打印风险评估摘要
        risk_assessor.print_risk_summary()

        # 存储结果
        self.analysis_results['enhanced_risk_assessment'] = risk_results

        # 为了保持兼容性，也提供简化的风险评估
        overall_risk = risk_results['comprehensive_assessment']['overall_risk_level']
        self.analysis_results['risk_assessment'] = {
            'overall_risk': overall_risk,
            'comprehensive_score': risk_results['comprehensive_assessment']['comprehensive_score'],
            'risk_advice': risk_results['comprehensive_assessment']['risk_advice'],
            'data_source': '综合风险评估 (日K + 时K + 异常检测)'
        }
    
    def _layer6_trading_advice(self):
        """第六层：交易建议"""
        print("   💡 第六层：交易建议 (综合分析)")
        
        # 综合信号
        overall_signal = self.analysis_results['current_signals']['overall_signal']
        
        # 市场类型
        market_type = self.analysis_results['market_characteristics']['market_type']
        
        # 风险水平
        risk_level = self.analysis_results['risk_assessment']['overall_risk']
        
        # 生成建议
        if overall_signal == 'BULLISH' and risk_level != 'HIGH':
            action = 'BUY'
            confidence = 75 if risk_level == 'LOW' else 60
        elif overall_signal == 'BEARISH' and risk_level != 'HIGH':
            action = 'SELL'
            confidence = 75 if risk_level == 'LOW' else 60
        else:
            action = 'HOLD'
            confidence = 50
        
        # 仓位建议
        if market_type == '高活跃交易市场':
            if confidence >= 70:
                position_size = '20-35%'
            else:
                position_size = '10-25%'
        else:
            position_size = '5-15%'
        
        print(f"      交易建议: {action}")
        print(f"      置信度: {confidence}%")
        print(f"      建议仓位: {position_size}")
        print(f"      适合策略: 基于{market_type}的技术分析")
        
        self.analysis_results['trading_advice'] = {
            'action': action,
            'confidence': confidence,
            'position_size': position_size,
            'strategy_type': f"基于{market_type}的技术分析",
            'data_source': '综合分析 (统一标准)'
        }

    def _layer7_market_sentiment(self):
        """第七层：市场情绪分析 (迁移原系统功能)"""
        print("   😨 第七层：市场情绪分析 (迁移原系统功能)")

        # 获取数据
        daily_data = self.data_manager.get_daily_data()
        trend_data = self.data_manager.get_trend_data()
        technical_indicators = self.analysis_results['technical_indicators']
        risk_assessment = self.analysis_results

        # 初始化市场情绪分析器
        sentiment_analyzer = MarketSentimentAnalyzer(
            daily_data, trend_data, technical_indicators, risk_assessment
        )

        # 运行情绪分析
        sentiment_results = sentiment_analyzer.analyze_market_sentiment()

        # 打印情绪分析摘要
        sentiment_analyzer.print_sentiment_summary()

        # 存储结果
        self.analysis_results['market_sentiment'] = sentiment_results

        # 获取情绪摘要
        sentiment_summary = sentiment_analyzer.get_sentiment_summary()
        self.analysis_results['sentiment_summary'] = sentiment_summary

    def _layer8_strategy_selection(self):
        """第八层：策略选择 (智能策略匹配)"""
        print("   🎯 第八层：策略选择 (智能策略匹配)")

        # 获取分析结果
        market_characteristics = self.analysis_results['market_characteristics']
        technical_signals = self.analysis_results['current_signals']
        sentiment_analysis = self.analysis_results.get('market_sentiment', {})
        risk_assessment = self.analysis_results

        # 初始化策略选择器
        strategy_selector = StrategySelector(
            market_characteristics, technical_signals, sentiment_analysis, risk_assessment
        )

        # 运行策略选择
        strategy_results = strategy_selector.select_optimal_strategy()

        # 打印策略选择摘要
        strategy_selector.print_strategy_summary()

        # 存储结果
        self.analysis_results['strategy_selection'] = strategy_results

        # 获取策略摘要
        strategy_summary = strategy_selector.get_strategy_summary()
        self.analysis_results['strategy_summary'] = strategy_summary

    def _generate_technical_analysis_report(self):
        """生成技术分析报告"""
        print("\n📊 第四步：生成技术分析报告")

        try:
            from technical_analysis_report_generator import TechnicalAnalysisReportGenerator

            # 创建技术分析报告生成器
            report_generator = TechnicalAnalysisReportGenerator(
                item_name=self.skin_name,
                analysis_results=self.analysis_results,
                analysis_system=self
            )

            # 生成技术指标数据
            technical_indicators_data = report_generator.get_technical_indicators_data()

            # 将技术分析报告添加到分析结果中（不生成文件，由外部统一生成）
            self.analysis_results['technical_analysis_report'] = technical_indicators_data

            print("✅ 技术分析报告生成完成")
            print(f"✅ 包含指标数量: {len(technical_indicators_data.get('technical_indicators', {}))}")

        except ImportError as e:
            print(f"❌ 技术分析报告生成器导入失败: {e}")
            self.analysis_results['technical_analysis_report'] = {}
        except Exception as e:
            print(f"❌ 技术分析报告生成失败: {e}")
            self.analysis_results['technical_analysis_report'] = {}

    def _generate_final_report(self) -> Dict:
        """生成最终报告"""
        print("\n📋 第四步：生成综合报告")
        
        # 数据使用摘要
        data_summary = self.tech_calculator.get_data_summary()
        
        final_report = {
            'skin_name': self.skin_name,
            'analysis_version': 'V2.1 (重构版本)',
            'data_summary': data_summary,
            'market_characteristics': self.analysis_results['market_characteristics'],
            'technical_signals': self.analysis_results['current_signals'],
            'fundamental_snapshot': self.analysis_results['fundamental_snapshot'],
            'multi_timeframe_analysis': self.analysis_results['multi_timeframe'],
            'risk_assessment': self.analysis_results['risk_assessment'],
            'trading_advice': self.analysis_results['trading_advice'],
            'support_resistance_levels': self.analysis_results['support_resistance'],
            'market_sentiment': self.analysis_results.get('market_sentiment', {}),
            'sentiment_summary': self.analysis_results.get('sentiment_summary', {}),
            'strategy_selection': self.analysis_results.get('strategy_selection', {}),
            'strategy_summary': self.analysis_results.get('strategy_summary', {}),
            'technical_analysis_report': self.analysis_results.get('technical_analysis_report', {})
        }
        
        print("✅ 综合报告生成完成")
        print(f"✅ 数据标准: {data_summary['calculation_standard']}")

        # 注释掉通俗易懂的用户报告生成（已禁用）
        # self._generate_user_friendly_report()

        # 将空的用户报告添加到最终报告中（保持兼容性）
        final_report['user_friendly_report'] = {}
        final_report['report_summary'] = {}

        return final_report

    def _generate_user_friendly_report(self):
        """生成通俗易懂的用户报告"""
        print("\n📋 第五步：生成通俗易懂的用户报告")

        # 初始化通俗报告生成器
        report_generator = UserFriendlyReportGenerator(self.skin_name, self.analysis_results)

        # 生成通俗报告
        user_friendly_report = report_generator.generate_comprehensive_report()

        # 打印通俗报告
        report_generator.print_user_friendly_report()

        # 存储结果
        self.analysis_results['user_friendly_report'] = user_friendly_report

        # 获取报告摘要
        report_summary = report_generator.get_report_summary()
        self.analysis_results['report_summary'] = report_summary

        print("✅ 通俗易懂报告生成完成")

    def generate_technical_report(self, save_path=None) -> str:
        """
        生成技术分析报告

        Args:
            save_path: 可选的报告保存路径

        Returns:
            str: Markdown格式的技术分析报告

        Raises:
            ValueError: 如果分析结果不存在
        """
        try:
            print("\n📋 生成技术分析报告...")

            # 确保分析已完成
            if not hasattr(self, 'analysis_results') or not self.analysis_results:
                raise ValueError('请先运行complete_analysis()方法完成分析')

            # 导入技术分析报告生成器
            from technical_analysis_report_generator import TechnicalAnalysisReportGenerator

            # 创建报告生成器，传入analysis_system实例以便访问数据管理器
            report_generator = TechnicalAnalysisReportGenerator(
                self.skin_name,
                self.analysis_results,
                analysis_system=self  # 传入self以便访问data_manager
            )

            # 生成报告
            report_content = report_generator.generate_technical_report()

            # 获取技术指标数据并保存到分析结果中
            technical_indicators_data = report_generator.get_technical_indicators_data()
            if not hasattr(self, 'analysis_results'):
                self.analysis_results = {}
            self.analysis_results['technical_analysis_report'] = technical_indicators_data

            # 保存报告（如果指定路径）
            if save_path:
                with open(save_path, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                print(f"✅ 技术分析报告已保存至: {save_path}")

            print("✅ 技术分析报告生成完成")
            return report_content

        except ImportError as e:
            error_msg = f"❌ 技术分析报告生成器导入失败: {str(e)}"
            print(error_msg)
            raise ImportError(error_msg)
        except Exception as e:
            error_msg = f"❌ 技术分析报告生成失败: {str(e)}"
            print(error_msg)
            raise Exception(error_msg)

# 测试重构系统
if __name__ == "__main__":
    # 创建分析系统实例
    analyzer = CS2AnalysisSystemV2('AK-47 传承 (久经沙场)')
    
    # 运行完整分析
    result = analyzer.run_complete_analysis()
    
    if 'error' not in result:
        print("\n" + "="*60)
        print("📊 重构系统分析摘要:")
        print(f"市场类型: {result['market_characteristics']['market_type']}")
        print(f"技术信号: {result['technical_signals']['overall_signal']}")
        print(f"交易建议: {result['trading_advice']['action']} (置信度: {result['trading_advice']['confidence']}%)")
        if 'sentiment_summary' in result and result['sentiment_summary']:
            print(f"市场情绪: {result['sentiment_summary']['overall_sentiment']} - {result['sentiment_summary']['sentiment_description']}")
        if 'strategy_summary' in result and result['strategy_summary']:
            print(f"推荐策略: {result['strategy_summary']['strategy_name']} (评分: {result['strategy_summary']['strategy_score']:.1f})")
        print(f"数据标准: {result['data_summary']['calculation_standard']}")
        print("="*60)
