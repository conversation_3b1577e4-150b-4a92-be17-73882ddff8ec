"""
价格更新定时器包装器

作为现有SmartPriceUpdatePool的包装器，提供统一的定时器接口。
"""

import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.services.smart_price_update_pool import SmartPriceUpdatePool


class PriceUpdateScheduler:
    """价格更新定时器包装器"""
    
    def __init__(self, api_key: str, user_id: str = "default_user"):
        """
        初始化价格更新定时器包装器
        
        Args:
            api_key: SteamDT API密钥
            user_id: 用户ID
        """
        self.api_key = api_key
        self.user_id = user_id
        self.logger = logging.getLogger(__name__)
        
        # 创建智能价格更新池实例
        self.smart_update_pool = SmartPriceUpdatePool(api_key, user_id)
        
        # 包装器状态
        self.is_running = False
        self.start_time = None
        self.last_health_check = None
        self.health_check_interval = 300  # 5分钟健康检查间隔
        self.health_check_task = None
        
        # 统计信息
        self.wrapper_stats = {
            'start_count': 0,
            'stop_count': 0,
            'restart_count': 0,
            'health_check_count': 0,
            'health_check_failures': 0,
            'last_restart_time': None,
            'uptime_seconds': 0
        }
    
    async def start(self):
        """启动价格更新定时器"""
        if self.is_running:
            self.logger.warning("价格更新定时器已经在运行")
            return

        try:
            self.logger.info("🚀 启动价格更新定时器包装器")
            
            # 启动智能价格更新池
            await self.smart_update_pool.start()
            
            # 更新状态
            self.is_running = True
            self.start_time = datetime.now()
            self.wrapper_stats['start_count'] += 1
            
            # 启动健康检查任务
            self._start_health_check()
            
            self.logger.info("✅ 价格更新定时器包装器启动成功")
            
        except Exception as e:
            self.logger.error(f"❌ 启动价格更新定时器包装器失败: {e}")
            self.is_running = False
            raise
    
    def stop(self):
        """停止价格更新定时器"""
        if not self.is_running:
            return

        try:
            self.logger.info("⏹️ 停止价格更新定时器包装器")
            
            # 停止健康检查任务
            self._stop_health_check()
            
            # 停止智能价格更新池
            self.smart_update_pool.stop()
            
            # 更新状态
            self.is_running = False
            if self.start_time:
                uptime = (datetime.now() - self.start_time).total_seconds()
                self.wrapper_stats['uptime_seconds'] += uptime
            self.wrapper_stats['stop_count'] += 1
            
            self.logger.info("✅ 价格更新定时器包装器已停止")
            
        except Exception as e:
            self.logger.error(f"❌ 停止价格更新定时器包装器失败: {e}")
    
    def _start_health_check(self):
        """启动健康检查任务"""
        if self.health_check_task and not self.health_check_task.done():
            return
        
        self.health_check_task = asyncio.create_task(self._health_check_loop())
        self.logger.info(f"📊 健康检查任务已启动，间隔: {self.health_check_interval}秒")
    
    def _stop_health_check(self):
        """停止健康检查任务"""
        if self.health_check_task and not self.health_check_task.done():
            self.health_check_task.cancel()
            self.logger.info("📊 健康检查任务已停止")
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while self.is_running:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                if not self.is_running:
                    break
                
                await self._perform_health_check()
                
            except asyncio.CancelledError:
                self.logger.info("健康检查任务被取消")
                break
            except Exception as e:
                self.logger.error(f"健康检查循环异常: {e}")
                self.wrapper_stats['health_check_failures'] += 1
                await asyncio.sleep(60)  # 出错后等待1分钟再继续
    
    async def _perform_health_check(self):
        """执行健康检查"""
        try:
            self.logger.debug("🔍 执行价格更新池健康检查")
            
            # 获取更新池状态
            pool_status = self.smart_update_pool.get_status()
            
            # 检查更新池是否正常运行
            if not pool_status.get('is_running', False):
                self.logger.warning("⚠️ 检测到价格更新池未运行，尝试重启")
                await self._restart_update_pool()
                return
            
            # 检查队列状态
            queue_status = pool_status.get('queue_status', {})
            total_queue_size = (queue_status.get('high_priority_queue', 0) + 
                              queue_status.get('low_priority_queue', 0))
            
            # 记录健康检查结果
            self.last_health_check = datetime.now()
            self.wrapper_stats['health_check_count'] += 1
            
            self.logger.debug(f"✅ 健康检查完成 - 队列大小: {total_queue_size}, 处理中: {queue_status.get('processing_tasks', 0)}")
            
        except Exception as e:
            self.logger.error(f"❌ 健康检查失败: {e}")
            self.wrapper_stats['health_check_failures'] += 1
    
    async def _restart_update_pool(self):
        """重启价格更新池"""
        try:
            self.logger.info("🔄 重启价格更新池")
            
            # 停止当前更新池
            self.smart_update_pool.stop()
            await asyncio.sleep(2)  # 等待停止完成
            
            # 重新启动更新池
            await self.smart_update_pool.start()
            
            # 更新统计
            self.wrapper_stats['restart_count'] += 1
            self.wrapper_stats['last_restart_time'] = datetime.now()
            
            self.logger.info("✅ 价格更新池重启成功")
            
        except Exception as e:
            self.logger.error(f"❌ 重启价格更新池失败: {e}")
            # 如果重启失败，停止整个包装器
            self.stop()
    
    def get_status(self) -> Dict[str, Any]:
        """获取定时器状态"""
        try:
            # 获取更新池状态
            pool_status = self.smart_update_pool.get_status()
            
            # 计算运行时间
            uptime = 0
            if self.is_running and self.start_time:
                uptime = (datetime.now() - self.start_time).total_seconds()
            
            return {
                'wrapper_status': {
                    'is_running': self.is_running,
                    'start_time': self.start_time.isoformat() if self.start_time else None,
                    'uptime_seconds': uptime,
                    'last_health_check': self.last_health_check.isoformat() if self.last_health_check else None,
                    'health_check_interval': self.health_check_interval
                },
                'pool_status': pool_status,
                'wrapper_stats': self.wrapper_stats.copy(),
                'health_status': self._get_health_status()
            }
        except Exception as e:
            self.logger.error(f"获取状态失败: {e}")
            return {
                'wrapper_status': {
                    'is_running': self.is_running,
                    'error': str(e)
                }
            }
    
    def _get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        try:
            pool_status = self.smart_update_pool.get_status()
            
            # 计算健康分数
            health_score = 100
            health_issues = []
            
            # 检查更新池运行状态
            if not pool_status.get('is_running', False):
                health_score -= 50
                health_issues.append("更新池未运行")
            
            # 检查健康检查失败率
            total_checks = self.wrapper_stats['health_check_count']
            failures = self.wrapper_stats['health_check_failures']
            if total_checks > 0:
                failure_rate = failures / total_checks
                if failure_rate > 0.1:  # 失败率超过10%
                    health_score -= 30
                    health_issues.append(f"健康检查失败率过高: {failure_rate:.1%}")
            
            # 检查最近健康检查时间
            if self.last_health_check:
                time_since_check = (datetime.now() - self.last_health_check).total_seconds()
                if time_since_check > self.health_check_interval * 2:
                    health_score -= 20
                    health_issues.append("健康检查超时")
            
            return {
                'health_score': max(0, health_score),
                'health_issues': health_issues,
                'is_healthy': health_score >= 80
            }
        except Exception as e:
            return {
                'health_score': 0,
                'health_issues': [f"健康检查异常: {str(e)}"],
                'is_healthy': False
            }
    
    async def force_update_cycle(self) -> Dict[str, Any]:
        """强制执行一次更新循环（用于测试）"""
        self.logger.info("🔄 强制执行价格更新循环")
        
        try:
            if not self.is_running:
                return {
                    'success': False,
                    'error': '定时器未运行'
                }
            
            # 调用更新池的单次更新
            result = await self.smart_update_pool.run_update_cycle()
            
            self.logger.info(f"✅ 强制更新循环完成: {result}")
            return {
                'success': True,
                'result': result
            }
        except Exception as e:
            self.logger.error(f"❌ 强制更新循环失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
