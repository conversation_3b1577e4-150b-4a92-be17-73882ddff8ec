"""
统一日志组件

提供项目统一的日志记录功能，支持多种日志级别、文件输出、格式化等功能。
所有模块都应该使用此组件进行日志记录。

使用示例:
    from src.cs2_investment.utils.logger import get_logger
    
    logger = get_logger(__name__)
    logger.info("这是一条信息日志")
    logger.error("这是一条错误日志")
"""

import os
import sys
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime
import threading
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler


class LoggerManager:
    """日志管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    _loggers: Dict[str, logging.Logger] = {}
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._setup_logging()
            self._initialized = True
    
    def _setup_logging(self):
        """设置日志配置"""
        # 获取项目根目录
        project_root = Path(__file__).parent.parent.parent.parent
        self.logs_dir = project_root / "logs"
        self.logs_dir.mkdir(exist_ok=True)
        
        # 默认配置
        self.config = {
            'level': logging.INFO,
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'date_format': '%Y-%m-%d %H:%M:%S',
            'file_max_size': 10 * 1024 * 1024,  # 10MB
            'file_backup_count': 5,
            'console_output': True,
            'file_output': True,
            'encoding': 'utf-8'
        }

        # 定时器专用配置
        self.timer_config = {
            'level': logging.INFO,
            'format': '%(asctime)s - [TIMER] %(name)s - %(levelname)s - %(message)s',
            'file_max_size': 50 * 1024 * 1024,  # 50MB（定时器日志更大）
            'file_backup_count': 10,  # 保留更多备份
            'separate_files': True,  # 使用独立的日志文件
            'performance_logging': True,  # 启用性能日志
            'error_tracking': True,  # 启用错误跟踪
            'daily_rotation': True  # 按天轮转
        }
        
        # 从环境变量读取配置
        self._load_config_from_env()
    
    def _load_config_from_env(self):
        """从环境变量加载配置"""
        # 日志级别
        log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        self.config['level'] = level_map.get(log_level, logging.INFO)
        
        # 控制台输出
        self.config['console_output'] = os.getenv('LOG_CONSOLE', 'true').lower() == 'true'
        
        # 文件输出
        self.config['file_output'] = os.getenv('LOG_FILE_OUTPUT', 'true').lower() == 'true'
    
    def get_logger(self, name: str, 
                   log_file: Optional[str] = None,
                   level: Optional[int] = None,
                   console_output: Optional[bool] = None,
                   file_output: Optional[bool] = None) -> logging.Logger:
        """
        获取日志记录器
        
        Args:
            name: 日志记录器名称，通常使用 __name__
            log_file: 日志文件名（可选），如果不指定则使用模块名
            level: 日志级别（可选）
            console_output: 是否输出到控制台（可选）
            file_output: 是否输出到文件（可选）
            
        Returns:
            logging.Logger: 配置好的日志记录器
        """
        # 使用缓存避免重复创建
        cache_key = f"{name}_{log_file}_{level}_{console_output}_{file_output}"
        if cache_key in self._loggers:
            return self._loggers[cache_key]
        
        # 创建日志记录器
        logger = logging.getLogger(name)
        logger.setLevel(level or self.config['level'])
        
        # 清除已有的处理器（避免重复添加）
        logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(
            fmt=self.config['format'],
            datefmt=self.config['date_format']
        )
        
        # 控制台处理器
        if console_output if console_output is not None else self.config['console_output']:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(level or self.config['level'])
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        # 文件处理器
        if file_output if file_output is not None else self.config['file_output']:
            # 确定日志文件名
            if log_file:
                if not log_file.endswith('.log'):
                    log_file += '.log'
            else:
                # 从模块名生成文件名
                module_parts = name.split('.')
                if len(module_parts) > 1:
                    log_file = f"{module_parts[-1]}.log"
                else:
                    log_file = f"{name}.log"
            
            log_path = self.logs_dir / log_file
            
            # 使用轮转文件处理器
            file_handler = RotatingFileHandler(
                filename=log_path,
                maxBytes=self.config['file_max_size'],
                backupCount=self.config['file_backup_count'],
                encoding=self.config['encoding']
            )
            file_handler.setLevel(level or self.config['level'])
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        # 防止日志向上传播（避免重复输出）
        logger.propagate = False
        
        # 缓存日志记录器
        self._loggers[cache_key] = logger
        
        return logger
    
    def get_module_logger(self, module_name: str) -> logging.Logger:
        """
        为特定模块获取日志记录器
        
        Args:
            module_name: 模块名称
            
        Returns:
            logging.Logger: 配置好的日志记录器
        """
        # 根据模块名确定日志文件
        module_log_files = {
            # API相关
            'api': 'api_service.log',
            'main': 'api_service.log',
            'routers': 'api_service.log',
            'services': 'api_service.log',
            
            # 调度器相关
            'scheduler': 'scheduler.log',
            'realtime_only_scheduler': 'scheduler.log',
            'scheduler_manager': 'scheduler.log',
            
            # 抓取器相关
            'scraper': 'scraper.log',
            'steamdt_scraper': 'scraper.log',
            'steamdt_scraper_final': 'scraper.log',
            'scraping_manager': 'scraper.log',
            
            # 分析系统相关
            'fx': 'analysis.log',
            'integrated_analysis_system': 'analysis.log',
            'integrated_ssync_system': 'analysis.log',
            
            # 数据库相关
            'dao': 'database.log',
            'database': 'database.log',
            'models': 'database.log',
            
            # 工具类
            'utils': 'utils.log',
            'config': 'config.log'
        }
        
        # 根据模块名匹配日志文件
        log_file = 'application.log'  # 默认文件
        for key, file in module_log_files.items():
            if key in module_name.lower():
                log_file = file
                break
        
        return self.get_logger(module_name, log_file=log_file)
    
    def set_level(self, level: int):
        """设置全局日志级别"""
        self.config['level'] = level
        for logger in self._loggers.values():
            logger.setLevel(level)
            for handler in logger.handlers:
                handler.setLevel(level)
    
    def get_log_files(self) -> list:
        """获取所有日志文件列表"""
        if not self.logs_dir.exists():
            return []
        
        log_files = []
        for file_path in self.logs_dir.glob('*.log'):
            log_files.append({
                'name': file_path.name,
                'path': str(file_path),
                'size': file_path.stat().st_size,
                'modified': datetime.fromtimestamp(file_path.stat().st_mtime)
            })
        
        return sorted(log_files, key=lambda x: x['modified'], reverse=True)

    def get_timer_logger(self, name: str, timer_type: str = None) -> logging.Logger:
        """
        获取定时器专用日志记录器

        Args:
            name: 日志记录器名称
            timer_type: 定时器类型（如 'item_info', 'price_update'）

        Returns:
            logging.Logger: 配置好的定时器日志记录器
        """
        # 构建定时器日志文件名
        if timer_type:
            log_file = f"timer_{timer_type}.log"
        else:
            log_file = "timer.log"

        # 使用定时器专用配置
        logger = logging.getLogger(f"timer.{name}")
        logger.setLevel(self.timer_config['level'])

        # 清除已有的处理器
        logger.handlers.clear()

        # 创建定时器专用格式化器
        formatter = logging.Formatter(
            fmt=self.timer_config['format'],
            datefmt=self.config['date_format']
        )

        # 控制台处理器
        if self.config['console_output']:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(self.timer_config['level'])
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)

        # 文件处理器
        if self.config['file_output']:
            log_path = self.logs_dir / log_file

            # 根据配置选择轮转方式
            if self.timer_config.get('daily_rotation', False):
                # 按天轮转
                file_handler = TimedRotatingFileHandler(
                    filename=log_path,
                    when='midnight',
                    interval=1,
                    backupCount=self.timer_config['file_backup_count'],
                    encoding=self.config['encoding']
                )
            else:
                # 按大小轮转
                file_handler = RotatingFileHandler(
                    filename=log_path,
                    maxBytes=self.timer_config['file_max_size'],
                    backupCount=self.timer_config['file_backup_count'],
                    encoding=self.config['encoding']
                )

            file_handler.setLevel(self.timer_config['level'])
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

        # 缓存日志记录器
        cache_key = f"timer_{name}_{timer_type}"
        self._loggers[cache_key] = logger

        return logger

    def cleanup_old_logs(self, days: int = 30):
        """
        清理旧日志文件

        Args:
            days: 保留天数，默认30天
        """
        if not self.logs_dir.exists():
            return

        cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)

        for log_file in self.logs_dir.glob('*.log*'):
            if log_file.stat().st_mtime < cutoff_time:
                try:
                    log_file.unlink()
                    print(f"已删除旧日志文件: {log_file}")
                except Exception as e:
                    print(f"删除日志文件失败 {log_file}: {e}")


# 全局日志管理器实例
_logger_manager = LoggerManager()


def get_logger(name: str = None, **kwargs) -> logging.Logger:
    """
    获取日志记录器的便捷函数
    
    Args:
        name: 日志记录器名称，通常使用 __name__
        **kwargs: 其他参数传递给 LoggerManager.get_logger
        
    Returns:
        logging.Logger: 配置好的日志记录器
        
    Examples:
        >>> logger = get_logger(__name__)
        >>> logger.info("这是一条信息日志")
        
        >>> logger = get_logger(__name__, log_file="custom.log")
        >>> logger.error("这是一条错误日志")
    """
    if name is None:
        # 如果没有提供名称，尝试从调用栈获取
        import inspect
        frame = inspect.currentframe().f_back
        name = frame.f_globals.get('__name__', 'unknown')
    
    return _logger_manager.get_module_logger(name)


def set_log_level(level: str):
    """
    设置全局日志级别
    
    Args:
        level: 日志级别 ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
    """
    level_map = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    
    if level.upper() in level_map:
        _logger_manager.set_level(level_map[level.upper()])
    else:
        raise ValueError(f"无效的日志级别: {level}")


def get_log_files() -> list:
    """
    获取所有日志文件信息
    
    Returns:
        list: 日志文件信息列表
    """
    return _logger_manager.get_log_files()


# 为了向后兼容，提供一些常用的日志记录器
api_logger = get_logger('api')
scheduler_logger = get_logger('scheduler')
scraper_logger = get_logger('scraper')
analysis_logger = get_logger('analysis')
database_logger = get_logger('database')


def get_timer_logger(name: str, timer_type: str = None) -> logging.Logger:
    """
    获取定时器专用日志记录器

    Args:
        name: 日志记录器名称
        timer_type: 定时器类型（如 'item_info', 'price_update'）

    Returns:
        logging.Logger: 配置好的定时器日志记录器
    """
    return _logger_manager.get_timer_logger(name, timer_type)


def log_timer_performance(logger: logging.Logger, operation: str, duration: float,
                         success: bool = True, **kwargs):
    """
    记录定时器性能日志

    Args:
        logger: 日志记录器
        operation: 操作名称
        duration: 执行时间（秒）
        success: 是否成功
        **kwargs: 额外的性能指标
    """
    status = "SUCCESS" if success else "FAILED"
    metrics = " | ".join([f"{k}={v}" for k, v in kwargs.items()])

    log_message = f"[PERFORMANCE] {operation} - {status} - Duration: {duration:.2f}s"
    if metrics:
        log_message += f" | {metrics}"

    if success:
        logger.info(log_message)
    else:
        logger.error(log_message)


def log_timer_error(logger: logging.Logger, operation: str, error: Exception, **context):
    """
    记录定时器错误日志

    Args:
        logger: 日志记录器
        operation: 操作名称
        error: 异常对象
        **context: 错误上下文信息
    """
    context_str = " | ".join([f"{k}={v}" for k, v in context.items()])

    log_message = f"[ERROR] {operation} - {type(error).__name__}: {str(error)}"
    if context_str:
        log_message += f" | Context: {context_str}"

    logger.error(log_message, exc_info=True)


def cleanup_old_logs(days: int = 30):
    """
    清理旧日志文件

    Args:
        days: 保留天数，默认30天
    """
    _logger_manager.cleanup_old_logs(days)
