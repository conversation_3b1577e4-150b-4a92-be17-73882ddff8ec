# CS2饰品实时分析系统 (ssync)

## 📊 系统概述

**ssync** 是专为CS2饰品投资设计的实时分析系统，基于《技术分析在CS2虚拟饰品市场有效性的实证研究》报告优化，提供专业级的技术指标分析和图表化展示。

### 🎯 核心特性

- ✅ **实时技术指标分析**：RSI、MACD、组合策略信号
- ✅ **专业图表生成**：K线图、技术指标图、成交量分析
- ✅ **异常检测系统**：24小时异常监控和预警
- ✅ **多饰品对比分析**：支持批量分析和对比
- ✅ **智能信号生成**：基于研究验证的最佳策略

## 📁 目录结构

```
ssync/
├── README.md                    # 主要使用文档
├── USAGE.md                     # 详细使用指南
├── EXAMPLES.md                  # 使用示例
├── 修复版实时监控.py             # 核心实时监控系统
├── 技术指标图表生成器.py         # 专业图表生成模块
├── 异常分析检测系统.py           # 异常检测模块
├── 三饰品分析.py                # 多饰品对比分析
└── 图表化分析演示.py            # 图表化功能演示
```

## 🚀 快速开始

### 1. 环境要求

```bash
# Python 3.8+
pip install pandas numpy matplotlib datetime warnings
```

### 2. 数据准备

确保饰品数据按以下结构组织：
```
../[饰品名称]/
├── 时k.json      # 时K数据（主要分析数据）
├── 日k1.json     # 日K数据1
├── 日k2.json     # 日K数据2
├── 周k.json      # 周K数据
└── 走势.json     # 走势数据
```

### 3. 基础使用

#### 单饰品实时分析
```python
from 修复版实时监控 import FixedRealTimeMonitor

# 创建监控器
monitor = FixedRealTimeMonitor('AK-47 传承 (久经沙场)')

# 加载数据并运行分析
if monitor.load_data():
    monitor.print_fixed_dashboard()
```

#### 生成专业图表
```python
# 生成技术指标图表
chart_path = monitor.generate_technical_chart()
print(f"图表已保存: {chart_path}")
```

#### 多饰品对比分析
```python
# 运行三饰品对比分析
python 三饰品分析.py
```

## 📊 主要功能模块

### 1. 实时监控系统 (`修复版实时监控.py`)

**核心功能**：
- 📈 实时技术指标计算（RSI、MACD）
- 🎯 MACD+RSI组合策略信号
- 📊 增强版交易信号生成
- ⚠️ 实时成交量异常检测
- 🚨 价格警报和风险管理

**主要方法**：
- `load_data()` - 数据加载
- `print_fixed_dashboard()` - 显示分析仪表板
- `generate_technical_chart()` - 生成技术图表
- `detect_volume_anomaly_real_time()` - 成交量异常检测

### 2. 图表生成系统 (`技术指标图表生成器.py`)

**专业图表类型**：
- 📊 K线图 + 移动平均线 + 买卖信号标注
- 📈 RSI指标图 + 超买超卖区域标注
- 📉 MACD指标图 + 金叉死叉点标注
- 📊 成交量图 + 异常检测标注

**使用示例**：
```python
from 技术指标图表生成器 import TechnicalChartGenerator

chart_generator = TechnicalChartGenerator(monitor)
chart_generator.generate_comprehensive_chart(
    save_path="分析图表.png",
    show_chart=False
)
```

### 3. 异常检测系统 (`异常分析检测系统.py`)

**检测类型**：
- 🔴 价格异常：大幅波动、突破关键位
- 📊 成交量异常：激增、萎缩、无量波动
- ⏰ 时间异常：深夜大额交易、连续异常
- 📈 技术异常：RSI极值、MACD背离

### 4. 多饰品分析 (`三饰品分析.py`)

**对比功能**：
- 📊 同时分析多个饰品
- 🎯 技术指标对比
- 📈 投资建议排序
- 📋 综合评估报告

## 🎯 核心技术指标

### RSI (相对强弱指标)
- **计算周期**：14小时
- **超买线**：70
- **超卖线**：30
- **信号**：超买超卖区域反转信号

### MACD (指数平滑移动平均线)
- **快线**：EMA(12)
- **慢线**：EMA(26)
- **信号线**：EMA(9)
- **信号**：金叉买入、死叉卖出

### 组合策略
- **MACD+RSI组合**：基于研究验证的最佳策略
- **置信度评分**：70%-85%置信度区间
- **时效性**：1-2小时内有效

## ⚠️ 重要说明

### 数据处理策略
- 📊 **统一数据基准**：加载时排除最后1小时数据
- ⏰ **时间基准**：所有分析基于相同时间点
- 🔍 **数据一致性**：避免使用不完整数据

### 风险提示
- 💰 **投资风险**：虚拟饰品投资存在较高风险
- 📊 **数据延迟**：分析基于历史数据，非实时价格
- 🎯 **决策参考**：技术分析仅供参考，需结合多因素判断

## 📞 技术支持

如有问题，请参考：
- 📖 [详细使用指南](USAGE.md)
- 💡 [使用示例](EXAMPLES.md)
- 🔧 代码注释和文档字符串

## 📄 许可证

本项目仅供学习和研究使用，请勿用于商业用途。

---

**版本**: v2.0  
**更新时间**: 2025-07-25  
**基于研究**: 《技术分析在CS2虚拟饰品市场有效性的实证研究》
