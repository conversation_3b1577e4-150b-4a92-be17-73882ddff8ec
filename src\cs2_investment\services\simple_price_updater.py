#!/usr/bin/env python3
"""
简化的价格更新器
按照用户建议的简单方案实现
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any

from ..dao.item_dao import ItemDAO
from ..dao.platform_price_dao import PlatformPriceDAO
from .steamdt_api_client import SteamDTAPIClient
from .arbitrage_calculator import ArbitrageCalculator
from ..utils.logger import get_timer_logger
from ..config.timer_config import get_timer_config


class SimplePriceUpdater:
    """简化的价格更新器"""
    
    def __init__(self, api_key: str = None):
        # 加载配置
        self.config = get_timer_config()

        # API配置
        self.api_key = api_key or self.config.api.steamdt_api_key
        if not self.api_key:
            raise ValueError("API key is required")

        self.api_client = SteamDTAPIClient(self.api_key)
        self.item_dao = ItemDAO()
        self.platform_price_dao = PlatformPriceDAO()

        # 初始化搬砖比例计算器
        self.arbitrage_calculator = ArbitrageCalculator()

        # 使用定时器专用logger
        self.logger = get_timer_logger(__name__, "simple_price_updater")

        # 从配置加载参数
        simple_config = self.config.simple_price_update
        self.batch_size = simple_config.batch_size  # 批量接口处理数量
        self.single_size = simple_config.single_size  # 单个接口处理数量
        self.single_interval = simple_config.single_interval_seconds  # 单个接口间隔（秒）
        self.items_limit = simple_config.items_limit  # 每次获取的饰品数量
        self.use_last_update_time = simple_config.use_last_update_time  # 是否按最后更新时间排序
        self.skip_zero_price_items = simple_config.skip_zero_price_items  # 是否跳过全零价格饰品
        self.zero_price_update_interval_hours = simple_config.zero_price_update_interval_hours  # 全零价格饰品重新查询间隔
        self.continuous_mode = simple_config.continuous_mode  # 是否启用持续运行模式
        self.cycle_interval_minutes = simple_config.cycle_interval_minutes  # 完整轮询间隔

        self.is_running = False
    
    def get_items_to_update(self, limit: int = None) -> List[Dict[str, Any]]:
        """获取需要更新的饰品"""
        if limit is None:
            limit = self.items_limit

        try:
            # 根据配置决定获取方式
            if self.use_last_update_time:
                # 按最后更新时间排序获取，支持跳过全零价格饰品
                items = self.item_dao.get_items_for_price_update(
                    limit=limit,
                    skip_zero_price_items=self.skip_zero_price_items,
                    zero_price_update_interval_hours=self.zero_price_update_interval_hours
                )
            else:
                # 简单获取活跃饰品
                items = self.item_dao.get_all_active_items(limit=limit)

            result = []
            for item in items:
                result.append({
                    'item_id': item['item_id'],
                    'market_hash_name': item['market_hash_name'],
                    'last_update': None  # 暂时不使用最后更新时间
                })

            return result

        except Exception as e:
            self.logger.error(f"获取待更新饰品失败: {e}")
            return []
    
    async def process_batch_items(self, items: List[Dict[str, Any]]) -> int:
        """处理批量饰品"""
        if not items:
            return 0
        
        try:
            # 提取饰品名称
            market_hash_names = [item['market_hash_name'] for item in items]
            
            self.logger.debug(f"📦 开始批量处理 {len(market_hash_names)} 个饰品")

            # 调用批量接口
            responses = await self.api_client.get_batch_prices(market_hash_names)

            if not responses:
                self.logger.warning("批量接口返回空结果")
                return 0

            # 保存数据
            saved_count = 0
            for item in items:
                market_hash_name = item['market_hash_name']
                if market_hash_name in responses:
                    response = responses[market_hash_name]
                    if response.success:
                        await self.save_price_data(item, response)
                        saved_count += 1
                    else:
                        self.logger.debug(f"批量接口返回错误: {market_hash_name} - {response.error_message}")

            self.logger.debug(f"✅ 批量处理完成: {saved_count}/{len(items)} 个饰品")
            return saved_count
            
        except Exception as e:
            self.logger.error(f"批量处理失败: {e}")
            return 0
    
    async def process_single_items(self, items: List[Dict[str, Any]]) -> int:
        """处理单个饰品"""
        if not items:
            return 0
        
        self.logger.debug(f"🔍 开始单个处理 {len(items)} 个饰品")

        saved_count = 0
        for i, item in enumerate(items):
            try:
                market_hash_name = item['market_hash_name']

                # 调用单个接口
                response = await self.api_client.get_single_price(market_hash_name)

                if response.success:
                    await self.save_price_data(item, response)
                    saved_count += 1
                    self.logger.debug(f"✅ 单个处理成功: {market_hash_name}")
                else:
                    self.logger.debug(f"单个接口返回错误: {market_hash_name} - {response.error_message}")

                # 间隔等待（除了最后一个）
                if i < len(items) - 1:
                    await asyncio.sleep(self.single_interval)

            except Exception as e:
                self.logger.error(f"单个处理失败: {item['market_hash_name']} - {e}")

        self.logger.debug(f"✅ 单个处理完成: {saved_count}/{len(items)} 个饰品")
        return saved_count
    
    async def save_price_data(self, item: Dict[str, Any], response):
        """保存价格数据"""
        saved_count = 0
        has_valid_data = False  # 标记是否有有效的价格数据

        for platform_price in response.platform_prices:
            # 标记是否有有效价格数据（用于搬砖计算）
            if platform_price.sell_price > 0 or platform_price.bidding_price > 0:
                has_valid_data = True

            # 保存所有价格数据，包括价格为0的数据（用于完整记录）
            try:
                # 确定数据源：Steam平台使用steamdt数据源（来自SteamDT API），其他平台也是steamdt
                data_source = 'steamdt'  # 简化价格更新器使用的都是SteamDT API数据

                # 标准化平台名称：确保Steam平台统一为大写STEAM
                platform_name = platform_price.platform
                if platform_name.upper() == 'STEAM':
                    platform_name = 'STEAM'

                self.platform_price_dao.save_platform_price(
                    item_id=item['item_id'],
                    market_hash_name=item['market_hash_name'],
                    platform=platform_name,  # 使用标准化后的平台名称
                    platform_item_id=platform_price.platform_item_id,
                    sell_price=platform_price.sell_price,
                    sell_count=platform_price.sell_count,
                    bidding_price=platform_price.bidding_price,
                    bidding_count=platform_price.bidding_count,
                    steamdt_update_time=platform_price.update_time,
                    query_time=response.query_time,
                    data_source=data_source  # 明确设置数据源
                )
                saved_count += 1
            except Exception as e:
                self.logger.error(f"保存价格数据失败: {item['market_hash_name']} - {platform_price.platform}, 错误: {e}")

        # 无论是否保存了价格数据，都要更新最后查询时间，避免重复查询价格为0的饰品
        try:
            self.item_dao.update_item_price_update_time(item['item_id'])

            if has_valid_data:
                self.logger.debug(f"成功保存 {saved_count} 条有效价格记录: {item['market_hash_name']}")
            elif saved_count > 0:
                self.logger.debug(f"保存 {saved_count} 条全零价格记录: {item['market_hash_name']}")
            else:
                self.logger.debug(f"饰品无价格数据，已更新查询时间: {item['market_hash_name']}")
        except Exception as e:
            self.logger.error(f"更新饰品时间失败: {item['market_hash_name']} - {e}")

        # 只有在有有效价格数据时才计算搬砖比例
        if has_valid_data:
            try:
                self.arbitrage_calculator.update_item_arbitrage_ratio(item['item_id'])
            except Exception as e:
                self.logger.error(f"更新搬砖比例失败: {item['market_hash_name']} - {e}")
    
    async def run_update_cycle(self):
        """运行一次更新周期"""
        start_time = datetime.now()
        
        try:
            # 1. 获取饰品数据（使用配置的数量）
            items = self.get_items_to_update()
            
            if not items:
                self.logger.info("没有需要更新的饰品")
                return {'total_processed': 0, 'batch_processed': 0, 'single_processed': 0}
            
            self.logger.info(f"🚀 开始价格更新: 获取到 {len(items)} 个饰品")

            # 2. 分配任务
            batch_items = items[:self.batch_size]  # 前100个用于批量处理
            single_items = items[self.batch_size:self.batch_size + self.single_size]  # 接下来50个用于单个处理

            # 3. 批量处理100条
            batch_processed = 0
            if batch_items:
                batch_processed = await self.process_batch_items(batch_items)

            # 4. 单个处理50条（每1.2秒一次）
            single_processed = 0
            if single_items:
                single_processed = await self.process_single_items(single_items)

            # 5. 统计结果
            total_processed = batch_processed + single_processed
            duration = (datetime.now() - start_time).total_seconds()

            self.logger.info(f"✅ 价格更新完成: 批量{batch_processed}个, 单个{single_processed}个, "
                           f"总计{total_processed}个, 耗时{duration:.1f}秒")
            
            return {
                'total_processed': total_processed,
                'batch_processed': batch_processed,
                'single_processed': single_processed,
                'duration': duration
            }
            
        except Exception as e:
            self.logger.error(f"更新周期异常: {e}")
            import traceback
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
            return {'total_processed': 0, 'batch_processed': 0, 'single_processed': 0}
    
    async def start_continuous_update(self):
        """启动持续更新（每分钟执行一次）"""
        self.is_running = True
        self.logger.info("🔄 启动简化价格更新器")
        
        try:
            while self.is_running:
                # 运行一次更新周期
                await self.run_update_cycle()
                
                # 等待60秒（1分钟）
                if self.is_running:
                    self.logger.info("⏳ 等待60秒后进行下一次更新...")
                    await asyncio.sleep(60)
                    
        except asyncio.CancelledError:
            self.logger.info("🛑 持续更新被取消")
        except Exception as e:
            self.logger.error(f"持续更新异常: {e}")
        finally:
            self.is_running = False
            self.logger.info("🛑 简化价格更新器已停止")
    
    async def start_continuous_update_cycle(self):
        """启动持续更新循环 - 直到所有饰品同步完成再重新开始"""
        self.is_running = True
        cycle_count = 0

        self.logger.info("🚀 启动持续价格更新循环")

        try:
            while self.is_running:
                cycle_count += 1
                self.logger.info(f"🔄 开始第 {cycle_count} 轮完整同步")

                total_processed = 0
                batch_count = 0

                # 持续处理直到没有更多饰品需要更新
                while self.is_running:
                    batch_count += 1

                    # 获取下一批饰品
                    items = self.get_items_to_update()
                    if not items:
                        self.logger.info(f"✅ 第 {cycle_count} 轮同步完成，共处理 {total_processed} 个饰品")
                        break  # 没有更多饰品需要处理

                    self.logger.info(f"📦 第 {cycle_count} 轮第 {batch_count} 批：处理 {len(items)} 个饰品")

                    # 处理这批饰品
                    result = await self.run_update_cycle()
                    total_processed += result.get('total_processed', 0)

                    # 短暂休息避免过载
                    await asyncio.sleep(2)

                if not self.is_running:
                    break

                # 完整轮询完成后等待一段时间再开始下一轮
                cycle_interval = self.cycle_interval_minutes * 60
                self.logger.info(f"⏳ 等待 {self.cycle_interval_minutes} 分钟后开始下一轮同步")

                # 分段等待，便于中断
                for i in range(cycle_interval):
                    if not self.is_running:
                        break
                    await asyncio.sleep(1)

        except asyncio.CancelledError:
            self.logger.info("🛑 持续更新循环被取消")
        except Exception as e:
            self.logger.error(f"❌ 持续更新循环异常: {e}")
        finally:
            self.is_running = False
            self.logger.info("🛑 持续价格更新循环已停止")

    def stop(self):
        """停止更新器"""
        self.logger.info("⏹️ 停止简化价格更新器")
        self.is_running = False
