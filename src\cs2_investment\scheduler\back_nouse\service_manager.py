#!/usr/bin/env python3
"""
服务管理器

负责管理定时任务调度器的启动、停止和状态监控
支持作为系统服务运行
"""

import asyncio
import signal
import logging
import sys
import os
from pathlib import Path
from datetime import datetime
import json

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.scheduler.back_nouse.simple_scheduler import simple_scheduler as scheduler

logger = logging.getLogger(__name__)


class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        """初始化服务管理器"""
        self.is_running = False
        self.start_time = None
        self.pid_file = Path("scheduler.pid")
        self.log_file = Path("scheduler.log")
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"收到信号 {signum}，准备停止服务...")
        asyncio.create_task(self.stop())
    
    async def start(self):
        """启动服务"""
        if self.is_running:
            logger.warning("服务已在运行中")
            return
        
        self.is_running = True
        self.start_time = datetime.now()
        
        # 写入PID文件
        self._write_pid_file()
        
        logger.info("🚀 启动SteamDT定时分析服务")
        logger.info(f"📅 启动时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"📁 工作目录: {os.getcwd()}")
        logger.info(f"🆔 进程ID: {os.getpid()}")
        
        try:
            # 启动调度器
            await scheduler.start()
        except Exception as e:
            logger.error(f"服务启动失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止服务"""
        if not self.is_running:
            logger.warning("服务未在运行")
            return
        
        logger.info("🛑 停止SteamDT定时分析服务")
        
        try:
            # 停止调度器
            await scheduler.stop()
        except Exception as e:
            logger.error(f"停止调度器时出错: {e}")
        
        self.is_running = False
        
        # 清理PID文件
        self._remove_pid_file()
        
        if self.start_time:
            duration = datetime.now() - self.start_time
            logger.info(f"✅ 服务已停止，运行时长: {duration}")
    
    def _write_pid_file(self):
        """写入PID文件"""
        try:
            with open(self.pid_file, 'w') as f:
                f.write(str(os.getpid()))
            logger.debug(f"PID文件已创建: {self.pid_file}")
        except Exception as e:
            logger.warning(f"创建PID文件失败: {e}")
    
    def _remove_pid_file(self):
        """删除PID文件"""
        try:
            if self.pid_file.exists():
                self.pid_file.unlink()
                logger.debug(f"PID文件已删除: {self.pid_file}")
        except Exception as e:
            logger.warning(f"删除PID文件失败: {e}")
    
    def get_status(self) -> dict:
        """获取服务状态"""
        status = {
            'service_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'pid': os.getpid(),
            'working_directory': os.getcwd(),
            'scheduler_status': scheduler.get_status()
        }
        
        if self.start_time:
            status['uptime_seconds'] = (datetime.now() - self.start_time).total_seconds()
        
        return status
    
    def is_already_running(self) -> bool:
        """检查是否已有实例在运行"""
        if not self.pid_file.exists():
            return False
        
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # 检查进程是否存在
            try:
                os.kill(pid, 0)  # 发送信号0检查进程是否存在
                return True
            except OSError:
                # 进程不存在，删除过期的PID文件
                self._remove_pid_file()
                return False
        except (ValueError, FileNotFoundError):
            return False


def setup_logging():
    """设置日志配置 - 使用统一日志组件"""
    from src.cs2_investment.utils.logger import get_logger
    # 统一日志组件会自动处理日志配置
    logger = get_logger(__name__)
    logger.info("📋 调度器日志系统已初始化")


async def main():
    """主函数"""
    setup_logging()
    
    service = ServiceManager()
    
    # 检查是否已有实例运行
    if service.is_already_running():
        logger.error("❌ 服务已在运行中，请先停止现有实例")
        sys.exit(1)
    
    try:
        logger.info("=" * 60)
        logger.info("🎯 SteamDT定时分析服务")
        logger.info("📊 常规分析: 每24小时执行一次")
        logger.info("📈 实时监控: 每1小时执行一次")
        logger.info("⏱️ 饰品间隔: 15-30秒随机")
        logger.info("=" * 60)
        
        # 启动服务
        await service.start()
        
    except KeyboardInterrupt:
        logger.info("收到中断信号")
    except Exception as e:
        logger.error(f"服务运行出错: {e}")
    finally:
        await service.stop()


def status_command():
    """状态查询命令"""
    setup_logging()
    
    service = ServiceManager()
    
    if service.is_already_running():
        print("✅ 服务正在运行")
        
        # 尝试获取详细状态（这需要服务提供状态接口）
        try:
            status = service.get_status()
            print(f"📅 启动时间: {status.get('start_time', 'N/A')}")
            print(f"🆔 进程ID: {status.get('pid', 'N/A')}")
            print(f"⏱️ 运行时长: {status.get('uptime_seconds', 0):.0f}秒")
            
            scheduler_status = status.get('scheduler_status', {})
            print(f"📊 常规分析: {'运行中' if scheduler_status.get('regular_analysis_running') else '停止'}")
            print(f"📈 实时监控: {'运行中' if scheduler_status.get('realtime_monitor_running') else '停止'}")
        except Exception as e:
            print(f"⚠️ 获取详细状态失败: {e}")
    else:
        print("❌ 服务未运行")


def stop_command():
    """停止服务命令"""
    setup_logging()
    
    service = ServiceManager()
    
    if not service.is_already_running():
        print("❌ 服务未运行")
        return
    
    try:
        with open(service.pid_file, 'r') as f:
            pid = int(f.read().strip())
        
        print(f"🛑 正在停止服务 (PID: {pid})...")
        os.kill(pid, signal.SIGTERM)
        print("✅ 停止信号已发送")
        
    except Exception as e:
        print(f"❌ 停止服务失败: {e}")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        if command == "status":
            status_command()
        elif command == "stop":
            stop_command()
        elif command == "start":
            asyncio.run(main())
        else:
            print("用法: python service_manager.py [start|stop|status]")
    else:
        # 默认启动服务
        asyncio.run(main())
