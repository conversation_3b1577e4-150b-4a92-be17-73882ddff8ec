#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品分析系统 - 增强风险评估器
结合日K数据(长期风险) + 时K数据(短期风险) + 异常检测
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class EnhancedRiskAssessment:
    """增强风险评估器 - 长短期风险结合"""
    
    def __init__(self, daily_data: pd.DataFrame, hourly_data: pd.DataFrame, trend_data: pd.DataFrame):
        """
        初始化增强风险评估器
        
        Args:
            daily_data: 日K数据 (长期风险评估)
            hourly_data: 时K数据 (短期风险监控)
            trend_data: 走势数据 (异常检测)
        """
        self.daily_data = daily_data.copy()
        self.hourly_data = hourly_data.copy()
        self.trend_data = trend_data.copy()
        
        # 风险评估结果
        self.long_term_risks = {}
        self.short_term_risks = {}
        self.anomaly_risks = {}
        self.comprehensive_risk = {}
    
    def assess_comprehensive_risk(self) -> Dict:
        """综合风险评估 - 长短期结合"""
        
        print("🔍 综合风险评估 (长期 + 短期 + 异常)")
        
        # 1. 长期风险评估 (基于日K数据)
        self.long_term_risks = self._assess_long_term_risks()
        
        # 2. 短期风险监控 (基于时K数据)
        self.short_term_risks = self._assess_short_term_risks()
        
        # 3. 异常风险检测 (基于走势数据)
        self.anomaly_risks = self._detect_anomaly_risks()
        
        # 4. 综合风险评估
        self.comprehensive_risk = self._calculate_comprehensive_risk()
        
        return {
            'long_term_risks': self.long_term_risks,
            'short_term_risks': self.short_term_risks,
            'anomaly_risks': self.anomaly_risks,
            'comprehensive_assessment': self.comprehensive_risk
        }
    
    def _assess_long_term_risks(self) -> Dict:
        """长期风险评估 (基于日K数据)"""
        
        daily_prices = self.daily_data['close']
        daily_volumes = self.daily_data['volume']
        
        # 1. 波动率风险
        daily_returns = daily_prices.pct_change().dropna()
        volatility = daily_returns.std() * 100
        
        if volatility > 5:
            volatility_risk = 'HIGH'
        elif volatility > 3:
            volatility_risk = 'MEDIUM'
        else:
            volatility_risk = 'LOW'
        
        # 2. RSI风险
        rsi = self._calculate_rsi(daily_prices)
        current_rsi = rsi.iloc[-1]
        
        if current_rsi > 80 or current_rsi < 20:
            rsi_risk = 'HIGH'
        elif current_rsi > 70 or current_rsi < 30:
            rsi_risk = 'MEDIUM'
        else:
            rsi_risk = 'LOW'
        
        # 3. 流动性风险 (基于成交量稳定性)
        volume_cv = daily_volumes.std() / daily_volumes.mean()  # 变异系数
        
        if volume_cv > 1.0:
            liquidity_risk = 'HIGH'
        elif volume_cv > 0.5:
            liquidity_risk = 'MEDIUM'
        else:
            liquidity_risk = 'LOW'
        
        # 4. 趋势风险 (基于价格趋势稳定性)
        price_trend = (daily_prices.iloc[-1] - daily_prices.iloc[-30]) / daily_prices.iloc[-30] * 100
        
        if abs(price_trend) > 20:
            trend_risk = 'HIGH'
        elif abs(price_trend) > 10:
            trend_risk = 'MEDIUM'
        else:
            trend_risk = 'LOW'
        
        return {
            'volatility_risk': {
                'level': volatility_risk,
                'value': volatility,
                'description': f'日波动率{volatility:.2f}%'
            },
            'rsi_risk': {
                'level': rsi_risk,
                'value': current_rsi,
                'description': f'RSI {current_rsi:.1f}'
            },
            'liquidity_risk': {
                'level': liquidity_risk,
                'value': volume_cv,
                'description': f'成交量变异系数{volume_cv:.2f}'
            },
            'trend_risk': {
                'level': trend_risk,
                'value': price_trend,
                'description': f'30日价格变化{price_trend:+.1f}%'
            },
            'data_source': '日K数据 (长期风险)'
        }
    
    def _assess_short_term_risks(self) -> Dict:
        """短期风险监控 (基于时K数据)"""
        
        hourly_prices = self.hourly_data['close']
        hourly_volumes = self.hourly_data['volume']
        
        # 1. 短期波动率突变
        recent_24h = hourly_prices.tail(24)
        short_volatility = recent_24h.pct_change().std() * np.sqrt(24) * 100
        
        # 与长期波动率对比
        long_volatility = self.long_term_risks['volatility_risk']['value']
        volatility_ratio = short_volatility / long_volatility if long_volatility > 0 else 1
        
        if volatility_ratio > 2:
            volatility_spike_risk = 'HIGH'
        elif volatility_ratio > 1.5:
            volatility_spike_risk = 'MEDIUM'
        else:
            volatility_spike_risk = 'LOW'
        
        # 2. 成交量异常
        recent_volume = hourly_volumes.tail(24).mean()
        avg_volume = hourly_volumes.mean()
        volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1
        
        if volume_ratio > 3 or volume_ratio < 0.3:
            volume_anomaly_risk = 'HIGH'
        elif volume_ratio > 2 or volume_ratio < 0.5:
            volume_anomaly_risk = 'MEDIUM'
        else:
            volume_anomaly_risk = 'LOW'
        
        # 3. 价格跳空风险
        price_gaps = []
        for i in range(1, min(24, len(hourly_prices))):
            gap = abs(hourly_prices.iloc[-i] - hourly_prices.iloc[-i-1]) / hourly_prices.iloc[-i-1] * 100
            price_gaps.append(gap)
        
        max_gap = max(price_gaps) if price_gaps else 0
        
        if max_gap > 5:
            gap_risk = 'HIGH'
        elif max_gap > 2:
            gap_risk = 'MEDIUM'
        else:
            gap_risk = 'LOW'
        
        # 4. 技术指标极值风险
        hourly_rsi = self._calculate_rsi(hourly_prices)
        current_hourly_rsi = hourly_rsi.iloc[-1] if len(hourly_rsi) > 0 else 50
        
        if current_hourly_rsi > 85 or current_hourly_rsi < 15:
            technical_extreme_risk = 'HIGH'
        elif current_hourly_rsi > 75 or current_hourly_rsi < 25:
            technical_extreme_risk = 'MEDIUM'
        else:
            technical_extreme_risk = 'LOW'
        
        return {
            'volatility_spike_risk': {
                'level': volatility_spike_risk,
                'value': volatility_ratio,
                'description': f'24小时波动率为长期的{volatility_ratio:.1f}倍'
            },
            'volume_anomaly_risk': {
                'level': volume_anomaly_risk,
                'value': volume_ratio,
                'description': f'24小时成交量为平均的{volume_ratio:.1f}倍'
            },
            'price_gap_risk': {
                'level': gap_risk,
                'value': max_gap,
                'description': f'最大价格跳空{max_gap:.1f}%'
            },
            'technical_extreme_risk': {
                'level': technical_extreme_risk,
                'value': current_hourly_rsi,
                'description': f'小时RSI {current_hourly_rsi:.1f}'
            },
            'data_source': '时K数据 (短期风险监控)'
        }
    
    def _detect_anomaly_risks(self) -> Dict:
        """异常风险检测 (调用之前的异常检测功能)"""
        
        # 调用之前实现的异常检测逻辑
        anomalies = self._detect_market_anomalies()
        
        # 统计异常风险
        high_risk_count = len([a for a in anomalies if a.get('severity') == 'HIGH'])
        medium_risk_count = len([a for a in anomalies if a.get('severity') == 'MEDIUM'])
        low_risk_count = len([a for a in anomalies if a.get('severity') == 'LOW'])
        
        total_anomalies = len(anomalies)
        
        # 异常风险等级
        if high_risk_count > 0:
            anomaly_risk_level = 'HIGH'
        elif medium_risk_count > 2:
            anomaly_risk_level = 'HIGH'
        elif medium_risk_count > 0 or low_risk_count > 5:
            anomaly_risk_level = 'MEDIUM'
        else:
            anomaly_risk_level = 'LOW'
        
        # 主要异常类型
        anomaly_types = {}
        for anomaly in anomalies:
            anomaly_type = anomaly.get('type', 'UNKNOWN')
            if anomaly_type not in anomaly_types:
                anomaly_types[anomaly_type] = 0
            anomaly_types[anomaly_type] += 1
        
        return {
            'anomaly_risk_level': anomaly_risk_level,
            'total_anomalies': total_anomalies,
            'high_risk_anomalies': high_risk_count,
            'medium_risk_anomalies': medium_risk_count,
            'low_risk_anomalies': low_risk_count,
            'anomaly_types': anomaly_types,
            'recent_anomalies': anomalies[-5:] if len(anomalies) > 5 else anomalies,  # 最近5个异常
            'data_source': '走势数据 (异常检测)'
        }
    
    def _detect_market_anomalies(self) -> List[Dict]:
        """市场异常检测 (简化版本，调用之前的逻辑)"""
        anomalies = []
        
        # 价格异常检测
        prices = self.trend_data['price']
        price_mean = prices.rolling(window=50).mean()
        price_std = prices.rolling(window=50).std()
        
        for i in range(len(prices)):
            if i < 50:
                continue
                
            current_price = prices.iloc[i]
            expected_price = price_mean.iloc[i]
            price_threshold = price_std.iloc[i] * 2
            
            if abs(current_price - expected_price) > price_threshold:
                severity = 'HIGH' if abs(current_price - expected_price) > price_threshold * 1.5 else 'MEDIUM'
                anomalies.append({
                    'type': 'PRICE_ANOMALY',
                    'severity': severity,
                    'timestamp': self.trend_data.iloc[i]['datetime'],
                    'description': f'价格异常: {current_price:.2f}元 (预期: {expected_price:.2f}元)'
                })
        
        # 成交量异常检测 (基于走势6m数据的日成交量)
        daily_counts = self.trend_data['daily_count']
        volume_mean = daily_counts.rolling(window=50).mean()
        volume_std = daily_counts.rolling(window=50).std()

        for i in range(len(daily_counts)):
            if i < 50:
                continue

            current_volume = daily_counts.iloc[i]
            expected_volume = volume_mean.iloc[i]
            volume_threshold = volume_std.iloc[i] * 2
            
            if current_volume > expected_volume + volume_threshold:
                severity = 'MEDIUM' if current_volume > expected_volume + volume_threshold * 1.5 else 'LOW'
                anomalies.append({
                    'type': 'VOLUME_SPIKE',
                    'severity': severity,
                    'timestamp': self.trend_data.iloc[i]['datetime'],
                    'description': f'成交量异常: {current_volume:.0f}件 (预期: {expected_volume:.0f}件)'
                })
        
        return anomalies[-20:] if len(anomalies) > 20 else anomalies  # 返回最近20个异常
    
    def _calculate_comprehensive_risk(self) -> Dict:
        """计算综合风险评估"""
        
        # 风险等级评分
        risk_scores = {'HIGH': 3, 'MEDIUM': 2, 'LOW': 1}
        
        # 长期风险评分
        long_term_score = (
            risk_scores[self.long_term_risks['volatility_risk']['level']] +
            risk_scores[self.long_term_risks['rsi_risk']['level']] +
            risk_scores[self.long_term_risks['liquidity_risk']['level']] +
            risk_scores[self.long_term_risks['trend_risk']['level']]
        ) / 4
        
        # 短期风险评分
        short_term_score = (
            risk_scores[self.short_term_risks['volatility_spike_risk']['level']] +
            risk_scores[self.short_term_risks['volume_anomaly_risk']['level']] +
            risk_scores[self.short_term_risks['price_gap_risk']['level']] +
            risk_scores[self.short_term_risks['technical_extreme_risk']['level']]
        ) / 4
        
        # 异常风险评分
        anomaly_score = risk_scores[self.anomaly_risks['anomaly_risk_level']]
        
        # 综合评分 (长期40%, 短期40%, 异常20%)
        comprehensive_score = (long_term_score * 0.4) + (short_term_score * 0.4) + (anomaly_score * 0.2)
        
        # 综合风险等级
        if comprehensive_score >= 2.5:
            overall_risk = 'HIGH'
        elif comprehensive_score >= 1.8:
            overall_risk = 'MEDIUM'
        else:
            overall_risk = 'LOW'
        
        # 风险建议
        risk_advice = self._generate_risk_advice(overall_risk, comprehensive_score)
        
        return {
            'overall_risk_level': overall_risk,
            'comprehensive_score': comprehensive_score,
            'long_term_score': long_term_score,
            'short_term_score': short_term_score,
            'anomaly_score': anomaly_score,
            'risk_advice': risk_advice,
            'calculation_method': '长期风险40% + 短期风险40% + 异常风险20%'
        }
    
    def _generate_risk_advice(self, risk_level: str, score: float) -> str:
        """生成风险建议"""
        if risk_level == 'HIGH':
            return f"高风险警告 (评分{score:.1f}): 建议减仓或暂停交易，密切监控市场变化"
        elif risk_level == 'MEDIUM':
            return f"中等风险 (评分{score:.1f}): 建议谨慎操作，控制仓位，设置止损"
        else:
            return f"低风险 (评分{score:.1f}): 风险可控，可正常交易，但仍需关注市场变化"
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def print_risk_summary(self):
        """打印风险评估摘要"""
        if not self.comprehensive_risk:
            print("❌ 请先运行 assess_comprehensive_risk()")
            return
        
        print(f"📊 长期风险 (基于日K数据):")
        for risk_type, risk_data in self.long_term_risks.items():
            if risk_type != 'data_source':
                print(f"   {risk_type}: {risk_data['level']} - {risk_data['description']}")
        
        print(f"⚡ 短期风险 (基于时K数据):")
        for risk_type, risk_data in self.short_term_risks.items():
            if risk_type != 'data_source':
                print(f"   {risk_type}: {risk_data['level']} - {risk_data['description']}")
        
        print(f"🚨 异常风险 (基于走势数据):")
        print(f"   异常风险等级: {self.anomaly_risks['anomaly_risk_level']}")
        print(f"   检测到异常: {self.anomaly_risks['total_anomalies']}个")
        print(f"   高风险异常: {self.anomaly_risks['high_risk_anomalies']}个")
        
        print(f"🎯 综合风险评估:")
        print(f"   综合风险等级: {self.comprehensive_risk['overall_risk_level']}")
        print(f"   综合评分: {self.comprehensive_risk['comprehensive_score']:.1f}/3.0")
        print(f"   风险建议: {self.comprehensive_risk['risk_advice']}")
