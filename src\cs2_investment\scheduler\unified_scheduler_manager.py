"""
统一定时器管理器

统一管理饰品信息更新定时器和价格更新定时器，提供统一的启动、停止、状态监控接口。
"""

import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List
from enum import Enum
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.scheduler.item_info_update_scheduler import ItemInfoUpdateScheduler
from src.cs2_investment.scheduler.price_update_scheduler import PriceUpdateScheduler
from src.cs2_investment.scheduler.steamdt_id_update_scheduler import SteamDTIdUpdateScheduler
from src.cs2_investment.config.timer_config import get_timer_config, TimerConfig
from src.cs2_investment.api.monitoring import get_timer_monitor, TimerMonitor
from src.cs2_investment.utils.logger import get_timer_logger


class SchedulerType(Enum):
    """定时器类型枚举"""
    ITEM_INFO_UPDATE = "item_info_update"
    PRICE_UPDATE = "price_update"
    STEAMDT_ID_UPDATE = "steamdt_id_update"


class SchedulerStatus(Enum):
    """定时器状态枚举"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


class UnifiedSchedulerManager:
    """统一定时器管理器"""
    
    def __init__(self, api_key: str = None, user_id: str = "default_user", config: TimerConfig = None):
        """
        初始化统一定时器管理器

        Args:
            api_key: SteamDT API密钥（可选，优先从配置获取）
            user_id: 用户ID
            config: 定时器配置（可选，默认从环境变量加载）
        """
        # 加载配置
        self.config = config or get_timer_config()

        # API密钥优先级：参数 > 配置 > 环境变量
        self.api_key = api_key or self.config.api.steamdt_api_key
        self.user_id = user_id
        self.logger = get_timer_logger(__name__, "unified_manager")

        # 初始化监控器
        self.monitor = get_timer_monitor("unified_scheduler_manager")

        # 验证配置
        validation_result = self.config.validate_all()
        if not validation_result['valid']:
            raise ValueError(f"配置验证失败: {validation_result['errors']}")

        if validation_result['warnings']:
            for warning in validation_result['warnings']:
                self.logger.warning(f"配置警告: {warning}")

        # 创建定时器实例（仅在启用时创建）
        self.item_info_scheduler = None
        self.price_update_scheduler = None
        self.steamdt_id_scheduler = None

        if self.config.item_info_update.enabled:
            self.item_info_scheduler = ItemInfoUpdateScheduler(self.api_key)

        # 启用价格更新调度器（SteamDT平台价格更新）- 已禁用，使用简化价格更新替代
        # if self.config.price_update.enabled:
        #     self.price_update_scheduler = PriceUpdateScheduler(self.api_key, user_id)

        if self.config.steamdt_id_update.enabled:
            self.steamdt_id_scheduler = SteamDTIdUpdateScheduler(self.config.steamdt_id_update)
        
        # 管理器状态
        self.is_running = False
        self.start_time = None
        self.status = SchedulerStatus.STOPPED
        
        # 定时器状态跟踪
        self.scheduler_states = {
            SchedulerType.ITEM_INFO_UPDATE: SchedulerStatus.STOPPED,
            SchedulerType.PRICE_UPDATE: SchedulerStatus.STOPPED,
            SchedulerType.STEAMDT_ID_UPDATE: SchedulerStatus.STOPPED
        }
        
        # 统计信息
        self.manager_stats = {
            'start_count': 0,
            'stop_count': 0,
            'restart_count': 0,
            'error_count': 0,
            'last_start_time': None,
            'last_stop_time': None,
            'total_uptime_seconds': 0
        }
        
        # 运行时配置（从配置对象提取）
        self.runtime_config = {
            'auto_restart_on_failure': True,  # 默认启用自动重启
            'health_check_interval': self.config.item_info_update.health_check_interval * 60,  # 转换为秒
            'startup_delay_seconds': self.config.scheduler.startup_delay,
            'shutdown_timeout_seconds': self.config.scheduler.shutdown_timeout
        }
        
        self.logger.info("🏗️ 统一定时器管理器初始化完成")
    
    async def start_all(self):
        """启动所有定时器"""
        if self.is_running:
            self.logger.warning("统一定时器管理器已经在运行")
            return

        start_time = datetime.now()
        try:
            self.logger.info("🚀 启动统一定时器管理器")
            self.status = SchedulerStatus.STARTING
            self.start_time = start_time
            
            # 根据配置启动定时器
            started_schedulers = []

            if self.config.item_info_update.enabled and self.item_info_scheduler:
                await self._start_scheduler(SchedulerType.ITEM_INFO_UPDATE)
                started_schedulers.append("饰品信息更新")

            # 延迟启动价格更新定时器，避免同时启动造成资源竞争
            if started_schedulers:
                await asyncio.sleep(self.runtime_config['startup_delay_seconds'])

            # 启用价格更新调度器（SteamDT平台价格更新）- 已禁用，使用简化价格更新替代
            # if self.config.price_update.enabled and self.price_update_scheduler:
            #     await self._start_scheduler(SchedulerType.PRICE_UPDATE)
            #     started_schedulers.append("价格更新")

            # 延迟启动SteamDT ID更新调度器，避免资源竞争
            if started_schedulers:
                await asyncio.sleep(self.runtime_config['startup_delay_seconds'])

            if self.config.steamdt_id_update.enabled and self.steamdt_id_scheduler:
                await self._start_scheduler(SchedulerType.STEAMDT_ID_UPDATE)
                started_schedulers.append("SteamDT ID更新")

            if not started_schedulers:
                self.logger.warning("没有启用任何定时器")
                return

            self.logger.info(f"已启动定时器: {', '.join(started_schedulers)}")

            # 更新状态
            self.is_running = True
            self.status = SchedulerStatus.RUNNING
            self.manager_stats['start_count'] += 1
            self.manager_stats['last_start_time'] = self.start_time

            # 记录启动性能
            duration = (datetime.now() - start_time).total_seconds()
            self.monitor.record_operation(
                "start_all", duration, True,
                schedulers_started=len(started_schedulers),
                scheduler_list=started_schedulers
            )

            self.logger.info("✅ 统一定时器管理器启动成功")
            
        except Exception as e:
            self.logger.error(f"❌ 启动统一定时器管理器失败: {e}")
            self.status = SchedulerStatus.ERROR
            self.manager_stats['error_count'] += 1

            # 记录启动失败
            duration = (datetime.now() - start_time).total_seconds()
            self.monitor.record_operation(
                "start_all", duration, False, str(e),
                error_type=type(e).__name__
            )

            # 尝试清理已启动的定时器
            await self._cleanup_on_failure()
            raise
    
    def stop_all(self):
        """停止所有定时器"""
        if not self.is_running:
            return

        try:
            self.logger.info("⏹️ 停止统一定时器管理器")
            self.status = SchedulerStatus.STOPPING
            
            # 停止所有定时器
            if self.steamdt_id_scheduler:
                self._stop_scheduler(SchedulerType.STEAMDT_ID_UPDATE)
            # if self.price_update_scheduler:
            #     self._stop_scheduler(SchedulerType.PRICE_UPDATE)
            if self.item_info_scheduler:
                self._stop_scheduler(SchedulerType.ITEM_INFO_UPDATE)
            
            # 更新状态
            self.is_running = False
            self.status = SchedulerStatus.STOPPED
            self.manager_stats['stop_count'] += 1
            self.manager_stats['last_stop_time'] = datetime.now()
            
            # 计算运行时间
            if self.start_time:
                uptime = (datetime.now() - self.start_time).total_seconds()
                self.manager_stats['total_uptime_seconds'] += uptime
            
            self.logger.info("✅ 统一定时器管理器已停止")
            
        except Exception as e:
            self.logger.error(f"❌ 停止统一定时器管理器失败: {e}")
            self.status = SchedulerStatus.ERROR
            self.manager_stats['error_count'] += 1
    
    async def _start_scheduler(self, scheduler_type: SchedulerType):
        """启动指定类型的定时器"""
        try:
            self.logger.info(f"🔄 启动{scheduler_type.value}定时器")
            self.scheduler_states[scheduler_type] = SchedulerStatus.STARTING
            
            if scheduler_type == SchedulerType.ITEM_INFO_UPDATE:
                await self.item_info_scheduler.start()
            # elif scheduler_type == SchedulerType.PRICE_UPDATE:
            #     await self.price_update_scheduler.start()
            elif scheduler_type == SchedulerType.STEAMDT_ID_UPDATE:
                await self.steamdt_id_scheduler.start()
            
            self.scheduler_states[scheduler_type] = SchedulerStatus.RUNNING
            self.logger.info(f"✅ {scheduler_type.value}定时器启动成功")
            
        except Exception as e:
            self.logger.error(f"❌ 启动{scheduler_type.value}定时器失败: {e}")
            self.scheduler_states[scheduler_type] = SchedulerStatus.ERROR
            raise
    
    def _stop_scheduler(self, scheduler_type: SchedulerType):
        """停止指定类型的定时器"""
        try:
            self.logger.info(f"⏹️ 停止{scheduler_type.value}定时器")
            self.scheduler_states[scheduler_type] = SchedulerStatus.STOPPING
            
            if scheduler_type == SchedulerType.ITEM_INFO_UPDATE:
                self.item_info_scheduler.stop()
            # elif scheduler_type == SchedulerType.PRICE_UPDATE:
            #     self.price_update_scheduler.stop()
            elif scheduler_type == SchedulerType.STEAMDT_ID_UPDATE:
                asyncio.create_task(self.steamdt_id_scheduler.stop())
            
            self.scheduler_states[scheduler_type] = SchedulerStatus.STOPPED
            self.logger.info(f"✅ {scheduler_type.value}定时器已停止")
            
        except Exception as e:
            self.logger.error(f"❌ 停止{scheduler_type.value}定时器失败: {e}")
            self.scheduler_states[scheduler_type] = SchedulerStatus.ERROR
    
    async def _cleanup_on_failure(self):
        """失败时清理已启动的定时器"""
        self.logger.info("🧹 清理已启动的定时器")
        
        try:
            # 停止所有可能已启动的定时器
            for scheduler_type in SchedulerType:
                if self.scheduler_states[scheduler_type] in [SchedulerStatus.STARTING, SchedulerStatus.RUNNING]:
                    self._stop_scheduler(scheduler_type)
        except Exception as e:
            self.logger.error(f"清理定时器失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取所有定时器状态"""
        try:
            # 计算运行时间
            uptime = 0
            if self.is_running and self.start_time:
                uptime = (datetime.now() - self.start_time).total_seconds()
            
            # 获取各个定时器的详细状态
            item_info_status = self.item_info_scheduler.get_status() if self.item_info_scheduler else None
            # price_update_status = self.price_update_scheduler.get_status() if self.price_update_scheduler else None
            steamdt_id_status = self.steamdt_id_scheduler.get_status() if self.steamdt_id_scheduler else None
            
            return {
                'manager_status': {
                    'is_running': self.is_running,
                    'status': self.status.value,
                    'start_time': self.start_time.isoformat() if self.start_time else None,
                    'uptime_seconds': uptime,
                    'api_key': self.api_key[:10] + "..." if self.api_key else None,
                    'user_id': self.user_id
                },
                'scheduler_states': {
                    scheduler_type.value: status.value 
                    for scheduler_type, status in self.scheduler_states.items()
                },
                'schedulers': {
                    'item_info_update': item_info_status if self.item_info_scheduler else None,
                    # 'price_update': price_update_status if self.price_update_scheduler else None,
                    'steamdt_id_update': steamdt_id_status if self.steamdt_id_scheduler else None
                },
                'manager_stats': self.manager_stats.copy(),
                'config': self.config.get_summary(),
                'runtime_config': self.runtime_config.copy(),
                'health_summary': self._get_health_summary(),
                'monitoring': self.monitor.get_status()
            }
        except Exception as e:
            self.logger.error(f"获取状态失败: {e}")
            return {
                'manager_status': {
                    'is_running': self.is_running,
                    'status': SchedulerStatus.ERROR.value,
                    'error': str(e)
                }
            }
    
    def _get_health_summary(self) -> Dict[str, Any]:
        """获取健康状态摘要"""
        try:
            health_issues = []
            healthy_schedulers = 0
            total_schedulers = len(SchedulerType)
            
            # 检查各个定时器状态
            for scheduler_type, status in self.scheduler_states.items():
                if status == SchedulerStatus.RUNNING:
                    healthy_schedulers += 1
                elif status == SchedulerStatus.ERROR:
                    health_issues.append(f"{scheduler_type.value}定时器异常")
                elif status == SchedulerStatus.STOPPED and self.is_running:
                    health_issues.append(f"{scheduler_type.value}定时器意外停止")
            
            # 计算健康分数
            health_score = (healthy_schedulers / total_schedulers) * 100
            
            # 检查错误率
            total_operations = self.manager_stats['start_count'] + self.manager_stats['stop_count']
            if total_operations > 0:
                error_rate = self.manager_stats['error_count'] / total_operations
                if error_rate > 0.1:  # 错误率超过10%
                    health_score -= 20
                    health_issues.append(f"操作错误率过高: {error_rate:.1%}")
            
            return {
                'health_score': max(0, health_score),
                'is_healthy': health_score >= 80 and len(health_issues) == 0,
                'healthy_schedulers': healthy_schedulers,
                'total_schedulers': total_schedulers,
                'health_issues': health_issues
            }
        except Exception as e:
            return {
                'health_score': 0,
                'is_healthy': False,
                'health_issues': [f"健康检查异常: {str(e)}"]
            }
    
    async def restart_scheduler(self, scheduler_type: SchedulerType):
        """重启指定定时器"""
        self.logger.info(f"🔄 重启{scheduler_type.value}定时器")
        
        try:
            # 停止定时器
            self._stop_scheduler(scheduler_type)
            await asyncio.sleep(1)  # 等待停止完成
            
            # 重新启动定时器
            await self._start_scheduler(scheduler_type)
            
            self.manager_stats['restart_count'] += 1
            self.logger.info(f"✅ {scheduler_type.value}定时器重启成功")
            
        except Exception as e:
            self.logger.error(f"❌ 重启{scheduler_type.value}定时器失败: {e}")
            self.manager_stats['error_count'] += 1
            raise
    
    async def restart_all(self):
        """重启所有定时器"""
        self.logger.info("🔄 重启所有定时器")
        
        try:
            # 停止所有定时器
            self.stop_all()
            await asyncio.sleep(2)  # 等待停止完成
            
            # 重新启动所有定时器
            await self.start_all()
            
            self.logger.info("✅ 所有定时器重启成功")
            
        except Exception as e:
            self.logger.error(f"❌ 重启所有定时器失败: {e}")
            raise

    def reload_config(self) -> Dict[str, Any]:
        """重新加载配置"""
        try:
            self.logger.info("🔄 重新加载定时器配置")

            # 重新加载配置
            from src.cs2_investment.config.timer_config import reload_timer_config
            new_config = reload_timer_config()

            # 验证新配置
            validation_result = new_config.validate_all()
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': f"新配置验证失败: {validation_result['errors']}"
                }

            # 更新配置
            old_config_summary = self.config.get_summary()
            self.config = new_config

            # 更新运行时配置
            self.runtime_config = {
                'auto_restart_on_failure': True,  # 默认启用自动重启
                'health_check_interval': self.config.item_info_update.health_check_interval * 60,  # 转换为秒
                'startup_delay_seconds': self.config.scheduler.startup_delay,
                'shutdown_timeout_seconds': self.config.scheduler.shutdown_timeout
            }

            new_config_summary = self.config.get_summary()

            self.logger.info("✅ 配置重新加载成功")

            return {
                'success': True,
                'old_config': old_config_summary,
                'new_config': new_config_summary,
                'warnings': validation_result.get('warnings', [])
            }

        except Exception as e:
            self.logger.error(f"❌ 重新加载配置失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def update_config(self, config_updates: Dict[str, Any]) -> Dict[str, Any]:
        """更新配置（热更新）"""
        try:
            self.logger.info("🔧 更新定时器配置")

            # 备份当前配置
            old_config_summary = self.config.get_summary()

            # 应用配置更新
            success = self.config.update_from_dict(config_updates)
            if not success:
                return {
                    'success': False,
                    'error': "配置更新失败：无效的配置格式"
                }

            # 验证更新后的配置
            validation_result = self.config.validate_all()
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': f"更新后配置验证失败: {validation_result['errors']}"
                }

            # 更新运行时配置
            self.runtime_config = {
                'auto_restart_on_failure': True,  # 默认启用自动重启
                'health_check_interval': self.config.item_info_update.health_check_interval * 60,  # 转换为秒
                'startup_delay_seconds': self.config.scheduler.startup_delay,
                'shutdown_timeout_seconds': self.config.scheduler.shutdown_timeout
            }

            new_config_summary = self.config.get_summary()

            self.logger.info("✅ 配置更新成功")

            return {
                'success': True,
                'old_config': old_config_summary,
                'new_config': new_config_summary,
                'warnings': validation_result.get('warnings', [])
            }

        except Exception as e:
            self.logger.error(f"❌ 配置更新失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
