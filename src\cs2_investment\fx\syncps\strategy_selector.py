#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品分析系统 - 策略选择器
基于市场特征和分析结果智能选择最适合的交易策略
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class StrategySelector:
    """策略选择器 - 智能策略匹配和权重分配"""
    
    def __init__(self, market_characteristics: Dict, technical_signals: Dict, 
                 sentiment_analysis: Dict, risk_assessment: Dict):
        """
        初始化策略选择器
        
        Args:
            market_characteristics: 市场特征分析结果
            technical_signals: 技术信号分析结果
            sentiment_analysis: 情绪分析结果
            risk_assessment: 风险评估结果
        """
        self.market_characteristics = market_characteristics
        self.technical_signals = technical_signals
        self.sentiment_analysis = sentiment_analysis
        self.risk_assessment = risk_assessment
        
        # 策略选择结果
        self.strategy_results = {}
        
        # 预定义策略库
        self.strategy_library = self._initialize_strategy_library()
    
    def _initialize_strategy_library(self) -> Dict:
        """初始化策略库"""
        return {
            'trend_following': {
                'name': '趋势跟踪策略',
                'description': '基于技术分析的趋势跟踪，适合高活跃市场',
                'suitable_markets': ['高活跃交易市场', '中等活跃市场'],
                'technical_weight': 0.70,
                'fundamental_weight': 0.20,
                'sentiment_weight': 0.10,
                'min_volatility': 0.02,
                'max_risk_tolerance': 'MEDIUM',
                'best_conditions': {
                    'trend_clarity': 'STRONG',
                    'volume_confirmation': 'HIGH',
                    'sentiment_alignment': 'CONSISTENT'
                }
            },
            'mean_reversion': {
                'name': '均值回归策略',
                'description': '基于价格回归均值的策略，适合震荡市场',
                'suitable_markets': ['中等活跃市场', '低活跃市场'],
                'technical_weight': 0.50,
                'fundamental_weight': 0.30,
                'sentiment_weight': 0.20,
                'min_volatility': 0.01,
                'max_risk_tolerance': 'HIGH',
                'best_conditions': {
                    'trend_clarity': 'WEAK',
                    'volatility_level': 'MODERATE',
                    'sentiment_extremes': 'PRESENT'
                }
            },
            'fundamental_value': {
                'name': '基本面价值策略',
                'description': '基于供需基本面的价值投资策略',
                'suitable_markets': ['低活跃市场', '收藏品市场'],
                'technical_weight': 0.20,
                'fundamental_weight': 0.60,
                'sentiment_weight': 0.20,
                'min_volatility': 0.005,
                'max_risk_tolerance': 'HIGH',
                'best_conditions': {
                    'supply_demand_imbalance': 'SIGNIFICANT',
                    'fundamental_strength': 'STRONG',
                    'long_term_outlook': 'POSITIVE'
                }
            },
            'momentum_breakout': {
                'name': '动量突破策略',
                'description': '基于动量和突破的短期交易策略',
                'suitable_markets': ['高活跃交易市场'],
                'technical_weight': 0.80,
                'fundamental_weight': 0.10,
                'sentiment_weight': 0.10,
                'min_volatility': 0.03,
                'max_risk_tolerance': 'LOW',
                'best_conditions': {
                    'momentum_strength': 'STRONG',
                    'breakout_confirmation': 'CLEAR',
                    'volume_surge': 'PRESENT'
                }
            },
            'contrarian_sentiment': {
                'name': '逆向情绪策略',
                'description': '基于情绪极值的逆向投资策略',
                'suitable_markets': ['高活跃交易市场', '中等活跃市场'],
                'technical_weight': 0.30,
                'fundamental_weight': 0.30,
                'sentiment_weight': 0.40,
                'min_volatility': 0.02,
                'max_risk_tolerance': 'MEDIUM',
                'best_conditions': {
                    'sentiment_extremes': 'EXTREME',
                    'contrarian_signals': 'STRONG',
                    'fundamental_support': 'PRESENT'
                }
            },
            'risk_parity': {
                'name': '风险平价策略',
                'description': '基于风险平衡的保守投资策略',
                'suitable_markets': ['所有市场'],
                'technical_weight': 0.40,
                'fundamental_weight': 0.40,
                'sentiment_weight': 0.20,
                'min_volatility': 0.01,
                'max_risk_tolerance': 'HIGH',
                'best_conditions': {
                    'risk_diversification': 'BALANCED',
                    'uncertainty_high': 'TRUE',
                    'preservation_priority': 'HIGH'
                }
            }
        }
    
    def select_optimal_strategy(self) -> Dict:
        """选择最优策略"""
        
        print("🎯 策略选择分析")
        
        # 1. 评估每个策略的适合度
        strategy_scores = self._evaluate_all_strategies()
        
        # 2. 选择最优策略
        optimal_strategy = self._select_best_strategy(strategy_scores)
        
        # 3. 动态调整权重
        adjusted_weights = self._adjust_strategy_weights(optimal_strategy)
        
        # 4. 生成策略建议
        strategy_recommendation = self._generate_strategy_recommendation(
            optimal_strategy, adjusted_weights, strategy_scores
        )
        
        self.strategy_results = {
            'optimal_strategy': optimal_strategy,
            'adjusted_weights': adjusted_weights,
            'strategy_scores': strategy_scores,
            'recommendation': strategy_recommendation
        }
        
        return self.strategy_results
    
    def _evaluate_all_strategies(self) -> Dict:
        """评估所有策略的适合度"""
        
        strategy_scores = {}
        
        for strategy_id, strategy in self.strategy_library.items():
            score = self._calculate_strategy_score(strategy_id, strategy)
            strategy_scores[strategy_id] = {
                'score': score,
                'strategy': strategy,
                'reasoning': self._generate_score_reasoning(strategy_id, strategy, score)
            }
        
        return strategy_scores
    
    def _calculate_strategy_score(self, strategy_id: str, strategy: Dict) -> float:
        """计算单个策略的适合度评分"""
        
        score = 0.0
        max_score = 100.0
        
        # 1. 市场类型匹配度 (30分)
        market_type = self.market_characteristics['market_type']
        if market_type in strategy['suitable_markets'] or '所有市场' in strategy['suitable_markets']:
            score += 30
        elif self._is_similar_market_type(market_type, strategy['suitable_markets']):
            score += 20
        else:
            score += 5
        
        # 2. 波动率匹配度 (20分)
        volatility = self.market_characteristics['daily_volatility'] / 100
        min_vol = strategy['min_volatility']
        
        if volatility >= min_vol:
            if volatility <= min_vol * 3:  # 理想范围
                score += 20
            else:  # 波动率过高
                score += 15
        else:
            score += 5  # 波动率不足
        
        # 3. 风险承受能力匹配度 (20分)
        risk_level = self.risk_assessment.get('risk_assessment', {}).get('overall_risk', 'MEDIUM')
        max_risk = strategy['max_risk_tolerance']
        
        risk_compatibility = self._check_risk_compatibility(risk_level, max_risk)
        score += risk_compatibility * 20
        
        # 4. 技术信号强度匹配度 (15分)
        technical_strength = self._assess_technical_strength()
        if strategy['technical_weight'] > 0.5 and technical_strength > 0.7:
            score += 15
        elif strategy['technical_weight'] <= 0.5 and technical_strength <= 0.5:
            score += 15
        else:
            score += 10
        
        # 5. 基本面强度匹配度 (10分)
        fundamental_strength = self._assess_fundamental_strength()
        if strategy['fundamental_weight'] > 0.4 and fundamental_strength > 0.6:
            score += 10
        elif strategy['fundamental_weight'] <= 0.4 and fundamental_strength <= 0.4:
            score += 10
        else:
            score += 7
        
        # 6. 情绪分析匹配度 (5分)
        sentiment_extremity = self._assess_sentiment_extremity()
        if strategy['sentiment_weight'] > 0.3 and sentiment_extremity > 0.7:
            score += 5
        elif strategy['sentiment_weight'] <= 0.3:
            score += 5
        else:
            score += 3
        
        return min(score, max_score)
    
    def _is_similar_market_type(self, current_type: str, suitable_types: List[str]) -> bool:
        """判断市场类型是否相似"""
        similarity_map = {
            '高活跃交易市场': ['中等活跃市场'],
            '中等活跃市场': ['高活跃交易市场', '低活跃市场'],
            '低活跃市场': ['中等活跃市场', '收藏品市场'],
            '收藏品市场': ['低活跃市场']
        }
        
        similar_types = similarity_map.get(current_type, [])
        return any(similar_type in suitable_types for similar_type in similar_types)
    
    def _check_risk_compatibility(self, current_risk: str, max_risk: str) -> float:
        """检查风险兼容性"""
        risk_levels = {'LOW': 1, 'MEDIUM': 2, 'HIGH': 3}
        
        current_level = risk_levels.get(current_risk, 2)
        max_level = risk_levels.get(max_risk, 2)
        
        if current_level <= max_level:
            return 1.0
        elif current_level == max_level + 1:
            return 0.7
        else:
            return 0.3
    
    def _assess_technical_strength(self) -> float:
        """评估技术分析信号强度"""
        signal = self.technical_signals['overall_signal']
        
        strength_map = {
            'BULLISH': 0.8,
            'BEARISH': 0.8,
            'NEUTRAL': 0.3
        }
        
        return strength_map.get(signal, 0.5)
    
    def _assess_fundamental_strength(self) -> float:
        """评估基本面分析强度"""
        # 基于供需情绪评估基本面强度
        if 'supply_demand_sentiment' in self.sentiment_analysis:
            supply_demand = self.sentiment_analysis['supply_demand_sentiment']
            score = supply_demand['overall_score']
            
            # 将0-100分转换为0-1的强度
            if score >= 70 or score <= 30:  # 极端值表示强度高
                return 0.8
            elif score >= 60 or score <= 40:  # 中等偏离
                return 0.6
            else:  # 中性
                return 0.4
        
        return 0.5
    
    def _assess_sentiment_extremity(self) -> float:
        """评估情绪极端程度"""
        if 'comprehensive_sentiment' in self.sentiment_analysis:
            sentiment = self.sentiment_analysis['comprehensive_sentiment']
            score = sentiment['comprehensive_score']
            
            # 计算与中性(50)的距离
            extremity = abs(score - 50) / 50
            return min(extremity, 1.0)
        
        return 0.5
    
    def _select_best_strategy(self, strategy_scores: Dict) -> Dict:
        """选择最佳策略"""
        
        best_strategy_id = max(strategy_scores.keys(), 
                              key=lambda x: strategy_scores[x]['score'])
        
        best_strategy = strategy_scores[best_strategy_id]
        
        return {
            'strategy_id': best_strategy_id,
            'strategy_name': best_strategy['strategy']['name'],
            'strategy_description': best_strategy['strategy']['description'],
            'score': best_strategy['score'],
            'reasoning': best_strategy['reasoning'],
            'base_weights': {
                'technical': best_strategy['strategy']['technical_weight'],
                'fundamental': best_strategy['strategy']['fundamental_weight'],
                'sentiment': best_strategy['strategy']['sentiment_weight']
            }
        }
    
    def _adjust_strategy_weights(self, optimal_strategy: Dict) -> Dict:
        """动态调整策略权重"""
        
        base_weights = optimal_strategy['base_weights']
        
        # 基于当前市场条件调整权重
        adjustments = self._calculate_weight_adjustments()
        
        adjusted_weights = {
            'technical': max(0.1, min(0.8, base_weights['technical'] + adjustments['technical'])),
            'fundamental': max(0.1, min(0.7, base_weights['fundamental'] + adjustments['fundamental'])),
            'sentiment': max(0.05, min(0.4, base_weights['sentiment'] + adjustments['sentiment']))
        }
        
        # 归一化权重
        total_weight = sum(adjusted_weights.values())
        adjusted_weights = {k: v/total_weight for k, v in adjusted_weights.items()}
        
        return {
            'base_weights': base_weights,
            'adjustments': adjustments,
            'final_weights': adjusted_weights,
            'adjustment_reasoning': self._generate_adjustment_reasoning(adjustments)
        }
    
    def _calculate_weight_adjustments(self) -> Dict:
        """计算权重调整"""
        
        adjustments = {'technical': 0.0, 'fundamental': 0.0, 'sentiment': 0.0}
        
        # 基于技术信号强度调整
        tech_strength = self._assess_technical_strength()
        if tech_strength > 0.7:
            adjustments['technical'] += 0.1
        elif tech_strength < 0.4:
            adjustments['technical'] -= 0.1
        
        # 基于基本面强度调整
        fund_strength = self._assess_fundamental_strength()
        if fund_strength > 0.7:
            adjustments['fundamental'] += 0.1
        elif fund_strength < 0.4:
            adjustments['fundamental'] -= 0.1
        
        # 基于情绪极端程度调整
        sentiment_extremity = self._assess_sentiment_extremity()
        if sentiment_extremity > 0.7:
            adjustments['sentiment'] += 0.1
        elif sentiment_extremity < 0.3:
            adjustments['sentiment'] -= 0.05
        
        # 基于风险水平调整
        risk_level = self.risk_assessment.get('risk_assessment', {}).get('overall_risk', 'MEDIUM')
        if risk_level == 'HIGH':
            adjustments['fundamental'] += 0.05  # 高风险时更重视基本面
            adjustments['technical'] -= 0.05
        elif risk_level == 'LOW':
            adjustments['technical'] += 0.05   # 低风险时可以更重视技术面
        
        return adjustments

    def _generate_score_reasoning(self, strategy_id: str, strategy: Dict, score: float) -> str:
        """生成策略评分理由"""

        market_type = self.market_characteristics['market_type']
        volatility = self.market_characteristics['daily_volatility']
        risk_level = self.risk_assessment.get('risk_assessment', {}).get('overall_risk', 'MEDIUM')

        reasoning = f"{strategy['name']}评分{score:.1f}分: "

        if market_type in strategy['suitable_markets']:
            reasoning += f"市场类型({market_type})完全匹配; "
        else:
            reasoning += f"市场类型({market_type})部分匹配; "

        if volatility >= strategy['min_volatility'] * 100:
            reasoning += f"波动率({volatility:.2f}%)满足要求; "
        else:
            reasoning += f"波动率({volatility:.2f}%)略低; "

        reasoning += f"风险水平({risk_level})与策略兼容"

        return reasoning

    def _generate_adjustment_reasoning(self, adjustments: Dict) -> str:
        """生成权重调整理由"""

        reasoning_parts = []

        if adjustments['technical'] > 0:
            reasoning_parts.append("技术信号强烈，增加技术分析权重")
        elif adjustments['technical'] < 0:
            reasoning_parts.append("技术信号较弱，降低技术分析权重")

        if adjustments['fundamental'] > 0:
            reasoning_parts.append("基本面因素突出，增加基本面权重")
        elif adjustments['fundamental'] < 0:
            reasoning_parts.append("基本面因素一般，降低基本面权重")

        if adjustments['sentiment'] > 0:
            reasoning_parts.append("情绪极端明显，增加情绪分析权重")
        elif adjustments['sentiment'] < 0:
            reasoning_parts.append("情绪相对平稳，降低情绪分析权重")

        if not reasoning_parts:
            return "市场条件均衡，维持基础权重配置"

        return "; ".join(reasoning_parts)

    def _generate_strategy_recommendation(self, optimal_strategy: Dict,
                                        adjusted_weights: Dict, strategy_scores: Dict) -> Dict:
        """生成策略建议"""

        # 获取前三名策略
        sorted_strategies = sorted(strategy_scores.items(),
                                 key=lambda x: x[1]['score'], reverse=True)

        top_strategies = sorted_strategies[:3]

        # 生成具体操作建议
        operation_advice = self._generate_operation_advice(optimal_strategy, adjusted_weights)

        # 生成风险提示
        risk_warnings = self._generate_risk_warnings(optimal_strategy)

        # 生成替代策略建议
        alternative_strategies = self._generate_alternative_strategies(top_strategies[1:])

        return {
            'primary_strategy': {
                'name': optimal_strategy['strategy_name'],
                'description': optimal_strategy['strategy_description'],
                'score': optimal_strategy['score'],
                'confidence_level': self._calculate_confidence_level(optimal_strategy['score'])
            },
            'weight_allocation': adjusted_weights['final_weights'],
            'operation_advice': operation_advice,
            'risk_warnings': risk_warnings,
            'alternative_strategies': alternative_strategies,
            'strategy_ranking': [
                {
                    'rank': i+1,
                    'name': strategy_scores[strategy_id]['strategy']['name'],
                    'score': strategy_scores[strategy_id]['score'],
                    'reasoning': strategy_scores[strategy_id]['reasoning']
                }
                for i, (strategy_id, _) in enumerate(top_strategies)
            ]
        }

    def _calculate_confidence_level(self, score: float) -> str:
        """计算置信度等级"""
        if score >= 85:
            return 'VERY_HIGH'
        elif score >= 75:
            return 'HIGH'
        elif score >= 65:
            return 'MEDIUM'
        elif score >= 50:
            return 'LOW'
        else:
            return 'VERY_LOW'

    def _generate_operation_advice(self, optimal_strategy: Dict, adjusted_weights: Dict) -> Dict:
        """生成具体操作建议"""

        strategy_id = optimal_strategy['strategy_id']
        final_weights = adjusted_weights['final_weights']

        # 基于策略类型生成具体建议
        if strategy_id == 'trend_following':
            return {
                'entry_conditions': '等待趋势确认信号，如EMA金叉或突破关键阻力位',
                'exit_conditions': '趋势反转信号出现，如EMA死叉或跌破关键支撑位',
                'position_sizing': '根据趋势强度调整仓位，强趋势可适当加仓',
                'stop_loss': '设置在关键支撑/阻力位下方/上方2-3%',
                'take_profit': '趋势延续时持有，出现反转信号时获利了结'
            }
        elif strategy_id == 'mean_reversion':
            return {
                'entry_conditions': 'RSI超买(>70)或超卖(<30)时逆向操作',
                'exit_conditions': 'RSI回归中性区间(40-60)时平仓',
                'position_sizing': '分批建仓，避免一次性重仓',
                'stop_loss': '设置较宽止损，给价格回归时间',
                'take_profit': '目标价位为近期均值附近'
            }
        elif strategy_id == 'fundamental_value':
            return {
                'entry_conditions': '供需失衡明显，求购溢价异常或供给稀缺时买入',
                'exit_conditions': '基本面恶化或估值过高时卖出',
                'position_sizing': '基于基本面强度决定仓位大小',
                'stop_loss': '基本面支撑位下方设置止损',
                'take_profit': '达到合理估值时分批获利'
            }
        elif strategy_id == 'momentum_breakout':
            return {
                'entry_conditions': '价格突破关键阻力位且成交量放大时买入',
                'exit_conditions': '动量衰减或跌破突破点时卖出',
                'position_sizing': '突破确认后快速建仓',
                'stop_loss': '突破点下方设置紧密止损',
                'take_profit': '动量延续时持有，衰减时快速获利'
            }
        elif strategy_id == 'contrarian_sentiment':
            return {
                'entry_conditions': '市场情绪极度悲观或乐观时逆向操作',
                'exit_conditions': '情绪回归正常时平仓',
                'position_sizing': '情绪极端程度决定仓位大小',
                'stop_loss': '基于技术支撑设置止损',
                'take_profit': '情绪修复到中性时获利'
            }
        else:  # risk_parity
            return {
                'entry_conditions': '分散投资，平衡各类风险因子',
                'exit_conditions': '定期再平衡，调整权重配置',
                'position_sizing': '基于风险贡献度分配仓位',
                'stop_loss': '组合层面设置风险控制',
                'take_profit': '长期持有，定期获利了结'
            }

    def _generate_risk_warnings(self, optimal_strategy: Dict) -> List[str]:
        """生成风险提示"""

        warnings = []
        strategy_id = optimal_strategy['strategy_id']

        # 通用风险提示
        overall_risk = self.risk_assessment.get('risk_assessment', {}).get('overall_risk', 'MEDIUM')
        if overall_risk == 'HIGH':
            warnings.append("当前市场风险较高，建议降低仓位或暂停交易")

        # 策略特定风险提示
        if strategy_id == 'trend_following':
            warnings.append("趋势跟踪策略在震荡市场中容易产生假信号")
            if self.market_characteristics['daily_volatility'] < 2:
                warnings.append("当前波动率较低，趋势信号可能不够明确")

        elif strategy_id == 'mean_reversion':
            warnings.append("均值回归策略在强趋势市场中风险较高")
            if self.technical_signals['overall_signal'] != 'NEUTRAL':
                warnings.append("当前存在明确趋势，均值回归策略需谨慎使用")

        elif strategy_id == 'momentum_breakout':
            warnings.append("动量突破策略容易遭遇假突破，需严格止损")
            warnings.append("高频交易增加交易成本，需考虑成本影响")

        elif strategy_id == 'contrarian_sentiment':
            warnings.append("逆向策略需要精确的时机把握，过早入场风险较大")
            warnings.append("市场可能比预期更长时间保持非理性")

        # 基于当前市场条件的特殊提示
        enhanced_risk = self.risk_assessment.get('enhanced_risk_assessment', {})
        if 'anomaly_risks' in enhanced_risk:
            anomaly_count = enhanced_risk['anomaly_risks'].get('total_anomalies', 0)
            if anomaly_count > 15:
                warnings.append(f"检测到{anomaly_count}个市场异常，建议密切监控")

        return warnings

    def _generate_alternative_strategies(self, alternative_strategies: List) -> List[Dict]:
        """生成替代策略建议"""

        alternatives = []

        for strategy_id, strategy_data in alternative_strategies:
            alternatives.append({
                'name': strategy_data['strategy']['name'],
                'score': strategy_data['score'],
                'reasoning': strategy_data['reasoning'],
                'when_to_consider': self._generate_alternative_conditions(strategy_id)
            })

        return alternatives

    def _generate_alternative_conditions(self, strategy_id: str) -> str:
        """生成替代策略使用条件"""

        conditions_map = {
            'trend_following': '当市场出现明确趋势且技术信号强烈时考虑',
            'mean_reversion': '当市场进入震荡区间且出现超买超卖时考虑',
            'fundamental_value': '当基本面出现重大变化或估值偏离时考虑',
            'momentum_breakout': '当出现重大突破且成交量配合时考虑',
            'contrarian_sentiment': '当市场情绪达到极端水平时考虑',
            'risk_parity': '当市场不确定性增加需要风险分散时考虑'
        }

        return conditions_map.get(strategy_id, '根据市场条件变化时考虑')

    def print_strategy_summary(self):
        """打印策略选择摘要"""

        if not self.strategy_results:
            print("❌ 请先运行 select_optimal_strategy()")
            return

        optimal = self.strategy_results['optimal_strategy']
        weights = self.strategy_results['adjusted_weights']
        recommendation = self.strategy_results['recommendation']

        print(f"🎯 最优策略: {optimal['strategy_name']}")
        print(f"   策略评分: {optimal['score']:.1f}/100")
        print(f"   置信度: {recommendation['primary_strategy']['confidence_level']}")
        print(f"   策略描述: {optimal['strategy_description']}")

        print(f"⚖️ 权重配置:")
        final_weights = weights['final_weights']
        print(f"   技术分析: {final_weights['technical']:.1%}")
        print(f"   基本面分析: {final_weights['fundamental']:.1%}")
        print(f"   情绪分析: {final_weights['sentiment']:.1%}")

        print(f"💡 操作建议:")
        advice = recommendation['operation_advice']
        print(f"   入场条件: {advice['entry_conditions']}")
        print(f"   出场条件: {advice['exit_conditions']}")
        print(f"   仓位管理: {advice['position_sizing']}")

        print(f"⚠️ 风险提示:")
        for warning in recommendation['risk_warnings']:
            print(f"   - {warning}")

    def get_strategy_summary(self) -> Dict:
        """获取策略选择摘要"""

        if not self.strategy_results:
            return {'error': '请先运行 select_optimal_strategy()'}

        optimal = self.strategy_results['optimal_strategy']
        recommendation = self.strategy_results['recommendation']

        return {
            'strategy_name': optimal['strategy_name'],
            'strategy_score': optimal['score'],
            'confidence_level': recommendation['primary_strategy']['confidence_level'],
            'weight_allocation': recommendation['weight_allocation'],
            'key_advice': recommendation['operation_advice']['entry_conditions'],
            'main_risk': recommendation['risk_warnings'][0] if recommendation['risk_warnings'] else '无特殊风险',
            'data_source': '综合策略分析'
        }
