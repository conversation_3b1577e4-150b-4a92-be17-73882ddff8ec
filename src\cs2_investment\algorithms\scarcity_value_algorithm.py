"""
稀缺价值资产筛选算法

识别存世量稀少、供应减少的资产。
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, asc

from .base_algorithm import BaseScreeningAlgorithm
from ..models.market_snapshot import MarketSnapshot


class ScarcityValueAlgorithm(BaseScreeningAlgorithm):
    """稀缺价值资产筛选算法"""
    
    def __init__(self):
        super().__init__(
            algorithm_name="ScarcityValueAlgorithm",
            investment_type="稀缺价值资产",
            version="1.0"
        )
    
    def get_screening_query(self, session: Session):
        """获取稀缺价值资产筛选查询"""
        return self.get_latest_snapshots_query(session)\
            .filter(
                and_(
                    MarketSnapshot.survive_num < 3000,              # 存世量 < 3000
                    MarketSnapshot.survive_num.isnot(None),
                    MarketSnapshot.sell_nums_1m_rate < -10,         # 1个月在售减少 > 10%
                    MarketSnapshot.current_price > 500,             # 价格 > 500
                    # 流通率 < 5%
                    (MarketSnapshot.sell_nums / MarketSnapshot.survive_num * 100) < 5
                )
            )\
            .order_by(asc(MarketSnapshot.sell_nums_1m_rate))
    
    def calculate_score(self, snapshot: MarketSnapshot) -> float:
        """计算稀缺价值评分"""
        score = 0.0
        
        # 稀缺性评分 (40分)
        if snapshot.survive_num:
            if snapshot.survive_num <= 500:
                score += 40
            elif snapshot.survive_num <= 1000:
                score += 35
            elif snapshot.survive_num <= 2000:
                score += 30
            elif snapshot.survive_num <= 3000:
                score += 25
            else:
                score += 15
        
        # 供应减少评分 (30分)
        if snapshot.sell_nums_1m_rate:
            decrease_rate = abs(float(snapshot.sell_nums_1m_rate))
            if decrease_rate >= 50:
                score += 30
            elif decrease_rate >= 30:
                score += 25
            elif decrease_rate >= 20:
                score += 20
            elif decrease_rate >= 10:
                score += 15
            else:
                score += 8
        
        # 价值基础评分 (15分)
        if snapshot.current_price:
            price = float(snapshot.current_price)
            if price >= 10000:
                score += 15
            elif price >= 5000:
                score += 12
            elif price >= 2000:
                score += 9
            elif price >= 1000:
                score += 6
            elif price >= 500:
                score += 3
        
        # 流通率评分 (10分)
        if snapshot.sell_nums and snapshot.survive_num:
            circulation_rate = snapshot.sell_nums / snapshot.survive_num * 100
            if circulation_rate <= 1:
                score += 10
            elif circulation_rate <= 2:
                score += 8
            elif circulation_rate <= 3:
                score += 6
            elif circulation_rate <= 5:
                score += 4
            else:
                score += 2
        
        # 需求稳定性评分 (5分)
        if snapshot.trans_count_1m and snapshot.trans_count_1m > 0:
            if snapshot.trans_count_1m >= 50:
                score += 5
            elif snapshot.trans_count_1m >= 20:
                score += 4
            elif snapshot.trans_count_1m >= 10:
                score += 3
            elif snapshot.trans_count_1m >= 5:
                score += 2
            else:
                score += 1
        
        return min(score, 100.0)
    
    def determine_risk_level(self, snapshot: MarketSnapshot, score: float) -> str:
        """确定稀缺资产风险等级"""
        risk_factors = 0
        
        # 流动性风险
        if not snapshot.trans_count_1m or snapshot.trans_count_1m < 10:
            risk_factors += 2
        elif snapshot.trans_count_1m < 30:
            risk_factors += 1
        
        # 在售数量风险
        if not snapshot.sell_nums or snapshot.sell_nums < 5:
            risk_factors += 2
        elif snapshot.sell_nums < 15:
            risk_factors += 1
        
        # 价格风险
        if snapshot.current_price and float(snapshot.current_price) > 20000:
            risk_factors += 1
        
        # 供应变化风险
        if snapshot.sell_nums_1m_rate and float(snapshot.sell_nums_1m_rate) < -50:
            risk_factors += 1  # 供应急剧减少可能是异常
        
        if risk_factors <= 1:
            return "LOW"
        elif risk_factors <= 3:
            return "MEDIUM"
        else:
            return "HIGH"
    
    def generate_analysis_summary(self, snapshot: MarketSnapshot, score: float) -> str:
        """生成稀缺资产分析摘要"""
        summary_parts = []
        
        # 基础信息
        if snapshot.current_price:
            summary_parts.append(f"价格: ¥{snapshot.current_price}")
        
        # 稀缺性信息
        if snapshot.survive_num:
            summary_parts.append(f"存世量: {snapshot.survive_num}")
        
        if snapshot.sell_nums:
            summary_parts.append(f"在售数量: {snapshot.sell_nums}")
        
        # 流通率
        if snapshot.sell_nums and snapshot.survive_num:
            circulation_rate = snapshot.sell_nums / snapshot.survive_num * 100
            summary_parts.append(f"流通率: {circulation_rate:.1f}%")
        
        # 供应变化
        if snapshot.sell_nums_1m_rate:
            summary_parts.append(f"1月在售减少: {abs(float(snapshot.sell_nums_1m_rate)):.1f}%")
        
        # 成交情况
        if snapshot.trans_count_1m:
            summary_parts.append(f"1月成交量: {snapshot.trans_count_1m}")
        
        # 投资建议
        if score >= 80:
            summary_parts.append("极稀缺资产，长期价值显著")
        elif score >= 60:
            summary_parts.append("稀缺资产，具备收藏价值")
        else:
            summary_parts.append("相对稀缺，需关注流动性")
        
        return "; ".join(summary_parts)
