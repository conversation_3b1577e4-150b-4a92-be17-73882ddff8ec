"""
CS2饰品投资分析系统 - Streamlit主应用

提供Web界面访问系统的各项功能。
"""

import streamlit as st
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.cs2_investment.app.config.app_config import setup_page_config, setup_sidebar
from src.cs2_investment.app.pages import item_query, favorites, holdings, arbitrage_data, config_management


def init_global_services():
    """初始化全局服务，确保在应用启动时就初始化所有必要的服务"""
    try:
        # 初始化分析相关的DAO
        if 'analysis_dao' not in st.session_state:
            from src.cs2_investment.dao.analysis_result_dao import AnalysisResultDAO
            st.session_state.analysis_dao = AnalysisResultDAO()

        if 'realtime_dao' not in st.session_state:
            from src.cs2_investment.dao.analysis_result_dao import RealtimeAnalysisResultDAO
            st.session_state.realtime_dao = RealtimeAnalysisResultDAO()

        if 'holding_service' not in st.session_state:
            from src.cs2_investment.app.services.holding_service import HoldingService
            st.session_state.holding_service = HoldingService()

    except Exception as e:
        st.error(f"❌ 初始化全局服务失败: {e}")


def main():
    """主应用入口"""
    # 设置页面配置
    setup_page_config()

    # 初始化全局服务
    init_global_services()

    # 注意：定时任务调度器已移至API服务中
    # 不再在Streamlit应用中启动定时任务
    if 'scheduler_notice_shown' not in st.session_state:
        st.session_state.scheduler_notice_shown = True
        st.info("📢 定时任务功能已迁移至API服务，请确保API服务正在运行")

    # 设置侧边栏导航
    page = setup_sidebar()

    # 根据选择的页面显示对应内容
    if page == "饰品查询":
        item_query.show_page()
    elif page == "收藏列表":
        favorites.show_page()
    elif page == "持仓管理":
        holdings.show_page()
    elif page == "搬砖数据":
        arbitrage_data.show_page()
    elif page == "配置管理":
        config_management.show_page()
    else:
        show_home_page()


def show_home_page():
    """显示首页"""
    st.title("🎮 CS2饰品投资分析系统")
    
    st.markdown("""
    ## 欢迎使用CS2饰品投资分析系统

    本系统提供以下功能：

    ### 📊 饰品查询
    - 支持多条件筛选查询饰品信息
    - 查看饰品详细信息和价格趋势
    - 收藏感兴趣的饰品
    - 深度分析功能（实时分析、常规分析）

    ### ⭐ 收藏管理
    - 查看和管理收藏的饰品
    - 支持收藏列表的筛选和排序
    - 快速访问收藏的饰品详情
    - 对收藏饰品进行投资分析

    ### 💼 持仓管理
    - 记录和管理饰品持仓信息
    - 实时计算投资盈亏和收益率
    - 投资组合风险分析和结构分析
    - 交易记录管理和统计分析
    - 持仓饰品的深度投资分析

    ### 🔄 搬砖数据管理
    - 导入和管理CS2饰品搬砖数据
    - 支持Steam和Youpin平台数据
    - 智能识别搬砖机会和利润分析
    - 多条件查询和数据导出功能
    - 实时统计和机会预览

    ---

    请使用左侧导航栏选择功能模块开始使用。
    """)
    
    # 显示系统状态
    with st.expander("系统状态", expanded=False):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("数据库状态", "正常", "✅")
        
        with col2:
            st.metric("最后更新", "2小时前", "🔄")
        
        with col3:
            st.metric("饰品总数", "12,345", "📈")


if __name__ == "__main__":
    main()
