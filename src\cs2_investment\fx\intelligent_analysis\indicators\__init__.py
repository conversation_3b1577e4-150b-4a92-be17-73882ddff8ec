"""
CS2饰品智能投资决策系统 - 技术指标计算模块

本模块包含增强的技术指标计算功能：
- EnhancedTechnicalCalculator: 增强技术指标计算器
- SupplyDemandAnalyzer: 供需关系分析器

基于pandas-ta技术指标库，提供130+种技术指标计算。
与现有TechnicalIndicatorCalculator保持兼容性。

版本: 1.0.0
"""

from loguru import logger

# 技术指标模块导入
try:
    from .enhanced_technical_calculator import EnhancedTechnicalCalculator
    from .supply_demand_analyzer import SupplyDemandAnalyzer
    
    logger.info("技术指标计算模块加载成功")
    
except ImportError as e:
    logger.warning(f"技术指标模块部分组件未实现: {e}")
    # 在开发阶段，部分模块可能尚未实现
    pass

# 导出的公共接口
__all__ = [
    'EnhancedTechnicalCalculator',
    'SupplyDemandAnalyzer'
]
