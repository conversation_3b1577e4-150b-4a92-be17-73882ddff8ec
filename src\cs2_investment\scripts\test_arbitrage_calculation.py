#!/usr/bin/env python3
"""
搬砖率计算测试脚本

测试搬砖率计算功能是否正常工作
"""

import sys
import asyncio
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.item_dao import ItemDAO
from src.cs2_investment.dao.platform_price_dao import PlatformPriceDAO
from src.cs2_investment.services.arbitrage_calculator import ArbitrageCalculator
from src.cs2_investment.utils.logger import get_logger

logger = get_logger(__name__)


def test_arbitrage_calculation():
    """测试搬砖率计算"""
    logger.info("🔍 开始测试搬砖率计算...")

    # 初始化DAO和计算器
    item_dao = ItemDAO()
    platform_price_dao = PlatformPriceDAO()
    arbitrage_calculator = ArbitrageCalculator()

    # 1. 获取一些活跃饰品
    logger.info("📦 获取活跃饰品...")
    items = item_dao.get_all_active_items(limit=10)

    if not items:
        logger.warning("⚠️ 没有找到活跃饰品")
        return

    logger.info(f"找到 {len(items)} 个活跃饰品")

    # 2. 测试搬砖率计算
    success_count = 0
    failed_count = 0

    for item in items[:5]:  # 只测试前5个
        item_id = item['item_id']
        market_hash_name = item.get('market_hash_name', 'Unknown')

        logger.info(f"🧮 计算饰品搬砖率: {market_hash_name} (ID: {item_id})")

        # 先检查是否有价格数据
        try:
            prices = platform_price_dao.get_latest_prices_by_item(item_id)
            if not prices:
                logger.warning(f"⚠️ 没有价格数据: {market_hash_name}")
                failed_count += 1
                continue

            logger.info(f"📊 找到 {len(prices)} 个平台的价格数据")
            for price in prices:
                logger.info(f"  - {price.platform_name}: 售价={price.sell_price}, 求购={price.bidding_price}")
        except Exception as e:
            logger.error(f"❌ 获取价格数据失败: {market_hash_name} - {e}")
            failed_count += 1
            continue

        try:
            # 计算搬砖率
            ratio = arbitrage_calculator.calculate_arbitrage_ratio(item_id)

            if ratio is not None:
                logger.info(f"✅ 搬砖率计算成功: {market_hash_name} = {ratio}")

                # 更新到数据库
                if arbitrage_calculator.update_item_arbitrage_ratio(item_id):
                    logger.info(f"✅ 搬砖率更新成功: {market_hash_name}")
                    success_count += 1
                else:
                    logger.warning(f"⚠️ 搬砖率更新失败: {market_hash_name}")
                    failed_count += 1
            else:
                logger.warning(f"⚠️ 无法计算搬砖率: {market_hash_name}")
                failed_count += 1

        except Exception as e:
            logger.error(f"❌ 搬砖率计算异常: {market_hash_name} - {e}")
            failed_count += 1

    logger.info(f"🎯 搬砖率计算测试完成: 成功 {success_count}, 失败 {failed_count}")


def check_arbitrage_data():
    """检查搬砖率数据"""
    logger.info("📊 检查搬砖率数据...")
    
    item_dao = ItemDAO()
    
    # 获取有搬砖率的饰品
    items_with_arbitrage = item_dao.get_items_with_arbitrage_ratio(limit=20)
    
    if not items_with_arbitrage:
        logger.warning("⚠️ 没有找到有搬砖率数据的饰品")
        return
    
    logger.info(f"找到 {len(items_with_arbitrage)} 个有搬砖率数据的饰品")
    
    for item in items_with_arbitrage[:10]:  # 显示前10个
        market_hash_name = item.get('market_hash_name', 'Unknown')
        arbitrage_ratio = item.get('arbitrage_ratio', 0)
        
        logger.info(f"📈 {market_hash_name}: 搬砖率 = {arbitrage_ratio:.6f}")


def analyze_price_data():
    """分析价格数据"""
    logger.info("🔍 分析价格数据...")

    platform_price_dao = PlatformPriceDAO()

    # 获取所有平台
    platforms = platform_price_dao.get_all_platforms()

    logger.info(f"📊 价格数据统计:")
    logger.info(f"  - 发现平台数: {len(platforms)}")

    # 按平台统计
    for platform in platforms:
        try:
            stats = platform_price_dao.get_platform_statistics(platform, days=7)
            logger.info(f"  - {platform}: {stats['total_items']} 个饰品")
        except Exception as e:
            logger.warning(f"  - {platform}: 统计失败 - {e}")


def main():
    """主函数"""
    logger.info("🚀 开始搬砖率计算测试")
    
    try:
        # 1. 分析价格数据
        analyze_price_data()
        
        print("-" * 50)
        
        # 2. 检查现有搬砖率数据
        check_arbitrage_data()
        
        print("-" * 50)
        
        # 3. 测试搬砖率计算
        test_arbitrage_calculation()
        
        print("-" * 50)
        
        # 4. 再次检查搬砖率数据
        logger.info("🔄 重新检查搬砖率数据...")
        check_arbitrage_data()
        
        logger.info("✅ 搬砖率计算测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生异常: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")


if __name__ == "__main__":
    main()
