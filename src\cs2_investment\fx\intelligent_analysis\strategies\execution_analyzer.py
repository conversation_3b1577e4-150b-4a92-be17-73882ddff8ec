"""
执行分析器

基于时K数据进行短期执行分析，提供即时操作建议、
最佳入场价格、止损止盈位等执行层面的精确分析。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from loguru import logger

from ...syncps.technical_indicator_calculator import TechnicalIndicatorCalculator


class ExecutionAnalyzer:
    """执行分析器 - 基于时K数据"""
    
    def __init__(self, hourly_data: pd.DataFrame, daily_data: pd.DataFrame = None):
        """
        初始化执行分析器
        
        Args:
            hourly_data: 时K数据
            daily_data: 日K数据（用于趋势确认）
        """
        self.hourly_data = hourly_data.copy() if hourly_data is not None else pd.DataFrame()
        self.daily_data = daily_data.copy() if daily_data is not None else pd.DataFrame()
        self.logger = logger.bind(analyzer=self.__class__.__name__)
        
        # 初始化技术指标计算器
        if not self.hourly_data.empty:
            self.indicator_calculator = TechnicalIndicatorCalculator(
                daily_data=self.hourly_data,  # 使用时K数据作为日K数据输入
                weekly_data=self.daily_data   # 使用日K数据作为周K数据输入
            )
        else:
            self.indicator_calculator = None
        
        # 分析结果缓存
        self.analysis_cache = {}
    
    def analyze_immediate_market_status(self) -> Dict[str, Any]:
        """
        分析即时市场状态 - 重构版本

        Returns:
            Dict包含当前小时级别的市场状态描述
        """
        try:
            if self.hourly_data.empty:
                return {
                    'market_status': '数据不足',
                    'status_description': '缺少时K数据，无法分析即时市场状态',
                    'status_reasoning': '执行分析需要充足的小时级别历史数据',
                    'error': '缺少时K数据'
                }

            self.logger.info("开始即时市场状态分析...")

            # 获取最新价格和成交量
            current_price = self.hourly_data['close'].iloc[-1]
            current_volume = self.hourly_data['volume'].iloc[-1]

            # 短期移动平均
            prices = self.hourly_data['close']
            ema_5 = prices.ewm(span=5).mean()
            ema_10 = prices.ewm(span=10).mean()

            # 成交量分析
            volume_sma = self.hourly_data['volume'].rolling(window=10).mean()
            volume_ratio = current_volume / volume_sma.iloc[-1] if not volume_sma.empty else 1

            # 价格动量分析
            price_momentum = self._calculate_price_momentum()

            # 生成市场状态分析
            market_analysis = self._generate_market_status_analysis(
                current_price, ema_5, ema_10, volume_ratio, price_momentum
            )

            result = {
                'market_status': market_analysis['status'],
                'status_description': market_analysis['description'],
                'status_reasoning': market_analysis['reasoning'],
                'current_price': current_price,
                'volume_ratio': volume_ratio,
                'price_momentum': price_momentum,
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"即时市场状态分析完成: {market_analysis['status']}")
            return result

        except Exception as e:
            self.logger.error(f"即时市场状态分析失败: {e}")
            return {
                'market_status': '分析异常',
                'status_description': f'即时市场状态分析过程中发生异常: {str(e)}',
                'status_reasoning': '请检查数据质量或联系技术支持',
                'error': str(e)
            }

    def analyze_short_term_risks(self) -> Dict[str, Any]:
        """
        分析短期风险 - 新增方法

        Returns:
            Dict包含需要注意的短期风险点
        """
        try:
            if self.hourly_data.empty:
                return {
                    'risk_level': '无法评估',
                    'risk_description': '缺少时K数据，无法分析短期风险',
                    'risk_factors': '短期风险分析需要充足的小时级别数据',
                    'error': '缺少时K数据'
                }

            self.logger.info("开始短期风险分析...")

            # 分析各类短期风险
            volatility_risk = self._analyze_short_term_volatility_risk()
            momentum_risk = self._analyze_momentum_risk()
            volume_risk = self._analyze_volume_risk()
            technical_risk = self._analyze_technical_risk()

            # 生成综合风险评估
            risk_assessment = self._generate_short_term_risk_assessment(
                volatility_risk, momentum_risk, volume_risk, technical_risk
            )

            result = {
                'risk_level': risk_assessment['level'],
                'risk_description': risk_assessment['description'],
                'risk_factors': risk_assessment['factors'],
                'risk_mitigation': risk_assessment['mitigation'],
                'volatility_risk': volatility_risk,
                'momentum_risk': momentum_risk,
                'volume_risk': volume_risk,
                'technical_risk': technical_risk,
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"短期风险分析完成: {risk_assessment['level']}")
            return result

        except Exception as e:
            self.logger.error(f"短期风险分析失败: {e}")
            return {
                'risk_level': '分析异常',
                'risk_description': f'短期风险分析过程中发生异常: {str(e)}',
                'risk_factors': '请检查数据质量或联系技术支持',
                'error': str(e)
            }

    def analyze_execution_timing(self) -> Dict[str, Any]:
        """
        分析执行时机 - 新增方法

        Returns:
            Dict包含具体的买入卖出时机建议
        """
        try:
            if self.hourly_data.empty:
                return {
                    'timing_assessment': '无法评估',
                    'timing_description': '缺少时K数据，无法分析执行时机',
                    'buy_timing': '无法确定买入时机',
                    'sell_timing': '无法确定卖出时机',
                    'error': '缺少时K数据'
                }

            self.logger.info("开始执行时机分析...")

            # 获取市场状态和风险分析
            market_status = self.analyze_immediate_market_status()
            risk_analysis = self.analyze_short_term_risks()

            # 分析技术指标时机
            technical_timing = self._analyze_technical_timing()

            # 分析价格位置时机
            price_timing = self._analyze_price_position_timing()

            # 分析成交量时机
            volume_timing = self._analyze_volume_timing()

            # 生成综合时机分析
            timing_analysis = self._generate_execution_timing_analysis(
                market_status, risk_analysis, technical_timing, price_timing, volume_timing
            )

            result = {
                'timing_assessment': timing_analysis['assessment'],
                'timing_description': timing_analysis['description'],
                'timing_reasoning': timing_analysis['reasoning'],
                'buy_timing': timing_analysis['buy_timing'],
                'sell_timing': timing_analysis['sell_timing'],
                'optimal_action': timing_analysis['optimal_action'],
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"执行时机分析完成: {timing_analysis['assessment']}")
            return result

        except Exception as e:
            self.logger.error(f"执行时机分析失败: {e}")
            return {
                'timing_assessment': '分析异常',
                'timing_description': f'执行时机分析过程中发生异常: {str(e)}',
                'buy_timing': '建议等待系统恢复',
                'sell_timing': '建议等待系统恢复',
                'error': str(e)
            }

    def calculate_stop_loss_take_profit(self) -> Dict[str, Any]:
        """
        计算止损止盈位 - 重构版本

        Returns:
            Dict包含基于技术指标的止损止盈价格建议
        """
        try:
            if self.hourly_data.empty:
                return {
                    'stop_loss_description': '数据不足，无法计算止损位',
                    'take_profit_description': '数据不足，无法计算止盈位',
                    'stop_loss_price': None,
                    'take_profit_price': None,
                    'error': '缺少时K数据'
                }

            self.logger.info("开始计算止损止盈位...")

            current_price = self.hourly_data['close'].iloc[-1]

            # 基于ATR计算止损止盈位
            atr_levels = self._calculate_atr_based_levels(current_price)

            # 基于支撑阻力位计算止损止盈位
            sr_levels = self._calculate_support_resistance_levels(current_price)

            # 基于技术指标计算止损止盈位
            technical_levels = self._calculate_technical_levels(current_price)

            # 生成综合止损止盈建议
            levels_analysis = self._generate_stop_loss_take_profit_analysis(
                current_price, atr_levels, sr_levels, technical_levels
            )

            result = {
                'stop_loss_description': levels_analysis['stop_loss_description'],
                'take_profit_description': levels_analysis['take_profit_description'],
                'stop_loss_price': levels_analysis['stop_loss_price'],
                'take_profit_price': levels_analysis['take_profit_price'],
                'risk_reward_ratio': levels_analysis['risk_reward_ratio'],
                'position_sizing_advice': levels_analysis['position_sizing_advice'],
                'current_price': current_price,
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"止损止盈位计算完成: 止损{levels_analysis['stop_loss_price']}, 止盈{levels_analysis['take_profit_price']}")
            return result

        except Exception as e:
            self.logger.error(f"止损止盈位计算失败: {e}")
            return {
                'stop_loss_description': f'止损位计算异常: {str(e)}',
                'take_profit_description': f'止盈位计算异常: {str(e)}',
                'stop_loss_price': None,
                'take_profit_price': None,
                'error': str(e)
            }

    def detect_urgent_alerts(self) -> Dict[str, Any]:
        """
        检测紧急提醒 - 新增方法

        Returns:
            Dict包含需要立即关注的异常情况
        """
        try:
            if self.hourly_data.empty:
                return {
                    'alert_level': '无法检测',
                    'alert_description': '缺少时K数据，无法检测异常情况',
                    'urgent_alerts': [],
                    'error': '缺少时K数据'
                }

            self.logger.info("开始检测紧急提醒...")

            urgent_alerts = []

            # 检测价格异常波动
            price_alerts = self._detect_price_anomalies()
            urgent_alerts.extend(price_alerts)

            # 检测成交量异常
            volume_alerts = self._detect_volume_anomalies()
            urgent_alerts.extend(volume_alerts)

            # 检测技术指标异常
            technical_alerts = self._detect_technical_anomalies()
            urgent_alerts.extend(technical_alerts)

            # 检测风险预警
            risk_alerts = self._detect_risk_warnings()
            urgent_alerts.extend(risk_alerts)

            # 生成综合预警评估
            alert_assessment = self._generate_urgent_alert_assessment(urgent_alerts)

            result = {
                'alert_level': alert_assessment['level'],
                'alert_description': alert_assessment['description'],
                'urgent_alerts': urgent_alerts,
                'alert_count': len(urgent_alerts),
                'recommended_action': alert_assessment['recommended_action'],
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"紧急提醒检测完成: {alert_assessment['level']}, {len(urgent_alerts)}个提醒")
            return result

        except Exception as e:
            self.logger.error(f"紧急提醒检测失败: {e}")
            return {
                'alert_level': '检测异常',
                'alert_description': f'紧急提醒检测过程中发生异常: {str(e)}',
                'urgent_alerts': [],
                'error': str(e)
            }

    def calculate_optimal_entry_price(self) -> Dict[str, Any]:
        """计算最佳入场价格"""
        try:
            if self.hourly_data.empty:
                return {
                    'optimal_entry_price': None,
                    'entry_range': {'min': None, 'max': None},
                    'confidence': 0,
                    'error': '缺少时K数据'
                }
            
            self.logger.info("开始计算最佳入场价格...")
            
            current_price = self.hourly_data['close'].iloc[-1]
            
            # 基于支撑阻力位计算入场价格
            support_resistance = self._identify_hourly_support_resistance()
            
            # 基于技术指标计算入场价格
            technical_entry = self._calculate_technical_entry_price()
            
            # 基于成交量分析计算入场价格
            volume_entry = self._calculate_volume_based_entry()
            
            # 综合计算最佳入场价格
            entry_prices = []
            confidences = []
            
            if support_resistance['support']:
                entry_prices.append(support_resistance['support'] * 1.002)  # 支撑位上方0.2%
                confidences.append(70)
            
            if technical_entry['price']:
                entry_prices.append(technical_entry['price'])
                confidences.append(technical_entry['confidence'])
            
            if volume_entry['price']:
                entry_prices.append(volume_entry['price'])
                confidences.append(volume_entry['confidence'])
            
            if entry_prices:
                # 加权平均计算最佳入场价格
                weights = np.array(confidences)
                optimal_entry_price = np.average(entry_prices, weights=weights)
                avg_confidence = np.mean(confidences)
                
                # 计算入场价格区间
                price_std = np.std(entry_prices)
                entry_range = {
                    'min': optimal_entry_price - price_std,
                    'max': optimal_entry_price + price_std
                }
            else:
                optimal_entry_price = current_price
                avg_confidence = 30
                entry_range = {
                    'min': current_price * 0.995,
                    'max': current_price * 1.005
                }
            
            result = {
                'optimal_entry_price': optimal_entry_price,
                'entry_range': entry_range,
                'confidence': avg_confidence,
                'current_price': current_price,
                'price_components': {
                    'support_resistance': support_resistance,
                    'technical_entry': technical_entry,
                    'volume_entry': volume_entry
                },
                'analysis_timestamp': datetime.now()
            }
            
            self.logger.info(f"最佳入场价格计算完成: {optimal_entry_price:.2f}")
            return result
            
        except Exception as e:
            self.logger.error(f"最佳入场价格计算失败: {e}")
            return {
                'optimal_entry_price': None,
                'entry_range': {'min': None, 'max': None},
                'confidence': 0,
                'error': str(e)
            }
    
    def calculate_stop_loss_take_profit(self) -> Dict[str, Any]:
        """计算止损止盈位"""
        try:
            if self.hourly_data.empty:
                return {
                    'stop_loss_price': None,
                    'take_profit_price_1': None,
                    'take_profit_price_2': None,
                    'risk_reward_ratio': None,
                    'error': '缺少时K数据'
                }
            
            self.logger.info("开始计算止损止盈位...")
            
            current_price = self.hourly_data['close'].iloc[-1]
            
            # 基于ATR计算止损止盈
            atr_levels = self._calculate_atr_based_levels()
            
            # 基于支撑阻力位计算止损止盈
            sr_levels = self._calculate_support_resistance_levels()
            
            # 基于波动率计算止损止盈
            volatility_levels = self._calculate_volatility_based_levels()
            
            # 综合计算止损位
            stop_loss_candidates = []
            if atr_levels['stop_loss']:
                stop_loss_candidates.append(atr_levels['stop_loss'])
            if sr_levels['stop_loss']:
                stop_loss_candidates.append(sr_levels['stop_loss'])
            if volatility_levels['stop_loss']:
                stop_loss_candidates.append(volatility_levels['stop_loss'])
            
            stop_loss_price = np.mean(stop_loss_candidates) if stop_loss_candidates else current_price * 0.95
            
            # 综合计算止盈位
            tp1_candidates = []
            tp2_candidates = []
            
            if atr_levels['take_profit_1']:
                tp1_candidates.append(atr_levels['take_profit_1'])
            if sr_levels['take_profit_1']:
                tp1_candidates.append(sr_levels['take_profit_1'])
            if volatility_levels['take_profit_1']:
                tp1_candidates.append(volatility_levels['take_profit_1'])
            
            if atr_levels['take_profit_2']:
                tp2_candidates.append(atr_levels['take_profit_2'])
            if sr_levels['take_profit_2']:
                tp2_candidates.append(sr_levels['take_profit_2'])
            if volatility_levels['take_profit_2']:
                tp2_candidates.append(volatility_levels['take_profit_2'])
            
            take_profit_price_1 = np.mean(tp1_candidates) if tp1_candidates else current_price * 1.02
            take_profit_price_2 = np.mean(tp2_candidates) if tp2_candidates else current_price * 1.05
            
            # 计算风险收益比
            risk = abs(current_price - stop_loss_price)
            reward1 = abs(take_profit_price_1 - current_price)
            reward2 = abs(take_profit_price_2 - current_price)
            
            risk_reward_ratio = {
                'risk': risk,
                'reward_1': reward1,
                'reward_2': reward2,
                'ratio_1': reward1 / risk if risk > 0 else 0,
                'ratio_2': reward2 / risk if risk > 0 else 0
            }
            
            result = {
                'stop_loss_price': stop_loss_price,
                'take_profit_price_1': take_profit_price_1,
                'take_profit_price_2': take_profit_price_2,
                'risk_reward_ratio': risk_reward_ratio,
                'current_price': current_price,
                'calculation_components': {
                    'atr_levels': atr_levels,
                    'sr_levels': sr_levels,
                    'volatility_levels': volatility_levels
                },
                'analysis_timestamp': datetime.now()
            }
            
            self.logger.info("止损止盈位计算完成")
            return result
            
        except Exception as e:
            self.logger.error(f"止损止盈位计算失败: {e}")
            return {
                'stop_loss_price': None,
                'take_profit_price_1': None,
                'take_profit_price_2': None,
                'risk_reward_ratio': None,
                'error': str(e)
            }

    def assess_execution_risk(self) -> Dict[str, Any]:
        """评估执行风险"""
        try:
            if self.hourly_data.empty:
                return {
                    'execution_risk_level': 'UNKNOWN',
                    'risk_factors': {},
                    'error': '缺少时K数据'
                }

            self.logger.info("开始执行风险评估...")

            risk_factors = {}
            risk_scores = []

            # 1. 短期波动风险
            prices = self.hourly_data['close']
            short_volatility = prices.pct_change().tail(6).std()  # 最近6小时波动率

            if short_volatility > 0.03:
                vol_risk = 'HIGH'
                vol_score = 80
            elif short_volatility > 0.02:
                vol_risk = 'MEDIUM'
                vol_score = 50
            else:
                vol_risk = 'LOW'
                vol_score = 20

            risk_factors['short_volatility'] = {
                'level': vol_risk,
                'score': vol_score,
                'value': short_volatility
            }
            risk_scores.append(vol_score)

            # 2. 流动性风险
            volumes = self.hourly_data['volume']
            avg_volume = volumes.tail(24).mean()  # 24小时平均成交量
            current_volume = volumes.iloc[-1]

            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1

            if volume_ratio < 0.3:
                liquidity_risk = 'HIGH'
                liquidity_score = 80
            elif volume_ratio < 0.6:
                liquidity_risk = 'MEDIUM'
                liquidity_score = 50
            else:
                liquidity_risk = 'LOW'
                liquidity_score = 20

            risk_factors['liquidity'] = {
                'level': liquidity_risk,
                'score': liquidity_score,
                'volume_ratio': volume_ratio
            }
            risk_scores.append(liquidity_score)

            # 3. 价格跳空风险
            gap_risk_score = self._calculate_gap_risk()
            if gap_risk_score > 70:
                gap_risk = 'HIGH'
            elif gap_risk_score > 40:
                gap_risk = 'MEDIUM'
            else:
                gap_risk = 'LOW'

            risk_factors['price_gap'] = {
                'level': gap_risk,
                'score': gap_risk_score
            }
            risk_scores.append(gap_risk_score)

            # 4. 市场时间风险
            time_risk_score = self._calculate_time_based_risk()
            if time_risk_score > 70:
                time_risk = 'HIGH'
            elif time_risk_score > 40:
                time_risk = 'MEDIUM'
            else:
                time_risk = 'LOW'

            risk_factors['market_timing'] = {
                'level': time_risk,
                'score': time_risk_score
            }
            risk_scores.append(time_risk_score)

            # 综合风险评估
            avg_risk_score = np.mean(risk_scores) if risk_scores else 50

            if avg_risk_score > 70:
                execution_risk_level = 'HIGH'
            elif avg_risk_score > 40:
                execution_risk_level = 'MEDIUM'
            else:
                execution_risk_level = 'LOW'

            result = {
                'execution_risk_level': execution_risk_level,
                'risk_score': avg_risk_score,
                'risk_factors': risk_factors,
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"执行风险评估完成: {execution_risk_level}")
            return result

        except Exception as e:
            self.logger.error(f"执行风险评估失败: {e}")
            return {
                'execution_risk_level': 'ERROR',
                'risk_factors': {},
                'error': str(e)
            }

    def analyze_immediate_action(self) -> Dict[str, Any]:
        """分析立即行动建议"""
        try:
            if self.hourly_data.empty:
                return {
                    'immediate_action': 'WAIT',
                    'action_description': '缺少时K数据，建议等待',
                    'urgency_level': 'LOW',
                    'reasoning': '数据不足，无法做出准确判断',
                    'error': '缺少数据'
                }

            self.logger.info("开始分析立即行动建议...")

            # 获取当前市场状态
            current_price = self.hourly_data['close'].iloc[-1]
            recent_prices = self.hourly_data['close'].iloc[-6:]  # 最近6小时

            # 分析价格动量
            price_change_1h = (current_price - self.hourly_data['close'].iloc[-2]) / self.hourly_data['close'].iloc[-2]
            price_change_6h = (current_price - self.hourly_data['close'].iloc[-7]) / self.hourly_data['close'].iloc[-7] if len(self.hourly_data) >= 7 else 0

            # 分析成交量
            current_volume = self.hourly_data['volume'].iloc[-1] if 'volume' in self.hourly_data.columns else 0
            avg_volume = self.hourly_data['volume'].iloc[-24:].mean() if 'volume' in self.hourly_data.columns and len(self.hourly_data) >= 24 else current_volume

            # 决策逻辑
            action = 'WAIT'
            urgency = 'LOW'
            reasoning = '市场状态正常，建议观望'

            # 强烈上涨信号
            if price_change_1h > 0.02 and price_change_6h > 0.05 and current_volume > avg_volume * 1.5:
                action = 'BUY'
                urgency = 'HIGH'
                reasoning = '价格强势上涨且成交量放大，建议立即买入'
            # 强烈下跌信号
            elif price_change_1h < -0.02 and price_change_6h < -0.05 and current_volume > avg_volume * 1.5:
                action = 'SELL'
                urgency = 'HIGH'
                reasoning = '价格快速下跌且成交量放大，建议立即卖出'
            # 温和上涨
            elif price_change_1h > 0.01 and price_change_6h > 0.02:
                action = 'BUY'
                urgency = 'MODERATE'
                reasoning = '价格稳步上涨，可考虑买入'
            # 温和下跌
            elif price_change_1h < -0.01 and price_change_6h < -0.02:
                action = 'SELL'
                urgency = 'MODERATE'
                reasoning = '价格持续下跌，可考虑卖出'

            return {
                'immediate_action': action,
                'action_description': reasoning,
                'urgency_level': urgency,
                'price_change_1h': f"{price_change_1h:.2%}",
                'price_change_6h': f"{price_change_6h:.2%}",
                'volume_ratio': current_volume / avg_volume if avg_volume > 0 else 1,
                'reasoning': reasoning,
                'analysis_timestamp': datetime.now()
            }

        except Exception as e:
            self.logger.error(f"分析立即行动建议失败: {e}")
            return {
                'immediate_action': 'ERROR',
                'action_description': f'立即行动分析失败: {str(e)}',
                'urgency_level': 'UNKNOWN',
                'reasoning': '分析过程中发生错误',
                'error': str(e)
            }

    def generate_execution_analysis_report(self) -> Dict[str, Any]:
        """生成执行分析报告"""
        try:
            self.logger.info("生成执行分析报告...")

            # 执行各项分析
            immediate_action = self.analyze_immediate_action()
            optimal_entry = self.calculate_optimal_entry_price()
            stop_loss_take_profit = self.calculate_stop_loss_take_profit()
            risk_assessment = self.assess_execution_risk()

            # 综合报告
            report = {
                'analysis_type': 'EXECUTION',
                'timeframe': 'HOURLY',
                'analysis_timestamp': datetime.now(),
                'data_quality': 'GOOD' if not self.hourly_data.empty else 'NO_DATA',
                'data_points': len(self.hourly_data),

                # 各项分析结果
                'immediate_action': immediate_action,
                'optimal_entry': optimal_entry,
                'stop_loss_take_profit': stop_loss_take_profit,
                'risk_assessment': risk_assessment,

                # 执行建议
                'execution_plan': self._generate_execution_plan(
                    immediate_action, optimal_entry, stop_loss_take_profit, risk_assessment
                )
            }

            self.logger.info("执行分析报告生成完成")
            return report

        except Exception as e:
            self.logger.error(f"执行分析报告生成失败: {e}")
            return {
                'analysis_type': 'EXECUTION',
                'error': str(e),
                'analysis_timestamp': datetime.now()
            }

    # 辅助方法
    def _calculate_price_momentum(self) -> float:
        """计算价格动量"""
        try:
            if len(self.hourly_data) < 3:
                return 0.0

            prices = self.hourly_data['close']
            current_price = prices.iloc[-1]
            prev_price = prices.iloc[-3]  # 3小时前价格

            momentum = (current_price - prev_price) / prev_price
            return momentum

        except Exception:
            return 0

    def execute_comprehensive_execution_analysis(self) -> Dict[str, Any]:
        """
        执行完整的执行分析 - 主要接口方法

        Returns:
            Dict包含符合需求文档要求的完整执行分析结果
        """
        try:
            self.logger.info("开始执行完整的执行分析...")

            # 执行各项分析
            market_status = self.analyze_immediate_market_status()
            risk_analysis = self.analyze_short_term_risks()
            timing_analysis = self.analyze_execution_timing()
            levels_analysis = self.calculate_stop_loss_take_profit()
            alerts_analysis = self.detect_urgent_alerts()

            # 整合分析结果，符合需求文档格式
            comprehensive_result = {
                # 即时市场状态：当前小时级别的市场状态描述
                'immediate_market_status': market_status.get('market_status', '数据不足'),
                'market_status_description': market_status.get('status_description', ''),
                'market_status_reasoning': market_status.get('status_reasoning', ''),

                # 短期风险提醒：需要注意的短期风险点
                'short_term_risk_level': risk_analysis.get('risk_level', '无法评估'),
                'risk_description': risk_analysis.get('risk_description', ''),
                'risk_factors': risk_analysis.get('risk_factors', ''),
                'risk_mitigation': risk_analysis.get('risk_mitigation', ''),

                # 执行时机：具体的买入卖出时机建议
                'execution_timing_assessment': timing_analysis.get('timing_assessment', ''),
                'timing_description': timing_analysis.get('timing_description', ''),
                'timing_reasoning': timing_analysis.get('timing_reasoning', ''),
                'buy_timing_advice': timing_analysis.get('buy_timing', ''),
                'sell_timing_advice': timing_analysis.get('sell_timing', ''),
                'optimal_action': timing_analysis.get('optimal_action', ''),

                # 止损止盈位：具体的止损止盈价格建议
                'stop_loss_description': levels_analysis.get('stop_loss_description', ''),
                'take_profit_description': levels_analysis.get('take_profit_description', ''),
                'stop_loss_price': levels_analysis.get('stop_loss_price'),
                'take_profit_price': levels_analysis.get('take_profit_price'),
                'risk_reward_ratio': levels_analysis.get('risk_reward_ratio'),
                'position_sizing_advice': levels_analysis.get('position_sizing_advice', ''),

                # 紧急提醒：需要立即关注的异常情况
                'urgent_alert_level': alerts_analysis.get('alert_level', '无异常'),
                'alert_description': alerts_analysis.get('alert_description', ''),
                'urgent_alerts': alerts_analysis.get('urgent_alerts', []),
                'alert_count': alerts_analysis.get('alert_count', 0),
                'recommended_action': alerts_analysis.get('recommended_action', ''),

                # 元数据
                'current_price': market_status.get('current_price'),
                'analysis_timestamp': datetime.now(),
                'data_quality': 'GOOD' if not self.hourly_data.empty else 'INSUFFICIENT',
                'analysis_success': True
            }

            self.logger.info("完整执行分析执行成功")
            return comprehensive_result

        except Exception as e:
            self.logger.error(f"完整执行分析执行失败: {e}")
            return {
                'immediate_market_status': '分析异常',
                'market_status_description': f'执行分析过程中发生异常: {str(e)}',
                'market_status_reasoning': '请检查数据质量或联系技术支持',
                'short_term_risk_level': '高',
                'risk_description': '由于分析异常，建议谨慎操作',
                'risk_factors': '分析系统异常，存在未知风险',
                'risk_mitigation': '建议暂停交易，等待系统恢复',
                'execution_timing_assessment': '无法评估',
                'timing_description': '执行时机分析异常',
                'timing_reasoning': '请检查数据质量',
                'buy_timing_advice': '建议等待系统恢复后重新评估',
                'sell_timing_advice': '建议等待系统恢复后重新评估',
                'stop_loss_description': '无法计算止损位',
                'take_profit_description': '无法计算止盈位',
                'urgent_alert_level': '系统异常',
                'alert_description': '执行分析系统发生异常，请立即检查',
                'urgent_alerts': [{'type': 'SYSTEM_ERROR', 'message': str(e)}],
                'recommended_action': '停止交易，联系技术支持',
                'analysis_timestamp': datetime.now(),
                'data_quality': 'ERROR',
                'analysis_success': False,
                'error': str(e)
            }

    def _generate_market_status_analysis(self, current_price: float, ema_5: pd.Series,
                                       ema_10: pd.Series, volume_ratio: float,
                                       price_momentum: float) -> Dict[str, str]:
        """生成市场状态分析"""
        try:
            # 分析价格趋势
            if len(ema_5) >= 1 and len(ema_10) >= 1:
                ema_5_current = ema_5.iloc[-1]
                ema_10_current = ema_10.iloc[-1]

                if current_price > ema_5_current > ema_10_current:
                    if price_momentum > 0.02:
                        status = '强势上涨'
                        description = f'当前价格{current_price:.2f}元显著高于5小时均线({ema_5_current:.2f}元)和10小时均线({ema_10_current:.2f}元)，短期动能强劲。'
                        reasoning = '短期均线呈现多头排列，价格动能积极，市场处于强势上涨状态。'
                    else:
                        status = '温和上涨'
                        description = f'当前价格{current_price:.2f}元位于5小时均线({ema_5_current:.2f}元)和10小时均线({ema_10_current:.2f}元)之上，呈现稳健上涨。'
                        reasoning = '短期均线支撑价格上涨，虽然动能温和，但趋势稳定。'

                elif current_price < ema_5_current < ema_10_current:
                    if price_momentum < -0.02:
                        status = '强势下跌'
                        description = f'当前价格{current_price:.2f}元大幅低于5小时均线({ema_5_current:.2f}元)和10小时均线({ema_10_current:.2f}元)，短期下跌压力沉重。'
                        reasoning = '短期均线呈现空头排列，价格动能消极，市场处于强势下跌状态。'
                    else:
                        status = '温和下跌'
                        description = f'当前价格{current_price:.2f}元位于5小时均线({ema_5_current:.2f}元)和10小时均线({ema_10_current:.2f}元)下方，呈现下跌态势。'
                        reasoning = '短期均线对价格形成压制，下跌趋势明确但动能有限。'

                else:
                    status = '震荡整理'
                    description = f'当前价格{current_price:.2f}元与短期均线交织运行，市场处于震荡整理状态。'
                    reasoning = '短期均线系统混乱，市场方向不明确，处于整理阶段。'
            else:
                status = '数据不足'
                description = '短期均线数据不完整，无法准确判断市场状态。'
                reasoning = '需要更多历史数据来计算可靠的短期均线。'

            # 结合成交量分析
            if volume_ratio > 2.0:
                description += f' 成交量放大{volume_ratio:.1f}倍，市场活跃度极高。'
            elif volume_ratio > 1.5:
                description += f' 成交量放大{volume_ratio:.1f}倍，市场活跃度较高。'
            elif volume_ratio < 0.5:
                description += f' 成交量萎缩至{volume_ratio:.1f}倍，市场活跃度低迷。'

            return {
                'status': status,
                'description': description,
                'reasoning': reasoning
            }

        except Exception:
            return {
                'status': '分析异常',
                'description': '市场状态分析过程中发生异常',
                'reasoning': '请检查数据质量或联系技术支持'
            }

    def _analyze_short_term_volatility_risk(self) -> str:
        """分析短期波动率风险"""
        try:
            if len(self.hourly_data) < 24:
                return '数据不足，无法分析短期波动率风险'

            # 计算24小时波动率
            returns = self.hourly_data['close'].pct_change().dropna()
            volatility = returns.std() * np.sqrt(24)  # 日化波动率

            if volatility > 0.15:
                return f'短期波动率极高({volatility:.1%})，价格变动剧烈，交易风险很大'
            elif volatility > 0.10:
                return f'短期波动率较高({volatility:.1%})，价格波动明显，需要谨慎操作'
            elif volatility > 0.05:
                return f'短期波动率中等({volatility:.1%})，价格波动在正常范围内'
            else:
                return f'短期波动率较低({volatility:.1%})，价格相对稳定'

        except Exception:
            return '短期波动率风险分析异常'

    def _analyze_momentum_risk(self) -> str:
        """分析动量风险"""
        try:
            price_momentum = self._calculate_price_momentum()

            if abs(price_momentum) > 0.05:
                if price_momentum > 0:
                    return f'价格动量过强({price_momentum:.1%})，存在回调风险，需要警惕追高'
                else:
                    return f'价格动量过弱({price_momentum:.1%})，存在进一步下跌风险'
            elif abs(price_momentum) > 0.02:
                if price_momentum > 0:
                    return f'价格动量适中({price_momentum:.1%})，上涨趋势健康'
                else:
                    return f'价格动量偏弱({price_momentum:.1%})，需要关注支撑位'
            else:
                return f'价格动量平缓({price_momentum:.1%})，市场相对平衡'

        except Exception:
            return '动量风险分析异常'

    def _analyze_volume_risk(self) -> str:
        """分析成交量风险"""
        try:
            if len(self.hourly_data) < 10:
                return '数据不足，无法分析成交量风险'

            current_volume = self.hourly_data['volume'].iloc[-1]
            avg_volume = self.hourly_data['volume'].rolling(window=10).mean().iloc[-1]
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1

            if volume_ratio < 0.3:
                return f'成交量极度萎缩({volume_ratio:.1f}倍)，流动性风险很高，交易执行困难'
            elif volume_ratio < 0.5:
                return f'成交量明显萎缩({volume_ratio:.1f}倍)，流动性风险较高，可能影响交易'
            elif volume_ratio > 3.0:
                return f'成交量异常放大({volume_ratio:.1f}倍)，可能存在异常波动风险'
            elif volume_ratio > 2.0:
                return f'成交量大幅放大({volume_ratio:.1f}倍)，市场情绪激烈，需要谨慎'
            else:
                return f'成交量正常({volume_ratio:.1f}倍)，流动性风险较低'

        except Exception:
            return '成交量风险分析异常'

    def _analyze_technical_risk(self) -> str:
        """分析技术指标风险"""
        try:
            if not self.indicator_calculator:
                return '技术指标计算器未初始化，无法分析技术风险'

            # 简化的技术风险分析
            prices = self.hourly_data['close']
            if len(prices) < 14:
                return '历史数据不足，无法计算技术指标风险'

            # 基于价格相对位置的风险评估
            recent_high = prices.rolling(window=24).max().iloc[-1] if len(prices) >= 24 else prices.max()
            recent_low = prices.rolling(window=24).min().iloc[-1] if len(prices) >= 24 else prices.min()
            current_price = prices.iloc[-1]

            if recent_high > recent_low:
                position = (current_price - recent_low) / (recent_high - recent_low)

                if position > 0.9:
                    return f'价格位于24小时高位区间({position:.1%})，存在回调风险'
                elif position < 0.1:
                    return f'价格位于24小时低位区间({position:.1%})，存在反弹机会但需谨慎'
                else:
                    return f'价格位于24小时中位区间({position:.1%})，技术风险适中'
            else:
                return '价格区间分析异常，技术风险无法确定'

        except Exception:
            return '技术风险分析异常'

    def _generate_short_term_risk_assessment(self, volatility_risk: str, momentum_risk: str,
                                           volume_risk: str, technical_risk: str) -> Dict[str, str]:
        """生成短期风险综合评估"""
        try:
            # 统计各类风险等级
            high_risks = []
            medium_risks = []
            low_risks = []

            # 分析波动率风险
            if '极高' in volatility_risk or '很大' in volatility_risk:
                high_risks.append('价格波动风险')
            elif '较高' in volatility_risk or '明显' in volatility_risk:
                medium_risks.append('价格波动风险')
            else:
                low_risks.append('价格波动风险')

            # 分析动量风险
            if '过强' in momentum_risk or '过弱' in momentum_risk:
                high_risks.append('价格动量风险')
            elif '偏弱' in momentum_risk or '需要关注' in momentum_risk:
                medium_risks.append('价格动量风险')
            else:
                low_risks.append('价格动量风险')

            # 分析成交量风险
            if '很高' in volume_risk or '极度' in volume_risk or '异常' in volume_risk:
                high_risks.append('流动性风险')
            elif '较高' in volume_risk or '大幅' in volume_risk:
                medium_risks.append('流动性风险')
            else:
                low_risks.append('流动性风险')

            # 分析技术风险
            if '回调风险' in technical_risk or '需谨慎' in technical_risk:
                medium_risks.append('技术位置风险')
            else:
                low_risks.append('技术位置风险')

            # 生成综合风险等级
            if len(high_risks) >= 2:
                level = '高'
                description = f'存在{len(high_risks)}项高风险因素：{", ".join(high_risks)}，建议暂停交易。'
                factors = f'风险详情：{volatility_risk}；{momentum_risk}；{volume_risk}；{technical_risk}。'
                mitigation = '建议立即停止新增仓位，考虑减仓或设置紧密止损，密切监控市场变化。'
            elif len(high_risks) >= 1 or len(medium_risks) >= 3:
                level = '中'
                description = f'存在一定短期风险因素，需要谨慎操作。'
                factors = f'风险详情：{volatility_risk}；{momentum_risk}；{volume_risk}；{technical_risk}。'
                mitigation = '建议控制仓位规模，设置合理止损，根据风险承受能力调整操作策略。'
            else:
                level = '低'
                description = f'短期风险相对可控，但仍需要基本的风险管理。'
                factors = f'风险状况：{volatility_risk}；{momentum_risk}；{volume_risk}；{technical_risk}。'
                mitigation = '建议保持正常操作节奏，设置基础止损保护，定期评估市场变化。'

            return {
                'level': level,
                'description': description,
                'factors': factors,
                'mitigation': mitigation
            }

        except Exception:
            return {
                'level': '评估异常',
                'description': '短期风险评估过程中发生异常',
                'factors': '无法准确分析各项风险因素',
                'mitigation': '建议谨慎操作，咨询专业人士'
            }

    def _calculate_atr_based_levels(self, current_price: float) -> Dict[str, Optional[float]]:
        """基于ATR计算止损止盈位"""
        try:
            if not self.indicator_calculator or len(self.hourly_data) < 14:
                return {'stop_loss': None, 'take_profit': None, 'atr_value': None}

            # 计算ATR
            indicators = self.indicator_calculator.calculate_all_indicators()

            if 'atr' in indicators and not indicators['atr'].empty:
                atr = indicators['atr'].iloc[-1]

                # 基于ATR的止损止盈位计算
                stop_loss = current_price - (atr * 2)  # 2倍ATR作为止损
                take_profit = current_price + (atr * 3)  # 3倍ATR作为止盈

                return {
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'atr_value': atr
                }
            else:
                return {'stop_loss': None, 'take_profit': None, 'atr_value': None}

        except Exception:
            return {'stop_loss': None, 'take_profit': None, 'atr_value': None}

    def _calculate_support_resistance_levels(self, current_price: float) -> Dict[str, Optional[float]]:
        """基于支撑阻力位计算止损止盈位"""
        try:
            if len(self.hourly_data) < 20:
                return {'support_stop_loss': None, 'resistance_take_profit': None}

            # 寻找近期支撑阻力位
            highs = self.hourly_data['high'].iloc[-48:]  # 最近48小时
            lows = self.hourly_data['low'].iloc[-48:]

            # 简化的支撑阻力位识别
            support_level = lows.min()
            resistance_level = highs.max()

            # 基于支撑阻力位的止损止盈
            stop_loss = support_level * 0.998  # 支撑位下方0.2%
            take_profit = resistance_level * 1.002  # 阻力位上方0.2%

            return {
                'support_stop_loss': stop_loss,
                'resistance_take_profit': take_profit
            }

        except Exception:
            return {'support_stop_loss': None, 'resistance_take_profit': None}

    def _calculate_technical_levels(self, current_price: float) -> Dict[str, Optional[float]]:
        """基于技术指标计算止损止盈位"""
        try:
            # 基于移动平均线的止损止盈
            prices = self.hourly_data['close']
            if len(prices) >= 20:
                ema_20 = prices.ewm(span=20).mean().iloc[-1]

                # 基于20EMA的止损止盈
                if current_price > ema_20:
                    stop_loss = ema_20 * 0.995  # EMA下方0.5%
                    take_profit = current_price + (current_price - ema_20) * 2  # 风险回报比2:1
                else:
                    stop_loss = ema_20 * 1.005  # EMA上方0.5%
                    take_profit = current_price - (ema_20 - current_price) * 2  # 风险回报比2:1

                return {
                    'technical_stop_loss': stop_loss,
                    'technical_take_profit': take_profit
                }
            else:
                return {'technical_stop_loss': None, 'technical_take_profit': None}

        except Exception:
            return {'technical_stop_loss': None, 'technical_take_profit': None}

    def _generate_stop_loss_take_profit_analysis(self, current_price: float, atr_levels: Dict,
                                               sr_levels: Dict, technical_levels: Dict) -> Dict[str, Any]:
        """生成止损止盈分析"""
        try:
            # 收集所有止损建议
            stop_loss_options = []
            take_profit_options = []

            if atr_levels.get('stop_loss'):
                stop_loss_options.append(('ATR', atr_levels['stop_loss']))
            if sr_levels.get('support_stop_loss'):
                stop_loss_options.append(('支撑位', sr_levels['support_stop_loss']))
            if technical_levels.get('technical_stop_loss'):
                stop_loss_options.append(('技术位', technical_levels['technical_stop_loss']))

            if atr_levels.get('take_profit'):
                take_profit_options.append(('ATR', atr_levels['take_profit']))
            if sr_levels.get('resistance_take_profit'):
                take_profit_options.append(('阻力位', sr_levels['resistance_take_profit']))
            if technical_levels.get('technical_take_profit'):
                take_profit_options.append(('技术位', technical_levels['technical_take_profit']))

            # 选择最优止损位（最接近当前价格但不过于紧密）
            if stop_loss_options:
                # 选择距离当前价格3-8%的止损位
                valid_stops = [(name, price) for name, price in stop_loss_options
                             if 0.92 <= price/current_price <= 0.97]
                if valid_stops:
                    stop_loss_name, stop_loss_price = max(valid_stops, key=lambda x: x[1])
                else:
                    stop_loss_name, stop_loss_price = max(stop_loss_options, key=lambda x: x[1])

                stop_loss_description = f'建议止损位{stop_loss_price:.2f}元(基于{stop_loss_name}计算)，较当前价格{current_price:.2f}元下跌{((current_price-stop_loss_price)/current_price)*100:.1f}%。'
            else:
                stop_loss_price = current_price * 0.95  # 默认5%止损
                stop_loss_description = f'建议止损位{stop_loss_price:.2f}元(默认5%止损)，较当前价格{current_price:.2f}元下跌5.0%。'

            # 选择最优止盈位
            if take_profit_options:
                # 选择距离当前价格5-15%的止盈位
                valid_profits = [(name, price) for name, price in take_profit_options
                               if 1.05 <= price/current_price <= 1.15]
                if valid_profits:
                    take_profit_name, take_profit_price = min(valid_profits, key=lambda x: x[1])
                else:
                    take_profit_name, take_profit_price = min(take_profit_options, key=lambda x: x[1])

                take_profit_description = f'建议止盈位{take_profit_price:.2f}元(基于{take_profit_name}计算)，较当前价格{current_price:.2f}元上涨{((take_profit_price-current_price)/current_price)*100:.1f}%。'
            else:
                take_profit_price = current_price * 1.10  # 默认10%止盈
                take_profit_description = f'建议止盈位{take_profit_price:.2f}元(默认10%止盈)，较当前价格{current_price:.2f}元上涨10.0%。'

            # 计算风险回报比
            risk = current_price - stop_loss_price
            reward = take_profit_price - current_price
            risk_reward_ratio = reward / risk if risk > 0 else 0

            # 仓位建议
            if risk_reward_ratio >= 2.0:
                position_sizing_advice = f'风险回报比{risk_reward_ratio:.1f}:1较为理想，可以考虑正常仓位操作。'
            elif risk_reward_ratio >= 1.5:
                position_sizing_advice = f'风险回报比{risk_reward_ratio:.1f}:1尚可接受，建议适度控制仓位。'
            else:
                position_sizing_advice = f'风险回报比{risk_reward_ratio:.1f}:1偏低，建议小仓位操作或等待更好机会。'

            return {
                'stop_loss_description': stop_loss_description,
                'take_profit_description': take_profit_description,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'risk_reward_ratio': risk_reward_ratio,
                'position_sizing_advice': position_sizing_advice
            }

        except Exception:
            return {
                'stop_loss_description': '止损位计算异常，建议设置5%止损',
                'take_profit_description': '止盈位计算异常，建议设置10%止盈',
                'stop_loss_price': current_price * 0.95,
                'take_profit_price': current_price * 1.10,
                'risk_reward_ratio': 2.0,
                'position_sizing_advice': '由于计算异常，建议小仓位操作'
            }

    def _detect_price_anomalies(self) -> List[Dict[str, str]]:
        """检测价格异常波动"""
        try:
            alerts = []

            if len(self.hourly_data) < 5:
                return alerts

            # 检测急剧价格变化
            recent_prices = self.hourly_data['close'].iloc[-5:]
            price_changes = recent_prices.pct_change().dropna()

            for i, change in enumerate(price_changes):
                if abs(change) > 0.05:  # 单小时变化超过5%
                    alerts.append({
                        'type': 'PRICE_SPIKE',
                        'severity': 'HIGH',
                        'message': f'价格在过去{i+1}小时内变化{change*100:.1f}%，存在异常波动'
                    })

            # 检测连续大幅变化
            if len(price_changes) >= 3:
                if all(abs(c) > 0.02 for c in price_changes[-3:]):
                    alerts.append({
                        'type': 'CONTINUOUS_VOLATILITY',
                        'severity': 'MEDIUM',
                        'message': '连续3小时出现大幅价格波动，市场不稳定'
                    })

            return alerts

        except Exception:
            return []

    def _detect_volume_anomalies(self) -> List[Dict[str, str]]:
        """检测成交量异常"""
        try:
            alerts = []

            if len(self.hourly_data) < 10:
                return alerts

            current_volume = self.hourly_data['volume'].iloc[-1]
            avg_volume = self.hourly_data['volume'].iloc[-10:].mean()

            if current_volume > avg_volume * 5:
                alerts.append({
                    'type': 'VOLUME_SPIKE',
                    'severity': 'HIGH',
                    'message': f'成交量异常放大{current_volume/avg_volume:.1f}倍，可能有重大消息'
                })
            elif current_volume < avg_volume * 0.2:
                alerts.append({
                    'type': 'VOLUME_DRY',
                    'severity': 'MEDIUM',
                    'message': f'成交量异常萎缩至{current_volume/avg_volume:.1f}倍，流动性极差'
                })

            return alerts

        except Exception:
            return []

    def _analyze_technical_timing(self) -> Dict[str, str]:
        """分析技术指标时机"""
        try:
            # 简化的技术时机分析
            if len(self.hourly_data) < 20:
                return {
                    'timing': '数据不足',
                    'description': '历史数据不足，无法分析技术时机'
                }

            prices = self.hourly_data['close']
            ema_5 = prices.ewm(span=5).mean()
            ema_10 = prices.ewm(span=10).mean()

            current_price = prices.iloc[-1]
            ema_5_current = ema_5.iloc[-1]
            ema_10_current = ema_10.iloc[-1]

            if current_price > ema_5_current > ema_10_current:
                return {
                    'timing': '技术面偏多',
                    'description': '短期均线呈现多头排列，技术面支持买入'
                }
            elif current_price < ema_5_current < ema_10_current:
                return {
                    'timing': '技术面偏空',
                    'description': '短期均线呈现空头排列，技术面支持卖出'
                }
            else:
                return {
                    'timing': '技术面中性',
                    'description': '短期均线交织，技术面信号不明确'
                }

        except Exception:
            return {
                'timing': '分析异常',
                'description': '技术时机分析过程中发生异常'
            }

    def _analyze_price_position_timing(self) -> Dict[str, str]:
        """分析价格位置时机"""
        try:
            if len(self.hourly_data) < 24:
                return {
                    'timing': '数据不足',
                    'description': '历史数据不足，无法分析价格位置'
                }

            prices = self.hourly_data['close']
            recent_high = prices.rolling(window=24).max().iloc[-1]
            recent_low = prices.rolling(window=24).min().iloc[-1]
            current_price = prices.iloc[-1]

            if recent_high > recent_low:
                position = (current_price - recent_low) / (recent_high - recent_low)

                if position > 0.8:
                    return {
                        'timing': '高位区间',
                        'description': f'价格位于24小时高位区间({position:.1%})，追高风险较大'
                    }
                elif position < 0.2:
                    return {
                        'timing': '低位区间',
                        'description': f'价格位于24小时低位区间({position:.1%})，存在反弹机会'
                    }
                else:
                    return {
                        'timing': '中位区间',
                        'description': f'价格位于24小时中位区间({position:.1%})，位置相对合理'
                    }
            else:
                return {
                    'timing': '位置异常',
                    'description': '价格区间分析异常'
                }

        except Exception:
            return {
                'timing': '分析异常',
                'description': '价格位置分析过程中发生异常'
            }

    def _analyze_volume_timing(self) -> Dict[str, str]:
        """分析成交量时机"""
        try:
            if len(self.hourly_data) < 10:
                return {
                    'timing': '数据不足',
                    'description': '历史数据不足，无法分析成交量时机'
                }

            current_volume = self.hourly_data['volume'].iloc[-1]
            avg_volume = self.hourly_data['volume'].rolling(window=10).mean().iloc[-1]
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1

            if volume_ratio > 2.0:
                return {
                    'timing': '成交量活跃',
                    'description': f'成交量放大{volume_ratio:.1f}倍，市场活跃度高，适合操作'
                }
            elif volume_ratio < 0.5:
                return {
                    'timing': '成交量低迷',
                    'description': f'成交量萎缩至{volume_ratio:.1f}倍，市场活跃度低，不宜操作'
                }
            else:
                return {
                    'timing': '成交量正常',
                    'description': f'成交量正常({volume_ratio:.1f}倍)，市场活跃度适中'
                }

        except Exception:
            return {
                'timing': '分析异常',
                'description': '成交量时机分析过程中发生异常'
            }

    def _generate_execution_timing_analysis(self, market_status: Dict, risk_analysis: Dict,
                                          technical_timing: Dict, price_timing: Dict,
                                          volume_timing: Dict) -> Dict[str, str]:
        """生成执行时机综合分析"""
        try:
            market_state = market_status.get('market_status', '中性')
            risk_level = risk_analysis.get('risk_level', '中')

            # 基于各项分析生成时机评估
            if '强势上涨' in market_state and risk_level == '低' and '偏多' in technical_timing.get('timing', ''):
                assessment = '买入时机优秀'
                description = '市场强势上涨，风险可控，技术面支持，是理想的买入时机。'
                reasoning = '多项指标确认上涨趋势，风险较低，适合积极操作。'
                buy_timing = '建议立即买入或在小幅回调时加仓。'
                sell_timing = '暂不建议卖出，持有等待更高价位。'
                optimal_action = '买入'

            elif '下跌' in market_state and '偏空' in technical_timing.get('timing', ''):
                assessment = '卖出时机良好'
                description = '市场下跌趋势明确，技术面支持卖出，建议及时止损。'
                reasoning = '下跌趋势和技术面双重确认，应当及时止损避免更大损失。'
                buy_timing = '暂不建议买入，等待趋势转换。'
                sell_timing = '建议立即卖出或在反弹时减仓。'
                optimal_action = '卖出'

            elif '震荡' in market_state or '中性' in technical_timing.get('timing', ''):
                assessment = '观望等待'
                description = '市场方向不明确，建议观望等待更清晰的信号。'
                reasoning = '缺乏明确的方向性信号，贸然操作风险较大。'
                buy_timing = '等待明确的买入信号。'
                sell_timing = '如有持仓可以继续持有，但要设置止损。'
                optimal_action = '观望'

            else:
                assessment = '谨慎操作'
                description = '当前市场信号混合，建议谨慎操作。'
                reasoning = '信号不够明确，需要更多确认。'
                buy_timing = '建议小仓位试探或等待更好时机。'
                sell_timing = '可以适量减仓，控制风险敞口。'
                optimal_action = '谨慎'

            return {
                'assessment': assessment,
                'description': description,
                'reasoning': reasoning,
                'buy_timing': buy_timing,
                'sell_timing': sell_timing,
                'optimal_action': optimal_action
            }

        except Exception:
            return {
                'assessment': '分析异常',
                'description': '执行时机分析过程中发生异常',
                'reasoning': '请检查数据质量或联系技术支持',
                'buy_timing': '建议等待系统恢复',
                'sell_timing': '建议等待系统恢复',
                'optimal_action': '等待'
            }

    def _detect_technical_anomalies(self) -> List[Dict[str, str]]:
        """检测技术指标异常"""
        try:
            alerts = []

            if len(self.hourly_data) < 20:
                return alerts

            # 检测均线异常
            prices = self.hourly_data['close']
            ema_5 = prices.ewm(span=5).mean()
            ema_10 = prices.ewm(span=10).mean()

            # 检测均线急剧变化
            if len(ema_5) >= 2:
                ema_5_change = (ema_5.iloc[-1] - ema_5.iloc[-2]) / ema_5.iloc[-2]
                if abs(ema_5_change) > 0.03:
                    alerts.append({
                        'type': 'EMA_ANOMALY',
                        'severity': 'MEDIUM',
                        'message': f'5小时均线变化{ema_5_change*100:.1f}%，技术指标异常'
                    })

            return alerts

        except Exception:
            return []

    def _detect_risk_warnings(self) -> List[Dict[str, str]]:
        """检测风险预警"""
        try:
            alerts = []

            # 检测高风险情况
            risk_analysis = self.analyze_short_term_risks()
            if risk_analysis.get('risk_level') == '高':
                alerts.append({
                    'type': 'HIGH_RISK',
                    'severity': 'HIGH',
                    'message': '短期风险等级为高，建议谨慎操作'
                })

            return alerts

        except Exception:
            return []

    def _generate_urgent_alert_assessment(self, urgent_alerts: List[Dict]) -> Dict[str, str]:
        """生成紧急预警评估"""
        try:
            if not urgent_alerts:
                return {
                    'level': '无异常',
                    'description': '当前未检测到异常情况，市场运行正常。',
                    'recommended_action': '可以正常进行交易操作，但要保持基本的风险管理。'
                }

            high_severity_count = sum(1 for alert in urgent_alerts if alert.get('severity') == 'HIGH')
            medium_severity_count = sum(1 for alert in urgent_alerts if alert.get('severity') == 'MEDIUM')

            if high_severity_count >= 2:
                level = '严重异常'
                description = f'检测到{high_severity_count}个高严重性异常，市场存在重大风险。'
                recommended_action = '建议立即停止交易，等待市场稳定后再操作。'
            elif high_severity_count >= 1:
                level = '重要异常'
                description = f'检测到{high_severity_count}个高严重性异常，需要立即关注。'
                recommended_action = '建议暂停新增仓位，密切监控市场变化。'
            elif medium_severity_count >= 3:
                level = '中等异常'
                description = f'检测到{medium_severity_count}个中等异常，需要注意风险。'
                recommended_action = '建议控制仓位规模，加强风险管理。'
            else:
                level = '轻微异常'
                description = f'检测到{len(urgent_alerts)}个轻微异常，整体风险可控。'
                recommended_action = '可以正常操作，但要注意相关风险点。'

            return {
                'level': level,
                'description': description,
                'recommended_action': recommended_action
            }

        except Exception:
            return {
                'level': '评估异常',
                'description': '预警评估过程中发生异常',
                'recommended_action': '建议谨慎操作，联系技术支持'
            }

    def _analyze_price_breakthrough(self) -> Dict[str, Any]:
        """分析价格突破"""
        try:
            if len(self.hourly_data) < 20:
                return {'signal': 'NONE', 'action': 'HOLD', 'strength': 0, 'reason': '数据不足'}

            prices = self.hourly_data['close']
            highs = self.hourly_data['high']
            lows = self.hourly_data['low']

            # 计算近期高低点
            recent_high = highs.tail(12).max()  # 12小时内最高点
            recent_low = lows.tail(12).min()    # 12小时内最低点
            current_price = prices.iloc[-1]

            # 突破判断
            if current_price > recent_high * 1.005:  # 向上突破
                return {
                    'signal': 'UPWARD_BREAKTHROUGH',
                    'action': 'BUY',
                    'strength': 75,
                    'reason': f'向上突破{recent_high:.2f}'
                }
            elif current_price < recent_low * 0.995:  # 向下突破
                return {
                    'signal': 'DOWNWARD_BREAKTHROUGH',
                    'action': 'SELL',
                    'strength': 75,
                    'reason': f'向下突破{recent_low:.2f}'
                }
            else:
                return {'signal': 'NONE', 'action': 'HOLD', 'strength': 0, 'reason': '无明显突破'}

        except Exception:
            return {'signal': 'ERROR', 'action': 'HOLD', 'strength': 0, 'reason': '突破分析异常'}

    def _identify_hourly_support_resistance(self) -> Dict[str, Optional[float]]:
        """识别时线支撑阻力位"""
        try:
            if len(self.hourly_data) < 24:
                return {'support': None, 'resistance': None}

            highs = self.hourly_data['high'].tail(24)
            lows = self.hourly_data['low'].tail(24)
            current_price = self.hourly_data['close'].iloc[-1]

            # 寻找支撑位
            support_candidates = []
            for i in range(1, len(lows) - 1):
                if lows.iloc[i] < lows.iloc[i-1] and lows.iloc[i] < lows.iloc[i+1]:
                    support_candidates.append(lows.iloc[i])

            # 改进支撑位选择逻辑：优先选择低于当前价格的，如果没有则选择最接近的
            valid_supports = [s for s in support_candidates if s < current_price]
            if valid_supports:
                support = max(valid_supports)  # 选择最接近当前价格的支撑位
            elif support_candidates:
                # 如果没有低于当前价格的支撑位，选择最接近当前价格的
                support = min(support_candidates, key=lambda x: abs(x - current_price))
            else:
                # 如果没有找到支撑位候选，使用最近低点作为支撑位
                support = lows.min() if len(lows) > 0 else None

            # 寻找阻力位
            resistance_candidates = []
            for i in range(1, len(highs) - 1):
                if highs.iloc[i] > highs.iloc[i-1] and highs.iloc[i] > highs.iloc[i+1]:
                    resistance_candidates.append(highs.iloc[i])

            valid_resistances = [r for r in resistance_candidates if r > current_price]
            resistance = min(valid_resistances) if valid_resistances else None

            return {'support': support, 'resistance': resistance}

        except Exception:
            return {'support': None, 'resistance': None}

    def _calculate_technical_entry_price(self) -> Dict[str, Any]:
        """基于技术指标计算入场价格"""
        try:
            if self.indicator_calculator is None:
                return {'price': None, 'confidence': 0}

            indicators = self.indicator_calculator.calculate_all_indicators()
            current_price = self.hourly_data['close'].iloc[-1]

            # 基于EMA计算入场价格
            if 'ema_12' in indicators and not indicators['ema_12'].empty:
                ema_12 = indicators['ema_12'].iloc[-1]

                # 如果价格在EMA上方，入场价格为EMA附近
                if current_price > ema_12:
                    entry_price = ema_12 * 1.001  # EMA上方0.1%
                    confidence = 60
                else:
                    entry_price = ema_12 * 0.999  # EMA下方0.1%
                    confidence = 60

                return {'price': entry_price, 'confidence': confidence}

            return {'price': current_price, 'confidence': 30}

        except Exception:
            return {'price': None, 'confidence': 0}

    def _calculate_volume_based_entry(self) -> Dict[str, Any]:
        """基于成交量计算入场价格"""
        try:
            if len(self.hourly_data) < 10:
                return {'price': None, 'confidence': 0}

            volumes = self.hourly_data['volume']
            prices = self.hourly_data['close']

            # 寻找成交量异常点
            volume_sma = volumes.rolling(window=10).mean()
            current_volume = volumes.iloc[-1]
            avg_volume = volume_sma.iloc[-1]

            if current_volume > avg_volume * 1.5:
                # 放量时，入场价格为当前价格
                entry_price = prices.iloc[-1]
                confidence = 70
            else:
                # 缩量时，等待回调入场
                entry_price = prices.iloc[-1] * 0.998
                confidence = 40

            return {'price': entry_price, 'confidence': confidence}

        except Exception:
            return {'price': None, 'confidence': 0}

    def _calculate_atr_based_levels(self) -> Dict[str, Optional[float]]:
        """基于ATR计算止损止盈位"""
        try:
            if self.indicator_calculator is None or len(self.hourly_data) < 14:
                return {'stop_loss': None, 'take_profit_1': None, 'take_profit_2': None}

            indicators = self.indicator_calculator.calculate_all_indicators()
            current_price = self.hourly_data['close'].iloc[-1]

            if 'atr' in indicators and not indicators['atr'].empty:
                atr = indicators['atr'].iloc[-1]

                # ATR倍数设置
                stop_loss_multiplier = 1.5
                tp1_multiplier = 2.0
                tp2_multiplier = 3.0

                return {
                    'stop_loss': current_price - (atr * stop_loss_multiplier),
                    'take_profit_1': current_price + (atr * tp1_multiplier),
                    'take_profit_2': current_price + (atr * tp2_multiplier)
                }

            return {'stop_loss': None, 'take_profit_1': None, 'take_profit_2': None}

        except Exception:
            return {'stop_loss': None, 'take_profit_1': None, 'take_profit_2': None}

    def _calculate_support_resistance_levels(self) -> Dict[str, Optional[float]]:
        """基于支撑阻力位计算止损止盈位"""
        try:
            sr_data = self._identify_hourly_support_resistance()
            current_price = self.hourly_data['close'].iloc[-1]

            support = sr_data['support']
            resistance = sr_data['resistance']

            # 基于支撑阻力位设置止损止盈
            stop_loss = support * 0.998 if support else current_price * 0.98
            take_profit_1 = resistance * 0.998 if resistance else current_price * 1.02
            take_profit_2 = resistance * 1.005 if resistance else current_price * 1.05

            return {
                'stop_loss': stop_loss,
                'take_profit_1': take_profit_1,
                'take_profit_2': take_profit_2
            }

        except Exception:
            return {'stop_loss': None, 'take_profit_1': None, 'take_profit_2': None}

    def _calculate_volatility_based_levels(self) -> Dict[str, Optional[float]]:
        """基于波动率计算止损止盈位"""
        try:
            if len(self.hourly_data) < 20:
                return {'stop_loss': None, 'take_profit_1': None, 'take_profit_2': None}

            prices = self.hourly_data['close']
            current_price = prices.iloc[-1]

            # 计算短期波动率
            volatility = prices.pct_change().tail(12).std()

            # 基于波动率设置止损止盈
            stop_loss_pct = volatility * 2
            tp1_pct = volatility * 3
            tp2_pct = volatility * 5

            return {
                'stop_loss': current_price * (1 - stop_loss_pct),
                'take_profit_1': current_price * (1 + tp1_pct),
                'take_profit_2': current_price * (1 + tp2_pct)
            }

        except Exception:
            return {'stop_loss': None, 'take_profit_1': None, 'take_profit_2': None}

    def _calculate_gap_risk(self) -> int:
        """计算价格跳空风险"""
        try:
            if len(self.hourly_data) < 5:
                return 30

            # 检查最近的价格跳空
            closes = self.hourly_data['close']
            opens = self.hourly_data['open']

            gaps = []
            for i in range(1, min(5, len(closes))):
                gap = abs(opens.iloc[i] - closes.iloc[i-1]) / closes.iloc[i-1]
                gaps.append(gap)

            avg_gap = np.mean(gaps) if gaps else 0

            if avg_gap > 0.02:
                return 80
            elif avg_gap > 0.01:
                return 50
            else:
                return 20

        except Exception:
            return 30

    def _calculate_time_based_risk(self) -> int:
        """计算基于时间的风险"""
        try:
            current_time = datetime.now()
            hour = current_time.hour

            # 根据交易时间评估风险
            if 9 <= hour <= 11 or 13 <= hour <= 15:  # 主要交易时间
                return 20
            elif 21 <= hour <= 23 or 1 <= hour <= 3:  # 夜间交易时间
                return 50
            else:  # 非主要交易时间
                return 80

        except Exception:
            return 50

    def _generate_execution_plan(self, immediate_action: Dict, optimal_entry: Dict,
                               stop_loss_take_profit: Dict, risk_assessment: Dict) -> Dict:
        """生成执行计划"""
        try:
            plan = {
                'recommended_action': 'HOLD',
                'entry_strategy': 'MARKET',
                'position_size': 'NORMAL',
                'execution_timing': 'IMMEDIATE',
                'risk_management': {},
                'execution_notes': []
            }

            # 基于即时行动建议
            action = immediate_action.get('immediate_action', 'HOLD')
            urgency = immediate_action.get('urgency_level', 'LOW')

            plan['recommended_action'] = action

            if urgency == 'HIGH':
                plan['execution_timing'] = 'IMMEDIATE'
                plan['entry_strategy'] = 'MARKET'
            elif urgency == 'MEDIUM':
                plan['execution_timing'] = 'WITHIN_HOUR'
                plan['entry_strategy'] = 'LIMIT'
            else:
                plan['execution_timing'] = 'PATIENT'
                plan['entry_strategy'] = 'LIMIT'

            # 基于风险评估调整仓位
            risk_level = risk_assessment.get('execution_risk_level', 'MEDIUM')

            if risk_level == 'HIGH':
                plan['position_size'] = 'SMALL'
                plan['execution_notes'].append('高风险环境，建议小仓位操作')
            elif risk_level == 'LOW':
                plan['position_size'] = 'LARGE'
                plan['execution_notes'].append('低风险环境，可适当加大仓位')
            else:
                plan['position_size'] = 'NORMAL'

            # 风险管理设置
            plan['risk_management'] = {
                'stop_loss': stop_loss_take_profit.get('stop_loss_price'),
                'take_profit_1': stop_loss_take_profit.get('take_profit_price_1'),
                'take_profit_2': stop_loss_take_profit.get('take_profit_price_2'),
                'entry_price': optimal_entry.get('optimal_entry_price')
            }

            # 添加执行注意事项
            if optimal_entry.get('confidence', 0) < 50:
                plan['execution_notes'].append('入场价格置信度较低，建议谨慎操作')

            risk_reward = stop_loss_take_profit.get('risk_reward_ratio', {})
            if risk_reward.get('ratio_1', 0) < 1.5:
                plan['execution_notes'].append('风险收益比偏低，建议重新评估')

            return plan

        except Exception as e:
            return {
                'recommended_action': 'HOLD',
                'entry_strategy': 'MARKET',
                'position_size': 'NORMAL',
                'execution_timing': 'IMMEDIATE',
                'risk_management': {},
                'execution_notes': [f'执行计划生成异常: {str(e)}']
            }
