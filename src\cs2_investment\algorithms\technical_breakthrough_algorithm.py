"""
技术突破型投资筛选算法

基于技术分析识别突破信号的投资机会。
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from typing import List, Dict, Any
from loguru import logger

from .base_algorithm import BaseScreeningAlgorithm
from ..models.market_snapshot import MarketSnapshot
from ..models.analysis_result import AnalysisResult


class TechnicalBreakthroughAlgorithm(BaseScreeningAlgorithm):
    """技术突破型投资筛选算法"""
    
    def __init__(self):
        super().__init__(
            algorithm_name="TechnicalBreakthroughAlgorithm",
            investment_type="技术突破型",
            version="1.0"
        )
    
    def get_screening_query(self, session: Session):
        """获取技术突破型筛选查询"""
        # 联合查询market_snapshots和analysis_results
        return session.query(MarketSnapshot, AnalysisResult)\
            .join(AnalysisResult, MarketSnapshot.item_id == AnalysisResult.item_id)\
            .filter(
                and_(
                    # 基础流动性要求
                    MarketSnapshot.trans_count_1d > 5,
                    MarketSnapshot.current_price > 50,
                    
                    # 技术指标要求
                    AnalysisResult.rsi_value.isnot(None),
                    AnalysisResult.support_level_1.isnot(None),
                    AnalysisResult.resistance_level_1.isnot(None),
                    
                    # 突破信号筛选条件
                    or_(
                        # 向上突破阻力位
                        and_(
                            MarketSnapshot.current_price > AnalysisResult.resistance_level_1,
                            MarketSnapshot.diff_1d > 2,  # 当日涨幅 > 2%
                            MarketSnapshot.trans_count_1d > MarketSnapshot.trans_count_7d / 7 * 1.5  # 成交量放大
                        ),
                        # RSI从超卖区域突破
                        and_(
                            AnalysisResult.rsi_value > 30,
                            AnalysisResult.rsi_value < 70,
                            MarketSnapshot.diff_3d > 0  # 3日累计上涨
                        ),
                        # MACD金叉信号
                        and_(
                            AnalysisResult.macd_value > 0,
                            MarketSnapshot.diff_7d > -5,  # 7日跌幅不超过5%
                            MarketSnapshot.trans_amount_1d > 10000  # 有一定成交额
                        )
                    )
                )
            )\
            .order_by(desc(MarketSnapshot.diff_1d))
    
    def calculate_score(self, snapshot_and_analysis) -> float:
        """计算技术突破评分"""
        snapshot, analysis = snapshot_and_analysis
        score = 0.0
        
        # 1. 突破强度评分 (30分)
        breakthrough_score = self._calculate_breakthrough_strength(snapshot, analysis)
        score += breakthrough_score
        
        # 2. 成交量确认评分 (25分)
        volume_score = self._calculate_volume_confirmation(snapshot)
        score += volume_score
        
        # 3. 技术指标确认评分 (25分)
        technical_score = self._calculate_technical_confirmation(analysis)
        score += technical_score
        
        # 4. 趋势一致性评分 (20分)
        trend_score = self._calculate_trend_consistency(snapshot)
        score += trend_score
        
        return min(score, 100.0)
    
    def _calculate_breakthrough_strength(self, snapshot: MarketSnapshot, analysis: AnalysisResult) -> float:
        """计算突破强度评分"""
        score = 0.0
        
        if not snapshot.current_price or not analysis.resistance_level_1:
            return score
        
        current_price = float(snapshot.current_price)
        resistance = float(analysis.resistance_level_1)
        
        # 价格突破阻力位的幅度
        if current_price > resistance:
            breakthrough_ratio = (current_price - resistance) / resistance * 100
            if breakthrough_ratio >= 5:
                score += 30  # 强突破
            elif breakthrough_ratio >= 2:
                score += 25  # 中等突破
            elif breakthrough_ratio >= 1:
                score += 20  # 弱突破
            else:
                score += 15  # 刚突破
        
        # 价格接近阻力位（蓄势突破）
        elif current_price > resistance * 0.98:
            score += 10
        
        return score
    
    def _calculate_volume_confirmation(self, snapshot: MarketSnapshot) -> float:
        """计算成交量确认评分"""
        score = 0.0
        
        # 当日成交量与7日平均的比较
        if snapshot.trans_count_1d and snapshot.trans_count_7d:
            daily_avg = snapshot.trans_count_7d / 7
            volume_ratio = snapshot.trans_count_1d / daily_avg
            
            if volume_ratio >= 3:
                score += 25  # 成交量大幅放大
            elif volume_ratio >= 2:
                score += 20  # 成交量明显放大
            elif volume_ratio >= 1.5:
                score += 15  # 成交量适度放大
            elif volume_ratio >= 1.2:
                score += 10  # 成交量略有放大
        
        # 成交额确认
        if snapshot.trans_amount_1d:
            amount = float(snapshot.trans_amount_1d)
            if amount >= 100000:
                score += 5  # 大额成交确认
            elif amount >= 50000:
                score += 3  # 中等成交确认
        
        return score
    
    def _calculate_technical_confirmation(self, analysis: AnalysisResult) -> float:
        """计算技术指标确认评分"""
        score = 0.0
        
        # RSI指标确认
        if analysis.rsi_value:
            rsi = float(analysis.rsi_value)
            if 40 <= rsi <= 65:  # 健康的突破区间
                score += 10
            elif 30 <= rsi <= 70:  # 可接受区间
                score += 8
            elif rsi > 70:  # 超买风险
                score += 3
        
        # MACD指标确认
        if analysis.macd_value:
            macd = float(analysis.macd_value)
            if macd > 0:
                score += 8  # 金叉确认
            elif macd > -0.1:
                score += 5  # 接近金叉
        
        # KDJ指标确认
        if analysis.kdj_k and analysis.kdj_d:
            k = float(analysis.kdj_k)
            d = float(analysis.kdj_d)
            if k > d and k < 80:  # K线上穿D线且未超买
                score += 7
            elif k > d:
                score += 4
        
        return score
    
    def _calculate_trend_consistency(self, snapshot: MarketSnapshot) -> float:
        """计算趋势一致性评分"""
        score = 0.0
        
        # 多时间框架趋势一致性
        positive_trends = 0
        total_trends = 0
        
        for period, diff_field in [
            ('1d', snapshot.diff_1d),
            ('3d', snapshot.diff_3d), 
            ('7d', snapshot.diff_7d),
            ('15d', snapshot.diff_15d)
        ]:
            if diff_field is not None:
                total_trends += 1
                if float(diff_field) > 0:
                    positive_trends += 1
        
        if total_trends > 0:
            trend_ratio = positive_trends / total_trends
            if trend_ratio >= 0.75:  # 75%以上时间框架上涨
                score += 20
            elif trend_ratio >= 0.5:  # 50%以上时间框架上涨
                score += 15
            elif trend_ratio >= 0.25:  # 25%以上时间框架上涨
                score += 10
        
        return score
    
    def determine_risk_level(self, snapshot_and_analysis, score: float) -> str:
        """确定技术突破风险等级"""
        snapshot, analysis = snapshot_and_analysis
        risk_factors = 0
        
        # RSI超买风险
        if analysis.rsi_value and float(analysis.rsi_value) > 75:
            risk_factors += 1
        
        # 价格波动风险
        if snapshot.diff_7d and abs(float(snapshot.diff_7d)) > 30:
            risk_factors += 1
        
        # 流动性风险
        if not snapshot.trans_count_1d or snapshot.trans_count_1d < 10:
            risk_factors += 1
        
        # 突破幅度过大风险
        if snapshot.diff_1d and float(snapshot.diff_1d) > 15:
            risk_factors += 1
        
        if risk_factors <= 1:
            return "LOW"
        elif risk_factors <= 2:
            return "MEDIUM"
        else:
            return "HIGH"
    
    def generate_recommendation(self, score: float, risk_level: str) -> str:
        """生成技术突破投资建议"""
        if score >= 75:
            return "BUY" if risk_level != "HIGH" else "HOLD"
        elif score >= 60:
            return "HOLD" if risk_level == "LOW" else "WAIT"
        elif score >= 40:
            return "WAIT"
        else:
            return "AVOID"
    
    def generate_analysis_summary(self, snapshot_and_analysis, score: float) -> str:
        """生成技术突破分析摘要"""
        snapshot, analysis = snapshot_and_analysis
        summary_parts = []
        
        # 基础信息
        if snapshot.current_price:
            summary_parts.append(f"价格: ¥{snapshot.current_price}")
        
        # 突破信息
        if analysis.resistance_level_1 and snapshot.current_price:
            resistance = float(analysis.resistance_level_1)
            current = float(snapshot.current_price)
            if current > resistance:
                breakthrough_pct = (current - resistance) / resistance * 100
                summary_parts.append(f"突破阻力位: +{breakthrough_pct:.1f}%")
        
        # 技术指标
        if analysis.rsi_value:
            summary_parts.append(f"RSI: {analysis.rsi_value}")
        
        if analysis.macd_value:
            macd_status = "金叉" if float(analysis.macd_value) > 0 else "死叉"
            summary_parts.append(f"MACD: {macd_status}")
        
        # 成交量
        if snapshot.trans_count_1d:
            summary_parts.append(f"日成交量: {snapshot.trans_count_1d}")
        
        # 评分总结
        if score >= 75:
            summary_parts.append("技术突破: 强烈")
        elif score >= 60:
            summary_parts.append("技术突破: 明显")
        elif score >= 40:
            summary_parts.append("技术突破: 一般")
        else:
            summary_parts.append("技术突破: 较弱")
        
        return "; ".join(summary_parts)
