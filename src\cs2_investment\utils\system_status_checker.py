#!/usr/bin/env python3
"""
系统状态检查脚本

检查CS2饰品投资分析系统各组件的运行状态
"""

import sys
import requests
import subprocess
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

def print_header(title: str):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def print_status(item: str, status: bool, details: str = ""):
    """打印状态"""
    status_icon = "✅" if status else "❌"
    print(f"{status_icon} {item}")
    if details:
        print(f"   {details}")

def check_api_service():
    """检查API服务状态"""
    print_header("API服务状态检查")
    
    try:
        # 健康检查
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print_status("API服务", True, f"状态: {health_data.get('data', {}).get('status', 'unknown')}")
            
            # 数据库状态
            db_status = health_data.get('data', {}).get('database', 'unknown')
            print_status("数据库连接", db_status == 'connected', f"状态: {db_status}")
            
        else:
            print_status("API服务", False, f"HTTP状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print_status("API服务", False, "连接失败 - 服务可能未启动")
        return False
    except Exception as e:
        print_status("API服务", False, f"检查失败: {e}")
        return False
    
    try:
        # 服务信息
        response = requests.get("http://localhost:8000/info", timeout=5)
        if response.status_code == 200:
            info_data = response.json()
            service_data = info_data.get('data', {})
            print_status("服务信息", True, f"版本: {service_data.get('version', 'unknown')}")
        
        # 任务引擎统计
        response = requests.get("http://localhost:8000/api/tasks/engine/stats", timeout=5)
        if response.status_code == 200:
            stats_data = response.json()
            engine_stats = stats_data.get('data', {}).get('engine_stats', {})
            
            print_status("任务引擎", engine_stats.get('is_running', False), 
                        f"队列: {engine_stats.get('queue_size', 0)}, "
                        f"运行中: {engine_stats.get('running_tasks_count', 0)}, "
                        f"工作线程: {engine_stats.get('worker_count', 0)}")
            
            # 任务统计
            task_stats = engine_stats.get('stats', {})
            total = task_stats.get('total_processed', 0)
            successful = task_stats.get('successful', 0)
            failed = task_stats.get('failed', 0)
            
            if total > 0:
                success_rate = successful / total * 100
                print_status("任务统计", True, 
                           f"总计: {total}, 成功: {successful}, 失败: {failed}, 成功率: {success_rate:.1f}%")
            else:
                print_status("任务统计", True, "暂无任务执行记录")
                
    except Exception as e:
        print_status("扩展信息获取", False, f"获取失败: {e}")
    
    return True

def check_streamlit_app():
    """检查Streamlit应用状态"""
    print_header("Streamlit应用状态检查")
    
    try:
        # 检查Streamlit端口
        response = requests.get("http://localhost:8504", timeout=5)
        if response.status_code == 200:
            print_status("Streamlit应用", True, "应用正常运行")
            return True
        else:
            print_status("Streamlit应用", False, f"HTTP状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print_status("Streamlit应用", False, "连接失败 - 应用可能未启动")
        return False
    except Exception as e:
        print_status("Streamlit应用", False, f"检查失败: {e}")
        return False

def check_database():
    """检查数据库连接"""
    print_header("数据库连接检查")
    
    try:
        from src.cs2_investment.config.database import create_database_engine
        
        engine = create_database_engine()
        with engine.connect() as conn:
            result = conn.execute("SELECT 1")
            print_status("数据库连接", True, "连接正常")
            
            # 检查主要表
            tables_to_check = ['items', 'analysis_logs', 'daily_analysis_schedules']
            for table in tables_to_check:
                try:
                    result = conn.execute(f"SELECT COUNT(*) FROM {table}")
                    count = result.scalar()
                    print_status(f"表 {table}", True, f"记录数: {count}")
                except Exception as e:
                    print_status(f"表 {table}", False, f"查询失败: {e}")
            
            return True
            
    except Exception as e:
        print_status("数据库连接", False, f"连接失败: {e}")
        return False

def check_dependencies():
    """检查依赖项"""
    print_header("依赖项检查")
    
    # 检查Python包
    required_packages = [
        'fastapi', 'uvicorn', 'streamlit', 'playwright', 
        'aiohttp', 'requests', 'pymysql', 'sqlalchemy'
    ]
    
    for package in required_packages:
        try:
            __import__(package)
            print_status(f"Python包 {package}", True)
        except ImportError:
            print_status(f"Python包 {package}", False, "未安装")
    
    # 检查Playwright浏览器
    try:
        result = subprocess.run(['playwright', 'install', '--dry-run'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print_status("Playwright浏览器", True, "已安装")
        else:
            print_status("Playwright浏览器", False, "可能未安装")
    except Exception as e:
        print_status("Playwright浏览器", False, f"检查失败: {e}")

def check_ports():
    """检查端口占用"""
    print_header("端口占用检查")
    
    import socket
    
    ports_to_check = [
        (8000, "FastAPI服务"),
        (8504, "Streamlit应用"),
        (3306, "MySQL数据库")
    ]
    
    for port, service in ports_to_check:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        
        try:
            result = sock.connect_ex(('localhost', port))
            if result == 0:
                print_status(f"端口 {port} ({service})", True, "端口已占用")
            else:
                print_status(f"端口 {port} ({service})", False, "端口未占用")
        except Exception as e:
            print_status(f"端口 {port} ({service})", False, f"检查失败: {e}")
        finally:
            sock.close()

def main():
    """主函数"""
    print("🎯 CS2饰品投资分析系统 - 状态检查")
    print(f"🕐 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项检查
    api_ok = check_api_service()
    streamlit_ok = check_streamlit_app()
    db_ok = check_database()
    
    check_dependencies()
    check_ports()
    
    # 总结
    print_header("检查总结")
    
    all_ok = api_ok and streamlit_ok and db_ok
    
    if all_ok:
        print("🎉 系统状态良好，所有核心组件正常运行！")
        print("📍 访问地址:")
        print("   - Streamlit应用: http://localhost:8504")
        print("   - API文档: http://localhost:8000/docs")
    else:
        print("⚠️ 系统存在问题，请检查上述失败项目")
        
        if not api_ok:
            print("💡 建议: 启动API服务 - python start_api_service.py")
        if not streamlit_ok:
            print("💡 建议: 启动Streamlit应用 - python start_streamlit_app.py")
        if not db_ok:
            print("💡 建议: 检查数据库配置和连接")
    
    print("\n" + "=" * 60)
    
    return 0 if all_ok else 1

if __name__ == "__main__":
    sys.exit(main())
