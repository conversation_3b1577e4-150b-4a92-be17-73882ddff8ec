"""
抓取器配置文件

定义抓取器的各种配置参数
"""

from dataclasses import dataclass
from typing import Dict, List


@dataclass
class ScraperConfig:
    """抓取器配置"""
    
    # 基础URL配置
    base_url: str = "https://steamdt.com"
    api_base_url: str = "https://sdt-api.ok-skins.com"
    
    # API端点
    trend_api_endpoint: str = "/user/steam/type-trend/v2/item/details"
    kline_api_endpoint: str = "/user/steam/category/v1/kline"
    
    # 浏览器配置
    headless: bool = True
    browser_args: List[str] = None
    user_agent: str = (
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
        '(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    )
    
    # 请求配置
    request_timeout: int = 60000  # 60秒（增加超时时间，适应实时分析的复杂操作）
    page_load_timeout: int = 60000  # 60秒（增加页面加载超时时间）
    request_delay: float = 2.0  # 请求间隔（秒）
    
    # 重试配置
    max_retries: int = 3
    retry_delay: float = 5.0
    
    # 数据配置
    trend_time_range: str = "6months"  # 走势数据时间范围
    
    # K线类型配置
    kline_types: Dict[str, Dict] = None
    
    # 输出配置
    output_dir: str = "output/scraper_data"
    save_json: bool = True
    save_csv: bool = True
    
    def __post_init__(self):
        """初始化后处理"""
        if self.browser_args is None:
            self.browser_args = [
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--disable-extensions'
            ]
        
        if self.kline_types is None:
            self.kline_types = {
                'hourly': {'type': 1, 'name': '时K'},
                'daily': {'type': 2, 'name': '日K'},
                'weekly': {'type': 3, 'name': '周K'}
            }
    
    @property
    def trend_api_url(self) -> str:
        """走势数据API完整URL"""
        return f"{self.api_base_url}{self.trend_api_endpoint}"
    
    @property
    def kline_api_url(self) -> str:
        """K线数据API完整URL"""
        return f"{self.api_base_url}{self.kline_api_endpoint}"


# 默认配置实例
DEFAULT_CONFIG = ScraperConfig()


# 预定义的饰品URL列表（用于测试）
SAMPLE_ITEM_URLS = [
    "https://steamdt.com/cs2/Desert%20Eagle%20%7C%20Printstream%20(Field-Tested)",
    "https://steamdt.com/cs2/AK-47%20%7C%20Redline%20(Field-Tested)",
    "https://steamdt.com/cs2/AWP%20%7C%20Dragon%20Lore%20(Field-Tested)",
    "https://steamdt.com/cs2/M4A4%20%7C%20Howl%20(Field-Tested)",
    "https://steamdt.com/cs2/Glock-18%20%7C%20Fade%20(Factory%20New)"
]


# 常用饰品类别
ITEM_CATEGORIES = {
    "rifles": [
        "AK-47",
        "M4A4",
        "M4A1-S",
        "AWP",
        "SSG 08"
    ],
    "pistols": [
        "Desert Eagle",
        "Glock-18",
        "USP-S",
        "P250",
        "Five-SeveN"
    ],
    "knives": [
        "Karambit",
        "M9 Bayonet",
        "Butterfly Knife",
        "Bayonet",
        "Flip Knife"
    ]
}


# 磨损等级映射
WEAR_LEVELS = {
    "Factory New": "崭新出厂",
    "Minimal Wear": "略有磨损", 
    "Field-Tested": "久经沙场",
    "Well-Worn": "破损不堪",
    "Battle-Scarred": "战痕累累"
}


# 稀有度映射
RARITY_LEVELS = {
    "Consumer Grade": "消费级",
    "Industrial Grade": "工业级",
    "Mil-Spec Grade": "军规级",
    "Restricted": "受限",
    "Classified": "保密",
    "Covert": "隐秘",
    "Extraordinary": "非凡"
}
