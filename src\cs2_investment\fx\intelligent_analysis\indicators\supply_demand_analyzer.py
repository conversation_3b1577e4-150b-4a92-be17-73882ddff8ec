"""
供需关系分析器

专门用于分析CS2饰品的供需关系，基于走势数据(3m/6m)进行
供需平衡分析、价差分析、异常检测等功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import warnings
from loguru import logger

# 导入统一数据处理器
from ..utils.data_processor import StandardizedDataProcessor, DataValidator

warnings.filterwarnings('ignore')


class SupplyDemandAnalyzer:
    """供需关系分析器"""
    
    def __init__(self, supply_demand_data: Dict[str, Any]):
        """
        初始化供需关系分析器
        
        Args:
            supply_demand_data: 供需数据字典 {'3m': data, '6m': data}
        """
        self.supply_demand_data = supply_demand_data or {}
        self.logger = logger.bind(analyzer=self.__class__.__name__)
        
        # 分析结果缓存
        self.analysis_cache = {}
        
        # 验证数据
        self._validate_data()
    
    def _validate_data(self):
        """验证供需数据"""
        if not self.supply_demand_data:
            self.logger.warning("未提供供需数据")
            return

        for source, data in self.supply_demand_data.items():
            if not data:
                self.logger.warning(f"数据源 {source} 为空")
                continue

            if isinstance(data, list) and len(data) == 0:
                self.logger.warning(f"数据源 {source} 列表为空")
            elif isinstance(data, pd.DataFrame) and data.empty:
                self.logger.warning(f"数据源 {source} DataFrame为空")

    def _get_price_position_pct(self, historical_data: Any, current_price: float) -> Optional[float]:
        """
        计算当前价格在历史区间中的位置百分比

        复用MarketContextAnalyzer的价格位置分析逻辑，用于供需分析中的价格位置感知

        Args:
            historical_data: 历史价格数据（可以是list、DataFrame等格式）
            current_price: 当前价格

        Returns:
            Optional[float]: 价格位置百分比（0-100），如果无法计算则返回None
        """
        try:
            if current_price is None or current_price <= 0:
                self.logger.debug("当前价格无效，无法计算价格位置")
                return None

            # 提取历史价格数据
            historical_prices = []

            if isinstance(historical_data, list):
                # 处理列表格式的走势数据
                for item in historical_data:
                    if isinstance(item, (list, tuple)) and len(item) >= 2:
                        # 走势数据格式：[timestamp, price, ...]
                        # 直接取第二个元素作为价格
                        price = item[1] if len(item) > 1 else None
                        if price and price > 0:
                            historical_prices.append(price)
                    elif isinstance(item, dict):
                        price = self._extract_value(item, ['steamPrice', 'price', 'current_price'])
                        if price and price > 0:
                            historical_prices.append(price)

            elif isinstance(historical_data, pd.DataFrame):
                # 处理DataFrame格式数据
                price_columns = ['steamPrice', 'price', 'current_price', 'close', 'high', 'low']
                for col in price_columns:
                    if col in historical_data.columns:
                        prices = historical_data[col].dropna()
                        if not prices.empty:
                            historical_prices.extend(prices.tolist())
                            break

            # 如果没有足够的历史数据，返回None
            if len(historical_prices) < 2:
                self.logger.debug(f"历史价格数据不足（{len(historical_prices)}个），无法计算价格位置")
                return None

            # 计算价格位置百分比
            historical_high = max(historical_prices)
            historical_low = min(historical_prices)

            if historical_high <= historical_low:
                self.logger.debug("历史价格区间无效，无法计算价格位置")
                return None

            # 计算位置百分比：(当前价格 - 历史最低) / (历史最高 - 历史最低) * 100
            position_pct = (current_price - historical_low) / (historical_high - historical_low) * 100
            position_pct = max(0, min(100, position_pct))  # 确保在0-100范围内

            self.logger.debug(f"价格位置计算: 当前={current_price}, 区间=[{historical_low}, {historical_high}], 位置={position_pct:.1f}%")
            return round(position_pct, 1)

        except Exception as e:
            self.logger.warning(f"价格位置计算失败: {e}")
            return None
    
    def analyze_supply_demand_status(self) -> Dict[str, Any]:
        """
        分析供需关系状态 - 新增方法

        Returns:
            Dict包含当前供需关系的具体描述
        """
        try:
            if not self.supply_demand_data:
                return {
                    'supply_demand_status': '数据不足',
                    'status_description': '缺少供需数据，无法分析供需关系状态',
                    'status_reasoning': '供需分析需要3m和6m的供需数据',
                    'error': '缺少供需数据'
                }

            self.logger.info("开始供需关系状态分析...")

            # 分析各个数据源的供需状态
            source_statuses = {}
            for source, data in self.supply_demand_data.items():
                source_status = self._analyze_source_supply_demand_status(source, data)
                source_statuses[source] = source_status

            # 生成综合供需状态分析
            status_analysis = self._generate_supply_demand_status_analysis(source_statuses)

            result = {
                'supply_demand_status': status_analysis['status'],
                'status_description': status_analysis['description'],
                'status_reasoning': status_analysis['reasoning'],
                'source_statuses': source_statuses,
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"供需关系状态分析完成: {status_analysis['status']}")
            return result

        except Exception as e:
            self.logger.error(f"供需关系状态分析失败: {e}")
            return {
                'supply_demand_status': '分析异常',
                'status_description': f'供需关系状态分析过程中发生异常: {str(e)}',
                'status_reasoning': '请检查数据质量或联系技术支持',
                'error': str(e)
            }

    def analyze_supply_demand_trends(self) -> Dict[str, Any]:
        """
        分析供需变化趋势 - 新增方法

        Returns:
            Dict包含供需关系的变化方向和程度
        """
        try:
            if not self.supply_demand_data:
                return {
                    'trend_direction': '无法确定',
                    'trend_description': '缺少供需数据，无法分析变化趋势',
                    'trend_reasoning': '趋势分析需要历史供需数据',
                    'error': '缺少供需数据'
                }

            self.logger.info("开始供需变化趋势分析...")

            # 分析各个数据源的趋势
            source_trends = {}
            for source, data in self.supply_demand_data.items():
                source_trend = self._analyze_source_trend(source, data)
                source_trends[source] = source_trend

            # 生成综合趋势分析
            trend_analysis = self._generate_supply_demand_trend_analysis(source_trends)

            result = {
                'trend_direction': trend_analysis['direction'],
                'trend_description': trend_analysis['description'],
                'trend_reasoning': trend_analysis['reasoning'],
                'trend_strength': trend_analysis['strength'],
                'source_trends': source_trends,
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"供需变化趋势分析完成: {trend_analysis['direction']}")
            return result

        except Exception as e:
            self.logger.error(f"供需变化趋势分析失败: {e}")
            return {
                'trend_direction': '分析异常',
                'trend_description': f'供需变化趋势分析过程中发生异常: {str(e)}',
                'trend_reasoning': '请检查数据质量或联系技术支持',
                'error': str(e)
            }

    def analyze_comprehensive(self) -> Dict:
        """综合供需关系分析 - 保留原有方法"""
        try:
            self.logger.info("开始综合供需关系分析...")

            analysis_result = {
                'analysis_timestamp': datetime.now(),
                'data_sources': list(self.supply_demand_data.keys()),
                'source_analyses': {},
                'combined_analysis': {}
            }

            # 分析各个数据源
            for source, data in self.supply_demand_data.items():
                source_analysis = self._analyze_single_source(source, data)
                analysis_result['source_analyses'][source] = source_analysis

            # 综合分析
            combined_analysis = self._combine_analyses(analysis_result['source_analyses'])
            analysis_result['combined_analysis'] = combined_analysis

            # 异常检测
            anomalies = self._detect_anomalies(analysis_result['source_analyses'])
            analysis_result['anomalies'] = anomalies

            # 市场平衡评分
            balance_score = self._calculate_market_balance_score(combined_analysis)
            analysis_result['market_balance_score'] = balance_score

            # 添加成功标志
            analysis_result['success'] = True
            analysis_result['status'] = 'SUCCESS'

            self.logger.info("综合供需关系分析完成")
            return analysis_result

        except Exception as e:
            self.logger.error(f"综合供需分析失败: {e}")
            return {
                'analysis_timestamp': datetime.now(),
                'error': str(e),
                'status': 'ERROR',
                'success': False
            }
    
    def _analyze_single_source(self, source: str, data: Any) -> Dict:
        """分析单一数据源"""
        try:
            if isinstance(data, list):
                return self._analyze_list_data(source, data)
            elif isinstance(data, pd.DataFrame):
                return self._analyze_dataframe_data(source, data)
            elif isinstance(data, dict):
                return self._analyze_dict_data(source, data)
            else:
                return {
                    'status': 'UNSUPPORTED_FORMAT',
                    'data_type': type(data).__name__
                }
                
        except Exception as e:
            self.logger.error(f"单一数据源分析失败 ({source}): {e}")
            return {
                'status': 'ERROR',
                'error': str(e)
            }
    
    def _analyze_list_data(self, source: str, data: List) -> Dict:
        """分析列表格式数据"""
        if not data:
            return {'status': 'NO_DATA'}
        
        try:
            # 走势数据是数组格式，每个元素包含供需信息
            # 数据结构：[timestamp, current_price, supply_quantity, bid_price, demand_quantity, amount, volume, circulation]
            latest_item = data[-1] if data else []

            # 验证数据格式
            if not isinstance(latest_item, (list, tuple)) or len(latest_item) < 8:
                self.logger.warning(f"走势数据格式错误: {type(latest_item)}, 长度: {len(latest_item) if hasattr(latest_item, '__len__') else 'N/A'}")
                return {'status': 'ERROR', 'error': '数据格式错误'}

            # 根据走势数据的真实数组结构提取字段
            supply_quantity = self._extract_value(latest_item, ['steamSellCount', 'supply', 'supply_quantity', 'sell_orders'])
            demand_quantity = self._extract_value(latest_item, ['steamBuyCount', 'demand', 'demand_quantity', 'buy_orders'])
            current_price = self._extract_value(latest_item, ['steamPrice', 'price', 'current_price', 'last_price'])
            bid_price = self._extract_value(latest_item, ['steamBuyPrice', 'bid', 'bid_price', 'buy_price'])

            # 提取额外的走势数据字段
            volume = self._extract_value(latest_item, ['volume'])
            amount = self._extract_value(latest_item, ['amount'])
            circulation = self._extract_value(latest_item, ['circulation', 'total_supply'])

            self.logger.debug(f"提取的供需数据: 在售={supply_quantity}, 求购={demand_quantity}, 价格={current_price}, 求购价={bid_price}")

            # 计算价格位置百分比（新增功能）
            price_position_pct = self._get_price_position_pct(data, current_price)

            # 计算传统供需比率（保持向后兼容）
            supply_demand_ratio = self._calculate_supply_demand_ratio(supply_quantity, demand_quantity)

            # 计算基于经济学原理的供需分析（新算法）
            unified_supply_analysis = self.calculate_unified_supply_ratio(
                supply_quantity=supply_quantity,
                demand_quantity=demand_quantity,
                current_price=current_price,
                bid_price=bid_price,
                volume=volume,
                circulation=circulation,
                price_position_pct=price_position_pct  # 传递价格位置信息
            )

            # 价差分析
            spread_analysis = self._analyze_price_spread(current_price, bid_price)

            # 趋势分析 (如果有历史数据)
            trend_analysis = self._analyze_trend(data, source)

            return {
                'status': 'SUCCESS',
                'supply_quantity': supply_quantity,
                'demand_quantity': demand_quantity,
                'supply_demand_ratio': supply_demand_ratio,  # 保持向后兼容
                'unified_supply_analysis': unified_supply_analysis,  # 新增统一分析
                'current_price': current_price,
                'bid_price': bid_price,
                'volume': volume,  # 新增：交易量
                'amount': amount,  # 新增：交易额
                'circulation': circulation,  # 新增：存世量
                'price_position_pct': price_position_pct,  # 新增：价格位置百分比
                'spread_analysis': spread_analysis,
                'trend_analysis': trend_analysis,
                'data_points': len(data)
            }
            
        except Exception as e:
            return {
                'status': 'ERROR',
                'error': str(e)
            }
    
    def _analyze_dataframe_data(self, source: str, data: pd.DataFrame) -> Dict:
        """分析DataFrame格式数据"""
        if data.empty:
            return {'status': 'NO_DATA'}
        
        try:
            latest_row = data.iloc[-1]
            
            # 根据实际数据结构提取字段
            supply_quantity = self._extract_value(latest_row, ['steamSellCount', 'supply', 'supply_quantity', 'sell_orders'])
            demand_quantity = self._extract_value(latest_row, ['steamBuyCount', 'demand', 'demand_quantity', 'buy_orders'])
            current_price = self._extract_value(latest_row, ['steamPrice', 'price', 'current_price', 'last_price'])
            bid_price = self._extract_value(latest_row, ['steamBuyPrice', 'bid', 'bid_price', 'buy_price'])
            
            # 计算价格位置百分比（新增功能）
            price_position_pct = self._get_price_position_pct(data, current_price)

            # 计算传统供需比率（保持向后兼容）
            supply_demand_ratio = self._calculate_supply_demand_ratio(supply_quantity, demand_quantity)

            # 计算基于经济学原理的供需分析
            # 从DataFrame中提取额外数据
            volume = latest_row.get('volume', 0) if isinstance(latest_row, dict) else 0
            circulation = str(self._estimate_total_circulation(supply_quantity, data.to_dict('records')))

            unified_supply_analysis = self.calculate_unified_supply_ratio(
                supply_quantity=supply_quantity,
                demand_quantity=demand_quantity,
                current_price=current_price,
                bid_price=bid_price,
                volume=volume,
                circulation=circulation,
                price_position_pct=price_position_pct  # 传递价格位置信息
            )

            spread_analysis = self._analyze_price_spread(current_price, bid_price)

            # DataFrame的趋势分析
            trend_analysis = self._analyze_dataframe_trend(data, source)

            return {
                'status': 'SUCCESS',
                'supply_quantity': supply_quantity,
                'demand_quantity': demand_quantity,
                'supply_demand_ratio': supply_demand_ratio,  # 保持向后兼容
                'unified_supply_analysis': unified_supply_analysis,  # 新增统一分析
                'current_price': current_price,
                'bid_price': bid_price,
                'price_position_pct': price_position_pct,  # 新增：价格位置百分比
                'spread_analysis': spread_analysis,
                'trend_analysis': trend_analysis,
                'data_points': len(data)
            }
            
        except Exception as e:
            return {
                'status': 'ERROR',
                'error': str(e)
            }
    
    def _analyze_dict_data(self, source: str, data: Dict) -> Dict:
        """分析字典格式数据"""
        try:
            # 根据实际数据结构提取字段
            supply_quantity = self._extract_value(data, ['steamSellCount', 'supply', 'supply_quantity', 'sell_orders'])
            demand_quantity = self._extract_value(data, ['steamBuyCount', 'demand', 'demand_quantity', 'buy_orders'])
            current_price = self._extract_value(data, ['steamPrice', 'price', 'current_price', 'last_price'])
            bid_price = self._extract_value(data, ['steamBuyPrice', 'bid', 'bid_price', 'buy_price'])
            
            # 计算价格位置百分比（新增功能）
            # 对于字典数据，通常只有单个数据点，无法计算历史位置
            price_position_pct = None

            # 计算传统供需比率（保持向后兼容）
            supply_demand_ratio = self._calculate_supply_demand_ratio(supply_quantity, demand_quantity)

            # 计算基于经济学原理的供需分析（新算法）
            # 从字典数据中提取额外字段
            volume = self._extract_value(data, ['volume']) or 0
            circulation = str(self._extract_value(data, ['circulation', 'total_supply']) or 10000)

            unified_supply_analysis = self.calculate_unified_supply_ratio(
                supply_quantity=supply_quantity,
                demand_quantity=demand_quantity,
                current_price=current_price,
                bid_price=bid_price,
                volume=volume,
                circulation=circulation,
                price_position_pct=price_position_pct  # 传递价格位置信息（可能为None）
            )

            spread_analysis = self._analyze_price_spread(current_price, bid_price)

            return {
                'status': 'SUCCESS',
                'supply_quantity': supply_quantity,
                'demand_quantity': demand_quantity,
                'supply_demand_ratio': supply_demand_ratio,  # 保持向后兼容
                'unified_supply_analysis': unified_supply_analysis,  # 新增统一分析
                'current_price': current_price,
                'bid_price': bid_price,
                'price_position_pct': price_position_pct,  # 新增：价格位置百分比
                'spread_analysis': spread_analysis,
                'data_points': 1
            }
            
        except Exception as e:
            return {
                'status': 'ERROR',
                'error': str(e)
            }
    
    def _extract_value(self, data: Any, possible_keys: List[str]) -> float:
        """从数据中提取值"""
        # 处理数组格式的走势数据 [timestamp, price, inventory_count, bid_price, bid_count, amount, volume, item_id]
        if isinstance(data, (list, tuple)) and len(data) >= 8:
            # 根据字段名映射到数组索引
            field_mapping = {
                'steamPrice': 1,      # price
                'price': 1,
                'current_price': 1,
                'last_price': 1,
                'steamSellCount': 2,  # inventory_count (在售数量)
                'supply': 2,
                'supply_quantity': 2,
                'sell_orders': 2,
                'steamBuyPrice': 3,   # bid_price
                'bid': 3,
                'bid_price': 3,
                'buy_price': 3,
                'steamBuyCount': 4,   # bid_count (求购数量)
                'demand': 4,
                'demand_quantity': 4,
                'buy_orders': 4,
                'amount': 5,          # amount
                'volume': 6,          # volume
                'circulation': 7,     # circulation (存世量)
                'total_supply': 7     # 存世量的别名
            }

            for key in possible_keys:
                if key in field_mapping:
                    try:
                        index = field_mapping[key]
                        if index < len(data):
                            value = data[index]
                            return float(value) if value is not None else 0.0
                    except (ValueError, TypeError, IndexError):
                        continue

        # 处理字典格式的数据
        elif isinstance(data, dict):
            for key in possible_keys:
                if key in data:
                    value = data[key]
                    return float(value) if value is not None else 0.0
        elif hasattr(data, '__getitem__'):
            # pandas Series等
            for key in possible_keys:
                try:
                    if hasattr(data, key):
                        value = getattr(data, key)
                        return float(value) if value is not None else 0.0
                    elif key in data:
                        value = data[key]
                        return float(value) if value is not None else 0.0
                except:
                    continue

        return 0.0
    
    def _calculate_supply_demand_ratio(self, supply: float, demand: float) -> float:
        """计算供需比率"""
        # 确保supply和demand都是有效的数值
        if supply is None or demand is None:
            return 1.0

        try:
            supply = float(supply)
            demand = float(demand)

            if demand > 0:
                return supply / demand
            elif supply > 0:
                return float('inf')
            else:
                return 1.0
        except (ValueError, TypeError):
            return 1.0

    def calculate_unified_supply_ratio(self, supply_quantity: int, demand_quantity: int = None,
                                     current_price: float = None, bid_price: float = None,
                                     volume: int = None, circulation: str = None,
                                     price_position_pct: float = None) -> Dict:
        """
        基于经济学原理的供需平衡分析

        新算法基于三个核心经济学指标：
        1. 供需比例 = 在售数量 / 求购数量 (供需平衡的直接衡量)
        2. 买卖价差率 = (当前价格 - 求购价) / 当前价格 × 100% (市场流动性指标)
        3. 流动性指标 = 成交量 / 存世量 × 100% (市场活跃度指标)

        经济学理论基础：
        - 供需比例反映市场基本面：比例高表示供过于求，比例低表示供不应求
        - 买卖价差反映市场流动性：价差大表示流动性差，价差小表示流动性好
        - 流动性指标反映市场活跃度：高流动性表示市场活跃，低流动性表示市场冷清

        Args:
            supply_quantity: 在售数量（市场供给）
            demand_quantity: 求购数量（市场需求）
            current_price: 当前价格
            bid_price: 求购价
            volume: 成交量
            circulation: 存世量
            price_position_pct: 价格位置百分比（0-100），用于价格位置感知分析

        Returns:
            Dict: 包含供需比例、价差分析、流动性分析等信息
        """
        try:
            # 数据有效性检查
            if supply_quantity is None or supply_quantity <= 0:
                return {
                    'status': 'ERROR',
                    'error': '在售数量数据无效',
                    'calculation_method': 'economic_indicators'
                }

            # 1. 分析挂单情况（市场参与度指标）
            order_analysis = self._calculate_supply_demand_balance(supply_quantity, demand_quantity, price_position_pct)

            # 2. 分析买卖价差（市场效率指标）
            spread_analysis = self._calculate_bid_ask_spread(current_price, bid_price)

            # 3. 分析流动性状况（市场活跃度指标）
            liquidity_analysis = self._calculate_liquidity_ratio(volume, circulation)

            # 4. 综合评估市场状态
            market_assessment = self._assess_overall_market_condition(
                order_analysis, spread_analysis, liquidity_analysis
            )

            # 5. 生成经济学解释和建议
            economic_interpretation = self._generate_economic_interpretation(
                order_analysis, spread_analysis, liquidity_analysis, market_assessment
            )

            return {
                'status': 'SUCCESS',
                'order_analysis': order_analysis,           # 挂单分析（替代原来的supply_demand_analysis）
                'spread_analysis': spread_analysis,         # 价差分析
                'liquidity_analysis': liquidity_analysis,   # 流动性分析
                'market_assessment': market_assessment,     # 市场评估
                'economic_interpretation': economic_interpretation,  # 经济学解释
                'calculation_method': 'cs2_market_mechanics',  # 更新计算方法标识
                'input_data': {
                    'sell_orders_quantity': supply_quantity,    # 在售挂单数量
                    'buy_orders_quantity': demand_quantity,     # 求购挂单数量
                    'lowest_sell_price': current_price,        # 最低在售价
                    'highest_buy_price': bid_price,            # 最高求购价
                    'daily_volume': volume,                     # 日成交量
                    'total_circulation': circulation            # 总流通量
                }
            }

        except Exception as e:
            self.logger.error(f"供需分析计算失败: {e}")
            return {
                'status': 'ERROR',
                'error': f'计算失败: {str(e)}',
                'calculation_method': 'economic_indicators'
            }

    def _calculate_supply_demand_balance(self, supply_quantity: int, demand_quantity: int = None,
                                       price_position_pct: float = None) -> Dict:
        """
        重新设计的供需平衡分析

        基于CS2饰品市场的真实机制：
        - supply_quantity: 愿意以在售价（或更高）出售的数量
        - demand_quantity: 愿意以求购价（或更低）购买的数量
        - price_position_pct: 当前价格在历史区间中的位置百分比（0-100）
        - 这两者反映的是买卖双方的挂单意愿强度，不是简单的供需关系

        分析维度：
        1. 挂单比例：反映买卖双方的积极性对比
        2. 市场深度：挂单数量的绝对值大小
        3. 竞争激烈程度：基于挂单数量判断市场活跃度
        4. 价格位置感知：根据价格位置调整挂单行为的解读
        """
        try:
            if demand_quantity is None or demand_quantity <= 0:
                return {
                    'order_ratio': None,
                    'market_sentiment': 'UNKNOWN',
                    'description': '求购数量数据缺失，无法分析挂单情况',
                    'trading_pressure': 'NEUTRAL',
                    'market_depth': 'UNKNOWN'
                }

            # 计算挂单比例（在售挂单 vs 求购挂单）
            order_ratio = supply_quantity / demand_quantity

            # 计算市场深度（总挂单数量）
            total_orders = supply_quantity + demand_quantity

            # 分析市场情绪和交易压力
            if order_ratio >= 4.0:
                market_sentiment = 'SELLER_DOMINATED'
                trading_pressure = 'STRONG_SELL_PRESSURE'
                description = f'卖方主导市场 (挂单比例: {order_ratio:.2f})，大量卖家等待出货，价格可能向求购价靠拢'
            elif order_ratio >= 2.5:
                market_sentiment = 'SELLER_ADVANTAGE'
                trading_pressure = 'SELL_PRESSURE'
                description = f'卖方占优势 (挂单比例: {order_ratio:.2f})，卖家较多，价格有下行压力'
            elif order_ratio >= 1.5:
                market_sentiment = 'SLIGHT_SELLER_ADVANTAGE'
                trading_pressure = 'WEAK_SELL_PRESSURE'
                description = f'卖方略占优势 (挂单比例: {order_ratio:.2f})，市场偏向卖方'
            elif order_ratio >= 0.8:
                market_sentiment = 'BALANCED'
                trading_pressure = 'NEUTRAL'
                description = f'买卖平衡 (挂单比例: {order_ratio:.2f})，买卖双方挂单数量相当，价格相对稳定'
            elif order_ratio >= 0.5:
                market_sentiment = 'SLIGHT_BUYER_ADVANTAGE'
                trading_pressure = 'WEAK_BUY_PRESSURE'
                description = f'买方略占优势 (挂单比例: {order_ratio:.2f})，市场偏向买方'
            elif order_ratio >= 0.25:
                market_sentiment = 'BUYER_ADVANTAGE'
                trading_pressure = 'BUY_PRESSURE'
                description = f'买方占优势 (挂单比例: {order_ratio:.2f})，买家较多，价格有上行压力'
            else:
                market_sentiment = 'BUYER_DOMINATED'
                trading_pressure = 'STRONG_BUY_PRESSURE'
                description = f'买方主导市场 (挂单比例: {order_ratio:.2f})，大量买家等待收货，价格可能向在售价靠拢'

            # 判断市场深度
            if total_orders >= 500:
                market_depth = 'VERY_DEEP'
                depth_desc = '市场深度很深，流动性充足'
            elif total_orders >= 200:
                market_depth = 'DEEP'
                depth_desc = '市场深度较深，流动性良好'
            elif total_orders >= 100:
                market_depth = 'MODERATE'
                depth_desc = '市场深度中等，流动性一般'
            elif total_orders >= 50:
                market_depth = 'SHALLOW'
                depth_desc = '市场深度较浅，流动性有限'
            else:
                market_depth = 'VERY_SHALLOW'
                depth_desc = '市场深度很浅，流动性不足'

            return {
                'order_ratio': round(order_ratio, 3),
                'market_sentiment': market_sentiment,
                'trading_pressure': trading_pressure,
                'market_depth': market_depth,
                'total_orders': total_orders,
                'description': description,
                'depth_description': depth_desc,
                'supply_quantity': supply_quantity,
                'demand_quantity': demand_quantity
            }

        except Exception as e:
            return {
                'order_ratio': None,
                'market_sentiment': 'ERROR',
                'description': f'挂单分析计算失败: {str(e)}',
                'trading_pressure': 'NEUTRAL',
                'market_depth': 'UNKNOWN'
            }

    def _calculate_average_daily_volume(self, historical_data: List) -> float:
        """
        从历史数据计算平均日成交量

        Args:
            historical_data: 历史数据列表，每个元素应包含成交量信息

        Returns:
            float: 平均日成交量
        """
        try:
            if not historical_data:
                return 10.0  # 默认值

            # 提取成交量数据
            volumes = []
            for item in historical_data:
                if isinstance(item, (list, tuple)) and len(item) >= 7:
                    # 数据格式: [时间戳, 价格, 在售量, 求购价, 求购量, 成交额, 成交量, 存世量]
                    volume = item[6]  # 索引6是日成交量
                    if volume is not None and volume > 0:
                        volumes.append(float(volume))
                elif isinstance(item, dict):
                    # 字典格式数据
                    volume = self._extract_value(item, ['volume', 'daily_volume', 'steamVolume', 'trade_volume'])
                    if volume is not None and volume > 0:
                        volumes.append(float(volume))

            if volumes:
                avg_volume = sum(volumes) / len(volumes)
                self.logger.info(f"从{len(volumes)}个有效数据点计算平均日成交量: {avg_volume:.1f}")
                return avg_volume
            else:
                self.logger.warning("历史数据中未找到有效成交量，使用默认值")
                return 10.0

        except Exception as e:
            self.logger.error(f"计算平均日成交量失败: {e}")
            return 10.0

    def _estimate_total_circulation(self, supply_quantity: int, historical_data: List = None) -> int:
        """
        估算总流通量

        Args:
            supply_quantity: 当前在售数量
            historical_data: 历史数据（可选）

        Returns:
            int: 估算的总流通量
        """
        try:
            if historical_data and len(historical_data) > 0:
                # 如果有历史数据，分析历史最大在售数量
                max_supply = supply_quantity
                for item in historical_data:
                    if isinstance(item, (list, tuple)) and len(item) >= 3:
                        hist_supply = self._extract_value(item, ['steamSellCount', 'supply', 'supply_quantity'])
                        if hist_supply and hist_supply > max_supply:
                            max_supply = hist_supply
                    elif isinstance(item, dict):
                        hist_supply = self._extract_value(item, ['steamSellCount', 'supply', 'supply_quantity'])
                        if hist_supply and hist_supply > max_supply:
                            max_supply = hist_supply

                # 基于历史最大在售数量估算总流通量
                # 经验公式：总流通量 = 历史最大在售数量 * 15-30倍
                estimated_circulation = max_supply * 20
            else:
                # 没有历史数据时的保守估算
                # 基于当前在售数量的经验倍数
                if supply_quantity < 50:
                    # 稀有饰品，流通量相对较小
                    estimated_circulation = supply_quantity * 30
                elif supply_quantity < 200:
                    # 中等稀有度
                    estimated_circulation = supply_quantity * 25
                else:
                    # 常见饰品
                    estimated_circulation = supply_quantity * 20

            # 确保最小值，避免不合理的估算
            min_circulation = max(supply_quantity * 10, 1000)
            return max(estimated_circulation, min_circulation)

        except Exception as e:
            self.logger.error(f"估算总流通量失败: {e}")
            # 返回保守估算值
            return max(supply_quantity * 25, 1000)
    
    def _analyze_price_spread(self, current_price: float, bid_price: float) -> Dict:
        """分析价差"""
        # 检查价格数据的有效性
        if current_price is None or bid_price is None:
            return {
                'spread_absolute': 0,
                'spread_percentage': 0,
                'liquidity_level': 'UNKNOWN'
            }

        try:
            current_price = float(current_price)
            bid_price = float(bid_price)

            if current_price <= 0 or bid_price <= 0:
                return {
                    'spread_absolute': 0,
                    'spread_percentage': 0,
                    'liquidity_level': 'UNKNOWN'
                }
        except (ValueError, TypeError):
            return {
                'spread_absolute': 0,
                'spread_percentage': 0,
                'liquidity_level': 'UNKNOWN'
            }
        
        spread_absolute = abs(current_price - bid_price)
        spread_percentage = (spread_absolute / current_price) * 100
        
        # 流动性等级判断
        if spread_percentage < 2:
            liquidity_level = 'HIGH'
        elif spread_percentage < 5:
            liquidity_level = 'MEDIUM'
        else:
            liquidity_level = 'LOW'
        
        return {
            'spread_absolute': spread_absolute,
            'spread_percentage': spread_percentage,
            'liquidity_level': liquidity_level
        }
    
    def _analyze_trend(self, data: List, source: str) -> Dict:
        """分析列表数据的趋势"""
        if len(data) < 2:
            return {'trend': 'INSUFFICIENT_DATA'}
        
        try:
            # 提取价格序列
            prices = []
            for item in data[-10:]:  # 最近10个数据点
                price = self._extract_value(item, ['steamPrice', 'price', 'current_price', 'last_price'])
                if price is not None:
                    try:
                        price_float = float(price)
                        if price_float > 0:
                            prices.append(price_float)
                    except (ValueError, TypeError):
                        continue
            
            if len(prices) < 2:
                return {'trend': 'NO_PRICE_DATA'}
            
            # 简单趋势判断
            if prices[-1] > prices[0]:
                trend = 'INCREASING'
            elif prices[-1] < prices[0]:
                trend = 'DECREASING'
            else:
                trend = 'STABLE'
            
            # 计算变化率
            change_rate = ((prices[-1] - prices[0]) / prices[0]) * 100 if prices[0] > 0 else 0
            
            return {
                'trend': trend,
                'change_rate': change_rate,
                'data_points_analyzed': len(prices)
            }
            
        except Exception as e:
            return {
                'trend': 'ERROR',
                'error': str(e)
            }
    
    def _analyze_dataframe_trend(self, data: pd.DataFrame, source: str) -> Dict:
        """分析DataFrame数据的趋势"""
        try:
            price_columns = ['price', 'current_price', 'last_price', 'close']
            price_column = None
            
            for col in price_columns:
                if col in data.columns:
                    price_column = col
                    break
            
            if price_column is None:
                return {'trend': 'NO_PRICE_COLUMN'}
            
            prices = data[price_column].dropna()
            if len(prices) < 2:
                return {'trend': 'INSUFFICIENT_DATA'}
            
            # 趋势分析
            recent_prices = prices.tail(10)
            if recent_prices.iloc[-1] > recent_prices.iloc[0]:
                trend = 'INCREASING'
            elif recent_prices.iloc[-1] < recent_prices.iloc[0]:
                trend = 'DECREASING'
            else:
                trend = 'STABLE'
            
            change_rate = ((recent_prices.iloc[-1] - recent_prices.iloc[0]) / recent_prices.iloc[0]) * 100
            
            return {
                'trend': trend,
                'change_rate': change_rate,
                'data_points_analyzed': len(recent_prices)
            }
            
        except Exception as e:
            return {
                'trend': 'ERROR',
                'error': str(e)
            }

    def _combine_analyses(self, source_analyses: Dict) -> Dict:
        """综合多个数据源的分析结果"""
        try:
            valid_analyses = []

            for source, analysis in source_analyses.items():
                if analysis.get('status') == 'SUCCESS':
                    valid_analyses.append(analysis)

            if not valid_analyses:
                return {
                    'status': 'NO_VALID_DATA',
                    'combined_supply_demand_status': 'UNKNOWN'
                }

            # 计算平均值
            avg_supply_demand_ratio = np.mean([a['supply_demand_ratio'] for a in valid_analyses
                                             if a['supply_demand_ratio'] != float('inf')])

            avg_spread_percentage = np.mean([a['spread_analysis']['spread_percentage'] for a in valid_analyses
                                           if 'spread_analysis' in a])

            # 供需状态投票
            status_votes = {}
            for analysis in valid_analyses:
                ratio = analysis['supply_demand_ratio']
                if ratio > 1.5:
                    status = 'SUPPLY_EXCESS'
                elif ratio < 0.67:
                    status = 'DEMAND_EXCESS'
                else:
                    status = 'BALANCED'

                status_votes[status] = status_votes.get(status, 0) + 1

            # 确定最终状态
            if status_votes:
                combined_status = max(status_votes.items(), key=lambda x: x[1])[0]
                confidence = max(status_votes.values()) / len(valid_analyses) * 100
            else:
                combined_status = 'UNKNOWN'
                confidence = 0

            # 流动性等级投票
            liquidity_votes = {}
            for analysis in valid_analyses:
                if 'spread_analysis' in analysis:
                    liquidity = analysis['spread_analysis']['liquidity_level']
                    liquidity_votes[liquidity] = liquidity_votes.get(liquidity, 0) + 1

            combined_liquidity = max(liquidity_votes.items(), key=lambda x: x[1])[0] if liquidity_votes else 'UNKNOWN'

            return {
                'status': 'SUCCESS',
                'combined_supply_demand_status': combined_status,
                'combined_supply_demand_ratio': avg_supply_demand_ratio,
                'combined_spread_percentage': avg_spread_percentage,
                'combined_liquidity_level': combined_liquidity,
                'confidence': confidence,
                'data_sources_count': len(valid_analyses),
                'status_distribution': status_votes,
                'liquidity_distribution': liquidity_votes
            }

        except Exception as e:
            self.logger.error(f"综合分析失败: {e}")
            return {
                'status': 'ERROR',
                'error': str(e)
            }

    def _detect_anomalies(self, source_analyses: Dict) -> Dict:
        """检测异常情况"""
        try:
            anomalies = {
                'price_anomaly': False,
                'volume_anomaly': False,
                'supply_anomaly': False,
                'demand_anomaly': False,
                'anomaly_details': []
            }

            valid_analyses = [a for a in source_analyses.values() if a.get('status') == 'SUCCESS']

            if not valid_analyses:
                return anomalies

            # 价格异常检测
            prices = [a['current_price'] for a in valid_analyses
                     if a.get('current_price') is not None and a['current_price'] > 0]
            if len(prices) > 1:
                price_std = np.std(prices)
                price_mean = np.mean(prices)

                for price in prices:
                    if abs(price - price_mean) > 2 * price_std:
                        anomalies['price_anomaly'] = True
                        anomalies['anomaly_details'].append(f"价格异常: {price:.2f} (均值: {price_mean:.2f})")

            # 供需比率异常检测
            ratios = [a['supply_demand_ratio'] for a in valid_analyses
                     if a['supply_demand_ratio'] != float('inf')]

            if ratios:
                for ratio in ratios:
                    if ratio > 10:  # 供应严重过剩
                        anomalies['supply_anomaly'] = True
                        anomalies['anomaly_details'].append(f"供应严重过剩: 比率 {ratio:.2f}")
                    elif ratio < 0.1:  # 需求严重过剩
                        anomalies['demand_anomaly'] = True
                        anomalies['anomaly_details'].append(f"需求严重过剩: 比率 {ratio:.2f}")

            # 价差异常检测
            spreads = [a['spread_analysis']['spread_percentage'] for a in valid_analyses
                      if 'spread_analysis' in a]

            if spreads:
                for spread in spreads:
                    if spread > 20:  # 价差过大
                        anomalies['volume_anomaly'] = True
                        anomalies['anomaly_details'].append(f"价差异常: {spread:.2f}%")

            return anomalies

        except Exception as e:
            self.logger.error(f"异常检测失败: {e}")
            return {
                'price_anomaly': False,
                'volume_anomaly': False,
                'supply_anomaly': False,
                'demand_anomaly': False,
                'error': str(e)
            }

    def _calculate_market_balance_score(self, combined_analysis: Dict) -> float:
        """计算市场平衡评分 (0-100)"""
        try:
            if combined_analysis.get('status') != 'SUCCESS':
                return 0.0

            ratio = combined_analysis.get('combined_supply_demand_ratio', 1.0)
            confidence = combined_analysis.get('confidence', 0)

            # 基于供需比率的平衡评分
            if ratio == 1.0:
                balance_score = 100.0
            elif ratio > 1.0:
                # 供应过剩，比率越大评分越低
                balance_score = max(0.0, 100.0 - (ratio - 1.0) * 30)
            else:
                # 需求过剩，比率越小评分越低
                balance_score = max(0.0, 100.0 - (1.0 - ratio) * 30)

            # 根据置信度调整评分
            confidence_factor = confidence / 100.0
            adjusted_score = balance_score * confidence_factor

            return min(100.0, max(0.0, adjusted_score))

        except Exception as e:
            self.logger.error(f"市场平衡评分计算失败: {e}")
            return 0.0

    def get_supply_demand_signals(self) -> Dict:
        """获取供需信号"""
        try:
            analysis = self.analyze_comprehensive()

            if 'combined_analysis' not in analysis:
                return {
                    'signal': 'NO_DATA',
                    'strength': 0,
                    'confidence': 0
                }

            combined = analysis['combined_analysis']
            status = combined.get('combined_supply_demand_status', 'UNKNOWN')
            confidence = combined.get('confidence', 0)
            balance_score = analysis.get('market_balance_score', 0)

            # 信号强度计算
            if status == 'SUPPLY_EXCESS':
                signal = 'BEARISH'
                strength = min(100, (combined.get('combined_supply_demand_ratio', 1) - 1) * 50)
            elif status == 'DEMAND_EXCESS':
                signal = 'BULLISH'
                strength = min(100, (1 - combined.get('combined_supply_demand_ratio', 1)) * 50)
            elif status == 'BALANCED':
                signal = 'NEUTRAL'
                strength = balance_score
            else:
                signal = 'UNKNOWN'
                strength = 0

            return {
                'signal': signal,
                'strength': strength,
                'confidence': confidence,
                'balance_score': balance_score,
                'liquidity_level': combined.get('combined_liquidity_level', 'UNKNOWN')
            }

        except Exception as e:
            self.logger.error(f"获取供需信号失败: {e}")
            return {
                'signal': 'ERROR',
                'strength': 0,
                'confidence': 0,
                'error': str(e)
            }

    def analyze_liquidity_status(self) -> Dict[str, Any]:
        """
        分析流动性状况 - 新增方法

        Returns:
            Dict包含市场流动性的具体评估
        """
        try:
            if not self.supply_demand_data:
                return {
                    'liquidity_status': '无法评估',
                    'liquidity_description': '缺少供需数据，无法评估市场流动性',
                    'liquidity_reasoning': '流动性分析需要供需数据和交易量信息',
                    'error': '缺少供需数据'
                }

            self.logger.info("开始流动性状况分析...")

            # 分析各个数据源的流动性
            source_liquidity = {}
            for source, data in self.supply_demand_data.items():
                source_liquidity[source] = self._analyze_source_liquidity(source, data)

            # 生成综合流动性分析
            liquidity_analysis = self._generate_liquidity_analysis(source_liquidity)

            result = {
                'liquidity_status': liquidity_analysis['status'],
                'liquidity_description': liquidity_analysis['description'],
                'liquidity_reasoning': liquidity_analysis['reasoning'],
                'liquidity_factors': liquidity_analysis['factors'],
                'source_liquidity': source_liquidity,
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"流动性状况分析完成: {liquidity_analysis['status']}")
            return result

        except Exception as e:
            self.logger.error(f"流动性状况分析失败: {e}")
            return {
                'liquidity_status': '分析异常',
                'liquidity_description': f'流动性状况分析过程中发生异常: {str(e)}',
                'liquidity_reasoning': '请检查数据质量或联系技术支持',
                'error': str(e)
            }

    def analyze_price_support_factors(self) -> Dict[str, Any]:
        """
        分析价格支撑因素 - 新增方法

        Returns:
            Dict包含影响价格的供需因素分析
        """
        try:
            if not self.supply_demand_data:
                return {
                    'support_factors_status': '无法分析',
                    'support_factors_description': '缺少供需数据，无法分析价格支撑因素',
                    'support_factors_reasoning': '价格支撑因素分析需要供需数据和价格数据',
                    'error': '缺少供需数据'
                }

            self.logger.info("开始价格支撑因素分析...")

            # 分析各个数据源的价格支撑因素
            source_factors = {}
            for source, data in self.supply_demand_data.items():
                source_factors[source] = self._analyze_source_price_factors(source, data)

            # 生成综合价格支撑因素分析
            factors_analysis = self._generate_price_support_factors_analysis(source_factors)

            result = {
                'support_factors_status': factors_analysis['status'],
                'support_factors_description': factors_analysis['description'],
                'support_factors_reasoning': factors_analysis['reasoning'],
                'key_support_factors': factors_analysis['key_factors'],
                'risk_factors': factors_analysis['risk_factors'],
                'source_factors': source_factors,
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"价格支撑因素分析完成: {factors_analysis['status']}")
            return result

        except Exception as e:
            self.logger.error(f"价格支撑因素分析失败: {e}")
            return {
                'support_factors_status': '分析异常',
                'support_factors_description': f'价格支撑因素分析过程中发生异常: {str(e)}',
                'support_factors_reasoning': '请检查数据质量或联系技术支持',
                'error': str(e)
            }

    def detect_supply_demand_anomalies(self) -> Dict[str, Any]:
        """
        检测供需关系异常 - 新增方法

        Returns:
            Dict包含供需关系的异常变化提醒
        """
        try:
            if not self.supply_demand_data:
                return {
                    'anomaly_level': '无法检测',
                    'anomaly_description': '缺少供需数据，无法检测异常情况',
                    'anomaly_alerts': [],
                    'error': '缺少供需数据'
                }

            self.logger.info("开始供需关系异常检测...")

            anomaly_alerts = []

            # 检测各个数据源的异常
            for source, data in self.supply_demand_data.items():
                source_anomalies = self._detect_source_anomalies(source, data)
                anomaly_alerts.extend(source_anomalies)

            # 生成综合异常评估
            anomaly_assessment = self._generate_anomaly_assessment(anomaly_alerts)

            result = {
                'anomaly_level': anomaly_assessment['level'],
                'anomaly_description': anomaly_assessment['description'],
                'anomaly_alerts': anomaly_alerts,
                'alert_count': len(anomaly_alerts),
                'recommended_action': anomaly_assessment['recommended_action'],
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"供需关系异常检测完成: {anomaly_assessment['level']}, {len(anomaly_alerts)}个异常")
            return result

        except Exception as e:
            self.logger.error(f"供需关系异常检测失败: {e}")
            return {
                'anomaly_level': '检测异常',
                'anomaly_description': f'供需关系异常检测过程中发生异常: {str(e)}',
                'anomaly_alerts': [],
                'error': str(e)
            }

    def execute_comprehensive_supply_demand_analysis(self) -> Dict[str, Any]:
        """
        执行完整的供需分析 - 主要接口方法

        Returns:
            Dict包含符合需求文档要求的完整供需分析结果
        """
        try:
            self.logger.info("开始执行完整的供需分析...")

            # 执行各项分析
            status_analysis = self.analyze_supply_demand_status()
            trends_analysis = self.analyze_supply_demand_trends()
            liquidity_analysis = self.analyze_liquidity_status()
            factors_analysis = self.analyze_price_support_factors()
            anomalies_analysis = self.detect_supply_demand_anomalies()

            # 整合分析结果，符合需求文档格式
            comprehensive_result = {
                # 供需关系状态：当前供需关系的具体描述
                'supply_demand_relationship_status': status_analysis.get('supply_demand_status', '数据不足'),
                'relationship_description': status_analysis.get('status_description', ''),
                'relationship_reasoning': status_analysis.get('status_reasoning', ''),

                # 供需变化趋势：供需关系的变化方向和程度
                'supply_demand_trend_direction': trends_analysis.get('trend_direction', '无法确定'),
                'trend_description': trends_analysis.get('trend_description', ''),
                'trend_reasoning': trends_analysis.get('trend_reasoning', ''),
                'trend_strength': trends_analysis.get('trend_strength', ''),

                # 流动性状况：市场流动性的具体评估
                'market_liquidity_status': liquidity_analysis.get('liquidity_status', '无法评估'),
                'liquidity_description': liquidity_analysis.get('liquidity_description', ''),
                'liquidity_reasoning': liquidity_analysis.get('liquidity_reasoning', ''),
                'liquidity_factors': liquidity_analysis.get('liquidity_factors', ''),

                # 价格支撑因素：影响价格的供需因素分析
                'price_support_factors_status': factors_analysis.get('support_factors_status', '无法分析'),
                'support_factors_description': factors_analysis.get('support_factors_description', ''),
                'support_factors_reasoning': factors_analysis.get('support_factors_reasoning', ''),
                'key_support_factors': factors_analysis.get('key_support_factors', []),
                'risk_factors': factors_analysis.get('risk_factors', []),

                # 异常情况提醒：供需关系的异常变化提醒
                'supply_demand_anomaly_level': anomalies_analysis.get('anomaly_level', '无异常'),
                'anomaly_description': anomalies_analysis.get('anomaly_description', ''),
                'anomaly_alerts': anomalies_analysis.get('anomaly_alerts', []),
                'alert_count': anomalies_analysis.get('alert_count', 0),
                'recommended_action': anomalies_analysis.get('recommended_action', ''),

                # 元数据
                'data_sources': list(self.supply_demand_data.keys()),
                'analysis_timestamp': datetime.now(),
                'data_quality': 'GOOD' if self.supply_demand_data else 'INSUFFICIENT',
                'analysis_success': True
            }

            self.logger.info("完整供需分析执行成功")
            return comprehensive_result

        except Exception as e:
            self.logger.error(f"完整供需分析执行失败: {e}")
            return {
                'supply_demand_relationship_status': '分析异常',
                'relationship_description': f'供需分析过程中发生异常: {str(e)}',
                'relationship_reasoning': '请检查数据质量或联系技术支持',
                'supply_demand_trend_direction': '无法确定',
                'trend_description': '供需趋势分析异常',
                'trend_reasoning': '请检查数据质量',
                'market_liquidity_status': '无法评估',
                'liquidity_description': '流动性分析异常',
                'liquidity_reasoning': '请检查数据质量',
                'price_support_factors_status': '无法分析',
                'support_factors_description': '价格支撑因素分析异常',
                'support_factors_reasoning': '请检查数据质量',
                'supply_demand_anomaly_level': '系统异常',
                'anomaly_description': '供需分析系统发生异常，请立即检查',
                'anomaly_alerts': [{'type': 'SYSTEM_ERROR', 'message': str(e)}],
                'recommended_action': '停止交易，联系技术支持',
                'analysis_timestamp': datetime.now(),
                'data_quality': 'ERROR',
                'analysis_success': False,
                'error': str(e)
            }

    def _analyze_source_supply_demand_status(self, source: str, data: Any) -> str:
        """分析单个数据源的供需状态"""
        try:
            if isinstance(data, list) and data:
                latest_item = data[-1]
                supply = self._extract_value(latest_item, ['steamSellCount', 'supply', 'supply_quantity', 'sell_orders'])
                demand = self._extract_value(latest_item, ['steamBuyCount', 'demand', 'demand_quantity', 'buy_orders'])

                # 确保supply和demand都是有效的数值
                if supply is not None and demand is not None and isinstance(supply, (int, float)) and isinstance(demand, (int, float)):
                    try:
                        if supply > demand * 2:
                            return f'{source}数据显示供应过剩，供应量({supply})远超需求量({demand})'
                        elif demand > supply * 2:
                            return f'{source}数据显示需求旺盛，需求量({demand})远超供应量({supply})'
                        elif supply > demand:
                            return f'{source}数据显示供应略大于需求，供应量({supply})，需求量({demand})'
                        elif demand > supply:
                            return f'{source}数据显示需求略大于供应，需求量({demand})，供应量({supply})'
                        else:
                            return f'{source}数据显示供需基本平衡，供应量({supply})，需求量({demand})'
                    except (TypeError, ValueError) as e:
                        return f'{source}数据类型错误，无法进行数值比较: {e}'
                else:
                    return f'{source}数据不完整，无法准确评估供需状态'
            else:
                return f'{source}数据格式不支持或为空'

        except Exception:
            return f'{source}数据分析异常'

    def _generate_supply_demand_status_analysis(self, source_statuses: Dict[str, str]) -> Dict[str, str]:
        """生成综合供需状态分析"""
        try:
            if not source_statuses:
                return {
                    'status': '数据不足',
                    'description': '缺少有效的供需数据源',
                    'reasoning': '需要至少一个有效的数据源来分析供需状态'
                }

            # 分析各数据源的状态
            supply_excess_count = sum(1 for status in source_statuses.values() if '供应过剩' in status or '供应略大于需求' in status)
            demand_excess_count = sum(1 for status in source_statuses.values() if '需求旺盛' in status or '需求略大于供应' in status)
            balanced_count = sum(1 for status in source_statuses.values() if '基本平衡' in status)

            total_sources = len(source_statuses)

            if supply_excess_count > total_sources / 2:
                status = '供应过剩'
                description = f'根据{total_sources}个数据源分析，{supply_excess_count}个数据源显示供应过剩，市场供应量充足，可能对价格形成下行压力。'
                reasoning = '多数数据源确认供应过剩状态，买方市场特征明显，价格上涨动力不足。'
            elif demand_excess_count > total_sources / 2:
                status = '需求旺盛'
                description = f'根据{total_sources}个数据源分析，{demand_excess_count}个数据源显示需求旺盛，市场需求强劲，对价格形成上行支撑。'
                reasoning = '多数数据源确认需求旺盛状态，卖方市场特征明显，价格具备上涨基础。'
            elif balanced_count > total_sources / 2:
                status = '供需平衡'
                description = f'根据{total_sources}个数据源分析，{balanced_count}个数据源显示供需基本平衡，市场处于相对均衡状态。'
                reasoning = '供需关系相对平衡，价格波动主要受其他因素影响，市场稳定性较好。'
            else:
                status = '供需混合'
                description = f'根据{total_sources}个数据源分析，各数据源显示不同的供需状态，市场信号混合。'
                reasoning = '不同数据源显示不同的供需状态，可能存在结构性差异或数据质量问题。'

            return {
                'status': status,
                'description': description,
                'reasoning': reasoning
            }

        except Exception:
            return {
                'status': '分析异常',
                'description': '供需状态分析过程中发生异常',
                'reasoning': '请检查数据质量或联系技术支持'
            }

    def _analyze_source_trend(self, source: str, data: Any) -> str:
        """分析单个数据源的趋势"""
        try:
            if isinstance(data, list) and len(data) >= 2:
                # 比较最近两个数据点
                latest = data[-1]
                previous = data[-2]

                latest_supply = self._extract_value(latest, ['steamSellCount', 'supply', 'supply_quantity', 'sell_orders'])
                latest_demand = self._extract_value(latest, ['steamBuyCount', 'demand', 'demand_quantity', 'buy_orders'])
                previous_supply = self._extract_value(previous, ['steamSellCount', 'supply', 'supply_quantity', 'sell_orders'])
                previous_demand = self._extract_value(previous, ['steamBuyCount', 'demand', 'demand_quantity', 'buy_orders'])

                if all(v is not None for v in [latest_supply, latest_demand, previous_supply, previous_demand]):
                    supply_change = latest_supply - previous_supply
                    demand_change = latest_demand - previous_demand

                    if supply_change > 0 and demand_change > 0:
                        return f'{source}显示供需双增，供应增加{supply_change}，需求增加{demand_change}'
                    elif supply_change > 0 and demand_change < 0:
                        return f'{source}显示供增需减，供应增加{supply_change}，需求减少{abs(demand_change)}'
                    elif supply_change < 0 and demand_change > 0:
                        return f'{source}显示供减需增，供应减少{abs(supply_change)}，需求增加{demand_change}'
                    elif supply_change < 0 and demand_change < 0:
                        return f'{source}显示供需双减，供应减少{abs(supply_change)}，需求减少{abs(demand_change)}'
                    else:
                        return f'{source}显示供需变化不明显'
                else:
                    return f'{source}数据不完整，无法分析趋势'
            else:
                return f'{source}历史数据不足，无法分析趋势'

        except Exception:
            return f'{source}趋势分析异常'

    def _generate_supply_demand_trend_analysis(self, source_trends: Dict[str, str]) -> Dict[str, str]:
        """生成综合供需趋势分析"""
        try:
            if not source_trends:
                return {
                    'direction': '无法确定',
                    'description': '缺少有效的趋势数据',
                    'reasoning': '需要历史数据来分析趋势变化',
                    'strength': '无法评估'
                }

            # 分析趋势方向
            supply_increase_count = sum(1 for trend in source_trends.values() if '供应增加' in trend or '供增' in trend)
            supply_decrease_count = sum(1 for trend in source_trends.values() if '供应减少' in trend or '供减' in trend)
            demand_increase_count = sum(1 for trend in source_trends.values() if '需求增加' in trend or '需增' in trend)
            demand_decrease_count = sum(1 for trend in source_trends.values() if '需求减少' in trend or '需减' in trend)

            total_sources = len(source_trends)

            # 确定主要趋势方向
            if supply_increase_count > total_sources / 2 and demand_decrease_count > total_sources / 2:
                direction = '供增需减'
                description = f'多数数据源显示供应增加、需求减少的趋势，市场向买方市场转变。'
                reasoning = '供应增加而需求减少，可能对价格形成下行压力，建议关注价格支撑位。'
                strength = '强'
            elif supply_decrease_count > total_sources / 2 and demand_increase_count > total_sources / 2:
                direction = '供减需增'
                description = f'多数数据源显示供应减少、需求增加的趋势，市场向卖方市场转变。'
                reasoning = '供应减少而需求增加，对价格形成上行支撑，有利于价格上涨。'
                strength = '强'
            elif supply_increase_count > total_sources / 2:
                direction = '供应增加'
                description = f'多数数据源显示供应增加趋势，市场供应量提升。'
                reasoning = '供应增加可能缓解供需紧张，但需要观察需求变化。'
                strength = '中'
            elif demand_increase_count > total_sources / 2:
                direction = '需求增加'
                description = f'多数数据源显示需求增加趋势，市场需求活跃。'
                reasoning = '需求增加显示市场活跃度提升，对价格形成正面支撑。'
                strength = '中'
            else:
                direction = '趋势混合'
                description = f'各数据源显示不同的趋势方向，市场变化方向不明确。'
                reasoning = '趋势信号混合，建议密切关注后续数据变化。'
                strength = '弱'

            return {
                'direction': direction,
                'description': description,
                'reasoning': reasoning,
                'strength': strength
            }

        except Exception:
            return {
                'direction': '分析异常',
                'description': '供需趋势分析过程中发生异常',
                'reasoning': '请检查数据质量或联系技术支持',
                'strength': '无法评估'
            }

    def _analyze_source_liquidity(self, source: str, data: Any) -> str:
        """分析单个数据源的流动性"""
        try:
            if isinstance(data, list) and data:
                # 计算供需总量作为流动性指标
                latest_item = data[-1]
                supply = self._extract_value(latest_item, ['steamSellCount', 'supply', 'supply_quantity', 'sell_orders'])
                demand = self._extract_value(latest_item, ['steamBuyCount', 'demand', 'demand_quantity', 'buy_orders'])

                if supply is not None and demand is not None:
                    total_liquidity = supply + demand
                    if total_liquidity > 300:
                        return f'{source}显示高流动性，总供需量{total_liquidity}'
                    elif total_liquidity > 150:
                        return f'{source}显示中等流动性，总供需量{total_liquidity}'
                    else:
                        return f'{source}显示低流动性，总供需量{total_liquidity}'
                else:
                    return f'{source}流动性数据不完整'
            else:
                return f'{source}无流动性数据'

        except Exception:
            return f'{source}流动性分析异常'

    def _generate_liquidity_analysis(self, source_liquidity: Dict[str, str]) -> Dict[str, str]:
        """生成流动性综合分析"""
        try:
            if not source_liquidity:
                return {
                    'status': '无法评估',
                    'description': '缺少流动性数据',
                    'reasoning': '需要供需数据来评估流动性',
                    'factors': '数据不足'
                }

            high_liquidity_count = sum(1 for liquidity in source_liquidity.values() if '高流动性' in liquidity)
            medium_liquidity_count = sum(1 for liquidity in source_liquidity.values() if '中等流动性' in liquidity)
            low_liquidity_count = sum(1 for liquidity in source_liquidity.values() if '低流动性' in liquidity)

            total_sources = len(source_liquidity)

            if high_liquidity_count > total_sources / 2:
                status = '流动性充足'
                description = f'多数数据源显示高流动性，市场交易活跃，买卖容易执行。'
                reasoning = '充足的流动性有利于价格发现和交易执行，市场效率较高。'
                factors = '供需总量充足，市场参与度高'
            elif medium_liquidity_count > total_sources / 2:
                status = '流动性适中'
                description = f'多数数据源显示中等流动性，市场交易正常，但可能存在一定的买卖价差。'
                reasoning = '适中的流动性能够满足正常交易需求，但大额交易可能影响价格。'
                factors = '供需总量适中，市场参与度一般'
            else:
                status = '流动性不足'
                description = f'多数数据源显示低流动性，市场交易不够活跃，买卖执行可能困难。'
                reasoning = '流动性不足可能导致较大的买卖价差，交易成本增加。'
                factors = '供需总量偏低，市场参与度不足'

            return {
                'status': status,
                'description': description,
                'reasoning': reasoning,
                'factors': factors
            }

        except Exception:
            return {
                'status': '分析异常',
                'description': '流动性分析过程中发生异常',
                'reasoning': '请检查数据质量或联系技术支持',
                'factors': '分析异常'
            }

    def _analyze_source_price_factors(self, source: str, data: Any) -> str:
        """分析单个数据源的价格支撑因素"""
        try:
            if isinstance(data, list) and data:
                latest_item = data[-1]
                supply = self._extract_value(latest_item, ['steamSellCount', 'supply', 'supply_quantity', 'sell_orders'])
                demand = self._extract_value(latest_item, ['steamBuyCount', 'demand', 'demand_quantity', 'buy_orders'])
                price = self._extract_value(latest_item, ['steamPrice', 'price', 'current_price', 'last_price'])

                factors = []

                if supply is not None and demand is not None:
                    if demand > supply:
                        factors.append(f'需求超过供应({demand}>{supply})支撑价格上涨')
                    elif supply > demand:
                        factors.append(f'供应超过需求({supply}>{demand})对价格形成压力')
                    else:
                        factors.append(f'供需平衡({supply}≈{demand})价格相对稳定')

                if price is not None:
                    factors.append(f'当前价格{price}元')

                return f'{source}: {"; ".join(factors)}' if factors else f'{source}价格因素数据不完整'
            else:
                return f'{source}无价格因素数据'

        except Exception:
            return f'{source}价格因素分析异常'

    def _generate_price_support_factors_analysis(self, source_factors: Dict[str, str]) -> Dict[str, Any]:
        """生成价格支撑因素综合分析"""
        try:
            if not source_factors:
                return {
                    'status': '无法分析',
                    'description': '缺少价格支撑因素数据',
                    'reasoning': '需要供需和价格数据来分析支撑因素',
                    'key_factors': [],
                    'risk_factors': []
                }

            key_factors = []
            risk_factors = []

            # 分析支撑和风险因素
            for source, factors in source_factors.items():
                if '支撑价格上涨' in factors:
                    key_factors.append(f'{source}数据显示需求旺盛，支撑价格上涨')
                elif '对价格形成压力' in factors:
                    risk_factors.append(f'{source}数据显示供应过剩，对价格形成下行压力')
                elif '价格相对稳定' in factors:
                    key_factors.append(f'{source}数据显示供需平衡，价格稳定性较好')

            # 生成综合评估
            if len(key_factors) > len(risk_factors):
                status = '价格支撑较强'
                description = f'多数因素支撑价格，{len(key_factors)}个积极因素，{len(risk_factors)}个风险因素。'
                reasoning = '积极因素占主导，价格具备上涨基础或保持稳定。'
            elif len(risk_factors) > len(key_factors):
                status = '价格支撑较弱'
                description = f'多数因素对价格不利，{len(risk_factors)}个风险因素，{len(key_factors)}个积极因素。'
                reasoning = '风险因素占主导，价格面临下行压力。'
            else:
                status = '价格支撑中性'
                description = f'积极因素与风险因素基本平衡，{len(key_factors)}个积极因素，{len(risk_factors)}个风险因素。'
                reasoning = '支撑因素与风险因素相互抵消，价格方向取决于其他因素。'

            return {
                'status': status,
                'description': description,
                'reasoning': reasoning,
                'key_factors': key_factors,
                'risk_factors': risk_factors
            }

        except Exception:
            return {
                'status': '分析异常',
                'description': '价格支撑因素分析过程中发生异常',
                'reasoning': '请检查数据质量或联系技术支持',
                'key_factors': [],
                'risk_factors': []
            }

    def _detect_source_anomalies(self, source: str, data: Any) -> List[Dict[str, str]]:
        """检测单个数据源的异常"""
        try:
            anomalies = []

            if isinstance(data, list) and len(data) >= 2:
                latest = data[-1]
                previous = data[-2]

                latest_supply = self._extract_value(latest, ['steamSellCount', 'supply', 'supply_quantity', 'sell_orders'])
                latest_demand = self._extract_value(latest, ['steamBuyCount', 'demand', 'demand_quantity', 'buy_orders'])
                previous_supply = self._extract_value(previous, ['steamSellCount', 'supply', 'supply_quantity', 'sell_orders'])
                previous_demand = self._extract_value(previous, ['steamBuyCount', 'demand', 'demand_quantity', 'buy_orders'])

                if all(v is not None for v in [latest_supply, latest_demand, previous_supply, previous_demand]):
                    # 检测供应异常变化
                    supply_change_pct = (latest_supply - previous_supply) / previous_supply if previous_supply > 0 else 0
                    if abs(supply_change_pct) > 0.5:  # 50%以上变化
                        anomalies.append({
                            'type': 'SUPPLY_ANOMALY',
                            'severity': 'HIGH' if abs(supply_change_pct) > 1.0 else 'MEDIUM',
                            'message': f'{source}供应量异常变化{supply_change_pct*100:.1f}%',
                            'source': source
                        })

                    # 检测需求异常变化
                    demand_change_pct = (latest_demand - previous_demand) / previous_demand if previous_demand > 0 else 0
                    if abs(demand_change_pct) > 0.5:  # 50%以上变化
                        anomalies.append({
                            'type': 'DEMAND_ANOMALY',
                            'severity': 'HIGH' if abs(demand_change_pct) > 1.0 else 'MEDIUM',
                            'message': f'{source}需求量异常变化{demand_change_pct*100:.1f}%',
                            'source': source
                        })

                    # 检测供需比例异常
                    latest_ratio = latest_supply / latest_demand if latest_demand > 0 else float('inf')
                    previous_ratio = previous_supply / previous_demand if previous_demand > 0 else float('inf')

                    if latest_ratio > 5 or latest_ratio < 0.2:  # 供需比例极端
                        anomalies.append({
                            'type': 'RATIO_ANOMALY',
                            'severity': 'MEDIUM',
                            'message': f'{source}供需比例异常({latest_ratio:.2f})',
                            'source': source
                        })

            return anomalies

        except Exception:
            return []

    def _generate_anomaly_assessment(self, anomaly_alerts: List[Dict]) -> Dict[str, str]:
        """生成异常评估"""
        try:
            if not anomaly_alerts:
                return {
                    'level': '无异常',
                    'description': '当前未检测到供需关系异常，市场运行正常。',
                    'recommended_action': '可以正常进行投资决策，但要保持基本的风险管理。'
                }

            high_severity_count = sum(1 for alert in anomaly_alerts if alert.get('severity') == 'HIGH')
            medium_severity_count = sum(1 for alert in anomaly_alerts if alert.get('severity') == 'MEDIUM')

            if high_severity_count >= 2:
                level = '严重异常'
                description = f'检测到{high_severity_count}个高严重性供需异常，市场存在重大变化。'
                recommended_action = '建议立即关注市场动态，谨慎调整投资策略。'
            elif high_severity_count >= 1:
                level = '重要异常'
                description = f'检测到{high_severity_count}个高严重性异常，需要密切关注。'
                recommended_action = '建议加强市场监控，适当调整仓位配置。'
            elif medium_severity_count >= 3:
                level = '中等异常'
                description = f'检测到{medium_severity_count}个中等异常，供需关系出现变化。'
                recommended_action = '建议关注供需变化趋势，做好风险防范。'
            else:
                level = '轻微异常'
                description = f'检测到{len(anomaly_alerts)}个轻微异常，整体风险可控。'
                recommended_action = '可以正常操作，但要注意相关风险点。'

            return {
                'level': level,
                'description': description,
                'recommended_action': recommended_action
            }

        except Exception:
            return {
                'level': '评估异常',
                'description': '异常评估过程中发生异常',
                'recommended_action': '建议谨慎操作，联系技术支持'
            }

    def _calculate_bid_ask_spread(self, current_price: float = None, bid_price: float = None) -> Dict:
        """
        重新设计的买卖价差分析（基于CS2饰品市场真实机制）

        CS2饰品市场机制：
        - current_price: 在售价（最低卖价），买家想立即买到需要支付的价格
        - bid_price: 求购价（最高买价），卖家想立即卖出可以接受的价格
        - 价差 = 在售价 - 求购价，反映买卖双方的价格分歧

        分析维度：
        1. 价格分歧程度：价差大小反映买卖双方对价值的不同认知
        2. 市场流动性：价差小说明容易撮合成交
        3. 交易成本：价差就是买卖双方的交易成本
        4. 市场效率：价差小说明价格发现机制有效
        """
        try:
            if current_price is None or bid_price is None or current_price <= 0:
                return {
                    'spread_rate': None,
                    'spread_absolute': None,
                    'market_efficiency': 'UNKNOWN',
                    'description': '价格数据缺失，无法计算价差',
                    'trading_cost': 'UNKNOWN'
                }

            # 计算绝对价差和相对价差
            spread_absolute = current_price - bid_price
            spread_rate = (spread_absolute / current_price) * 100

            # 分析市场效率和流动性（基于CS2饰品市场特点）
            if spread_rate <= 0.5:
                market_efficiency = 'EXTREMELY_HIGH'
                liquidity_status = 'EXCELLENT'
                trading_cost = 'MINIMAL'
                market_condition = 'VERY_ACTIVE'
                description = f'市场极度活跃 (价差: {spread_rate:.2f}%)，买卖价格几乎一致，随时可能成交'
            elif spread_rate <= 1.5:
                market_efficiency = 'VERY_HIGH'
                liquidity_status = 'VERY_GOOD'
                trading_cost = 'VERY_LOW'
                market_condition = 'ACTIVE'
                description = f'市场非常活跃 (价差: {spread_rate:.2f}%)，买卖分歧很小，成交概率高'
            elif spread_rate <= 3.0:
                market_efficiency = 'HIGH'
                liquidity_status = 'GOOD'
                trading_cost = 'LOW'
                market_condition = 'MODERATELY_ACTIVE'
                description = f'市场较为活跃 (价差: {spread_rate:.2f}%)，买卖分歧较小，有一定成交可能'
            elif spread_rate <= 5.0:
                market_efficiency = 'MODERATE'
                liquidity_status = 'FAIR'
                trading_cost = 'MODERATE'
                market_condition = 'NORMAL'
                description = f'市场活跃度一般 (价差: {spread_rate:.2f}%)，买卖分歧适中，需要时间撮合'
            elif spread_rate <= 8.0:
                market_efficiency = 'LOW'
                liquidity_status = 'POOR'
                trading_cost = 'HIGH'
                market_condition = 'SLUGGISH'
                description = f'市场较为冷清 (价差: {spread_rate:.2f}%)，买卖分歧较大，成交困难'
            elif spread_rate <= 15.0:
                market_efficiency = 'VERY_LOW'
                liquidity_status = 'VERY_POOR'
                trading_cost = 'VERY_HIGH'
                market_condition = 'INACTIVE'
                description = f'市场很不活跃 (价差: {spread_rate:.2f}%)，买卖分歧很大，成交很困难'
            else:
                market_efficiency = 'EXTREMELY_LOW'
                liquidity_status = 'EXTREMELY_POOR'
                trading_cost = 'EXTREMELY_HIGH'
                market_condition = 'STAGNANT'
                description = f'市场几乎停滞 (价差: {spread_rate:.2f}%)，买卖分歧巨大，基本无法成交'

            # 分析价格趋势倾向
            if spread_rate <= 2.0:
                price_convergence = 'HIGH'
                convergence_desc = '买卖价格接近，可能很快达成一致'
            elif spread_rate <= 5.0:
                price_convergence = 'MODERATE'
                convergence_desc = '买卖价格存在分歧，需要一方妥协'
            else:
                price_convergence = 'LOW'
                convergence_desc = '买卖价格分歧严重，短期内难以成交'

            # 交易建议
            if spread_rate <= 3.0:
                trading_advice = '适合交易，成交概率较高'
            elif spread_rate <= 8.0:
                trading_advice = '可以尝试交易，但需要耐心等待'
            else:
                trading_advice = '不建议急于交易，价格分歧过大'

            return {
                'spread_rate': round(spread_rate, 3),
                'spread_absolute': round(spread_absolute, 2),
                'market_efficiency': market_efficiency,
                'liquidity_status': liquidity_status,
                'trading_cost': trading_cost,
                'market_condition': market_condition,
                'price_convergence': price_convergence,
                'description': description,
                'convergence_description': convergence_desc,
                'trading_advice': trading_advice,
                'sell_price': current_price,  # 更准确的命名
                'buy_price': bid_price        # 更准确的命名
            }

        except Exception as e:
            return {
                'spread_rate': None,
                'market_efficiency': 'ERROR',
                'description': f'价差计算失败: {str(e)}',
                'trading_cost': 'UNKNOWN'
            }

    def _calculate_liquidity_ratio(self, volume: int = None, circulation: str = None) -> Dict:
        """
        计算流动性指标（市场活跃度）

        流动性指标 = 成交量 / 存世量 × 100%
        - 高流动性: 市场活跃，容易买卖
        - 低流动性: 市场冷清，交易困难
        """
        try:
            if volume is None or circulation is None:
                return {
                    'liquidity_ratio': None,
                    'activity_status': 'UNKNOWN',
                    'description': '交易量或存世量数据缺失，无法计算流动性',
                    'market_activity': 'UNKNOWN'
                }

            # 转换存世量为数值
            try:
                circulation_num = float(circulation) if isinstance(circulation, str) else circulation
            except (ValueError, TypeError):
                return {
                    'liquidity_ratio': None,
                    'activity_status': 'ERROR',
                    'description': '存世量数据格式错误',
                    'market_activity': 'UNKNOWN'
                }

            if circulation_num <= 0:
                return {
                    'liquidity_ratio': None,
                    'activity_status': 'ERROR',
                    'description': '存世量数据无效',
                    'market_activity': 'UNKNOWN'
                }

            # 计算流动性比率
            liquidity_ratio = (volume / circulation_num) * 100

            # 判断市场活跃度
            if liquidity_ratio >= 5.0:
                activity_status = 'VERY_ACTIVE'
                market_activity = 'EXCELLENT'
                description = f'市场极度活跃 (流动性: {liquidity_ratio:.3f}%)，交易频繁，容易买卖'
            elif liquidity_ratio >= 2.0:
                activity_status = 'ACTIVE'
                market_activity = 'GOOD'
                description = f'市场活跃 (流动性: {liquidity_ratio:.3f}%)，交易较频繁，买卖相对容易'
            elif liquidity_ratio >= 1.0:
                activity_status = 'MODERATE'
                market_activity = 'FAIR'
                description = f'市场活跃度中等 (流动性: {liquidity_ratio:.3f}%)，交易适中'
            elif liquidity_ratio >= 0.5:
                activity_status = 'QUIET'
                market_activity = 'POOR'
                description = f'市场较冷清 (流动性: {liquidity_ratio:.3f}%)，交易不够活跃'
            else:
                activity_status = 'VERY_QUIET'
                market_activity = 'VERY_POOR'
                description = f'市场很冷清 (流动性: {liquidity_ratio:.3f}%)，交易稀少，买卖困难'

            return {
                'liquidity_ratio': round(liquidity_ratio, 4),
                'activity_status': activity_status,
                'market_activity': market_activity,
                'description': description,
                'volume': volume,
                'circulation': circulation_num
            }

        except Exception as e:
            return {
                'liquidity_ratio': None,
                'activity_status': 'ERROR',
                'description': f'流动性计算失败: {str(e)}',
                'market_activity': 'UNKNOWN'
            }

    def _assess_overall_market_condition(self, supply_demand_analysis: Dict,
                                       spread_analysis: Dict, liquidity_analysis: Dict) -> Dict:
        """综合评估市场状态"""
        try:
            # 简化的综合评估逻辑
            conditions = []

            # 供需状态权重
            if supply_demand_analysis.get('pressure_direction') == 'STRONG_UPWARD':
                conditions.append('BULLISH')
            elif supply_demand_analysis.get('pressure_direction') == 'STRONG_DOWNWARD':
                conditions.append('BEARISH')
            else:
                conditions.append('NEUTRAL')

            # 流动性状态
            liquidity_status = spread_analysis.get('liquidity_status', 'UNKNOWN')
            activity_status = liquidity_analysis.get('activity_status', 'UNKNOWN')

            overall_condition = 'NEUTRAL'
            if 'BULLISH' in conditions and liquidity_status in ['EXCELLENT', 'GOOD']:
                overall_condition = 'STRONG_BULLISH'
            elif 'BEARISH' in conditions and liquidity_status in ['POOR', 'VERY_POOR']:
                overall_condition = 'STRONG_BEARISH'
            elif 'BULLISH' in conditions:
                overall_condition = 'BULLISH'
            elif 'BEARISH' in conditions:
                overall_condition = 'BEARISH'

            return {
                'overall_condition': overall_condition,
                'confidence_level': 'MEDIUM',
                'key_factors': conditions
            }

        except Exception:
            return {
                'overall_condition': 'UNKNOWN',
                'confidence_level': 'LOW',
                'key_factors': []
            }

    def _generate_economic_interpretation(self, supply_demand_analysis: Dict,
                                        spread_analysis: Dict, liquidity_analysis: Dict,
                                        market_assessment: Dict) -> Dict:
        """生成经济学解释和建议"""
        try:
            interpretation = []
            recommendations = []

            # 基于供需分析的解释
            if supply_demand_analysis.get('balance_status') == 'SEVERE_UNDERSUPPLY':
                interpretation.append("市场呈现严重供不应求状态，买方需求远超卖方供给")
                recommendations.append("建议买方适当提高出价以获得成交机会")
            elif supply_demand_analysis.get('balance_status') == 'SEVERE_OVERSUPPLY':
                interpretation.append("市场呈现严重供过于求状态，卖方供给远超买方需求")
                recommendations.append("建议卖方适当降低要价以促进成交")

            # 基于流动性分析的解释
            if spread_analysis.get('liquidity_status') == 'VERY_POOR':
                interpretation.append("买卖价差过大，表明市场流动性不足")
                recommendations.append("建议谨慎交易，注意较高的交易成本")

            return {
                'economic_interpretation': interpretation,
                'trading_recommendations': recommendations,
                'market_outlook': market_assessment.get('overall_condition', 'NEUTRAL')
            }

        except Exception:
            return {
                'economic_interpretation': ['分析过程中发生异常'],
                'trading_recommendations': ['建议谨慎操作'],
                'market_outlook': 'UNKNOWN'
            }


