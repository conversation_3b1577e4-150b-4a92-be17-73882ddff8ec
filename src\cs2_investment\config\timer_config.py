"""
定时器配置管理

使用Pydantic进行定时器配置管理，支持环境变量和.env文件。
"""

from typing import Optional, Dict, Any
from pydantic import Field, validator
from pydantic_settings import BaseSettings
from pathlib import Path
import os


class ItemInfoUpdateSettings(BaseSettings):
    """饰品信息更新定时器配置"""

    enabled: bool = Field(default=True, alias="ITEM_INFO_UPDATE_ENABLED")
    cron_schedule: str = Field(default="0 2 * * *", alias="ITEM_INFO_UPDATE_CRON")  # 每天凌晨2点
    health_check_interval: int = Field(default=60, alias="ITEM_INFO_HEALTH_CHECK_INTERVAL")  # 分钟
    max_retry_attempts: int = Field(default=3, alias="ITEM_INFO_MAX_RETRY")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"

    @validator('cron_schedule')
    def validate_cron_schedule(cls, v):
        """验证cron表达式格式"""
        if not v or not isinstance(v, str):
            raise ValueError("cron_schedule must be a non-empty string")

        parts = v.strip().split()
        if len(parts) != 5:
            raise ValueError("cron_schedule must have exactly 5 parts (minute hour day month weekday)")

        return v

    @validator('health_check_interval')
    def validate_health_check_interval(cls, v):
        """验证健康检查间隔"""
        if v < 1 or v > 1440:  # 1分钟到24小时
            raise ValueError("health_check_interval must be between 1 and 1440 minutes")
        return v



class SimplePriceUpdateSettings(BaseSettings):
    """简化价格更新器配置"""

    enabled: bool = Field(default=True, alias="SIMPLE_PRICE_UPDATE_ENABLED")
    update_interval_minutes: int = Field(default=1, alias="SIMPLE_PRICE_UPDATE_INTERVAL")  # 每分钟执行一次
    batch_size: int = Field(default=100, alias="SIMPLE_PRICE_BATCH_SIZE")  # 批量接口处理数量
    single_size: int = Field(default=50, alias="SIMPLE_PRICE_SINGLE_SIZE")  # 单个接口处理数量
    single_interval_seconds: float = Field(default=1.2, alias="SIMPLE_PRICE_SINGLE_INTERVAL")  # 单个接口间隔
    items_limit: int = Field(default=150, alias="SIMPLE_PRICE_ITEMS_LIMIT")  # 每次获取的饰品数量
    use_last_update_time: bool = Field(default=True, alias="SIMPLE_PRICE_USE_LAST_UPDATE")  # 是否按最后更新时间排序
    skip_zero_price_items: bool = Field(default=False, alias="SIMPLE_PRICE_SKIP_ZERO_ITEMS")  # 是否跳过全零价格饰品
    zero_price_update_interval_hours: int = Field(default=24, alias="SIMPLE_PRICE_ZERO_UPDATE_INTERVAL")  # 全零价格饰品重新查询间隔（小时）
    continuous_mode: bool = Field(default=True, alias="SIMPLE_PRICE_CONTINUOUS_MODE")  # 是否启用持续运行模式
    cycle_interval_minutes: int = Field(default=30, alias="SIMPLE_PRICE_CYCLE_INTERVAL")  # 完整轮询间隔（分钟）

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"

    @validator('update_interval_minutes')
    def validate_update_interval(cls, v):
        """验证更新间隔"""
        if v < 1 or v > 60:
            raise ValueError("update_interval_minutes must be between 1 and 60")
        return v

    @validator('batch_size')
    def validate_batch_size(cls, v):
        """验证批量大小"""
        if v < 1 or v > 100:
            raise ValueError("batch_size must be between 1 and 100 (API limit)")
        return v

    @validator('single_size')
    def validate_single_size(cls, v):
        """验证单个处理大小"""
        if v < 1 or v > 60:
            raise ValueError("single_size must be between 1 and 60 (API limit)")
        return v

    @validator('single_interval_seconds')
    def validate_single_interval(cls, v):
        """验证单个接口间隔"""
        if v < 1.0 or v > 10.0:
            raise ValueError("single_interval_seconds must be between 1.0 and 10.0")
        return v

    @validator('items_limit')
    def validate_items_limit(cls, v):
        """验证饰品数量限制"""
        if v < 1 or v > 1000:
            raise ValueError("items_limit must be between 1 and 1000")
        return v


class SteamMonitorSettings(BaseSettings):
    """Steam监控配置"""

    enabled: bool = Field(default=True, alias="STEAM_MONITOR_ENABLED")
    monitor_interval_minutes: int = Field(default=30, alias="STEAM_MONITOR_INTERVAL")
    batch_size: int = Field(default=50, alias="STEAM_BATCH_SIZE")
    min_delay_seconds: int = Field(default=5, alias="STEAM_MIN_DELAY_SECONDS")
    max_delay_seconds: int = Field(default=15, alias="STEAM_MAX_DELAY_SECONDS")

    # Steam专用代理配置（用于访问国外Steam网站）
    proxy_enabled: bool = Field(default=False, alias="STEAM_PROXY_ENABLED")
    proxy_url: str = Field(default="http://127.0.0.1:7890", alias="STEAM_PROXY_URL")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"

    @validator('monitor_interval_minutes')
    def validate_monitor_interval(cls, v):
        """验证监控间隔"""
        if v < 1 or v > 1440:  # 1分钟到24小时
            raise ValueError("monitor_interval_minutes must be between 1 and 1440 minutes")
        return v

    @validator('proxy_url')
    def validate_proxy_url(cls, v):
        """验证Steam代理URL格式"""
        if v and not v.startswith(('http://', 'https://', 'socks4://', 'socks5://')):
            raise ValueError("Steam proxy_url must start with http://, https://, socks4://, or socks5://")
        return v

    @validator('batch_size')
    def validate_batch_size(cls, v):
        """验证批量大小"""
        if v < 1 or v > 200:
            raise ValueError("batch_size must be between 1 and 200")
        return v

    @validator('min_delay_seconds', 'max_delay_seconds')
    def validate_delay_seconds(cls, v):
        """验证延迟秒数"""
        if v < 1 or v > 300:
            raise ValueError("delay seconds must be between 1 and 300")
        return v

    @validator('max_delay_seconds')
    def validate_max_delay_greater_than_min(cls, v, values):
        """验证最大延迟大于最小延迟"""
        if 'min_delay_seconds' in values and v <= values['min_delay_seconds']:
            raise ValueError("max_delay_seconds must be greater than min_delay_seconds")
        return v

class SteamDTIdUpdateSettings(BaseSettings):
    """SteamDT ID更新调度器配置"""

    enabled: bool = Field(default=True, alias="STEAMDT_ID_UPDATE_ENABLED")
    continuous_mode: bool = Field(default=True, alias="STEAMDT_ID_CONTINUOUS_MODE")  # 持续运行模式
    cycle_interval_minutes: int = Field(default=10, alias="STEAMDT_ID_CYCLE_INTERVAL")  # 完整轮询间隔（分钟）
    batch_size: int = Field(default=50, alias="STEAMDT_ID_BATCH_SIZE")
    min_wait_seconds: int = Field(default=5, alias="STEAMDT_ID_MIN_WAIT")
    max_wait_seconds: int = Field(default=20, alias="STEAMDT_ID_MAX_WAIT")
    max_retry_attempts: int = Field(default=3, alias="STEAMDT_ID_MAX_RETRY")
    health_check_interval: int = Field(default=60, alias="STEAMDT_ID_HEALTH_CHECK_INTERVAL")  # 分钟

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"

    @validator('cycle_interval_minutes')
    def validate_cycle_interval(cls, v):
        """验证轮询间隔"""
        if v < 1 or v > 1440:  # 1分钟到24小时
            raise ValueError("cycle_interval_minutes must be between 1 and 1440 minutes")
        return v

    @validator('batch_size')
    def validate_batch_size(cls, v):
        """验证批量大小"""
        if v < 1 or v > 200:
            raise ValueError("batch_size must be between 1 and 200")
        return v

    @validator('min_wait_seconds', 'max_wait_seconds')
    def validate_wait_seconds(cls, v):
        """验证等待秒数"""
        if v < 1 or v > 300:
            raise ValueError("wait seconds must be between 1 and 300")
        return v

    @validator('max_wait_seconds')
    def validate_max_wait_greater_than_min(cls, v, values):
        """验证最大等待时间大于最小等待时间"""
        if 'min_wait_seconds' in values and v <= values['min_wait_seconds']:
            raise ValueError("max_wait_seconds must be greater than min_wait_seconds")
        return v

    @validator('max_retry_attempts')
    def validate_max_retry_attempts(cls, v):
        """验证最大重试次数"""
        if v < 1 or v > 10:
            raise ValueError("max_retry_attempts must be between 1 and 10")
        return v

    @validator('health_check_interval')
    def validate_health_check_interval(cls, v):
        """验证健康检查间隔"""
        if v < 1 or v > 1440:  # 1分钟到24小时
            raise ValueError("health_check_interval must be between 1 and 1440 minutes")
        return v


class ScrapingSettings(BaseSettings):
    """数据抓取配置"""

    scraping_method: str = Field(default="api", alias="SCRAPING_METHOD")
    api_fallback_enabled: bool = Field(default=True, alias="SCRAPING_API_FALLBACK_ENABLED")
    api_timeout: int = Field(default=600, alias="SCRAPING_API_TIMEOUT")  # 增加到10分钟，适应多接口调用
    data_validation_enabled: bool = Field(default=True, alias="SCRAPING_DATA_VALIDATION_ENABLED")

    # 代理配置
    proxy_enabled: bool = Field(default=True, alias="SCRAPING_PROXY_ENABLED")
    proxy_url: str = Field(default="http://127.0.0.1:1080", alias="SCRAPING_PROXY_URL")

    # API请求延迟配置
    api_delay_min_seconds: int = Field(default=10, alias="SCRAPING_API_DELAY_MIN")
    api_delay_max_seconds: int = Field(default=30, alias="SCRAPING_API_DELAY_MAX")

    max_retry_attempts: int = Field(default=3, alias="SCRAPING_MAX_RETRY_ATTEMPTS")
    retry_delay: float = Field(default=2.0, alias="SCRAPING_RETRY_DELAY")
    playwright_headless: bool = Field(default=True, alias="SCRAPING_PLAYWRIGHT_HEADLESS")
    playwright_timeout: int = Field(default=60, alias="SCRAPING_PLAYWRIGHT_TIMEOUT")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"

    @validator('scraping_method')
    def validate_scraping_method(cls, v):
        """验证抓取方式"""
        valid_methods = ['playwright', 'api']
        if v.lower() not in valid_methods:
            raise ValueError(f"scraping_method must be one of {valid_methods}")
        return v.lower()

    @validator('api_timeout', 'playwright_timeout')
    def validate_timeout(cls, v):
        """验证超时时间"""
        if v < 5 or v > 1200:  # 增加到20分钟，适应复杂的多接口调用
            raise ValueError("timeout must be between 5 and 1200 seconds")
        return v

    @validator('proxy_url')
    def validate_proxy_url(cls, v):
        """验证代理URL格式"""
        if v and not v.startswith(('http://', 'https://', 'socks4://', 'socks5://')):
            raise ValueError("proxy_url must start with http://, https://, socks4://, or socks5://")
        return v

    @validator('max_retry_attempts')
    def validate_max_retry_attempts(cls, v):
        """验证最大重试次数"""
        if v < 1 or v > 10:
            raise ValueError("max_retry_attempts must be between 1 and 10")
        return v

    @validator('retry_delay')
    def validate_retry_delay(cls, v):
        """验证重试延迟"""
        if v < 0.1 or v > 60.0:
            raise ValueError("retry_delay must be between 0.1 and 60.0 seconds")
        return v

    @validator('api_delay_min_seconds', 'api_delay_max_seconds')
    def validate_api_delay_range(cls, v):
        """验证API延迟时间范围"""
        if v < 1 or v > 300:  # 1秒到5分钟
            raise ValueError("api_delay must be between 1 and 300 seconds")
        return v

    @validator('api_delay_max_seconds')
    def validate_api_delay_max_greater_than_min(cls, v, values):
        """确保最大延迟大于最小延迟"""
        if 'api_delay_min_seconds' in values and v <= values['api_delay_min_seconds']:
            raise ValueError("api_delay_max_seconds must be greater than api_delay_min_seconds")
        return v


class APISettings(BaseSettings):
    """API配置"""

    steamdt_api_key: str = Field(default="", alias="STEAMDT_API_KEY")
    api_timeout: int = Field(default=30, alias="API_TIMEOUT")
    max_retries: int = Field(default=3, alias="API_MAX_RETRIES")
    retry_delay: float = Field(default=1.0, alias="API_RETRY_DELAY")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"

    @validator('steamdt_api_key')
    def validate_api_key(cls, v):
        """验证API密钥"""
        if v and len(v) < 10:
            raise ValueError("steamdt_api_key must be at least 10 characters long")
        return v

    @validator('api_timeout')
    def validate_timeout(cls, v):
        """验证超时时间"""
        if v < 1 or v > 1200:  # 增加到20分钟
            raise ValueError("api_timeout must be between 1 and 1200 seconds")
        return v


class SchedulerSettings(BaseSettings):
    """调度器全局配置"""

    enabled: bool = Field(default=True, alias="SCHEDULER_ENABLED")
    startup_delay: int = Field(default=2, alias="SCHEDULER_STARTUP_DELAY")  # 秒
    shutdown_timeout: int = Field(default=30, alias="SCHEDULER_SHUTDOWN_TIMEOUT")  # 秒
    timezone: str = Field(default="Asia/Shanghai", alias="SCHEDULER_TIMEZONE")
    log_level: str = Field(default="INFO", alias="SCHEDULER_LOG_LEVEL")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"

    @validator('startup_delay', 'shutdown_timeout')
    def validate_timing(cls, v):
        """验证时间配置"""
        if v < 0 or v > 300:
            raise ValueError("timing values must be between 0 and 300 seconds")
        return v

    @validator('log_level')
    def validate_log_level(cls, v):
        """验证日志级别"""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"log_level must be one of {valid_levels}")
        return v.upper()


class TimerConfig(BaseSettings):
    """定时器主配置类"""

    # 子配置
    item_info_update: ItemInfoUpdateSettings = ItemInfoUpdateSettings()
    simple_price_update: SimplePriceUpdateSettings = SimplePriceUpdateSettings()
    steam_monitor: SteamMonitorSettings = SteamMonitorSettings()
    steamdt_id_update: SteamDTIdUpdateSettings = SteamDTIdUpdateSettings()
    scraping: ScrapingSettings = ScrapingSettings()
    api: APISettings = APISettings()
    scheduler: SchedulerSettings = SchedulerSettings()

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"

    def validate_all(self) -> Dict[str, Any]:
        """验证所有配置"""
        validation_results = {
            'valid': True,
            'errors': [],
            'warnings': []
        }

        try:
            # 验证API密钥
            if not self.api.steamdt_api_key:
                validation_results['warnings'].append("STEAMDT_API_KEY not configured")
            elif self.api.steamdt_api_key in ['your_api_key_here', 'test_api_key']:
                validation_results['warnings'].append("Using placeholder API key")

            # 验证定时器启用状态
            if not self.scheduler.enabled:
                validation_results['warnings'].append("Scheduler is disabled")

            if (not self.item_info_update.enabled and
                not self.simple_price_update.enabled and
                not self.steam_monitor.enabled and
                not self.steamdt_id_update.enabled):
                validation_results['warnings'].append("All timers are disabled")

            # 验证抓取配置
            if self.scraping.scraping_method == 'api' and not self.api.steamdt_api_key:
                validation_results['errors'].append("API scraping method requires STEAMDT_API_KEY to be configured")

            if self.scraping.scraping_method == 'api' and not self.scraping.api_fallback_enabled:
                validation_results['warnings'].append("API scraping without fallback may cause failures")

            if self.scraping.api_timeout > self.scraping.playwright_timeout:
                validation_results['warnings'].append("API timeout is longer than playwright timeout")

        except Exception as e:
            validation_results['valid'] = False
            validation_results['errors'].append(f"Configuration validation error: {str(e)}")

        return validation_results

    def get_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'scheduler': {
                'enabled': self.scheduler.enabled,
                'timezone': self.scheduler.timezone,
                'startup_delay': self.scheduler.startup_delay
            },
            'item_info_update': {
                'enabled': self.item_info_update.enabled,
                'schedule': self.item_info_update.cron_schedule,
                'health_check_interval': self.item_info_update.health_check_interval
            },
            'simple_price_update': {
                'enabled': self.simple_price_update.enabled,
                'continuous_mode': self.simple_price_update.continuous_mode,
                'cycle_interval': self.simple_price_update.cycle_interval_minutes,
                'batch_size': self.simple_price_update.batch_size,
                'single_size': self.simple_price_update.single_size,
                'items_limit': self.simple_price_update.items_limit
            },
            'steam_monitor': {
                'enabled': self.steam_monitor.enabled,
                'monitor_interval': self.steam_monitor.monitor_interval_minutes,
                'batch_size': self.steam_monitor.batch_size,
                'delay_range': f"{self.steam_monitor.min_delay_seconds}-{self.steam_monitor.max_delay_seconds}s"
            },
            'steamdt_id_update': {
                'enabled': self.steamdt_id_update.enabled,
                'continuous_mode': self.steamdt_id_update.continuous_mode,
                'cycle_interval': self.steamdt_id_update.cycle_interval_minutes,
                'batch_size': self.steamdt_id_update.batch_size,
                'wait_range': f"{self.steamdt_id_update.min_wait_seconds}-{self.steamdt_id_update.max_wait_seconds}s",
                'max_retry_attempts': self.steamdt_id_update.max_retry_attempts,
                'health_check_interval': self.steamdt_id_update.health_check_interval
            },
            'scraping': {
                'method': self.scraping.scraping_method,
                'api_fallback_enabled': self.scraping.api_fallback_enabled,
                'data_validation_enabled': self.scraping.data_validation_enabled,
                'api_timeout': self.scraping.api_timeout,
                'playwright_timeout': self.scraping.playwright_timeout,
                'playwright_headless': self.scraping.playwright_headless,
                'max_retry_attempts': self.scraping.max_retry_attempts
            },
            'api': {
                'has_api_key': bool(self.api.steamdt_api_key),
                'timeout': self.api.api_timeout,
                'max_retries': self.api.max_retries
            }
        }

    def update_from_dict(self, config_dict: Dict[str, Any]) -> bool:
        """从字典更新配置（热更新）"""
        try:
            for section, values in config_dict.items():
                if hasattr(self, section):
                    section_obj = getattr(self, section)
                    for key, value in values.items():
                        if hasattr(section_obj, key):
                            setattr(section_obj, key, value)
            return True
        except Exception as e:
            return False

    @classmethod
    def load_from_env(cls) -> 'TimerConfig':
        """从环境变量加载配置"""
        return cls()

    @classmethod
    def load_from_file(cls, config_file: Path) -> 'TimerConfig':
        """从配置文件加载配置"""
        if config_file.exists():
            # 临时设置环境变量文件路径
            original_env_file = os.environ.get('ENV_FILE', '')
            os.environ['ENV_FILE'] = str(config_file)

            try:
                config = cls()
                return config
            finally:
                # 恢复原始环境变量
                if original_env_file:
                    os.environ['ENV_FILE'] = original_env_file
                else:
                    os.environ.pop('ENV_FILE', None)
        else:
            return cls()


# 全局配置实例
_timer_config: Optional[TimerConfig] = None


def get_timer_config() -> TimerConfig:
    """获取定时器配置实例（单例模式）"""
    global _timer_config

    if _timer_config is None:
        _timer_config = TimerConfig.load_from_env()

    return _timer_config


def reload_timer_config() -> TimerConfig:
    """重新加载定时器配置"""
    global _timer_config
    _timer_config = None
    return get_timer_config()


def create_env_template(output_file: Path = None) -> str:
    """创建环境变量模板"""
    template = """# 定时器配置环境变量模板
# 复制此文件为 .env 并根据需要修改配置值

# ===== 调度器全局配置 =====
SCHEDULER_ENABLED=true
SCHEDULER_STARTUP_DELAY=2
SCHEDULER_SHUTDOWN_TIMEOUT=30
SCHEDULER_TIMEZONE=Asia/Shanghai
SCHEDULER_LOG_LEVEL=INFO

# ===== API配置 =====
STEAMDT_API_KEY=your_steamdt_api_key_here
API_TIMEOUT=30
API_MAX_RETRIES=3
API_RETRY_DELAY=1.0

# ===== 饰品信息更新定时器 =====
ITEM_INFO_UPDATE_ENABLED=true
ITEM_INFO_UPDATE_CRON=0 2 * * *
ITEM_INFO_HEALTH_CHECK_INTERVAL=60
ITEM_INFO_MAX_RETRY=3

# ===== 价格更新定时器 =====
PRICE_UPDATE_ENABLED=true
PRICE_UPDATE_HEALTH_CHECK_INTERVAL=5
PRICE_UPDATE_AUTO_RESTART=true
PRICE_UPDATE_BATCH_SIZE=100
PRICE_UPDATE_SINGLE_RPM=60
PRICE_UPDATE_BATCH_RPM=10

# ===== 简化价格更新器 =====
SIMPLE_PRICE_UPDATE_ENABLED=true
SIMPLE_PRICE_UPDATE_INTERVAL=1
SIMPLE_PRICE_BATCH_SIZE=100
SIMPLE_PRICE_SINGLE_SIZE=50
SIMPLE_PRICE_SINGLE_INTERVAL=1.2
SIMPLE_PRICE_ITEMS_LIMIT=150
SIMPLE_PRICE_USE_LAST_UPDATE=true
SIMPLE_PRICE_SKIP_ZERO_ITEMS=false
SIMPLE_PRICE_ZERO_UPDATE_INTERVAL=24
SIMPLE_PRICE_CONTINUOUS_MODE=true
SIMPLE_PRICE_CYCLE_INTERVAL=30

# ===== Steam市场监控 =====
STEAM_MONITOR_ENABLED=true
STEAM_MONITOR_INTERVAL=30
STEAM_BATCH_SIZE=50
STEAM_MIN_DELAY_SECONDS=5
STEAM_MAX_DELAY_SECONDS=30

# ===== SteamDT ID更新调度器 =====
STEAMDT_ID_UPDATE_ENABLED=true
STEAMDT_ID_CONTINUOUS_MODE=true
STEAMDT_ID_CYCLE_INTERVAL=10
STEAMDT_ID_BATCH_SIZE=50
STEAMDT_ID_MIN_WAIT=5
STEAMDT_ID_MAX_WAIT=20
STEAMDT_ID_MAX_RETRY=3
STEAMDT_ID_HEALTH_CHECK_INTERVAL=60
"""

    if output_file:
        output_file.write_text(template, encoding='utf-8')

    return template
