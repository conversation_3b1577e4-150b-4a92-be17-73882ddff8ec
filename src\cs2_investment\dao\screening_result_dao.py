"""
筛选结果数据访问对象

提供投资筛选结果相关的数据库操作。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import desc, asc, and_, or_, func
from loguru import logger

from .base_dao import BaseDAO
from ..models.screening_result import ScreeningResult
from ..models.item import Item
from ..config.database import get_db_session


class ScreeningResultDAO(BaseDAO[ScreeningResult]):
    """筛选结果DAO"""
    
    def __init__(self):
        super().__init__(ScreeningResult)
    
    def get_latest_results(self, investment_type: Optional[str] = None, 
                          limit: int = 100) -> List[ScreeningResult]:
        """获取最新的筛选结果"""
        try:
            with get_db_session() as session:
                query = session.query(ScreeningResult)\
                    .options(joinedload(ScreeningResult.item))
                
                if investment_type:
                    query = query.filter(ScreeningResult.investment_type == investment_type)
                
                return query.order_by(desc(ScreeningResult.screening_time))\
                    .limit(limit).all()
        except SQLAlchemyError as e:
            self.logger.error(f"获取最新筛选结果失败: {e}")
            raise
    
    def get_results_by_type(self, investment_type: str, days: int = 7) -> List[ScreeningResult]:
        """获取指定投资类型的筛选结果"""
        try:
            with get_db_session() as session:
                start_time = datetime.now() - timedelta(days=days)
                return session.query(ScreeningResult)\
                    .options(joinedload(ScreeningResult.item))\
                    .filter(
                        and_(
                            ScreeningResult.investment_type == investment_type,
                            ScreeningResult.screening_time >= start_time
                        )
                    )\
                    .order_by(desc(ScreeningResult.score)).all()
        except SQLAlchemyError as e:
            self.logger.error(f"获取投资类型筛选结果失败: {e}")
            raise
    
    def get_top_scored_results(self, investment_type: Optional[str] = None, 
                              limit: int = 20) -> List[ScreeningResult]:
        """获取高评分的筛选结果"""
        try:
            with get_db_session() as session:
                # 获取最新筛选时间
                latest_time = session.query(func.max(ScreeningResult.screening_time)).scalar()
                if not latest_time:
                    return []
                
                query = session.query(ScreeningResult)\
                    .options(joinedload(ScreeningResult.item))\
                    .filter(
                        and_(
                            ScreeningResult.screening_time >= latest_time - timedelta(hours=1),
                            ScreeningResult.score.isnot(None)
                        )
                    )
                
                if investment_type:
                    query = query.filter(ScreeningResult.investment_type == investment_type)
                
                return query.order_by(desc(ScreeningResult.score))\
                    .limit(limit).all()
        except SQLAlchemyError as e:
            self.logger.error(f"获取高评分筛选结果失败: {e}")
            raise
    
    def get_results_by_item(self, item_id: str, days: int = 30) -> List[ScreeningResult]:
        """获取指定饰品的筛选历史"""
        try:
            with get_db_session() as session:
                start_time = datetime.now() - timedelta(days=days)
                return session.query(ScreeningResult)\
                    .filter(
                        and_(
                            ScreeningResult.item_id == item_id,
                            ScreeningResult.screening_time >= start_time
                        )
                    )\
                    .order_by(desc(ScreeningResult.screening_time)).all()
        except SQLAlchemyError as e:
            self.logger.error(f"获取饰品筛选历史失败: {e}")
            raise
    
    def get_investment_summary(self, days: int = 7) -> Dict[str, Any]:
        """获取投资机会摘要"""
        try:
            with get_db_session() as session:
                start_time = datetime.now() - timedelta(days=days)
                
                # 按投资类型统计
                type_stats = session.query(
                    ScreeningResult.investment_type,
                    func.count(ScreeningResult.id).label('count'),
                    func.avg(ScreeningResult.score).label('avg_score'),
                    func.max(ScreeningResult.score).label('max_score')
                ).filter(
                    ScreeningResult.screening_time >= start_time
                ).group_by(ScreeningResult.investment_type).all()
                
                # 风险等级统计
                risk_stats = session.query(
                    ScreeningResult.risk_level,
                    func.count(ScreeningResult.id).label('count')
                ).filter(
                    ScreeningResult.screening_time >= start_time
                ).group_by(ScreeningResult.risk_level).all()
                
                # 推荐等级统计
                recommendation_stats = session.query(
                    ScreeningResult.recommendation,
                    func.count(ScreeningResult.id).label('count')
                ).filter(
                    ScreeningResult.screening_time >= start_time
                ).group_by(ScreeningResult.recommendation).all()
                
                return {
                    'type_distribution': [
                        {
                            'investment_type': stat[0],
                            'count': stat[1],
                            'avg_score': float(stat[2]) if stat[2] else 0,
                            'max_score': float(stat[3]) if stat[3] else 0
                        }
                        for stat in type_stats
                    ],
                    'risk_distribution': [
                        {'risk_level': stat[0], 'count': stat[1]}
                        for stat in risk_stats
                    ],
                    'recommendation_distribution': [
                        {'recommendation': stat[0], 'count': stat[1]}
                        for stat in recommendation_stats
                    ]
                }
        except SQLAlchemyError as e:
            self.logger.error(f"获取投资机会摘要失败: {e}")
            raise
    
    def batch_insert_results(self, results_data: List[dict]) -> List[ScreeningResult]:
        """批量插入筛选结果"""
        try:
            with get_db_session() as session:
                results = []
                for data in results_data:
                    result = ScreeningResult(**data)
                    results.append(result)
                
                session.add_all(results)
                session.flush()
                
                for result in results:
                    session.refresh(result)
                
                self.logger.info(f"批量插入筛选结果: {len(results)}条")
                return results
        except SQLAlchemyError as e:
            self.logger.error(f"批量插入筛选结果失败: {e}")
            raise
    
    def cleanup_old_results(self, days: int = 90) -> int:
        """清理旧的筛选结果"""
        try:
            with get_db_session() as session:
                cutoff_time = datetime.now() - timedelta(days=days)
                deleted_count = session.query(ScreeningResult)\
                    .filter(ScreeningResult.screening_time < cutoff_time)\
                    .delete()
                
                self.logger.info(f"清理旧筛选结果: {deleted_count}条")
                return deleted_count
        except SQLAlchemyError as e:
            self.logger.error(f"清理旧筛选结果失败: {e}")
            raise
    
    def update_result_rank(self, screening_time: datetime, investment_type: str):
        """更新筛选结果排名"""
        try:
            with get_db_session() as session:
                # 获取指定时间和类型的结果，按评分排序
                results = session.query(ScreeningResult)\
                    .filter(
                        and_(
                            ScreeningResult.screening_time == screening_time,
                            ScreeningResult.investment_type == investment_type,
                            ScreeningResult.score.isnot(None)
                        )
                    )\
                    .order_by(desc(ScreeningResult.score)).all()
                
                # 更新排名
                for rank, result in enumerate(results, 1):
                    result.rank = rank
                
                session.flush()
                self.logger.info(f"更新排名: {investment_type} - {len(results)}条记录")
        except SQLAlchemyError as e:
            self.logger.error(f"更新筛选结果排名失败: {e}")
            raise
