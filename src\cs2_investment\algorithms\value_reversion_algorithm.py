"""
价值回归型投资筛选算法

识别被低估且有回归潜力的投资机会。
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, asc, or_
from typing import List, Dict, Any
from loguru import logger

from .base_algorithm import BaseScreeningAlgorithm
from ..models.market_snapshot import MarketSnapshot
from ..models.analysis_result import AnalysisResult


class ValueReversionAlgorithm(BaseScreeningAlgorithm):
    """价值回归型投资筛选算法"""
    
    def __init__(self):
        super().__init__(
            algorithm_name="ValueReversionAlgorithm",
            investment_type="价值回归型",
            version="1.0"
        )
    
    def get_screening_query(self, session: Session):
        """获取价值回归型筛选查询"""
        return session.query(MarketSnapshot, AnalysisResult)\
            .join(AnalysisResult, MarketSnapshot.item_id == AnalysisResult.item_id)\
            .filter(
                and_(
                    # 基础要求
                    MarketSnapshot.current_price > 100,  # 有一定价值基础
                    MarketSnapshot.survive_num.isnot(None),  # 有存世量数据
                    MarketSnapshot.trans_count_1m > 50,  # 有基本流动性
                    
                    # 价值回归信号
                    and_(
                        # 当前价格相对历史低位
                        or_(
                            MarketSnapshot.diff_1m < -15,  # 1个月跌幅超过15%
                            MarketSnapshot.diff_3m < -20,  # 3个月跌幅超过20%
                        ),
                        # 但近期有企稳迹象
                        MarketSnapshot.diff_7d > -10,  # 7日跌幅不超过10%
                        
                        # 基本面支撑
                        or_(
                            MarketSnapshot.survive_num < 5000,  # 稀缺性支撑
                            and_(
                                MarketSnapshot.sell_nums_1m_rate < -5,  # 供应减少
                                MarketSnapshot.trans_amount_1m > 500000  # 有成交支撑
                            )
                        )
                    )
                )
            )\
            .order_by(asc(MarketSnapshot.diff_1m))  # 按跌幅排序，跌幅大的优先
    
    def calculate_score(self, snapshot_and_analysis) -> float:
        """计算价值回归评分"""
        snapshot, analysis = snapshot_and_analysis
        score = 0.0
        
        # 1. 价格偏离度评分 (30分)
        deviation_score = self._calculate_price_deviation(snapshot)
        score += deviation_score
        
        # 2. 基本面价值评分 (25分)
        fundamental_score = self._calculate_fundamental_value(snapshot)
        score += fundamental_score
        
        # 3. 供需平衡评分 (25分)
        supply_demand_score = self._calculate_supply_demand(snapshot)
        score += supply_demand_score
        
        # 4. 技术支撑评分 (20分)
        technical_support_score = self._calculate_technical_support(snapshot, analysis)
        score += technical_support_score
        
        return min(score, 100.0)
    
    def _calculate_price_deviation(self, snapshot: MarketSnapshot) -> float:
        """计算价格偏离度评分"""
        score = 0.0
        
        if not snapshot.current_price:
            return score
        
        current_price = float(snapshot.current_price)
        
        # 计算历史均价（基于不同时期的价格）
        price_points = []
        
        # 收集历史价格点
        for period, diff_field in [
            ('1m', snapshot.diff_1m),
            ('3m', snapshot.diff_3m),
            ('6m', snapshot.diff_6m)
        ]:
            if diff_field is not None:
                # 根据涨跌幅反推历史价格
                historical_price = current_price / (1 + float(diff_field) / 100)
                price_points.append(historical_price)
        
        if price_points:
            avg_historical_price = sum(price_points) / len(price_points)
            deviation = (current_price - avg_historical_price) / avg_historical_price * 100
            
            # 价格低于历史均价越多，得分越高
            if deviation <= -30:
                score += 30  # 严重低估
            elif deviation <= -20:
                score += 25  # 明显低估
            elif deviation <= -10:
                score += 20  # 适度低估
            elif deviation <= -5:
                score += 15  # 轻微低估
            elif deviation <= 0:
                score += 10  # 略低于均价
        
        # 近期跌幅评分
        if snapshot.diff_1m and float(snapshot.diff_1m) < -20:
            score += 5  # 近期大幅下跌，增加回归潜力
        
        return score
    
    def _calculate_fundamental_value(self, snapshot: MarketSnapshot) -> float:
        """计算基本面价值评分"""
        score = 0.0
        
        # 稀缺性评分
        if snapshot.survive_num:
            survive_num = snapshot.survive_num
            if survive_num < 1000:
                score += 15  # 极稀缺
            elif survive_num < 3000:
                score += 12  # 很稀缺
            elif survive_num < 5000:
                score += 10  # 稀缺
            elif survive_num < 10000:
                score += 8   # 较稀缺
            else:
                score += 5   # 一般稀缺
        
        # 价格基础评分（价格越高，基本面价值越强）
        if snapshot.current_price:
            price = float(snapshot.current_price)
            if price >= 5000:
                score += 10  # 高价值饰品
            elif price >= 2000:
                score += 8   # 中高价值
            elif price >= 1000:
                score += 6   # 中等价值
            elif price >= 500:
                score += 4   # 中低价值
            else:
                score += 2   # 低价值
        
        return score
    
    def _calculate_supply_demand(self, snapshot: MarketSnapshot) -> float:
        """计算供需平衡评分"""
        score = 0.0
        
        # 供应变化评分
        if snapshot.sell_nums_1m_rate:
            supply_change = float(snapshot.sell_nums_1m_rate)
            if supply_change < -20:
                score += 15  # 供应大幅减少
            elif supply_change < -10:
                score += 12  # 供应明显减少
            elif supply_change < -5:
                score += 10  # 供应适度减少
            elif supply_change < 0:
                score += 8   # 供应略有减少
        
        # 流通率评分（在售数量/存世量）
        if snapshot.sell_nums and snapshot.survive_num:
            circulation_rate = snapshot.sell_nums / snapshot.survive_num * 100
            if circulation_rate < 2:
                score += 10  # 极低流通率
            elif circulation_rate < 5:
                score += 8   # 低流通率
            elif circulation_rate < 10:
                score += 6   # 中等流通率
            else:
                score += 3   # 高流通率
        
        return score
    
    def _calculate_technical_support(self, snapshot: MarketSnapshot, analysis: AnalysisResult) -> float:
        """计算技术支撑评分"""
        score = 0.0
        
        # 支撑位评分
        if analysis.support_level_1 and snapshot.current_price:
            support = float(analysis.support_level_1)
            current = float(snapshot.current_price)
            
            # 价格接近或略高于支撑位
            support_ratio = current / support
            if 1.0 <= support_ratio <= 1.05:
                score += 10  # 在强支撑位附近
            elif 1.05 < support_ratio <= 1.1:
                score += 8   # 略高于支撑位
            elif 0.95 <= support_ratio < 1.0:
                score += 6   # 略低于支撑位
        
        # RSI超卖评分
        if analysis.rsi_value:
            rsi = float(analysis.rsi_value)
            if rsi < 30:
                score += 10  # 超卖区域
            elif rsi < 40:
                score += 8   # 接近超卖
            elif rsi < 50:
                score += 5   # 偏弱区域
        
        return score
    
    def determine_risk_level(self, snapshot_and_analysis, score: float) -> str:
        """确定价值回归风险等级"""
        snapshot, analysis = snapshot_and_analysis
        risk_factors = 0
        
        # 持续下跌风险
        if snapshot.diff_7d and float(snapshot.diff_7d) < -15:
            risk_factors += 1
        
        # 流动性风险
        if not snapshot.trans_count_1m or snapshot.trans_count_1m < 20:
            risk_factors += 1
        
        # 基本面恶化风险
        if snapshot.sell_nums_1m_rate and float(snapshot.sell_nums_1m_rate) > 20:
            risk_factors += 1  # 供应大幅增加
        
        # 价格基础风险
        if not snapshot.current_price or float(snapshot.current_price) < 200:
            risk_factors += 1
        
        if risk_factors <= 1:
            return "LOW"
        elif risk_factors <= 2:
            return "MEDIUM"
        else:
            return "HIGH"
    
    def generate_recommendation(self, score: float, risk_level: str) -> str:
        """生成价值回归投资建议"""
        if score >= 70:
            return "BUY" if risk_level != "HIGH" else "HOLD"
        elif score >= 55:
            return "HOLD" if risk_level == "LOW" else "WAIT"
        elif score >= 40:
            return "WAIT"
        else:
            return "AVOID"
    
    def generate_analysis_summary(self, snapshot_and_analysis, score: float) -> str:
        """生成价值回归分析摘要"""
        snapshot, analysis = snapshot_and_analysis
        summary_parts = []
        
        # 基础信息
        if snapshot.current_price:
            summary_parts.append(f"价格: ¥{snapshot.current_price}")
        
        # 跌幅信息
        if snapshot.diff_1m:
            summary_parts.append(f"1月跌幅: {snapshot.diff_1m:.1f}%")
        
        # 稀缺性
        if snapshot.survive_num:
            summary_parts.append(f"存世量: {snapshot.survive_num:,}")
        
        # 供应变化
        if snapshot.sell_nums_1m_rate:
            change_desc = "减少" if float(snapshot.sell_nums_1m_rate) < 0 else "增加"
            summary_parts.append(f"供应{change_desc}: {abs(float(snapshot.sell_nums_1m_rate)):.1f}%")
        
        # 技术支撑
        if analysis.support_level_1:
            summary_parts.append(f"支撑位: ¥{analysis.support_level_1}")
        
        # 评分总结
        if score >= 70:
            summary_parts.append("回归潜力: 很高")
        elif score >= 55:
            summary_parts.append("回归潜力: 较高")
        elif score >= 40:
            summary_parts.append("回归潜力: 一般")
        else:
            summary_parts.append("回归潜力: 较低")
        
        return "; ".join(summary_parts)
