#!/usr/bin/env python3
"""
检查缺失搬砖率的饰品

执行SQL查询，分析有多少饰品可以计算搬砖率但是却没有保存到items表的arbitrage_ratio字段
"""

import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.config.database import get_db_session
from src.cs2_investment.utils.logger import get_logger
from sqlalchemy import text

logger = get_logger(__name__)


def execute_sql_query(session, query_name, sql_query):
    """执行SQL查询并显示结果"""
    try:
        logger.info(f"🔍 执行查询: {query_name}")
        result = session.execute(text(sql_query))
        
        # 获取列名
        columns = result.keys()
        rows = result.fetchall()
        
        if not rows:
            logger.info("  📊 查询结果为空")
            return
        
        # 显示结果
        logger.info(f"  📊 查询结果 ({len(rows)} 行):")
        
        # 显示表头
        header = " | ".join([f"{col:20}" for col in columns])
        logger.info(f"  {header}")
        logger.info(f"  {'-' * len(header)}")
        
        # 显示数据行
        for row in rows:
            row_data = " | ".join([f"{str(val):20}" for val in row])
            logger.info(f"  {row_data}")
        
        logger.info("")
        
    except Exception as e:
        logger.error(f"❌ 查询执行失败: {query_name} - {e}")


def main():
    """主函数"""
    logger.info("🚀 开始检查缺失搬砖率的饰品")
    
    try:
        with get_db_session() as session:
            
            # 1. 查询有价格数据但没有搬砖率的饰品数量
            query1 = """
            SELECT 
                '有价格数据但没有搬砖率的饰品数量' as description,
                COUNT(DISTINCT i.item_id) as count
            FROM items i
            INNER JOIN platform_prices pp ON i.item_id = pp.item_id
            WHERE pp.is_active = 1
              AND pp.sell_price > 0
              AND (i.arbitrage_ratio IS NULL OR i.arbitrage_ratio = 0)
              AND i.market_hash_name IS NOT NULL
            """
            execute_sql_query(session, "有价格数据但没有搬砖率的饰品数量", query1)
            
            # 2. 查询具体可以计算搬砖率的饰品（有BUFF/YOUPIN和Steam价格）
            query2 = """
            SELECT 
                '可以计算搬砖率但未计算的饰品' as description,
                COUNT(DISTINCT item_id) as count
            FROM (
                SELECT 
                    i.item_id,
                    i.market_hash_name,
                    i.arbitrage_ratio,
                    SUM(CASE WHEN pp.platform_name IN ('BUFF', 'YOUPIN') AND pp.sell_price > 0 THEN 1 ELSE 0 END) as non_steam_platforms,
                    SUM(CASE WHEN pp.platform_name = 'STEAM' AND pp.sell_price > 0 THEN 1 ELSE 0 END) as steam_platforms
                FROM items i
                INNER JOIN platform_prices pp ON i.item_id = pp.item_id
                WHERE pp.is_active = 1
                  AND i.market_hash_name IS NOT NULL
                  AND (i.arbitrage_ratio IS NULL OR i.arbitrage_ratio = 0)
                GROUP BY i.item_id, i.market_hash_name, i.arbitrage_ratio
                HAVING non_steam_platforms > 0 AND steam_platforms > 0
            ) as calculable_items
            """
            execute_sql_query(session, "可以计算搬砖率但未计算的饰品", query2)
            
            # 3. 查询各平台价格数据统计
            query3 = """
            SELECT 
                pp.platform_name,
                COUNT(DISTINCT pp.item_id) as unique_items,
                COUNT(*) as total_records,
                ROUND(AVG(pp.sell_price), 2) as avg_sell_price
            FROM platform_prices pp
            WHERE pp.is_active = 1 
              AND pp.sell_price > 0
            GROUP BY pp.platform_name
            ORDER BY unique_items DESC
            """
            execute_sql_query(session, "各平台价格数据统计", query3)
            
            # 4. 查询已有搬砖率的饰品统计
            query4 = """
            SELECT 
                COUNT(*) as items_with_arbitrage,
                ROUND(AVG(arbitrage_ratio), 6) as avg_arbitrage_ratio,
                ROUND(MIN(arbitrage_ratio), 6) as min_arbitrage_ratio,
                ROUND(MAX(arbitrage_ratio), 6) as max_arbitrage_ratio,
                COUNT(CASE WHEN arbitrage_ratio < 0.5 THEN 1 END) as low_arbitrage_count,
                COUNT(CASE WHEN arbitrage_ratio BETWEEN 0.5 AND 0.8 THEN 1 END) as medium_arbitrage_count,
                COUNT(CASE WHEN arbitrage_ratio > 0.8 THEN 1 END) as high_arbitrage_count
            FROM items 
            WHERE arbitrage_ratio IS NOT NULL 
              AND arbitrage_ratio > 0
            """
            execute_sql_query(session, "已有搬砖率的饰品统计", query4)
            
            # 5. 详细查看前10个可以计算搬砖率但未计算的饰品
            query5 = """
            SELECT 
                i.item_id,
                i.market_hash_name,
                ROUND(MIN(CASE WHEN pp.platform_name = 'BUFF' AND pp.sell_price > 0 THEN pp.sell_price END), 2) as buff_price,
                ROUND(MIN(CASE WHEN pp.platform_name = 'YOUPIN' AND pp.sell_price > 0 THEN pp.sell_price END), 2) as youpin_price,
                ROUND(MIN(CASE WHEN pp.platform_name = 'STEAM' AND pp.sell_price > 0 THEN pp.sell_price END), 2) as steam_price,
                ROUND(
                    CASE 
                        WHEN MIN(CASE WHEN pp.platform_name = 'STEAM' AND pp.sell_price > 0 THEN pp.sell_price END) > 0
                        THEN MIN(CASE WHEN pp.platform_name IN ('BUFF', 'YOUPIN') AND pp.sell_price > 0 THEN pp.sell_price END) / 
                             MIN(CASE WHEN pp.platform_name = 'STEAM' AND pp.sell_price > 0 THEN pp.sell_price END)
                        ELSE NULL 
                    END, 6
                ) as calculated_arbitrage_ratio
            FROM items i
            INNER JOIN platform_prices pp ON i.item_id = pp.item_id
            WHERE pp.is_active = 1
              AND i.market_hash_name IS NOT NULL
              AND (i.arbitrage_ratio IS NULL OR i.arbitrage_ratio = 0)
            GROUP BY i.item_id, i.market_hash_name, i.arbitrage_ratio
            HAVING 
                MIN(CASE WHEN pp.platform_name IN ('BUFF', 'YOUPIN') AND pp.sell_price > 0 THEN pp.sell_price END) IS NOT NULL
                AND MIN(CASE WHEN pp.platform_name = 'STEAM' AND pp.sell_price > 0 THEN pp.sell_price END) IS NOT NULL
            ORDER BY calculated_arbitrage_ratio ASC
            LIMIT 10
            """
            execute_sql_query(session, "可计算但未计算搬砖率的饰品详情(前10个)", query5)
            
            # 6. 查询最近更新但没有搬砖率的饰品
            query6 = """
            SELECT 
                i.item_id,
                i.market_hash_name,
                i.last_price_update,
                COUNT(DISTINCT pp.platform_name) as platform_count,
                MAX(pp.update_time) as latest_price_update
            FROM items i
            INNER JOIN platform_prices pp ON i.item_id = pp.item_id
            WHERE pp.is_active = 1
              AND pp.sell_price > 0
              AND i.market_hash_name IS NOT NULL
              AND (i.arbitrage_ratio IS NULL OR i.arbitrage_ratio = 0)
              AND i.last_price_update >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            GROUP BY i.item_id, i.market_hash_name, i.arbitrage_ratio, i.last_price_update
            HAVING platform_count >= 2
            ORDER BY i.last_price_update DESC
            LIMIT 5
            """
            execute_sql_query(session, "最近更新但没有搬砖率的饰品(前5个)", query6)
            
        logger.info("✅ 搬砖率缺失分析完成")
        
    except Exception as e:
        logger.error(f"❌ 分析过程中发生异常: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")


if __name__ == "__main__":
    main()
