-- 查询有多少饰品可以计算搬砖率但是却没有保存到items表的arbitrage_ratio字段

-- 1. 查询有价格数据但没有搬砖率的饰品数量
SELECT 
    '有价格数据但没有搬砖率的饰品数量' as description,
    COUNT(DISTINCT i.item_id) as count
FROM items i
INNER JOIN platform_prices pp ON i.item_id = pp.item_id
WHERE pp.is_active = 1
  AND pp.sell_price > 0
  AND (i.arbitrage_ratio IS NULL OR i.arbitrage_ratio = 0)
  AND i.market_hash_name IS NOT NULL;

-- 2. 查询具体可以计算搬砖率的饰品（有BUFF/YOUPIN和Steam价格）
SELECT 
    '可以计算搬砖率但未计算的饰品' as description,
    COUNT(DISTINCT item_id) as count
FROM (
    SELECT 
        i.item_id,
        i.market_hash_name,
        i.arbitrage_ratio,
        -- 统计各平台价格
        SUM(CASE WHEN pp.platform_name IN ('BUFF', 'YOUPIN') AND pp.sell_price > 0 THEN 1 ELSE 0 END) as non_steam_platforms,
        SUM(CASE WHEN pp.platform_name = 'STEAM' AND pp.sell_price > 0 THEN 1 ELSE 0 END) as steam_platforms
    FROM items i
    INNER JOIN platform_prices pp ON i.item_id = pp.item_id
    WHERE pp.is_active = 1
      AND i.market_hash_name IS NOT NULL
      AND (i.arbitrage_ratio IS NULL OR i.arbitrage_ratio = 0)
    GROUP BY i.item_id, i.market_hash_name, i.arbitrage_ratio
    HAVING non_steam_platforms > 0 AND steam_platforms > 0
) as calculable_items;

-- 3. 查询各平台价格数据统计
SELECT 
    '各平台价格数据统计' as description,
    pp.platform_name,
    COUNT(DISTINCT pp.item_id) as unique_items,
    COUNT(*) as total_records,
    AVG(pp.sell_price) as avg_sell_price,
    MIN(pp.update_time) as earliest_update,
    MAX(pp.update_time) as latest_update
FROM platform_prices pp
WHERE pp.is_active = 1 
  AND pp.sell_price > 0
GROUP BY pp.platform_name
ORDER BY unique_items DESC;

-- 4. 查询已有搬砖率的饰品统计
SELECT 
    '已有搬砖率的饰品统计' as description,
    COUNT(*) as items_with_arbitrage,
    AVG(arbitrage_ratio) as avg_arbitrage_ratio,
    MIN(arbitrage_ratio) as min_arbitrage_ratio,
    MAX(arbitrage_ratio) as max_arbitrage_ratio,
    COUNT(CASE WHEN arbitrage_ratio < 0.5 THEN 1 END) as low_arbitrage_count,
    COUNT(CASE WHEN arbitrage_ratio BETWEEN 0.5 AND 0.8 THEN 1 END) as medium_arbitrage_count,
    COUNT(CASE WHEN arbitrage_ratio > 0.8 THEN 1 END) as high_arbitrage_count
FROM items 
WHERE arbitrage_ratio IS NOT NULL 
  AND arbitrage_ratio > 0;

-- 5. 详细查看前20个可以计算搬砖率但未计算的饰品
SELECT 
    '可计算但未计算搬砖率的饰品详情(前20个)' as description,
    i.item_id,
    i.market_hash_name,
    i.arbitrage_ratio,
    -- BUFF/YOUPIN价格
    MIN(CASE WHEN pp.platform_name = 'BUFF' AND pp.sell_price > 0 THEN pp.sell_price END) as buff_price,
    MIN(CASE WHEN pp.platform_name = 'YOUPIN' AND pp.sell_price > 0 THEN pp.sell_price END) as youpin_price,
    -- Steam价格
    MIN(CASE WHEN pp.platform_name = 'STEAM' AND pp.sell_price > 0 THEN pp.sell_price END) as steam_price,
    -- 最低非Steam价格
    MIN(CASE WHEN pp.platform_name IN ('BUFF', 'YOUPIN') AND pp.sell_price > 0 THEN pp.sell_price END) as min_non_steam_price,
    -- 理论搬砖率
    CASE 
        WHEN MIN(CASE WHEN pp.platform_name = 'STEAM' AND pp.sell_price > 0 THEN pp.sell_price END) > 0
        THEN MIN(CASE WHEN pp.platform_name IN ('BUFF', 'YOUPIN') AND pp.sell_price > 0 THEN pp.sell_price END) / 
             MIN(CASE WHEN pp.platform_name = 'STEAM' AND pp.sell_price > 0 THEN pp.sell_price END)
        ELSE NULL 
    END as calculated_arbitrage_ratio
FROM items i
INNER JOIN platform_prices pp ON i.item_id = pp.item_id
WHERE pp.is_active = 1
  AND i.market_hash_name IS NOT NULL
  AND (i.arbitrage_ratio IS NULL OR i.arbitrage_ratio = 0)
GROUP BY i.item_id, i.market_hash_name, i.arbitrage_ratio
HAVING 
    -- 有BUFF或YOUPIN价格
    MIN(CASE WHEN pp.platform_name IN ('BUFF', 'YOUPIN') AND pp.sell_price > 0 THEN pp.sell_price END) IS NOT NULL
    -- 有Steam价格
    AND MIN(CASE WHEN pp.platform_name = 'STEAM' AND pp.sell_price > 0 THEN pp.sell_price END) IS NOT NULL
ORDER BY calculated_arbitrage_ratio ASC
LIMIT 20;

-- 6. 查询最近更新但没有搬砖率的饰品
SELECT 
    '最近更新但没有搬砖率的饰品(前10个)' as description,
    i.item_id,
    i.market_hash_name,
    i.arbitrage_ratio,
    i.last_price_update,
    COUNT(DISTINCT pp.platform_name) as platform_count,
    GROUP_CONCAT(DISTINCT pp.platform_name) as platforms,
    MAX(pp.update_time) as latest_price_update
FROM items i
INNER JOIN platform_prices pp ON i.item_id = pp.item_id
WHERE pp.is_active = 1
  AND pp.sell_price > 0
  AND i.market_hash_name IS NOT NULL
  AND (i.arbitrage_ratio IS NULL OR i.arbitrage_ratio = 0)
  AND i.last_price_update >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY i.item_id, i.market_hash_name, i.arbitrage_ratio, i.last_price_update
HAVING platform_count >= 2
ORDER BY i.last_price_update DESC
LIMIT 10;
