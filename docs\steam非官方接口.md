# Steam 非官方公开接口文档 (Unofficial API)

## ⚠️ 重要声明

- **非官方性质**: 本文档所列接口均为社区通过逆向工程发现，并非由Valve官方提供支持。Valve可以随时更改、限制或禁用这些接口，恕不另行通知。
- **使用风险**: 滥用这些接口（尤其是写入操作）或进行超高频率的请求，可能导致您的账户或IP被Steam临时或永久限制功能。请自行承担使用风险。
- **身份验证**: 任何涉及用户私人信息或写入操作的接口，都需要模拟登录并携带有效的 sessionid Cookie。

**最后更新**：2025年08月09日

---

## 📋 目录

### 1. [社区市场 (Community Market)](#1-社区市场-community-market)
- 1.1 [获取物品价格总览](#11-获取物品价格总览-marketpriceoverview)
- 1.2 [获取物品价格历史](#12-获取物品价格历史-marketpricehistory)
- 1.3 [获取物品订单信息](#13-获取物品订单信息-marketitemordershistogram)

### 2. [库存与经济 (Inventory & Economy)](#2-库存与经济-inventory--economy)
- 2.1 [获取用户公开库存](#21-获取用户公开库存-inventoryjson)

### 3. [社区与个人资料 (Community & Profiles)](#3-社区与个人资料-community--profiles)
- 3.1 [获取XML格式的个人资料](#31-获取xml格式的个人资料-xml1)
- 3.2 [在个人资料页发表评论](#32-在个人资料页发表评论-commentprofilepost)

### 4. [如何自行发现更多接口](#4-如何自行发现更多接口)

---

## 1. 社区市场 (Community Market)

### 1.1 获取物品价格总览 (/market/priceoverview)

**用途**: 快速查询某个物品的当前最低售价、24小时内的交易量以及历史售价中位数。

- **请求方式**: `GET`
- **地址**: `https://steamcommunity.com/market/priceoverview/`

#### 主要参数

| 参数 | 类型 | 描述 |
|------|------|------|
| `appid` | Integer | 游戏的AppID。例如，CS:GO (CS2) 是 730。 |
| `currency` | Integer | 货币代码。例如 1 是美元(USD), 8 是日元(JPY), 23 是人民币(CNY)。 |
| `market_hash_name` | String | 物品的唯一市场名称，需要进行URL编码。 |

#### 请求示例

```
https://steamcommunity.com/market/priceoverview/?appid=730&currency=1&market_hash_name=AK-47%20%7C%20Redline%20%28Field-Tested%29
```

#### 返回示例

```json
{
  "success": true,
  "lowest_price": "$11.99",
  "volume": "1,234",
  "median_price": "$11.50"
}
```

---

### 1.2 获取物品价格历史 (/market/pricehistory)

**用途**: 获取某个物品的历史成交价（中位数），用于绘制长期价格走势图。

- **请求方式**: `GET`
- **地址**: `https://steamcommunity.com/market/pricehistory/`

#### 主要参数

| 参数 | 类型 | 描述 |
|------|------|------|
| `appid` | Integer | 游戏的AppID。 |
| `market_hash_name` | String | 物品的唯一市场名称，需要URL编码。 |

#### 请求示例

```
https://steamcommunity.com/market/pricehistory/?appid=730&market_hash_name=AK-47%20%7C%20Redline%20%28Field-Tested%29
```

#### 返回示例

```json
{
  "success": true,
  "price_history": [
    [ "Aug 01 2025 01: +0", 11.50, "15" ],
    [ "Aug 02 2025 01: +0", 11.55, "22" ],
    [ "Aug 03 2025 01: +0", 11.60, "18" ]
  ]
}
```

---

### 1.3 获取物品订单信息 (/market/itemordershistogram)

**用途**: 获取某个物品详细的买卖盘订单数据，包括最高求购价和最低出售价。

- **请求方式**: `GET`
- **地址**: `https://steamcommunity.com/market/itemordershistogram/`

#### 主要参数

| 参数 | 类型 | 描述 |
|------|------|------|
| `country` | String | 国家代码，例如 US, JP, CN。 |
| `language` | String | 语言，例如 english, schinese (简体中文)。 |
| `currency` | Integer | 货币代码。 |
| `item_nameid` | Integer | 物品的内部ID，需从物品市场页面的网页源代码中获取。 |
| `two_factor` | Integer | 固定为 0。 |

#### 请求示例

```
https://steamcommunity.com/market/itemordershistogram?country=US&language=schinese&currency=1&item_nameid=176414125&two_factor=0
```

#### 返回示例

```json
{
  "success": 1,
  "sell_order_graph": [ [ 0.55, 10, "10 @ $0.55" ] ],
  "buy_order_graph": [ [ 0.50, 150, "150 @ $0.50" ] ],
  "highest_buy_order": "0.50",
  "lowest_sell_order": "0.55",
  "price_prefix": "$",
  "price_suffix": ""
}
```

---

## 2. 库存与经济 (Inventory & Economy)

### 2.1 获取用户公开库存 (/inventory/json)

**用途**: 以对程序更友好的JSON格式获取指定用户的公开库存。

- **请求方式**: `GET`
- **地址**: `https://steamcommunity.com/inventory/<SteamID64>/<AppID>/<ContextID>`

#### URL内嵌参数

| 参数 | 描述 |
|------|------|
| `<SteamID64>` | 用户的64位SteamID。 |
| `<AppID>` | 游戏的AppID。 |
| `<ContextID>` | 库存上下文ID。对CS:GO/Dota2/TF2等大部分游戏，常规物品的ID是 2。 |

#### 请求示例

```
https://steamcommunity.com/inventory/76561197960287930/730/2
```

#### 返回示例

```json
{
  "assets": [ { "appid": 730, "assetid": "...", "instanceid": "..." } ],
  "descriptions": [ { "appid": 730, "market_hash_name": "...", "name": "..." } ],
  "total_inventory_count": 123,
  "success": 1
}
```

---

## 3. 社区与个人资料 (Community & Profiles)

### 3.1 获取XML格式的个人资料 (?xml=1)

**用途**: 以XML格式获取用户的公开个人资料信息。

- **请求方式**: `GET`
- **地址**: 在用户个人资料URL后添加 `?xml=1`

#### 请求示例

```
https://steamcommunity.com/id/gabelogannewell/?xml=1
```

#### 返回示例

```xml
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<profile>
    <steamID64>76561197960287930</steamID64>
    <steamID><![CDATA[gabelogannewell]]></steamID>
    <onlineState>online</onlineState>
    <stateMessage>Online</stateMessage>
    ...
</profile>
```

---

### 3.2 在个人资料页发表评论 (/comment/Profile/post)

**用途**: [写入操作] 在某用户的个人资料页上发表评论。需要有效登录凭证。

- **请求方式**: `POST`
- **地址**: `https://steamcommunity.com/comment/Profile/post/<ProfileOwnerSteamID64>/`

#### URL内嵌参数

| 参数 | 描述 |
|------|------|
| `<ProfileOwnerSteamID64>` | 要评论的个人资料所有者的64位ID。 |

#### POST表单数据

| 参数 | 描述 |
|------|------|
| `comment` | 评论的文本内容。 |
| `sessionid` | 从已登录的浏览器Cookie中获取的会话ID。 |

---

## 4. 如何自行发现更多接口

您可以通过现代浏览器的开发者工具，亲自探索Steam网站的运作方式：

### 🔧 操作步骤

1. **打开开发者工具**: 在您的浏览器中，按 `F12` 键。

2. **切换到"网络(Network)"标签页**。

3. **筛选请求类型**: 在筛选器中，点击 `Fetch/XHR`。这会只显示网页后台与服务器进行数据交换的请求。

4. **在网页上进行操作**: 在Steam网站上执行您感兴趣的任何操作（例如搜索市场、切换库存页面、给好友点赞等）。

5. **观察网络活动**: 观察"网络"标签页中出现的新条目。点击这些条目，即可详细查看它们的请求URL、方法、头部信息、发送的数据，以及服务器返回的响应内容。

---

## 📝 注意事项

- 请遵守Steam的使用条款和服务协议
- 合理控制请求频率，避免对Steam服务器造成过大负担
- 定期检查接口是否仍然有效，因为Steam可能随时更改这些接口
- 对于需要登录的接口，请确保妥善保护您的登录凭证

---

## 📚 相关资源

- [Steam Web API 官方文档](https://steamcommunity.com/dev)
- [Steam 开发者社区](https://steamcommunity.com/groups/steamworks)
- [Valve 开发者网站](https://partner.steamgames.com/)

---

*本文档仅供学习和研究目的使用，请遵守相关法律法规和服务条款。*
