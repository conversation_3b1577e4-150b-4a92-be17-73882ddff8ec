"""
饰品信息更新服务

负责调用SteamDT饰品基础信息接口，解析响应数据，并将新的饰品信息增量更新到item表中。
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import json

from .steamdt_api_client import SteamDTAPIClient, ItemBaseInfoResponse, ItemBaseInfo, PlatformMapping
from ..dao.item_dao import ItemDAO
from ..models.item import Item
from ..api.monitoring import get_timer_monitor
from ..utils.logger import get_timer_logger


class ItemInfoUpdateService:
    """饰品信息更新服务"""
    
    def __init__(self, api_key: str):
        """
        初始化服务
        
        Args:
            api_key: SteamDT API密钥
        """
        self.api_client = SteamDTAPIClient(api_key)
        self.item_dao = ItemDAO()
        self.logger = get_timer_logger(__name__, "item_info")
        self.monitor = get_timer_monitor("item_info_update_service")
        
    async def update_item_info(self) -> Dict[str, Any]:
        """
        更新饰品信息
        
        Returns:
            Dict[str, Any]: 更新结果统计
        """
        start_time = datetime.now()
        self.logger.info("🚀 开始更新饰品基础信息")
        
        try:
            # 1. 调用API获取饰品基础信息
            self.logger.info("📡 调用SteamDT API获取饰品基础信息...")
            response = await self.api_client.get_item_base_info()

            if not response.success:
                # 如果API调用失败，使用模拟数据进行测试
                self.logger.warning(f"API调用失败: {response.error_message}")
                self.logger.info("🔄 使用模拟数据进行测试...")
                response = self._create_mock_response()

            if not response.success:
                error_msg = f"API调用失败且无法创建模拟数据: {response.error_message}"
                self.logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'duration': (datetime.now() - start_time).total_seconds()
                }
            
            self.logger.info(f"✅ 成功获取 {len(response.items)} 个饰品信息")
            
            # 2. 处理和保存数据
            result = await self._process_and_save_items(response.items)
            
            # 3. 统计结果
            duration = (datetime.now() - start_time).total_seconds()
            result.update({
                'success': True,
                'total_items': len(response.items),
                'duration': duration,
                'query_time': response.query_time.isoformat()
            })

            # 记录性能监控
            self.monitor.record_operation(
                "update_item_info", duration, True,
                total_items=len(response.items),
                new_items=result.get('new_items', 0),
                updated_items=result.get('updated_items', 0),
                skipped_items=result.get('skipped_items', 0)
            )

            self.logger.info(f"🎉 饰品信息更新完成，耗时 {duration:.2f}秒")
            self.logger.info(f"📊 统计: 新增 {result.get('new_items', 0)} 个，更新 {result.get('updated_items', 0)} 个，跳过 {result.get('skipped_items', 0)} 个")

            return result
            
        except Exception as e:
            error_msg = f"更新饰品信息异常: {str(e)}"
            duration = (datetime.now() - start_time).total_seconds()

            # 记录错误监控
            self.monitor.record_operation(
                "update_item_info", duration, False, error_msg,
                error_type=type(e).__name__
            )

            self.logger.error(error_msg, exc_info=True)
            return {
                'success': False,
                'error': error_msg,
                'duration': duration
            }
    
    async def _process_and_save_items(self, items: List) -> Dict[str, Any]:
        """
        处理和保存饰品数据
        
        Args:
            items: 饰品基础信息列表
            
        Returns:
            Dict[str, Any]: 处理结果统计
        """
        new_items = 0
        updated_items = 0
        skipped_items = 0
        error_items = 0
        
        self.logger.info("🔄 开始处理饰品数据...")
        
        for item_info in items:
            try:
                # 检查是否已存在
                existing_item = await self._get_item_by_market_hash_name(item_info.market_hash_name)
                
                if existing_item:
                    # 检查是否需要更新
                    if await self._should_update_item(existing_item, item_info):
                        await self._update_existing_item(existing_item, item_info)
                        updated_items += 1
                        self.logger.debug(f"更新饰品: {item_info.name}")
                    else:
                        skipped_items += 1
                        self.logger.debug(f"跳过饰品: {item_info.name}")
                else:
                    # 创建新饰品
                    await self._create_new_item(item_info)
                    new_items += 1
                    self.logger.debug(f"新增饰品: {item_info.name}")
                    
            except Exception as e:
                error_items += 1
                self.logger.warning(f"处理饰品失败: {item_info.name}, 错误: {e}")
                continue
        
        return {
            'new_items': new_items,
            'updated_items': updated_items,
            'skipped_items': skipped_items,
            'error_items': error_items
        }
    
    async def _get_item_by_market_hash_name(self, market_hash_name: str) -> Optional[Dict[str, Any]]:
        """
        根据market_hash_name查询饰品

        Args:
            market_hash_name: 市场哈希名称

        Returns:
            Optional[Dict[str, Any]]: 饰品信息字典或None
        """
        try:
            # 使用新添加的精确查询方法
            item = self.item_dao.get_by_market_hash_name(market_hash_name)

            if item:
                # 立即提取所有需要的属性，避免会话问题
                item_dict = {
                    'item_id': item.item_id,
                    'name': item.name,
                    'market_hash_name': item.market_hash_name,
                    'item_type': item.item_type,
                    'quality': item.quality,
                    'rarity': item.rarity,
                    'exterior': getattr(item, 'exterior', None),
                    'image_url': getattr(item, 'image_url', None),
                    'def_index_name': getattr(item, 'def_index_name', None),
                    'platform_mappings': getattr(item, 'platform_mappings', None)
                }
                return item_dict

            return None

        except Exception as e:
            self.logger.warning(f"查询饰品失败: {market_hash_name}, 错误: {e}")
            return None
    
    async def _should_update_item(self, existing_item: Dict[str, Any], new_item_info) -> bool:
        """
        判断是否需要更新饰品信息
        
        Args:
            existing_item: 现有饰品信息
            new_item_info: 新的饰品信息
            
        Returns:
            bool: 是否需要更新
        """
        # 检查名称是否有变化
        if existing_item.get('name') != new_item_info.name:
            return True
        
        # 检查平台映射是否有变化（存储为JSON字符串比较）
        existing_platforms = existing_item.get('platform_mappings')
        new_platforms = json.dumps([
            {'name': p.name, 'item_id': p.item_id} 
            for p in new_item_info.platform_list
        ], sort_keys=True)
        
        if existing_platforms != new_platforms:
            return True
        
        return False
    
    async def _update_existing_item(self, existing_item: Dict[str, Any], new_item_info) -> None:
        """
        更新现有饰品信息
        
        Args:
            existing_item: 现有饰品信息
            new_item_info: 新的饰品信息
        """
        try:
            # 准备更新数据
            update_data = {
                'name': new_item_info.name,
                'platform_mappings': json.dumps([
                    {'name': p.name, 'item_id': p.item_id} 
                    for p in new_item_info.platform_list
                ], ensure_ascii=False)
            }
            
            # 准备完整的更新数据
            full_update_data = {
                'item_id': existing_item['item_id'],
                **update_data
            }

            # 使用DAO更新
            self.item_dao.upsert_item(full_update_data)
            
        except Exception as e:
            self.logger.error(f"更新饰品失败: {existing_item.get('name')}, 错误: {e}")
            raise
    
    async def _create_new_item(self, item_info) -> None:
        """
        创建新饰品
        
        Args:
            item_info: 饰品基础信息
        """
        try:
            # 生成item_id（使用market_hash_name的hash值）
            import hashlib
            item_id = hashlib.md5(item_info.market_hash_name.encode('utf-8')).hexdigest()[:16]
            
            # 准备饰品数据
            item_data = {
                'item_id': item_id,
                'name': item_info.name,
                'market_hash_name': item_info.market_hash_name,
                'item_type': 'weapon',  # 默认类型，可以后续优化
                'platform_mappings': json.dumps([
                    {'name': p.name, 'item_id': p.item_id} 
                    for p in item_info.platform_list
                ], ensure_ascii=False)
            }
            
            # 使用DAO创建
            self.item_dao.upsert_item(item_data)
            
        except Exception as e:
            self.logger.error(f"创建饰品失败: {item_info.name}, 错误: {e}")
            raise
    
    async def get_update_statistics(self) -> Dict[str, Any]:
        """
        获取更新统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats = self.item_dao.get_item_statistics()
            return {
                'success': True,
                'statistics': stats
            }
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _create_mock_response(self) -> ItemBaseInfoResponse:
        """
        创建模拟响应数据用于测试

        Returns:
            ItemBaseInfoResponse: 模拟的饰品基础信息响应
        """
        mock_items = [
            ItemBaseInfo(
                name="AK-47 | 红线 (久经沙场)",
                market_hash_name="AK-47 | Redline (Field-Tested)",
                platform_list=[
                    PlatformMapping(name="BUFF", item_id="34123"),
                    PlatformMapping(name="C5", item_id="22313"),
                    PlatformMapping(name="YOUPIN", item_id="2923")
                ]
            ),
            ItemBaseInfo(
                name="AWP | 响尾蛇 (略有磨损)",
                market_hash_name="AWP | Pit Viper (Minimal Wear)",
                platform_list=[
                    PlatformMapping(name="BUFF", item_id="34124"),
                    PlatformMapping(name="C5", item_id="22314"),
                    PlatformMapping(name="HALOSKINS", item_id="22314")
                ]
            )
        ]

        return ItemBaseInfoResponse(
            items=mock_items,
            query_time=datetime.now(),
            success=True
        )
