"""
统一分析接口

提供统一的分析接口，整合智能分析系统和传统系统，
为上层应用提供一致的API接口。
"""

import asyncio
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入核心分析组件
from ..core.decision_fusion_engine import DecisionFusionEngine
from ..core.multi_timeframe_engine import MultiTimeframeEngine
from ..core.unified_data_manager import UnifiedDataManager

# 导入数据库相关组件
try:
    from ....dao.intelligent_analysis_result_dao import IntelligentAnalysisResultDAO
    logger.debug("IntelligentAnalysisResultDAO 导入成功")

    from ....services.investment_recommendation_service import InvestmentRecommendationService
    logger.debug("InvestmentRecommendationService 导入成功")

    from ....config.database import get_db_session, test_database_connection
    logger.debug("数据库配置组件导入成功")

    DATABASE_AVAILABLE = True
    logger.info("数据库组件导入成功")
except ImportError as e:
    logger.warning(f"数据库组件导入失败: {e}")
    import traceback
    logger.warning(f"详细错误信息: {traceback.format_exc()}")
    DATABASE_AVAILABLE = False
except Exception as e:
    logger.error(f"数据库组件导入异常: {e}")
    import traceback
    logger.error(f"详细错误信息: {traceback.format_exc()}")
    DATABASE_AVAILABLE = False


class AnalysisMode(Enum):
    """分析模式"""
    INTELLIGENT_ONLY = "intelligent_only"      # 仅智能分析
    LEGACY_ONLY = "legacy_only"               # 仅传统分析
    INTELLIGENT_WITH_FALLBACK = "intelligent_with_fallback"  # 智能分析+传统回退
    COMPARISON = "comparison"                  # 对比分析
    AUTO = "auto"                             # 自动选择


class UnifiedAnalysisInterface:
    """统一分析接口"""

    def __init__(self, data_base_path: str = "data/scraped_data"):
        """
        初始化统一分析接口

        Args:
            data_base_path: 数据基础路径
        """
        self.data_base_path = data_base_path
        self.logger = logger.bind(interface=self.__class__.__name__)

        # 初始化核心分析组件
        self.decision_fusion_engines: Dict[str, DecisionFusionEngine] = {}
        self.multi_timeframe_engines: Dict[str, MultiTimeframeEngine] = {}

        # 初始化数据库组件
        if DATABASE_AVAILABLE:
            self.intelligent_dao = IntelligentAnalysisResultDAO()
            self.investment_service = InvestmentRecommendationService()
            self.db_available = False  # 将在初始化时测试
        else:
            self.intelligent_dao = None
            self.investment_service = None
            self.db_available = False

        # 默认配置
        self.default_config = {
            'analysis_mode': AnalysisMode.INTELLIGENT_WITH_FALLBACK,
            'enable_caching': True,
            'timeout_seconds': 300,
            'max_retries': 2,
            'save_to_database': True  # 默认启用数据库保存
        }

        self.initialized = False
        
        self.logger.info("统一分析接口初始化完成")
    
    async def initialize(self):
        """初始化接口"""
        try:
            self.logger.info("初始化统一分析接口...")

            # 检查数据库连接
            if DATABASE_AVAILABLE:
                try:
                    # 使用更安全的数据库连接测试
                    from sqlalchemy import create_engine, text
                    from ....config.settings import get_settings

                    settings = get_settings()
                    engine = create_engine(
                        settings.database.url,
                        pool_pre_ping=True,
                        connect_args={"connect_timeout": 5}
                    )

                    with engine.connect() as connection:
                        result = connection.execute(text("SELECT 1"))
                        row = result.fetchone()

                        if row and row[0] == 1:
                            self.db_available = True
                            self.logger.info("数据库连接测试成功，将保存分析结果到数据库")
                        else:
                            self.db_available = False
                            self.logger.warning("数据库连接测试失败，分析结果仅返回不保存")

                except Exception as e:
                    self.logger.warning(f"数据库连接测试异常: {e}，分析结果仅返回不保存")
                    self.db_available = False
            else:
                self.logger.warning("数据库组件不可用，分析结果仅返回不保存")
                self.db_available = False

            self.initialized = True
            self.logger.info("统一分析接口初始化完成")

        except Exception as e:
            self.logger.error(f"统一分析接口初始化失败: {e}")
            raise
    
    async def analyze_item(
        self,
        item_id: str,
        market_hash_name: str = None,
        analysis_mode: AnalysisMode = None,
        analysis_type: str = "comprehensive",
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        分析饰品
        
        Args:
            item_id: 饰品ID
            market_hash_name: 市场哈希名称
            analysis_mode: 分析模式
            analysis_type: 分析类型 (comprehensive, strategic, tactical, execution)
            config: 配置参数
            
        Returns:
            Dict: 分析结果
        """
        try:
            # 合并配置
            effective_config = {**self.default_config}
            if config:
                effective_config.update(config)
            
            # 确定分析模式
            if analysis_mode is None:
                analysis_mode = effective_config['analysis_mode']
            
            self.logger.info(f"开始分析饰品: {item_id} (模式: {analysis_mode.value}, 类型: {analysis_type})")
            
            # 根据分析模式执行分析
            if analysis_mode == AnalysisMode.INTELLIGENT_ONLY:
                result = await self._analyze_intelligent_only(item_id, analysis_type, effective_config)

            elif analysis_mode == AnalysisMode.INTELLIGENT_WITH_FALLBACK:
                result = await self._analyze_intelligent_with_fallback(item_id, analysis_type, effective_config)

            elif analysis_mode == AnalysisMode.AUTO:
                result = await self._analyze_auto(item_id, analysis_type, effective_config)

            else:
                # 默认使用智能分析
                result = await self._analyze_intelligent_only(item_id, analysis_type, effective_config)
                
            # 保存到数据库（如果可用）
            if self.db_available and effective_config.get('save_to_database', False) and result.get('success', False):
                await self._save_analysis_result(item_id, result, analysis_mode, analysis_type)

            # 添加元数据
            result['analysis_metadata'] = {
                'item_id': item_id,
                'analysis_mode': analysis_mode.value,
                'analysis_type': analysis_type,
                'interface_timestamp': datetime.now(),
                'database_saved': effective_config.get('save_to_database', False) and result.get('success', False)
            }

            self.logger.info(f"饰品分析完成: {item_id}")
            return result

        except Exception as e:
            self.logger.error(f"饰品分析失败 ({item_id}): {e}")
            return {
                'item_id': item_id,
                'success': False,
                'error': str(e),
                'analysis_mode': analysis_mode.value if analysis_mode else 'unknown',
                'interface_timestamp': datetime.now()
            }
    
    async def _analyze_intelligent_only(self, item_id: str, analysis_type: str, config: Dict) -> Dict[str, Any]:
        """仅智能分析"""
        try:
            start_time = datetime.now()

            # 执行决策融合分析
            fusion_engine = DecisionFusionEngine(item_id, self.data_base_path)
            fusion_result = fusion_engine.execute_intelligent_fusion()

            if not fusion_result.get('success', False):
                raise Exception(f"决策融合分析失败: {fusion_result.get('error', '未知错误')}")

            # 执行多时间框架分析
            multi_engine = MultiTimeframeEngine(item_id, self.data_base_path)
            if multi_engine.initialize_analyzers():
                multi_result = multi_engine.execute_comprehensive_analysis()
            else:
                multi_result = {'success': False, 'error': '多时间框架分析器初始化失败'}

            # 计算分析耗时
            analysis_duration = (datetime.now() - start_time).total_seconds() * 1000

            return {
                'analysis_mode': 'intelligent_only',
                'success': True,
                'fusion_result': fusion_result,
                'multi_timeframe_result': multi_result,
                'analysis_duration_ms': int(analysis_duration),
                'analysis_timestamp': start_time
            }

        except Exception as e:
            self.logger.error(f"智能分析失败: {e}")
            raise Exception(f"智能分析失败: {str(e)}")
    
    async def _analyze_intelligent_with_fallback(self, item_id: str, analysis_type: str, config: Dict) -> Dict[str, Any]:
        """智能分析+传统回退"""
        try:
            # 首先尝试智能分析
            try:
                return await self._analyze_intelligent_only(item_id, analysis_type, config)
            except Exception as e:
                self.logger.warning(f"智能分析失败，尝试回退: {e}")

                # 回退到基础分析
                return {
                    'analysis_mode': 'intelligent_with_fallback',
                    'success': True,
                    'fallback_used': True,
                    'fallback_reason': str(e),
                    'basic_result': {
                        'recommendation_type': 'HOLD',
                        'confidence_level': 50.0,
                        'risk_level': 'MEDIUM',
                        'note': '使用回退模式，建议谨慎操作'
                    }
                }

        except Exception as e:
            raise Exception(f"智能分析+回退失败: {str(e)}")
    
    async def _analyze_auto(self, item_id: str, analysis_type: str, config: Dict) -> Dict[str, Any]:
        """自动选择最佳分析模式"""
        try:
            # 检查数据可用性
            data_manager = UnifiedDataManager(item_id, self.data_base_path)
            multi_timeframe_data = data_manager.load_multi_timeframe_data()

            if multi_timeframe_data.get('success', False):
                # 数据充足，使用智能分析
                return await self._analyze_intelligent_only(item_id, analysis_type, config)
            else:
                # 数据不足，使用回退模式
                return await self._analyze_intelligent_with_fallback(item_id, analysis_type, config)

        except Exception as e:
            raise Exception(f"自动分析失败: {str(e)}")

    async def _save_analysis_result(self, item_id: str, result: Dict[str, Any],
                                  analysis_mode: AnalysisMode, analysis_type: str):
        """保存分析结果到数据库"""
        try:
            if not self.db_available or not self.intelligent_dao:
                self.logger.warning("数据库不可用，跳过保存分析结果")
                return

            # 提取决策融合结果
            fusion_result = result.get('fusion_result', {})
            final_decision = fusion_result.get('final_decision', {})

            # 提取多时间框架结果
            multi_result = result.get('multi_timeframe_result', {})
            comprehensive_conclusion = multi_result.get('comprehensive_conclusion', {})

            # 提取各时间框架的分析结果
            timeframe_analyses = multi_result.get('timeframe_analyses', {})
            strategic_analysis = timeframe_analyses.get('strategic', {})
            tactical_analysis = timeframe_analyses.get('tactical', {})
            execution_analysis = timeframe_analyses.get('execution', {})
            supply_demand_analysis = timeframe_analyses.get('supply_demand', {})

            # 准备数据库记录，映射到实际的模型字段
            analysis_data = {
                'item_id': str(item_id),  # 保持字符串格式
                'analysis_timestamp': result.get('analysis_timestamp', datetime.now()),

                # 战略分析结果
                'strategic_trend': strategic_analysis.get('trend_direction', 'UNKNOWN'),
                'investment_value_score': strategic_analysis.get('investment_value_score', 0),
                'strategic_risk_level': strategic_analysis.get('risk_level', 'UNKNOWN'),

                # 战术分析结果
                'tactical_trend': tactical_analysis.get('trend_direction', 'UNKNOWN'),
                'entry_signal': tactical_analysis.get('entry_signal', 'UNKNOWN'),
                'exit_signal': tactical_analysis.get('exit_signal', 'UNKNOWN'),
                'tactical_risk_level': tactical_analysis.get('risk_level', 'UNKNOWN'),

                # 执行分析结果
                'immediate_action': execution_analysis.get('immediate_action', 'UNKNOWN'),
                'execution_risk_level': execution_analysis.get('risk_level', 'UNKNOWN'),
                'optimal_entry_price': execution_analysis.get('optimal_entry_price', 0),
                'stop_loss_price': execution_analysis.get('stop_loss_price', 0),
                'take_profit_price_1': execution_analysis.get('take_profit_price_1', 0),
                'take_profit_price_2': execution_analysis.get('take_profit_price_2', 0),

                # 供需分析结果
                'supply_demand_status': supply_demand_analysis.get('status', 'UNKNOWN'),
                'supply_pressure_level': supply_demand_analysis.get('supply_pressure_level', 'UNKNOWN'),
                'demand_strength_level': supply_demand_analysis.get('demand_strength_level', 'UNKNOWN'),
                'liquidity_score': supply_demand_analysis.get('liquidity_score', 0),
                'scarcity_score': supply_demand_analysis.get('scarcity_score', 0),

                # 综合决策结果
                'final_action': final_decision.get('recommendation_type', 'UNKNOWN'),
                'confidence_level': self._map_confidence_level(final_decision.get('confidence_level', 0)),
                'signal_consistency': self._calculate_signal_consistency(comprehensive_conclusion)
            }

            # 使用DAO保存（DAO内部会处理session）
            db_result = self.intelligent_dao.create_analysis_result(analysis_data)
            if db_result:
                self.logger.info(f"分析结果已保存到数据库: {db_result.id}")
                return db_result.id
            else:
                self.logger.warning("分析结果保存到数据库失败")
                return None

        except Exception as e:
            self.logger.error(f"保存分析结果失败: {e}")
            return None

    def _map_confidence_level(self, confidence_score: float) -> str:
        """映射置信度分数到等级"""
        if confidence_score >= 90:
            return 'VERY_HIGH'
        elif confidence_score >= 75:
            return 'HIGH'
        elif confidence_score >= 60:
            return 'MEDIUM'
        elif confidence_score >= 40:
            return 'LOW'
        else:
            return 'VERY_LOW'

    def _calculate_signal_consistency(self, comprehensive_conclusion: Dict) -> str:
        """计算信号一致性"""
        try:
            signals = [
                comprehensive_conclusion.get('strategic_recommendation', ''),
                comprehensive_conclusion.get('tactical_recommendation', ''),
                comprehensive_conclusion.get('execution_recommendation', '')
            ]

            # 过滤空信号
            valid_signals = [s for s in signals if s and s != 'UNKNOWN']
            if not valid_signals:
                return 'UNKNOWN'

            # 计算一致性
            most_common = max(set(valid_signals), key=valid_signals.count)
            consistency_ratio = valid_signals.count(most_common) / len(valid_signals)

            if consistency_ratio >= 0.8:
                return 'HIGH'
            elif consistency_ratio >= 0.6:
                return 'MEDIUM'
            else:
                return 'LOW'

        except Exception:
            return 'UNKNOWN'
    
    def get_recommendation_summary(self, analysis_result: Dict) -> Dict[str, Any]:
        """获取推荐摘要"""
        try:
            if not analysis_result.get('success', False):
                return {
                    'recommendation_type': 'UNKNOWN',
                    'confidence_level': 0.0,
                    'risk_level': 'HIGH',
                    'error': analysis_result.get('error', '分析失败')
                }

            # 从融合结果中提取推荐信息
            fusion_result = analysis_result.get('fusion_result', {})
            final_decision = fusion_result.get('final_decision', {})

            # 从回退结果中提取推荐信息
            basic_result = analysis_result.get('basic_result', {})

            if final_decision:
                return {
                    'recommendation_type': final_decision.get('recommendation_type', 'UNKNOWN'),
                    'confidence_level': final_decision.get('confidence_level', 0.0),
                    'risk_level': final_decision.get('risk_level', 'UNKNOWN'),
                    'total_score': final_decision.get('total_score', 0.0)
                }
            elif basic_result:
                return {
                    'recommendation_type': basic_result.get('recommendation_type', 'UNKNOWN'),
                    'confidence_level': basic_result.get('confidence_level', 0.0),
                    'risk_level': basic_result.get('risk_level', 'UNKNOWN'),
                    'note': basic_result.get('note', '')
                }
            else:
                return {
                    'recommendation_type': 'HOLD',
                    'confidence_level': 50.0,
                    'risk_level': 'MEDIUM',
                    'note': '默认推荐'
                }

        except Exception as e:
            self.logger.error(f"获取推荐摘要失败: {e}")
            return {
                'recommendation_type': 'UNKNOWN',
                'confidence_level': 0.0,
                'risk_level': 'HIGH',
                'error': str(e)
            }

    def get_interface_status(self) -> Dict[str, Any]:
        """获取接口状态"""
        return {
            'status': 'active',
            'initialized': self.initialized,
            'database_components_available': DATABASE_AVAILABLE,
            'database_connection_available': self.db_available,
            'available_components': [
                'DecisionFusionEngine',
                'MultiTimeframeEngine',
                'UnifiedDataManager'
            ],
            'supported_analysis_modes': [mode.value for mode in AnalysisMode],
            'default_config': {k: v.value if isinstance(v, Enum) else v for k, v in self.default_config.items()},
            'timestamp': datetime.now()
        }


# 全局实例
unified_analysis_interface = UnifiedAnalysisInterface()
