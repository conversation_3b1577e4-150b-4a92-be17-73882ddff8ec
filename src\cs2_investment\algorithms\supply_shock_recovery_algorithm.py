"""
供应冲击恢复型筛选算法

识别短期供应冲击后的恢复机会。
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, or_

from .base_algorithm import BaseScreeningAlgorithm
from ..models.market_snapshot import MarketSnapshot


class SupplyShockRecoveryAlgorithm(BaseScreeningAlgorithm):
    """供应冲击恢复型筛选算法"""
    
    def __init__(self):
        super().__init__(
            algorithm_name="SupplyShockRecoveryAlgorithm",
            investment_type="供应冲击恢复型",
            version="1.0"
        )
    
    def get_screening_query(self, session: Session):
        """获取供应冲击恢复型筛选查询"""
        return self.get_latest_snapshots_query(session)\
            .filter(
                and_(
                    # 供应冲击条件
                    or_(
                        MarketSnapshot.sell_nums_1d_rate > 100,
                        MarketSnapshot.sell_nums_7d_rate > 80
                    ),
                    # 价格下跌但不过度
                    MarketSnapshot.diff_7d.between(-30, -5),
                    # 有成交活动
                    or_(
                        MarketSnapshot.trans_count_rate_48h > 0,
                        MarketSnapshot.trans_count_48h > 0
                    )
                )
            )\
            .order_by(desc(MarketSnapshot.sell_nums_1d_rate))
    
    def calculate_score(self, snapshot: MarketSnapshot) -> float:
        """计算供应冲击恢复评分"""
        score = 0.0
        
        # 供应冲击强度评分 (30分)
        supply_shock_score = 0
        if snapshot.sell_nums_1d_rate and float(snapshot.sell_nums_1d_rate) > 0:
            shock_rate = float(snapshot.sell_nums_1d_rate)
            if shock_rate >= 200:
                supply_shock_score = 30
            elif shock_rate >= 150:
                supply_shock_score = 25
            elif shock_rate >= 100:
                supply_shock_score = 20
            elif shock_rate >= 50:
                supply_shock_score = 15
            else:
                supply_shock_score = 10
        elif snapshot.sell_nums_7d_rate and float(snapshot.sell_nums_7d_rate) > 0:
            shock_rate = float(snapshot.sell_nums_7d_rate)
            supply_shock_score = min(shock_rate / 100 * 25, 25)
        
        score += supply_shock_score
        
        # 价格调整合理性评分 (25分)
        if snapshot.diff_7d:
            price_drop = abs(float(snapshot.diff_7d))
            if 5 <= price_drop <= 15:  # 合理调整范围
                score += 25
            elif 15 < price_drop <= 25:
                score += 20
            elif 25 < price_drop <= 30:
                score += 15
            elif price_drop < 5:
                score += 10  # 调整不足
            else:
                score += 5   # 调整过度
        
        # 需求吸收能力评分 (20分)
        absorption_score = 0
        if snapshot.trans_count_rate_48h and float(snapshot.trans_count_rate_48h) > 0:
            absorption_score += 10
        if snapshot.trans_count_48h and snapshot.trans_count_48h > 0:
            if snapshot.trans_count_48h >= 50:
                absorption_score += 10
            elif snapshot.trans_count_48h >= 20:
                absorption_score += 8
            elif snapshot.trans_count_48h >= 10:
                absorption_score += 6
            else:
                absorption_score += 3
        
        score += absorption_score
        
        # 基础价值支撑评分 (15分)
        if snapshot.current_price:
            price = float(snapshot.current_price)
            if price >= 1000:
                score += 15
            elif price >= 500:
                score += 12
            elif price >= 200:
                score += 9
            elif price >= 100:
                score += 6
            else:
                score += 3
        
        # 历史稳定性评分 (10分)
        stability_score = 10
        if snapshot.diff_1m:
            monthly_volatility = abs(float(snapshot.diff_1m))
            if monthly_volatility > 100:
                stability_score = 3
            elif monthly_volatility > 50:
                stability_score = 6
            elif monthly_volatility > 30:
                stability_score = 8
        
        score += stability_score
        
        return min(score, 100.0)
    
    def determine_risk_level(self, snapshot: MarketSnapshot, score: float) -> str:
        """确定供应冲击恢复风险等级"""
        risk_factors = 0
        
        # 供应冲击过度风险
        if snapshot.sell_nums_1d_rate and float(snapshot.sell_nums_1d_rate) > 300:
            risk_factors += 2
        elif snapshot.sell_nums_1d_rate and float(snapshot.sell_nums_1d_rate) > 200:
            risk_factors += 1
        
        # 价格下跌过度风险
        if snapshot.diff_7d and float(snapshot.diff_7d) < -25:
            risk_factors += 1
        
        # 流动性风险
        if not snapshot.trans_count_48h or snapshot.trans_count_48h < 5:
            risk_factors += 1
        
        # 基础价值风险
        if not snapshot.current_price or float(snapshot.current_price) < 50:
            risk_factors += 1
        
        if risk_factors <= 1:
            return "MEDIUM"  # 供应冲击本身就有风险
        elif risk_factors <= 3:
            return "HIGH"
        else:
            return "HIGH"
    
    def generate_recommendation(self, score: float, risk_level: str) -> str:
        """生成供应冲击恢复投资建议"""
        if score >= 75:
            return "BUY"  # 高分恢复机会
        elif score >= 60:
            return "HOLD" if risk_level != "HIGH" else "AVOID"
        elif score >= 40:
            return "HOLD"
        else:
            return "AVOID"
    
    def generate_analysis_summary(self, snapshot: MarketSnapshot, score: float) -> str:
        """生成供应冲击恢复分析摘要"""
        summary_parts = []
        
        # 基础信息
        if snapshot.current_price:
            summary_parts.append(f"价格: ¥{snapshot.current_price}")
        
        # 供应冲击信息
        if snapshot.sell_nums_1d_rate:
            summary_parts.append(f"1天在售激增: +{snapshot.sell_nums_1d_rate:.1f}%")
        elif snapshot.sell_nums_7d_rate:
            summary_parts.append(f"7天在售增长: +{snapshot.sell_nums_7d_rate:.1f}%")
        
        # 价格调整
        if snapshot.diff_7d:
            summary_parts.append(f"7天价格调整: {snapshot.diff_7d:.1f}%")
        
        # 需求反应
        if snapshot.trans_count_48h:
            summary_parts.append(f"48h成交量: {snapshot.trans_count_48h}")
        
        if snapshot.trans_count_rate_48h:
            change_desc = "增长" if float(snapshot.trans_count_rate_48h) > 0 else "下降"
            summary_parts.append(f"成交量{change_desc}: {abs(float(snapshot.trans_count_rate_48h)):.1f}%")
        
        # 投资建议
        if score >= 75:
            summary_parts.append("供应冲击后恢复机会明显")
        elif score >= 60:
            summary_parts.append("存在恢复潜力，需观察")
        else:
            summary_parts.append("恢复不确定，谨慎对待")
        
        return "; ".join(summary_parts)
