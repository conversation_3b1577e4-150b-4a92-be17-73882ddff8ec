"""
数据库管理器

提供数据库初始化、表创建、索引管理等功能。
"""

from sqlalchemy import text
from loguru import logger

from ..config.database import (
    create_database_engine, 
    get_db_session, 
    initialize_database,
    create_database_if_not_exists
)
from ..models import (
    Base, Item, MarketSnapshot, PlatformPrice,
    DataSource, ScreeningResult, Favorite,
    AnalysisResult, RealtimeAnalysisResult
)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.logger = logger.bind(manager="DatabaseManager")
    
    def initialize_database(self) -> bool:
        """初始化数据库"""
        try:
            # 创建数据库（如果不存在）
            create_database_if_not_exists()
            
            # 初始化连接
            if not initialize_database():
                return False
            
            self.logger.info("数据库初始化成功")
            return True
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            return False
    
    def create_tables(self) -> bool:
        """创建所有表"""
        try:
            engine = create_database_engine()
            
            # 创建所有表
            Base.metadata.create_all(engine)
            
            self.logger.info("数据库表创建成功")
            return True
        except Exception as e:
            self.logger.error(f"创建数据库表失败: {e}")
            return False
    
    def drop_tables(self) -> bool:
        """删除所有表（谨慎使用）"""
        try:
            engine = create_database_engine()
            
            # 删除所有表
            Base.metadata.drop_all(engine)
            
            self.logger.warning("数据库表已删除")
            return True
        except Exception as e:
            self.logger.error(f"删除数据库表失败: {e}")
            return False
    
    def create_indexes(self) -> bool:
        """创建额外的索引"""
        try:
            with get_db_session() as session:
                # 创建全文索引（MySQL特定）
                indexes = [
                    "CREATE FULLTEXT INDEX idx_items_name_search ON items(name, market_hash_name)",
                    "CREATE INDEX idx_market_snapshots_hot_rank ON market_snapshots(hot_rank) WHERE hot_rank IS NOT NULL",
                    "CREATE INDEX idx_market_snapshots_trans_amount_3m ON market_snapshots(trans_amount_3m) WHERE trans_amount_3m IS NOT NULL",
                    "CREATE INDEX idx_screening_results_score_desc ON screening_results(score DESC) WHERE score IS NOT NULL",
                    "CREATE INDEX idx_market_snapshots_ranking_count ON market_snapshots((JSON_LENGTH(ranking_info))) WHERE ranking_info IS NOT NULL",
                ]
                
                for index_sql in indexes:
                    try:
                        session.execute(text(index_sql))
                        self.logger.info(f"创建索引成功: {index_sql}")
                    except Exception as e:
                        # 索引可能已存在，记录警告但继续
                        self.logger.warning(f"创建索引跳过: {e}")
                
                return True
        except Exception as e:
            self.logger.error(f"创建索引失败: {e}")
            return False

    def add_ranking_info_column(self) -> bool:
        """添加排行榜信息字段"""
        try:
            with get_db_session() as session:
                column_sql = "ALTER TABLE market_snapshots ADD COLUMN ranking_info JSON COMMENT '排行榜排名信息'"
                try:
                    session.execute(text(column_sql))
                    self.logger.info("添加ranking_info字段成功")
                    return True
                except Exception as e:
                    self.logger.warning(f"字段可能已存在: {e}")
                    return True
        except Exception as e:
            self.logger.error(f"添加字段失败: {e}")
            return False

    def add_steam_monitor_fields(self) -> bool:
        """添加Steam监控相关字段"""
        try:
            with get_db_session() as session:
                # 添加Steam价格最后更新时间字段
                column_sql = "ALTER TABLE favorites ADD COLUMN last_steam_update_time DATETIME COMMENT 'Steam价格最后更新时间'"
                try:
                    session.execute(text(column_sql))
                    self.logger.info("添加last_steam_update_time字段成功")

                    # 创建索引优化查询性能
                    index_sql = "CREATE INDEX idx_favorites_steam_update_time ON favorites(last_steam_update_time)"
                    try:
                        session.execute(text(index_sql))
                        self.logger.info("创建Steam更新时间索引成功")
                    except Exception as e:
                        self.logger.warning(f"索引可能已存在: {e}")

                    return True
                except Exception as e:
                    self.logger.warning(f"字段可能已存在: {e}")
                    return True
        except Exception as e:
            self.logger.error(f"添加Steam监控字段失败: {e}")
            return False

    def add_steamdt_id_field(self) -> bool:
        """添加SteamDT ID字段"""
        try:
            with get_db_session() as session:
                # 添加SteamDT饰品ID字段
                column_sql = "ALTER TABLE items ADD COLUMN steamdt_item_id VARCHAR(50) DEFAULT NULL COMMENT 'SteamDT饰品ID'"
                try:
                    session.execute(text(column_sql))
                    self.logger.info("添加steamdt_item_id字段成功")

                    # 创建索引优化查询性能
                    index_sql = "CREATE INDEX idx_items_steamdt_id ON items(steamdt_item_id)"
                    try:
                        session.execute(text(index_sql))
                        self.logger.info("创建SteamDT ID索引成功")
                    except Exception as e:
                        self.logger.warning(f"索引可能已存在: {e}")

                    return True
                except Exception as e:
                    self.logger.warning(f"字段可能已存在: {e}")
                    return True
        except Exception as e:
            self.logger.error(f"添加SteamDT ID字段失败: {e}")
            return False

    def add_technical_indicators_columns(self, phase: str = "all") -> bool:
        """添加技术分析指标字段

        Args:
            phase: 实施阶段 ("phase1", "phase2", "phase3", "all")
                  - phase1: 核心技术指标（30个字段）
                  - phase2: 重要分析指标（15个字段）
                  - phase3: 辅助分析指标（12个字段）
                  - all: 所有字段（57个字段）

        Returns:
            bool: 是否成功添加字段
        """
        try:
            with get_db_session() as session:
                self.logger.info(f"开始添加技术分析指标字段 - 阶段: {phase}")

                # 定义各阶段的字段
                phase1_columns = [
                    # MACD指标扩展
                    "ALTER TABLE analysis_results ADD COLUMN macd_signal_line DECIMAL(10,4) DEFAULT NULL COMMENT 'MACD信号线'",
                    "ALTER TABLE analysis_results ADD COLUMN macd_histogram DECIMAL(10,4) DEFAULT NULL COMMENT 'MACD柱状图'",
                    "ALTER TABLE analysis_results ADD COLUMN macd_trend VARCHAR(20) DEFAULT NULL COMMENT 'MACD趋势方向'",
                    "ALTER TABLE analysis_results ADD COLUMN macd_cross_status VARCHAR(20) DEFAULT NULL COMMENT 'MACD交叉状态'",

                    # ATR指标
                    "ALTER TABLE analysis_results ADD COLUMN atr_value DECIMAL(8,2) DEFAULT NULL COMMENT 'ATR指标值'",
                    "ALTER TABLE analysis_results ADD COLUMN atr_volatility_pct DECIMAL(5,2) DEFAULT NULL COMMENT 'ATR波动率百分比'",
                    "ALTER TABLE analysis_results ADD COLUMN atr_volatility_level VARCHAR(20) DEFAULT NULL COMMENT 'ATR波动率等级'",
                    "ALTER TABLE analysis_results ADD COLUMN atr_trend_direction VARCHAR(20) DEFAULT NULL COMMENT 'ATR趋势方向'",

                    # OBV指标
                    "ALTER TABLE analysis_results ADD COLUMN obv_value BIGINT DEFAULT NULL COMMENT 'OBV指标值'",
                    "ALTER TABLE analysis_results ADD COLUMN obv_trend VARCHAR(20) DEFAULT NULL COMMENT 'OBV趋势方向'",
                    "ALTER TABLE analysis_results ADD COLUMN obv_sync_status VARCHAR(20) DEFAULT NULL COMMENT 'OBV同步状态'",
                    "ALTER TABLE analysis_results ADD COLUMN obv_signal_type VARCHAR(20) DEFAULT NULL COMMENT 'OBV信号类型'",

                    # 布林带指标
                    "ALTER TABLE analysis_results ADD COLUMN bollinger_position DECIMAL(5,2) DEFAULT NULL COMMENT '布林带位置百分比'",
                    "ALTER TABLE analysis_results ADD COLUMN bollinger_zone VARCHAR(20) DEFAULT NULL COMMENT '布林带区间'",
                    "ALTER TABLE analysis_results ADD COLUMN bollinger_band_status VARCHAR(20) DEFAULT NULL COMMENT '布林带状态'",
                    "ALTER TABLE analysis_results ADD COLUMN bollinger_breakthrough VARCHAR(20) DEFAULT NULL COMMENT '布林带突破状态'",

                    # 成交量分析扩展
                    "ALTER TABLE analysis_results ADD COLUMN volume_ratio DECIMAL(5,2) DEFAULT NULL COMMENT '成交量比率'",
                    "ALTER TABLE analysis_results ADD COLUMN volume_status VARCHAR(20) DEFAULT NULL COMMENT '成交量状态'",
                    "ALTER TABLE analysis_results ADD COLUMN volume_average INTEGER DEFAULT NULL COMMENT '平均成交量'",
                    "ALTER TABLE analysis_results ADD COLUMN price_volume_match VARCHAR(20) DEFAULT NULL COMMENT '价量匹配状态'",

                    # 资金流向
                    "ALTER TABLE analysis_results ADD COLUMN money_flow_ratio DECIMAL(5,2) DEFAULT NULL COMMENT '资金流向比率'",
                    "ALTER TABLE analysis_results ADD COLUMN money_flow_status VARCHAR(20) DEFAULT NULL COMMENT '资金流向状态'",
                    "ALTER TABLE analysis_results ADD COLUMN money_flow_green_ratio DECIMAL(5,2) DEFAULT NULL COMMENT '绿色资金比率'",
                    "ALTER TABLE analysis_results ADD COLUMN money_flow_red_ratio DECIMAL(5,2) DEFAULT NULL COMMENT '红色资金比率'",

                    # 价格变化数据扩展
                    "ALTER TABLE analysis_results ADD COLUMN price_change_7d DECIMAL(10,2) DEFAULT NULL COMMENT '7天价格变化'",
                    "ALTER TABLE analysis_results ADD COLUMN price_change_30d DECIMAL(10,2) DEFAULT NULL COMMENT '30天价格变化'",
                    "ALTER TABLE analysis_results ADD COLUMN price_change_7d_pct DECIMAL(5,2) DEFAULT NULL COMMENT '7天价格变化百分比'",
                    "ALTER TABLE analysis_results ADD COLUMN price_change_30d_pct DECIMAL(5,2) DEFAULT NULL COMMENT '30天价格变化百分比'",
                    "ALTER TABLE analysis_results ADD COLUMN price_52w_high DECIMAL(12,2) DEFAULT NULL COMMENT '52周最高价'",
                    "ALTER TABLE analysis_results ADD COLUMN price_52w_low DECIMAL(12,2) DEFAULT NULL COMMENT '52周最低价'"
                ]

                phase2_columns = [
                    # 均线分析
                    "ALTER TABLE analysis_results ADD COLUMN price_ma_position VARCHAR(50) DEFAULT NULL COMMENT '价格相对均线位置'",
                    "ALTER TABLE analysis_results ADD COLUMN ma_arrangement VARCHAR(20) DEFAULT NULL COMMENT '均线排列状态'",
                    "ALTER TABLE analysis_results ADD COLUMN ema_12_distance DECIMAL(5,2) DEFAULT NULL COMMENT 'EMA12距离百分比'",
                    "ALTER TABLE analysis_results ADD COLUMN ema_26_distance DECIMAL(5,2) DEFAULT NULL COMMENT 'EMA26距离百分比'",
                    "ALTER TABLE analysis_results ADD COLUMN sma_20_distance DECIMAL(5,2) DEFAULT NULL COMMENT 'SMA20距离百分比'",

                    # 交易信号评估
                    "ALTER TABLE analysis_results ADD COLUMN buy_signal_count INTEGER DEFAULT NULL COMMENT '买入信号数量'",
                    "ALTER TABLE analysis_results ADD COLUMN sell_signal_count INTEGER DEFAULT NULL COMMENT '卖出信号数量'",
                    "ALTER TABLE analysis_results ADD COLUMN watch_signal_count INTEGER DEFAULT NULL COMMENT '观望信号数量'",
                    "ALTER TABLE analysis_results ADD COLUMN signal_strength VARCHAR(20) DEFAULT NULL COMMENT '信号强度'",
                    "ALTER TABLE analysis_results ADD COLUMN main_signal VARCHAR(20) DEFAULT NULL COMMENT '主要信号'",

                    # 关键确认指标
                    "ALTER TABLE analysis_results ADD COLUMN key_confirmation_obv_sync VARCHAR(20) DEFAULT NULL COMMENT 'OBV同步确认'",
                    "ALTER TABLE analysis_results ADD COLUMN key_confirmation_volume_match VARCHAR(20) DEFAULT NULL COMMENT '成交量匹配确认'",
                    "ALTER TABLE analysis_results ADD COLUMN key_confirmation_signal_type VARCHAR(20) DEFAULT NULL COMMENT '信号类型确认'",

                    # 支撑阻力位扩展
                    "ALTER TABLE analysis_results ADD COLUMN support_distance DECIMAL(5,2) DEFAULT NULL COMMENT '支撑位距离百分比'",
                    "ALTER TABLE analysis_results ADD COLUMN resistance_distance DECIMAL(5,2) DEFAULT NULL COMMENT '阻力位距离百分比'"
                ]

                phase3_columns = [
                    # 风险分析扩展
                    "ALTER TABLE analysis_results ADD COLUMN volatility_risk_level VARCHAR(20) DEFAULT NULL COMMENT '波动率风险等级'",
                    "ALTER TABLE analysis_results ADD COLUMN volatility_risk_advice VARCHAR(100) DEFAULT NULL COMMENT '波动率风险建议'",
                    "ALTER TABLE analysis_results ADD COLUMN risk_score INTEGER DEFAULT NULL COMMENT '风险评分(0-100)'",

                    # 止损位分析
                    "ALTER TABLE analysis_results ADD COLUMN stop_loss_conservative DECIMAL(12,2) DEFAULT NULL COMMENT '保守止损位'",
                    "ALTER TABLE analysis_results ADD COLUMN stop_loss_standard DECIMAL(12,2) DEFAULT NULL COMMENT '标准止损位'",
                    "ALTER TABLE analysis_results ADD COLUMN stop_loss_loose DECIMAL(12,2) DEFAULT NULL COMMENT '宽松止损位'",
                    "ALTER TABLE analysis_results ADD COLUMN stop_loss_recommended_level VARCHAR(20) DEFAULT NULL COMMENT '推荐止损等级'",

                    # 关键位分析
                    "ALTER TABLE analysis_results ADD COLUMN key_upper_resistance DECIMAL(12,2) DEFAULT NULL COMMENT '关键阻力位'",
                    "ALTER TABLE analysis_results ADD COLUMN key_lower_support DECIMAL(12,2) DEFAULT NULL COMMENT '关键支撑位'",
                    "ALTER TABLE analysis_results ADD COLUMN key_breakthrough DECIMAL(12,2) DEFAULT NULL COMMENT '关键突破位'",
                    "ALTER TABLE analysis_results ADD COLUMN key_breakdown DECIMAL(12,2) DEFAULT NULL COMMENT '关键跌破位'",
                    "ALTER TABLE analysis_results ADD COLUMN risk_warnings JSON DEFAULT NULL COMMENT '风险警告列表'"
                ]

                # 根据阶段选择要执行的字段
                columns_to_add = []
                if phase == "phase1":
                    columns_to_add = phase1_columns
                elif phase == "phase2":
                    columns_to_add = phase2_columns
                elif phase == "phase3":
                    columns_to_add = phase3_columns
                elif phase == "all":
                    columns_to_add = phase1_columns + phase2_columns + phase3_columns
                else:
                    self.logger.error(f"无效的阶段参数: {phase}")
                    return False

                # 批量执行字段添加
                success_count = 0
                total_count = len(columns_to_add)

                for i, column_sql in enumerate(columns_to_add, 1):
                    try:
                        session.execute(text(column_sql))
                        success_count += 1
                        if i % 10 == 0:  # 每10个字段记录一次进度
                            self.logger.info(f"字段添加进度: {i}/{total_count}")
                    except Exception as e:
                        # 字段可能已存在，这是正常情况
                        self.logger.debug(f"字段添加跳过 ({i}/{total_count}): {e}")
                        success_count += 1

                self.logger.info(f"技术分析指标字段添加完成 - 阶段: {phase}, 成功: {success_count}/{total_count}")
                return True

        except Exception as e:
            self.logger.error(f"添加技术分析指标字段失败: {e}")
            return False

    def create_technical_indicators_indexes(self, phase: str = "all") -> bool:
        """创建技术分析指标索引

        Args:
            phase: 实施阶段 ("phase1", "phase2", "phase3", "all")

        Returns:
            bool: 是否成功创建索引
        """
        try:
            with get_db_session() as session:
                self.logger.info(f"开始创建技术分析指标索引 - 阶段: {phase}")

                # 定义各阶段的索引
                phase1_indexes = [
                    "CREATE INDEX idx_core_technical_indicators ON analysis_results(rsi_value, macd_value, kdj_k, kdj_d, kdj_j)",
                    "CREATE INDEX idx_atr_obv ON analysis_results(atr_value, obv_value)",
                    "CREATE INDEX idx_bollinger_volume ON analysis_results(bollinger_position, volume_ratio)",
                    "CREATE INDEX idx_money_flow ON analysis_results(money_flow_ratio, money_flow_status)",
                    "CREATE INDEX idx_price_changes_core ON analysis_results(price_change_7d_pct, price_change_30d_pct)"
                ]

                phase2_indexes = [
                    "CREATE INDEX idx_trading_signals ON analysis_results(trading_signal, main_signal, signal_strength)",
                    "CREATE INDEX idx_signal_counts ON analysis_results(buy_signal_count, sell_signal_count, watch_signal_count)",
                    "CREATE INDEX idx_ma_analysis ON analysis_results(ma_arrangement, ema_12_distance, ema_26_distance)"
                ]

                phase3_indexes = [
                    "CREATE INDEX idx_risk_analysis ON analysis_results(risk_level, volatility_score, risk_score)",
                    "CREATE INDEX idx_stop_loss_levels ON analysis_results(stop_loss_conservative, stop_loss_standard, stop_loss_loose)"
                ]

                # 根据阶段选择要创建的索引
                indexes_to_create = []
                if phase == "phase1":
                    indexes_to_create = phase1_indexes
                elif phase == "phase2":
                    indexes_to_create = phase2_indexes
                elif phase == "phase3":
                    indexes_to_create = phase3_indexes
                elif phase == "all":
                    indexes_to_create = phase1_indexes + phase2_indexes + phase3_indexes
                else:
                    self.logger.error(f"无效的阶段参数: {phase}")
                    return False

                # 批量创建索引
                success_count = 0
                total_count = len(indexes_to_create)

                for i, index_sql in enumerate(indexes_to_create, 1):
                    try:
                        session.execute(text(index_sql))
                        success_count += 1
                        self.logger.debug(f"索引创建成功 ({i}/{total_count})")
                    except Exception as e:
                        # 索引可能已存在，这是正常情况
                        self.logger.debug(f"索引创建跳过 ({i}/{total_count}): {e}")
                        success_count += 1

                self.logger.info(f"技术分析指标索引创建完成 - 阶段: {phase}, 成功: {success_count}/{total_count}")
                return True

        except Exception as e:
            self.logger.error(f"创建技术分析指标索引失败: {e}")
            return False

    def migrate_technical_indicators(self, phase: str = "phase1") -> bool:
        """执行技术分析指标完整迁移（字段+索引）

        Args:
            phase: 实施阶段 ("phase1", "phase2", "phase3", "all")

        Returns:
            bool: 是否成功完成迁移
        """
        try:
            self.logger.info(f"开始技术分析指标迁移 - 阶段: {phase}")

            # 1. 添加字段
            if not self.add_technical_indicators_columns(phase):
                self.logger.error("字段添加失败")
                return False

            # 2. 创建索引
            if not self.create_technical_indicators_indexes(phase):
                self.logger.error("索引创建失败")
                return False

            # 3. 验证迁移结果
            if not self._verify_technical_indicators_migration(phase):
                self.logger.warning("迁移验证失败，但迁移可能已成功")

            self.logger.info(f"技术分析指标迁移完成 - 阶段: {phase}")
            return True

        except Exception as e:
            self.logger.error(f"技术分析指标迁移失败: {e}")
            return False

    def _verify_technical_indicators_migration(self, phase: str) -> bool:
        """验证技术分析指标迁移结果

        Args:
            phase: 实施阶段

        Returns:
            bool: 验证是否通过
        """
        try:
            with get_db_session() as session:
                # 定义各阶段预期的字段数量
                expected_fields = {
                    "phase1": 30,
                    "phase2": 15,
                    "phase3": 12,
                    "all": 57
                }

                if phase not in expected_fields:
                    return False

                # 检查新增字段
                check_sql = """
                SELECT COUNT(*) as field_count
                FROM information_schema.COLUMNS
                WHERE TABLE_SCHEMA = DATABASE()
                  AND TABLE_NAME = 'analysis_results'
                  AND COLUMN_NAME IN (
                    'macd_signal_line', 'macd_histogram', 'atr_value', 'obv_value',
                    'bollinger_position', 'volume_ratio', 'money_flow_ratio',
                    'price_change_7d', 'price_change_30d', 'buy_signal_count',
                    'main_signal', 'risk_score'
                  )
                """

                result = session.execute(text(check_sql)).fetchone()
                field_count = result[0] if result else 0

                # 检查索引
                index_check_sql = """
                SELECT COUNT(DISTINCT INDEX_NAME) as index_count
                FROM information_schema.STATISTICS
                WHERE TABLE_SCHEMA = DATABASE()
                  AND TABLE_NAME = 'analysis_results'
                  AND INDEX_NAME LIKE 'idx_%technical%'
                   OR INDEX_NAME LIKE 'idx_%atr%'
                   OR INDEX_NAME LIKE 'idx_%obv%'
                   OR INDEX_NAME LIKE 'idx_%bollinger%'
                   OR INDEX_NAME LIKE 'idx_%money_flow%'
                """

                index_result = session.execute(text(index_check_sql)).fetchone()
                index_count = index_result[0] if index_result else 0

                self.logger.info(f"迁移验证结果 - 字段数: {field_count}, 索引数: {index_count}")

                # 基本验证：至少有一些字段和索引被创建
                return field_count > 0 and index_count > 0

        except Exception as e:
            self.logger.error(f"迁移验证失败: {e}")
            return False

    def create_partitions(self) -> bool:
        """创建分区表（MySQL特定）"""
        try:
            with get_db_session() as session:
                # 为market_snapshots表创建按月分区
                partition_sql = """
                ALTER TABLE market_snapshots 
                PARTITION BY RANGE (YEAR(snapshot_time) * 100 + MONTH(snapshot_time)) (
                    PARTITION p202501 VALUES LESS THAN (202502),
                    PARTITION p202502 VALUES LESS THAN (202503),
                    PARTITION p202503 VALUES LESS THAN (202504),
                    PARTITION p202504 VALUES LESS THAN (202505),
                    PARTITION p202505 VALUES LESS THAN (202506),
                    PARTITION p202506 VALUES LESS THAN (202507),
                    PARTITION p202507 VALUES LESS THAN (202508),
                    PARTITION p202508 VALUES LESS THAN (202509),
                    PARTITION p202509 VALUES LESS THAN (202510),
                    PARTITION p202510 VALUES LESS THAN (202511),
                    PARTITION p202511 VALUES LESS THAN (202512),
                    PARTITION p202512 VALUES LESS THAN (202601),
                    PARTITION p_future VALUES LESS THAN MAXVALUE
                )
                """
                
                try:
                    session.execute(text(partition_sql))
                    self.logger.info("创建分区表成功")
                except Exception as e:
                    self.logger.warning(f"创建分区表跳过: {e}")
                
                return True
        except Exception as e:
            self.logger.error(f"创建分区表失败: {e}")
            return False
    
    def initialize_data_sources(self) -> bool:
        """初始化数据源配置"""
        try:
            with get_db_session() as session:
                # 检查是否已有数据源
                existing_count = session.query(DataSource).count()
                if existing_count > 0:
                    self.logger.info("数据源配置已存在，跳过初始化")
                    return True
                
                # 初始化数据源
                data_sources = [
                    {
                        'source_name': '1天成交额榜单',
                        'source_type': 'ranking',
                        'description': '1天成交额排行榜数据',
                        'update_frequency': 'daily'
                    },
                    {
                        'source_name': '热度榜单',
                        'source_type': 'ranking',
                        'description': '热度排行榜数据',
                        'update_frequency': 'daily'
                    },
                    {
                        'source_name': '1天在售变化榜',
                        'source_type': 'ranking',
                        'description': '1天在售数量变化排行榜',
                        'update_frequency': 'daily'
                    },
                    {
                        'source_name': '7天价格上涨榜',
                        'source_type': 'ranking',
                        'description': '7天价格上涨排行榜',
                        'update_frequency': 'daily'
                    },
                    {
                        'source_name': '30天成交额榜单',
                        'source_type': 'ranking',
                        'description': '30天成交额排行榜数据',
                        'update_frequency': 'daily'
                    }
                ]
                
                for source_data in data_sources:
                    source = DataSource(**source_data)
                    session.add(source)
                
                session.flush()
                self.logger.info(f"初始化数据源配置: {len(data_sources)}个")
                return True
        except Exception as e:
            self.logger.error(f"初始化数据源配置失败: {e}")
            return False
    
    def get_database_info(self) -> dict:
        """获取数据库信息"""
        try:
            with get_db_session() as session:
                # 获取表信息
                tables_info = {}
                for table_name in ['items', 'market_snapshots', 'platform_prices', 'screening_results']:
                    count_sql = f"SELECT COUNT(*) FROM {table_name}"
                    result = session.execute(text(count_sql)).scalar()
                    tables_info[table_name] = result
                
                # 获取数据库大小
                size_sql = """
                SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                """
                db_size = session.execute(text(size_sql)).scalar()
                
                return {
                    'tables': tables_info,
                    'database_size_mb': float(db_size) if db_size else 0
                }
        except Exception as e:
            self.logger.error(f"获取数据库信息失败: {e}")
            return {}
    
    def setup_database(self) -> bool:
        """完整的数据库设置"""
        try:
            self.logger.info("开始数据库设置...")
            
            # 1. 初始化数据库
            if not self.initialize_database():
                return False
            
            # 2. 创建表
            if not self.create_tables():
                return False
            
            # 3. 创建索引
            if not self.create_indexes():
                return False
            
            # 4. 初始化数据源
            if not self.initialize_data_sources():
                return False
            
            self.logger.info("数据库设置完成")
            return True
        except Exception as e:
            self.logger.error(f"数据库设置失败: {e}")
            return False
