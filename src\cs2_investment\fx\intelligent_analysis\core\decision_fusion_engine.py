"""
决策融合引擎

基于现有InvestmentRecommendationService的决策逻辑，实现多维度分析结果的智能融合。
包含信号一致性检查、冲突信号处理、最终决策生成等功能。
输出结构化的分析结果而非简单评分。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
from loguru import logger

from .multi_timeframe_engine import MultiTimeframeEngine
from .market_context_analyzer import MarketContextAnalyzer


class DecisionFusionEngine:
    """决策融合引擎"""
    
    def __init__(self, item_id: str, data_base_path: str = "data/scraped_data"):
        """
        初始化决策融合引擎
        
        Args:
            item_id: 饰品ID
            data_base_path: 数据基础路径
        """
        self.item_id = item_id
        self.data_base_path = data_base_path
        self.logger = logger.bind(engine=self.__class__.__name__, item_id=item_id)
        
        # 初始化多时间框架分析引擎
        self.multi_timeframe_engine = MultiTimeframeEngine(item_id, data_base_path)

        # 初始化市场环境分析器
        self.market_context_analyzer = MarketContextAnalyzer(item_id)
        
        # 决策权重配置 - 基于市场特性和修复后算法重新设计
        self.decision_weights = self._initialize_decision_weights()
        
        # 决策阈值配置
        self.decision_thresholds = {
            'strong_buy': 80,
            'buy': 60,
            'hold': 40,
            'sell': 20,
            'strong_sell': 0
        }
        
        # 融合结果缓存
        self.fusion_cache = {}
    
    def execute_intelligent_fusion(self) -> Dict[str, Any]:
        """执行智能决策融合"""
        try:
            self.logger.info("开始执行智能决策融合...")
            
            # 1. 获取多时间框架分析结果
            multi_timeframe_result = self.multi_timeframe_engine.execute_comprehensive_analysis()
            
            if not multi_timeframe_result.get('success', False):
                return {
                    'success': False,
                    'error': '多时间框架分析失败',
                    'fusion_timestamp': datetime.now()
                }
            
            # 2. 执行信号一致性检查
            consistency_analysis = self._check_signal_consistency(multi_timeframe_result)
            
            # 3. 处理冲突信号
            conflict_resolution = self._resolve_signal_conflicts(multi_timeframe_result, consistency_analysis)
            
            # 4. 生成最终决策
            final_decision = self._generate_final_decision(multi_timeframe_result, conflict_resolution, consistency_analysis)
            
            # 5. 生成决策推理和解释
            coordinated_signals = conflict_resolution.get('final_coordinated_signals', {})
            decision_reasoning = self._generate_decision_reasoning(
                multi_timeframe_result, consistency_analysis, coordinated_signals
            )
            
            # 6. 构建融合结果
            fusion_result = {
                'item_id': self.item_id,
                'fusion_timestamp': datetime.now(),
                'success': True,
                
                # 原始分析结果
                'multi_timeframe_analysis': multi_timeframe_result,
                
                # 融合分析结果
                'consistency_analysis': consistency_analysis,
                'conflict_resolution': conflict_resolution,
                'final_decision': final_decision,
                'decision_reasoning': decision_reasoning,
                
                # 兼容现有系统的格式
                'recommendation_summary': self._generate_recommendation_summary(final_decision)
            }
            
            # 缓存结果
            self.fusion_cache['latest_fusion'] = fusion_result
            
            self.logger.info("智能决策融合完成")
            return fusion_result
            
        except Exception as e:
            self.logger.error(f"智能决策融合失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'fusion_timestamp': datetime.now()
            }
    
    def _check_signal_consistency(self, multi_timeframe_result: Dict) -> Dict[str, Any]:
        """检查信号一致性"""
        try:
            self.logger.info("开始信号一致性检查...")
            
            timeframe_analyses = multi_timeframe_result.get('timeframe_analyses', {})
            
            # 提取各时间框架的信号
            signals = {
                'strategic': self._extract_strategic_signals(timeframe_analyses.get('strategic', {})),
                'tactical': self._extract_tactical_signals(timeframe_analyses.get('tactical', {})),
                'execution': self._extract_execution_signals(timeframe_analyses.get('execution', {})),
                'supply_demand': self._extract_supply_demand_signals(timeframe_analyses.get('supply_demand', {}))
            }
            
            # 分析信号一致性
            consistency_metrics = self._calculate_consistency_metrics(signals)
            
            # 识别一致性问题
            consistency_issues = self._identify_consistency_issues(signals, consistency_metrics)
            
            consistency_result = {
                'signals': signals,
                'consistency_metrics': consistency_metrics,
                'consistency_issues': consistency_issues,
                'overall_consistency': consistency_metrics.get('overall_consistency_score', 0)
            }
            
            self.logger.info(f"信号一致性检查完成，总体一致性: {consistency_metrics.get('overall_consistency_score', 0):.1f}%")
            return consistency_result
            
        except Exception as e:
            self.logger.error(f"信号一致性检查失败: {e}")
            return {
                'signals': {},
                'consistency_metrics': {},
                'consistency_issues': [],
                'overall_consistency': 0,
                'error': str(e)
            }
    
    def _resolve_signal_conflicts(self, multi_timeframe_result: Dict, consistency_analysis: Dict) -> Dict[str, Any]:
        """解决信号冲突"""
        try:
            self.logger.info("开始解决信号冲突...")
            
            signals = consistency_analysis.get('signals', {})
            consistency_issues = consistency_analysis.get('consistency_issues', [])
            
            # 识别冲突类型
            conflicts = self._identify_conflicts(signals, consistency_issues)
            
            # 应用冲突解决策略
            resolution_strategies = []
            resolved_signals = {}
            
            for conflict in conflicts:
                strategy = self._apply_conflict_resolution_strategy(conflict, signals)
                resolution_strategies.append(strategy)
                
                # 更新解决后的信号
                if strategy.get('resolved_signal'):
                    resolved_signals[conflict['dimension']] = strategy['resolved_signal']
            
            # 生成最终协调信号
            final_coordinated_signals = self._generate_coordinated_signals(signals, resolved_signals)
            
            conflict_resolution = {
                'conflicts_identified': conflicts,
                'resolution_strategies': resolution_strategies,
                'resolved_signals': resolved_signals,
                'final_coordinated_signals': final_coordinated_signals,
                'conflict_count': len(conflicts)
            }
            
            self.logger.info(f"信号冲突解决完成，处理了{len(conflicts)}个冲突")
            return conflict_resolution
            
        except Exception as e:
            self.logger.error(f"信号冲突解决失败: {e}")
            return {
                'conflicts_identified': [],
                'resolution_strategies': [],
                'resolved_signals': {},
                'final_coordinated_signals': {},
                'conflict_count': 0,
                'error': str(e)
            }
    
    def _generate_final_decision(self, multi_timeframe_result: Dict, conflict_resolution: Dict, consistency_analysis: Dict) -> Dict[str, Any]:
        """生成最终决策"""
        try:
            self.logger.info("开始生成最终决策...")
            
            # 获取协调后的信号
            coordinated_signals = conflict_resolution.get('final_coordinated_signals', {})

            # 分析市场条件用于动态权重调整
            market_conditions = self._extract_market_conditions(multi_timeframe_result)

            # 基于信号和市场条件确定推荐类型（使用动态权重）
            recommendation_type = self._determine_recommendation_from_signals(coordinated_signals, market_conditions)
            
            # 确定风险等级
            risk_level = self._determine_risk_level(coordinated_signals)
            
            # 确定投资期限
            investment_horizon = self._determine_investment_horizon(coordinated_signals, multi_timeframe_result)
            
            # 生成具体建议
            specific_recommendations = self._generate_specific_recommendations(
                recommendation_type, coordinated_signals, multi_timeframe_result
            )

            # 添加市场环境分析
            market_context = self._analyze_market_context(multi_timeframe_result)

            final_decision = {
                'recommendation_type': recommendation_type,
                'risk_level': risk_level,
                'investment_horizon': investment_horizon,
                'specific_recommendations': specific_recommendations,
                'decision_reasoning': self._generate_decision_reasoning(multi_timeframe_result, consistency_analysis, coordinated_signals),
                'market_context': market_context,
                'decision_timestamp': datetime.now()
            }
            
            self.logger.info(f"最终决策生成完成: {recommendation_type}")
            return final_decision
            
        except Exception as e:
            self.logger.error(f"最终决策生成失败: {e}")
            return {
                'recommendation_type': 'HOLD',
                'risk_level': 'MEDIUM',
                'investment_horizon': 'MEDIUM_TERM',
                'specific_recommendations': {},
                'decision_reasoning': '分析过程中出现错误，建议持有观望',
                'error': str(e)
            }

    def _determine_recommendation_from_signals(self, coordinated_signals: Dict, market_conditions: Dict = None) -> str:
        """基于信号直接确定推荐类型，使用动态权重"""
        try:
            # 获取动态权重（如果提供了市场条件）
            if market_conditions:
                timeframe_weights = self.get_dynamic_weights(market_conditions)
            else:
                timeframe_weights = self.decision_weights

            weighted_signals = {}

            for timeframe, signal in coordinated_signals.items():
                if timeframe in timeframe_weights:
                    signal_type = signal.get('signal_type', 'HOLD')
                    weight = timeframe_weights[timeframe]
                    confidence = signal.get('confidence', 50) / 100  # 归一化置信度

                    # 考虑置信度的权重调整
                    adjusted_weight = weight * (0.5 + 0.5 * confidence)

                    if signal_type not in weighted_signals:
                        weighted_signals[signal_type] = 0
                    weighted_signals[signal_type] += adjusted_weight

            # 找出权重最高的信号类型
            if weighted_signals:
                recommendation_type = max(weighted_signals.items(), key=lambda x: x[1])[0]
                self.logger.info(f"权重信号分布: {weighted_signals}, 最终推荐: {recommendation_type}")
            else:
                recommendation_type = 'HOLD'

            return recommendation_type

        except Exception as e:
            self.logger.error(f"确定推荐类型失败: {e}")
            return 'HOLD'

    def _generate_decision_reasoning(self, multi_timeframe_result: Dict, consistency_analysis: Dict,
                                   coordinated_signals: Dict) -> str:
        """生成简单的决策推理文字描述"""
        try:
            reasoning_parts = []

            # 分析各时间框架的信号
            strategic_signal = coordinated_signals.get('strategic', {}).get('signal_type', 'HOLD')
            tactical_signal = coordinated_signals.get('tactical', {}).get('signal_type', 'HOLD')
            execution_signal = coordinated_signals.get('execution', {}).get('signal_type', 'HOLD')
            supply_demand_signal = coordinated_signals.get('supply_demand', {}).get('signal_type', 'HOLD')

            # 战略层面分析
            if strategic_signal in ['BUY', 'STRONG_BUY']:
                reasoning_parts.append("长期趋势向好")
            elif strategic_signal in ['SELL', 'STRONG_SELL']:
                reasoning_parts.append("长期趋势偏弱")
            else:
                reasoning_parts.append("长期趋势不明确")

            # 战术层面分析
            if tactical_signal in ['BUY', 'STRONG_BUY']:
                reasoning_parts.append("中期技术指标支持")
            elif tactical_signal in ['SELL', 'STRONG_SELL']:
                reasoning_parts.append("中期技术指标偏弱")
            else:
                reasoning_parts.append("中期震荡整理")

            # 供需分析
            if supply_demand_signal in ['SELL', 'STRONG_SELL']:
                reasoning_parts.append("当前卖盘压力较大")
            elif supply_demand_signal in ['BUY', 'STRONG_BUY']:
                reasoning_parts.append("当前买盘支撑较强")
            else:
                reasoning_parts.append("供需关系相对平衡")

            return "，".join(reasoning_parts) + "。"

        except Exception as e:
            self.logger.error(f"生成决策推理失败: {e}")
            return "基于多维度分析得出投资建议。"

    def _analyze_market_context(self, multi_timeframe_result: Dict) -> Dict[str, Any]:
        """分析市场环境"""
        try:
            # 获取数据
            weekly_data = self.multi_timeframe_engine.weekly_data
            daily_data = self.multi_timeframe_engine.daily_data
            hourly_data = self.multi_timeframe_engine.hourly_data

            # 获取当前价格
            current_price = None
            if not daily_data.empty:
                current_price = daily_data['close'].iloc[-1]
            elif not hourly_data.empty:
                current_price = hourly_data['close'].iloc[-1]

            if current_price is None:
                return {'error': '无法获取当前价格'}

            # 使用市场环境分析器
            context = self.market_context_analyzer.analyze_market_context(
                weekly_data, daily_data, hourly_data, current_price
            )

            return context

        except Exception as e:
            self.logger.error(f"市场环境分析失败: {e}")
            return {'error': str(e)}
            
            # 替代方案
            alternative_scenarios = self._generate_alternative_scenarios(
                multi_timeframe_result, final_decision
            )
            
            reasoning = {
                'key_factors': key_factors,
                'supporting_evidence': supporting_evidence,
                'risk_warnings': risk_warnings,
                'decision_logic_chain': decision_logic_chain,
                'alternative_scenarios': alternative_scenarios,
                'reasoning_confidence': self._calculate_reasoning_confidence(
                    key_factors, supporting_evidence, risk_warnings
                )
            }
            
            self.logger.info("决策推理生成完成")
            return reasoning
            
        except Exception as e:
            self.logger.error(f"决策推理生成失败: {e}")
            return {
                'key_factors': [],
                'supporting_evidence': [],
                'risk_warnings': [],
                'decision_logic_chain': [],
                'alternative_scenarios': [],
                'reasoning_confidence': 50,
                'error': str(e)
            }

    def _extract_strategic_signals(self, strategic_data: Dict) -> Dict[str, Any]:
        """提取战略信号"""
        try:
            if not strategic_data.get('success', False):
                return {'signal_type': 'NO_DATA', 'strength': 0, 'confidence': 0}

            data = strategic_data.get('data', {})

            # 提取投资价值评分
            investment_value = data.get('investment_value', {})
            value_score = investment_value.get('investment_value_score', 0)

            # 提取趋势分析
            trend_analysis = data.get('trend_analysis', {})
            trend_direction = trend_analysis.get('trend_direction', 'UNKNOWN')

            # 将中文趋势描述转换为标准化值
            strategic_trend = self._normalize_trend_direction(trend_direction)
            trend_strength = self._calculate_trend_strength(trend_direction)

            # 提取风险评估
            risk_assessment = data.get('risk_assessment', {})
            risk_level = risk_assessment.get('strategic_risk_level', 'MEDIUM')

            # 综合评估
            overall_assessment = data.get('overall_assessment', {})
            recommendation = overall_assessment.get('recommendation', 'HOLD')
            confidence = overall_assessment.get('confidence', 50)

            return {
                'signal_type': recommendation,
                'strength': trend_strength,
                'confidence': confidence,
                'value_score': value_score,
                'trend': strategic_trend,
                'risk_level': risk_level,
                'timeframe': 'STRATEGIC'
            }

        except Exception as e:
            self.logger.error(f"提取战略信号失败: {e}")
            return {'signal_type': 'ERROR', 'strength': 0, 'confidence': 0}

    def _normalize_trend_direction(self, trend_direction: str) -> str:
        """将中文趋势描述转换为标准化值"""
        if not trend_direction or trend_direction == 'UNKNOWN':
            return 'UNKNOWN'

        trend_lower = trend_direction.lower()

        # 强烈看涨
        if any(keyword in trend_lower for keyword in ['强烈看涨', '强烈上涨', '大涨']):
            return 'STRONG_UP'
        # 看涨
        elif any(keyword in trend_lower for keyword in ['看涨', '上涨', '涨', '向上', '偏涨']):
            return 'UP'
        # 强烈看跌
        elif any(keyword in trend_lower for keyword in ['强烈看跌', '强烈下跌', '大跌']):
            return 'STRONG_DOWN'
        # 看跌
        elif any(keyword in trend_lower for keyword in ['看跌', '下跌', '跌', '向下', '偏跌']):
            return 'DOWN'
        # 中性/横盘
        elif any(keyword in trend_lower for keyword in ['中性', '横盘', '震荡', '盘整', '平稳']):
            return 'SIDEWAYS'
        else:
            return 'UNKNOWN'

    def _calculate_trend_strength(self, trend_direction: str) -> int:
        """根据趋势方向计算趋势强度"""
        if not trend_direction or trend_direction == 'UNKNOWN':
            return 0

        trend_lower = trend_direction.lower()

        # 强烈趋势
        if any(keyword in trend_lower for keyword in ['强烈', '大涨', '大跌']):
            return 90
        # 明确趋势
        elif any(keyword in trend_lower for keyword in ['看涨', '看跌', '上涨', '下跌']):
            return 70
        # 偏向趋势
        elif any(keyword in trend_lower for keyword in ['偏涨', '偏跌']):
            return 50
        # 中性/震荡
        elif any(keyword in trend_lower for keyword in ['中性', '震荡', '横盘']):
            return 30
        else:
            return 0

    def _extract_tactical_signals(self, tactical_data: Dict) -> Dict[str, Any]:
        """提取战术信号"""
        try:
            if not tactical_data.get('success', False):
                return {'signal_type': 'NO_DATA', 'strength': 0, 'confidence': 0}

            data = tactical_data.get('data', {})

            # 提取入场信号
            entry_signals = data.get('entry_signals', {})
            entry_signal = entry_signals.get('entry_signal', 'NO_SIGNAL')
            entry_strength_raw = entry_signals.get('signal_strength', 0)

            # 确保 entry_strength 是数字
            try:
                entry_strength = float(entry_strength_raw) if entry_strength_raw is not None else 0
            except (ValueError, TypeError):
                entry_strength = 0

            # 提取出场信号
            exit_signals = data.get('exit_signals', {})
            exit_signal = exit_signals.get('exit_signal', 'NO_SIGNAL')
            exit_strength_raw = exit_signals.get('signal_strength', 0)

            # 确保 exit_strength 是数字
            try:
                exit_strength = float(exit_strength_raw) if exit_strength_raw is not None else 0
            except (ValueError, TypeError):
                exit_strength = 0

            # 提取趋势分析
            trend_analysis = data.get('trend_analysis', {})
            trend_status = trend_analysis.get('trend_status', 'UNKNOWN')
            trend_confirmation = trend_analysis.get('trend_confirmation', False)

            # 将中文趋势描述转换为标准化值
            tactical_trend = self._normalize_trend_direction(trend_status)

            # 提取风险评估
            risk_assessment = data.get('risk_assessment', {})
            risk_level = risk_assessment.get('tactical_risk_level', 'MEDIUM')

            # 综合战术建议
            tactical_recommendation = data.get('tactical_recommendation', {})
            action = tactical_recommendation.get('action', 'HOLD')
            confidence = tactical_recommendation.get('confidence', 50)

            return {
                'signal_type': action,
                'strength': max(entry_strength, exit_strength),
                'confidence': confidence,
                'entry_signal': entry_signal,
                'exit_signal': exit_signal,
                'trend': tactical_trend,
                'trend_confirmation': trend_confirmation,
                'risk_level': risk_level,
                'timeframe': 'TACTICAL'
            }

        except Exception as e:
            self.logger.error(f"提取战术信号失败: {e}")
            return {'signal_type': 'ERROR', 'strength': 0, 'confidence': 0}

    def _extract_execution_signals(self, execution_data: Dict) -> Dict[str, Any]:
        """提取执行信号"""
        try:
            if not execution_data.get('success', False):
                return {'signal_type': 'NO_DATA', 'strength': 0, 'confidence': 0}

            data = execution_data.get('data', {})

            # 提取即时行动建议
            immediate_action = data.get('immediate_action', {})
            action = immediate_action.get('immediate_action', 'HOLD')
            action_strength = immediate_action.get('action_strength', 0)
            urgency_level = immediate_action.get('urgency_level', 'LOW')

            # 提取最佳入场价格
            optimal_entry = data.get('optimal_entry', {})
            entry_confidence = optimal_entry.get('confidence', 0)

            # 提取风险评估
            risk_assessment = data.get('risk_assessment', {})
            risk_level = risk_assessment.get('execution_risk_level', 'MEDIUM')

            # 提取执行计划
            execution_plan = data.get('execution_plan', {})
            recommended_action = execution_plan.get('recommended_action', 'HOLD')
            position_size = execution_plan.get('position_size', 'NORMAL')
            execution_timing = execution_plan.get('execution_timing', 'IMMEDIATE')

            return {
                'signal_type': recommended_action,
                'strength': action_strength,
                'confidence': entry_confidence,
                'immediate_action': action,
                'urgency_level': urgency_level,
                'position_size': position_size,
                'execution_timing': execution_timing,
                'risk_level': risk_level,
                'timeframe': 'EXECUTION'
            }

        except Exception as e:
            self.logger.error(f"提取执行信号失败: {e}")
            return {'signal_type': 'ERROR', 'strength': 0, 'confidence': 0}

    def _extract_supply_demand_signals(self, supply_demand_data: Dict) -> Dict[str, Any]:
        """提取供需信号"""
        try:
            if not supply_demand_data.get('success', False):
                return {'signal_type': 'NO_DATA', 'strength': 0, 'confidence': 0}

            data = supply_demand_data.get('data', {})

            # 提取综合分析结果
            combined_analysis = data.get('combined_analysis', {})
            sd_status = combined_analysis.get('combined_supply_demand_status', 'UNKNOWN')
            confidence = combined_analysis.get('confidence', 0)

            # 提取市场平衡评分
            balance_score = data.get('market_balance_score', 50)

            # 转换供需状态为信号类型
            signal_mapping = {
                'SUPPLY_EXCESS': 'SELL',
                'DEMAND_EXCESS': 'BUY',
                'BALANCED': 'HOLD',
                'UNKNOWN': 'HOLD'
            }

            signal_type = signal_mapping.get(sd_status, 'HOLD')

            # 计算信号强度
            if sd_status in ['SUPPLY_EXCESS', 'DEMAND_EXCESS']:
                strength = min(100, confidence + (100 - balance_score))
            else:
                strength = balance_score

            return {
                'signal_type': signal_type,
                'strength': strength,
                'confidence': confidence,
                'supply_demand_status': sd_status,
                'balance_score': balance_score,
                'timeframe': 'SUPPLY_DEMAND'
            }

        except Exception as e:
            self.logger.error(f"提取供需信号失败: {e}")
            return {'signal_type': 'ERROR', 'strength': 0, 'confidence': 0}

    def _calculate_consistency_metrics(self, signals: Dict) -> Dict[str, Any]:
        """计算一致性指标"""
        try:
            # 提取信号类型
            signal_types = []
            confidences = []
            strengths = []

            for timeframe, signal in signals.items():
                if signal.get('signal_type') not in ['NO_DATA', 'ERROR']:
                    signal_types.append(signal.get('signal_type', 'HOLD'))
                    confidences.append(signal.get('confidence', 0))
                    strengths.append(signal.get('strength', 0))

            if not signal_types:
                return {
                    'overall_consistency_score': 0,
                    'signal_agreement_rate': 0,
                    'confidence_variance': 100,
                    'strength_variance': 100
                }

            # 计算信号一致性
            from collections import Counter
            signal_counts = Counter(signal_types)
            most_common_signal = signal_counts.most_common(1)[0]
            agreement_rate = (most_common_signal[1] / len(signal_types)) * 100

            # 计算置信度方差
            confidence_variance = np.var(confidences) if len(confidences) > 1 else 0

            # 计算强度方差
            strength_variance = np.var(strengths) if len(strengths) > 1 else 0

            # 综合一致性评分
            consistency_score = (
                agreement_rate * 0.5 +
                max(0, 100 - confidence_variance) * 0.3 +
                max(0, 100 - strength_variance) * 0.2
            )

            return {
                'overall_consistency_score': consistency_score,
                'signal_agreement_rate': agreement_rate,
                'confidence_variance': confidence_variance,
                'strength_variance': strength_variance,
                'dominant_signal': most_common_signal[0],
                'signal_distribution': dict(signal_counts)
            }

        except Exception as e:
            self.logger.error(f"计算一致性指标失败: {e}")
            return {
                'overall_consistency_score': 0,
                'signal_agreement_rate': 0,
                'confidence_variance': 100,
                'strength_variance': 100
            }

    def _identify_consistency_issues(self, signals: Dict, consistency_metrics: Dict) -> List[Dict]:
        """识别一致性问题"""
        try:
            issues = []

            # 检查信号一致性
            agreement_rate = consistency_metrics.get('signal_agreement_rate', 0)
            if agreement_rate < 50:
                issues.append({
                    'type': 'SIGNAL_DISAGREEMENT',
                    'severity': 'HIGH',
                    'description': f'信号一致性较低({agreement_rate:.1f}%)',
                    'affected_timeframes': list(signals.keys())
                })

            # 检查置信度差异
            confidence_variance = consistency_metrics.get('confidence_variance', 0)
            if confidence_variance > 400:  # 标准差>20
                issues.append({
                    'type': 'CONFIDENCE_VARIANCE',
                    'severity': 'MEDIUM',
                    'description': f'置信度差异较大(方差: {confidence_variance:.1f})',
                    'affected_timeframes': [tf for tf, s in signals.items() if s.get('confidence', 0) != 0]
                })

            # 检查强度差异
            strength_variance = consistency_metrics.get('strength_variance', 0)
            if strength_variance > 900:  # 标准差>30
                issues.append({
                    'type': 'STRENGTH_VARIANCE',
                    'severity': 'MEDIUM',
                    'description': f'信号强度差异较大(方差: {strength_variance:.1f})',
                    'affected_timeframes': [tf for tf, s in signals.items() if s.get('strength', 0) != 0]
                })

            # 检查数据缺失
            missing_data_timeframes = [tf for tf, s in signals.items() if s.get('signal_type') in ['NO_DATA', 'ERROR']]
            if missing_data_timeframes:
                issues.append({
                    'type': 'MISSING_DATA',
                    'severity': 'HIGH' if len(missing_data_timeframes) > 2 else 'MEDIUM',
                    'description': f'缺少{len(missing_data_timeframes)}个时间框架的数据',
                    'affected_timeframes': missing_data_timeframes
                })

            return issues

        except Exception as e:
            self.logger.error(f"识别一致性问题失败: {e}")
            return []

    def _identify_conflicts(self, signals: Dict, consistency_issues: List) -> List[Dict]:
        """识别冲突"""
        try:
            conflicts = []

            # 基于信号类型识别冲突
            signal_types = {}
            for timeframe, signal in signals.items():
                signal_type = signal.get('signal_type', 'HOLD')
                if signal_type not in ['NO_DATA', 'ERROR']:
                    signal_types[timeframe] = signal_type

            # 检查买卖冲突
            buy_signals = [tf for tf, st in signal_types.items() if st in ['BUY', 'STRONG_BUY']]
            sell_signals = [tf for tf, st in signal_types.items() if st in ['SELL', 'STRONG_SELL']]

            if buy_signals and sell_signals:
                conflicts.append({
                    'type': 'BUY_SELL_CONFLICT',
                    'severity': 'HIGH',
                    'description': '存在买入和卖出信号冲突',
                    'buy_timeframes': buy_signals,
                    'sell_timeframes': sell_signals,
                    'dimension': 'signal_direction'
                })

            # 检查风险等级冲突
            risk_levels = {}
            for timeframe, signal in signals.items():
                risk_level = signal.get('risk_level', 'MEDIUM')
                if risk_level != 'MEDIUM':
                    risk_levels[timeframe] = risk_level

            high_risk = [tf for tf, rl in risk_levels.items() if rl == 'HIGH']
            low_risk = [tf for tf, rl in risk_levels.items() if rl == 'LOW']

            if high_risk and low_risk:
                conflicts.append({
                    'type': 'RISK_LEVEL_CONFLICT',
                    'severity': 'MEDIUM',
                    'description': '风险等级评估存在冲突',
                    'high_risk_timeframes': high_risk,
                    'low_risk_timeframes': low_risk,
                    'dimension': 'risk_assessment'
                })

            return conflicts

        except Exception as e:
            self.logger.error(f"识别冲突失败: {e}")
            return []

    def _apply_conflict_resolution_strategy(self, conflict: Dict, signals: Dict) -> Dict[str, Any]:
        """应用冲突解决策略"""
        try:
            conflict_type = conflict.get('type', '')

            if conflict_type == 'BUY_SELL_CONFLICT':
                # 买卖冲突：优先考虑战略和战术信号
                buy_timeframes = conflict.get('buy_timeframes', [])
                sell_timeframes = conflict.get('sell_timeframes', [])

                # 权重优先级：strategic > tactical > execution > supply_demand
                priority_weights = {'strategic': 4, 'tactical': 3, 'execution': 2, 'supply_demand': 1}

                buy_weight = sum(priority_weights.get(tf, 0) for tf in buy_timeframes)
                sell_weight = sum(priority_weights.get(tf, 0) for tf in sell_timeframes)

                if buy_weight > sell_weight:
                    resolved_signal = 'BUY'
                    confidence = 70
                elif sell_weight > buy_weight:
                    resolved_signal = 'SELL'
                    confidence = 70
                else:
                    resolved_signal = 'HOLD'
                    confidence = 40

                return {
                    'conflict_type': conflict_type,
                    'strategy': 'WEIGHTED_PRIORITY',
                    'resolved_signal': resolved_signal,
                    'confidence': confidence,
                    'reasoning': f'基于权重优先级解决冲突，买入权重{buy_weight}，卖出权重{sell_weight}'
                }

            elif conflict_type == 'RISK_LEVEL_CONFLICT':
                # 风险等级冲突：采用保守策略，选择较高风险等级
                return {
                    'conflict_type': conflict_type,
                    'strategy': 'CONSERVATIVE_APPROACH',
                    'resolved_signal': 'HIGH',
                    'confidence': 80,
                    'reasoning': '采用保守策略，选择较高风险等级'
                }

            else:
                return {
                    'conflict_type': conflict_type,
                    'strategy': 'NO_STRATEGY',
                    'resolved_signal': None,
                    'confidence': 0,
                    'reasoning': '未找到适用的冲突解决策略'
                }

        except Exception as e:
            self.logger.error(f"应用冲突解决策略失败: {e}")
            return {
                'conflict_type': conflict.get('type', 'UNKNOWN'),
                'strategy': 'ERROR',
                'resolved_signal': None,
                'confidence': 0,
                'reasoning': f'策略应用失败: {str(e)}'
            }

    def _generate_coordinated_signals(self, original_signals: Dict, resolved_signals: Dict) -> Dict[str, Any]:
        """生成协调后的信号"""
        try:
            coordinated = {}

            for timeframe, signal in original_signals.items():
                # 如果有解决方案，使用解决后的信号
                if timeframe in resolved_signals:
                    coordinated[timeframe] = {
                        **signal,
                        'signal_type': resolved_signals[timeframe],
                        'is_resolved': True
                    }
                else:
                    coordinated[timeframe] = {
                        **signal,
                        'is_resolved': False
                    }

            return coordinated

        except Exception as e:
            self.logger.error(f"生成协调信号失败: {e}")
            return original_signals

    def _calculate_weighted_scores(self, coordinated_signals: Dict) -> Dict[str, Any]:
        """计算加权综合评分"""
        try:
            weighted_scores = {}
            total_score = 0
            total_weight = 0

            # 信号类型评分映射
            signal_score_mapping = {
                'STRONG_BUY': 90,
                'BUY': 70,
                'HOLD': 50,
                'SELL': 30,
                'STRONG_SELL': 10,
                'NO_DATA': 50,
                'ERROR': 50
            }

            for timeframe, signal in coordinated_signals.items():
                if timeframe in self.decision_weights:
                    weight = self.decision_weights[timeframe]
                    signal_type = signal.get('signal_type', 'HOLD')
                    base_score = signal_score_mapping.get(signal_type, 50)

                    # 根据置信度调整评分
                    confidence = signal.get('confidence', 50) / 100
                    adjusted_score = base_score * (0.5 + 0.5 * confidence)

                    weighted_score = adjusted_score * weight
                    weighted_scores[timeframe] = {
                        'base_score': base_score,
                        'adjusted_score': adjusted_score,
                        'weight': weight,
                        'weighted_score': weighted_score,
                        'confidence': signal.get('confidence', 50)
                    }

                    total_score += weighted_score
                    total_weight += weight

            # 归一化总分
            if total_weight > 0:
                normalized_score = total_score / total_weight
            else:
                normalized_score = 50

            return {
                'timeframe_scores': weighted_scores,
                'total_score': normalized_score,
                'total_weight': total_weight
            }

        except Exception as e:
            self.logger.error(f"计算加权评分失败: {e}")
            return {
                'timeframe_scores': {},
                'total_score': 50,
                'total_weight': 0
            }

    def _determine_recommendation_type(self, weighted_scores: Dict) -> str:
        """确定推荐类型（基于现有InvestmentRecommendationService逻辑）"""
        try:
            total_score = weighted_scores.get('total_score', 50)

            if total_score >= self.decision_thresholds['strong_buy']:
                return 'STRONG_BUY'
            elif total_score >= self.decision_thresholds['buy']:
                return 'BUY'
            elif total_score >= self.decision_thresholds['hold']:
                return 'HOLD'
            elif total_score >= self.decision_thresholds['sell']:
                return 'SELL'
            else:
                return 'STRONG_SELL'

        except Exception:
            return 'HOLD'

    def _calculate_decision_confidence(self, coordinated_signals: Dict, conflict_count: int, coordination_analysis: Dict) -> float:
        """计算决策置信度（基于现有InvestmentRecommendationService逻辑）"""
        try:
            # 基础置信度
            base_confidence = 50

            # 信号数量加分
            valid_signals = sum(1 for s in coordinated_signals.values() if s.get('signal_type') not in ['NO_DATA', 'ERROR'])
            count_bonus = min(valid_signals * 10, 30)

            # 一致性加分
            consistency_score = coordination_analysis.get('trend_consistency', {}).get('consistency_score', 0)
            consistency_bonus = consistency_score * 0.2

            # 冲突惩罚
            conflict_penalty = conflict_count * 10

            # 综合置信度
            total_confidence = base_confidence + count_bonus + consistency_bonus - conflict_penalty

            return max(0, min(100, total_confidence))

        except Exception:
            return 50.0

    def _determine_risk_level(self, coordinated_signals: Dict) -> str:
        """确定风险等级（基于现有InvestmentRecommendationService逻辑）"""
        try:
            risk_levels = []

            for signal in coordinated_signals.values():
                risk_level = signal.get('risk_level', 'MEDIUM')
                if risk_level in ['LOW', 'MEDIUM', 'HIGH']:
                    risk_levels.append(risk_level)

            if not risk_levels:
                return 'MEDIUM'

            high_count = risk_levels.count('HIGH')
            medium_count = risk_levels.count('MEDIUM')
            total_count = len(risk_levels)

            # 采用保守策略
            if high_count >= total_count * 0.3:
                return 'HIGH'
            elif medium_count >= total_count * 0.5:
                return 'MEDIUM'
            else:
                return 'LOW'

        except Exception:
            return 'MEDIUM'

    def _determine_investment_horizon(self, coordinated_signals: Dict, multi_timeframe_result: Dict) -> str:
        """确定投资期限"""
        try:
            # 基于战略分析的投资价值评分
            strategic_signal = coordinated_signals.get('strategic', {})
            value_score = strategic_signal.get('value_score', 50)

            if value_score > 70:
                return 'LONG_TERM'
            elif value_score < 40:
                return 'SHORT_TERM'
            else:
                return 'MEDIUM_TERM'

        except Exception:
            return 'MEDIUM_TERM'

    def _generate_specific_recommendations(self, recommendation_type: str, coordinated_signals: Dict, multi_timeframe_result: Dict) -> Dict[str, Any]:
        """生成具体建议"""
        try:
            recommendations = {
                'position_sizing': 'NORMAL',
                'entry_strategy': 'GRADUAL',
                'exit_strategy': 'SYSTEMATIC',
                'monitoring_frequency': 'DAILY',
                'specific_actions': []
            }

            # 基于推荐类型调整建议
            if recommendation_type in ['STRONG_BUY', 'BUY']:
                recommendations['specific_actions'].extend([
                    '寻找合适的买入时机',
                    '分批建仓以降低风险',
                    '设置止损位保护资金'
                ])

                if recommendation_type == 'STRONG_BUY':
                    recommendations['position_sizing'] = 'LARGE'
                    recommendations['monitoring_frequency'] = 'HOURLY'

            elif recommendation_type in ['STRONG_SELL', 'SELL']:
                recommendations['specific_actions'].extend([
                    '考虑减仓或清仓',
                    '关注反弹机会',
                    '重新评估投资逻辑'
                ])

                if recommendation_type == 'STRONG_SELL':
                    recommendations['position_sizing'] = 'MINIMAL'
                    recommendations['exit_strategy'] = 'IMMEDIATE'

            else:  # HOLD
                recommendations['specific_actions'].extend([
                    '维持当前仓位',
                    '密切关注市场变化',
                    '等待明确信号'
                ])

            # 基于执行信号调整
            execution_signal = coordinated_signals.get('execution', {})
            urgency_level = execution_signal.get('urgency_level', 'LOW')

            if urgency_level == 'HIGH':
                recommendations['monitoring_frequency'] = 'REAL_TIME'
                recommendations['entry_strategy'] = 'IMMEDIATE'

            return recommendations

        except Exception as e:
            self.logger.error(f"生成具体建议失败: {e}")
            return {
                'position_sizing': 'NORMAL',
                'entry_strategy': 'GRADUAL',
                'exit_strategy': 'SYSTEMATIC',
                'monitoring_frequency': 'DAILY',
                'specific_actions': ['维持观望']
            }

    def _generate_recommendation_summary(self, final_decision: Dict) -> Dict[str, Any]:
        """生成推荐摘要（兼容现有系统格式）"""
        try:
            return {
                'item_id': self.item_id,
                'recommendation_type': final_decision.get('recommendation_type', 'HOLD'),
                'risk_level': final_decision.get('risk_level', 'MEDIUM'),
                'investment_horizon': final_decision.get('investment_horizon', 'MEDIUM_TERM'),
                'recommendation_reason': final_decision.get('decision_reasoning', '基于多维度分析得出投资建议'),
                'market_context': final_decision.get('market_context', {}),
                'recommendation_date': datetime.now().date(),
                'recommendation_time': datetime.now()
            }

        except Exception as e:
            self.logger.error(f"生成推荐摘要失败: {e}")
            return {
                'item_id': self.item_id,
                'recommendation_type': 'HOLD',
                'risk_level': 'MEDIUM',
                'recommendation_reason': '分析过程中出现错误，建议持有观望',
                'error': str(e)
            }

    def _generate_simple_reason(self, final_decision: Dict) -> str:
        """生成简单推荐理由"""
        try:
            recommendation_type = final_decision.get('recommendation_type', 'HOLD')
            total_score = final_decision.get('total_score', 50)
            confidence_level = final_decision.get('confidence_level', 50)

            type_desc_map = {
                'STRONG_BUY': '强烈建议买入',
                'BUY': '建议买入',
                'HOLD': '建议持有',
                'SELL': '建议卖出',
                'STRONG_SELL': '强烈建议卖出'
            }

            type_desc = type_desc_map.get(recommendation_type, '无明确建议')

            return f"经过多时间框架智能分析，综合评分{total_score:.1f}分，置信度{confidence_level:.1f}%，{type_desc}。"

        except Exception:
            return "基于多维度分析，建议持有观望。"

    # 简化版本的推理方法（核心功能）
    def _identify_key_decision_factors(self, multi_timeframe_result: Dict, consistency_analysis: Dict, final_decision: Dict) -> List[str]:
        """识别关键决策因素"""
        try:
            factors = []

            # 基于一致性分析
            consistency_score = consistency_analysis.get('overall_consistency', 0)
            if consistency_score > 70:
                factors.append(f"多时间框架信号高度一致({consistency_score:.1f}%)")
            elif consistency_score < 40:
                factors.append(f"多时间框架信号存在分歧({consistency_score:.1f}%)")

            # 基于评分
            total_score = final_decision.get('total_score', 50)
            if total_score > 70:
                factors.append(f"综合评分优秀({total_score:.1f}分)")
            elif total_score < 40:
                factors.append(f"综合评分偏低({total_score:.1f}分)")

            return factors

        except Exception:
            return ["基于多维度综合分析"]

    def _collect_supporting_evidence(self, multi_timeframe_result: Dict, final_decision: Dict) -> List[str]:
        """收集支持证据"""
        try:
            evidence = []

            # 从各时间框架提取证据
            timeframe_analyses = multi_timeframe_result.get('timeframe_analyses', {})

            for timeframe, analysis in timeframe_analyses.items():
                if analysis.get('success', False):
                    # 简化的证据提取
                    evidence.append(f"{timeframe}分析支持当前决策")

            return evidence

        except Exception:
            return ["多维度分析结果支持"]

    def _generate_risk_warnings(self, final_decision: Dict, conflict_resolution: Dict, multi_timeframe_result: Dict) -> List[str]:
        """生成风险警告"""
        try:
            warnings = []

            # 基于风险等级
            risk_level = final_decision.get('risk_level', 'MEDIUM')
            if risk_level == 'HIGH':
                warnings.append("整体风险水平较高，建议谨慎操作")

            # 基于冲突数量
            conflict_count = conflict_resolution.get('conflict_count', 0)
            if conflict_count > 2:
                warnings.append("存在多个信号冲突，增加决策不确定性")

            # 基于置信度
            confidence_level = final_decision.get('confidence_level', 50)
            if confidence_level < 50:
                warnings.append("决策置信度较低，建议等待更多确认信号")

            return warnings

        except Exception:
            return ["请注意市场风险"]

    def _build_decision_logic_chain(self, consistency_analysis: Dict, conflict_resolution: Dict, final_decision: Dict) -> List[str]:
        """构建决策逻辑链"""
        try:
            logic_chain = [
                "1. 收集多时间框架分析结果",
                "2. 检查信号一致性",
                "3. 识别并解决信号冲突",
                "4. 计算加权综合评分",
                "5. 生成最终投资建议"
            ]

            return logic_chain

        except Exception:
            return ["基于标准决策流程"]

    def _generate_alternative_scenarios(self, multi_timeframe_result: Dict, final_decision: Dict) -> List[Dict]:
        """生成替代方案"""
        try:
            scenarios = []

            recommendation_type = final_decision.get('recommendation_type', 'HOLD')

            if recommendation_type == 'BUY':
                scenarios.append({
                    'scenario': '如果市场出现回调',
                    'action': '可考虑加仓机会',
                    'condition': '价格回调5-10%'
                })
            elif recommendation_type == 'SELL':
                scenarios.append({
                    'scenario': '如果出现反弹信号',
                    'action': '可考虑部分回补',
                    'condition': '技术指标转强'
                })

            return scenarios

        except Exception:
            return []

    def _calculate_reasoning_confidence(self, key_factors: List, supporting_evidence: List, risk_warnings: List) -> float:
        """计算推理置信度"""
        try:
            base_confidence = 60

            # 基于因素数量
            factor_bonus = min(len(key_factors) * 5, 20)

            # 基于证据数量
            evidence_bonus = min(len(supporting_evidence) * 3, 15)

            # 基于风险警告数量（惩罚）
            warning_penalty = len(risk_warnings) * 5

            total_confidence = base_confidence + factor_bonus + evidence_bonus - warning_penalty

            return max(0, min(100, total_confidence))

        except Exception:
            return 60.0

    def get_latest_fusion_result(self) -> Optional[Dict[str, Any]]:
        """获取最新融合结果"""
        return self.fusion_cache.get('latest_fusion')

    def _initialize_decision_weights(self) -> Dict[str, float]:
        """
        初始化决策权重体系

        基于CS2饰品市场特性和修复后的算法重新设计权重分配：

        理论依据：
        1. 供需关系是市场定价的根本驱动力，应该有更高权重
        2. 战术分析（日K）反映中期趋势，对短期决策最重要
        3. 战略分析（周K）提供大趋势背景，权重适中
        4. 执行分析（时K）反映短期波动，权重相对较低

        市场特性考虑：
        - CS2饰品市场供需变化对价格影响显著
        - 技术分析在虚拟商品市场有效性较传统金融市场低
        - 流动性和市场情绪对短期价格影响较大

        Returns:
            Dict[str, float]: 权重配置字典
        """
        # 新的权重配置 - 提高供需分析权重
        base_weights = {
            'supply_demand': 0.30,  # 供需分析权重30% (从15%提升)
            'tactical': 0.28,       # 战术分析权重28% (日K，中期趋势)
            'strategic': 0.25,      # 战略分析权重25% (周K，长期趋势)
            'execution': 0.17       # 执行分析权重17% (时K，短期波动)
        }

        # 验证权重总和为1
        total_weight = sum(base_weights.values())
        if abs(total_weight - 1.0) > 0.001:
            self.logger.warning(f"权重总和不为1: {total_weight}，进行归一化")
            # 归一化权重
            base_weights = {k: v / total_weight for k, v in base_weights.items()}

        self.logger.info(f"决策权重配置: {base_weights}")
        return base_weights

    def get_dynamic_weights(self, market_conditions: Dict[str, Any] = None) -> Dict[str, float]:
        """
        获取动态调整的权重

        根据市场条件动态调整权重分配：
        - 高波动期：提高执行分析权重
        - 趋势明确期：提高战术/战略分析权重
        - 供需失衡期：进一步提高供需分析权重

        Args:
            market_conditions: 市场条件字典

        Returns:
            Dict[str, float]: 动态调整后的权重
        """
        try:
            base_weights = self.decision_weights.copy()

            if not market_conditions:
                return base_weights

            # 获取市场条件指标
            volatility = market_conditions.get('volatility', 'NORMAL')
            trend_strength = market_conditions.get('trend_strength', 'MODERATE')
            supply_demand_imbalance = market_conditions.get('supply_demand_imbalance', 'BALANCED')
            liquidity = market_conditions.get('liquidity', 'NORMAL')

            # 动态调整权重
            adjusted_weights = base_weights.copy()

            # 1. 根据波动性调整
            if volatility == 'HIGH':
                # 高波动期，提高执行分析权重，降低战略分析权重
                adjusted_weights['execution'] *= 1.2
                adjusted_weights['strategic'] *= 0.9
            elif volatility == 'LOW':
                # 低波动期，提高战略分析权重
                adjusted_weights['strategic'] *= 1.1
                adjusted_weights['execution'] *= 0.9

            # 2. 根据趋势强度调整
            if trend_strength == 'STRONG':
                # 强趋势期，提高技术分析权重
                adjusted_weights['tactical'] *= 1.1
                adjusted_weights['strategic'] *= 1.05
                adjusted_weights['supply_demand'] *= 0.95

            # 3. 根据供需失衡程度调整
            if supply_demand_imbalance in ['SEVERE_OVERSUPPLY', 'SEVERE_UNDERSUPPLY']:
                # 严重供需失衡，进一步提高供需分析权重
                adjusted_weights['supply_demand'] *= 1.15
                adjusted_weights['tactical'] *= 0.95
                adjusted_weights['execution'] *= 0.9

            # 4. 根据流动性调整
            if liquidity == 'LOW':
                # 低流动性，提高供需分析权重，降低技术分析权重
                adjusted_weights['supply_demand'] *= 1.1
                adjusted_weights['tactical'] *= 0.95
                adjusted_weights['execution'] *= 0.9

            # 归一化权重
            total_weight = sum(adjusted_weights.values())
            if total_weight > 0:
                adjusted_weights = {k: v / total_weight for k, v in adjusted_weights.items()}

            self.logger.debug(f"动态权重调整: {base_weights} -> {adjusted_weights}")
            return adjusted_weights

        except Exception as e:
            self.logger.error(f"动态权重调整失败: {e}")
            return self.decision_weights

    def update_weights_based_on_performance(self, performance_metrics: Dict[str, float]):
        """
        基于历史表现更新权重

        Args:
            performance_metrics: 各时间框架的表现指标
        """
        try:
            if not performance_metrics:
                return

            # 计算表现权重调整因子
            total_performance = sum(performance_metrics.values())
            if total_performance <= 0:
                return

            # 基于表现调整权重
            performance_weights = {}
            for timeframe, performance in performance_metrics.items():
                if timeframe in self.decision_weights:
                    # 表现好的时间框架获得更高权重
                    adjustment_factor = performance / total_performance * len(performance_metrics)
                    performance_weights[timeframe] = self.decision_weights[timeframe] * adjustment_factor

            # 归一化
            total_weight = sum(performance_weights.values())
            if total_weight > 0:
                self.decision_weights = {k: v / total_weight for k, v in performance_weights.items()}
                self.logger.info(f"基于表现更新权重: {self.decision_weights}")

        except Exception as e:
            self.logger.error(f"基于表现更新权重失败: {e}")

    def get_weight_explanation(self) -> Dict[str, str]:
        """
        获取权重设置的理论解释

        Returns:
            Dict[str, str]: 各权重的理论依据说明
        """
        return {
            'supply_demand': (
                f"供需分析权重 {self.decision_weights.get('supply_demand', 0):.1%}: "
                "供需关系是市场定价的根本驱动力，特别是在CS2饰品这种稀缺性商品市场中，"
                "供需平衡状态直接决定价格走向。修复后的经济学算法提供了更准确的供需分析。"
            ),
            'tactical': (
                f"战术分析权重 {self.decision_weights.get('tactical', 0):.1%}: "
                "基于日K线的中期技术分析，反映1-4周的价格趋势，对短中期投资决策最为重要。"
                "日K数据经过合并处理，提供了完整的价格历史。"
            ),
            'strategic': (
                f"战略分析权重 {self.decision_weights.get('strategic', 0):.1%}: "
                "基于周K线的长期技术分析，提供大趋势背景和长期投资方向指导。"
                "适合长期持有策略的投资者参考。"
            ),
            'execution': (
                f"执行分析权重 {self.decision_weights.get('execution', 0):.1%}: "
                "基于小时K线的短期技术分析，反映日内波动和短期交易机会。"
                "权重相对较低，因为短期波动的预测难度较大。"
            )
        }

    def _extract_market_conditions(self, multi_timeframe_result: Dict) -> Dict[str, str]:
        """
        从多时间框架分析结果中提取市场条件

        Args:
            multi_timeframe_result: 多时间框架分析结果

        Returns:
            Dict[str, str]: 市场条件字典
        """
        try:
            market_conditions = {}

            # 提取供需分析结果
            supply_demand_data = multi_timeframe_result.get('supply_demand_analysis', {})
            if supply_demand_data.get('success'):
                supply_demand_result = supply_demand_data.get('analysis_result', {})

                # 提取供需失衡程度
                if 'unified_supply_analysis' in supply_demand_result:
                    unified_analysis = supply_demand_result['unified_supply_analysis']
                    if isinstance(unified_analysis, dict) and 'supply_demand_analysis' in unified_analysis:
                        balance_status = unified_analysis['supply_demand_analysis'].get('balance_status', 'BALANCED')
                        market_conditions['supply_demand_imbalance'] = balance_status

                    # 提取流动性状态
                    if 'spread_analysis' in unified_analysis:
                        liquidity_status = unified_analysis['spread_analysis'].get('liquidity_status', 'NORMAL')
                        if liquidity_status in ['VERY_POOR', 'POOR']:
                            market_conditions['liquidity'] = 'LOW'
                        elif liquidity_status in ['EXCELLENT', 'GOOD']:
                            market_conditions['liquidity'] = 'HIGH'
                        else:
                            market_conditions['liquidity'] = 'NORMAL'

            # 提取波动性（从技术分析结果）
            timeframe_data = multi_timeframe_result.get('timeframe_analysis', {})
            volatility_indicators = []

            for timeframe in ['tactical', 'execution']:
                if timeframe in timeframe_data:
                    tf_data = timeframe_data[timeframe]
                    if tf_data.get('success') and 'technical_indicators' in tf_data:
                        # 可以从ATR、布林带宽度等指标判断波动性
                        # 这里简化处理，基于价格变化幅度
                        pass

            # 默认值
            market_conditions.setdefault('volatility', 'NORMAL')
            market_conditions.setdefault('trend_strength', 'MODERATE')
            market_conditions.setdefault('supply_demand_imbalance', 'BALANCED')
            market_conditions.setdefault('liquidity', 'NORMAL')

            self.logger.debug(f"提取的市场条件: {market_conditions}")
            return market_conditions

        except Exception as e:
            self.logger.error(f"提取市场条件失败: {e}")
            return {
                'volatility': 'NORMAL',
                'trend_strength': 'MODERATE',
                'supply_demand_imbalance': 'BALANCED',
                'liquidity': 'NORMAL'
            }
