"""
智能触发条件引擎

基于具体分析结果的买入/卖出信号触发条件，避免依赖综合评分，
而是基于各层分析的具体结论进行逻辑判断。
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass
from loguru import logger


class SignalType(Enum):
    """信号类型"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    RISK_ALERT = "risk_alert"


class RiskLevel(Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class AlertLevel(Enum):
    """预警级别"""
    LEVEL_1 = "level_1"  # 一级预警：轻微风险增加
    LEVEL_2 = "level_2"  # 二级预警：中等风险出现
    LEVEL_3 = "level_3"  # 三级预警：高风险需立即处理


@dataclass
class TriggerResult:
    """触发结果"""
    signal_type: SignalType
    confidence: float
    trigger_reasons: List[str]
    analysis_summary: Dict[str, str]
    risk_level: RiskLevel
    alert_level: Optional[AlertLevel] = None
    recommended_action: str = ""
    trigger_timestamp: datetime = None
    
    def __post_init__(self):
        if self.trigger_timestamp is None:
            self.trigger_timestamp = datetime.now()


class TriggerConditionEngine:
    """智能触发条件引擎"""
    
    def __init__(self, user_config: Optional[Dict] = None):
        """
        初始化触发条件引擎
        
        Args:
            user_config: 用户配置，包含触发条件和风险偏好
        """
        self.logger = logger.bind(engine=self.__class__.__name__)
        self.user_config = user_config or self._get_default_config()
        
        # 触发历史记录
        self.trigger_history: List[TriggerResult] = []
        
        self.logger.info("智能触发条件引擎初始化完成")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'buy_trigger_conditions': {
                'strategic_required': True,      # 需要战略分析支持
                'tactical_required': True,       # 需要战术分析支持
                'execution_required': True,      # 需要执行分析支持
                'supply_demand_required': True,  # 需要供需分析支持
                'max_risk_level': 'medium',      # 最大可接受风险等级
                'min_confidence': 0.7            # 最低置信度要求
            },
            'sell_trigger_conditions': {
                'stop_loss_enabled': True,       # 启用止损
                'take_profit_enabled': True,     # 启用止盈
                'risk_exit_enabled': True,       # 启用风险退出
                'trend_reversal_exit': True,     # 趋势反转退出
                'max_acceptable_risk': 'high'    # 卖出时最大可接受风险
            },
            'risk_alert_conditions': {
                'level_1_threshold': 0.3,        # 一级预警阈值
                'level_2_threshold': 0.6,        # 二级预警阈值
                'level_3_threshold': 0.8,        # 三级预警阈值
                'enable_volatility_alert': True, # 启用波动率预警
                'enable_volume_alert': True      # 启用成交量预警
            }
        }
    
    def analyze_buy_signals(self, analysis_results: Dict[str, Any]) -> TriggerResult:
        """
        分析买入信号
        
        Args:
            analysis_results: 包含四层分析结果的字典
            
        Returns:
            TriggerResult: 触发结果
        """
        try:
            self.logger.info("开始分析买入信号...")
            
            trigger_reasons = []
            analysis_summary = {}
            confidence_scores = []
            
            # 提取各层分析结果
            strategic_result = analysis_results.get('strategic', {})
            tactical_result = analysis_results.get('tactical', {})
            execution_result = analysis_results.get('execution', {})
            supply_demand_result = analysis_results.get('supply_demand', {})
            
            # 战略分析检查
            strategic_support = self._check_strategic_buy_support(strategic_result)
            if strategic_support['supported']:
                trigger_reasons.append(strategic_support['reason'])
                confidence_scores.append(strategic_support['confidence'])
                analysis_summary['strategic'] = strategic_support['summary']
            
            # 战术分析检查
            tactical_support = self._check_tactical_buy_support(tactical_result)
            if tactical_support['supported']:
                trigger_reasons.append(tactical_support['reason'])
                confidence_scores.append(tactical_support['confidence'])
                analysis_summary['tactical'] = tactical_support['summary']
            
            # 执行分析检查
            execution_support = self._check_execution_buy_support(execution_result)
            if execution_support['supported']:
                trigger_reasons.append(execution_support['reason'])
                confidence_scores.append(execution_support['confidence'])
                analysis_summary['execution'] = execution_support['summary']
            
            # 供需分析检查
            supply_demand_support = self._check_supply_demand_buy_support(supply_demand_result)
            if supply_demand_support['supported']:
                trigger_reasons.append(supply_demand_support['reason'])
                confidence_scores.append(supply_demand_support['confidence'])
                analysis_summary['supply_demand'] = supply_demand_support['summary']
            
            # 风险评估
            risk_assessment = self._assess_overall_risk(analysis_results)
            
            # 判断是否触发买入信号
            buy_conditions = self.user_config['buy_trigger_conditions']
            required_supports = sum([
                buy_conditions.get('strategic_required', True),
                buy_conditions.get('tactical_required', True),
                buy_conditions.get('execution_required', True),
                buy_conditions.get('supply_demand_required', True)
            ])
            
            actual_supports = len([s for s in [strategic_support, tactical_support, 
                                             execution_support, supply_demand_support] 
                                 if s['supported']])
            
            # 计算综合置信度
            overall_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
            
            # 判断触发条件
            min_confidence = buy_conditions.get('min_confidence', 0.7)
            max_risk = buy_conditions.get('max_risk_level', 'medium')
            
            if (actual_supports >= required_supports * 0.75 and  # 至少75%的条件满足
                overall_confidence >= min_confidence and
                self._is_risk_acceptable(risk_assessment['risk_level'], max_risk)):
                
                signal_type = SignalType.BUY
                recommended_action = "建议买入：多项分析确认买入时机，风险可控"
                
            else:
                signal_type = SignalType.HOLD
                recommended_action = "建议观望：买入条件不够充分或风险过高"
                
                # 添加未满足条件的说明
                if actual_supports < required_supports * 0.75:
                    trigger_reasons.append(f"支持条件不足：{actual_supports}/{required_supports}")
                if overall_confidence < min_confidence:
                    trigger_reasons.append(f"置信度不足：{overall_confidence:.2f} < {min_confidence}")
                if not self._is_risk_acceptable(risk_assessment['risk_level'], max_risk):
                    trigger_reasons.append(f"风险过高：{risk_assessment['risk_level']} > {max_risk}")
            
            result = TriggerResult(
                signal_type=signal_type,
                confidence=overall_confidence,
                trigger_reasons=trigger_reasons,
                analysis_summary=analysis_summary,
                risk_level=risk_assessment['risk_level'],
                recommended_action=recommended_action
            )
            
            # 记录触发历史
            self.trigger_history.append(result)
            
            self.logger.info(f"买入信号分析完成: {signal_type.value}, 置信度: {overall_confidence:.2f}")
            return result
            
        except Exception as e:
            self.logger.error(f"买入信号分析失败: {e}")
            return TriggerResult(
                signal_type=SignalType.HOLD,
                confidence=0.0,
                trigger_reasons=[f"分析异常: {str(e)}"],
                analysis_summary={},
                risk_level=RiskLevel.HIGH,
                recommended_action="系统异常，建议等待修复后再操作"
            )

    def analyze_sell_signals(self, analysis_results: Dict[str, Any],
                           current_price: float, entry_price: float = None) -> TriggerResult:
        """
        分析卖出信号

        Args:
            analysis_results: 包含四层分析结果的字典
            current_price: 当前价格
            entry_price: 入场价格（如果有持仓）

        Returns:
            TriggerResult: 触发结果
        """
        try:
            self.logger.info("开始分析卖出信号...")

            trigger_reasons = []
            analysis_summary = {}
            confidence_scores = []

            # 提取各层分析结果
            strategic_result = analysis_results.get('strategic', {})
            tactical_result = analysis_results.get('tactical', {})
            execution_result = analysis_results.get('execution', {})
            supply_demand_result = analysis_results.get('supply_demand', {})

            # 检查止盈止损条件
            if entry_price:
                stop_loss_check = self._check_stop_loss_conditions(current_price, entry_price, execution_result)
                take_profit_check = self._check_take_profit_conditions(current_price, entry_price, execution_result)

                if stop_loss_check['triggered']:
                    trigger_reasons.append(stop_loss_check['reason'])
                    confidence_scores.append(0.9)  # 止损信号置信度高
                    analysis_summary['stop_loss'] = stop_loss_check['summary']

                if take_profit_check['triggered']:
                    trigger_reasons.append(take_profit_check['reason'])
                    confidence_scores.append(0.8)  # 止盈信号置信度较高
                    analysis_summary['take_profit'] = take_profit_check['summary']

            # 战略分析检查
            strategic_sell = self._check_strategic_sell_signal(strategic_result)
            if strategic_sell['triggered']:
                trigger_reasons.append(strategic_sell['reason'])
                confidence_scores.append(strategic_sell['confidence'])
                analysis_summary['strategic'] = strategic_sell['summary']

            # 战术分析检查
            tactical_sell = self._check_tactical_sell_signal(tactical_result)
            if tactical_sell['triggered']:
                trigger_reasons.append(tactical_sell['reason'])
                confidence_scores.append(tactical_sell['confidence'])
                analysis_summary['tactical'] = tactical_sell['summary']

            # 供需分析检查
            supply_demand_sell = self._check_supply_demand_sell_signal(supply_demand_result)
            if supply_demand_sell['triggered']:
                trigger_reasons.append(supply_demand_sell['reason'])
                confidence_scores.append(supply_demand_sell['confidence'])
                analysis_summary['supply_demand'] = supply_demand_sell['summary']

            # 风险评估
            risk_assessment = self._assess_overall_risk(analysis_results)
            risk_exit = self._check_risk_exit_conditions(risk_assessment)
            if risk_exit['triggered']:
                trigger_reasons.append(risk_exit['reason'])
                confidence_scores.append(risk_exit['confidence'])
                analysis_summary['risk_exit'] = risk_exit['summary']

            # 计算综合置信度
            overall_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0

            # 判断是否触发卖出信号
            if trigger_reasons:
                signal_type = SignalType.SELL
                recommended_action = f"建议卖出：{'; '.join(trigger_reasons[:2])}"  # 显示前两个主要原因
            else:
                signal_type = SignalType.HOLD
                recommended_action = "建议持有：暂无明确卖出信号"

            result = TriggerResult(
                signal_type=signal_type,
                confidence=overall_confidence,
                trigger_reasons=trigger_reasons,
                analysis_summary=analysis_summary,
                risk_level=risk_assessment['risk_level'],
                recommended_action=recommended_action
            )

            # 记录触发历史
            self.trigger_history.append(result)

            self.logger.info(f"卖出信号分析完成: {signal_type.value}, 置信度: {overall_confidence:.2f}")
            return result

        except Exception as e:
            self.logger.error(f"卖出信号分析失败: {e}")
            return TriggerResult(
                signal_type=SignalType.HOLD,
                confidence=0.0,
                trigger_reasons=[f"分析异常: {str(e)}"],
                analysis_summary={},
                risk_level=RiskLevel.HIGH,
                recommended_action="系统异常，建议等待修复后再操作"
            )

    def analyze_risk_alerts(self, analysis_results: Dict[str, Any]) -> TriggerResult:
        """
        分析风险预警

        Args:
            analysis_results: 包含四层分析结果的字典

        Returns:
            TriggerResult: 风险预警结果
        """
        try:
            self.logger.info("开始分析风险预警...")

            alert_reasons = []
            analysis_summary = {}
            risk_scores = []

            # 检查各层分析的风险指标
            strategic_risk = self._check_strategic_risk(analysis_results.get('strategic', {}))
            tactical_risk = self._check_tactical_risk(analysis_results.get('tactical', {}))
            execution_risk = self._check_execution_risk(analysis_results.get('execution', {}))
            supply_demand_risk = self._check_supply_demand_risk(analysis_results.get('supply_demand', {}))

            # 收集风险信息
            for risk_check, layer_name in [
                (strategic_risk, 'strategic'),
                (tactical_risk, 'tactical'),
                (execution_risk, 'execution'),
                (supply_demand_risk, 'supply_demand')
            ]:
                if risk_check['has_risk']:
                    alert_reasons.append(risk_check['reason'])
                    risk_scores.append(risk_check['risk_score'])
                    analysis_summary[layer_name] = risk_check['summary']

            # 计算综合风险评分
            overall_risk_score = max(risk_scores) if risk_scores else 0

            # 确定预警级别
            alert_conditions = self.user_config['risk_alert_conditions']
            if overall_risk_score >= alert_conditions.get('level_3_threshold', 0.8):
                alert_level = AlertLevel.LEVEL_3
                risk_level = RiskLevel.HIGH
                recommended_action = "三级预警：高风险需立即处理，建议立即减仓或止损"
            elif overall_risk_score >= alert_conditions.get('level_2_threshold', 0.6):
                alert_level = AlertLevel.LEVEL_2
                risk_level = RiskLevel.MEDIUM
                recommended_action = "二级预警：中等风险出现，建议密切关注并准备应对措施"
            elif overall_risk_score >= alert_conditions.get('level_1_threshold', 0.3):
                alert_level = AlertLevel.LEVEL_1
                risk_level = RiskLevel.LOW
                recommended_action = "一级预警：轻微风险增加，建议加强监控"
            else:
                alert_level = None
                risk_level = RiskLevel.LOW
                recommended_action = "风险正常：当前风险水平可控"

            result = TriggerResult(
                signal_type=SignalType.RISK_ALERT,
                confidence=overall_risk_score,
                trigger_reasons=alert_reasons,
                analysis_summary=analysis_summary,
                risk_level=risk_level,
                alert_level=alert_level,
                recommended_action=recommended_action
            )

            # 记录触发历史
            self.trigger_history.append(result)

            self.logger.info(f"风险预警分析完成: {alert_level.value if alert_level else 'normal'}, 风险评分: {overall_risk_score:.2f}")
            return result

        except Exception as e:
            self.logger.error(f"风险预警分析失败: {e}")
            return TriggerResult(
                signal_type=SignalType.RISK_ALERT,
                confidence=1.0,
                trigger_reasons=[f"风险分析异常: {str(e)}"],
                analysis_summary={},
                risk_level=RiskLevel.HIGH,
                alert_level=AlertLevel.LEVEL_3,
                recommended_action="系统异常，建议立即停止操作并联系技术支持"
            )

    def _check_strategic_buy_support(self, strategic_result: Dict) -> Dict:
        """检查战略分析的买入支持"""
        try:
            # 从重构后的战略分析结果中提取关键信息
            trend_judgment = strategic_result.get('long_term_trend_judgment', '')
            investment_value = strategic_result.get('investment_value_assessment', '')
            risk_level = strategic_result.get('risk_level_assessment', '')

            supported = False
            confidence = 0.0
            reason = ""

            # 检查长期趋势
            if any(keyword in trend_judgment.lower() for keyword in ['看涨', '上涨', '强势', '积极']):
                supported = True
                confidence += 0.4
                reason += "长期趋势看涨; "

            # 检查投资价值
            if any(keyword in investment_value.lower() for keyword in ['较高', '优秀', '良好', '值得投资']):
                supported = True
                confidence += 0.4
                reason += "投资价值较高; "

            # 检查风险等级
            if any(keyword in risk_level.lower() for keyword in ['低', '可控', '适中']):
                confidence += 0.2
                reason += "风险可控; "

            return {
                'supported': supported,
                'confidence': min(confidence, 1.0),
                'reason': f"战略分析: {reason.rstrip('; ')}",
                'summary': f"趋势: {trend_judgment[:50]}..., 价值: {investment_value[:30]}..."
            }

        except Exception as e:
            return {
                'supported': False,
                'confidence': 0.0,
                'reason': f"战略分析检查异常: {str(e)}",
                'summary': "战略分析数据异常"
            }

    def _check_tactical_buy_support(self, tactical_result: Dict) -> Dict:
        """检查战术分析的买入支持"""
        try:
            # 从重构后的战术分析结果中提取关键信息
            trend_status = tactical_result.get('medium_term_trend_status', '')
            technical_status = tactical_result.get('technical_indicator_status', '')
            timing_judgment = tactical_result.get('buy_sell_timing_judgment', '')

            supported = False
            confidence = 0.0
            reason = ""

            # 检查中期趋势
            if any(keyword in trend_status.lower() for keyword in ['上涨', '强势', '看涨', '积极']):
                supported = True
                confidence += 0.4
                reason += "中期趋势积极; "

            # 检查技术指标
            if any(keyword in technical_status.lower() for keyword in ['看涨', '积极', '强势', '买入']):
                supported = True
                confidence += 0.3
                reason += "技术指标看涨; "

            # 检查买卖时机
            if any(keyword in timing_judgment.lower() for keyword in ['买入', '适合入场', '时机良好']):
                supported = True
                confidence += 0.3
                reason += "买入时机良好; "

            return {
                'supported': supported,
                'confidence': min(confidence, 1.0),
                'reason': f"战术分析: {reason.rstrip('; ')}",
                'summary': f"趋势: {trend_status[:30]}..., 时机: {timing_judgment[:30]}..."
            }

        except Exception as e:
            return {
                'supported': False,
                'confidence': 0.0,
                'reason': f"战术分析检查异常: {str(e)}",
                'summary': "战术分析数据异常"
            }

    def _check_execution_buy_support(self, execution_result: Dict) -> Dict:
        """检查执行分析的买入支持"""
        try:
            # 从重构后的执行分析结果中提取关键信息
            market_status = execution_result.get('immediate_market_status', '')
            timing_assessment = execution_result.get('execution_timing_assessment', '')
            risk_level = execution_result.get('short_term_risk_level', '')

            supported = False
            confidence = 0.0
            reason = ""

            # 检查即时市场状态
            if any(keyword in market_status.lower() for keyword in ['强势', '上涨', '积极', '良好']):
                supported = True
                confidence += 0.4
                reason += "市场状态良好; "

            # 检查执行时机
            if any(keyword in timing_assessment.lower() for keyword in ['买入时机', '适合入场', '时机优秀', '建议买入']):
                supported = True
                confidence += 0.4
                reason += "执行时机适合; "

            # 检查短期风险
            if any(keyword in risk_level.lower() for keyword in ['低', '可控', '较低']):
                confidence += 0.2
                reason += "短期风险可控; "

            return {
                'supported': supported,
                'confidence': min(confidence, 1.0),
                'reason': f"执行分析: {reason.rstrip('; ')}",
                'summary': f"市场: {market_status[:30]}..., 时机: {timing_assessment[:30]}..."
            }

        except Exception as e:
            return {
                'supported': False,
                'confidence': 0.0,
                'reason': f"执行分析检查异常: {str(e)}",
                'summary': "执行分析数据异常"
            }

    def _check_supply_demand_buy_support(self, supply_demand_result: Dict) -> Dict:
        """检查供需分析的买入支持"""
        try:
            # 从重构后的供需分析结果中提取关键信息
            relationship_status = supply_demand_result.get('supply_demand_relationship_status', '')
            trend_direction = supply_demand_result.get('supply_demand_trend_direction', '')
            support_factors = supply_demand_result.get('price_support_factors_status', '')

            supported = False
            confidence = 0.0
            reason = ""

            # 检查供需关系状态
            if any(keyword in relationship_status.lower() for keyword in ['需求旺盛', '供需平衡', '需求大于供应']):
                supported = True
                confidence += 0.4
                reason += "供需关系支撑; "

            # 检查供需趋势
            if any(keyword in trend_direction.lower() for keyword in ['需求增加', '供减需增', '积极']):
                supported = True
                confidence += 0.3
                reason += "供需趋势积极; "

            # 检查价格支撑因素
            if any(keyword in support_factors.lower() for keyword in ['支撑较强', '积极因素', '价格支撑']):
                confidence += 0.3
                reason += "价格支撑充分; "

            return {
                'supported': supported,
                'confidence': min(confidence, 1.0),
                'reason': f"供需分析: {reason.rstrip('; ')}",
                'summary': f"关系: {relationship_status[:30]}..., 趋势: {trend_direction[:30]}..."
            }

        except Exception as e:
            return {
                'supported': False,
                'confidence': 0.0,
                'reason': f"供需分析检查异常: {str(e)}",
                'summary': "供需分析数据异常"
            }

    def _assess_overall_risk(self, analysis_results: Dict[str, Any]) -> Dict:
        """评估整体风险"""
        try:
            risk_indicators = []

            # 从各层分析中提取风险信息
            strategic_risk = analysis_results.get('strategic', {}).get('risk_level_assessment', '')
            tactical_risk = analysis_results.get('tactical', {}).get('technical_indicator_status', '')
            execution_risk = analysis_results.get('execution', {}).get('short_term_risk_level', '')
            supply_demand_risk = analysis_results.get('supply_demand', {}).get('supply_demand_anomaly_level', '')

            # 评估各层风险
            for risk_info, weight in [
                (strategic_risk, 0.3),
                (tactical_risk, 0.25),
                (execution_risk, 0.25),
                (supply_demand_risk, 0.2)
            ]:
                if '高' in risk_info or 'high' in risk_info.lower():
                    risk_indicators.append(weight * 1.0)
                elif '中' in risk_info or 'medium' in risk_info.lower():
                    risk_indicators.append(weight * 0.6)
                elif '低' in risk_info or 'low' in risk_info.lower():
                    risk_indicators.append(weight * 0.3)
                else:
                    risk_indicators.append(weight * 0.5)  # 默认中等风险

            # 计算综合风险评分
            overall_risk_score = sum(risk_indicators)

            # 确定风险等级
            if overall_risk_score >= 0.7:
                risk_level = RiskLevel.HIGH
            elif overall_risk_score >= 0.4:
                risk_level = RiskLevel.MEDIUM
            else:
                risk_level = RiskLevel.LOW

            return {
                'risk_level': risk_level,
                'risk_score': overall_risk_score,
                'risk_details': {
                    'strategic': strategic_risk,
                    'tactical': tactical_risk,
                    'execution': execution_risk,
                    'supply_demand': supply_demand_risk
                }
            }

        except Exception as e:
            return {
                'risk_level': RiskLevel.HIGH,
                'risk_score': 1.0,
                'risk_details': {'error': str(e)}
            }

    def _is_risk_acceptable(self, current_risk: RiskLevel, max_acceptable: str) -> bool:
        """检查风险是否可接受"""
        risk_levels = {'low': 1, 'medium': 2, 'high': 3}
        current_level = risk_levels.get(current_risk.value, 3)
        max_level = risk_levels.get(max_acceptable.lower(), 2)
        return current_level <= max_level

    def _check_stop_loss_conditions(self, current_price: float, entry_price: float,
                                   execution_result: Dict) -> Dict:
        """检查止损条件"""
        try:
            # 从执行分析中获取止损位
            stop_loss_price = execution_result.get('stop_loss_price', 0)

            if stop_loss_price > 0 and current_price <= stop_loss_price:
                return {
                    'triggered': True,
                    'reason': f"触及止损位: 当前价格{current_price} <= 止损价{stop_loss_price}",
                    'summary': f"止损保护: 价格跌破{stop_loss_price}",
                    'confidence': 0.9
                }

            # 简单的百分比止损
            loss_percentage = (entry_price - current_price) / entry_price
            if loss_percentage > 0.1:  # 10%止损
                return {
                    'triggered': True,
                    'reason': f"亏损超过10%: 当前亏损{loss_percentage*100:.1f}%",
                    'summary': f"百分比止损: 亏损{loss_percentage*100:.1f}%",
                    'confidence': 0.8
                }

            return {'triggered': False, 'reason': '', 'summary': '', 'confidence': 0}

        except Exception:
            return {'triggered': False, 'reason': '', 'summary': '', 'confidence': 0}

    def _check_take_profit_conditions(self, current_price: float, entry_price: float,
                                     execution_result: Dict) -> Dict:
        """检查止盈条件"""
        try:
            # 从执行分析中获取止盈位
            take_profit_price = execution_result.get('take_profit_price', 0)

            if take_profit_price > 0 and current_price >= take_profit_price:
                return {
                    'triggered': True,
                    'reason': f"达到止盈位: 当前价格{current_price} >= 止盈价{take_profit_price}",
                    'summary': f"止盈目标: 价格突破{take_profit_price}",
                    'confidence': 0.8
                }

            # 简单的百分比止盈
            profit_percentage = (current_price - entry_price) / entry_price
            if profit_percentage > 0.2:  # 20%止盈
                return {
                    'triggered': True,
                    'reason': f"盈利超过20%: 当前盈利{profit_percentage*100:.1f}%",
                    'summary': f"百分比止盈: 盈利{profit_percentage*100:.1f}%",
                    'confidence': 0.7
                }

            return {'triggered': False, 'reason': '', 'summary': '', 'confidence': 0}

        except Exception:
            return {'triggered': False, 'reason': '', 'summary': '', 'confidence': 0}

    def _check_strategic_sell_signal(self, strategic_result: Dict) -> Dict:
        """检查战略分析的卖出信号"""
        try:
            trend_judgment = strategic_result.get('long_term_trend_judgment', '')
            investment_value = strategic_result.get('investment_value_assessment', '')

            triggered = False
            confidence = 0.0
            reason = ""

            # 检查长期趋势转弱
            if any(keyword in trend_judgment.lower() for keyword in ['看跌', '下跌', '转弱', '疲软']):
                triggered = True
                confidence += 0.5
                reason += "长期趋势转弱; "

            # 检查投资价值下降
            if any(keyword in investment_value.lower() for keyword in ['较低', '下降', '不佳', '避免投资']):
                triggered = True
                confidence += 0.4
                reason += "投资价值下降; "

            return {
                'triggered': triggered,
                'confidence': min(confidence, 1.0),
                'reason': f"战略分析: {reason.rstrip('; ')}",
                'summary': f"趋势转弱或价值下降"
            }

        except Exception:
            return {'triggered': False, 'confidence': 0, 'reason': '', 'summary': ''}

    def _check_tactical_sell_signal(self, tactical_result: Dict) -> Dict:
        """检查战术分析的卖出信号"""
        try:
            trend_status = tactical_result.get('medium_term_trend_status', '')
            timing_judgment = tactical_result.get('buy_sell_timing_judgment', '')

            triggered = False
            confidence = 0.0
            reason = ""

            # 检查中期趋势转弱
            if any(keyword in trend_status.lower() for keyword in ['下跌', '转弱', '看跌', '疲软']):
                triggered = True
                confidence += 0.5
                reason += "中期趋势转弱; "

            # 检查卖出时机
            if any(keyword in timing_judgment.lower() for keyword in ['卖出', '适合离场', '建议减仓']):
                triggered = True
                confidence += 0.4
                reason += "出现卖出信号; "

            return {
                'triggered': triggered,
                'confidence': min(confidence, 1.0),
                'reason': f"战术分析: {reason.rstrip('; ')}",
                'summary': f"趋势转弱或出现卖出信号"
            }

        except Exception:
            return {'triggered': False, 'confidence': 0, 'reason': '', 'summary': ''}

    def _check_supply_demand_sell_signal(self, supply_demand_result: Dict) -> Dict:
        """检查供需分析的卖出信号"""
        try:
            relationship_status = supply_demand_result.get('supply_demand_relationship_status', '')
            trend_direction = supply_demand_result.get('supply_demand_trend_direction', '')

            triggered = False
            confidence = 0.0
            reason = ""

            # 检查供需关系恶化
            if any(keyword in relationship_status.lower() for keyword in ['供应过剩', '需求不足', '供大于求']):
                triggered = True
                confidence += 0.5
                reason += "供需关系恶化; "

            # 检查供需趋势转差
            if any(keyword in trend_direction.lower() for keyword in ['供增需减', '需求减少', '供应增加']):
                triggered = True
                confidence += 0.4
                reason += "供需趋势转差; "

            return {
                'triggered': triggered,
                'confidence': min(confidence, 1.0),
                'reason': f"供需分析: {reason.rstrip('; ')}",
                'summary': f"供需关系恶化或趋势转差"
            }

        except Exception:
            return {'triggered': False, 'confidence': 0, 'reason': '', 'summary': ''}

    def _check_risk_exit_conditions(self, risk_assessment: Dict) -> Dict:
        """检查风险退出条件"""
        try:
            risk_level = risk_assessment.get('risk_level', RiskLevel.LOW)
            risk_score = risk_assessment.get('risk_score', 0)

            # 检查是否需要风险退出
            sell_conditions = self.user_config['sell_trigger_conditions']
            max_acceptable_risk = sell_conditions.get('max_acceptable_risk', 'high')

            if not self._is_risk_acceptable(risk_level, max_acceptable_risk):
                return {
                    'triggered': True,
                    'confidence': risk_score,
                    'reason': f"风险等级超出承受范围: {risk_level.value} > {max_acceptable_risk}",
                    'summary': f"风险过高需要退出"
                }

            return {'triggered': False, 'confidence': 0, 'reason': '', 'summary': ''}

        except Exception:
            return {'triggered': False, 'confidence': 0, 'reason': '', 'summary': ''}

    def _check_strategic_risk(self, strategic_result: Dict) -> Dict:
        """检查战略层面风险"""
        try:
            risk_level = strategic_result.get('risk_level_assessment', '')
            trend_judgment = strategic_result.get('long_term_trend_judgment', '')

            has_risk = False
            risk_score = 0.0
            reason = ""

            if any(keyword in risk_level.lower() for keyword in ['高', 'high']):
                has_risk = True
                risk_score = 0.8
                reason += "战略风险等级高; "
            elif any(keyword in trend_judgment.lower() for keyword in ['不确定', '混乱', '风险']):
                has_risk = True
                risk_score = 0.6
                reason += "长期趋势不确定; "

            return {
                'has_risk': has_risk,
                'risk_score': risk_score,
                'reason': reason.rstrip('; '),
                'summary': f"战略风险: {risk_level}"
            }

        except Exception:
            return {'has_risk': False, 'risk_score': 0, 'reason': '', 'summary': ''}

    def _check_tactical_risk(self, tactical_result: Dict) -> Dict:
        """检查战术层面风险"""
        try:
            technical_status = tactical_result.get('technical_indicator_status', '')
            trend_status = tactical_result.get('medium_term_trend_status', '')

            has_risk = False
            risk_score = 0.0
            reason = ""

            if any(keyword in technical_status.lower() for keyword in ['混乱', '分歧', '不明确']):
                has_risk = True
                risk_score = 0.6
                reason += "技术指标混乱; "

            if any(keyword in trend_status.lower() for keyword in ['震荡', '不稳定', '反复']):
                has_risk = True
                risk_score = max(risk_score, 0.5)
                reason += "中期趋势不稳定; "

            return {
                'has_risk': has_risk,
                'risk_score': risk_score,
                'reason': reason.rstrip('; '),
                'summary': f"战术风险: 技术面不稳定"
            }

        except Exception:
            return {'has_risk': False, 'risk_score': 0, 'reason': '', 'summary': ''}

    def _check_execution_risk(self, execution_result: Dict) -> Dict:
        """检查执行层面风险"""
        try:
            risk_level = execution_result.get('short_term_risk_level', '')
            alert_level = execution_result.get('urgent_alert_level', '')

            has_risk = False
            risk_score = 0.0
            reason = ""

            if any(keyword in risk_level.lower() for keyword in ['高', 'high']):
                has_risk = True
                risk_score = 0.8
                reason += "短期风险高; "

            if any(keyword in alert_level.lower() for keyword in ['严重', '重要', '异常']):
                has_risk = True
                risk_score = max(risk_score, 0.7)
                reason += "出现紧急预警; "

            return {
                'has_risk': has_risk,
                'risk_score': risk_score,
                'reason': reason.rstrip('; '),
                'summary': f"执行风险: {risk_level}"
            }

        except Exception:
            return {'has_risk': False, 'risk_score': 0, 'reason': '', 'summary': ''}

    def _check_supply_demand_risk(self, supply_demand_result: Dict) -> Dict:
        """检查供需层面风险"""
        try:
            anomaly_level = supply_demand_result.get('supply_demand_anomaly_level', '')
            alert_count = supply_demand_result.get('alert_count', 0)

            has_risk = False
            risk_score = 0.0
            reason = ""

            if any(keyword in anomaly_level.lower() for keyword in ['严重', '重要', '异常']):
                has_risk = True
                risk_score = 0.7
                reason += "供需关系异常; "

            if alert_count > 2:
                has_risk = True
                risk_score = max(risk_score, 0.6)
                reason += f"供需异常数量多({alert_count}个); "

            return {
                'has_risk': has_risk,
                'risk_score': risk_score,
                'reason': reason.rstrip('; '),
                'summary': f"供需风险: {anomaly_level}"
            }

        except Exception:
            return {'has_risk': False, 'risk_score': 0, 'reason': '', 'summary': ''}

    def get_trigger_history(self, limit: int = 10) -> List[TriggerResult]:
        """获取触发历史记录"""
        return self.trigger_history[-limit:] if self.trigger_history else []

    def clear_trigger_history(self):
        """清空触发历史记录"""
        self.trigger_history.clear()
        self.logger.info("触发历史记录已清空")

    def update_user_config(self, new_config: Dict):
        """更新用户配置"""
        self.user_config.update(new_config)
        self.logger.info("用户配置已更新")
