#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品策略回测验证系统
基于现有数据的专业回测框架
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class CS2BacktestEngine:
    """CS2饰品策略回测引擎"""
    
    def __init__(self, skin_name: str, initial_capital: float = 10000.0):
        self.skin_name = skin_name
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.position = 0  # 持仓数量
        self.trades = []  # 交易记录
        self.equity_curve = []  # 资金曲线
        self.daily_returns = []  # 日收益率
        
    def load_backtest_data(self, data_path: str) -> Dict:
        """加载回测数据（修复：统一使用时K数据）"""
        try:
            # 修复：使用时K数据作为主要回测数据，与分析系统保持一致
            with open(f'{data_path}/时k.json', 'r', encoding='utf-8') as f:
                hourly_raw = json.load(f)

            # 转换为DataFrame
            hourly_df = pd.DataFrame(hourly_raw, columns=[
                'timestamp', 'open', 'low', 'high', 'close', 'volume', 'amount'
            ])
            hourly_df['datetime'] = pd.to_datetime(hourly_df['timestamp'], unit='s')
            hourly_df = hourly_df.sort_values('datetime').reset_index(drop=True)

            # 为了保持接口一致性，将时K数据赋值给daily_df
            daily_df = hourly_df
            
            # 加载走势数据用于基本面信息
            with open(f'{data_path}/走势.json', 'r', encoding='utf-8') as f:
                trend_raw = json.load(f)
            
            trend_df = pd.DataFrame(trend_raw, columns=[
                'timestamp', 'price', 'supply', 'bid_price', 'bid_quantity', 
                'hourly_amount', 'hourly_count', 'total_supply'
            ])
            trend_df['datetime'] = pd.to_datetime(trend_df['timestamp'], unit='s')
            
            return {
                'daily_data': daily_df,
                'trend_data': trend_df,
                'data_range': {
                    'start': daily_df['datetime'].min(),
                    'end': daily_df['datetime'].max(),
                    'days': (daily_df['datetime'].max() - daily_df['datetime'].min()).days
                }
            }
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return None
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        df = df.copy()
        
        # 基础价格数据
        df['close'] = df['close'].astype(float)
        df['high'] = df['high'].astype(float)
        df['low'] = df['low'].astype(float)
        df['volume'] = df['volume'].astype(float)
        
        # EMA指标
        df['ema_12'] = df['close'].ewm(span=12).mean()
        df['ema_26'] = df['close'].ewm(span=26).mean()
        
        # MACD
        df['macd'] = df['ema_12'] - df['ema_26']
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 布林线
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower']) * 100
        
        # ATR
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        df['atr'] = true_range.rolling(window=14).mean()
        
        return df
    
    def generate_signals(self, df: pd.DataFrame, strategy_type: str = 'trend_following') -> pd.DataFrame:
        """生成交易信号"""
        df = df.copy()
        
        if strategy_type == 'trend_following':
            # 趋势跟踪策略
            df['buy_signal'] = (
                (df['ema_12'] > df['ema_26']) &  # EMA金叉
                (df['rsi'] < 70) &  # RSI不超买
                (df['macd'] > df['macd_signal']) &  # MACD金叉
                (df['close'] > df['bb_middle'])  # 价格在布林线中轨上方
            )
            
            df['sell_signal'] = (
                (df['ema_12'] < df['ema_26']) |  # EMA死叉
                (df['rsi'] > 80) |  # RSI严重超买
                (df['macd'] < df['macd_signal'])  # MACD死叉
            )
            
        elif strategy_type == 'mean_reversion':
            # 均值回归策略
            df['buy_signal'] = (
                (df['rsi'] < 30) &  # RSI超卖
                (df['bb_position'] < 20) &  # 价格接近布林线下轨
                (df['close'] < df['bb_middle'])  # 价格在中轨下方
            )
            
            df['sell_signal'] = (
                (df['rsi'] > 70) |  # RSI超买
                (df['bb_position'] > 80) |  # 价格接近布林线上轨
                (df['close'] > df['bb_upper'])  # 价格突破上轨
            )
        
        return df
    
    def execute_backtest(self, data: Dict, strategy_type: str = 'trend_following', 
                        position_size: float = 0.1) -> Dict:
        """执行回测"""
        daily_df = data['daily_data'].copy()
        
        # 计算技术指标
        daily_df = self.calculate_technical_indicators(daily_df)
        
        # 生成交易信号
        daily_df = self.generate_signals(daily_df, strategy_type)
        
        # 初始化
        self.current_capital = self.initial_capital
        self.position = 0
        self.trades = []
        self.equity_curve = []
        
        # 逐日回测
        for i in range(len(daily_df)):
            row = daily_df.iloc[i]
            current_date = row['datetime']
            current_price = row['close']
            
            # 检查卖出信号
            if self.position > 0 and row['sell_signal']:
                # 执行卖出
                sell_amount = self.position * current_price
                self.current_capital += sell_amount
                
                # 记录交易
                self.trades.append({
                    'date': current_date,
                    'type': 'SELL',
                    'price': current_price,
                    'quantity': self.position,
                    'amount': sell_amount,
                    'capital_after': self.current_capital
                })
                
                self.position = 0
            
            # 检查买入信号
            elif self.position == 0 and row['buy_signal']:
                # 计算买入数量
                invest_amount = self.current_capital * position_size
                buy_quantity = invest_amount / current_price
                
                if invest_amount > 0:
                    # 执行买入
                    self.current_capital -= invest_amount
                    self.position = buy_quantity
                    
                    # 记录交易
                    self.trades.append({
                        'date': current_date,
                        'type': 'BUY',
                        'price': current_price,
                        'quantity': buy_quantity,
                        'amount': invest_amount,
                        'capital_after': self.current_capital
                    })
            
            # 计算当前总资产
            current_portfolio_value = self.current_capital + (self.position * current_price)
            
            # 记录资金曲线
            self.equity_curve.append({
                'date': current_date,
                'cash': self.current_capital,
                'position_value': self.position * current_price,
                'total_value': current_portfolio_value,
                'return_pct': (current_portfolio_value - self.initial_capital) / self.initial_capital * 100
            })
        
        # 计算最终结果
        final_value = self.equity_curve[-1]['total_value'] if self.equity_curve else self.initial_capital
        total_return = (final_value - self.initial_capital) / self.initial_capital * 100
        
        return {
            'daily_data': daily_df,
            'trades': self.trades,
            'equity_curve': self.equity_curve,
            'final_value': final_value,
            'total_return': total_return,
            'backtest_period': data['data_range']
        }
    
    def calculate_performance_metrics(self, backtest_result: Dict) -> Dict:
        """计算性能指标"""
        equity_curve = pd.DataFrame(backtest_result['equity_curve'])
        trades_df = pd.DataFrame(backtest_result['trades'])
        
        if len(equity_curve) == 0:
            return {'error': '无有效数据'}
        
        # 基础收益指标
        total_return = backtest_result['total_return']
        days = len(equity_curve)
        annualized_return = (1 + total_return/100) ** (365/days) - 1 if days > 0 else 0
        
        # 计算日收益率
        equity_curve['daily_return'] = equity_curve['total_value'].pct_change()
        daily_returns = equity_curve['daily_return'].dropna()
        
        # 风险指标
        volatility = daily_returns.std() * np.sqrt(365) if len(daily_returns) > 1 else 0
        sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
        
        # 最大回撤
        equity_curve['cummax'] = equity_curve['total_value'].cummax()
        equity_curve['drawdown'] = (equity_curve['total_value'] - equity_curve['cummax']) / equity_curve['cummax']
        max_drawdown = equity_curve['drawdown'].min() * 100
        
        # 交易统计
        if len(trades_df) > 0:
            buy_trades = trades_df[trades_df['type'] == 'BUY']
            sell_trades = trades_df[trades_df['type'] == 'SELL']
            
            total_trades = len(sell_trades)  # 以卖出次数计算完整交易
            
            if total_trades > 0:
                # 计算每笔交易的盈亏
                trade_returns = []
                for i in range(min(len(buy_trades), len(sell_trades))):
                    buy_price = buy_trades.iloc[i]['price']
                    sell_price = sell_trades.iloc[i]['price']
                    trade_return = (sell_price - buy_price) / buy_price * 100
                    trade_returns.append(trade_return)
                
                winning_trades = len([r for r in trade_returns if r > 0])
                win_rate = winning_trades / total_trades * 100
                
                avg_win = np.mean([r for r in trade_returns if r > 0]) if winning_trades > 0 else 0
                avg_loss = np.mean([r for r in trade_returns if r < 0]) if (total_trades - winning_trades) > 0 else 0
                profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
            else:
                total_trades = win_rate = avg_win = avg_loss = profit_factor = 0
        else:
            total_trades = win_rate = avg_win = avg_loss = profit_factor = 0
        
        return {
            'total_return': total_return,
            'annualized_return': annualized_return * 100,
            'volatility': volatility * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'backtest_days': days
        }
    
    def generate_backtest_report(self, backtest_result: Dict, performance_metrics: Dict) -> str:
        """生成回测报告"""
        report = f"""
📊 CS2饰品策略回测报告 - {self.skin_name}
{'='*60}

📈 基础信息:
   回测期间: {backtest_result['backtest_period']['start'].strftime('%Y-%m-%d')} 到 {backtest_result['backtest_period']['end'].strftime('%Y-%m-%d')}
   回测天数: {performance_metrics['backtest_days']}天
   初始资金: {self.initial_capital:,.2f}元
   最终资金: {backtest_result['final_value']:,.2f}元

💰 收益指标:
   总收益率: {performance_metrics['total_return']:+.2f}%
   年化收益率: {performance_metrics['annualized_return']:+.2f}%
   最大回撤: {performance_metrics['max_drawdown']:.2f}%

⚡ 风险指标:
   年化波动率: {performance_metrics['volatility']:.2f}%
   夏普比率: {performance_metrics['sharpe_ratio']:.2f}

📋 交易统计:
   总交易次数: {performance_metrics['total_trades']}次
   胜率: {performance_metrics['win_rate']:.1f}%
   平均盈利: {performance_metrics['avg_win']:+.2f}%
   平均亏损: {performance_metrics['avg_loss']:+.2f}%
   盈亏比: {performance_metrics['profit_factor']:.2f}

🎯 策略评估:
"""
        
        # 策略评估
        if performance_metrics['total_return'] > 10:
            if performance_metrics['sharpe_ratio'] > 1:
                assessment = "优秀 - 收益高且风险控制良好"
            else:
                assessment = "良好 - 收益可观但波动较大"
        elif performance_metrics['total_return'] > 0:
            assessment = "一般 - 微盈利，需要优化"
        else:
            assessment = "不佳 - 策略需要重新设计"
        
        report += f"   综合评估: {assessment}\n"
        
        # 风险提示
        if performance_metrics['max_drawdown'] < -20:
            report += "   ⚠️ 风险提示: 最大回撤过大，需要加强风险控制\n"
        
        if performance_metrics['win_rate'] < 40:
            report += "   ⚠️ 风险提示: 胜率偏低，建议优化入场条件\n"
        
        if performance_metrics['total_trades'] < 10:
            report += "   ⚠️ 数据提示: 交易次数较少，统计意义有限\n"
        
        return report

class CS2BacktestRunner:
    """CS2回测运行器"""
    
    def __init__(self):
        self.results = {}
    
    def run_single_backtest(self, skin_name: str, data_path: str, 
                          strategy_type: str = 'trend_following',
                          initial_capital: float = 10000.0,
                          position_size: float = 0.1) -> Dict:
        """运行单个饰品的回测"""
        
        print(f"🔄 开始回测 {skin_name} ({strategy_type}策略)")
        
        # 初始化回测引擎
        engine = CS2BacktestEngine(skin_name, initial_capital)
        
        # 加载数据
        data = engine.load_backtest_data(data_path)
        if not data:
            return {'error': '数据加载失败'}
        
        # 执行回测
        backtest_result = engine.execute_backtest(data, strategy_type, position_size)
        
        # 计算性能指标
        performance_metrics = engine.calculate_performance_metrics(backtest_result)
        
        # 生成报告
        report = engine.generate_backtest_report(backtest_result, performance_metrics)
        
        result = {
            'skin_name': skin_name,
            'strategy_type': strategy_type,
            'backtest_result': backtest_result,
            'performance_metrics': performance_metrics,
            'report': report
        }
        
        self.results[f"{skin_name}_{strategy_type}"] = result
        
        return result
    
    def run_strategy_comparison(self, skin_name: str, data_path: str) -> Dict:
        """运行策略对比回测"""
        
        print(f"🔄 开始策略对比回测 - {skin_name}")
        
        strategies = ['trend_following', 'mean_reversion']
        comparison_results = {}
        
        for strategy in strategies:
            result = self.run_single_backtest(skin_name, data_path, strategy)
            comparison_results[strategy] = result
        
        # 生成对比报告
        comparison_report = self.generate_comparison_report(comparison_results)
        
        return {
            'skin_name': skin_name,
            'strategies': comparison_results,
            'comparison_report': comparison_report
        }
    
    def generate_comparison_report(self, results: Dict) -> str:
        """生成策略对比报告"""
        
        report = "\n📊 策略对比分析报告\n" + "="*50 + "\n"
        
        for strategy_name, result in results.items():
            if 'error' in result:
                continue
                
            metrics = result['performance_metrics']
            report += f"\n{strategy_name.upper()}策略:\n"
            report += f"   总收益率: {metrics['total_return']:+.2f}%\n"
            report += f"   夏普比率: {metrics['sharpe_ratio']:.2f}\n"
            report += f"   最大回撤: {metrics['max_drawdown']:.2f}%\n"
            report += f"   胜率: {metrics['win_rate']:.1f}%\n"
        
        # 推荐最佳策略
        best_strategy = None
        best_score = -float('inf')
        
        for strategy_name, result in results.items():
            if 'error' in result:
                continue
                
            metrics = result['performance_metrics']
            # 综合评分：收益率 + 夏普比率 - 回撤惩罚
            score = metrics['total_return'] + metrics['sharpe_ratio'] * 10 + metrics['max_drawdown'] * 0.5
            
            if score > best_score:
                best_score = score
                best_strategy = strategy_name
        
        if best_strategy:
            report += f"\n🏆 推荐策略: {best_strategy.upper()}\n"
            report += f"   综合评分: {best_score:.2f}\n"
        
        return report
