#!/usr/bin/env python3
"""
仅实时监控调度器

只包含实时监控功能，不包含常规分析功能
用于API服务启动时的定时任务
"""

import asyncio
import logging
import random
import sys
from datetime import datetime, time, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.favorite_dao import FavoriteDAO
from src.cs2_investment.dao.holding_dao import HoldingDAO
from src.cs2_investment.dao.arbitrage_item_dao import ArbitrageItemDAO
# 导入原始实时分析系统（注释掉但保留）
# from src.cs2_investment.fx.integrated_ssync_system import IntegratedSSyncSystem
# 导入新的统一分析系统
from src.cs2_investment.fx.unified_analysis_system import UnifiedAnalysisSystem

logger = logging.getLogger(__name__)


class RealtimeOnlyScheduler:
    """仅实时监控调度器"""
    
    def __init__(self):
        """初始化调度器"""
        self.is_running = False
        self.realtime_monitor_task = None
        
        # 任务配置
        self.realtime_interval_hours = 1    # 实时监控每1小时执行一次
        self.item_delay_seconds = (30, 90)  # 饰品之间间隔15-30秒
        self.max_retry_count = 3            # 最大重试次数
        
        # DAO实例
        self.favorite_dao = FavoriteDAO()
        self.holding_dao = HoldingDAO()
        self.arbitrage_dao = ArbitrageItemDAO()

        # 原始实时监控系统（注释掉但保留）
        # self.realtime_system = IntegratedSSyncSystem()

        # 新的统一分析系统（包含实时分析和投资分析）
        self.unified_system = UnifiedAnalysisSystem()

        logger.info("🚀 仅实时监控调度器初始化完成（已升级为统一分析系统）")
    
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("实时监控调度器已在运行中")
            return

        self.is_running = True
        logger.info("🚀 启动仅实时监控调度器")
        
        # 只启动实时监控任务
        self.realtime_monitor_task = asyncio.create_task(
            self._realtime_monitor_loop()
        )
        
        logger.info("✅ 仅实时监控调度器启动完成")
        
        # 等待任务完成
        try:
            await self.realtime_monitor_task
        except asyncio.CancelledError:
            logger.info("实时监控任务被取消")
    
    def stop(self):
        """停止调度器"""
        if not self.is_running:
            return
        
        logger.info("🛑 停止仅实时监控调度器")
        self.is_running = False
        
        if self.realtime_monitor_task and not self.realtime_monitor_task.done():
            self.realtime_monitor_task.cancel()
        
        logger.info("✅ 仅实时监控调度器已停止")
    
    async def _realtime_monitor_loop(self):
        """实时监控循环"""
        logger.info("🔄 启动实时监控循环")
        
        while self.is_running:
            try:
                # 获取需要监控的饰品（收藏+持仓，去重合并）
                monitor_items = self._get_items_for_monitor()

                if not monitor_items:
                    logger.warning("没有找到需要监控的饰品（收藏或持仓）")
                    await asyncio.sleep(3600)  # 1小时后重试
                    continue

                logger.info(f"📋 开始实时监控，共 {len(monitor_items)} 个饰品")
                logger.info(f"📊 监控来源统计: {self._get_monitor_source_stats(monitor_items)}")

                # 逐个监控饰品（顺序执行，避免IP封禁）
                for i, item in enumerate(monitor_items, 1):
                    if not self.is_running:
                        break

                    await self._run_realtime_monitor(item, i, len(monitor_items))
                    
                    # 饰品之间的随机间隔
                    if i < len(monitor_items):  # 不是最后一个饰品
                        delay = random.randint(*self.item_delay_seconds)
                        logger.info(f"⏳ 等待 {delay} 秒后监控下一个饰品...")
                        await asyncio.sleep(delay)

                # 等待下一轮监控
                if self.is_running:
                    next_run = datetime.now() + timedelta(hours=self.realtime_interval_hours)
                    logger.info(f"⏰ 实时监控完成，下次运行时间: {next_run.strftime('%H:%M:%S')}")
                    await asyncio.sleep(self.realtime_interval_hours * 3600)

            except Exception as e:
                logger.error(f"❌ 实时监控循环异常: {e}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
                
                # 等待一段时间后重试
                await asyncio.sleep(300)  # 5分钟后重试
    
    def _get_favorites_for_monitor(self) -> List[Dict[str, Any]]:
        """获取需要监控的收藏饰品"""
        try:
            # 使用默认用户ID获取收藏饰品
            default_user_id = "default_user"
            favorites = self.favorite_dao.get_user_favorites(default_user_id, limit=1000)

            if not favorites:
                logger.info("没有找到收藏饰品")
                return []

            logger.info(f"找到 {len(favorites)} 个收藏饰品，开始获取market_hash_name...")

            # 需要获取饰品的market_hash_name来构造正确的URL
            from src.cs2_investment.dao.item_dao import ItemDAO
            item_dao = ItemDAO()

            monitor_list = []
            for fav in favorites:
                try:
                    item_id = fav.get('item_id', '')
                    item_name = fav.get('item_name', '')

                    if not item_id:
                        logger.warning(f"收藏饰品缺少item_id: {fav}")
                        continue

                    # 从items表获取market_hash_name
                    item = item_dao.get_by_item_id(item_id)
                    if not item:
                        logger.warning(f"未找到饰品信息: item_id={item_id}, name={item_name}")
                        continue

                    # item是字典格式
                    market_hash_name = item.get('market_hash_name')
                    if not market_hash_name:
                        logger.warning(f"饰品缺少market_hash_name: item_id={item_id}, name={item_name}")
                        continue

                    # 构造正确的SteamDT URL，使用URL编码的market_hash_name
                    import urllib.parse
                    encoded_name = urllib.parse.quote(market_hash_name, safe='')
                    url = f"https://steamdt.com/cs2/{encoded_name}"

                    monitor_list.append({
                        'id': f"favorite_{fav['id']}",
                        'name': item_name or item.get('name', '未知饰品'),
                        'url': url,
                        'item_id': item_id,
                        'market_hash_name': market_hash_name,
                        'source': 'favorite',
                        'priority': 2
                    })

                    logger.debug(f"成功处理收藏饰品: {item_name} -> {url}")

                except Exception as item_error:
                    logger.warning(f"处理收藏饰品失败: {item_error}")
                    continue

            logger.info(f"成功获取 {len(monitor_list)} 个可监控的收藏饰品")
            return monitor_list

        except Exception as e:
            logger.error(f"获取收藏饰品失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return []

    def _get_holdings_for_monitor(self) -> List[Dict[str, Any]]:
        """获取需要实时监控的持仓饰品"""
        try:
            from src.cs2_investment.dao.holding_dao import HoldingDAO
            from src.cs2_investment.dao.item_dao import ItemDAO

            holding_dao = HoldingDAO()
            item_dao = ItemDAO()

            # 获取默认用户的所有持仓
            default_user_id = "default_user"
            holdings = holding_dao.get_user_holdings(default_user_id, limit=1000)

            if not holdings:
                logger.debug("没有找到持仓饰品")
                return []

            logger.info(f"找到 {len(holdings)} 个持仓饰品，开始获取market_hash_name...")

            monitor_list = []
            for holding in holdings:
                try:
                    item_id = holding.get('item_id', '')
                    item_name = holding.get('item_name', '')

                    if not item_id:
                        logger.warning(f"持仓饰品缺少item_id: {holding}")
                        continue

                    # 从items表获取market_hash_name
                    item = item_dao.get_by_item_id(item_id)
                    if not item:
                        logger.warning(f"未找到饰品信息: item_id={item_id}, name={item_name}")
                        continue

                    # item是字典格式
                    market_hash_name = item.get('market_hash_name')
                    if not market_hash_name:
                        logger.warning(f"饰品缺少market_hash_name: item_id={item_id}, name={item_name}")
                        continue

                    # 构造正确的SteamDT URL，使用URL编码的market_hash_name
                    import urllib.parse
                    encoded_name = urllib.parse.quote(market_hash_name, safe='')
                    url = f"https://steamdt.com/cs2/{encoded_name}"

                    monitor_list.append({
                        'id': f"holding_{holding['id']}",
                        'item_id': item_id,
                        'name': item_name or item.get('name', '未知饰品'),
                        'url': url,
                        'market_hash_name': market_hash_name,
                        'source': 'holding',
                        'quantity': holding.get('total_quantity', 0),
                        'total_cost': holding.get('total_cost', 0),
                        'priority': 1
                    })

                    logger.debug(f"成功处理持仓饰品: {item_name} -> {url}")

                except Exception as item_error:
                    logger.warning(f"处理持仓饰品失败: {item_error}")
                    continue

            logger.info(f"成功获取 {len(monitor_list)} 个可监控的持仓饰品")
            return monitor_list

        except Exception as e:
            logger.error(f"获取持仓饰品失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return []

    def _get_arbitrage_items_for_monitor(self) -> List[Dict[str, Any]]:
        """获取需要实时监控的搬砖饰品（已禁用）"""
        # 禁用搬砖表数据处理
        logger.info("📋 搬砖表数据处理已禁用")
        return []

    def _get_items_for_monitor(self) -> List[Dict[str, Any]]:
        """获取需要监控的所有饰品（收藏+持仓+搬砖，去重合并）"""
        try:
            # 获取收藏饰品
            favorites = self._get_favorites_for_monitor()

            # 获取持仓饰品
            holdings = self._get_holdings_for_monitor()

            # 获取搬砖饰品（已禁用）
            # arbitrage_items = self._get_arbitrage_items_for_monitor()
            arbitrage_items = []  # 禁用搬砖表数据处理

            # 合并列表，使用多种标识去重
            seen_items = set()
            monitor_items = []

            # 先添加持仓饰品（优先级最高）
            for holding in holdings:
                item_id = holding.get('item_id')
                hash_name = holding.get('market_hash_name')
                # 使用item_id或hash_name作为去重标识
                unique_key = item_id or hash_name
                if unique_key and unique_key not in seen_items:
                    seen_items.add(unique_key)
                    monitor_items.append(holding)

            # 再添加收藏饰品（去重）
            for favorite in favorites:
                item_id = favorite.get('item_id')
                hash_name = favorite.get('market_hash_name')
                unique_key = item_id or hash_name
                if unique_key and unique_key not in seen_items:
                    seen_items.add(unique_key)
                    favorite['priority'] = 2  # 收藏优先级
                    monitor_items.append(favorite)

            # 搬砖饰品处理已禁用
            # for arbitrage in arbitrage_items:
            #     item_id = arbitrage.get('item_id')
            #     hash_name = arbitrage.get('market_hash_name')
            #     unique_key = item_id or hash_name
            #     if unique_key and unique_key not in seen_items:
            #         seen_items.add(unique_key)
            #         monitor_items.append(arbitrage)

            # 按优先级排序（持仓 > 收藏）
            monitor_items.sort(key=lambda x: x.get('priority', 999))

            logger.info(f"合并监控列表完成: 持仓 {len(holdings)} 个, 收藏 {len(favorites)} 个, 搬砖 {len(arbitrage_items)} 个（已禁用）, 去重后 {len(monitor_items)} 个")

            return monitor_items

        except Exception as e:
            logger.error(f"合并监控列表失败: {e}")
            return []

    def _get_monitor_source_stats(self, monitor_items: List[Dict[str, Any]]) -> str:
        """获取监控来源统计信息"""
        try:
            holding_count = sum(1 for item in monitor_items if item.get('source') == 'holding')
            favorite_count = sum(1 for item in monitor_items if item.get('source') == 'favorite')
            # arbitrage_count = sum(1 for item in monitor_items if item.get('source') == 'arbitrage')

            return f"持仓 {holding_count} 个, 收藏 {favorite_count} 个（搬砖数据已禁用）"
        except Exception:
            return "统计失败"
    
    async def _run_realtime_monitor(self, favorite: Dict[str, Any], current: int, total: int):
        """运行单个饰品的实时监控"""
        start_time = datetime.now()

        try:
            logger.info(f"📈 [{current}/{total}] 开始实时监控: {favorite['name']}")
            logger.info(f"📍 URL: {favorite['url']}")

            # 设置更长的超时时间，因为实时分析比较复杂，并添加重试机制
            max_retries = 2  # 最多重试2次
            retry_count = 0

            while retry_count <= max_retries:
                try:

                    # 新方式：使用统一分析系统，一次获得实时分析和投资分析结果
                    result = await asyncio.wait_for(
                        self.unified_system.run_complete_analysis(favorite['url'], favorite['name']),
                        timeout=900  # 15分钟超时（增加时间因为要运行两套分析）
                    )

                    end_time = datetime.now()
                    duration = (end_time - start_time).total_seconds()

                    if result['success']:
                        logger.info(f"✅ [{current}/{total}] 实时监控成功: {favorite['name']} (耗时: {duration:.1f}秒)")

                        # 搬砖饰品监控完成，不需要额外处理
                        if favorite.get('source') == 'arbitrage':
                            logger.info(f"🔄 搬砖饰品监控完成: {favorite['name']}")

                        break  # 成功则跳出重试循环
                    else:
                        error_msg = result.get('error', '未知错误')
                        if retry_count < max_retries:
                            logger.warning(f"⚠️ [{current}/{total}] 实时监控失败，准备重试: {favorite['name']} - {error_msg}")
                            retry_count += 1
                            await asyncio.sleep(30)  # 等待30秒后重试
                            continue
                        else:
                            logger.error(f"❌ [{current}/{total}] 实时监控失败（已重试{max_retries}次）: {favorite['name']} - {error_msg}")
                            break

                except asyncio.TimeoutError:
                    if retry_count < max_retries:
                        logger.warning(f"⚠️ [{current}/{total}] 实时监控超时，准备重试: {favorite['name']} - 超过10分钟未完成")
                        retry_count += 1
                        await asyncio.sleep(60)  # 超时后等待1分钟再重试
                        continue
                    else:
                        logger.error(f"❌ [{current}/{total}] 实时监控超时（已重试{max_retries}次）: {favorite['name']} - 超过10分钟未完成")
                        break
                except Exception as analysis_error:
                    if retry_count < max_retries:
                        logger.warning(f"⚠️ [{current}/{total}] 实时监控异常，准备重试: {favorite['name']} - {analysis_error}")
                        retry_count += 1
                        await asyncio.sleep(30)  # 异常后等待30秒再重试
                        continue
                    else:
                        logger.error(f"❌ [{current}/{total}] 实时监控异常（已重试{max_retries}次）: {favorite['name']} - {analysis_error}")
                        break

        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ [{current}/{total}] 实时监控异常: {favorite['name']} - {error_msg}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
    


    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        return {
            'is_running': self.is_running,
            'realtime_interval_hours': self.realtime_interval_hours,
            'item_delay_seconds': self.item_delay_seconds,
            'max_retry_count': self.max_retry_count,
            'task_status': {
                'realtime_monitor': 'running' if (self.realtime_monitor_task and not self.realtime_monitor_task.done()) else 'stopped'
            }
        }


# 全局实例
realtime_only_scheduler = RealtimeOnlyScheduler()


if __name__ == "__main__":
    import asyncio

    async def main():
        """主函数"""
        scheduler = RealtimeOnlyScheduler()
        try:
            logger.info("🚀 启动实时监控调度器...")
            await scheduler.start()
        except KeyboardInterrupt:
            logger.info("⏹️ 收到停止信号，正在关闭调度器...")
            await scheduler.stop()
        except Exception as e:
            logger.error(f"❌ 调度器运行异常: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

    # 运行主函数
    asyncio.run(main())
