"""
抓取器工厂类

实现工厂模式，根据配置参数动态创建相应的抓取器实例。
支持playwright/api切换和自动回退机制，提供单例模式确保资源复用。
"""

import asyncio
from typing import Optional, Dict, Any, Type
from datetime import datetime
from loguru import logger

from .abstract_data_scraper import AbstractDataScraper, ScraperError
from .steamdt_scraper_adapter import SteamDTScraperAdapter
from .api_scraper import APIScraper
from ..config.timer_config import get_timer_config


class ScraperFactory:
    """抓取器工厂类
    
    根据配置参数动态创建抓取器实例，支持playwright/api切换。
    提供智能回退机制和健康检查功能，使用单例模式确保资源复用。
    """
    
    _instance: Optional['ScraperFactory'] = None
    _scrapers: Dict[str, AbstractDataScraper] = {}
    _health_status: Dict[str, Dict[str, Any]] = {}
    _fallback_enabled: bool = True
    _config = None
    
    def __new__(cls) -> 'ScraperFactory':
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化工厂"""
        if hasattr(self, '_initialized') and self._initialized:
            return
        
        self.logger = logger.bind(factory="ScraperFactory")
        self._config = get_timer_config()
        self._fallback_enabled = self._config.scraping.api_fallback_enabled
        
        # 初始化健康状态
        self._health_status = {
            'playwright': {
                'available': True,
                'last_success': None,
                'last_failure': None,
                'failure_count': 0,
                'success_count': 0
            },
            'api': {
                'available': True,
                'last_success': None,
                'last_failure': None,
                'failure_count': 0,
                'success_count': 0
            }
        }
        
        self._initialized = True
        self.logger.info("🏭 ScraperFactory初始化完成")
    
    @classmethod
    def get_instance(cls) -> 'ScraperFactory':
        """获取工厂单例实例"""
        return cls()
    
    def create_scraper(self, scraping_method: Optional[str] = None) -> AbstractDataScraper:
        """创建抓取器实例
        
        Args:
            scraping_method: 抓取方式，如果为None则从配置读取
            
        Returns:
            AbstractDataScraper: 抓取器实例
            
        Raises:
            ScraperError: 创建失败时抛出
        """
        try:
            # 确定抓取方式
            method = scraping_method or self._config.scraping.scraping_method
            method = method.lower()
            
            self.logger.info(f"🔧 创建抓取器: {method}")
            
            # 验证抓取方式
            if method not in ['playwright', 'api']:
                raise ScraperError(f"不支持的抓取方式: {method}")
            
            # 检查健康状态
            if not self._is_scraper_healthy(method):
                self.logger.warning(f"⚠️ {method}抓取器健康状态异常")
                
                # 尝试回退
                if self._fallback_enabled and method == 'api':
                    self.logger.info("🔄 API抓取器不健康，回退到playwright")
                    method = 'playwright'
                elif not self._is_scraper_healthy('playwright'):
                    self.logger.error("❌ 所有抓取器都不健康")
                    raise ScraperError("所有抓取器都不可用")
            
            # 创建或复用抓取器实例
            scraper = self._get_or_create_scraper(method)
            
            self.logger.info(f"✅ 成功创建{method}抓取器")
            return scraper
            
        except Exception as e:
            self.logger.error(f"❌ 创建抓取器失败: {e}")
            raise ScraperError(f"Failed to create scraper: {e}")
    
    def _get_or_create_scraper(self, method: str) -> AbstractDataScraper:
        """获取或创建抓取器实例"""
        # 检查是否已存在实例
        if method in self._scrapers:
            scraper = self._scrapers[method]
            if scraper.is_started:
                return scraper
            else:
                # 实例存在但未启动，移除并重新创建
                self.logger.info(f"🔄 {method}抓取器未启动，重新创建")
                del self._scrapers[method]
        
        # 创建新实例
        if method == 'playwright':
            scraper = SteamDTScraperAdapter()
        elif method == 'api':
            # API抓取器使用配置文件中的代理设置
            scraper = APIScraper()  # 不传参数，自动从配置文件读取
        else:
            raise ScraperError(f"Unknown scraper method: {method}")
        
        # 缓存实例
        self._scrapers[method] = scraper
        return scraper
    
    def _is_scraper_healthy(self, method: str) -> bool:
        """检查抓取器健康状态"""
        if method not in self._health_status:
            return False
        
        status = self._health_status[method]
        
        # 如果标记为不可用，返回False
        if not status['available']:
            return False
        
        # 如果失败次数过多，认为不健康
        if status['failure_count'] >= 3:
            return False
        
        return True
    
    def update_scraper_health(self, method: str, success: bool, error_message: Optional[str] = None) -> None:
        """更新抓取器健康状态
        
        Args:
            method: 抓取方式
            success: 是否成功
            error_message: 错误信息（失败时）
        """
        if method not in self._health_status:
            return
        
        status = self._health_status[method]
        current_time = datetime.now()
        
        if success:
            status['last_success'] = current_time
            status['success_count'] += 1
            status['failure_count'] = 0  # 重置失败计数
            status['available'] = True
            self.logger.debug(f"✅ {method}抓取器健康状态更新: 成功")
        else:
            status['last_failure'] = current_time
            status['failure_count'] += 1
            
            # 如果连续失败3次，标记为不可用
            if status['failure_count'] >= 3:
                status['available'] = False
                self.logger.warning(f"⚠️ {method}抓取器连续失败{status['failure_count']}次，标记为不可用")
            
            self.logger.debug(f"❌ {method}抓取器健康状态更新: 失败 - {error_message}")
    
    def get_health_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有抓取器的健康状态"""
        return self._health_status.copy()
    
    def reset_health_status(self, method: Optional[str] = None) -> None:
        """重置健康状态
        
        Args:
            method: 要重置的抓取方式，如果为None则重置所有
        """
        if method:
            if method in self._health_status:
                self._health_status[method].update({
                    'available': True,
                    'failure_count': 0,
                    'last_failure': None
                })
                self.logger.info(f"🔄 重置{method}抓取器健康状态")
        else:
            for status in self._health_status.values():
                status.update({
                    'available': True,
                    'failure_count': 0,
                    'last_failure': None
                })
            self.logger.info("🔄 重置所有抓取器健康状态")
    
    async def cleanup_all_scrapers(self) -> None:
        """清理所有抓取器资源"""
        self.logger.info("🧹 开始清理所有抓取器资源...")
        
        for method, scraper in self._scrapers.items():
            try:
                if scraper.is_started:
                    await scraper.stop()
                    self.logger.info(f"✅ {method}抓取器资源清理完成")
            except Exception as e:
                self.logger.warning(f"⚠️ 清理{method}抓取器时出现警告: {e}")
        
        self._scrapers.clear()
        self.logger.info("🧹 所有抓取器资源清理完成")
    
    def get_factory_info(self) -> Dict[str, Any]:
        """获取工厂信息"""
        return {
            'factory_type': 'ScraperFactory',
            'singleton_instance': True,
            'fallback_enabled': self._fallback_enabled,
            'active_scrapers': list(self._scrapers.keys()),
            'health_status': self._health_status,
            'config_method': self._config.scraping.scraping_method,
            'supported_scrapers': ['playwright', 'api'],
            'initialized': getattr(self, '_initialized', False)
        }

    async def create_scraper_with_fallback(self, preferred_method: Optional[str] = None) -> AbstractDataScraper:
        """创建抓取器并自动处理回退

        这是一个高级方法，会自动处理抓取器创建失败的情况。

        Args:
            preferred_method: 首选抓取方式

        Returns:
            AbstractDataScraper: 可用的抓取器实例

        Raises:
            ScraperError: 所有抓取器都不可用时抛出
        """
        method = preferred_method or self._config.scraping.scraping_method

        try:
            # 尝试创建首选抓取器
            scraper = self.create_scraper(method)
            await scraper.start()

            # 更新健康状态
            self.update_scraper_health(method, True)
            return scraper

        except Exception as e:
            self.logger.warning(f"⚠️ {method}抓取器创建失败: {e}")
            self.update_scraper_health(method, False, str(e))

            # 尝试回退
            if self._fallback_enabled and method == 'api':
                try:
                    self.logger.info("🔄 尝试回退到playwright抓取器")
                    fallback_scraper = self.create_scraper('playwright')
                    await fallback_scraper.start()

                    self.update_scraper_health('playwright', True)
                    self.logger.info("✅ 成功回退到playwright抓取器")
                    return fallback_scraper

                except Exception as fallback_error:
                    self.logger.error(f"❌ 回退到playwright也失败: {fallback_error}")
                    self.update_scraper_health('playwright', False, str(fallback_error))

            raise ScraperError(f"所有抓取器都不可用。主要错误: {e}")

    def get_recommended_scraper_method(self) -> str:
        """获取推荐的抓取器方式

        基于健康状态和配置返回最佳的抓取方式。

        Returns:
            str: 推荐的抓取方式
        """
        config_method = self._config.scraping.scraping_method

        # 如果配置的方式健康，直接返回
        if self._is_scraper_healthy(config_method):
            return config_method

        # 如果配置的方式不健康，尝试其他方式
        if config_method == 'api' and self._is_scraper_healthy('playwright'):
            self.logger.info("🔄 API不健康，推荐使用playwright")
            return 'playwright'
        elif config_method == 'playwright' and self._is_scraper_healthy('api'):
            self.logger.info("🔄 Playwright不健康，推荐使用API")
            return 'api'

        # 如果都不健康，返回配置的方式（让调用者处理错误）
        self.logger.warning("⚠️ 所有抓取器都不健康，返回配置方式")
        return config_method

    async def health_check_all_scrapers(self) -> Dict[str, bool]:
        """对所有抓取器进行健康检查

        Returns:
            Dict[str, bool]: 各抓取器的健康状态
        """
        results = {}

        for method in ['playwright', 'api']:
            try:
                self.logger.info(f"🔍 检查{method}抓取器健康状态...")

                # 创建临时抓取器进行测试
                test_scraper = self._get_or_create_scraper(method)

                # 尝试启动和停止
                await test_scraper.start()
                await asyncio.sleep(1)  # 短暂等待
                await test_scraper.stop()

                results[method] = True
                self.update_scraper_health(method, True)
                self.logger.info(f"✅ {method}抓取器健康检查通过")

            except Exception as e:
                results[method] = False
                self.update_scraper_health(method, False, str(e))
                self.logger.warning(f"❌ {method}抓取器健康检查失败: {e}")

        return results

    @classmethod
    def reset_singleton(cls) -> None:
        """重置单例实例（主要用于测试）"""
        if cls._instance:
            # 清理资源
            if hasattr(cls._instance, '_scrapers'):
                cls._instance._scrapers.clear()
            cls._instance = None
            logger.info("🔄 ScraperFactory单例已重置")


# 便利函数
def get_scraper_factory() -> ScraperFactory:
    """获取抓取器工厂实例"""
    return ScraperFactory.get_instance()


async def create_scraper(method: Optional[str] = None) -> AbstractDataScraper:
    """便利函数：创建抓取器实例"""
    factory = get_scraper_factory()
    return await factory.create_scraper_with_fallback(method)


def get_recommended_method() -> str:
    """便利函数：获取推荐的抓取方式"""
    factory = get_scraper_factory()
    return factory.get_recommended_scraper_method()
