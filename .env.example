# 定时器配置环境变量
# 复制此文件为 .env 并根据需要修改配置值

# ===== 调度器全局配置 =====
SCHEDULER_ENABLED=true
SCHEDULER_STARTUP_DELAY=2
SCHEDULER_SHUTDOWN_TIMEOUT=30
SCHEDULER_TIMEZONE=Asia/Shanghai
SCHEDULER_LOG_LEVEL=INFO

# ===== API配置 =====
STEAMDT_API_KEY=your_steamdt_api_key_here
API_TIMEOUT=30
API_MAX_RETRIES=3
API_RETRY_DELAY=1.0

# ===== 饰品信息更新定时器 =====
ITEM_INFO_UPDATE_ENABLED=true
ITEM_INFO_UPDATE_CRON=0 2 * * *
ITEM_INFO_HEALTH_CHECK_INTERVAL=60
ITEM_INFO_MAX_RETRY=3

# ===== 价格更新定时器 =====
PRICE_UPDATE_ENABLED=true
PRICE_UPDATE_HEALTH_CHECK_INTERVAL=5
PRICE_UPDATE_AUTO_RESTART=true
PRICE_UPDATE_BATCH_SIZE=100
PRICE_UPDATE_SINGLE_RPM=60
PRICE_UPDATE_BATCH_RPM=10

# ===== 抓取配置 =====
SCRAPING_METHOD=api
SCRAPING_API_FALLBACK_ENABLED=true
SCRAPING_DATA_VALIDATION_ENABLED=true

# SteamDT API代理配置（国内代理，用于访问SteamDT API）
SCRAPING_PROXY_ENABLED=true
SCRAPING_PROXY_URL=http://127.0.0.1:1080

# 超时配置
SCRAPING_API_TIMEOUT=600
SCRAPING_PLAYWRIGHT_TIMEOUT=60

# 重试配置
SCRAPING_MAX_RETRY_ATTEMPTS=3
SCRAPING_RETRY_DELAY=2.0

# Playwright配置
SCRAPING_PLAYWRIGHT_HEADLESS=false

# ===== Steam监控配置 =====
STEAM_MONITOR_ENABLED=true
STEAM_MONITOR_INTERVAL=30
STEAM_BATCH_SIZE=50
STEAM_MIN_DELAY_SECONDS=5
STEAM_MAX_DELAY_SECONDS=30

# Steam专用代理配置（国外代理，用于访问Steam社区网站）
STEAM_PROXY_ENABLED=true
STEAM_PROXY_URL=http://127.0.0.1:7890

# ===== 代理配置说明 =====
# 注意：系统使用两套不同的代理配置
#
# 1. SCRAPING_PROXY_* : SteamDT API代理（国内代理）
#   - 用于访问SteamDT API服务器
#   - 通常使用国内代理或直连
#
# 2. STEAM_PROXY_* : Steam网站代理（国外代理）
#   - 用于访问Steam社区网站获取价格数据
#   - 必须使用能访问国外网站的代理
#
# 支持的代理格式:
#   - HTTP代理: http://127.0.0.1:1080
#   - HTTPS代理: https://proxy.example.com:8080
#   - SOCKS4代理: socks4://127.0.0.1:1080
#   - SOCKS5代理: socks5://127.0.0.1:1080
