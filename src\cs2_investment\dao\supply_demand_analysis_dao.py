"""
供需关系分析数据访问对象

提供供需关系分析的数据库操作功能，包括供需数据存储、
时间序列查询、异常检测、市场平衡分析等功能。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy import desc, func, and_, or_
from sqlalchemy.exc import SQLAlchemyError
from loguru import logger

from .base_dao import BaseDAO
from ..models.supply_demand_analysis import SupplyDemandAnalysis
from ..config.database import get_db_session


class SupplyDemandAnalysisDAO(BaseDAO[SupplyDemandAnalysis]):
    """供需关系分析DAO"""
    
    def __init__(self):
        super().__init__(SupplyDemandAnalysis)
        self.logger = logger.bind(dao=self.__class__.__name__)
    
    def create_analysis_record(self, analysis_data: Dict[str, Any]) -> Optional[SupplyDemandAnalysis]:
        """创建供需分析记录"""
        try:
            with get_db_session() as session:
                analysis = SupplyDemandAnalysis(**analysis_data)
                session.add(analysis)
                session.flush()
                session.refresh(analysis)
                self.logger.info(f"创建供需分析记录成功: {analysis.id} - {analysis.item_id}")
                
                session.expunge(analysis)
                return analysis
        except SQLAlchemyError as e:
            self.logger.error(f"创建供需分析记录失败: {e}")
            raise
    
    def get_latest_analysis(self, item_id: str, data_source: str = None) -> Optional[SupplyDemandAnalysis]:
        """获取最新的供需分析"""
        try:
            with get_db_session() as session:
                query = session.query(SupplyDemandAnalysis).filter(
                    SupplyDemandAnalysis.item_id == item_id
                )
                
                if data_source:
                    query = query.filter(SupplyDemandAnalysis.data_source == data_source)
                
                analysis = query.order_by(desc(SupplyDemandAnalysis.analysis_timestamp)).first()
                
                if analysis:
                    session.expunge(analysis)
                
                return analysis
        except SQLAlchemyError as e:
            self.logger.error(f"获取最新供需分析失败: {e}")
            raise
    
    def get_analysis_by_data_source(self, item_id: str, data_source: str, days: int = 30) -> List[SupplyDemandAnalysis]:
        """根据数据源获取供需分析历史"""
        try:
            with get_db_session() as session:
                start_date = datetime.now() - timedelta(days=days)
                
                analyses = session.query(SupplyDemandAnalysis).filter(
                    and_(
                        SupplyDemandAnalysis.item_id == item_id,
                        SupplyDemandAnalysis.data_source == data_source,
                        SupplyDemandAnalysis.analysis_timestamp >= start_date
                    )
                ).order_by(desc(SupplyDemandAnalysis.analysis_timestamp)).all()
                
                for analysis in analyses:
                    session.expunge(analysis)
                
                return analyses
        except SQLAlchemyError as e:
            self.logger.error(f"获取数据源供需分析失败: {e}")
            raise
    
    def get_supply_demand_status(self, status: str, limit: int = 100) -> List[SupplyDemandAnalysis]:
        """根据供需状态获取分析结果"""
        try:
            with get_db_session() as session:
                analyses = session.query(SupplyDemandAnalysis).filter(
                    SupplyDemandAnalysis.supply_demand_status == status
                ).order_by(desc(SupplyDemandAnalysis.analysis_timestamp)).limit(limit).all()
                
                for analysis in analyses:
                    session.expunge(analysis)
                
                return analyses
        except SQLAlchemyError as e:
            self.logger.error(f"获取供需状态分析失败: {e}")
            raise
    
    def get_anomaly_records(self, anomaly_types: List[str] = None, days: int = 7) -> List[SupplyDemandAnalysis]:
        """获取异常记录"""
        if anomaly_types is None:
            anomaly_types = ['price_anomaly', 'volume_anomaly', 'supply_anomaly', 'demand_anomaly']
        
        try:
            with get_db_session() as session:
                start_date = datetime.now() - timedelta(days=days)
                
                # 构建异常条件
                anomaly_conditions = []
                for anomaly_type in anomaly_types:
                    if hasattr(SupplyDemandAnalysis, anomaly_type):
                        anomaly_conditions.append(getattr(SupplyDemandAnalysis, anomaly_type) == True)
                
                if not anomaly_conditions:
                    return []
                
                analyses = session.query(SupplyDemandAnalysis).filter(
                    and_(
                        SupplyDemandAnalysis.analysis_timestamp >= start_date,
                        or_(*anomaly_conditions)
                    )
                ).order_by(desc(SupplyDemandAnalysis.analysis_timestamp)).all()
                
                for analysis in analyses:
                    session.expunge(analysis)
                
                return analyses
        except SQLAlchemyError as e:
            self.logger.error(f"获取异常记录失败: {e}")
            raise
    
    def get_liquidity_analysis(self, item_id: str, days: int = 30) -> Dict[str, Any]:
        """获取流动性分析"""
        try:
            with get_db_session() as session:
                start_date = datetime.now() - timedelta(days=days)
                
                # 流动性等级分布
                liquidity_dist = session.query(
                    SupplyDemandAnalysis.liquidity_level,
                    func.count(SupplyDemandAnalysis.id).label('count')
                ).filter(
                    and_(
                        SupplyDemandAnalysis.item_id == item_id,
                        SupplyDemandAnalysis.analysis_timestamp >= start_date
                    )
                ).group_by(SupplyDemandAnalysis.liquidity_level).all()
                
                # 价差统计
                spread_stats = session.query(
                    func.avg(SupplyDemandAnalysis.spread_percentage).label('avg_spread'),
                    func.max(SupplyDemandAnalysis.spread_percentage).label('max_spread'),
                    func.min(SupplyDemandAnalysis.spread_percentage).label('min_spread')
                ).filter(
                    and_(
                        SupplyDemandAnalysis.item_id == item_id,
                        SupplyDemandAnalysis.analysis_timestamp >= start_date,
                        SupplyDemandAnalysis.spread_percentage.isnot(None)
                    )
                ).first()
                
                return {
                    'liquidity_distribution': {level: count for level, count in liquidity_dist},
                    'spread_statistics': {
                        'average': float(spread_stats.avg_spread) if spread_stats.avg_spread else None,
                        'maximum': float(spread_stats.max_spread) if spread_stats.max_spread else None,
                        'minimum': float(spread_stats.min_spread) if spread_stats.min_spread else None
                    },
                    'analysis_period_days': days
                }
        except SQLAlchemyError as e:
            self.logger.error(f"获取流动性分析失败: {e}")
            raise
    
    def get_supply_demand_trends(self, item_id: str, days: int = 30) -> Dict[str, Any]:
        """获取供需趋势分析"""
        try:
            with get_db_session() as session:
                start_date = datetime.now() - timedelta(days=days)
                
                analyses = session.query(SupplyDemandAnalysis).filter(
                    and_(
                        SupplyDemandAnalysis.item_id == item_id,
                        SupplyDemandAnalysis.analysis_timestamp >= start_date
                    )
                ).order_by(SupplyDemandAnalysis.analysis_timestamp).all()
                
                if not analyses:
                    return {'supply_trend': 'UNKNOWN', 'demand_trend': 'UNKNOWN'}
                
                # 供应趋势统计
                supply_trends = {}
                demand_trends = {}
                
                for analysis in analyses:
                    if analysis.supply_trend:
                        supply_trends[analysis.supply_trend] = supply_trends.get(analysis.supply_trend, 0) + 1
                    if analysis.demand_trend:
                        demand_trends[analysis.demand_trend] = demand_trends.get(analysis.demand_trend, 0) + 1
                
                # 获取主导趋势
                dominant_supply_trend = max(supply_trends.items(), key=lambda x: x[1])[0] if supply_trends else 'UNKNOWN'
                dominant_demand_trend = max(demand_trends.items(), key=lambda x: x[1])[0] if demand_trends else 'UNKNOWN'
                
                return {
                    'supply_trend': dominant_supply_trend,
                    'demand_trend': dominant_demand_trend,
                    'supply_trend_distribution': supply_trends,
                    'demand_trend_distribution': demand_trends,
                    'data_points': len(analyses)
                }
        except SQLAlchemyError as e:
            self.logger.error(f"获取供需趋势分析失败: {e}")
            raise
    
    def get_market_balance_scores(self, days: int = 7) -> List[Dict[str, Any]]:
        """获取市场平衡评分排行"""
        try:
            with get_db_session() as session:
                start_date = datetime.now() - timedelta(days=days)
                
                # 获取最新的供需分析记录
                subquery = session.query(
                    SupplyDemandAnalysis.item_id,
                    func.max(SupplyDemandAnalysis.analysis_timestamp).label('latest_timestamp')
                ).filter(
                    SupplyDemandAnalysis.analysis_timestamp >= start_date
                ).group_by(SupplyDemandAnalysis.item_id).subquery()
                
                analyses = session.query(SupplyDemandAnalysis).join(
                    subquery,
                    and_(
                        SupplyDemandAnalysis.item_id == subquery.c.item_id,
                        SupplyDemandAnalysis.analysis_timestamp == subquery.c.latest_timestamp
                    )
                ).all()
                
                # 计算平衡评分
                balance_scores = []
                for analysis in analyses:
                    score = analysis.calculate_market_balance_score()
                    balance_scores.append({
                        'item_id': analysis.item_id,
                        'balance_score': score,
                        'supply_demand_ratio': float(analysis.supply_demand_ratio) if analysis.supply_demand_ratio else None,
                        'supply_demand_status': analysis.supply_demand_status,
                        'analysis_timestamp': analysis.analysis_timestamp
                    })
                
                # 按评分排序
                balance_scores.sort(key=lambda x: x['balance_score'], reverse=True)
                
                return balance_scores
        except SQLAlchemyError as e:
            self.logger.error(f"获取市场平衡评分失败: {e}")
            raise
    
    def batch_create_analyses(self, analyses_data: List[Dict[str, Any]]) -> List[SupplyDemandAnalysis]:
        """批量创建供需分析记录"""
        try:
            with get_db_session() as session:
                analyses = []
                for data in analyses_data:
                    analysis = SupplyDemandAnalysis(**data)
                    analyses.append(analysis)
                
                session.add_all(analyses)
                session.flush()
                
                for analysis in analyses:
                    session.refresh(analysis)
                    session.expunge(analysis)
                
                self.logger.info(f"批量创建供需分析记录成功: {len(analyses)}条")
                return analyses
        except SQLAlchemyError as e:
            self.logger.error(f"批量创建供需分析失败: {e}")
            raise
    
    def delete_old_analyses(self, days: int = 90) -> int:
        """删除过期的供需分析记录"""
        try:
            with get_db_session() as session:
                cutoff_date = datetime.now() - timedelta(days=days)
                
                deleted_count = session.query(SupplyDemandAnalysis).filter(
                    SupplyDemandAnalysis.analysis_timestamp < cutoff_date
                ).delete()
                
                self.logger.info(f"删除过期供需分析记录: {deleted_count}条")
                return deleted_count
        except SQLAlchemyError as e:
            self.logger.error(f"删除过期供需分析失败: {e}")
            raise
