# CS2饰品投资分析系统环境配置

# 数据库配置
DB_HOST=**************
DB_PORT=3306
DB_USER=root
DB_PASSWORD=Lwz@86622
DB_NAME=cs2_market

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/cs2_investment.log

# 数据目录
DATA_DIR=data

# 输出目录
OUTPUT_DIR=output

# 应用配置
DEBUG=False
TIMEZONE=Asia/Shanghai

# ===== 定时器配置 =====
# 调度器全局配置
SCHEDULER_ENABLED=true
SCHEDULER_STARTUP_DELAY=2
SCHEDULER_SHUTDOWN_TIMEOUT=30
SCHEDULER_TIMEZONE=Asia/Shanghai
SCHEDULER_LOG_LEVEL=INFO

# API配置
STEAMDT_API_KEY=dc40820d55ce47528a1d08d1221836e7
API_TIMEOUT=30
API_MAX_RETRIES=3
API_RETRY_DELAY=1.0

# 饰品信息更新定时器（暂时禁用）
# ITEM_INFO_UPDATE_ENABLED=true
ITEM_INFO_UPDATE_ENABLED=false
# ITEM_INFO_UPDATE_CRON=0 2 * * *
ITEM_INFO_HEALTH_CHECK_INTERVAL=60
ITEM_INFO_MAX_RETRY=3

# 价格更新定时器（严格遵守SteamDT API限制）
# 暂时禁用，使用简化价格更新器代替，避免API请求冲突
PRICE_UPDATE_ENABLED=false
PRICE_UPDATE_HEALTH_CHECK_INTERVAL=5
PRICE_UPDATE_AUTO_RESTART=true
PRICE_UPDATE_BATCH_SIZE=100
PRICE_UPDATE_SINGLE_RPM=50
PRICE_UPDATE_BATCH_RPM=1

# ===== 简化价格更新器配置（推荐使用） =====
# 基础配置
SIMPLE_PRICE_UPDATE_ENABLED=true
SIMPLE_PRICE_UPDATE_INTERVAL=1
SIMPLE_PRICE_BATCH_SIZE=100
SIMPLE_PRICE_SINGLE_SIZE=50
SIMPLE_PRICE_SINGLE_INTERVAL=1.5
SIMPLE_PRICE_ITEMS_LIMIT=150
SIMPLE_PRICE_USE_LAST_UPDATE=true

# 🔥 价格为0优化配置
# 是否跳过全零价格饰品（避免重复查询）
SIMPLE_PRICE_SKIP_ZERO_ITEMS=true
# 全零价格饰品重新查询间隔（小时）
SIMPLE_PRICE_ZERO_UPDATE_INTERVAL=120

# 🚀 持续运行模式配置
# 是否启用持续运行模式（推荐：true）
SIMPLE_PRICE_CONTINUOUS_MODE=true
# 完整轮询间隔（分钟）- 所有饰品同步完成后等待时间
SIMPLE_PRICE_CYCLE_INTERVAL=10

# ===== 分析任务配置 =====
# 启用分析任务
ANALYSIS_ENABLED=true
# 分析任务执行时间（每天凌晨4点）
ANALYSIS_CRON=0 4 * * *
# 启用报告生成
ANALYSIS_REPORT_ENABLED=true
# 报告生成时间（每周一早上6点）
ANALYSIS_REPORT_CRON=0 6 * * 1
# 启用搬砖比例计算
ANALYSIS_ARBITRAGE_ENABLED=true
# 搬砖比例计算间隔（小时）
ANALYSIS_ARBITRAGE_INTERVAL=6
