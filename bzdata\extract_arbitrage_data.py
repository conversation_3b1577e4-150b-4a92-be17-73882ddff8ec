#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品搬砖数据提取脚本
从Steam JSON文件中提取Steam和Youpin平台的关键搬砖数据
"""

import json
import csv
import os
from datetime import datetime
from typing import List, Dict, Any

def load_json_file(file_path: str) -> List[Dict[str, Any]]:
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"文件未找到: {file_path}")
        return []
    except json.JSONDecodeError as e:
        print(f"JSON解析错误 {file_path}: {e}")
        return []

def extract_arbitrage_data(item: Dict[str, Any], page_num: int) -> Dict[str, Any]:
    """提取单个饰品的搬砖关键数据"""
    return {
        # 基础信息 - 放到前面
        '页面': page_num,
        '饰品': item.get('marketName', ''),  # 改为使用marketName
        'HashName': item.get('marketHashName', ''),  # 新增hashname列
        '武器类型': item.get('goodsType01Name', ''),
        '品质等级': item.get('goodsLevelName', ''),
        '品质类型': item.get('goodsQualityName', ''),
        'Steam在售量': item.get('steamSellCount', 0),  # 修改名称
        'Steam今日销量': item.get('steam24SellCount', 0),  # 修改名称

        # Steam平台数据 (对应截图中的Steam列)
        'Steam在售价(美元)': item.get('steamPrice', 0),
        'Steam求购价(美元)': item.get('steamBuyPrice', 0),
        'Steam求购数': item.get('steamBuyCount', 0),
        'Steam前5档求购数': item.get('steamBuyCount5', 0),
        'Steam求购价格列表': item.get('steamBuyPriceList', ''),

        # Youpin平台数据 (对应截图中的悠品列)
        '悠品在售价': item.get('youpinPrice', 0),
        '悠品求购价': item.get('youpinPurchasePrice', 0),
        '悠品求购数': item.get('youpinPurchaseCount', 0),
        '悠品在售数': item.get('youpinSellCount', 0),

        # 利润数据 (对应截图中的利润列)
        '利润': item.get('youpinProfit', 0),
        '利润率': item.get('youpinProfitRate', 0),

        # 更新时间
        'Steam更新时间': item.get('steamLastUpdateTimeDesc', ''),
        '悠品更新时间': item.get('youpinLastUpdateTimeDesc', ''),
    }

def process_all_pages() -> List[Dict[str, Any]]:
    """处理所有页面的数据"""
    all_data = []
    
    for page in range(1, 5):  # 1-4页
        steam_file = f"{page}.steam.json"
        
        if not os.path.exists(steam_file):
            print(f"文件不存在: {steam_file}")
            continue
            
        print(f"正在处理第{page}页数据: {steam_file}")
        
        items = load_json_file(steam_file)
        if not items:
            continue
            
        for item in items:
            extracted_data = extract_arbitrage_data(item, page)
            all_data.append(extracted_data)
    
    return all_data

def save_to_csv(data: List[Dict[str, Any]], output_file: str):
    """保存数据到CSV文件"""
    if not data:
        print("没有数据可保存")
        return
    
    # CSV列标题 - 基础信息放到前面
    fieldnames = [
        '页面', '饰品', 'HashName', '武器类型', '品质等级', '品质类型', 'Steam在售量', 'Steam今日销量',
        'Steam在售价(美元)', 'Steam求购价(美元)', 'Steam求购数', 'Steam前5档求购数', 'Steam求购价格列表',
        '悠品在售价', '悠品求购价', '悠品求购数', '悠品在售数',
        '利润', '利润率',
        'Steam更新时间', '悠品更新时间'
    ]
    
    try:
        with open(output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        
        print(f"数据已成功保存到: {output_file}")
        print(f"总共处理了 {len(data)} 条记录")
        
    except Exception as e:
        print(f"保存CSV文件时出错: {e}")

def main():
    """主函数"""
    print("=== CS2饰品搬砖数据提取工具 ===")
    print("提取Steam和Youpin平台的关键搬砖数据...")
    
    # 处理所有页面数据
    all_data = process_all_pages()
    
    if not all_data:
        print("没有找到任何数据")
        return
    
    # 生成输出文件名（包含时间戳）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"cs2_arbitrage_data_{timestamp}.csv"
    
    # 保存到CSV
    save_to_csv(all_data, output_file)
    
    # 显示统计信息
    print("\n=== 数据统计 ===")
    for page in range(1, 5):
        page_count = len([item for item in all_data if item['页面'] == page])
        print(f"第{page}页: {page_count} 条记录")

    # 显示利润率最高的前5个商品
    profitable_items = [item for item in all_data if item['利润率'] and item['利润率'] > 0]
    if profitable_items:
        profitable_items.sort(key=lambda x: x['利润率'], reverse=True)
        print(f"\n=== 利润率最高的前5个商品 ===")
        for i, item in enumerate(profitable_items[:5], 1):
            print(f"{i}. {item['饰品']} - 利润率: {item['利润率']:.2%}")

if __name__ == "__main__":
    main()
