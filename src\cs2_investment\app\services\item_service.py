"""
饰品服务层

提供饰品查询和相关业务逻辑。
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.item_dao import ItemDAO
from src.cs2_investment.models.item import Item


class ItemService:
    """饰品服务类"""
    
    def __init__(self):
        self.item_dao = ItemDAO()
    
    def search_items_with_prices(self,
                                name_query: Optional[str] = None,
                                item_types: Optional[List[str]] = None,
                                qualities: Optional[List[str]] = None,
                                rarities: Optional[List[str]] = None,
                                exteriors: Optional[List[str]] = None,
                                price_min: Optional[float] = None,
                                price_max: Optional[float] = None,
                                sell_count_min: Optional[int] = None,
                                sell_count_max: Optional[int] = None,
                                arbitrage_threshold: Optional[float] = None,
                                arbitrage_card_price: Optional[float] = None,
                                sort_by: str = "updated_desc",
                                limit: int = 1000,
                                offset: int = 0,
                                item_ids: Optional[List[str]] = None,
                                user_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """搜索带价格信息的饰品 - 使用统一的SQL实现"""

        # 优先使用统一的SQL实现
        query_mode = "收藏饰品" if user_id else "全部饰品"
        print(f"🚀 [统一查询] 使用单条SQL查询{query_mode}数据")

        try:
            # 使用优化的DAO实现
            from src.cs2_investment.dao.item_dao_optimized import ItemDaoOptimized
            optimized_dao = ItemDaoOptimized()

            # 构建查询参数
            filter_params = {
                'name_query': name_query,
                'item_types': item_types,
                'qualities': qualities,
                'rarities': rarities,
                'exteriors': exteriors,
                'price_min': price_min,
                'price_max': price_max,
                'sell_count_min': sell_count_min,
                'sell_count_max': sell_count_max,
                'arbitrage_threshold': arbitrage_threshold,
                'item_ids': item_ids,
                'user_id': user_id,  # 支持收藏模式
                'sort_by': sort_by,
                'limit': limit,
                'offset': offset
            }

            # 添加搬砖卡价参数
            if arbitrage_card_price is not None:
                filter_params['arbitrage_card_price'] = arbitrage_card_price

            # 一条SQL查询获取所有数据
            results = optimized_dao.search_items_with_complete_data(**filter_params)

            print(f"✅ [统一查询] 查询完成，返回 {len(results)} 个{query_mode}")
            return results

        except Exception as e:
            print(f"❌ [正确实现失败] 降级到原有方法: {e}")
            return self._search_items_with_prices_fallback(
                name_query=name_query,
                item_types=item_types,
                qualities=qualities,
                rarities=rarities,
                price_min=price_min,
                price_max=price_max,
                sell_count_min=sell_count_min,
                sell_count_max=sell_count_max,
                arbitrage_threshold=arbitrage_threshold,
                arbitrage_card_price=arbitrage_card_price,
                sort_by=sort_by,
                limit=limit,
                offset=offset,
                item_ids=item_ids
            )

    def _search_items_with_prices_fallback(self,
                                          name_query: Optional[str] = None,
                                          item_types: Optional[List[str]] = None,
                                          qualities: Optional[List[str]] = None,
                                          rarities: Optional[List[str]] = None,
                                          price_min: Optional[float] = None,
                                          price_max: Optional[float] = None,
                                          sell_count_min: Optional[int] = None,
                                          sell_count_max: Optional[int] = None,
                                          arbitrage_threshold: Optional[float] = None,
                                          arbitrage_card_price: Optional[float] = None,
                                          sort_by: str = "updated_desc",
                                          limit: int = 1000,
                                          offset: int = 0,
                                          item_ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """降级方法：使用原有的低效查询"""

        print(f"⚠️ [降级] 使用原有的低效查询方式")

        try:
            # 直接在DAO层获取字典格式的数据，避免ORM对象的Session问题
            items_data = self._get_items_as_dict(
                name_query=name_query,
                item_types=item_types,
                qualities=qualities,
                rarities=rarities,
                arbitrage_threshold=arbitrage_threshold,
                sort_by=sort_by if sort_by not in ["price_asc", "price_desc", "arbitrage_desc"] else "updated_desc",
                limit=limit * 2,  # 获取更多数据以便价格筛选
                offset=offset,
                item_ids=item_ids
            )

            if not items_data:
                return []

            # 获取平台价格数据（移除快照表依赖）
            item_ids = [item['item_id'] for item in items_data]
            platform_prices_data = self._get_platform_prices_as_dict(item_ids)

            # 创建平台价格字典以便快速查找
            platform_prices_dict = {}
            for price_data in platform_prices_data:
                item_id = price_data['item_id']
                if item_id not in platform_prices_dict:
                    platform_prices_dict[item_id] = {}
                platform_prices_dict[item_id][price_data['platform_name']] = price_data

            # 合并饰品和价格信息
            results = []
            for item_data in items_data:
                platform_prices = platform_prices_dict.get(item_data['item_id'], {})

                # 构建结果字典
                result = item_data.copy()  # 复制基础数据

                # 添加平台价格信息
                result['platform_prices'] = {
                    'buff': platform_prices.get('BUFF', {}),
                    'youpin': platform_prices.get('YOUPIN', {}),
                    'steam': {},
                    'steam_direct': {}
                }

                # 处理Steam数据（数据库中平台名称为STEAM）
                steam_data = platform_prices.get('STEAM', {})
                if steam_data:
                    data_source = steam_data.get('data_source', 'steamdt')
                    if data_source == 'steam_direct':
                        result['platform_prices']['steam_direct'] = steam_data
                    else:
                        result['platform_prices']['steam'] = steam_data
                
                # 价格筛选 - 检查BUFF和YOUPIN平台的在售价格
                if price_min is not None or price_max is not None:
                    # 获取BUFF和YOUPIN平台的在售价格
                    buff_price = result['platform_prices']['buff'].get('sell_price', 0)
                    youpin_price = result['platform_prices']['youpin'].get('sell_price', 0)

                    # 只要其中一个平台的价格在范围内就通过筛选
                    price_in_range = False

                    # 检查BUFF价格
                    if buff_price > 0:
                        if price_min is None or buff_price >= price_min:
                            if price_max is None or buff_price <= price_max:
                                price_in_range = True

                    # 检查YOUPIN价格
                    if not price_in_range and youpin_price > 0:
                        if price_min is None or youpin_price >= price_min:
                            if price_max is None or youpin_price <= price_max:
                                price_in_range = True

                    # 如果没有任何平台价格在范围内，跳过这个饰品
                    if not price_in_range:
                        continue

                # 在售数量筛选 - 检查steamdt数据源的在售数量总和
                if sell_count_min is not None or sell_count_max is not None:
                    # 计算steamdt数据源的在售数量总和
                    total_sell_count = 0
                    platform_details = []

                    for platform_name, platform_data in result['platform_prices'].items():
                        # 只统计steamdt数据源的在售数量
                        if platform_data.get('data_source') == 'steamdt':
                            sell_count = platform_data.get('sell_count', 0)
                            platform_details.append(f"{platform_name}:{sell_count}")
                            if sell_count > 0:
                                total_sell_count += sell_count

                    # 详细的筛选日志
                    item_name = result.get('name', 'Unknown')[:30]
                    print(f"🔍 [在售量筛选] {item_name}...")
                    print(f"   📊 平台详情: {', '.join(platform_details) if platform_details else '无steamdt数据'}")
                    print(f"   📈 总在售量: {total_sell_count}")
                    print(f"   🎯 筛选条件: min={sell_count_min}, max={sell_count_max}")

                    # 检查在售数量是否在范围内
                    if sell_count_min is not None and total_sell_count < sell_count_min:
                        print(f"   ❌ 被过滤: {total_sell_count} < {sell_count_min}")
                        continue
                    if sell_count_max is not None and total_sell_count > sell_count_max:
                        print(f"   ❌ 被过滤: {total_sell_count} > {sell_count_max}")
                        continue

                    print(f"   ✅ 通过筛选: {total_sell_count} 符合条件")

                # 计算求购利润
                purchase_profit_info = self._calculate_purchase_profit_for_item(result, arbitrage_card_price)
                if purchase_profit_info:
                    result['purchase_profit'] = purchase_profit_info['profit']
                    result['purchase_profit_rate'] = purchase_profit_info['profit_rate']
                else:
                    result['purchase_profit'] = None
                    result['purchase_profit_rate'] = None

                results.append(result)
            
            # 按价格、搬砖率或求购利润排序（如果需要）
            if sort_by == "price_asc":
                results.sort(key=lambda x: x['current_price'])
            elif sort_by == "price_desc":
                results.sort(key=lambda x: x['current_price'], reverse=True)
            elif sort_by == "arbitrage_desc":
                # 按搬砖率降序排序，None值排在最后
                results.sort(key=lambda x: x.get('arbitrage_ratio') or 0, reverse=True)
            elif sort_by == "purchase_profit_desc":
                # 按求购利润降序排序
                results.sort(key=lambda x: self._get_purchase_profit_for_sort(x, arbitrage_card_price), reverse=True)
            elif sort_by == "purchase_profit_rate_desc":
                # 按求购利润率降序排序
                results.sort(key=lambda x: self._get_purchase_profit_rate_for_sort(x, arbitrage_card_price), reverse=True)
            
            # 限制结果数量
            return results[:limit]
            
        except Exception as e:
            print(f"搜索饰品失败: {e}")
            return []
    


    def get_item_with_prices(self, item_id: str) -> Optional[Dict[str, Any]]:
        """获取饰品完整信息（包含搬砖率和平台价格）- 优化版本"""
        try:
            print(f"🔍 [get_item_with_prices] 获取饰品信息: {item_id}")

            # 🚀 优化：直接查询单个饰品，而不是查询所有饰品
            from src.cs2_investment.config.database import get_db_session
            from src.cs2_investment.models.item import Item

            with get_db_session() as session:
                item = session.query(Item).filter(Item.item_id == item_id).first()

                if not item:
                    print(f"❌ 饰品不存在: {item_id}")
                    return None

                # 转换为字典格式
                target_item = {
                    'item_id': item.item_id,
                    'name': item.name,
                    'item_type': item.item_type,
                    'quality': item.quality,
                    'rarity': item.rarity,
                    'exterior': getattr(item, 'exterior', None),
                    'image_url': getattr(item, 'image_url', None),
                    'market_hash_name': getattr(item, 'market_hash_name', None),
                    'arbitrage_ratio': getattr(item, 'arbitrage_ratio', None),
                    'last_price_update': getattr(item, 'last_price_update', None),
                    'def_index_name': getattr(item, 'def_index_name', None),
                    'platform_mappings': getattr(item, 'platform_mappings', None),
                    'created_at': getattr(item, 'created_at', None),
                    'updated_at': getattr(item, 'updated_at', None)
                }

            # 🚀 优化：使用已有的高效方法获取平台价格
            platform_prices = self._get_platform_prices_for_item(item_id)

            # 构建结果
            result = target_item.copy()

            # 添加平台价格信息（转换为标准格式）
            result['platform_prices'] = {
                'buff': platform_prices.get('buff', {}),
                'youpin': platform_prices.get('youpin', {}),
                'steam': platform_prices.get('steam', {}),
                'steam_direct': platform_prices.get('steam_direct', {})
            }

            print(f"✅ [get_item_with_prices] 获取成功: {item.name}")
            return result

        except Exception as e:
            print(f"❌ [get_item_with_prices] 获取饰品完整信息失败: {item_id} - {e}")
            return None
    
    def get_filter_options(self) -> Dict[str, List[str]]:
        """获取筛选选项"""
        try:
            return {
                'item_types': self.item_dao.get_distinct_values('item_type'),
                'qualities': self.item_dao.get_distinct_values('quality'),
                'rarities': self.item_dao.get_distinct_values('rarity'),
                'exteriors': self.item_dao.get_distinct_values('exterior')
            }
        except Exception as e:
            print(f"获取筛选选项失败: {e}")
            return {
                'item_types': [],
                'qualities': [],
                'rarities': [],
                'exteriors': []
            }
    
    def get_item_statistics(self) -> Dict[str, Any]:
        """获取饰品统计信息（移除快照表依赖）"""
        try:
            stats = self.item_dao.get_item_statistics()
            return stats
        except Exception as e:
            print(f"获取统计信息失败: {e}")
            return {}
    
    def count_search_results(self,
                           name_query: Optional[str] = None,
                           item_types: Optional[List[str]] = None,
                           qualities: Optional[List[str]] = None,
                           rarities: Optional[List[str]] = None,
                           price_min: Optional[float] = None,
                           price_max: Optional[float] = None) -> int:
        """统计搜索结果数量"""
        try:
            # 这里简化实现，实际应该在数据库层面进行计数
            results = self.search_items_with_prices(
                name_query=name_query,
                item_types=item_types,
                qualities=qualities,
                rarities=rarities,
                price_min=price_min,
                price_max=price_max,
                limit=10000  # 设置一个较大的限制来获取总数
            )
            return len(results)
        except Exception as e:
            print(f"统计搜索结果失败: {e}")
            return 0

    def count_items(self,
                   name_query: Optional[str] = None,
                   item_types: Optional[List[str]] = None,
                   qualities: Optional[List[str]] = None,
                   rarities: Optional[List[str]] = None,
                   arbitrage_threshold: Optional[float] = None,
                   item_ids: Optional[List[str]] = None,
                   price_min: Optional[float] = None,
                   price_max: Optional[float] = None,
                   sell_count_min: Optional[int] = None,
                   sell_count_max: Optional[int] = None) -> int:
        """统计符合条件的饰品数量（支持item_ids过滤）"""
        try:
            # 如果有价格筛选条件或在售数量筛选条件，使用search_items_with_prices方法来计数
            # 因为平台价格筛选和在售数量筛选需要在应用层处理
            if (price_min is not None or price_max is not None or
                sell_count_min is not None or sell_count_max is not None):
                results = self.search_items_with_prices(
                    name_query=name_query,
                    item_types=item_types,
                    qualities=qualities,
                    rarities=rarities,
                    arbitrage_threshold=arbitrage_threshold,
                    item_ids=item_ids,
                    price_min=price_min,
                    price_max=price_max,
                    sell_count_min=sell_count_min,
                    sell_count_max=sell_count_max,
                    limit=50000  # 设置一个较大的限制来获取总数
                )
                return len(results)

            # 没有价格筛选条件时，使用数据库层面的计数（更高效）
            from src.cs2_investment.config.database import get_db_session
            from src.cs2_investment.models.item import Item

            with get_db_session() as session:
                query = session.query(Item)

                # 名称搜索
                if name_query:
                    query = query.filter(
                        Item.name.like(f"%{name_query}%") |
                        Item.market_hash_name.like(f"%{name_query}%")
                    )

                # 类型过滤
                if item_types:
                    query = query.filter(Item.item_type.in_(item_types))

                # 品质过滤
                if qualities:
                    query = query.filter(Item.quality.in_(qualities))

                # 稀有度过滤
                if rarities:
                    query = query.filter(Item.rarity.in_(rarities))

                # 饰品ID过滤（用于收藏页面等场景）
                if item_ids:
                    query = query.filter(Item.item_id.in_(item_ids))

                # 搬砖率过滤
                if arbitrage_threshold is not None:
                    query = query.filter(
                        Item.arbitrage_ratio.isnot(None),
                        Item.arbitrage_ratio >= arbitrage_threshold
                    )

                return query.count()

        except Exception as e:
            print(f"统计饰品数量失败: {e}")
            return 0

    def _search_items_with_sell_count_filter_optimized(self,
                                                       name_query: Optional[str] = None,
                                                       item_types: Optional[List[str]] = None,
                                                       qualities: Optional[List[str]] = None,
                                                       rarities: Optional[List[str]] = None,
                                                       price_min: Optional[float] = None,
                                                       price_max: Optional[float] = None,
                                                       sell_count_min: Optional[int] = None,
                                                       sell_count_max: Optional[int] = None,
                                                       arbitrage_threshold: Optional[float] = None,
                                                       arbitrage_card_price: Optional[float] = None,
                                                       sort_by: str = "updated_desc",
                                                       limit: int = 1000,
                                                       offset: int = 0,
                                                       item_ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """使用SQL优化的在售量筛选查询"""

        from src.cs2_investment.config.database import get_db_session
        from src.cs2_investment.models.item import Item
        from src.cs2_investment.models.platform_price import PlatformPrice
        from sqlalchemy import func, and_, text

        try:
            with get_db_session() as session:
                # 构建优化的SQL查询
                # 1. 先计算每个饰品的steamdt总在售量
                sell_count_subquery = session.query(
                    PlatformPrice.item_id,
                    func.sum(PlatformPrice.sell_count).label('total_sell_count')
                ).filter(
                    and_(
                        PlatformPrice.data_source == 'steamdt',
                        PlatformPrice.is_active == True,
                        PlatformPrice.sell_count > 0
                    )
                ).group_by(PlatformPrice.item_id).subquery()

                # 2. 主查询：关联饰品表和在售量子查询
                query = session.query(Item).join(
                    sell_count_subquery,
                    Item.item_id == sell_count_subquery.c.item_id
                )

                # 3. 应用在售量筛选条件
                if sell_count_min is not None:
                    query = query.filter(sell_count_subquery.c.total_sell_count >= sell_count_min)
                if sell_count_max is not None:
                    query = query.filter(sell_count_subquery.c.total_sell_count <= sell_count_max)

                # 4. 应用其他筛选条件
                if name_query:
                    query = query.filter(
                        Item.name.like(f"%{name_query}%") |
                        Item.market_hash_name.like(f"%{name_query}%")
                    )

                if item_types:
                    query = query.filter(Item.item_type.in_(item_types))

                if qualities:
                    query = query.filter(Item.quality.in_(qualities))

                if rarities:
                    query = query.filter(Item.rarity.in_(rarities))

                if item_ids:
                    query = query.filter(Item.item_id.in_(item_ids))

                if arbitrage_threshold is not None:
                    query = query.filter(
                        Item.arbitrage_ratio.isnot(None),
                        Item.arbitrage_ratio >= arbitrage_threshold
                    )

                # 5. 排序
                if sort_by == "name_asc":
                    query = query.order_by(Item.name.asc())
                elif sort_by == "name_desc":
                    query = query.order_by(Item.name.desc())
                elif sort_by == "arbitrage_desc":
                    query = query.order_by(Item.arbitrage_ratio.desc())
                else:  # updated_desc
                    query = query.order_by(Item.last_price_update.desc())

                # 6. 分页
                query = query.offset(offset).limit(limit)

                # 打印SQL查询（调试用）
                print(f"🔍 [SQL查询] {query}")

                # 执行查询
                items = query.all()

                print(f"✅ [SQL优化] 查询完成，找到 {len(items)} 个饰品")

                # 7. 转换为字典格式并添加价格信息
                results = []
                for item in items:
                    item_dict = {
                        'item_id': item.item_id,
                        'name': item.name,
                        'item_type': item.item_type,
                        'quality': item.quality,
                        'rarity': item.rarity,
                        'exterior': getattr(item, 'exterior', None),
                        'image_url': getattr(item, 'image_url', None),
                        'market_hash_name': getattr(item, 'market_hash_name', None),
                        'arbitrage_ratio': getattr(item, 'arbitrage_ratio', None),
                        'last_price_update': getattr(item, 'last_price_update', None)
                    }

                    # 获取平台价格信息
                    platform_prices = self._get_platform_prices_for_item(item.item_id)
                    item_dict['platform_prices'] = platform_prices

                    # 应用价格筛选（如果需要）
                    if price_min is not None or price_max is not None:
                        if not self._check_price_filter(platform_prices, price_min, price_max):
                            continue

                    # 计算求购利润
                    purchase_profit_info = self._calculate_purchase_profit_for_item(item_dict, arbitrage_card_price)
                    if purchase_profit_info:
                        item_dict.update(purchase_profit_info)

                    results.append(item_dict)

                print(f"🎯 [最终结果] 返回 {len(results)} 个饰品")
                return results

        except Exception as e:
            print(f"❌ [SQL优化查询失败] {e}")
            # 降级到原有实现
            return self._search_items_fallback(
                name_query=name_query,
                item_types=item_types,
                qualities=qualities,
                rarities=rarities,
                price_min=price_min,
                price_max=price_max,
                sell_count_min=sell_count_min,
                sell_count_max=sell_count_max,
                arbitrage_threshold=arbitrage_threshold,
                arbitrage_card_price=arbitrage_card_price,
                sort_by=sort_by,
                limit=limit,
                offset=offset,
                item_ids=item_ids
            )

    def _check_price_filter(self, platform_prices: Dict, price_min: Optional[float], price_max: Optional[float]) -> bool:
        """检查价格筛选条件"""
        if price_min is None and price_max is None:
            return True

        # 检查BUFF和YOUPIN平台的在售价格
        buff_price = platform_prices.get('buff', {}).get('sell_price', 0)
        youpin_price = platform_prices.get('youpin', {}).get('sell_price', 0)

        # 只要其中一个平台的价格在范围内就通过筛选
        for price in [buff_price, youpin_price]:
            if price > 0:
                if price_min is None or price >= price_min:
                    if price_max is None or price <= price_max:
                        return True
        return False

    def _get_platform_prices_for_item(self, item_id: str) -> Dict[str, Dict]:
        """获取单个饰品的平台价格信息"""
        from src.cs2_investment.config.database import get_db_session
        from src.cs2_investment.models.platform_price import PlatformPrice
        from sqlalchemy import func, and_

        try:
            with get_db_session() as session:
                # 子查询：获取每个平台的最新更新时间
                subquery = session.query(
                    PlatformPrice.platform_name,
                    func.max(PlatformPrice.steamdt_update_time).label('max_update_time')
                ).filter(
                    and_(
                        PlatformPrice.item_id == item_id,
                        PlatformPrice.is_active == True
                    )
                ).group_by(PlatformPrice.platform_name).subquery()

                # 主查询：获取最新价格记录
                platform_prices = session.query(PlatformPrice).join(
                    subquery,
                    and_(
                        PlatformPrice.platform_name == subquery.c.platform_name,
                        PlatformPrice.steamdt_update_time == subquery.c.max_update_time
                    )
                ).filter(
                    PlatformPrice.item_id == item_id
                ).all()

                # 转换为字典格式
                result = {}
                for price in platform_prices:
                    platform_key = price.platform_name.lower().replace(' ', '_')
                    result[platform_key] = {
                        'platform': price.platform_name,
                        'sell_price': price.sell_price,
                        'sell_count': price.sell_count,
                        'bidding_price': price.bidding_price,
                        'bidding_count': price.bidding_count,
                        'update_time': price.update_time,
                        'data_source': price.data_source
                    }

                return result

        except Exception as e:
            print(f"❌ 获取平台价格失败: {item_id} - {e}")
            return {}

    def _search_items_fallback(self, **kwargs) -> List[Dict[str, Any]]:
        """降级到原有的查询实现"""
        print("⚠️ [降级] 使用原有的低效查询方式")
        # 这里可以调用原有的实现逻辑
        return []

    def _get_items_as_dict(self, name_query: Optional[str] = None,
                          item_types: Optional[List[str]] = None,
                          qualities: Optional[List[str]] = None,
                          rarities: Optional[List[str]] = None,
                          arbitrage_threshold: Optional[float] = None,
                          sort_by: str = "updated_desc",
                          limit: int = 1000,
                          offset: int = 0,
                          item_ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """获取饰品数据并直接转换为字典格式，避免Session问题"""
        try:
            from src.cs2_investment.config.database import get_db_session
            from src.cs2_investment.models.item import Item
            from sqlalchemy import desc, asc

            with get_db_session() as session:
                query = session.query(Item)

                # 名称搜索
                if name_query:
                    query = query.filter(
                        Item.name.like(f"%{name_query}%") |
                        Item.market_hash_name.like(f"%{name_query}%")
                    )

                # 类型过滤
                if item_types:
                    query = query.filter(Item.item_type.in_(item_types))

                # 品质过滤
                if qualities:
                    query = query.filter(Item.quality.in_(qualities))

                # 稀有度过滤
                if rarities:
                    query = query.filter(Item.rarity.in_(rarities))

                # 饰品ID过滤（用于收藏页面等场景）
                if item_ids:
                    query = query.filter(Item.item_id.in_(item_ids))

                # 搬砖率过滤
                if arbitrage_threshold is not None:
                    query = query.filter(
                        Item.arbitrage_ratio.isnot(None),
                        Item.arbitrage_ratio >= arbitrage_threshold
                    )

                # 排序
                if sort_by == "name_asc":
                    query = query.order_by(Item.name.asc())
                elif sort_by == "name_desc":
                    query = query.order_by(Item.name.desc())
                elif sort_by == "updated_desc":
                    query = query.order_by(Item.updated_at.desc())
                elif sort_by == "updated_asc":
                    query = query.order_by(Item.updated_at.asc())
                elif sort_by == "arbitrage_desc":
                    # 按搬砖率降序排序，NULL值排在最后
                    query = query.order_by(Item.arbitrage_ratio.desc().nulls_last())
                else:
                    query = query.order_by(Item.updated_at.desc())

                # 获取结果并立即转换为字典
                items = query.offset(offset).limit(limit).all()

                results = []
                for item in items:
                    # 解析platform_mappings数据
                    platform_mappings = {}
                    try:
                        platform_mappings_data = getattr(item, 'platform_mappings', None)
                        if platform_mappings_data:
                            # 检查数据类型
                            if isinstance(platform_mappings_data, list):
                                # 已经是列表格式，直接处理
                                platform_mappings_list = platform_mappings_data
                            elif isinstance(platform_mappings_data, str):
                                # 是JSON字符串，需要解析
                                import json
                                platform_mappings_list = json.loads(platform_mappings_data)
                            else:
                                platform_mappings_list = []

                            # 转换为字典格式，方便查找
                            for mapping in platform_mappings_list:
                                if isinstance(mapping, dict):
                                    platform_name = mapping.get('name', '').upper()
                                    # 注意：数据库中字段名是'itemId'，不是'item_id'
                                    platform_id = mapping.get('itemId', '') or mapping.get('item_id', '')
                                    if platform_name and platform_id:
                                        platform_mappings[platform_name] = platform_id
                    except (json.JSONDecodeError, TypeError, AttributeError):
                        platform_mappings = {}

                    item_dict = {
                        'item_id': item.item_id,
                        'name': item.name,
                        'item_type': item.item_type,
                        'quality': item.quality,
                        'rarity': item.rarity,
                        'exterior': getattr(item, 'exterior', None),
                        'image_url': getattr(item, 'image_url', None),
                        'market_hash_name': getattr(item, 'market_hash_name', None),
                        'platform_mappings': platform_mappings,
                        'arbitrage_ratio': float(item.arbitrage_ratio) if item.arbitrage_ratio else None,
                        'created_at': getattr(item, 'created_at', None),
                        'updated_at': getattr(item, 'updated_at', None)
                    }
                    results.append(item_dict)

                return results

        except Exception as e:
            print(f"获取饰品数据失败: {e}")
            return []



    def _get_platform_prices_as_dict(self, item_ids: List[str]) -> List[Dict[str, Any]]:
        """获取平台价格数据并直接转换为字典格式，避免Session问题"""
        try:
            from src.cs2_investment.config.database import get_db_session
            from src.cs2_investment.models.platform_price import PlatformPrice
            from sqlalchemy import and_

            with get_db_session() as session:
                # 获取指定饰品的最新平台价格
                query = session.query(PlatformPrice).filter(
                    and_(
                        PlatformPrice.item_id.in_(item_ids),
                        PlatformPrice.is_active == True
                    )
                ).order_by(PlatformPrice.update_time.desc())

                platform_prices = query.all()

                results = []
                for price in platform_prices:
                    price_dict = {
                        'item_id': price.item_id,
                        'platform_name': price.platform_name,
                        'platform_item_id': price.platform_item_id,
                        'sell_price': float(price.sell_price) if price.sell_price else 0,
                        'sell_count': price.sell_count or 0,
                        'bidding_price': float(price.bidding_price) if price.bidding_price else 0,
                        'bidding_count': price.bidding_count or 0,
                        'data_source': price.data_source or 'steamdt',
                        'update_time': price.update_time,
                        'query_time': price.query_time
                    }
                    results.append(price_dict)

                return results

        except Exception as e:
            print(f"获取平台价格数据失败: {e}")
            return []



    def _get_purchase_profit_for_sort(self, item_data: Dict[str, Any], arbitrage_card_price: Optional[float] = None) -> float:
        """获取求购利润用于排序（返回利润金额）"""
        try:
            profit_info = self._calculate_purchase_profit_for_item(item_data, arbitrage_card_price)
            return profit_info.get('profit', -999999) if profit_info else -999999
        except Exception:
            return -999999

    def _get_purchase_profit_rate_for_sort(self, item_data: Dict[str, Any], arbitrage_card_price: Optional[float] = None) -> float:
        """获取求购利润率用于排序（返回利润率百分比）"""
        try:
            profit_info = self._calculate_purchase_profit_for_item(item_data, arbitrage_card_price)
            return profit_info.get('profit_rate', -999999) if profit_info else -999999
        except Exception:
            return -999999

    def _calculate_purchase_profit_for_item(self, item_data: Dict[str, Any], arbitrage_card_price: Optional[float] = None) -> Optional[Dict[str, Any]]:
        """
        计算单个饰品的求购利润

        Args:
            item_data: 饰品数据，包含platform_prices等信息
            arbitrage_card_price: 搬砖卡价，如果为None则使用配置中的汇率

        Returns:
            Dict包含profit和profit_rate，如果无法计算则返回None
        """
        try:
            platform_prices = item_data.get('platform_prices', {})
            if not platform_prices:
                return None

            # 获取steam_direct的求购价
            steam_direct_data = platform_prices.get('steam_direct', {})
            if not steam_direct_data or not steam_direct_data.get('bidding_price'):
                return None

            steam_bidding_price = float(steam_direct_data.get('bidding_price', 0))
            if steam_bidding_price <= 0:
                return None

            # 获取buff和youpin的最低在售价
            buff_data = platform_prices.get('buff', {})
            youpin_data = platform_prices.get('youpin', {})

            buff_sell_price = float(buff_data.get('sell_price', 0)) if buff_data else 0
            youpin_sell_price = float(youpin_data.get('sell_price', 0)) if youpin_data else 0

            # 找到最低在售价
            valid_prices = [price for price in [buff_sell_price, youpin_sell_price] if price > 0]
            if not valid_prices:
                return None

            min_sell_price = min(valid_prices)

            # 获取卡价
            if arbitrage_card_price is not None and arbitrage_card_price > 0:
                card_price = arbitrage_card_price
            else:
                card_price = self._get_card_price_for_calculation()
                if card_price is None:
                    return None

            # 计算求购利润
            # 利润 = 最低在售价 - (卡价 × steam_direct求购价)
            profit = min_sell_price - (card_price * steam_bidding_price)

            # 计算利润率
            cost = card_price * steam_bidding_price
            profit_rate = (profit / cost * 100) if cost > 0 else 0

            return {
                'profit': profit,
                'profit_rate': profit_rate
            }

        except Exception as e:
            print(f"计算求购利润失败: {e}")
            return None

    def _get_card_price_for_calculation(self) -> Optional[float]:
        """
        获取卡价用于计算
        优先使用查询条件中的搬砖卡价，否则使用streamlit配置的汇率

        Returns:
            卡价，如果无法获取则返回None
        """
        try:
            # 尝试从streamlit session_state获取查询条件中的搬砖卡价
            import streamlit as st
            if hasattr(st, 'session_state') and hasattr(st.session_state, 'query_params'):
                query_params = st.session_state.query_params
                arbitrage_card_price = query_params.get('arbitrage_card_price')
                if arbitrage_card_price is not None and arbitrage_card_price > 0:
                    return float(arbitrage_card_price)

            # 如果没有查询条件中的卡价，使用汇率配置
            from src.cs2_investment.config.streamlit_config import get_streamlit_config
            streamlit_config = get_streamlit_config()
            return streamlit_config.real_exchange_rate

        except Exception as e:
            print(f"获取卡价失败: {e}")
            # 默认返回7.21作为备用卡价
            return 7.21
