"""
投资推荐数据访问对象
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, date
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_, func

from ..models.investment_recommendation import InvestmentRecommendation
from ..config.database import get_db_session
from ..utils.logger import get_logger

logger = get_logger(__name__)


class InvestmentRecommendationDAO:
    """投资推荐数据访问对象"""
    
    def save_recommendation(self, recommendation: InvestmentRecommendation) -> bool:
        """保存投资推荐"""
        try:
            with get_db_session() as session:
                # 检查是否已存在相同的推荐
                existing = session.query(InvestmentRecommendation)\
                    .filter(
                        InvestmentRecommendation.item_id == recommendation.item_id,
                        InvestmentRecommendation.recommendation_date == recommendation.recommendation_date
                    ).first()

                if existing:
                    logger.warning(f"⚠️ 推荐已存在，更新现有记录: {recommendation.item_id} - {recommendation.recommendation_date}")
                    # 更新现有记录
                    existing.recommendation_time = recommendation.recommendation_time
                    existing.recommendation_session_id = recommendation.recommendation_session_id
                    existing.recommendation_type = recommendation.recommendation_type
                    existing.total_score = recommendation.total_score
                    existing.algorithm_count = recommendation.algorithm_count
                    existing.algorithm_details = recommendation.algorithm_details
                    existing.recommendation_reason = recommendation.recommendation_reason
                    existing.risk_level = recommendation.risk_level
                    existing.confidence_level = recommendation.confidence_level
                    existing.current_price = recommendation.current_price
                    existing.price_change_7d = recommendation.price_change_7d
                    existing.price_change_30d = recommendation.price_change_30d
                    existing.volume_30d = recommendation.volume_30d
                    existing.amount_30d = recommendation.amount_30d
                    existing.hot_rank = recommendation.hot_rank
                    existing.valid_until = recommendation.valid_until
                    existing.status = recommendation.status
                else:
                    # 添加新记录
                    session.add(recommendation)

                session.commit()
                logger.info(f"✅ 投资推荐保存成功: {recommendation.item_id} - {recommendation.recommendation_type}")

                # 验证保存是否成功
                verify_count = session.query(InvestmentRecommendation)\
                    .filter(
                        InvestmentRecommendation.item_id == recommendation.item_id,
                        InvestmentRecommendation.recommendation_date == recommendation.recommendation_date
                    ).count()

                if verify_count > 0:
                    logger.info(f"✅ 保存验证成功: 数据库中已有记录")
                    return True
                else:
                    logger.error(f"❌ 保存验证失败: 数据库中未找到记录")
                    return False

        except Exception as e:
            logger.error(f"❌ 投资推荐保存失败: {e}")
            import traceback
            logger.debug(f"保存失败详细错误: {traceback.format_exc()}")
            return False
    
    def batch_save_recommendations(self, recommendations: List[InvestmentRecommendation]) -> bool:
        """批量保存投资推荐"""
        try:
            with get_db_session() as session:
                session.add_all(recommendations)
                session.commit()
                logger.info(f"✅ 批量投资推荐保存成功: {len(recommendations)} 条")
                return True
        except Exception as e:
            logger.error(f"❌ 批量投资推荐保存失败: {e}")
            return False
    
    def _detach_recommendation(self, rec: InvestmentRecommendation) -> InvestmentRecommendation:
        """将推荐对象从Session中分离，创建新的独立对象"""
        new_rec = InvestmentRecommendation()

        # 复制所有属性
        new_rec.id = rec.id
        new_rec.item_id = rec.item_id
        new_rec.recommendation_date = rec.recommendation_date
        new_rec.recommendation_time = rec.recommendation_time
        new_rec.recommendation_session_id = rec.recommendation_session_id
        new_rec.recommendation_type = rec.recommendation_type
        new_rec.total_score = rec.total_score
        new_rec.algorithm_count = rec.algorithm_count
        new_rec.algorithm_details = rec.algorithm_details
        new_rec.recommendation_reason = rec.recommendation_reason
        new_rec.risk_level = rec.risk_level
        new_rec.confidence_level = rec.confidence_level
        new_rec.current_price = rec.current_price
        new_rec.price_change_7d = rec.price_change_7d
        new_rec.price_change_30d = rec.price_change_30d
        new_rec.volume_30d = rec.volume_30d
        new_rec.amount_30d = rec.amount_30d
        new_rec.hot_rank = rec.hot_rank
        new_rec.valid_until = rec.valid_until
        new_rec.status = rec.status
        new_rec.created_at = rec.created_at
        new_rec.updated_at = rec.updated_at

        return new_rec

    def get_latest_recommendations(self, limit: int = 50) -> List[InvestmentRecommendation]:
        """获取最新的投资推荐"""
        try:
            with get_db_session() as session:
                recommendations = session.query(InvestmentRecommendation)\
                    .filter(InvestmentRecommendation.status == 'ACTIVE')\
                    .order_by(desc(InvestmentRecommendation.recommendation_time))\
                    .limit(limit)\
                    .all()

                # 创建脱离Session的对象列表
                return [self._detach_recommendation(rec) for rec in recommendations]
        except Exception as e:
            logger.error(f"❌ 获取最新投资推荐失败: {e}")
            return []
    
    def get_recommendations_by_date(self, target_date: date, limit: int = 100) -> List[InvestmentRecommendation]:
        """获取指定日期的投资推荐"""
        try:
            with get_db_session() as session:
                recommendations = session.query(InvestmentRecommendation)\
                    .filter(InvestmentRecommendation.recommendation_date == target_date)\
                    .order_by(desc(InvestmentRecommendation.total_score))\
                    .limit(limit)\
                    .all()

                # 创建脱离Session的对象列表
                return [self._detach_recommendation(rec) for rec in recommendations]
        except Exception as e:
            logger.error(f"❌ 获取指定日期投资推荐失败: {e}")
            return []
    
    def get_recommendations_by_type(self, recommendation_type: str, limit: int = 50) -> List[InvestmentRecommendation]:
        """获取指定类型的投资推荐"""
        try:
            with get_db_session() as session:
                recommendations = session.query(InvestmentRecommendation)\
                    .filter(
                        and_(
                            InvestmentRecommendation.recommendation_type == recommendation_type,
                            InvestmentRecommendation.status == 'ACTIVE'
                        )
                    )\
                    .order_by(desc(InvestmentRecommendation.total_score))\
                    .limit(limit)\
                    .all()

                # 创建脱离Session的对象列表
                return [self._detach_recommendation(rec) for rec in recommendations]
        except Exception as e:
            logger.error(f"❌ 获取指定类型投资推荐失败: {e}")
            return []
    
    def get_recommendation_by_item_and_date(self, item_id: str, target_date: date) -> Optional[InvestmentRecommendation]:
        """获取指定饰品和日期的推荐"""
        try:
            with get_db_session() as session:
                return session.query(InvestmentRecommendation)\
                    .filter(
                        and_(
                            InvestmentRecommendation.item_id == item_id,
                            InvestmentRecommendation.recommendation_date == target_date
                        )
                    )\
                    .first()
        except Exception as e:
            logger.error(f"❌ 获取指定饰品推荐失败: {e}")
            return None
    
    def get_top_recommendations(self, limit: int = 20, min_score: float = 70.0) -> List[InvestmentRecommendation]:
        """获取高评分推荐"""
        try:
            with get_db_session() as session:
                return session.query(InvestmentRecommendation)\
                    .filter(
                        and_(
                            InvestmentRecommendation.status == 'ACTIVE',
                            InvestmentRecommendation.total_score >= min_score,
                            InvestmentRecommendation.recommendation_type.in_(['BUY', 'HOLD'])
                        )
                    )\
                    .order_by(desc(InvestmentRecommendation.total_score))\
                    .limit(limit)\
                    .all()
        except Exception as e:
            logger.error(f"❌ 获取高评分推荐失败: {e}")
            return []
    
    def get_recommendations_statistics(self, target_date: Optional[date] = None) -> Dict[str, Any]:
        """获取推荐统计信息"""
        try:
            with get_db_session() as session:
                query = session.query(InvestmentRecommendation)
                
                if target_date:
                    query = query.filter(InvestmentRecommendation.recommendation_date == target_date)
                else:
                    # 默认获取最近7天的统计
                    from datetime import timedelta
                    week_ago = date.today() - timedelta(days=7)
                    query = query.filter(InvestmentRecommendation.recommendation_date >= week_ago)
                
                # 总数统计
                total_count = query.count()
                
                # 按类型统计
                type_stats = session.query(
                    InvestmentRecommendation.recommendation_type,
                    func.count(InvestmentRecommendation.id).label('count')
                ).filter(
                    InvestmentRecommendation.recommendation_date >= (target_date or week_ago)
                ).group_by(InvestmentRecommendation.recommendation_type).all()
                
                # 按风险等级统计
                risk_stats = session.query(
                    InvestmentRecommendation.risk_level,
                    func.count(InvestmentRecommendation.id).label('count')
                ).filter(
                    InvestmentRecommendation.recommendation_date >= (target_date or week_ago)
                ).group_by(InvestmentRecommendation.risk_level).all()
                
                # 平均评分
                avg_score = session.query(
                    func.avg(InvestmentRecommendation.total_score)
                ).filter(
                    InvestmentRecommendation.recommendation_date >= (target_date or week_ago)
                ).scalar()
                
                return {
                    'total_count': total_count,
                    'type_distribution': {row[0]: row[1] for row in type_stats},
                    'risk_distribution': {row[0]: row[1] for row in risk_stats},
                    'average_score': float(avg_score) if avg_score else 0.0,
                    'period': target_date.isoformat() if target_date else f"最近7天"
                }
                
        except Exception as e:
            logger.error(f"❌ 获取推荐统计失败: {e}")
            return {}
    
    def update_recommendation_status(self, recommendation_id: int, status: str) -> bool:
        """更新推荐状态"""
        try:
            with get_db_session() as session:
                recommendation = session.query(InvestmentRecommendation)\
                    .filter(InvestmentRecommendation.id == recommendation_id)\
                    .first()
                
                if recommendation:
                    recommendation.status = status
                    session.commit()
                    logger.info(f"✅ 推荐状态更新成功: {recommendation_id} -> {status}")
                    return True
                else:
                    logger.warning(f"⚠️ 推荐不存在: {recommendation_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ 推荐状态更新失败: {e}")
            return False
    
    def get_recommended_item_ids_by_date(self, target_date: date) -> List[str]:
        """获取指定日期已有推荐记录的饰品ID列表"""
        try:
            with get_db_session() as session:
                result = session.query(InvestmentRecommendation.item_id)\
                    .filter(InvestmentRecommendation.recommendation_date == target_date)\
                    .distinct()\
                    .all()

                item_ids = [row[0] for row in result]
                logger.debug(f"📊 获取到指定日期({target_date})已有推荐记录的饰品: {len(item_ids)} 个")
                return item_ids

        except Exception as e:
            logger.error(f"❌ 获取已推荐饰品ID列表失败: {e}")
            return []

    def expire_old_recommendations(self, days_old: int = 7) -> int:
        """将过期的推荐标记为过期状态"""
        try:
            from datetime import timedelta
            cutoff_date = date.today() - timedelta(days=days_old)

            with get_db_session() as session:
                updated_count = session.query(InvestmentRecommendation)\
                    .filter(
                        and_(
                            InvestmentRecommendation.recommendation_date < cutoff_date,
                            InvestmentRecommendation.status == 'ACTIVE'
                        )
                    )\
                    .update({'status': 'EXPIRED'})

                session.commit()
                logger.info(f"✅ 过期推荐标记完成: {updated_count} 条")
                return updated_count

        except Exception as e:
            logger.error(f"❌ 过期推荐标记失败: {e}")
            return 0


# 创建全局实例
investment_recommendation_dao = InvestmentRecommendationDAO()
