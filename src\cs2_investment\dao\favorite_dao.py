"""
收藏数据访问对象

提供收藏相关的数据库操作。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import desc, asc, and_, or_, func
from loguru import logger

from .base_dao import BaseDAO
from ..models.favorite import Favorite
from ..models.item import Item
from ..models.screening_result import ScreeningResult
from ..config.database import get_db_session


class FavoriteDAO(BaseDAO[Favorite]):
    """收藏DAO"""
    
    def __init__(self):
        super().__init__(Favorite)
    
    def add_favorite(self, user_id: str, item_id: str,
                    item_name: str = None, notes: str = None) -> Optional[Favorite]:
        """添加饰品收藏"""
        try:
            with get_db_session() as session:
                # 检查是否已经收藏
                existing = session.query(Favorite).filter(
                    and_(
                        Favorite.user_id == user_id,
                        Favorite.item_id == item_id
                    )
                ).first()

                if existing:
                    self.logger.info(f"饰品已收藏: user_id={user_id}, item_id={item_id}")
                    return existing

                # 创建新收藏
                favorite = Favorite(
                    user_id=user_id,
                    item_id=item_id,
                    item_name=item_name,
                    notes=notes,
                    created_at=datetime.now()
                )

                session.add(favorite)
                session.flush()
                session.refresh(favorite)

                self.logger.info(f"添加饰品收藏成功: user_id={user_id}, item_id={item_id}")
                return favorite

        except SQLAlchemyError as e:
            self.logger.error(f"添加收藏失败: {e}")
            raise
    
    def remove_favorite(self, user_id: str, item_id: str) -> bool:
        """取消饰品收藏"""
        try:
            with get_db_session() as session:
                deleted_count = session.query(Favorite).filter(
                    and_(
                        Favorite.user_id == user_id,
                        Favorite.item_id == item_id
                    )
                ).delete()

                if deleted_count > 0:
                    self.logger.info(f"取消收藏成功: user_id={user_id}, item_id={item_id}")
                    return True
                else:
                    self.logger.warning(f"收藏不存在: user_id={user_id}, item_id={item_id}")
                    return False

        except SQLAlchemyError as e:
            self.logger.error(f"取消收藏失败: {e}")
            raise
    
    def is_favorited(self, user_id: str, item_id: str) -> bool:
        """检查饰品是否已收藏"""
        try:
            with get_db_session() as session:
                count = session.query(Favorite).filter(
                    and_(
                        Favorite.user_id == user_id,
                        Favorite.item_id == item_id
                    )
                ).count()

                return count > 0

        except SQLAlchemyError as e:
            self.logger.error(f"检查收藏状态失败: {e}")
            return False
    
    def get_user_favorites(self, user_id: str, limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """获取用户收藏列表，返回字典格式避免Session问题"""
        try:
            with get_db_session() as session:
                favorites = session.query(Favorite).filter(
                    Favorite.user_id == user_id
                ).order_by(desc(Favorite.created_at))\
                .offset(offset).limit(limit).all()

                # 立即转换为字典格式
                results = []
                for fav in favorites:
                    fav_dict = {
                        'id': fav.id,
                        'user_id': fav.user_id,
                        'item_id': fav.item_id,
                        'item_name': fav.item_name,
                        'created_at': fav.created_at,
                        'updated_at': fav.updated_at,
                        'notes': fav.notes
                    }
                    results.append(fav_dict)

                return results

        except SQLAlchemyError as e:
            self.logger.error(f"获取用户收藏列表失败: {e}")
            raise

    def get_favorite_count(self, user_id: str) -> int:
        """获取收藏数量"""
        try:
            with get_db_session() as session:
                count = session.query(Favorite).filter(
                    Favorite.user_id == user_id
                ).count()

                return count

        except SQLAlchemyError as e:
            self.logger.error(f"获取收藏数量失败: {e}")
            return 0

    def search_user_favorites(self, user_id: str,
                             name_query: Optional[str] = None,
                             limit: int = 1000,
                             offset: int = 0) -> List[Dict[str, Any]]:
        """搜索用户收藏列表（带条件筛选）"""
        try:
            with get_db_session() as session:
                query = session.query(Favorite).filter(
                    Favorite.user_id == user_id
                )

                # 名称搜索
                if name_query:
                    query = query.filter(
                        Favorite.item_name.like(f"%{name_query}%")
                    )

                favorites = query.order_by(desc(Favorite.created_at))\
                    .offset(offset).limit(limit).all()

                # 立即转换为字典格式
                results = []
                for fav in favorites:
                    fav_dict = {
                        'id': fav.id,
                        'user_id': fav.user_id,
                        'item_id': fav.item_id,
                        'item_name': fav.item_name,
                        'created_at': fav.created_at,
                        'updated_at': fav.updated_at,
                        'notes': fav.notes
                    }
                    results.append(fav_dict)

                return results

        except SQLAlchemyError as e:
            self.logger.error(f"搜索用户收藏列表失败: {e}")
            raise

    def count_search_favorites(self, user_id: str, name_query: Optional[str] = None) -> int:
        """统计搜索收藏结果数量"""
        try:
            with get_db_session() as session:
                query = session.query(Favorite).filter(
                    Favorite.user_id == user_id
                )

                # 名称搜索
                if name_query:
                    query = query.filter(
                        Favorite.item_name.like(f"%{name_query}%")
                    )

                return query.count()

        except SQLAlchemyError as e:
            self.logger.error(f"统计搜索收藏结果失败: {e}")
            return 0

    def get_user_favorite_item_ids(self, user_id: str) -> List[str]:
        """获取用户收藏的所有饰品ID列表"""
        try:
            with get_db_session() as session:
                favorites = session.query(Favorite.item_id).filter(
                    Favorite.user_id == user_id
                ).all()

                return [fav.item_id for fav in favorites if fav.item_id]

        except SQLAlchemyError as e:
            self.logger.error(f"获取用户收藏饰品ID列表失败: {e}")
            return []

    def get_favorite_by_item_id(self, user_id: str, item_id: str) -> Optional[Dict[str, Any]]:
        """根据饰品ID获取收藏信息"""
        try:
            with get_db_session() as session:
                favorite = session.query(Favorite).filter(
                    Favorite.user_id == user_id,
                    Favorite.item_id == item_id
                ).first()

                if favorite:
                    return {
                        'id': favorite.id,
                        'user_id': favorite.user_id,
                        'item_id': favorite.item_id,
                        'item_name': favorite.item_name,
                        'created_at': favorite.created_at,
                        'updated_at': favorite.updated_at,
                        'notes': favorite.notes
                    }
                return None

        except SQLAlchemyError as e:
            self.logger.error(f"根据饰品ID获取收藏信息失败: {e}")
            return None
    

    
    def count_user_favorites(self, user_id: str, favorite_type: str = 'item') -> int:
        """统计用户收藏数量"""
        try:
            with get_db_session() as session:
                return session.query(Favorite).filter(
                    and_(
                        Favorite.user_id == user_id,
                        Favorite.favorite_type == favorite_type
                    )
                ).count()
                
        except SQLAlchemyError as e:
            self.logger.error(f"统计用户收藏数量失败: {e}")
            return 0
    
    def get_favorite_statistics(self, user_id: str) -> Dict[str, Any]:
        """获取收藏统计信息"""
        try:
            with get_db_session() as session:
                # 总收藏数
                total_count = session.query(Favorite).filter(Favorite.user_id == user_id).count()
                
                # 按类型统计
                type_stats = session.query(
                    Favorite.favorite_type,
                    func.count(Favorite.id).label('count')
                ).filter(Favorite.user_id == user_id)\
                .group_by(Favorite.favorite_type).all()
                
                # 最近收藏
                recent_count = session.query(Favorite).filter(
                    and_(
                        Favorite.user_id == user_id,
                        Favorite.created_at >= datetime.now() - timedelta(days=7)
                    )
                ).count()
                
                return {
                    'total_count': total_count,
                    'type_distribution': {stat[0]: stat[1] for stat in type_stats},
                    'recent_count': recent_count
                }
                
        except SQLAlchemyError as e:
            self.logger.error(f"获取收藏统计信息失败: {e}")
            return {}

    def get_favorites_need_update(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取需要更新Steam价格的收藏饰品
        按最老更新时间排序，优先更新最久未更新的饰品

        Args:
            user_id: 用户ID
            limit: 返回数量限制

        Returns:
            需要更新的收藏饰品列表
        """
        try:
            with get_db_session() as session:
                favorites = session.query(Favorite).filter(
                    Favorite.user_id == user_id
                ).order_by(
                    # 使用MySQL兼容的方式：NULL值排在前面，然后按时间升序
                    # ISNULL(field) 在MySQL中返回1表示NULL，0表示非NULL
                    func.isnull(Favorite.last_steam_update_time).desc(),
                    Favorite.last_steam_update_time.asc()
                ).limit(limit).all()

                return [self._favorite_to_dict(fav) for fav in favorites]

        except SQLAlchemyError as e:
            self.logger.error(f"获取需要更新的收藏列表失败: {e}")
            raise

    def update_favorite_steam_time(self, favorite_id: int) -> bool:
        """
        更新指定收藏记录的Steam更新时间

        Args:
            favorite_id: 收藏记录ID

        Returns:
            更新是否成功
        """
        try:
            with get_db_session() as session:
                favorite = session.query(Favorite).filter(Favorite.id == favorite_id).first()

                if not favorite:
                    self.logger.warning(f"收藏记录不存在: id={favorite_id}")
                    return False

                favorite.last_steam_update_time = datetime.now()
                session.commit()

                self.logger.debug(f"更新Steam时间成功: favorite_id={favorite_id}")
                return True

        except SQLAlchemyError as e:
            self.logger.error(f"更新Steam时间失败: favorite_id={favorite_id}, error={e}")
            return False

    def batch_update_steam_times(self, favorite_ids: List[int]) -> int:
        """
        批量更新多个收藏记录的Steam更新时间

        Args:
            favorite_ids: 收藏记录ID列表

        Returns:
            成功更新的记录数量
        """
        if not favorite_ids:
            return 0

        try:
            with get_db_session() as session:
                update_count = session.query(Favorite).filter(
                    Favorite.id.in_(favorite_ids)
                ).update(
                    {Favorite.last_steam_update_time: datetime.now()},
                    synchronize_session=False
                )

                session.commit()

                self.logger.info(f"批量更新Steam时间成功: 更新了{update_count}条记录")
                return update_count

        except SQLAlchemyError as e:
            self.logger.error(f"批量更新Steam时间失败: favorite_ids={favorite_ids}, error={e}")
            return 0

    def _favorite_to_dict(self, favorite: Favorite) -> Dict[str, Any]:
        """
        将Favorite对象转换为字典

        Args:
            favorite: Favorite对象

        Returns:
            字典格式的收藏数据
        """
        return {
            'id': favorite.id,
            'user_id': favorite.user_id,
            'item_id': favorite.item_id,
            'item_name': favorite.item_name,
            'created_at': favorite.created_at,
            'updated_at': favorite.updated_at,
            'last_steam_update_time': favorite.last_steam_update_time,
            'notes': favorite.notes,
            'favorite_type': getattr(favorite, 'favorite_type', None)
        }


# ScreeningResultFavoriteDAO 已废弃，使用统一的 FavoriteDAO
