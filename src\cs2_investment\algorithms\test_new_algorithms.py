"""
新算法测试脚本

测试技术突破型、价值回归型、热点追踪型算法的功能。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.algorithms.algorithm_manager import AlgorithmManager
from loguru import logger


def test_algorithm_registration():
    """测试算法注册"""
    print("🔍 测试算法注册...")
    
    try:
        manager = AlgorithmManager()
        algorithms = manager.get_available_algorithms()
        
        print(f"✅ 成功注册 {len(algorithms)} 个算法:")
        for algo in algorithms:
            print(f"   - {algo}")
        
        # 检查新算法是否注册成功
        expected_new_algorithms = [
            "技术突破型",
            "价值回归型", 
            "热点追踪型"
        ]
        
        for expected in expected_new_algorithms:
            if expected in algorithms:
                print(f"✅ {expected}算法注册成功")
            else:
                print(f"❌ {expected}算法注册失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 算法注册测试失败: {e}")
        return False


def test_algorithm_info():
    """测试算法信息获取"""
    print("\n🔍 测试算法信息获取...")
    
    try:
        manager = AlgorithmManager()
        all_info = manager.get_all_algorithms_info()
        
        print(f"✅ 获取到 {len(all_info)} 个算法信息:")
        for info in all_info:
            print(f"   - {info['investment_type']}: {info['algorithm_name']} v{info['version']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 算法信息测试失败: {e}")
        return False


def test_single_algorithm_execution():
    """测试单个算法执行"""
    print("\n🔍 测试单个算法执行...")
    
    try:
        manager = AlgorithmManager()
        
        # 测试新算法
        test_algorithms = [
            "技术突破型",
            "价值回归型",
            "热点追踪型"
        ]
        
        for algo_type in test_algorithms:
            try:
                print(f"\n   测试 {algo_type} 算法...")
                results = manager.run_single_algorithm(algo_type, limit=5)
                print(f"   ✅ {algo_type} 算法执行成功，返回 {len(results)} 个结果")
                
                # 显示第一个结果的详细信息
                if results:
                    first_result = results[0]
                    print(f"      示例结果: {first_result.analysis_summary}")
                    print(f"      评分: {first_result.score}, 建议: {first_result.recommendation}")
                
            except Exception as e:
                print(f"   ❌ {algo_type} 算法执行失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 单个算法执行测试失败: {e}")
        return False


def test_all_algorithms_execution():
    """测试所有算法批量执行"""
    print("\n🔍 测试所有算法批量执行...")
    
    try:
        manager = AlgorithmManager()
        all_results = manager.run_all_algorithms(limit_per_type=3)
        
        print(f"✅ 批量执行成功，共 {len(all_results)} 种算法类型:")
        
        total_results = 0
        for algo_type, results in all_results.items():
            result_count = len(results)
            total_results += result_count
            print(f"   - {algo_type}: {result_count} 个结果")
        
        print(f"   总计: {total_results} 个投资建议")
        
        return True
        
    except Exception as e:
        print(f"❌ 批量算法执行测试失败: {e}")
        return False


def test_algorithm_performance():
    """测试算法性能"""
    print("\n🔍 测试算法性能...")
    
    try:
        import time
        manager = AlgorithmManager()
        
        # 测试新算法的性能
        test_algorithms = [
            "技术突破型",
            "价值回归型", 
            "热点追踪型"
        ]
        
        for algo_type in test_algorithms:
            try:
                start_time = time.time()
                results = manager.run_single_algorithm(algo_type, limit=10)
                end_time = time.time()
                
                duration = end_time - start_time
                print(f"   {algo_type}: {duration:.2f}秒, {len(results)}个结果")
                
            except Exception as e:
                print(f"   {algo_type}: 执行失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 算法性能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试新增投资筛选算法")
    print("=" * 60)
    
    test_results = []
    
    # 执行各项测试
    test_functions = [
        ("算法注册", test_algorithm_registration),
        ("算法信息", test_algorithm_info),
        ("单个算法执行", test_single_algorithm_execution),
        ("批量算法执行", test_all_algorithms_execution),
        ("算法性能", test_algorithm_performance),
    ]
    
    for test_name, test_func in test_functions:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！新算法集成成功！")
    else:
        print("⚠️ 部分测试失败，请检查相关问题")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
