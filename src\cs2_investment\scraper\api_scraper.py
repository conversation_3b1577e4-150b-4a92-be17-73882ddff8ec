"""
API数据抓取器

基于SteamDT API的数据抓取实现，继承AbstractDataScraper抽象接口。
使用HTTP API直接获取数据，确保输出格式与PlaywrightScraper完全一致。
"""

import asyncio
from datetime import datetime
from typing import Optional, Dict, Any
from loguru import logger

from .abstract_data_scraper import (
    AbstractDataScraper, 
    ScraperStartupError, 
    ScraperShutdownError, 
    ScrapingDataError,
    ScrapingTimeoutError
)
from .data_models import ScrapingResult, TrendData, KlineData, ItemInfo
from .steamdt_api import SteamDTAPI
from ..config.timer_config import get_timer_config


class APIScraper(AbstractDataScraper):
    """基于API的数据抓取器
    
    继承AbstractDataScraper抽象接口，使用SteamDT API直接获取数据。
    确保输出的ScrapingResult格式与PlaywrightScraper完全一致。
    """

    def __init__(self, proxy_enabled: bool = None, proxy_url: str = None):
        """初始化API抓取器

        Args:
            proxy_enabled: 是否启用代理，如果为None则使用配置文件设置
            proxy_url: 代理URL，如果为None则使用配置文件设置
        """
        super().__init__()

        # 加载配置
        self.config = get_timer_config()
        self.scraping_config = self.config.scraping

        # 代理配置 - 优先使用传入参数，否则使用配置文件
        self.proxy_enabled = proxy_enabled if proxy_enabled is not None else self.scraping_config.proxy_enabled
        self.proxy_url = proxy_url if proxy_url is not None else self.scraping_config.proxy_url

        # API客户端
        self.api_client: Optional[SteamDTAPI] = None

        # 重试配置
        self.max_retries = self.scraping_config.max_retry_attempts
        self.retry_delay = self.scraping_config.retry_delay

        # 超时配置
        self.timeout = self.scraping_config.api_timeout

    async def start(self) -> None:
        """启动API客户端
        
        初始化SteamDTAPI客户端并建立连接。
        
        Raises:
            ScraperStartupError: 启动失败时抛出
        """
        try:
            if self._is_started:
                self.logger.warning("API抓取器已经启动")
                return
            
            self.logger.info("🚀 启动API客户端...")
            
            # 创建API客户端（传递代理配置和延迟配置）
            self.api_client = SteamDTAPI(
                proxy_enabled=self.proxy_enabled,
                proxy_url=self.proxy_url,
                api_delay_min=self.scraping_config.api_delay_min_seconds,
                api_delay_max=self.scraping_config.api_delay_max_seconds
            )

            # API客户端现在每次请求都使用全新session，无需初始化
            if self.proxy_enabled:
                self.logger.info(f"🌐 API抓取器将使用代理: {self.proxy_url}")
            else:
                self.logger.info("🚫 API抓取器代理已禁用，直接连接")
            
            self._is_started = True
            self.logger.info("✅ API客户端启动成功")
            
        except Exception as e:
            self.logger.error(f"❌ API客户端启动失败: {e}")
            await self._cleanup_resources()
            raise ScraperStartupError(f"Failed to start API client: {e}")

    async def stop(self) -> None:
        """停止API客户端
        
        关闭API客户端连接并清理资源。
        
        Raises:
            ScraperShutdownError: 停止失败时抛出
        """
        try:
            if not self._is_started:
                self.logger.warning("API抓取器未启动")
                return
            
            self.logger.info("🛑 停止API客户端...")
            
            await self._cleanup_resources()
            
            self._is_started = False
            self.logger.info("✅ API客户端停止成功")
            
        except Exception as e:
            self.logger.error(f"❌ API客户端停止失败: {e}")
            raise ScraperShutdownError(f"Failed to stop API client: {e}")

    async def _cleanup_resources(self) -> None:
        """清理API客户端资源"""
        try:
            if self.api_client:
                # API客户端现在不需要特殊清理，每次请求都是全新session
                self.api_client = None

        except Exception as e:
            self.logger.warning(f"清理API资源时出现警告: {e}")

    async def scrape_item_data(self, item_url: str, data_requirements: Optional[Dict[str, bool]] = None) -> ScrapingResult:
        """抓取单个饰品的数据
        
        使用API方式获取饰品数据，确保输出格式与PlaywrightScraper完全一致。
        
        Args:
            item_url: 饰品页面URL
            data_requirements: 数据需求配置，如果为None则抓取所有数据
            
        Returns:
            ScrapingResult: 抓取结果对象，格式与PlaywrightScraper一致
            
        Raises:
            ScrapingDataError: 抓取过程中发生错误时抛出
        """
        try:
            if not self._is_started:
                raise ScrapingDataError("API scraper not started")
            
            # 验证和标准化数据需求
            validated_requirements = self.validate_data_requirements(data_requirements)
            
            # 记录抓取开始
            self.log_scraping_start(item_url, validated_requirements)
            
            # 执行带重试的抓取逻辑
            result = await self._perform_scraping_with_retry(item_url, validated_requirements)
            
            # 记录抓取结果
            self.log_scraping_result(result, item_url)
            
            return result
            
        except Exception as e:
            error_msg = f"Failed to scrape item data from {item_url}: {e}"
            self.logger.error(f"❌ {error_msg}")
            
            return ScrapingResult(
                success=False,
                error_message=error_msg,
                collected_at=datetime.now()
            )

    async def _perform_scraping_with_retry(self, item_url: str, data_requirements: Dict[str, bool]) -> ScrapingResult:
        """执行带重试机制的抓取逻辑"""
        last_error = None
        
        for attempt in range(self.max_retries):
            try:
                self.logger.info(f"🔄 第{attempt + 1}次尝试抓取数据...")
                
                # 设置超时
                result = await asyncio.wait_for(
                    self._perform_api_scraping(item_url, data_requirements),
                    timeout=self.timeout
                )
                
                if result.success:
                    if attempt > 0:
                        self.logger.info(f"✅ 第{attempt + 1}次尝试成功")
                    return result
                else:
                    last_error = result.error_message
                    self.logger.warning(f"⚠️ 第{attempt + 1}次尝试失败: {last_error}")
                    
            except asyncio.TimeoutError:
                last_error = f"API请求超时（{self.timeout}秒）"
                self.logger.warning(f"⏰ 第{attempt + 1}次尝试超时")
                
            except Exception as e:
                last_error = str(e)
                self.logger.warning(f"❌ 第{attempt + 1}次尝试异常: {last_error}")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < self.max_retries - 1:
                await asyncio.sleep(self.retry_delay)
        
        # 所有重试都失败
        return ScrapingResult(
            success=False,
            error_message=f"API抓取失败，已重试{self.max_retries}次。最后错误: {last_error}",
            collected_at=datetime.now()
        )

    async def _perform_api_scraping(self, item_url: str, data_requirements: Dict[str, bool]) -> ScrapingResult:
        """执行实际的API抓取逻辑
        
        使用SteamDTAPI获取数据，并转换为与PlaywrightScraper一致的格式。
        """
        try:
            # 使用现有的API客户端方法（已经返回ScrapingResult）
            result = await self.api_client.scrape_item_data_api(item_url, data_requirements)

            # 如果启用了数据验证，进行格式验证
            if self.scraping_config.data_validation_enabled:
                self._validate_result_format(result)

            return result
            
        except Exception as e:
            self.logger.error(f"API抓取过程中发生错误: {e}")
            return ScrapingResult(
                success=False,
                error_message=str(e),
                collected_at=datetime.now()
            )

    def _standardize_api_result(self, api_result: ScrapingResult, data_requirements: Dict[str, bool]) -> ScrapingResult:
        """标准化API抓取结果，确保与PlaywrightScraper格式完全一致

        Args:
            api_result: 来自SteamDTAPI的原始结果
            data_requirements: 数据需求配置

        Returns:
            ScrapingResult: 标准化后的结果，与PlaywrightScraper格式一致
        """
        try:
            if not api_result.success:
                return api_result

            # 确保数据格式与PlaywrightScraper一致
            standardized_result = ScrapingResult(
                success=True,
                item_info=None,  # 不保存饰品信息，与PlaywrightScraper保持一致
                collected_at=datetime.now()
            )

            # 处理趋势数据
            if api_result.trend_data_3m and data_requirements.get('trend_data_3m', False):
                standardized_result.trend_data_3m = self._standardize_trend_data(
                    api_result.trend_data_3m, '3m'
                )

            if api_result.trend_data_6m and data_requirements.get('trend_data_6m', False):
                standardized_result.trend_data_6m = self._standardize_trend_data(
                    api_result.trend_data_6m, '6m'
                )

            # 设置主要趋势数据（优先使用6个月数据，如果没有则使用3个月数据）
            standardized_result.trend_data = (
                standardized_result.trend_data_6m if standardized_result.trend_data_6m
                else standardized_result.trend_data_3m
            )

            # 处理K线数据
            if api_result.hourly_kline and data_requirements.get('hourly_kline', False):
                standardized_result.hourly_kline = self._standardize_kline_data(
                    api_result.hourly_kline, 'hourly'
                )

            if api_result.daily_kline_1 and data_requirements.get('daily_kline_1', False):
                standardized_result.daily_kline_1 = self._standardize_kline_data(
                    api_result.daily_kline_1, 'daily'
                )

            if api_result.daily_kline_2 and data_requirements.get('daily_kline_2', False):
                standardized_result.daily_kline_2 = self._standardize_kline_data(
                    api_result.daily_kline_2, 'daily'
                )

            if api_result.weekly_kline and data_requirements.get('weekly_kline', False):
                standardized_result.weekly_kline = self._standardize_kline_data(
                    api_result.weekly_kline, 'weekly'
                )

            # 设置向后兼容的daily_kline（使用第一次日K数据）
            standardized_result.daily_kline = standardized_result.daily_kline_1

            return standardized_result

        except Exception as e:
            self.logger.error(f"标准化API结果失败: {e}")
            return ScrapingResult(
                success=False,
                error_message=f"Failed to standardize API result: {e}",
                collected_at=datetime.now()
            )

    def _standardize_trend_data(self, trend_data: TrendData, timerange: str) -> TrendData:
        """标准化趋势数据格式

        确保趋势数据格式与PlaywrightScraper完全一致。
        """
        try:
            # 创建新的TrendData对象，确保格式一致
            return TrendData(
                item_id=trend_data.item_id,
                platform=getattr(trend_data, 'platform', 'ALL'),
                time_range=timerange,
                data_points=getattr(trend_data, 'data_points', []),
                collected_at=datetime.now(),
                raw_data=trend_data.raw_data if hasattr(trend_data, 'raw_data') else None
            )

        except Exception as e:
            self.logger.error(f"标准化趋势数据失败: {e}")
            return trend_data  # 返回原数据

    def _standardize_kline_data(self, kline_data: KlineData, kline_type: str) -> KlineData:
        """标准化K线数据格式

        确保K线数据格式与PlaywrightScraper完全一致。
        """
        try:
            # 创建新的KlineData对象，确保格式一致
            return KlineData(
                item_id=kline_data.item_id,
                kline_type=kline_type,
                platform=getattr(kline_data, 'platform', 'ALL'),
                data_points=getattr(kline_data, 'data_points', []),
                collected_at=datetime.now(),
                raw_data=kline_data.raw_data if hasattr(kline_data, 'raw_data') else None
            )

        except Exception as e:
            self.logger.error(f"标准化K线数据失败: {e}")
            return kline_data  # 返回原数据

    def get_scraper_info(self) -> Dict[str, Any]:
        """获取抓取器信息

        Returns:
            Dict: 抓取器的基本信息
        """
        return {
            'scraper_type': 'api',
            'version': '1.0.0',
            'scraper_name': 'APIScraper',
            'capabilities': [
                'trend_data',
                'kline_data',
                'api_requests',
                'fast_response'
            ],
            'config_method': self.scraping_config.scraping_method,
            'timeout_settings': {
                'api_timeout': self.scraping_config.api_timeout,
                'retry_delay': self.scraping_config.retry_delay
            },
            'retry_settings': {
                'max_retry_attempts': self.scraping_config.max_retry_attempts,
                'retry_delay': self.scraping_config.retry_delay
            },
            'is_started': self.is_started,
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay,
            'timeout': self.timeout,
            'api_client_available': self.api_client is not None
        }

    def _validate_result_format(self, result: ScrapingResult) -> None:
        """验证抓取结果格式

        Args:
            result: 抓取结果
        """
        try:
            from .data_validator import validate_scraping_result

            # 进行格式验证
            validation_report = validate_scraping_result(result, "api")

            # 记录验证结果
            if validation_report['validation_summary']['overall_valid']:
                self.logger.debug("✅ API抓取结果格式验证通过")
            else:
                error_count = validation_report['validation_summary']['total_errors']
                warning_count = validation_report['validation_summary']['total_warnings']
                self.logger.warning(f"⚠️ API抓取结果格式验证发现问题: {error_count}个错误, {warning_count}个警告")

                # 记录具体错误
                format_errors = validation_report.get('format_validation', {}).get('errors', [])
                for error in format_errors[:3]:  # 只记录前3个错误
                    self.logger.warning(f"   - {error}")

        except Exception as e:
            self.logger.warning(f"数据格式验证失败: {e}")
