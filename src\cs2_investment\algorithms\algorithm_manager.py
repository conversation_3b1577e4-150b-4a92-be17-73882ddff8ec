"""
算法管理器

统一管理和执行所有投资筛选算法。
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from loguru import logger

from .base_algorithm import BaseScreeningAlgorithm
from .blue_chip_algorithm import BlueChipAlgorithm
from .growth_potential_algorithm import GrowthPotentialAlgorithm
from .scarcity_value_algorithm import ScarcityValueAlgorithm
from .supply_shock_recovery_algorithm import SupplyShockRecoveryAlgorithm
from .technical_breakthrough_algorithm import TechnicalBreakthroughAlgorithm
from .value_reversion_algorithm import ValueReversionAlgorithm
from .hotspot_tracking_algorithm import HotspotTrackingAlgorithm

from ..dao.screening_result_dao import ScreeningResultDAO
from ..models.screening_result import ScreeningResult


class AlgorithmManager:
    """算法管理器"""
    
    def __init__(self):
        self.algorithms: Dict[str, BaseScreeningAlgorithm] = {}
        self.screening_dao = ScreeningResultDAO()
        self.logger = logger.bind(manager="AlgorithmManager")
        
        # 注册所有算法
        self._register_algorithms()
    
    def _register_algorithms(self):
        """注册所有筛选算法"""
        algorithms = [
            BlueChipAlgorithm(),
            GrowthPotentialAlgorithm(),
            ScarcityValueAlgorithm(),
            SupplyShockRecoveryAlgorithm(),
            TechnicalBreakthroughAlgorithm(),
            ValueReversionAlgorithm(),
            HotspotTrackingAlgorithm(),
        ]
        
        for algorithm in algorithms:
            self.algorithms[algorithm.investment_type] = algorithm
            self.logger.info(f"注册算法: {algorithm.investment_type}")
    
    def get_available_algorithms(self) -> List[str]:
        """获取可用的算法列表"""
        return list(self.algorithms.keys())
    
    def run_single_algorithm(self, investment_type: str, limit: int = 100) -> List[ScreeningResult]:
        """运行单个筛选算法
        
        Args:
            investment_type: 投资类型
            limit: 结果数量限制
            
        Returns:
            List[ScreeningResult]: 筛选结果列表
        """
        if investment_type not in self.algorithms:
            raise ValueError(f"未找到算法: {investment_type}")
        
        algorithm_instance = self.algorithms[investment_type]  # 这已经是实例了！
        self.logger.info(f"开始运行算法: {investment_type}")

        try:
            # 直接运行筛选（不需要再次实例化）
            results = algorithm_instance.run_screening(limit)
            self.logger.info(f"算法运行完成: {investment_type}, 结果数量: {len(results)}")
            return results
        except Exception as e:
            self.logger.error(f"算法运行失败: {investment_type}, 错误: {e}")
            raise
    
    def run_all_algorithms(self, limit_per_type: int = 50) -> Dict[str, List[ScreeningResult]]:
        """运行所有筛选算法
        
        Args:
            limit_per_type: 每种类型的结果数量限制
            
        Returns:
            Dict[str, List[ScreeningResult]]: 按投资类型分组的筛选结果
        """
        all_results = {}
        
        for investment_type in self.algorithms:
            try:
                results = self.run_single_algorithm(investment_type, limit_per_type)
                all_results[investment_type] = results
            except Exception as e:
                self.logger.error(f"算法执行失败: {investment_type}, 错误: {e}")
                all_results[investment_type] = []
        
        return all_results
    
    def save_screening_results(self, results: List[ScreeningResult]) -> bool:
        """保存筛选结果到数据库
        
        Args:
            results: 筛选结果列表
            
        Returns:
            bool: 保存是否成功
        """
        if not results:
            self.logger.warning("没有筛选结果需要保存")
            return True
        
        try:
            # 转换为字典格式
            results_data = []
            for result in results:
                result_dict = {
                    'item_id': result.item_id,
                    'screening_time': result.screening_time,
                    'investment_type': result.investment_type,
                    'algorithm_version': result.algorithm_version,
                    'score': result.score,
                    'rank': result.rank,
                    'confidence': result.confidence,
                    'current_price': result.current_price,
                    'price_change_7d': result.price_change_7d,
                    'price_change_30d': result.price_change_30d,
                    'volume_30d': result.volume_30d,
                    'amount_30d': result.amount_30d,
                    'hot_rank': result.hot_rank,
                    'analysis_summary': result.analysis_summary,
                    'risk_level': result.risk_level,
                    'recommendation': result.recommendation
                }
                results_data.append(result_dict)
            
            # 批量保存
            saved_results = self.screening_dao.batch_insert_results(results_data)
            self.logger.info(f"筛选结果保存成功: {len(saved_results)} 条")
            return True
            
        except Exception as e:
            self.logger.error(f"保存筛选结果失败: {e}")
            return False

    def run_algorithms_for_single_item(self, item_id: str, analysis_data: Dict) -> List[Dict]:
        """为单个饰品运行所有投资筛选算法

        Args:
            item_id: 饰品ID
            analysis_data: 分析数据

        Returns:
            List[Dict]: 筛选结果列表
        """
        try:
            self.logger.info(f"🔍 开始为饰品 {item_id} 运行投资筛选算法")

            all_results = []

            # 遍历所有算法
            for investment_type, algorithm_class in self.algorithms.items():
                try:
                    self.logger.debug(f"运行算法: {investment_type}")

                    # 直接运行单个算法（避免递归调用）
                    try:
                        # 运行算法（使用现有的run方法，但限制为单个饰品）
                        results = self.run_single_algorithm(investment_type, limit=100)

                        # 过滤出匹配的饰品
                        matching_results = [r for r in results if r.item_id == item_id]

                        if matching_results:
                            # 转换为字典格式
                            for result in matching_results:
                                result_dict = {
                                    'item_id': result.item_id,
                                    'investment_type': result.investment_type,
                                    'score': result.score,
                                    'reason': result.analysis_summary,  # 使用analysis_summary作为reason
                                    'risk_level': result.risk_level,
                                    'recommendation': result.recommendation,
                                    'screening_time': result.screening_time
                                }
                                all_results.append(result_dict)

                            self.logger.info(f"✅ {investment_type}: 生成 {len(matching_results)} 个推荐")
                        else:
                            self.logger.debug(f"⚠️ {investment_type}: 该饰品未通过筛选")

                    except Exception as algo_error:
                        self.logger.error(f"❌ 算法 {investment_type} 执行异常: {algo_error}")
                        continue

                except Exception as e:
                    self.logger.error(f"❌ 算法 {investment_type} 运行失败: {e}")
                    continue

            self.logger.info(f"🎯 单饰品投资筛选完成: 总共生成 {len(all_results)} 个推荐")
            return all_results

        except Exception as e:
            self.logger.error(f"❌ 单饰品投资筛选失败: {e}")
            return []

    def run_and_save_screening(self, investment_types: Optional[List[str]] = None,
                              limit_per_type: int = 50) -> Dict[str, Any]:
        """运行筛选并保存结果
        
        Args:
            investment_types: 指定要运行的投资类型，None表示运行所有
            limit_per_type: 每种类型的结果数量限制
            
        Returns:
            Dict[str, Any]: 执行结果统计
        """
        start_time = datetime.now()
        
        # 确定要运行的算法
        if investment_types is None:
            investment_types = self.get_available_algorithms()
        else:
            # 验证投资类型
            invalid_types = [t for t in investment_types if t not in self.algorithms]
            if invalid_types:
                raise ValueError(f"无效的投资类型: {invalid_types}")
        
        self.logger.info(f"开始运行筛选: {investment_types}")
        
        # 运行筛选
        all_results = {}
        total_results = []
        
        for investment_type in investment_types:
            try:
                results = self.run_single_algorithm(investment_type, limit_per_type)
                all_results[investment_type] = len(results)
                total_results.extend(results)
            except Exception as e:
                self.logger.error(f"算法执行失败: {investment_type}, 错误: {e}")
                all_results[investment_type] = 0
        
        # 保存结果
        save_success = self.save_screening_results(total_results)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 统计结果
        summary = {
            'start_time': start_time,
            'end_time': end_time,
            'duration_seconds': duration,
            'algorithms_run': len(investment_types),
            'total_results': len(total_results),
            'results_by_type': all_results,
            'save_success': save_success
        }
        
        self.logger.info(f"筛选完成: 运行 {len(investment_types)} 个算法, 生成 {len(total_results)} 个结果, 耗时 {duration:.2f} 秒")
        
        return summary
    
    def get_algorithm_info(self, investment_type: str) -> Dict[str, Any]:
        """获取算法信息
        
        Args:
            investment_type: 投资类型
            
        Returns:
            Dict[str, Any]: 算法信息
        """
        if investment_type not in self.algorithms:
            raise ValueError(f"未找到算法: {investment_type}")
        
        algorithm = self.algorithms[investment_type]
        
        return {
            'algorithm_name': algorithm.algorithm_name,
            'investment_type': algorithm.investment_type,
            'version': algorithm.version,
            'description': algorithm.__doc__ or "无描述"
        }
    
    def get_all_algorithms_info(self) -> List[Dict[str, Any]]:
        """获取所有算法信息"""
        return [
            self.get_algorithm_info(investment_type)
            for investment_type in self.algorithms
        ]
