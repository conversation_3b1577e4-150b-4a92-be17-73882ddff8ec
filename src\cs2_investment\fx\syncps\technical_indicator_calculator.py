#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品分析系统 - 重构技术指标计算器
统一基于日K数据计算所有技术指标
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple
import warnings
warnings.filterwarnings('ignore')

class TechnicalIndicatorCalculator:
    """技术指标计算器 - 统一基于日K数据"""
    
    def __init__(self, daily_data: pd.DataFrame, weekly_data: pd.DataFrame = None):
        """
        初始化技术指标计算器
        
        Args:
            daily_data: 日K数据 (主要计算数据源)
            weekly_data: 周K数据 (长期趋势分析)
        """
        self.daily_data = daily_data.copy()
        self.weekly_data = weekly_data.copy() if weekly_data is not None else None
        self.indicators = {}
        
        # 验证数据完整性
        self._validate_data()
    
    def _validate_data(self):
        """验证数据完整性"""
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        
        for col in required_columns:
            if col not in self.daily_data.columns:
                raise ValueError(f"日K数据缺少必要字段: {col}")
        
        if len(self.daily_data) < 26:
            print("⚠️ 警告: 日K数据不足26条，部分指标可能不准确")
    
    def calculate_all_indicators(self) -> Dict:
        """计算所有技术指标 - 统一基于日K数据"""
        
        print("🔄 计算技术指标 (基于日K数据)...")
        
        # 基础价格数据
        prices = self.daily_data['close']
        highs = self.daily_data['high']
        lows = self.daily_data['low']
        volumes = self.daily_data['volume']
        
        # 1. 移动平均线
        self.indicators['ema_12'] = self._calculate_ema(prices, 12)
        self.indicators['ema_26'] = self._calculate_ema(prices, 26)
        self.indicators['sma_20'] = self._calculate_sma(prices, 20)
        
        # 2. MACD指标
        macd_data = self._calculate_macd(prices)
        self.indicators.update(macd_data)
        
        # 3. RSI指标
        self.indicators['rsi'] = self._calculate_rsi(prices, 14)
        
        # 4. 布林线
        bollinger_data = self._calculate_bollinger_bands(prices, 20, 2)
        self.indicators.update(bollinger_data)
        
        # 5. ATR指标
        self.indicators['atr'] = self._calculate_atr(highs, lows, prices, 14)
        
        # 6. 成交量指标
        volume_data = self._calculate_volume_indicators(prices, volumes)
        self.indicators.update(volume_data)
        
        # 7. 趋势指标
        trend_data = self._calculate_trend_indicators()
        self.indicators.update(trend_data)
        
        # 8. 长期趋势 (基于周K数据)
        if self.weekly_data is not None:
            weekly_trend = self._calculate_weekly_trend()
            self.indicators.update(weekly_trend)
        
        print("✅ 技术指标计算完成")
        return self.indicators
    
    def _calculate_ema(self, prices: pd.Series, period: int) -> pd.Series:
        """计算指数移动平均线"""
        return prices.ewm(span=period).mean()
    
    def _calculate_sma(self, prices: pd.Series, period: int) -> pd.Series:
        """计算简单移动平均线"""
        return prices.rolling(window=period).mean()
    
    def _calculate_macd(self, prices: pd.Series) -> Dict:
        """计算MACD指标"""
        ema_12 = self._calculate_ema(prices, 12)
        ema_26 = self._calculate_ema(prices, 26)
        
        macd = ema_12 - ema_26
        macd_signal = macd.ewm(span=9).mean()
        macd_histogram = macd - macd_signal
        
        return {
            'macd': macd,
            'macd_signal': macd_signal,
            'macd_histogram': macd_histogram
        }
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: float = 2) -> Dict:
        """计算布林线"""
        sma = self._calculate_sma(prices, period)
        std = prices.rolling(window=period).std()
        
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        
        # 计算价格在布林线中的位置
        bb_position = (prices - lower_band) / (upper_band - lower_band) * 100
        
        return {
            'bb_upper': upper_band,
            'bb_middle': sma,
            'bb_lower': lower_band,
            'bb_position': bb_position
        }
    
    def _calculate_atr(self, highs: pd.Series, lows: pd.Series, closes: pd.Series, period: int = 14) -> pd.Series:
        """计算平均真实波幅"""
        high_low = highs - lows
        high_close = np.abs(highs - closes.shift())
        low_close = np.abs(lows - closes.shift())
        
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        
        atr = true_range.rolling(window=period).mean()
        return atr
    
    def _calculate_volume_indicators(self, prices: pd.Series, volumes: pd.Series) -> Dict:
        """计算成交量指标"""
        # OBV (On Balance Volume)
        price_change = prices.diff()
        obv = (volumes * np.sign(price_change)).cumsum()
        
        # 成交量移动平均
        volume_ma = volumes.rolling(window=20).mean()
        
        # 成交量比率
        volume_ratio = volumes / volume_ma
        
        return {
            'obv': obv,
            'volume_ma': volume_ma,
            'volume_ratio': volume_ratio
        }
    
    def _calculate_trend_indicators(self) -> Dict:
        """计算趋势指标"""
        ema_12 = self.indicators['ema_12']
        ema_26 = self.indicators['ema_26']
        
        # 趋势方向
        trend_direction = np.where(ema_12 > ema_26, 1, -1)  # 1=上升, -1=下降
        
        # 趋势强度 (基于EMA差值)
        trend_strength = abs(ema_12 - ema_26) / ema_26 * 100
        
        return {
            'trend_direction': trend_direction,
            'trend_strength': trend_strength
        }
    
    def _calculate_weekly_trend(self) -> Dict:
        """计算长期趋势 (基于周K数据)"""
        if self.weekly_data is None or len(self.weekly_data) < 12:
            return {'weekly_trend': 'UNKNOWN'}
        
        weekly_prices = self.weekly_data['close']
        weekly_ema_12 = self._calculate_ema(weekly_prices, 12)
        
        # 长期趋势判断
        if len(weekly_ema_12) >= 2:
            if weekly_ema_12.iloc[-1] > weekly_ema_12.iloc[-2]:
                weekly_trend = 'UP'
            else:
                weekly_trend = 'DOWN'
        else:
            weekly_trend = 'UNKNOWN'
        
        return {
            'weekly_trend': weekly_trend,
            'weekly_ema_12': weekly_ema_12
        }
    
    def get_current_signals(self) -> Dict:
        """获取当前技术信号 - 基于日K数据"""
        if not self.indicators:
            self.calculate_all_indicators()
        
        # 获取最新值
        latest_idx = -1
        
        current_price = self.daily_data['close'].iloc[latest_idx]
        current_rsi = self.indicators['rsi'].iloc[latest_idx]
        current_macd = self.indicators['macd'].iloc[latest_idx]
        current_macd_signal = self.indicators['macd_signal'].iloc[latest_idx]
        current_ema_12 = self.indicators['ema_12'].iloc[latest_idx]
        current_ema_26 = self.indicators['ema_26'].iloc[latest_idx]
        current_bb_position = self.indicators['bb_position'].iloc[latest_idx]
        
        # 信号判断
        signals = {
            'price': current_price,
            'rsi': current_rsi,
            'rsi_signal': self._get_rsi_signal(current_rsi),
            'macd': current_macd,
            'macd_signal': current_macd_signal,
            'macd_trend': 'BULLISH' if current_macd > current_macd_signal else 'BEARISH',
            'ema_trend': 'UP' if current_ema_12 > current_ema_26 else 'DOWN',
            'bb_position': current_bb_position,
            'bb_signal': self._get_bb_signal(current_bb_position),
            'data_source': '日K数据 (统一标准)'
        }
        
        # 综合信号
        signals['overall_signal'] = self._get_overall_signal(signals)
        
        return signals
    
    def _get_rsi_signal(self, rsi: float) -> str:
        """RSI信号判断"""
        if rsi > 70:
            return 'OVERBOUGHT'
        elif rsi < 30:
            return 'OVERSOLD'
        else:
            return 'NEUTRAL'
    
    def _get_bb_signal(self, bb_position: float) -> str:
        """布林线信号判断"""
        if bb_position > 80:
            return 'NEAR_UPPER'
        elif bb_position < 20:
            return 'NEAR_LOWER'
        else:
            return 'MIDDLE_RANGE'
    
    def _get_overall_signal(self, signals: Dict) -> str:
        """综合信号判断"""
        bullish_count = 0
        bearish_count = 0
        
        # RSI信号
        if signals['rsi_signal'] == 'OVERSOLD':
            bullish_count += 1
        elif signals['rsi_signal'] == 'OVERBOUGHT':
            bearish_count += 1
        
        # MACD信号
        if signals['macd_trend'] == 'BULLISH':
            bullish_count += 1
        else:
            bearish_count += 1
        
        # EMA趋势
        if signals['ema_trend'] == 'UP':
            bullish_count += 1
        else:
            bearish_count += 1
        
        # 布林线信号
        if signals['bb_signal'] == 'NEAR_LOWER':
            bullish_count += 1
        elif signals['bb_signal'] == 'NEAR_UPPER':
            bearish_count += 1
        
        # 综合判断
        if bullish_count > bearish_count:
            return 'BULLISH'
        elif bearish_count > bullish_count:
            return 'BEARISH'
        else:
            return 'NEUTRAL'
    
    def get_support_resistance_levels(self) -> Dict:
        """计算支撑阻力位 - 基于日K数据"""
        prices = self.daily_data['close']
        highs = self.daily_data['high']
        lows = self.daily_data['low']
        
        # 近期高低点
        recent_high = highs.tail(20).max()
        recent_low = lows.tail(20).min()
        
        # 长期高低点
        long_term_high = highs.tail(60).max()
        long_term_low = lows.tail(60).min()
        
        # 当前价格
        current_price = prices.iloc[-1]
        
        return {
            'current_price': current_price,
            'recent_resistance': recent_high,
            'recent_support': recent_low,
            'long_term_resistance': long_term_high,
            'long_term_support': long_term_low,
            'resistance_distance': (recent_high - current_price) / current_price * 100,
            'support_distance': (current_price - recent_low) / current_price * 100,
            'data_source': '日K数据 (统一标准)'
        }

    def get_data_summary(self) -> Dict:
        """获取数据使用摘要"""
        return {
            'primary_data_source': '日K数据',
            'secondary_data_source': '周K数据 (长期趋势)',
            'data_period': f"{self.daily_data['datetime'].min().date()} 到 {self.daily_data['datetime'].max().date()}",
            'total_trading_days': len(self.daily_data),
            'calculation_standard': '统一基于日K数据计算所有技术指标',
            'data_consistency': '已消除数据粒度混用问题'
        }
