"""
价格更新服务

提供饰品价格的定时更新功能，从SteamDT API获取最新价格并保存到现有的平台价格表。
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.config.database import get_db_session
from src.cs2_investment.dao.platform_price_dao import PlatformPriceDAO
from src.cs2_investment.dao.item_dao import ItemDAO
from src.cs2_investment.services.steamdt_api_client import SteamDTAPIClient, ItemPriceResponse
from src.cs2_investment.services.arbitrage_calculator import ArbitrageCalculator
from src.cs2_investment.models.platform_price import PlatformPrice


class PriceUpdateService:
    """价格更新服务"""
    
    def __init__(self, api_key: str):
        """
        初始化价格更新服务

        Args:
            api_key: SteamDT API密钥
        """
        self.api_client = SteamDTAPIClient(api_key)
        self.logger = logging.getLogger(__name__)

        # 初始化搬砖比例计算器
        self.arbitrage_calculator = ArbitrageCalculator()

        # 更新配置
        self.batch_size = 100  # 批量处理大小
        self.max_concurrent = 5  # 最大并发数
        self.update_interval_hours = 1  # 更新间隔（小时）
        
    async def update_all_item_prices(self) -> Dict[str, Any]:
        """
        更新所有饰品价格
        
        Returns:
            Dict[str, Any]: 更新结果统计
        """
        start_time = datetime.now()
        self.logger.info("开始更新所有饰品价格")
        
        try:
            # 获取所有需要更新的饰品
            item_dao = ItemDAO()
            items = item_dao.get_all_active_items()

            if not items:
                self.logger.warning("没有找到需要更新的饰品")
                return {
                    'success': False,
                    'message': '没有找到需要更新的饰品',
                    'total_items': 0,
                    'updated_items': 0,
                    'failed_items': 0,
                    'duration_seconds': 0
                }

            # 提取市场哈希名称
            market_hash_names = [item['market_hash_name'] for item in items if item.get('market_hash_name')]

            self.logger.info(f"找到 {len(market_hash_names)} 个饰品需要更新价格")

            # 批量获取价格数据
            price_responses = await self.api_client.get_batch_prices(market_hash_names)

            # 保存价格数据
            platform_price_dao = PlatformPriceDAO()  # 不传session，让它自己管理
            update_stats = await self._save_price_responses(platform_price_dao, price_responses, items)

            # 清理过期数据
            deactivated_count = platform_price_dao.deactivate_old_prices(hours=24)

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            result = {
                'success': True,
                'message': '价格更新完成',
                'total_items': len(market_hash_names),
                'updated_items': update_stats['success_count'],
                'failed_items': update_stats['failed_count'],
                'total_platforms': update_stats['platform_count'],
                'deactivated_old_records': deactivated_count,
                'duration_seconds': duration,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat()
            }

            self.logger.info(f"价格更新完成: {result}")
            return result
                
        except Exception as e:
            self.logger.error(f"更新所有饰品价格失败: {e}")
            return {
                'success': False,
                'message': f'更新失败: {str(e)}',
                'total_items': 0,
                'updated_items': 0,
                'failed_items': 0,
                'duration_seconds': (datetime.now() - start_time).total_seconds()
            }
    
    async def update_specific_items(self, market_hash_names: List[str]) -> Dict[str, Any]:
        """
        更新指定饰品价格
        
        Args:
            market_hash_names: 市场哈希名称列表
            
        Returns:
            Dict[str, Any]: 更新结果统计
        """
        start_time = datetime.now()
        self.logger.info(f"开始更新指定饰品价格: {len(market_hash_names)} 个")
        
        try:
            # 批量获取价格数据
            price_responses = await self.api_client.get_batch_prices(market_hash_names)
            
            with get_db_session() as session:
                # 保存价格数据
                platform_price_dao = PlatformPriceDAO(session)
                update_stats = await self._save_price_responses(platform_price_dao, price_responses)
                
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                result = {
                    'success': True,
                    'message': '指定饰品价格更新完成',
                    'total_items': len(market_hash_names),
                    'updated_items': update_stats['success_count'],
                    'failed_items': update_stats['failed_count'],
                    'total_platforms': update_stats['platform_count'],
                    'duration_seconds': duration
                }
                
                self.logger.info(f"指定饰品价格更新完成: {result}")
                return result
                
        except Exception as e:
            self.logger.error(f"更新指定饰品价格失败: {e}")
            return {
                'success': False,
                'message': f'更新失败: {str(e)}',
                'total_items': len(market_hash_names),
                'updated_items': 0,
                'failed_items': len(market_hash_names),
                'duration_seconds': (datetime.now() - start_time).total_seconds()
            }
    
    async def _save_price_responses(self,
                                  platform_price_dao: PlatformPriceDAO,
                                  price_responses: Dict[str, ItemPriceResponse],
                                  items: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        保存价格响应数据

        Args:
            platform_price_dao: 平台价格DAO
            price_responses: 价格响应数据
            items: 饰品数据列表

        Returns:
            Dict[str, int]: 保存统计信息
        """
        success_count = 0
        failed_count = 0
        platform_count = 0

        # 创建市场哈希名称到饰品ID的映射
        item_id_mapping = {
            item['market_hash_name']: item['item_id']
            for item in items
            if item.get('market_hash_name') and item.get('item_id')
        }
        
        for market_hash_name, response in price_responses.items():
            try:
                if not response.success:
                    self.logger.warning(f"获取价格失败: {market_hash_name}, 错误: {response.error_message}")
                    failed_count += 1
                    continue
                
                # 获取饰品ID
                item_id = item_id_mapping.get(market_hash_name)
                if not item_id:
                    self.logger.warning(f"未找到饰品ID: {market_hash_name}")
                    failed_count += 1
                    continue
                
                # 保存每个平台的价格数据
                item_platform_count = 0
                for platform_price in response.platform_prices:
                    try:
                        # 确定数据源：价格更新服务使用的都是SteamDT API数据
                        data_source = 'steamdt'

                        # 标准化平台名称：确保Steam平台统一为大写STEAM
                        platform_name = platform_price.platform
                        if platform_name.upper() == 'STEAM':
                            platform_name = 'STEAM'

                        platform_price_dao.save_platform_price(
                            item_id=item_id,
                            market_hash_name=market_hash_name,
                            platform=platform_name,  # 使用标准化后的平台名称
                            platform_item_id=platform_price.platform_item_id,
                            sell_price=platform_price.sell_price,
                            sell_count=platform_price.sell_count,
                            bidding_price=platform_price.bidding_price,
                            bidding_count=platform_price.bidding_count,
                            steamdt_update_time=platform_price.update_time,
                            query_time=response.query_time,
                            data_source=data_source  # 明确设置数据源
                        )
                        item_platform_count += 1
                    except Exception as e:
                        self.logger.error(f"保存平台价格失败: {market_hash_name} - {platform_price.platform}, 错误: {e}")
                        continue
                
                if item_platform_count > 0:
                    success_count += 1
                    platform_count += item_platform_count

                    # 计算并更新搬砖比例
                    try:
                        self.arbitrage_calculator.update_item_arbitrage_ratio(item_id)
                        self.logger.debug(f"更新搬砖比例成功: {market_hash_name}")
                    except Exception as e:
                        self.logger.error(f"更新搬砖比例失败: {market_hash_name} - {e}")
                else:
                    failed_count += 1
                    
            except Exception as e:
                self.logger.error(f"处理价格响应失败: {market_hash_name}, 错误: {e}")
                failed_count += 1
                continue
        
        return {
            'success_count': success_count,
            'failed_count': failed_count,
            'platform_count': platform_count
        }
    
    async def get_update_status(self) -> Dict[str, Any]:
        """
        获取更新状态
        
        Returns:
            Dict[str, Any]: 更新状态信息
        """
        try:
            with get_db_session() as session:
                platform_price_dao = PlatformPriceDAO(session)
                
                # 获取最近更新时间
                from sqlalchemy import func
                latest_update = session.query(
                    func.max(PlatformPrice.query_time)
                ).filter(
                    PlatformPrice.is_active == True
                ).scalar()
                
                # 获取平台统计
                platforms = platform_price_dao.get_all_platforms()
                platform_stats = []
                
                for platform in platforms:
                    stats = platform_price_dao.get_platform_statistics(platform, days=1)
                    platform_stats.append(stats)
                
                # 计算下次更新时间
                next_update = None
                if latest_update:
                    next_update = latest_update + timedelta(hours=self.update_interval_hours)
                
                return {
                    'latest_update': latest_update.isoformat() if latest_update else None,
                    'next_update': next_update.isoformat() if next_update else None,
                    'update_interval_hours': self.update_interval_hours,
                    'total_platforms': len(platforms),
                    'platform_stats': platform_stats,
                    'api_status': await self.api_client.test_connection()
                }
                
        except Exception as e:
            self.logger.error(f"获取更新状态失败: {e}")
            return {
                'error': str(e),
                'api_status': False
            }
    
    async def test_api_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            bool: 连接是否成功
        """
        return await self.api_client.test_connection()
    
    def cleanup_old_data(self, days: int = 90) -> int:
        """
        清理旧数据
        
        Args:
            days: 保留天数
            
        Returns:
            int: 删除的记录数
        """
        try:
            with get_db_session() as session:
                platform_price_dao = PlatformPriceDAO(session)
                deleted_count = platform_price_dao.cleanup_old_data(days)
                self.logger.info(f"清理了 {deleted_count} 条旧数据")
                return deleted_count
        except Exception as e:
            self.logger.error(f"清理旧数据失败: {e}")
            return 0
