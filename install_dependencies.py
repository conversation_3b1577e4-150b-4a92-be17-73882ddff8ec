#!/usr/bin/env python3
"""
使用国内镜像安装项目依赖
"""

import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🔄 {description}...")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True, encoding='utf-8')
        print(f"✅ {description} 成功完成")
        if result.stdout:
            print("输出:", result.stdout.strip())
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败")
        print("错误:", e.stderr.strip() if e.stderr else str(e))
        return False

def main():
    """主函数"""
    print("🚀 开始安装 CS2 饰品投资分析系统依赖包")
    print("使用清华大学 PyPI 镜像源")
    print("=" * 60)
    
    # 检查 requirements.txt 是否存在
    if not os.path.exists("requirements.txt"):
        print("❌ 未找到 requirements.txt 文件")
        return 1
    
    # 升级 pip
    if not run_command(
        "python -m pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple",
        "升级 pip"
    ):
        print("⚠️  pip 升级失败，继续安装依赖...")
    
    # 安装关键依赖
    critical_packages = [
        "streamlit==1.48.1",
        "pandas==2.3.1", 
        "numpy==2.3.2",
        "plotly==5.17.0",
        "streamlit-aggrid==0.3.4.post3"
    ]
    
    print(f"\n📦 安装关键依赖包...")
    for package in critical_packages:
        if not run_command(
            f"pip install {package} -i https://pypi.tuna.tsinghua.edu.cn/simple",
            f"安装 {package}"
        ):
            print(f"❌ 关键依赖 {package} 安装失败")
            return 1
    
    # 安装所有依赖
    print(f"\n📋 从 requirements.txt 安装所有依赖...")
    if not run_command(
        "pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple",
        "安装所有依赖"
    ):
        print("❌ 依赖安装失败")
        return 1
    
    # 验证安装
    print(f"\n🔍 验证安装结果...")
    if os.path.exists("verify_dependencies.py"):
        if not run_command("python verify_dependencies.py", "验证依赖"):
            print("⚠️  依赖验证失败，但可能仍然可以正常使用")
    
    print("\n" + "=" * 60)
    print("✅ 依赖安装完成！")
    print("\n📝 下一步:")
    print("1. 运行 'streamlit run start_streamlit_app.py' 启动应用")
    print("2. 访问 http://localhost:8501 查看应用")
    print("3. 测试收藏页面功能")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
