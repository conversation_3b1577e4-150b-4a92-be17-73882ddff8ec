"""
数据格式验证工具

确保API抓取和playwright抓取的JSON输出格式完全一致，
提供格式对比和验证功能。
"""

import json
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime
from dataclasses import asdict
from loguru import logger

from .data_models import ScrapingResult, TrendData, KlineData, ItemInfo


class DataFormatValidator:
    """数据格式验证器
    
    提供ScrapingResult格式验证、对比和一致性检查功能。
    确保不同抓取方式产生的数据格式完全一致。
    """
    
    def __init__(self):
        """初始化验证器"""
        self.logger = logger.bind(validator="DataFormatValidator")
        
        # 定义期望的字段结构
        self.expected_scraping_result_fields = {
            'success': bool,
            'item_info': (type(None), ItemInfo),
            'trend_data': (type(None), TrendData),
            'trend_data_3m': (type(None), TrendData),
            'trend_data_6m': (type(None), TrendData),
            'daily_kline': (type(None), KlineData),
            'daily_kline_1': (type(None), KlineData),
            'daily_kline_2': (type(None), KlineData),
            'hourly_kline': (type(None), KlineData),
            'weekly_kline': (type(None), KlineData),
            'error_message': (type(None), str),
            'collected_at': (type(None), datetime)
        }
        
        self.expected_trend_data_fields = {
            'item_id': str,
            'platform': str,
            'time_range': str,
            'data_points': list,
            'collected_at': datetime,
            'raw_data': (type(None), list, dict)
        }
        
        self.expected_kline_data_fields = {
            'item_id': str,
            'kline_type': str,
            'platform': str,
            'data_points': list,
            'collected_at': datetime,
            'raw_data': (type(None), list, dict)
        }
    
    def validate_scraping_result(self, result: ScrapingResult) -> Dict[str, Any]:
        """验证ScrapingResult格式
        
        Args:
            result: 要验证的抓取结果
            
        Returns:
            Dict: 验证报告
        """
        report = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'field_checks': {},
            'data_summary': {}
        }
        
        try:
            # 检查基本字段
            self._check_fields(result, self.expected_scraping_result_fields, report, 'ScrapingResult')
            
            # 检查嵌套对象
            if result.trend_data_3m:
                self._validate_trend_data(result.trend_data_3m, report, '3m趋势数据')
            
            if result.trend_data_6m:
                self._validate_trend_data(result.trend_data_6m, report, '6m趋势数据')
            
            # 检查K线数据
            kline_data_map = {
                'hourly_kline': '时K数据',
                'daily_kline_1': '日K数据1',
                'daily_kline_2': '日K数据2',
                'weekly_kline': '周K数据'
            }
            
            for attr_name, description in kline_data_map.items():
                kline_data = getattr(result, attr_name, None)
                if kline_data:
                    self._validate_kline_data(kline_data, report, description)
            
            # 生成数据摘要
            self._generate_data_summary(result, report)
            
            # 设置总体验证状态
            report['valid'] = len(report['errors']) == 0
            
        except Exception as e:
            report['valid'] = False
            report['errors'].append(f"验证过程中发生异常: {e}")
            self.logger.error(f"验证ScrapingResult时发生异常: {e}")
        
        return report
    
    def _check_fields(self, obj: Any, expected_fields: Dict[str, Any], report: Dict, context: str) -> None:
        """检查对象字段"""
        for field_name, expected_type in expected_fields.items():
            if hasattr(obj, field_name):
                field_value = getattr(obj, field_name)
                
                # 检查类型
                if isinstance(expected_type, tuple):
                    # 多种可能的类型
                    if not isinstance(field_value, expected_type):
                        report['errors'].append(f"{context}.{field_name}: 类型错误，期望 {expected_type}，实际 {type(field_value)}")
                else:
                    # 单一类型
                    if not isinstance(field_value, expected_type):
                        report['errors'].append(f"{context}.{field_name}: 类型错误，期望 {expected_type}，实际 {type(field_value)}")
                
                report['field_checks'][f"{context}.{field_name}"] = {
                    'exists': True,
                    'type': str(type(field_value)),
                    'value_preview': self._get_value_preview(field_value)
                }
            else:
                report['warnings'].append(f"{context}.{field_name}: 字段缺失")
                report['field_checks'][f"{context}.{field_name}"] = {
                    'exists': False,
                    'type': None,
                    'value_preview': None
                }
    
    def _validate_trend_data(self, trend_data: TrendData, report: Dict, context: str) -> None:
        """验证趋势数据"""
        self._check_fields(trend_data, self.expected_trend_data_fields, report, context)
        
        # 检查数据点数量
        if hasattr(trend_data, 'data_points') and trend_data.data_points:
            data_count = len(trend_data.data_points)
            if data_count == 0:
                report['warnings'].append(f"{context}: 数据点为空")
            elif data_count < 10:
                report['warnings'].append(f"{context}: 数据点数量较少 ({data_count})")
    
    def _validate_kline_data(self, kline_data: KlineData, report: Dict, context: str) -> None:
        """验证K线数据"""
        self._check_fields(kline_data, self.expected_kline_data_fields, report, context)
        
        # 检查数据点数量
        if hasattr(kline_data, 'data_points') and kline_data.data_points:
            data_count = len(kline_data.data_points)
            if data_count == 0:
                report['warnings'].append(f"{context}: 数据点为空")
            elif data_count < 5:
                report['warnings'].append(f"{context}: 数据点数量较少 ({data_count})")
    
    def _generate_data_summary(self, result: ScrapingResult, report: Dict) -> None:
        """生成数据摘要"""
        summary = {
            'success': result.success,
            'has_trend_data_3m': result.trend_data_3m is not None,
            'has_trend_data_6m': result.trend_data_6m is not None,
            'has_hourly_kline': result.hourly_kline is not None,
            'has_daily_kline_1': result.daily_kline_1 is not None,
            'has_daily_kline_2': result.daily_kline_2 is not None,
            'has_weekly_kline': result.weekly_kline is not None,
            'has_error_message': result.error_message is not None,
            'collected_at': result.collected_at.isoformat() if result.collected_at else None
        }
        
        # 统计数据点数量
        if result.trend_data_3m and hasattr(result.trend_data_3m, 'data_points'):
            summary['trend_3m_data_points'] = len(result.trend_data_3m.data_points or [])
        
        if result.trend_data_6m and hasattr(result.trend_data_6m, 'data_points'):
            summary['trend_6m_data_points'] = len(result.trend_data_6m.data_points or [])
        
        report['data_summary'] = summary
    
    def _get_value_preview(self, value: Any) -> str:
        """获取值的预览字符串"""
        if value is None:
            return "None"
        elif isinstance(value, (str, int, float, bool)):
            return str(value)[:50]
        elif isinstance(value, (list, tuple)):
            return f"[{len(value)} items]"
        elif isinstance(value, dict):
            return f"{{dict with {len(value)} keys}}"
        elif isinstance(value, datetime):
            return value.isoformat()
        else:
            return f"<{type(value).__name__}>"

    def compare_scraping_results(self, result1: ScrapingResult, result2: ScrapingResult,
                                label1: str = "结果1", label2: str = "结果2") -> Dict[str, Any]:
        """对比两个抓取结果的格式一致性

        Args:
            result1: 第一个抓取结果
            result2: 第二个抓取结果
            label1: 第一个结果的标签
            label2: 第二个结果的标签

        Returns:
            Dict: 对比报告
        """
        report = {
            'consistent': True,
            'differences': [],
            'field_comparison': {},
            'structure_comparison': {},
            'data_comparison': {}
        }

        try:
            # 验证两个结果
            report1 = self.validate_scraping_result(result1)
            report2 = self.validate_scraping_result(result2)

            # 比较字段存在性
            self._compare_field_existence(result1, result2, report, label1, label2)

            # 比较数据结构
            self._compare_data_structure(result1, result2, report, label1, label2)

            # 比较数据内容（如果都成功）
            if result1.success and result2.success:
                self._compare_data_content(result1, result2, report, label1, label2)

            # 设置总体一致性状态
            report['consistent'] = len(report['differences']) == 0

        except Exception as e:
            report['consistent'] = False
            report['differences'].append(f"对比过程中发生异常: {e}")
            self.logger.error(f"对比ScrapingResult时发生异常: {e}")

        return report

    def _compare_field_existence(self, result1: ScrapingResult, result2: ScrapingResult,
                                report: Dict, label1: str, label2: str) -> None:
        """比较字段存在性"""
        for field_name in self.expected_scraping_result_fields.keys():
            has_field1 = hasattr(result1, field_name) and getattr(result1, field_name) is not None
            has_field2 = hasattr(result2, field_name) and getattr(result2, field_name) is not None

            if has_field1 != has_field2:
                report['differences'].append(
                    f"字段 {field_name}: {label1}={'有' if has_field1 else '无'}, "
                    f"{label2}={'有' if has_field2 else '无'}"
                )

            report['field_comparison'][field_name] = {
                f'{label1}_exists': has_field1,
                f'{label2}_exists': has_field2,
                'consistent': has_field1 == has_field2
            }

    def _compare_data_structure(self, result1: ScrapingResult, result2: ScrapingResult,
                               report: Dict, label1: str, label2: str) -> None:
        """比较数据结构"""
        structure_fields = ['trend_data_3m', 'trend_data_6m', 'hourly_kline',
                           'daily_kline_1', 'daily_kline_2', 'weekly_kline']

        for field_name in structure_fields:
            data1 = getattr(result1, field_name, None)
            data2 = getattr(result2, field_name, None)

            if data1 and data2:
                # 比较数据点数量
                count1 = len(getattr(data1, 'data_points', []))
                count2 = len(getattr(data2, 'data_points', []))

                if abs(count1 - count2) > 5:  # 允许5个数据点的差异
                    report['differences'].append(
                        f"{field_name} 数据点数量差异较大: {label1}={count1}, {label2}={count2}"
                    )

                report['structure_comparison'][field_name] = {
                    f'{label1}_count': count1,
                    f'{label2}_count': count2,
                    'count_diff': abs(count1 - count2)
                }

    def _compare_data_content(self, result1: ScrapingResult, result2: ScrapingResult,
                             report: Dict, label1: str, label2: str) -> None:
        """比较数据内容"""
        # 比较基本字段
        if result1.success != result2.success:
            report['differences'].append(f"成功状态不一致: {label1}={result1.success}, {label2}={result2.success}")

        # 比较时间戳（允许一定差异）
        if result1.collected_at and result2.collected_at:
            time_diff = abs((result1.collected_at - result2.collected_at).total_seconds())
            if time_diff > 300:  # 超过5分钟认为不一致
                report['differences'].append(f"收集时间差异过大: {time_diff}秒")

        report['data_comparison'] = {
            'success_consistent': result1.success == result2.success,
            'time_diff_seconds': time_diff if result1.collected_at and result2.collected_at else None
        }

    def validate_json_serialization(self, result: ScrapingResult) -> Dict[str, Any]:
        """验证JSON序列化格式

        Args:
            result: 要验证的抓取结果

        Returns:
            Dict: 序列化验证报告
        """
        report = {
            'serializable': True,
            'errors': [],
            'warnings': [],
            'json_size': 0,
            'serialization_time': 0
        }

        try:
            start_time = datetime.now()

            # 尝试序列化为JSON
            json_data = self._convert_to_json_dict(result)
            json_str = json.dumps(json_data, ensure_ascii=False, indent=2)

            end_time = datetime.now()

            report['json_size'] = len(json_str.encode('utf-8'))
            report['serialization_time'] = (end_time - start_time).total_seconds()

            # 验证反序列化
            parsed_data = json.loads(json_str)

            # 检查关键字段
            required_keys = ['success', 'collected_at']
            for key in required_keys:
                if key not in parsed_data:
                    report['errors'].append(f"JSON中缺少必需字段: {key}")

            # 检查数据完整性
            if parsed_data.get('success') and not any([
                parsed_data.get('trend_data_3m'),
                parsed_data.get('trend_data_6m'),
                parsed_data.get('hourly_kline'),
                parsed_data.get('daily_kline_1'),
                parsed_data.get('weekly_kline')
            ]):
                report['warnings'].append("成功的抓取结果但没有任何数据")

            report['serializable'] = len(report['errors']) == 0

        except Exception as e:
            report['serializable'] = False
            report['errors'].append(f"JSON序列化失败: {e}")
            self.logger.error(f"JSON序列化验证失败: {e}")

        return report

    def _convert_to_json_dict(self, result: ScrapingResult) -> Dict[str, Any]:
        """将ScrapingResult转换为JSON字典"""
        json_dict = {
            'success': result.success,
            'collected_at': result.collected_at.isoformat() if result.collected_at else None,
            'error_message': result.error_message
        }

        # 转换趋势数据
        if result.trend_data_3m:
            json_dict['trend_data_3m'] = self._trend_data_to_dict(result.trend_data_3m)

        if result.trend_data_6m:
            json_dict['trend_data_6m'] = self._trend_data_to_dict(result.trend_data_6m)

        # 转换K线数据
        kline_fields = ['hourly_kline', 'daily_kline_1', 'daily_kline_2', 'weekly_kline']
        for field in kline_fields:
            kline_data = getattr(result, field, None)
            if kline_data:
                json_dict[field] = self._kline_data_to_dict(kline_data)

        return json_dict

    def _trend_data_to_dict(self, trend_data: TrendData) -> Dict[str, Any]:
        """将TrendData转换为字典"""
        # 处理数据点序列化
        data_points = getattr(trend_data, 'data_points', [])
        serializable_points = []
        for point in data_points:
            if hasattr(point, '__dict__'):
                # 如果是对象，转换为字典
                serializable_points.append(point.__dict__)
            else:
                # 如果已经是字典或基本类型，直接使用
                serializable_points.append(point)

        return {
            'item_id': trend_data.item_id,
            'platform': getattr(trend_data, 'platform', 'ALL'),
            'time_range': getattr(trend_data, 'time_range', ''),
            'data_points': serializable_points,
            'collected_at': trend_data.collected_at.isoformat() if trend_data.collected_at else None,
            'raw_data': getattr(trend_data, 'raw_data', None)
        }

    def _kline_data_to_dict(self, kline_data: KlineData) -> Dict[str, Any]:
        """将KlineData转换为字典"""
        # 处理数据点序列化
        data_points = getattr(kline_data, 'data_points', [])
        serializable_points = []
        for point in data_points:
            if hasattr(point, '__dict__'):
                # 如果是对象，转换为字典
                serializable_points.append(point.__dict__)
            else:
                # 如果已经是字典或基本类型，直接使用
                serializable_points.append(point)

        return {
            'item_id': kline_data.item_id,
            'kline_type': kline_data.kline_type,
            'platform': getattr(kline_data, 'platform', 'ALL'),
            'data_points': serializable_points,
            'collected_at': kline_data.collected_at.isoformat() if kline_data.collected_at else None,
            'raw_data': getattr(kline_data, 'raw_data', None)
        }

    def generate_validation_report(self, result: ScrapingResult, scraper_type: str = "unknown") -> Dict[str, Any]:
        """生成完整的验证报告

        Args:
            result: 抓取结果
            scraper_type: 抓取器类型（playwright/api）

        Returns:
            Dict: 完整验证报告
        """
        report = {
            'scraper_type': scraper_type,
            'timestamp': datetime.now().isoformat(),
            'validation_summary': {
                'overall_valid': True,
                'total_errors': 0,
                'total_warnings': 0
            }
        }

        # 基本格式验证
        format_report = self.validate_scraping_result(result)
        report['format_validation'] = format_report

        # JSON序列化验证
        json_report = self.validate_json_serialization(result)
        report['json_validation'] = json_report

        # 汇总统计
        total_errors = len(format_report['errors']) + len(json_report['errors'])
        total_warnings = len(format_report['warnings']) + len(json_report['warnings'])

        report['validation_summary'].update({
            'overall_valid': format_report['valid'] and json_report['serializable'],
            'total_errors': total_errors,
            'total_warnings': total_warnings,
            'format_valid': format_report['valid'],
            'json_serializable': json_report['serializable']
        })

        return report

    def print_validation_report(self, report: Dict[str, Any]) -> None:
        """打印验证报告

        Args:
            report: 验证报告
        """
        print(f"\n📋 数据格式验证报告 - {report.get('scraper_type', 'unknown')}")
        print("=" * 50)

        summary = report.get('validation_summary', {})
        if summary.get('overall_valid'):
            print("✅ 总体验证: 通过")
        else:
            print("❌ 总体验证: 失败")

        print(f"📊 统计信息:")
        print(f"   - 错误数量: {summary.get('total_errors', 0)}")
        print(f"   - 警告数量: {summary.get('total_warnings', 0)}")
        print(f"   - 格式验证: {'✅' if summary.get('format_valid') else '❌'}")
        print(f"   - JSON序列化: {'✅' if summary.get('json_serializable') else '❌'}")

        # 显示错误和警告
        format_report = report.get('format_validation', {})
        if format_report.get('errors'):
            print(f"\n❌ 格式错误:")
            for error in format_report['errors']:
                print(f"   - {error}")

        if format_report.get('warnings'):
            print(f"\n⚠️ 格式警告:")
            for warning in format_report['warnings']:
                print(f"   - {warning}")

        json_report = report.get('json_validation', {})
        if json_report.get('errors'):
            print(f"\n❌ JSON错误:")
            for error in json_report['errors']:
                print(f"   - {error}")

        # 显示数据摘要
        data_summary = format_report.get('data_summary', {})
        if data_summary:
            print(f"\n📈 数据摘要:")
            print(f"   - 成功状态: {data_summary.get('success')}")
            print(f"   - 3个月趋势: {'✅' if data_summary.get('has_trend_data_3m') else '❌'}")
            print(f"   - 6个月趋势: {'✅' if data_summary.get('has_trend_data_6m') else '❌'}")
            print(f"   - 时K数据: {'✅' if data_summary.get('has_hourly_kline') else '❌'}")
            print(f"   - 日K数据1: {'✅' if data_summary.get('has_daily_kline_1') else '❌'}")
            print(f"   - 日K数据2: {'✅' if data_summary.get('has_daily_kline_2') else '❌'}")
            print(f"   - 周K数据: {'✅' if data_summary.get('has_weekly_kline') else '❌'}")

    def print_comparison_report(self, report: Dict[str, Any]) -> None:
        """打印对比报告

        Args:
            report: 对比报告
        """
        print(f"\n🔍 数据格式对比报告")
        print("=" * 50)

        if report.get('consistent'):
            print("✅ 格式一致性: 通过")
        else:
            print("❌ 格式一致性: 失败")

        differences = report.get('differences', [])
        if differences:
            print(f"\n❌ 发现 {len(differences)} 个差异:")
            for i, diff in enumerate(differences, 1):
                print(f"   {i}. {diff}")

        # 显示字段对比
        field_comparison = report.get('field_comparison', {})
        if field_comparison:
            print(f"\n📋 字段对比:")
            for field, comparison in field_comparison.items():
                status = "✅" if comparison.get('consistent') else "❌"
                print(f"   {status} {field}")

        # 显示结构对比
        structure_comparison = report.get('structure_comparison', {})
        if structure_comparison:
            print(f"\n📊 数据结构对比:")
            for field, comparison in structure_comparison.items():
                count_diff = comparison.get('count_diff', 0)
                status = "✅" if count_diff <= 5 else "⚠️"
                print(f"   {status} {field}: 数据点差异 {count_diff}")


# 便利函数
def validate_scraping_result(result: ScrapingResult, scraper_type: str = "unknown") -> Dict[str, Any]:
    """便利函数：验证抓取结果

    Args:
        result: 抓取结果
        scraper_type: 抓取器类型

    Returns:
        Dict: 验证报告
    """
    validator = DataFormatValidator()
    return validator.generate_validation_report(result, scraper_type)


def compare_scraping_results(result1: ScrapingResult, result2: ScrapingResult,
                           label1: str = "Playwright", label2: str = "API") -> Dict[str, Any]:
    """便利函数：对比两个抓取结果

    Args:
        result1: 第一个抓取结果
        result2: 第二个抓取结果
        label1: 第一个结果的标签
        label2: 第二个结果的标签

    Returns:
        Dict: 对比报告
    """
    validator = DataFormatValidator()
    return validator.compare_scraping_results(result1, result2, label1, label2)


def print_validation_summary(result: ScrapingResult, scraper_type: str = "unknown") -> None:
    """便利函数：打印验证摘要

    Args:
        result: 抓取结果
        scraper_type: 抓取器类型
    """
    validator = DataFormatValidator()
    report = validator.generate_validation_report(result, scraper_type)
    validator.print_validation_report(report)
