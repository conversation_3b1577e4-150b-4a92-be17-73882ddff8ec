#!/usr/bin/env python3
"""
分析任务API路由

提供常规分析和实时监控的API接口
"""

import asyncio
import logging
import uuid
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.api.models.requests import (
    RegularAnalysisRequest,
    BatchAnalysisRequest
)
from src.cs2_investment.api.models.responses import (
    AnalysisTaskResponse, 
    BatchTaskResponse
)
# 不再需要任务引擎和分析系统导入，直接使用 UnifiedAnalysisSystem

# 使用统一日志组件
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/analysis", tags=["分析任务"])

# 分析系统初始化已移除，直接在接口中使用 UnifiedAnalysisSystem


def generate_task_id() -> str:
    """生成任务ID"""
    return f"task_{uuid.uuid4()}"


def generate_batch_id() -> str:
    """生成批量任务ID"""
    return f"batch_{uuid.uuid4()}"


# 旧的任务执行函数已被TaskEngine替代


@router.post("/regular",
             response_model=AnalysisTaskResponse,
             summary="启动常规分析",
             description="对指定饰品进行完整的投资分析")
async def start_regular_analysis(
    request: RegularAnalysisRequest
):
    """
    启动常规分析任务
    
    - **item_id**: 饰品ID
    - **item_name**: 饰品名称
    - **item_url**: 饰品URL
    - **analysis_options**: 分析选项（可选）
    """
    try:
        # 直接调用 UnifiedAnalysisSystem 进行分析
        from src.cs2_investment.fx.unified_analysis_system import UnifiedAnalysisSystem

        logger.info(f"📊 开始常规分析: {request.item_name} ({request.item_id})")

        # 创建分析系统实例
        unified_system = UnifiedAnalysisSystem()

        # 运行完整分析
        result = await unified_system.run_complete_analysis(
            item_url=request.item_url,
            item_name=request.item_name
        )

        if result['success']:
            logger.info(f"✅ 常规分析完成: {request.item_name}")
            return AnalysisTaskResponse(
                task_id=f"direct_{request.item_id}_{int(datetime.now().timestamp())}",
                status="completed",
                message="分析已完成",
                created_at=datetime.now(),
                item_id=request.item_id,
                item_name=request.item_name,
                analysis_type="regular"
            )
        else:
            error_msg = result.get('error', '分析失败')
            logger.error(f"❌ 常规分析失败: {request.item_name} - {error_msg}")
            raise HTTPException(status_code=500, detail=f"分析失败: {error_msg}")
        
    except Exception as e:
        logger.error(f"创建常规分析任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建分析任务失败: {str(e)}")


# 实时分析接口已删除，统一使用常规分析接口


@router.post("/batch",
             response_model=BatchTaskResponse,
             summary="批量分析",
             description="批量处理多个饰品的分析任务")
async def start_batch_analysis(
    request: BatchAnalysisRequest
):
    """
    启动批量分析任务
    
    - **items**: 饰品列表
    - **analysis_type**: 分析类型（regular/realtime）
    - **priority**: 任务优先级（high/normal/low）
    """
    try:
        batch_id = generate_batch_id()
        tasks = []
        
        logger.info(f"📦 开始创建批量分析任务: {batch_id} - {len(request.items)} 个饰品")
        
        for item in request.items:
            # 创建分析日志记录
            log = analysis_log_dao.create_analysis_log(
                item_id=item.item_id,
                item_name=item.item_name,
                analysis_type=request.analysis_type,
                scheduled_date=datetime.now()
            )
            
            actual_task_id = str(log['id'])
            
            # 根据分析类型创建任务信息
            task_type = TaskType.REGULAR if request.analysis_type == "regular" else TaskType.REALTIME

            task_info = TaskInfo(
                task_id=log['id'],
                item_id=item.item_id,
                item_name=item.item_name,
                item_url=item.item_url,
                analysis_type=task_type
            )

            # 提交任务到执行引擎
            await task_engine.submit_task(task_info)
            
            # 添加到任务列表
            tasks.append(AnalysisTaskResponse(
                task_id=actual_task_id,
                status="pending",
                message=f"{request.analysis_type}分析任务已创建",
                created_at=datetime.now(),
                item_id=item.item_id,
                item_name=item.item_name,
                analysis_type=request.analysis_type
            ))
        
        logger.info(f"✅ 批量分析任务创建完成: {batch_id} - {len(tasks)} 个子任务")
        
        return BatchTaskResponse(
            batch_id=batch_id,
            total_items=len(request.items),
            tasks=tasks,
            message=f"批量{request.analysis_type}分析任务已创建",
            created_at=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"创建批量分析任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建批量任务失败: {str(e)}")
