#!/usr/bin/env python3
"""
SteamDT抓取服务启动脚本

独立启动FastAPI抓取服务端
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """启动API服务"""
    print("=" * 60)
    print("🚀 SteamDT抓取服务启动脚本")
    print("=" * 60)

    try:
        # 导入并启动FastAPI应用
        from src.cs2_investment.api.main import main as api_main

        print("📍 服务地址: http://localhost:8000")
        print("📖 API文档: http://localhost:8000/docs")
        print("🔍 健康检查: http://localhost:8000/health")
        print("📊 服务信息: http://localhost:8000/info")
        print("⏰ 调度器状态: http://localhost:8000/scheduler/status")
        print("")
        print("🎯 当前启用功能:")
        print("  ✅ 排行榜抓取调度器 - 定时抓取SteamDT排行榜数据")
        print("  ✅ 仅实时监控调度器 - 饰品实时数据抓取")
        print("  ✅ 智能投资分析调度器 - 完整投资分析业务流程")
        print("     📊 排行榜数据扫描 → 常规分析 → 投资筛选算法 → 推荐结果")
        print("")
        print("🚀 正在启动服务...")

        # 启动服务
        api_main()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所需依赖: pip install -r requirements.txt")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
