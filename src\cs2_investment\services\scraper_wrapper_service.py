#!/usr/bin/env python3
"""
爬虫包装服务 - 调用steamdt_scraper_final.py并处理结果
"""

import sys
import subprocess
import pymysql
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
from loguru import logger


class ScraperWrapperService:
    """爬虫包装服务 - 使用steamdt_scraper_final.py作为工具"""
    
    def __init__(self):
        self.scraper_path = Path(__file__).parent.parent.parent.parent / "steamdt_scraper_final.py"
        self.logger = logger.bind(service="ScraperWrapperService")
    
    def get_available_rankings(self) -> Dict[str, str]:
        """获取可用的榜单"""
        return {
            "price_up": "价格榜-上涨榜",
            "price_down": "价格榜-下跌榜", 
            "inventory": "在售数榜",
            "transaction_amount": "成交榜-成交额",
            "transaction_count": "成交榜-成交量",
            "popularity": "热度榜",
            "popularity_up": "热度上升榜"
        }
    
    def run_scraper(self, ranking_keys: Optional[List[str]] = None, target_count: int = 200) -> Dict[str, Any]:
        """运行steamdt_scraper_final.py"""
        try:
            # 构建命令
            cmd = [sys.executable, str(self.scraper_path)]
            
            if ranking_keys:
                cmd.extend(ranking_keys)
            
            self.logger.info(f"🚀 运行爬虫命令: {' '.join(cmd)}")
            
            # 运行爬虫 - 实时输出日志
            self.logger.info("🚀 开始运行爬虫，实时输出日志...")

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                cwd=str(self.scraper_path.parent),
                bufsize=1,
                universal_newlines=True
            )

            # 实时读取并输出日志
            output_lines = []
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    line = output.strip()
                    print(line)  # 实时输出到控制台
                    output_lines.append(line)

            return_code = process.poll()
            full_output = '\n'.join(output_lines)
            
            if return_code == 0:
                self.logger.info("✅ 爬虫运行成功")
                self.logger.info(f"输出长度: {len(full_output)} 字符")

                # steamdt_scraper_final.py现在直接保存到数据库，不需要处理CSV文件
                return {"success": True, "message": "数据已直接保存到数据库"}
            else:
                self.logger.error(f"❌ 爬虫运行失败，返回码: {return_code}")
                return {"success": False, "error": f"进程返回码: {return_code}"}
                
        except subprocess.TimeoutExpired:
            self.logger.error("❌ 爬虫运行超时")
            return {"success": False, "error": "运行超时"}
        except Exception as e:
            self.logger.error(f"❌ 运行爬虫异常: {e}")
            return {"success": False, "error": str(e)}
    
    def _process_csv_files(self) -> Dict[str, Any]:
        """处理steamdt_scraper_final.py生成的CSV文件"""
        try:
            # 查找生成的CSV文件 - 检查多个可能的位置
            search_paths = [
                Path(self.scraper_path.parent),  # 脚本目录
                Path(self.scraper_path.parent) / "steamdt_data",  # steamdt_data目录
                Path.cwd(),  # 当前工作目录
                Path.cwd() / "steamdt_data"  # 当前目录下的steamdt_data
            ]

            csv_files = []
            for search_path in search_paths:
                if search_path.exists():
                    csv_files.extend(list(search_path.glob("*.csv")))
                    self.logger.info(f"🔍 搜索路径: {search_path}, 找到CSV文件: {len(list(search_path.glob('*.csv')))}")

            if not csv_files:
                self.logger.warning("⚠️ 没有找到生成的CSV文件")
                # 显示搜索的路径
                for search_path in search_paths:
                    self.logger.info(f"   搜索路径: {search_path} (存在: {search_path.exists()})")
                return {"success": False, "error": "没有找到CSV文件"}
            
            total_imported = 0
            results = []
            
            for csv_file in csv_files:
                self.logger.info(f"📄 处理CSV文件: {csv_file.name}")
                
                # 读取CSV文件
                data = self._read_csv_file(csv_file)
                if data:
                    # 转换为数据库格式并保存
                    imported_count = self._save_to_database(data, csv_file.stem)
                    total_imported += imported_count
                    
                    results.append({
                        "file": csv_file.name,
                        "data_count": len(data),
                        "imported_count": imported_count
                    })
                    
                    self.logger.info(f"✅ {csv_file.name}: {len(data)}条数据, 导入{imported_count}条")
                
                # 删除处理过的CSV文件
                csv_file.unlink()
                self.logger.info(f"🗑️ 已删除CSV文件: {csv_file.name}")
            
            return {
                "success": True,
                "total_imported": total_imported,
                "files_processed": len(results),
                "results": results
            }
            
        except Exception as e:
            self.logger.error(f"❌ 处理CSV文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _read_csv_file(self, csv_file: Path) -> List[Dict]:
        """读取CSV文件"""
        try:
            data = []
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    data.append(row)
            return data
        except Exception as e:
            self.logger.error(f"❌ 读取CSV文件失败 {csv_file}: {e}")
            return []
    
    def _save_to_database(self, csv_data: List[Dict], data_source: str) -> int:
        """将CSV数据保存到数据库"""
        if not csv_data:
            return 0
        
        try:
            connection = pymysql.connect(**self.db_config)
            success_count = 0
            snapshot_time = datetime.now()
            
            with connection.cursor() as cursor:
                for row in csv_data:
                    try:
                        # 转换CSV行为数据库记录
                        item_record, snapshot_record = self._convert_csv_row(row, data_source, snapshot_time)
                        
                        if not item_record or not item_record.get("item_id"):
                            continue
                        
                        # 保存饰品信息
                        cursor.execute("""
                            INSERT INTO items (item_id, item_type, name, market_hash_name, 
                                             quality, rarity, exterior)
                            VALUES (%(item_id)s, %(item_type)s, %(name)s, %(market_hash_name)s,
                                    %(quality)s, %(rarity)s, %(exterior)s)
                            ON DUPLICATE KEY UPDATE
                            item_type = VALUES(item_type),
                            name = VALUES(name),
                            market_hash_name = VALUES(market_hash_name),
                            quality = VALUES(quality),
                            rarity = VALUES(rarity),
                            exterior = VALUES(exterior),
                            updated_at = CURRENT_TIMESTAMP
                        """, item_record)
                        
                        # 保存市场快照
                        if snapshot_record:
                            # 先删除同一数据源的旧记录
                            cursor.execute("""
                                DELETE FROM market_snapshots 
                                WHERE item_id = %s AND data_source = %s
                            """, (snapshot_record["item_id"], snapshot_record["data_source"]))
                            
                            # 插入新记录
                            cursor.execute("""
                                INSERT INTO market_snapshots (
                                    item_id, snapshot_time, data_source, current_price, 
                                    diff_7d, trans_count_7d, trans_amount_7d, hot_rank, sell_nums
                                ) VALUES (
                                    %(item_id)s, %(snapshot_time)s, %(data_source)s, %(current_price)s,
                                    %(diff_7d)s, %(trans_count_7d)s, %(trans_amount_7d)s, %(hot_rank)s, %(sell_nums)s
                                )
                            """, snapshot_record)
                        
                        success_count += 1
                        
                    except Exception as e:
                        self.logger.error(f"❌ 保存单条数据失败: {e}")
                        continue
                
                connection.commit()
            
            connection.close()
            return success_count
            
        except Exception as e:
            self.logger.error(f"❌ 保存数据到数据库失败: {e}")
            return 0
    
    def _convert_csv_row(self, row: Dict, data_source: str, snapshot_time: datetime) -> tuple:
        """将CSV行转换为数据库记录格式"""
        try:
            # 饰品基本信息
            item_record = {
                "item_id": self.data_cleaner.safe_str(row.get("item_id"), 50),
                "item_type": self.data_cleaner.safe_str(row.get("item_type"), 100),
                "name": self.data_cleaner.safe_str(row.get("name"), 255),
                "market_hash_name": self.data_cleaner.safe_str(row.get("market_hash_name"), 255),
                "quality": self.data_cleaner.safe_str(row.get("quality"), 50),
                "rarity": self.data_cleaner.safe_str(row.get("rarity"), 50),
                "exterior": self.data_cleaner.safe_str(row.get("exterior"), 50)
            }
            
            # 市场快照信息
            snapshot_record = {
                "item_id": item_record["item_id"],
                "snapshot_time": snapshot_time,
                "data_source": data_source,
                "current_price": self.data_cleaner.safe_decimal(row.get("current_price")),
                "diff_7d": self.data_cleaner.safe_decimal(row.get("diff_7d")),
                "trans_count_7d": self.data_cleaner.safe_int(row.get("trans_count_7d")),
                "trans_amount_7d": self.data_cleaner.safe_decimal(row.get("trans_amount_7d")),
                "hot_rank": self.data_cleaner.safe_int(row.get("hot_rank")),
                "sell_nums": self.data_cleaner.safe_int(row.get("sell_nums"))
            }
            
            return item_record, snapshot_record
            
        except Exception as e:
            self.logger.error(f"❌ 转换CSV行失败: {e}")
            return None, None
    
    def run_single_ranking(self, ranking_key: str) -> Dict[str, Any]:
        """运行单个榜单抓取"""
        return self.run_scraper([ranking_key])
    
    def run_all_rankings(self) -> Dict[str, Any]:
        """运行所有榜单抓取"""
        return self.run_scraper()
    
    def get_scraper_status(self) -> Dict[str, Any]:
        """获取爬虫状态"""
        try:
            # 检查数据库中的最新数据
            connection = pymysql.connect(**self.db_config)
            
            with connection.cursor() as cursor:
                # 获取最新的快照时间
                cursor.execute("""
                    SELECT data_source, COUNT(*) as count, MAX(snapshot_time) as latest_time
                    FROM market_snapshots 
                    WHERE snapshot_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                    GROUP BY data_source
                    ORDER BY latest_time DESC
                """)
                
                recent_data = cursor.fetchall()
                
                cursor.execute("SELECT COUNT(*) FROM items")
                total_items = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM market_snapshots")
                total_snapshots = cursor.fetchone()[0]
            
            connection.close()
            
            return {
                "total_items": total_items,
                "total_snapshots": total_snapshots,
                "recent_data": [
                    {
                        "data_source": row[0],
                        "count": row[1],
                        "latest_time": row[2].strftime("%Y-%m-%d %H:%M:%S") if row[2] else None
                    }
                    for row in recent_data
                ]
            }
            
        except Exception as e:
            self.logger.error(f"❌ 获取爬虫状态失败: {e}")
            return {"error": str(e)}
