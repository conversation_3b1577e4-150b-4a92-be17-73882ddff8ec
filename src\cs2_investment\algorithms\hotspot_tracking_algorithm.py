"""
热点追踪型投资筛选算法

基于热度变化和市场动量识别热点投资机会。
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from typing import List, Dict, Any
from loguru import logger

from .base_algorithm import BaseScreeningAlgorithm
from ..models.market_snapshot import MarketSnapshot
from ..models.analysis_result import AnalysisResult


class HotspotTrackingAlgorithm(BaseScreeningAlgorithm):
    """热点追踪型投资筛选算法"""
    
    def __init__(self):
        super().__init__(
            algorithm_name="HotspotTrackingAlgorithm",
            investment_type="热点追踪型",
            version="1.0"
        )
    
    def get_screening_query(self, session: Session):
        """获取热点追踪型筛选查询"""
        return session.query(MarketSnapshot, AnalysisResult)\
            .join(AnalysisResult, MarketSnapshot.item_id == AnalysisResult.item_id)\
            .filter(
                and_(
                    # 基础要求
                    MarketSnapshot.current_price > 50,
                    MarketSnapshot.hot_rank.isnot(None),
                    
                    # 热点信号筛选
                    or_(
                        # 热度排名上升
                        and_(
                            MarketSnapshot.hot_rank < 500,  # 进入热度榜
                            MarketSnapshot.hot_rank_change < 0,  # 排名上升（数值减少）
                            MarketSnapshot.hot_keep_days >= 1  # 至少保持1天
                        ),
                        # 成交量激增
                        and_(
                            MarketSnapshot.trans_count_1d > 20,
                            # 成交量比7日平均高50%以上
                            MarketSnapshot.trans_count_1d > MarketSnapshot.trans_count_7d / 7 * 1.5,
                            MarketSnapshot.diff_1d > 0  # 价格上涨
                        ),
                        # 价格动量强劲
                        and_(
                            MarketSnapshot.diff_1d > 5,  # 单日涨幅超过5%
                            MarketSnapshot.diff_3d > 8,  # 3日涨幅超过8%
                            MarketSnapshot.trans_amount_1d > 20000  # 有成交额支撑
                        )
                    )
                )
            )\
            .order_by(desc(MarketSnapshot.hot_rank_change * -1))  # 热度上升幅度排序
    
    def calculate_score(self, snapshot_and_analysis) -> float:
        """计算热点追踪评分"""
        snapshot, analysis = snapshot_and_analysis
        score = 0.0
        
        # 1. 热度变化评分 (30分)
        hotness_score = self._calculate_hotness_momentum(snapshot)
        score += hotness_score
        
        # 2. 成交量动量评分 (25分)
        volume_momentum_score = self._calculate_volume_momentum(snapshot)
        score += volume_momentum_score
        
        # 3. 价格动量评分 (25分)
        price_momentum_score = self._calculate_price_momentum(snapshot)
        score += price_momentum_score
        
        # 4. 持续性评分 (20分)
        sustainability_score = self._calculate_sustainability(snapshot)
        score += sustainability_score
        
        return min(score, 100.0)
    
    def _calculate_hotness_momentum(self, snapshot: MarketSnapshot) -> float:
        """计算热度动量评分"""
        score = 0.0
        
        # 热度排名评分
        if snapshot.hot_rank:
            rank = snapshot.hot_rank
            if rank <= 50:
                score += 15  # 顶级热度
            elif rank <= 100:
                score += 12  # 高热度
            elif rank <= 200:
                score += 10  # 中等热度
            elif rank <= 500:
                score += 8   # 一般热度
            else:
                score += 5   # 低热度
        
        # 热度排名变化评分
        if snapshot.hot_rank_change:
            rank_change = snapshot.hot_rank_change
            if rank_change <= -100:  # 排名大幅上升
                score += 15
            elif rank_change <= -50:
                score += 12
            elif rank_change <= -20:
                score += 10
            elif rank_change <= -10:
                score += 8
            elif rank_change < 0:
                score += 5
        
        return score
    
    def _calculate_volume_momentum(self, snapshot: MarketSnapshot) -> float:
        """计算成交量动量评分"""
        score = 0.0
        
        # 当日成交量与历史对比
        if snapshot.trans_count_1d and snapshot.trans_count_7d:
            daily_avg = snapshot.trans_count_7d / 7
            if daily_avg > 0:
                volume_ratio = snapshot.trans_count_1d / daily_avg
                
                if volume_ratio >= 5:
                    score += 15  # 成交量爆发
                elif volume_ratio >= 3:
                    score += 12  # 成交量大增
                elif volume_ratio >= 2:
                    score += 10  # 成交量明显增加
                elif volume_ratio >= 1.5:
                    score += 8   # 成交量适度增加
                elif volume_ratio >= 1.2:
                    score += 5   # 成交量略有增加
        
        # 成交额确认
        if snapshot.trans_amount_1d:
            amount = float(snapshot.trans_amount_1d)
            if amount >= 200000:
                score += 10  # 大额成交
            elif amount >= 100000:
                score += 8   # 中等成交
            elif amount >= 50000:
                score += 6   # 一般成交
            elif amount >= 20000:
                score += 4   # 小额成交
        
        return score
    
    def _calculate_price_momentum(self, snapshot: MarketSnapshot) -> float:
        """计算价格动量评分"""
        score = 0.0
        
        # 短期价格动量
        momentum_scores = []
        
        # 1日涨幅
        if snapshot.diff_1d:
            diff_1d = float(snapshot.diff_1d)
            if diff_1d >= 15:
                momentum_scores.append(10)  # 强劲上涨
            elif diff_1d >= 10:
                momentum_scores.append(8)
            elif diff_1d >= 5:
                momentum_scores.append(6)
            elif diff_1d >= 2:
                momentum_scores.append(4)
            elif diff_1d > 0:
                momentum_scores.append(2)
        
        # 3日涨幅
        if snapshot.diff_3d:
            diff_3d = float(snapshot.diff_3d)
            if diff_3d >= 20:
                momentum_scores.append(8)
            elif diff_3d >= 15:
                momentum_scores.append(6)
            elif diff_3d >= 10:
                momentum_scores.append(4)
            elif diff_3d > 0:
                momentum_scores.append(2)
        
        # 7日涨幅
        if snapshot.diff_7d:
            diff_7d = float(snapshot.diff_7d)
            if diff_7d >= 25:
                momentum_scores.append(7)
            elif diff_7d >= 15:
                momentum_scores.append(5)
            elif diff_7d >= 10:
                momentum_scores.append(3)
            elif diff_7d > 0:
                momentum_scores.append(1)
        
        # 取动量评分的平均值
        if momentum_scores:
            score = sum(momentum_scores) / len(momentum_scores)
        
        return score
    
    def _calculate_sustainability(self, snapshot: MarketSnapshot) -> float:
        """计算热点持续性评分"""
        score = 0.0
        
        # 热度保持天数
        if snapshot.hot_keep_days:
            keep_days = snapshot.hot_keep_days
            if keep_days >= 7:
                score += 10  # 持续热度
            elif keep_days >= 5:
                score += 8
            elif keep_days >= 3:
                score += 6
            elif keep_days >= 1:
                score += 4
        
        # 价格稳定性（避免过度波动）
        if snapshot.diff_7d and snapshot.diff_1d:
            week_change = abs(float(snapshot.diff_7d))
            day_change = abs(float(snapshot.diff_1d))
            
            # 如果7日涨幅合理且1日涨幅不过度
            if week_change <= 50 and day_change <= 20:
                score += 5  # 健康的上涨
            elif week_change <= 30 and day_change <= 15:
                score += 8  # 稳健的上涨
        
        # 基础流动性支撑
        if snapshot.trans_count_7d and snapshot.trans_count_7d > 50:
            score += 5  # 有基础流动性
        
        return score
    
    def determine_risk_level(self, snapshot_and_analysis, score: float) -> str:
        """确定热点追踪风险等级"""
        snapshot, analysis = snapshot_and_analysis
        risk_factors = 0
        
        # 价格过度上涨风险
        if snapshot.diff_1d and float(snapshot.diff_1d) > 20:
            risk_factors += 2  # 单日涨幅过大
        elif snapshot.diff_1d and float(snapshot.diff_1d) > 15:
            risk_factors += 1
        
        # 热度过度集中风险
        if snapshot.hot_rank and snapshot.hot_rank <= 10:
            risk_factors += 1  # 过度热门可能回调
        
        # 成交量异常风险
        if snapshot.trans_count_1d and snapshot.trans_count_7d:
            volume_ratio = snapshot.trans_count_1d / (snapshot.trans_count_7d / 7)
            if volume_ratio > 10:  # 成交量异常放大
                risk_factors += 1
        
        # 流动性风险
        if not snapshot.trans_count_1d or snapshot.trans_count_1d < 10:
            risk_factors += 1
        
        if risk_factors <= 1:
            return "MEDIUM"  # 热点投资本身就有一定风险
        elif risk_factors <= 3:
            return "HIGH"
        else:
            return "HIGH"
    
    def generate_recommendation(self, score: float, risk_level: str) -> str:
        """生成热点追踪投资建议"""
        if score >= 80:
            return "BUY" if risk_level != "HIGH" else "HOLD"
        elif score >= 65:
            return "HOLD" if risk_level == "MEDIUM" else "WAIT"
        elif score >= 50:
            return "WAIT"
        else:
            return "AVOID"
    
    def generate_analysis_summary(self, snapshot_and_analysis, score: float) -> str:
        """生成热点追踪分析摘要"""
        snapshot, analysis = snapshot_and_analysis
        summary_parts = []
        
        # 基础信息
        if snapshot.current_price:
            summary_parts.append(f"价格: ¥{snapshot.current_price}")
        
        # 热度信息
        if snapshot.hot_rank:
            summary_parts.append(f"热度排名: #{snapshot.hot_rank}")
        
        if snapshot.hot_rank_change:
            change_desc = "上升" if snapshot.hot_rank_change < 0 else "下降"
            summary_parts.append(f"排名{change_desc}: {abs(snapshot.hot_rank_change)}")
        
        # 涨幅信息
        if snapshot.diff_1d:
            summary_parts.append(f"日涨幅: {snapshot.diff_1d:.1f}%")
        
        # 成交量
        if snapshot.trans_count_1d:
            summary_parts.append(f"日成交量: {snapshot.trans_count_1d}")
        
        # 热度持续
        if snapshot.hot_keep_days:
            summary_parts.append(f"热度保持: {snapshot.hot_keep_days}天")
        
        # 评分总结
        if score >= 80:
            summary_parts.append("热点强度: 很强")
        elif score >= 65:
            summary_parts.append("热点强度: 较强")
        elif score >= 50:
            summary_parts.append("热点强度: 一般")
        else:
            summary_parts.append("热点强度: 较弱")
        
        return "; ".join(summary_parts)
