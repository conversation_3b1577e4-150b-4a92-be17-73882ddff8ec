"""
配置管理服务

提供配置的读取、保存、验证和管理功能。
"""

import os
import json
import logging
import requests
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime

from src.cs2_investment.config.timer_config import get_timer_config, TimerConfig


class ConfigService:
    """配置管理服务"""
    
    def __init__(self):
        """初始化配置服务"""
        self.logger = logging.getLogger(__name__)
        self.env_file = Path(".env")
        self.backup_dir = Path("config_backups")
        self.backup_dir.mkdir(exist_ok=True)
    
    def save_config(self, config_updates: Dict[str, Any]) -> bool:
        """
        保存配置更新到.env文件
        
        Args:
            config_updates: 要更新的配置项字典
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 创建备份
            self._create_backup()
            
            # 读取现有配置
            existing_config = self._read_env_file()
            
            # 更新配置
            existing_config.update(config_updates)
            
            # 写入配置文件
            self._write_env_file(existing_config)
            
            self.logger.info(f"配置已保存: {list(config_updates.keys())}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            return False
    
    def _read_env_file(self) -> Dict[str, str]:
        """读取.env文件内容"""
        config = {}
        
        if self.env_file.exists():
            try:
                with open(self.env_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            config[key.strip()] = value.strip()
            except Exception as e:
                self.logger.error(f"读取.env文件失败: {e}")
        
        return config
    
    def _write_env_file(self, config: Dict[str, Any]) -> None:
        """写入.env文件"""
        lines = []
        
        # 添加文件头注释
        lines.append("# CS2投资分析系统配置文件")
        lines.append(f"# 最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")
        
        # 按分组写入配置
        groups = self._group_config_items(config)
        
        for group_name, group_items in groups.items():
            lines.append(f"# ===== {group_name} =====")
            for key, value in group_items.items():
                # 处理布尔值
                if isinstance(value, bool):
                    value = str(value).lower()
                lines.append(f"{key}={value}")
            lines.append("")
        
        # 写入文件
        with open(self.env_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
    
    def _group_config_items(self, config: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """将配置项按功能分组"""
        groups = {
            "调度器全局配置": {},
            "API配置": {},
            "饰品信息更新": {},
            "价格更新": {},
            "简化价格更新": {},
            "Steam监控": {},
            "SteamDT ID更新": {},
            "数据抓取": {},
            "其他配置": {}
        }
        
        # 配置项分组映射
        group_mapping = {
            "SCHEDULER_": "调度器全局配置",
            "API_": "API配置",
            "STEAMDT_API_": "API配置",
            "ITEM_INFO_": "饰品信息更新",
            "PRICE_UPDATE_": "价格更新",
            "SIMPLE_PRICE_": "简化价格更新",
            "STEAM_MONITOR_": "Steam监控",
            "STEAMDT_ID_": "SteamDT ID更新",
            "SCRAPING_": "数据抓取",
        }
        
        for key, value in config.items():
            # 确定分组
            group_name = "其他配置"
            for prefix, group in group_mapping.items():
                if key.startswith(prefix):
                    group_name = group
                    break
            
            groups[group_name][key] = value
        
        # 移除空分组
        return {k: v for k, v in groups.items() if v}
    
    def _create_backup(self) -> None:
        """创建配置文件备份"""
        if self.env_file.exists():
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = self.backup_dir / f".env.backup_{timestamp}"
            
            try:
                import shutil
                shutil.copy2(self.env_file, backup_file)
                self.logger.info(f"配置备份已创建: {backup_file}")
                
                # 清理旧备份（保留最近10个）
                self._cleanup_old_backups()
                
            except Exception as e:
                self.logger.warning(f"创建配置备份失败: {e}")
    
    def _cleanup_old_backups(self) -> None:
        """清理旧的配置备份"""
        try:
            backup_files = list(self.backup_dir.glob(".env.backup_*"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # 保留最近10个备份
            for old_backup in backup_files[10:]:
                old_backup.unlink()
                self.logger.debug(f"删除旧备份: {old_backup}")
                
        except Exception as e:
            self.logger.warning(f"清理旧备份失败: {e}")
    
    def export_config(self) -> Dict[str, Any]:
        """导出当前配置为字典"""
        try:
            config = get_timer_config()
            
            # 将配置对象转换为字典
            config_dict = {
                "scheduler": {
                    "enabled": config.scheduler.enabled,
                    "startup_delay": config.scheduler.startup_delay,
                    "shutdown_timeout": config.scheduler.shutdown_timeout,
                    "timezone": config.scheduler.timezone,
                    "log_level": config.scheduler.log_level,
                },
                "api": {
                    "steamdt_api_key": config.api.steamdt_api_key,
                    "timeout": config.api.timeout,
                    "max_retries": config.api.max_retries,
                    "retry_delay": config.api.retry_delay,
                },
                "item_info_update": {
                    "enabled": config.item_info_update.enabled,
                    "cron_schedule": config.item_info_update.cron_schedule,
                    "health_check_interval": config.item_info_update.health_check_interval,
                    "max_retry_attempts": config.item_info_update.max_retry_attempts,
                },
                "price_update": {
                    "enabled": config.price_update.enabled,
                    "health_check_interval": config.price_update.health_check_interval,
                    "auto_restart_on_failure": config.price_update.auto_restart_on_failure,
                    "batch_size": config.price_update.batch_size,
                    "single_requests_per_minute": config.price_update.single_requests_per_minute,
                    "batch_requests_per_minute": config.price_update.batch_requests_per_minute,
                },
                "simple_price_update": {
                    "enabled": config.simple_price_update.enabled,
                    "update_interval_minutes": config.simple_price_update.update_interval_minutes,
                    "batch_size": config.simple_price_update.batch_size,
                    "single_size": config.simple_price_update.single_size,
                    "single_interval_seconds": config.simple_price_update.single_interval_seconds,
                    "items_limit": config.simple_price_update.items_limit,
                    "continuous_mode": config.simple_price_update.continuous_mode,
                },
                "steam_monitor": {
                    "enabled": config.steam_monitor.enabled,
                    "update_interval_minutes": config.steam_monitor.update_interval_minutes,
                    "max_concurrent_tasks": config.steam_monitor.max_concurrent_tasks,
                    "request_delay_seconds": config.steam_monitor.request_delay_seconds,
                    "max_retry_attempts": config.steam_monitor.max_retry_attempts,
                },
                "steamdt_id_update": {
                    "enabled": config.steamdt_id_update.enabled,
                    "cycle_interval_minutes": config.steamdt_id_update.cycle_interval_minutes,
                    "batch_size": config.steamdt_id_update.batch_size,
                    "min_wait_seconds": config.steamdt_id_update.min_wait_seconds,
                    "max_wait_seconds": config.steamdt_id_update.max_wait_seconds,
                    "max_retry_attempts": config.steamdt_id_update.max_retry_attempts,
                },
                "scraping": {
                    "scraping_method": config.scraping.scraping_method,
                    "proxy_enabled": config.scraping.proxy_enabled,
                    "proxy_url": config.scraping.proxy_url,
                    "playwright_headless": config.scraping.playwright_headless,
                    "playwright_timeout": config.scraping.playwright_timeout,
                },
                "export_info": {
                    "timestamp": datetime.now().isoformat(),
                    "version": "1.0.0"
                }
            }
            
            return config_dict
            
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            return {}
    
    def import_config(self, config_data: Dict[str, Any]) -> bool:
        """
        导入配置数据
        
        Args:
            config_data: 配置数据字典
            
        Returns:
            bool: 导入是否成功
        """
        try:
            # 验证配置数据格式
            if not self._validate_config_data(config_data):
                return False
            
            # 转换为环境变量格式
            env_config = self._convert_to_env_format(config_data)
            
            # 保存配置
            return self.save_config(env_config)
            
        except Exception as e:
            self.logger.error(f"导入配置失败: {e}")
            return False
    
    def _validate_config_data(self, config_data: Dict[str, Any]) -> bool:
        """验证配置数据格式"""
        required_sections = ["scheduler", "api", "price_update"]
        
        for section in required_sections:
            if section not in config_data:
                self.logger.error(f"配置数据缺少必需的节: {section}")
                return False
        
        return True
    
    def _convert_to_env_format(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """将配置数据转换为环境变量格式"""
        env_config = {}
        
        # 映射配置节到环境变量前缀
        section_mapping = {
            "scheduler": "SCHEDULER_",
            "api": "API_",
            "item_info_update": "ITEM_INFO_",
            "price_update": "PRICE_UPDATE_",
            "simple_price_update": "SIMPLE_PRICE_",
            "steam_monitor": "STEAM_MONITOR_",
            "steamdt_id_update": "STEAMDT_ID_",
            "scraping": "SCRAPING_",
        }
        
        for section, prefix in section_mapping.items():
            if section in config_data:
                section_data = config_data[section]
                for key, value in section_data.items():
                    env_key = f"{prefix}{key.upper()}"
                    env_config[env_key] = value
        
        return env_config
    
    def generate_env_template(self) -> str:
        """生成.env文件模板"""
        from src.cs2_investment.config.timer_config import create_env_template
        return create_env_template()
    
    def get_config_status(self) -> Dict[str, Any]:
        """获取配置状态信息"""
        try:
            config = get_timer_config()
            validation_result = config.validate_all()
            
            return {
                "env_file_exists": self.env_file.exists(),
                "env_file_size": self.env_file.stat().st_size if self.env_file.exists() else 0,
                "env_file_modified": datetime.fromtimestamp(self.env_file.stat().st_mtime) if self.env_file.exists() else None,
                "validation_valid": validation_result['valid'],
                "validation_errors": validation_result['errors'],
                "validation_warnings": validation_result['warnings'],
                "backup_count": len(list(self.backup_dir.glob(".env.backup_*"))),
            }
            
        except Exception as e:
            self.logger.error(f"获取配置状态失败: {e}")
            return {
                "env_file_exists": False,
                "validation_valid": False,
                "validation_errors": [str(e)],
                "validation_warnings": [],
                "backup_count": 0,
            }

    def check_api_service_available(self, api_base_url: str = "http://localhost:8000") -> bool:
        """
        检查API服务是否可用

        Args:
            api_base_url: API服务基础URL

        Returns:
            bool: API服务是否可用
        """
        try:
            response = requests.get(f"{api_base_url}/health", timeout=5)
            return response.status_code == 200
        except requests.RequestException:
            return False

    def reload_config_via_api(self, api_base_url: str = "http://localhost:8000") -> Dict[str, Any]:
        """
        通过API重载配置

        Args:
            api_base_url: API服务基础URL

        Returns:
            Dict[str, Any]: 重载结果
        """
        try:
            response = requests.post(f"{api_base_url}/config/reload", timeout=30)
            response.raise_for_status()
            result = response.json()

            # 检查API响应格式
            if result.get("success"):
                self.logger.info("通过API重载配置成功")
                return result.get("data", {})
            else:
                self.logger.error(f"API重载配置失败: {result.get('message', '未知错误')}")
                return {
                    "success": False,
                    "error": result.get("message", "API重载配置失败")
                }

        except requests.RequestException as e:
            self.logger.error(f"API调用失败: {e}")
            return {
                "success": False,
                "error": f"API调用失败: {str(e)}"
            }
        except Exception as e:
            self.logger.error(f"重载配置异常: {e}")
            return {
                "success": False,
                "error": f"重载配置异常: {str(e)}"
            }

    def update_config_via_api(self, config_updates: Dict[str, Any],
                             api_base_url: str = "http://localhost:8000") -> Dict[str, Any]:
        """
        通过API更新配置

        Args:
            config_updates: 配置更新字典
            api_base_url: API服务基础URL

        Returns:
            Dict[str, Any]: 更新结果
        """
        try:
            # 构建请求数据
            request_data = {
                "config_updates": config_updates
            }

            response = requests.put(
                f"{api_base_url}/config/update",
                json=request_data,
                timeout=30,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            result = response.json()

            # 检查API响应格式
            if result.get("success"):
                self.logger.info("通过API更新配置成功")
                return result.get("data", {})
            else:
                self.logger.error(f"API更新配置失败: {result.get('message', '未知错误')}")
                return {
                    "success": False,
                    "error": result.get("message", "API更新配置失败")
                }

        except requests.RequestException as e:
            self.logger.error(f"API调用失败: {e}")
            return {
                "success": False,
                "error": f"API调用失败: {str(e)}"
            }
        except Exception as e:
            self.logger.error(f"更新配置异常: {e}")
            return {
                "success": False,
                "error": f"更新配置异常: {str(e)}"
            }

    def get_config_status_via_api(self, api_base_url: str = "http://localhost:8000") -> Dict[str, Any]:
        """
        通过API获取配置状态

        Args:
            api_base_url: API服务基础URL

        Returns:
            Dict[str, Any]: 配置状态
        """
        try:
            response = requests.get(f"{api_base_url}/config/status", timeout=30)
            response.raise_for_status()
            result = response.json()

            # 检查API响应格式
            if result.get("success"):
                self.logger.debug("通过API获取配置状态成功")
                return result.get("data", {})
            else:
                self.logger.error(f"API获取配置状态失败: {result.get('message', '未知错误')}")
                return {
                    "success": False,
                    "error": result.get("message", "API获取配置状态失败")
                }

        except requests.RequestException as e:
            self.logger.error(f"API调用失败: {e}")
            return {
                "success": False,
                "error": f"API调用失败: {str(e)}"
            }
        except Exception as e:
            self.logger.error(f"获取配置状态异常: {e}")
            return {
                "success": False,
                "error": f"获取配置状态异常: {str(e)}"
            }

    def save_config_with_reload(self, config_updates: Dict[str, Any],
                               api_base_url: str = "http://localhost:8000") -> Dict[str, Any]:
        """
        保存配置并尝试通过API重载

        Args:
            config_updates: 配置更新字典
            api_base_url: API服务基础URL

        Returns:
            Dict[str, Any]: 操作结果
        """
        # 首先保存配置到文件
        save_success = self.save_config(config_updates)
        if not save_success:
            return {
                "success": False,
                "error": "保存配置文件失败",
                "file_saved": False,
                "api_reloaded": False
            }

        # 检查API服务是否可用
        if not self.check_api_service_available(api_base_url):
            self.logger.warning("API服务不可用，配置已保存但未能立即生效")
            return {
                "success": True,
                "message": "配置已保存，但API服务不可用，需要重启服务以应用配置",
                "file_saved": True,
                "api_reloaded": False,
                "warning": "API服务不可用"
            }

        # 尝试通过API重载配置
        reload_result = self.reload_config_via_api(api_base_url)

        return {
            "success": True,
            "message": "配置已保存并应用",
            "file_saved": True,
            "api_reloaded": reload_result.get("success", False),
            "reload_result": reload_result
        }

    def rollback_config(self) -> Dict[str, Any]:
        """
        回滚到最近的备份配置

        Returns:
            Dict[str, Any]: 回滚结果
        """
        try:
            backup_files = list(self.backup_dir.glob(".env.backup_*"))
            if not backup_files:
                self.logger.error("没有找到备份文件")
                return {
                    "success": False,
                    "error": "没有找到备份文件"
                }

            # 获取最新的备份文件
            latest_backup = max(backup_files, key=lambda x: x.stat().st_mtime)

            # 创建当前配置的备份（回滚前备份）
            self._create_backup()

            # 恢复备份
            import shutil
            shutil.copy2(latest_backup, self.env_file)

            self.logger.info(f"配置已回滚到: {latest_backup}")
            return {
                "success": True,
                "message": f"配置已回滚到备份文件: {latest_backup.name}",
                "backup_file": str(latest_backup),
                "backup_time": datetime.fromtimestamp(latest_backup.stat().st_mtime)
            }

        except Exception as e:
            self.logger.error(f"配置回滚失败: {e}")
            return {
                "success": False,
                "error": f"配置回滚失败: {str(e)}"
            }

    def get_backup_list(self) -> List[Dict[str, Any]]:
        """
        获取备份文件列表

        Returns:
            List[Dict[str, Any]]: 备份文件信息列表
        """
        try:
            backup_files = list(self.backup_dir.glob(".env.backup_*"))
            backup_list = []

            for backup_file in sorted(backup_files, key=lambda x: x.stat().st_mtime, reverse=True):
                backup_info = {
                    "filename": backup_file.name,
                    "path": str(backup_file),
                    "size": backup_file.stat().st_size,
                    "modified_time": datetime.fromtimestamp(backup_file.stat().st_mtime),
                    "is_latest": backup_file == max(backup_files, key=lambda x: x.stat().st_mtime) if backup_files else False
                }
                backup_list.append(backup_info)

            return backup_list

        except Exception as e:
            self.logger.error(f"获取备份列表失败: {e}")
            return []

    def validate_config_before_save(self, config_updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        保存前验证配置

        Args:
            config_updates: 要验证的配置更新

        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            # 读取当前配置
            current_config = self._read_env_file()

            # 应用更新
            test_config = current_config.copy()
            test_config.update(config_updates)

            # 创建临时配置文件进行验证
            temp_env_file = self.env_file.with_suffix('.temp')
            self._write_env_file_to_path(test_config, temp_env_file)

            try:
                # 尝试加载配置进行验证
                from src.cs2_investment.config.timer_config import TimerConfig

                # 临时设置环境变量文件路径
                original_env_file = os.environ.get('ENV_FILE', '')
                os.environ['ENV_FILE'] = str(temp_env_file)

                try:
                    test_timer_config = TimerConfig()
                    validation_result = test_timer_config.validate_all()

                    return {
                        "valid": validation_result['valid'],
                        "errors": validation_result['errors'],
                        "warnings": validation_result['warnings']
                    }
                finally:
                    # 恢复原始环境变量
                    if original_env_file:
                        os.environ['ENV_FILE'] = original_env_file
                    else:
                        os.environ.pop('ENV_FILE', None)

            finally:
                # 清理临时文件
                if temp_env_file.exists():
                    temp_env_file.unlink()

        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return {
                "valid": False,
                "errors": [f"配置验证异常: {str(e)}"],
                "warnings": []
            }

    def _write_env_file_to_path(self, config: Dict[str, Any], file_path: Path) -> None:
        """写入配置到指定路径"""
        lines = []

        # 添加文件头注释
        lines.append("# CS2投资分析系统配置文件")
        lines.append(f"# 最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")

        # 按分组写入配置
        groups = self._group_config_items(config)

        for group_name, group_items in groups.items():
            lines.append(f"# ===== {group_name} =====")
            for key, value in group_items.items():
                # 处理布尔值
                if isinstance(value, bool):
                    value = str(value).lower()
                lines.append(f"{key}={value}")
            lines.append("")

        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
