"""
SteamDT抓取器适配器

将原有的SteamDTScraper适配到新的抽象接口，保持原有功能不变。
"""

from typing import Dict, Any, Optional
from datetime import datetime

from .abstract_data_scraper import AbstractDataScraper
from .steamdt_scraper import SteamDTScraper
from .data_models import ScrapingResult


class SteamDTScraperAdapter(AbstractDataScraper):
    """SteamDT抓取器适配器
    
    将原有的SteamDTScraper包装成符合AbstractDataScraper接口的类，
    保持原有功能完全不变。
    """
    
    def __init__(self):
        """初始化适配器"""
        super().__init__()
        self._original_scraper = SteamDTScraper()
        self._is_started = False
    
    async def start(self) -> None:
        """启动抓取器"""
        if self._is_started:
            self.logger.warning("SteamDT抓取器已经启动")
            return
        
        try:
            # 原有的SteamDTScraper使用异步上下文管理器
            await self._original_scraper.__aenter__()
            self._is_started = True
            self.logger.info("✅ SteamDT抓取器启动成功")
        except Exception as e:
            self.logger.error(f"❌ SteamDT抓取器启动失败: {e}")
            raise
    
    async def stop(self) -> None:
        """停止抓取器"""
        if not self._is_started:
            self.logger.warning("SteamDT抓取器未启动")
            return
        
        try:
            # 原有的SteamDTScraper使用异步上下文管理器
            await self._original_scraper.__aexit__(None, None, None)
            self._is_started = False
            self.logger.info("✅ SteamDT抓取器停止成功")
        except Exception as e:
            self.logger.error(f"❌ SteamDT抓取器停止失败: {e}")
            raise
    
    async def scrape_item_data(self, item_url: str, data_requirements: Optional[Dict[str, bool]] = None) -> ScrapingResult:
        """抓取物品数据
        
        Args:
            item_url: 物品URL
            data_requirements: 数据需求配置
            
        Returns:
            ScrapingResult: 抓取结果
        """
        if not self._is_started:
            raise RuntimeError("抓取器未启动，请先调用start()方法")
        
        # 验证和标准化数据需求
        validated_requirements = self.validate_data_requirements(data_requirements)
        
        # 记录抓取开始
        self.log_scraping_start(item_url, validated_requirements)
        
        try:
            # 调用原有的抓取方法
            result = await self._original_scraper.scrape_item_data(item_url, validated_requirements)
            
            # 记录抓取结果
            self.log_scraping_result(result, item_url)
            
            return result
            
        except Exception as e:
            # 创建失败结果
            error_result = ScrapingResult(
                success=False,
                error_message=str(e),
                collected_at=datetime.now()
            )
            
            self.log_scraping_result(error_result, item_url)
            return error_result
    
    def get_scraper_info(self) -> Dict[str, Any]:
        """获取抓取器信息
        
        Returns:
            Dict: 抓取器详细信息
        """
        return {
            'scraper_type': 'playwright',
            'version': '1.0.0',
            'adapter': True,
            'original_class': 'SteamDTScraper',
            'capabilities': [
                'trend_data',
                'kline_data',
                'javascript_execution',
                'dynamic_content',
                'browser_automation'
            ],
            'config_method': 'playwright',
            'timeout_settings': {
                'playwright_timeout': 30000,
                'page_load_timeout': 30000,
                'navigation_timeout': 30000
            },
            'browser_settings': {
                'headless': True,
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'viewport': {'width': 1920, 'height': 1080}
            },
            'data_requirements_support': {
                'trend_data_3m': True,
                'trend_data_6m': True,
                'hourly_kline': True,
                'daily_kline_1': True,
                'daily_kline_2': True,
                'weekly_kline': True
            },
            'is_started': self._is_started,
            'original_scraper_available': self._original_scraper is not None
        }
    
    @property
    def is_started(self) -> bool:
        """检查抓取器是否已启动"""
        return self._is_started
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.stop()
        return False
