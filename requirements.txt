# CS2饰品投资分析系统依赖包

# 数据库相关
mysql-connector-python==8.2.0
SQLAlchemy==2.0.23
alembic==1.13.1

# 数据处理
pandas==2.1.4
numpy<2.0,>=1.24.0

# 配置管理
python-dotenv==1.0.0
pydantic==2.5.2
pydantic-settings==2.1.0

# 日志
loguru==0.7.2

# 测试
pytest==7.4.3
pytest-cov==4.1.0
pytest-mock==3.12.0

# 报告生成
jinja2==3.1.2
matplotlib==3.8.2
seaborn==0.13.0

# 工具类
click==8.1.7
rich==13.7.0
tqdm==4.66.1

# Web界面
streamlit==1.48.1
plotly==5.17.0
streamlit-aggrid==0.3.4.post3

# 爬虫相关
playwright>=1.40.0
aiohttp>=3.9.0
pymysql>=1.1.0

# API服务
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# 定时任务
schedule>=1.2.0

# 开发工具
black==23.12.1
flake8==6.1.0
mypy==1.8.0

# 技术分析库
TA-Lib>=0.4.25
scipy>=1.11.0

# 通知系统
plyer>=2.1.0

# 异步支持
pytest-asyncio>=0.21.0

# 配置文件处理
pyyaml>=6.0

# 时间处理
python-dateutil>=2.8.0
pytz>=2023.3

# 数据验证
marshmallow>=3.20.0

# 性能监控
prometheus-client>=0.18.0
psutil>=5.9.0

# 缓存
diskcache>=5.6.0

# 加密和安全
cryptography>=41.0.0

# 数据导出
openpyxl>=3.1.0

# 网页抓取
beautifulsoup4>=4.12.0

# 其他工具
typing-extensions>=4.8.0
pathlib2>=2.3.7

# 监控和健康检查
healthcheck>=1.3.3

# 错误追踪
sentry-sdk>=1.38.0

# 模板引擎（邮件通知）
jinja2>=3.1.0

# 消息序列化
msgpack>=1.0.0

# 统计分析
statsmodels>=0.14.0

# 数学计算
sympy>=1.12.0

# 网络工具
urllib3>=2.0.0
certifi>=2023.7.0

# 文档生成
docstring-parser>=0.15.0

# 代码格式化
autopep8>=2.0.0
isort>=5.12.0

# 依赖分析
pipdeptree>=2.13.0
