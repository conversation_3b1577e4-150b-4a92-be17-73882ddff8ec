# 价格更新优化配置示例
# 复制相关配置到你的 .env 文件中

# ===== 简化价格更新器优化配置 =====

# 启用简化价格更新器
SIMPLE_PRICE_UPDATE_ENABLED=true

# 更新间隔（分钟）- 建议保持1分钟
SIMPLE_PRICE_UPDATE_INTERVAL=1

# 批量接口处理数量 - 可以适当增加以提高效率
SIMPLE_PRICE_BATCH_SIZE=100

# 单个接口处理数量 - 可以适当增加
SIMPLE_PRICE_SINGLE_SIZE=50

# 单个接口间隔（秒）- 避免API限制
SIMPLE_PRICE_SINGLE_INTERVAL=1.2

# 每次获取的饰品数量 - 可以增加以提高覆盖率
SIMPLE_PRICE_ITEMS_LIMIT=200

# 是否按最后更新时间排序 - 建议启用
SIMPLE_PRICE_USE_LAST_UPDATE=true

# 🔥 关键优化：是否跳过全零价格饰品
# 启用此选项可以避免重复查询价格全为0的饰品
SIMPLE_PRICE_SKIP_ZERO_ITEMS=true

# 🔥 关键优化：全零价格饰品重新查询间隔（小时）
# 设置为24小时，意味着价格全为0的饰品24小时后才会重新查询
SIMPLE_PRICE_ZERO_UPDATE_INTERVAL=24

# ===== 其他相关配置 =====

# API配置
STEAMDT_API_KEY=your_api_key_here
API_TIMEOUT=30
API_MAX_RETRIES=3

# ===== API请求延迟配置 =====
# 🔥 新增：API请求之间的随机延迟配置
# 最小延迟秒数（建议不少于5秒）
SCRAPING_API_DELAY_MIN=10

# 最大延迟秒数（建议不超过60秒）
SCRAPING_API_DELAY_MAX=30

# 注意：延迟时间会影响数据抓取速度
# - 较短延迟：抓取速度快，但可能触发API限制
# - 较长延迟：抓取速度慢，但更稳定可靠
# 建议根据实际使用情况调整

# 调度器配置
SCHEDULER_ENABLED=true
SCHEDULER_TIMEZONE=Asia/Shanghai

# 日志级别
SCHEDULER_LOG_LEVEL=INFO
