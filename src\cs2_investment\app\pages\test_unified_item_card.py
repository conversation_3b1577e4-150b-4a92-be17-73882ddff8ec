"""
统一饰品卡片组件测试页面

用于验证unified_item_card组件的各种配置和功能。
包含不同模式的展示、自定义配置的演示、以及与现有页面效果的对比。
"""

import streamlit as st
import pandas as pd
from typing import List, Dict, Any
import sys
from pathlib import Path
from datetime import datetime, timedelta
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.app.components.unified_item_card import render_item_card
from src.cs2_investment.app.pages.item_query import display_item_row
from src.cs2_investment.app.pages.favorites import show_favorite_item
from src.cs2_investment.app.services.favorite_service import FavoriteService


def show_page():
    """显示测试页面"""
    st.title("🧪 统一饰品卡片组件测试")
    
    st.markdown("""
    ## 测试说明
    
    本页面用于测试统一饰品卡片组件的各种功能和配置：
    
    1. **预设模式测试**：验证query、favorite、custom三种预设模式
    2. **功能对比测试**：对比新组件与原始函数的效果
    3. **交互功能测试**：验证操作按钮的响应和状态变化
    4. **性能测试**：测试大量数据的渲染性能
    5. **边界情况测试**：测试各种数据格式和边界条件
    """)
    
    # 初始化服务
    if 'favorite_service' not in st.session_state:
        st.session_state.favorite_service = FavoriteService()
    
    # 创建测试数据
    test_data = create_test_data()
    
    # 测试区域选择
    test_mode = st.selectbox(
        "选择测试模式",
        ["预设模式测试", "功能对比测试", "交互功能测试", "性能测试", "边界情况测试"]
    )
    
    st.divider()
    
    if test_mode == "预设模式测试":
        show_preset_mode_tests(test_data)
    elif test_mode == "功能对比测试":
        show_comparison_tests(test_data)
    elif test_mode == "交互功能测试":
        show_interaction_tests(test_data)
    elif test_mode == "性能测试":
        show_performance_tests()
    elif test_mode == "边界情况测试":
        show_edge_case_tests()


def create_test_data():
    """创建测试数据"""
    base_time = datetime.now()
    
    return [
        {
            # Query模式测试数据
            'item_id': 'test_001',
            'name': 'AK-47 | 传承 (久经沙场)',
            'market_hash_name': 'AK-47 | Inheritance (Field-Tested)',
            'arbitrage_ratio': 0.85,
            'platform_prices': {
                'buff': {
                    'sell_price': 1250.00,
                    'sell_count': 15,
                    'bidding_price': 1200.00,
                    'bidding_count': 8,
                    'update_time': base_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'data_source': 'steamdt'
                },
                'youpin': {
                    'sell_price': 1280.00,
                    'sell_count': 12,
                    'bidding_price': 1220.00,
                    'bidding_count': 6,
                    'update_time': base_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'data_source': 'steamdt'
                },
                'steam': {
                    'sell_price': 1300.00,
                    'sell_count': 20,
                    'bidding_price': 1250.00,
                    'bidding_count': 10,
                    'update_time': base_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'data_source': 'steamdt'
                },
                'steam_direct': {
                    'sell_price': 180.50,
                    'sell_count': 5,
                    'bidding_price': 175.00,
                    'bidding_count': 3,
                    'update_time': base_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'data_source': 'steam_direct'
                }
            },
            'platform_mappings': {
                'BUFF': '12345',
                'YOUPIN': '67890'
            }
        },
        {
            # Favorite模式测试数据
            'id': 1,
            'item_id': 'test_002',
            'item_name': 'M4A4 | 龙王 (崭新出厂)',
            'created_at': base_time - timedelta(days=3),
            'notes': '这是一个很好的投资选择，价格稳定上涨',
            'arbitrage_ratio': 0.65
        },
        {
            # 低搬砖率测试数据
            'item_id': 'test_003',
            'name': 'AWP | 龙狙 (崭新出厂)',
            'market_hash_name': 'AWP | Dragon Lore (Factory New)',
            'arbitrage_ratio': 0.45,
            'platform_prices': {},
            'platform_mappings': {}
        },
        {
            # 无数据测试
            'item_id': 'test_004',
            'name': '测试饰品 (无数据)',
            'market_hash_name': '',
            'arbitrage_ratio': None,
            'platform_prices': {},
            'platform_mappings': {}
        }
    ]


def show_preset_mode_tests(test_data):
    """显示预设模式测试"""
    st.header("📋 预设模式测试")
    
    # Query模式测试
    st.subheader("1. Query模式测试")
    st.markdown("**功能**：搬砖率显示、收藏按钮、分析按钮、平台价格表格")
    
    with st.container():
        render_item_card(
            item_data=test_data[0],
            card_type='query',
            key_suffix='query_test'
        )
    
    # Favorite模式测试
    st.subheader("2. Favorite模式测试")
    st.markdown("**功能**：收藏时间、备注、分析按钮、删除按钮")
    
    with st.container():
        render_item_card(
            item_data=test_data[1],
            card_type='favorite',
            key_suffix='favorite_test'
        )
    
    # Custom模式测试
    st.subheader("3. Custom模式测试")
    st.markdown("**功能**：自定义操作按钮配置")
    
    custom_actions = [
        {'type': 'favorite', 'icon': '💖', 'help': '自定义收藏按钮'},
        {'type': 'analysis', 'icon': '📊', 'help': '自定义分析按钮'},
        {'type': 'custom', 'icon': '⚙️', 'help': '自定义操作', 'callback': lambda x: st.info(f"自定义操作: {x.get('name')}")}
    ]
    
    with st.container():
        render_item_card(
            item_data=test_data[0],
            card_type='custom',
            actions=custom_actions,
            key_suffix='custom_test'
        )


def show_comparison_tests(test_data):
    """显示功能对比测试"""
    st.header("🔄 功能对比测试")
    
    st.subheader("Query模式 vs 原始display_item_row函数")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**新组件 (Query模式)**")
        render_item_card(
            item_data=test_data[0],
            card_type='query',
            key_suffix='comparison_new'
        )
    
    with col2:
        st.markdown("**原始函数 (display_item_row)**")
        display_item_row(test_data[0])
    
    st.subheader("Favorite模式 vs 原始show_favorite_item函数")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**新组件 (Favorite模式)**")
        render_item_card(
            item_data=test_data[1],
            card_type='favorite',
            key_suffix='comparison_fav_new'
        )
    
    with col2:
        st.markdown("**原始函数 (show_favorite_item)**")
        show_favorite_item(test_data[1])


def show_interaction_tests(test_data):
    """显示交互功能测试"""
    st.header("🎯 交互功能测试")
    
    st.markdown("""
    **测试说明**：点击各种按钮，验证功能是否正常工作
    
    - 收藏按钮：应该能够添加/取消收藏，图标会变化
    - 分析按钮：应该能够打开分析对话框
    - 删除按钮：应该能够删除收藏项
    """)
    
    # 收藏功能测试
    st.subheader("收藏功能测试")
    render_item_card(
        item_data=test_data[0],
        card_type='query',
        key_suffix='interaction_favorite'
    )
    
    # 分析功能测试
    st.subheader("分析功能测试")
    render_item_card(
        item_data=test_data[1],
        card_type='favorite',
        key_suffix='interaction_analysis'
    )
    
    # 状态显示
    st.subheader("状态信息")
    if st.button("显示当前状态"):
        st.json({
            "session_state_keys": list(st.session_state.keys()),
            "favorite_service_initialized": 'favorite_service' in st.session_state,
            "analysis_component_initialized": 'item_analysis_component' in st.session_state
        })


def show_performance_tests():
    """显示性能测试"""
    st.header("⚡ 性能测试")
    
    st.markdown("**测试说明**：测试大量数据的渲染性能")
    
    # 数据量选择
    data_count = st.selectbox("选择测试数据量", [10, 50, 100, 200])
    
    if st.button("开始性能测试"):
        # 生成大量测试数据
        large_test_data = []
        for i in range(data_count):
            large_test_data.append({
                'item_id': f'perf_test_{i}',
                'name': f'测试饰品 #{i}',
                'market_hash_name': f'Test Item #{i}',
                'arbitrage_ratio': 0.5 + (i % 50) / 100,
                'platform_prices': {},
                'platform_mappings': {}
            })
        
        # 性能测试
        start_time = time.time()
        
        progress_bar = st.progress(0)
        for i, item in enumerate(large_test_data):
            render_item_card(
                item_data=item,
                card_type='query',
                key_suffix=f'perf_{i}'
            )
            progress_bar.progress((i + 1) / len(large_test_data))
        
        end_time = time.time()
        
        st.success(f"性能测试完成！")
        st.info(f"渲染 {data_count} 个卡片耗时: {end_time - start_time:.2f} 秒")
        st.info(f"平均每个卡片: {(end_time - start_time) / data_count * 1000:.2f} 毫秒")


def show_edge_case_tests():
    """显示边界情况测试"""
    st.header("🔍 边界情况测试")
    
    st.markdown("**测试说明**：测试各种边界条件和异常数据")
    
    edge_cases = [
        {
            'name': '空数据测试',
            'data': {},
            'description': '测试完全空的数据字典'
        },
        {
            'name': '缺失关键字段',
            'data': {'name': '只有名称的饰品'},
            'description': '测试缺失item_id等关键字段'
        },
        {
            'name': '超长文本测试',
            'data': {
                'item_id': 'long_text_test',
                'name': '这是一个非常非常非常非常非常非常长的饰品名称，用来测试文本显示是否正常' * 3,
                'notes': '这是一个超长的备注信息，' * 20,
                'created_at': datetime.now()
            },
            'description': '测试超长文本的显示效果'
        },
        {
            'name': '特殊字符测试',
            'data': {
                'item_id': 'special_char_test',
                'name': 'AK-47 | 测试 <>&"\'',
                'market_hash_name': 'Test & Special <> Characters "\'',
                'notes': '包含特殊字符: <>&"\''
            },
            'description': '测试特殊字符的处理'
        }
    ]
    
    for i, case in enumerate(edge_cases):
        st.subheader(f"{i+1}. {case['name']}")
        st.markdown(f"**描述**: {case['description']}")
        
        try:
            render_item_card(
                item_data=case['data'],
                card_type='query',
                key_suffix=f'edge_{i}'
            )
            st.success("✅ 测试通过")
        except Exception as e:
            st.error(f"❌ 测试失败: {str(e)}")
        
        st.divider()
