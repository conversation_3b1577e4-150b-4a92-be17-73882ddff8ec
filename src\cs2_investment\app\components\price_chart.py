"""
价格趋势图表组件

提供饰品价格趋势和成交量的可视化图表。
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.app.utils.data_formatter import format_price, format_number


def render_price_trend_chart(price_trend: List[Dict[str, Any]], 
                           item_name: str = "饰品",
                           default_period: str = "1m") -> None:
    """渲染价格趋势图表
    
    Args:
        price_trend: 价格趋势数据列表
        item_name: 饰品名称
        default_period: 默认时间段
    """
    if not price_trend:
        st.info("📊 暂无价格趋势数据")
        return
    
    # 时间段选择器
    st.subheader("📈 价格趋势分析")
    
    col1, col2 = st.columns([3, 1])
    
    with col2:
        period = st.selectbox(
            "时间段",
            options=["1d", "7d", "1m", "3m", "6m", "1y"],
            format_func=lambda x: {
                "1d": "1天",
                "7d": "7天", 
                "1m": "1月",
                "3m": "3月",
                "6m": "6月",
                "1y": "1年"
            }.get(x, x),
            index=2,  # 默认选择1月
            key="price_chart_period"
        )
    
    # 根据选择的时间段过滤数据
    filtered_data = filter_data_by_period(price_trend, period)
    
    if not filtered_data:
        st.warning(f"📊 {period}时间段内暂无数据")
        return
    
    with col1:
        st.write(f"**{item_name}** - {get_period_name(period)}价格走势")
    
    # 创建双轴图表
    fig = create_price_volume_chart(filtered_data, item_name, period)
    
    # 显示图表
    st.plotly_chart(fig, use_container_width=True)
    
    # 显示关键统计信息
    show_chart_statistics(filtered_data, period)


def filter_data_by_period(price_trend: List[Dict[str, Any]], period: str) -> List[Dict[str, Any]]:
    """根据时间段过滤数据"""
    if not price_trend:
        return []
    
    # 计算时间范围
    now = datetime.now()
    period_days = {
        "1d": 1,
        "7d": 7,
        "1m": 30,
        "3m": 90,
        "6m": 180,
        "1y": 365
    }
    
    days = period_days.get(period, 30)
    start_date = now - timedelta(days=days)
    
    # 过滤数据
    filtered = []
    for item in price_trend:
        snapshot_time = item.get('snapshot_time')
        if snapshot_time:
            if isinstance(snapshot_time, str):
                try:
                    snapshot_time = datetime.fromisoformat(snapshot_time.replace('Z', '+00:00'))
                except:
                    continue
            
            if snapshot_time >= start_date:
                filtered.append(item)
    
    # 数据采样优化性能
    return sample_data_for_performance(filtered, period)


def sample_data_for_performance(data: List[Dict[str, Any]], period: str) -> List[Dict[str, Any]]:
    """智能数据采样优化性能，保留重要数据点"""
    if len(data) <= 100:
        return data

    # 根据时间段确定采样策略
    sample_size = {
        "1d": 50,   # 1天数据保持高精度
        "7d": 70,   # 7天数据适中精度
        "1m": 100,  # 1月数据标准精度
        "3m": 120,  # 3月数据稍低精度
        "6m": 150,  # 6月数据稍低精度
        "1y": 200   # 1年数据低精度，但增加采样点
    }.get(period, 100)

    if len(data) <= sample_size:
        return data

    # 智能采样：保留重要数据点
    sampled = []
    step = len(data) // sample_size

    # 基础等间隔采样
    for i in range(0, len(data), step):
        sampled.append(data[i])

    # 保留价格突变点（变化超过5%的点）
    for i, item in enumerate(data):
        if i > 0:
            prev_price = data[i-1].get('current_price', 0)
            curr_price = item.get('current_price', 0)
            if prev_price > 0:
                change_pct = abs((curr_price - prev_price) / prev_price * 100)
                if change_pct > 5 and item not in sampled:  # 价格变化超过5%
                    sampled.append(item)

    # 确保包含最后一个数据点
    if data[-1] not in sampled:
        sampled.append(data[-1])

    # 按时间排序
    sampled.sort(key=lambda x: x.get('snapshot_time', ''))

    return sampled


def create_price_volume_chart(data: List[Dict[str, Any]], 
                            item_name: str, 
                            period: str) -> go.Figure:
    """创建价格和成交量双轴图表"""
    
    # 准备数据
    df = pd.DataFrame(data)
    df['snapshot_time'] = pd.to_datetime(df['snapshot_time'])
    df = df.sort_values('snapshot_time')
    
    # 创建子图
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.05,
        subplot_titles=('价格走势', '成交量'),
        row_heights=[0.7, 0.3]
    )
    
    # 价格线图
    fig.add_trace(
        go.Scatter(
            x=df['snapshot_time'],
            y=df['current_price'],
            mode='lines+markers',
            name='价格',
            line=dict(color='#1f77b4', width=2),
            marker=dict(size=4),
            hovertemplate='<b>时间</b>: %{x}<br>' +
                         '<b>价格</b>: ¥%{y:.2f}<br>' +
                         '<extra></extra>'
        ),
        row=1, col=1
    )
    
    # 成交量柱状图
    fig.add_trace(
        go.Bar(
            x=df['snapshot_time'],
            y=df['trans_count_1d'],
            name='日成交量',
            marker_color='rgba(55, 128, 191, 0.6)',
            hovertemplate='<b>时间</b>: %{x}<br>' +
                         '<b>成交量</b>: %{y}笔<br>' +
                         '<extra></extra>'
        ),
        row=2, col=1
    )
    
    # 添加价格变化颜色
    add_price_change_colors(fig, df)
    
    # 添加关键事件标注
    add_key_events_annotation(fig, df)
    
    # 设置图表样式
    fig.update_layout(
        title=f"{item_name} - {get_period_name(period)}走势图",
        xaxis_title="时间",
        yaxis_title="价格 (¥)",
        yaxis2_title="成交量 (笔)",
        hovermode='x unified',
        showlegend=True,
        height=600,
        margin=dict(l=50, r=50, t=80, b=50)
    )
    
    # 设置x轴格式
    fig.update_xaxes(
        tickformat=get_time_format(period),
        tickangle=45
    )
    
    # 设置y轴格式
    fig.update_yaxes(
        tickformat='.2f',
        row=1, col=1
    )
    
    fig.update_yaxes(
        tickformat='d',
        row=2, col=1
    )
    
    return fig


def add_price_change_colors(fig: go.Figure, df: pd.DataFrame) -> None:
    """添加价格变化颜色标注"""
    # 计算价格变化
    df['price_change'] = df['current_price'].pct_change()
    
    # 添加上涨/下跌的背景色
    for i in range(1, len(df)):
        if df.iloc[i]['price_change'] > 0.05:  # 上涨超过5%
            fig.add_vrect(
                x0=df.iloc[i-1]['snapshot_time'],
                x1=df.iloc[i]['snapshot_time'],
                fillcolor="rgba(0, 200, 81, 0.1)",
                layer="below",
                line_width=0,
                row=1, col=1
            )
        elif df.iloc[i]['price_change'] < -0.05:  # 下跌超过5%
            fig.add_vrect(
                x0=df.iloc[i-1]['snapshot_time'],
                x1=df.iloc[i]['snapshot_time'],
                fillcolor="rgba(255, 68, 68, 0.1)",
                layer="below",
                line_width=0,
                row=1, col=1
            )


def add_key_events_annotation(fig: go.Figure, df: pd.DataFrame) -> None:
    """添加关键事件标注"""
    if len(df) < 2:
        return

    # 找出价格突变点（调整为更合理的阈值）
    df['price_change'] = df['current_price'].pct_change()

    annotation_count = 0  # 限制标注数量，避免图表过于拥挤
    max_annotations = 5

    for i, row in df.iterrows():
        price_change = row['price_change']

        # 调整阈值：变化超过10%且限制标注数量
        if abs(price_change) > 0.10 and annotation_count < max_annotations:
            annotation_text = f"{'📈' if price_change > 0 else '📉'} {price_change:.1%}"

            fig.add_annotation(
                x=row['snapshot_time'],
                y=row['current_price'],
                text=annotation_text,
                showarrow=True,
                arrowhead=2,
                arrowsize=1,
                arrowwidth=2,
                arrowcolor="red" if price_change < 0 else "green",
                bgcolor="white",
                bordercolor="red" if price_change < 0 else "green",
                borderwidth=1,
                font=dict(size=9),
                row=1, col=1
            )
            annotation_count += 1


def show_chart_statistics(data: List[Dict[str, Any]], period: str) -> None:
    """显示图表统计信息"""
    if not data:
        return
    
    df = pd.DataFrame(data)
    
    # 计算统计指标
    current_price = df['current_price'].iloc[-1] if len(df) > 0 else 0
    min_price = df['current_price'].min()
    max_price = df['current_price'].max()
    avg_price = df['current_price'].mean()
    
    total_volume = df['trans_count_1d'].sum()
    avg_volume = df['trans_count_1d'].mean()
    
    # 价格变化
    if len(df) > 1:
        price_change = (current_price - df['current_price'].iloc[0]) / df['current_price'].iloc[0] * 100
    else:
        price_change = 0
    
    # 显示统计信息
    st.subheader(f"📊 {get_period_name(period)}统计数据")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "期间涨跌",
            f"{price_change:+.1f}%",
            delta=f"{current_price - df['current_price'].iloc[0]:.2f}" if len(df) > 1 else None
        )
    
    with col2:
        st.metric("最高价", format_price(max_price))
        st.metric("最低价", format_price(min_price))
    
    with col3:
        st.metric("平均价", format_price(avg_price))
        st.metric("当前价", format_price(current_price))
    
    with col4:
        st.metric("总成交量", format_number(total_volume, "笔"))
        st.metric("日均成交量", format_number(avg_volume, "笔"))


def get_period_name(period: str) -> str:
    """获取时间段中文名称"""
    period_names = {
        "1d": "1天",
        "7d": "7天",
        "1m": "1月",
        "3m": "3月", 
        "6m": "6月",
        "1y": "1年"
    }
    return period_names.get(period, period)


def get_time_format(period: str) -> str:
    """获取时间轴格式"""
    time_formats = {
        "1d": "%H:%M",      # 小时:分钟
        "7d": "%m-%d",      # 月-日
        "1m": "%m-%d",      # 月-日
        "3m": "%m-%d",      # 月-日
        "6m": "%Y-%m",      # 年-月
        "1y": "%Y-%m"       # 年-月
    }
    return time_formats.get(period, "%m-%d")
