"""
集成定时任务管理器

将价格更新任务集成到应用启动中，支持后台定时执行。
"""

import asyncio
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.services.simple_price_updater import SimplePriceUpdater
from src.cs2_investment.services.steam_market_monitor_service import SteamMarketMonitorService
from src.cs2_investment.config.timer_config import get_timer_config


class IntegratedScheduler:
    """集成定时任务管理器"""
    
    def __init__(self, api_key: str = None):
        """
        初始化集成定时任务管理器

        Args:
            api_key: SteamDT API密钥（可选，从配置文件读取）
        """
        # 加载配置
        self.config = get_timer_config()

        self.api_key = api_key or self.config.api.steamdt_api_key
        if not self.api_key:
            raise ValueError("API key is required")

        self.logger = logging.getLogger(__name__)

        # 创建后台调度器
        scheduler_config = self.config.scheduler
        self.scheduler = BackgroundScheduler(
            timezone=scheduler_config.timezone,
            job_defaults={
                'coalesce': True,  # 合并错过的任务
                'max_instances': 1,  # 每个任务最多同时运行1个实例
                'misfire_grace_time': 300  # 错过任务的宽限时间（秒）
            }
        )

        # 简化价格更新器（替代复杂的智能价格更新池）
        self.simple_price_updater = SimplePriceUpdater(self.api_key)

        # Steam市场监控服务
        self.steam_monitor_service = SteamMarketMonitorService()
        # 从配置更新Steam监控服务配置
        steam_config = self.config.steam_monitor
        self.steam_monitor_service.update_config({
            'min_delay_seconds': steam_config.min_delay_seconds,
            'max_delay_seconds': steam_config.max_delay_seconds,
            'batch_size': steam_config.batch_size
        })

        # 运行状态跟踪
        self.simple_price_update_running = False
        self.steam_monitor_running = False
        
        # 任务状态
        self.is_running = False
        self.task_status = {
            'last_price_update': None,
            'last_price_update_result': None,
            'next_price_update': None,
            'price_update_count': 0,
            'price_update_errors': 0,
            'simple_price_update_count': 0,
            'simple_price_update_errors': 0,
            'last_steam_monitor': None,
            'last_steam_monitor_result': None,
            'steam_monitor_count': 0,
            'steam_monitor_errors': 0
        }

        # 运行时配置
        self.runtime_config = {
            'cleanup_cron': '0 3 * * *',  # 每天凌晨3点清理数据
            'status_check_interval_minutes': 30,  # 状态检查间隔（分钟）
        }
        
        # 设置事件监听器
        self.scheduler.add_listener(self._job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)
    
    def _job_listener(self, event):
        """任务执行监听器"""
        job_id = event.job_id
        
        if event.exception:
            self.logger.error(f"定时任务执行失败: {job_id}, 异常: {event.exception}")
            if 'price_update' in job_id:
                self.task_status['price_update_errors'] += 1
            elif 'simple_price_update' in job_id:
                self.task_status['simple_price_update_errors'] += 1
            elif 'steam_monitor' in job_id:
                self.task_status['steam_monitor_errors'] += 1
        else:
            self.logger.info(f"定时任务执行成功: {job_id}")
            if 'price_update' in job_id:
                self.task_status['price_update_count'] += 1
            elif 'simple_price_update' in job_id:
                self.task_status['simple_price_update_count'] += 1
            elif 'steam_monitor' in job_id:
                self.task_status['steam_monitor_count'] += 1
    
    async def start(self):
        """启动定时任务调度器"""
        if self.is_running:
            self.logger.warning("定时任务调度器已经在运行")
            return

        try:
            self.logger.info("🚀 启动集成定时任务调度器")

            # 根据配置决定启动哪些服务
            # 注意：已删除复杂的智能价格更新池，只使用简化价格更新器

            if self.config.simple_price_update.enabled:
                # 添加简化价格更新任务
                self._add_simple_price_update_jobs()
                self.logger.info("✅ 简化价格更新任务已添加")
            else:
                self.logger.info("ℹ️ 简化价格更新已禁用")

            if self.config.steam_monitor.enabled:
                # 添加Steam监控任务
                self._add_steam_monitor_jobs()
                self.logger.info("✅ Steam监控任务已添加")
            else:
                self.logger.info("ℹ️ Steam监控已禁用")

            # 添加数据清理任务
            self._add_cleanup_jobs()

            # 添加状态检查任务
            self._add_status_check_jobs()

            # 启动调度器
            self.scheduler.start()
            self.is_running = True

            # 显示任务信息
            self._log_scheduled_jobs()

            self.logger.info("✅ 集成定时任务调度器启动成功")
            
        except Exception as e:
            self.logger.error(f"❌ 启动定时任务调度器失败: {e}")
            raise
    
    def stop(self):
        """停止定时任务调度器"""
        if not self.is_running:
            return

        try:
            self.logger.info("⏹️ 停止集成定时任务调度器")

            # 注意：已删除复杂的智能价格更新池停止逻辑

            # 停止简化价格更新器
            if hasattr(self, 'simple_price_updater') and self.simple_price_updater:
                self.simple_price_updater.stop()

            # 重置Steam监控运行状态
            self.steam_monitor_running = False

            # 停止调度器
            self.scheduler.shutdown(wait=True)
            self.is_running = False
            self.logger.info("✅ 定时任务调度器已停止")
        except Exception as e:
            self.logger.error(f"❌ 停止定时任务调度器失败: {e}")
    

    
    def _add_simple_price_update_jobs(self):
        """添加简化价格更新任务"""
        simple_config = self.config.simple_price_update

        if simple_config.continuous_mode:
            # 持续运行模式：启动后立即开始持续运行
            self.scheduler.add_job(
                func=self._start_continuous_price_update,
                trigger='date',  # 立即执行一次
                id='continuous_price_update',
                name='持续价格更新',
                replace_existing=True
            )
        else:
            # 传统模式：每30秒检查一次
            self.scheduler.add_job(
                func=self._run_simple_price_update_task,
                trigger=IntervalTrigger(seconds=30),
                id='simple_price_update',
                name='简化价格更新',
                replace_existing=True
            )

    def _add_steam_monitor_jobs(self):
        """添加Steam监控任务"""
        # Steam监控应该立即启动并持续运行，而不是定时触发
        # 启动后立即开始第一次监控
        self.scheduler.add_job(
            func=self._start_continuous_steam_monitor,
            trigger='date',  # 立即执行一次
            id='steam_monitor_starter',
            name='Steam监控启动器',
            replace_existing=True
        )

    def _add_cleanup_jobs(self):
        """添加数据清理任务"""
        # 每天凌晨3点清理过期数据
        self.scheduler.add_job(
            func=self._run_cleanup_task,
            trigger=CronTrigger.from_crontab(self.runtime_config['cleanup_cron']),
            id='daily_cleanup',
            name='每日数据清理',
            replace_existing=True
        )
    
    def _add_status_check_jobs(self):
        """添加状态检查任务"""
        # 每30分钟检查一次状态
        self.scheduler.add_job(
            func=self._run_status_check_task,
            trigger=IntervalTrigger(minutes=self.runtime_config['status_check_interval_minutes']),
            id='status_check',
            name='状态检查',
            replace_existing=True
        )
    

    
    def _run_simple_price_update_task(self):
        """检查并执行简化价格更新任务（每30秒检查一次）"""
        try:
            # 检查是否正在运行
            if self.simple_price_update_running:
                self.logger.debug("🔄 简化价格更新正在运行中，等待下次检查")
                return

            # 标记为运行中
            self.simple_price_update_running = True

            try:
                # 在新的事件循环中运行异步任务
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    self.logger.debug("🚀 开始执行简化价格更新任务")

                    # 运行一次更新周期
                    result = loop.run_until_complete(self.simple_price_updater.run_update_cycle())

                    # 更新任务状态
                    self.task_status['simple_price_update_count'] += 1
                    self.task_status['last_price_update'] = datetime.now()
                    self.task_status['last_price_update_result'] = result

                    self.logger.info(f"✅ 价格更新完成: "
                                   f"批量{result.get('batch_processed', 0)}个, "
                                   f"单个{result.get('single_processed', 0)}个, "
                                   f"总计{result.get('total_processed', 0)}个")

                finally:
                    loop.close()

            finally:
                # 无论成功还是失败，都要重置运行状态
                self.simple_price_update_running = False

        except Exception as e:
            self.task_status['simple_price_update_errors'] += 1
            self.simple_price_update_running = False  # 确保重置状态
            self.logger.error(f"❌ 简化价格更新任务异常: {e}", exc_info=True)

    def _start_continuous_price_update(self):
        """启动持续价格更新（新模式）"""
        try:
            # 检查是否已经在运行
            if self.simple_price_update_running:
                self.logger.warning("🔄 持续价格更新已在运行中")
                return

            # 标记为运行中
            self.simple_price_update_running = True

            try:
                # 在新的事件循环中运行异步任务
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    self.logger.info("🚀 启动持续价格更新模式")

                    # 启动持续更新循环
                    loop.run_until_complete(self.simple_price_updater.start_continuous_update_cycle())

                finally:
                    loop.close()

            finally:
                # 重置运行状态
                self.simple_price_update_running = False

        except Exception as e:
            self.task_status['simple_price_update_errors'] += 1
            self.simple_price_update_running = False
            self.logger.error(f"❌ 持续价格更新异常: {e}", exc_info=True)

    def _start_continuous_steam_monitor(self):
        """启动持续的Steam监控"""
        try:
            # 检查是否已经在运行
            if self.steam_monitor_running:
                self.logger.debug("🔄 Steam监控已在运行中")
                return

            # 标记为运行中
            self.steam_monitor_running = True

            # 在新线程中启动持续监控
            import threading

            def continuous_monitor():
                """持续监控函数"""
                try:
                    # 创建新的事件循环
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    try:
                        self.logger.info("🚀 启动持续Steam监控")

                        # 持续运行监控循环
                        loop.run_until_complete(self._continuous_steam_monitor_loop())

                    finally:
                        loop.close()

                except Exception as e:
                    self.logger.error(f"❌ 持续Steam监控异常: {e}", exc_info=True)
                finally:
                    self.steam_monitor_running = False

            monitor_thread = threading.Thread(target=continuous_monitor, daemon=True, name="SteamMonitorThread")
            monitor_thread.start()

            self.logger.info("✅ Steam监控线程已启动")

        except Exception as e:
            self.task_status['steam_monitor_errors'] += 1
            self.steam_monitor_running = False
            self.logger.error(f"❌ 启动Steam监控失败: {e}", exc_info=True)

    async def _continuous_steam_monitor_loop(self):
        """持续Steam监控循环"""
        steam_config = self.config.steam_monitor

        self.logger.info(f"🔄 开始持续Steam监控循环 - 批量大小: {steam_config.batch_size}")

        while self.is_running and self.steam_monitor_running:
            try:
                self.logger.debug("🚀 执行Steam监控循环")

                # 运行Steam监控
                result = await self.steam_monitor_service.monitor_favorite_items("default_user")

                # 更新任务状态
                self.task_status['steam_monitor_count'] += 1
                self.task_status['last_steam_monitor'] = datetime.now()
                self.task_status['last_steam_monitor_result'] = result

                self.logger.info(f"✅ Steam监控循环完成: "
                               f"总计{result.get('total_items', 0)}个, "
                               f"更新{result.get('updated_items', 0)}个, "
                               f"失败{result.get('failed_items', 0)}个, "
                               f"跳过{result.get('skipped_items', 0)}个, "
                               f"耗时{result.get('duration_seconds', 0):.1f}秒")

                # 如果没有需要更新的饰品，等待一段时间再检查
                if result.get('total_items', 0) == 0:
                    wait_time = steam_config.monitor_interval_minutes * 60  # 转换为秒
                    self.logger.info(f"😴 没有需要更新的饰品，等待{wait_time}秒后重新检查")
                    await asyncio.sleep(wait_time)
                else:
                    # 有饰品更新，短暂等待后继续下一轮
                    wait_time = 60  # 1分钟后继续
                    self.logger.debug(f"⏳ 等待{wait_time}秒后继续下一轮监控")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                self.task_status['steam_monitor_errors'] += 1
                self.logger.error(f"❌ Steam监控循环异常: {e}", exc_info=True)

                # 发生异常时等待一段时间再重试
                await asyncio.sleep(300)  # 5分钟后重试

        self.logger.info("🛑 Steam监控循环已停止")

    def _run_cleanup_task(self):
        """执行数据清理任务"""
        self.logger.info("🗑️ 开始执行数据清理任务")

        try:
            # 清理90天前的数据
            # deleted_count = self.price_service.cleanup_old_data(days=90)
            # self.logger.info(f"✅ 数据清理完成，删除了{deleted_count}条记录")
            self.logger.info("✅ 数据清理任务跳过（未实现）")

        except Exception as e:
            self.logger.error(f"❌ 数据清理任务异常: {e}", exc_info=True)
    
    def _run_status_check_task(self):
        """执行状态检查任务"""
        try:
            # 在新的事件循环中运行异步任务
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 检查简化价格更新器状态
                self.logger.debug("✅ 简化价格更新器运行正常")
                    
            finally:
                loop.close()
                
        except Exception as e:
            self.logger.error(f"❌ 状态检查任务异常: {e}")
    
    def _log_scheduled_jobs(self):
        """记录已调度的任务信息"""
        jobs = self.scheduler.get_jobs()
        
        self.logger.info("📅 已调度的任务:")
        for job in jobs:
            next_run = job.next_run_time
            if next_run:
                self.logger.info(f"  - {job.name}: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        jobs = self.scheduler.get_jobs()
        job_info = []
        
        for job in jobs:
            job_info.append({
                'id': job.id,
                'name': job.name,
                'next_run': job.next_run_time.isoformat() if job.next_run_time else None,
                'trigger': str(job.trigger)
            })
        
        # 添加运行状态信息
        task_status = self.task_status.copy()
        task_status['simple_price_update_running'] = self.simple_price_update_running

        return {
            'is_running': self.is_running,
            'scheduler_running': self.scheduler.running if hasattr(self.scheduler, 'running') else False,
            'jobs': job_info,
            'task_status': task_status,
            'config': self.config.get_summary(),
            'runtime_config': self.runtime_config.copy()
        }
    
    async def trigger_smart_price_update_now(self):
        """立即触发智能价格更新任务"""
        if not self.is_running:
            raise RuntimeError("调度器未运行")

        self.logger.info("🚀 手动触发简化价格更新任务")

        # 直接执行一次简化更新周期
        try:
            result = await self.simple_price_updater.run_update_cycle()
            self.logger.info(f"✅ 手动更新完成: "
                           f"批量处理{result.get('batch_processed', 0)}个, "
                           f"单个处理{result.get('single_processed', 0)}个")
            return result
        except Exception as e:
            self.logger.error(f"❌ 手动更新失败: {e}")
            raise

    async def trigger_simple_price_update_now(self):
        """立即触发简化价格更新任务"""
        if not self.is_running:
            raise RuntimeError("调度器未运行")

        self.logger.info("🚀 手动触发简化价格更新任务")

        # 直接执行一次更新周期
        try:
            result = await self.simple_price_updater.run_update_cycle()
            self.logger.info(f"✅ 手动简化更新完成: "
                           f"批量{result.get('batch_processed', 0)}个, "
                           f"单个{result.get('single_processed', 0)}个, "
                           f"总计{result.get('total_processed', 0)}个")
            return result
        except Exception as e:
            self.logger.error(f"❌ 手动简化更新失败: {e}")
            raise
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        self.runtime_config.update(new_config)

        if self.is_running:
            self.logger.info("🔄 重新配置定时任务")

            # 移除旧任务
            try:
                self.scheduler.remove_job('simple_price_update')
            except:
                pass
            try:
                self.scheduler.remove_job('steam_monitor_starter')
            except:
                pass
            try:
                self.scheduler.remove_job('daily_cleanup')
            except:
                pass
            try:
                self.scheduler.remove_job('status_check')
            except:
                pass

            # 重新添加任务
            if self.config.simple_price_update.enabled:
                self._add_simple_price_update_jobs()
            if self.config.steam_monitor.enabled:
                self._add_steam_monitor_jobs()
            self._add_cleanup_jobs()
            self._add_status_check_jobs()

            self.logger.info("✅ 定时任务重新配置完成")

    async def trigger_steam_monitor_now(self):
        """立即触发Steam监控任务（在持续运行模式下，这将执行一次独立的监控）"""
        if not self.is_running:
            raise RuntimeError("调度器未运行")

        self.logger.info("🚀 手动触发Steam监控任务")

        # 直接执行一次Steam监控（独立于持续运行的监控循环）
        try:
            result = await self.steam_monitor_service.monitor_favorite_items("default_user")

            # 更新手动触发的统计
            self.task_status['steam_monitor_count'] += 1
            self.task_status['last_steam_monitor'] = datetime.now()
            self.task_status['last_steam_monitor_result'] = result

            self.logger.info(f"✅ 手动Steam监控完成: "
                           f"总计{result.get('total_items', 0)}个, "
                           f"更新{result.get('updated_items', 0)}个, "
                           f"失败{result.get('failed_items', 0)}个")
            return result
        except Exception as e:
            self.task_status['steam_monitor_errors'] += 1
            self.logger.error(f"❌ 手动Steam监控失败: {e}")
            raise
