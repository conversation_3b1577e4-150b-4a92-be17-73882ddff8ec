"""
数据清洗工具

提供JSON数据的清洗、验证和标准化功能。
"""

import json
from typing import Dict, List, Any, Optional, Union
from decimal import Decimal, InvalidOperation
from datetime import datetime
from loguru import logger


class DataCleaner:
    """数据清洗器"""
    
    def __init__(self):
        self.logger = logger.bind(cleaner="DataCleaner")
    
    def safe_str(self, value: Any, max_length: Optional[int] = None) -> Optional[str]:
        """安全转换为字符串"""
        if value is None or value == '' or value == 'null':
            return None
        
        try:
            result = str(value).strip()
            if max_length and len(result) > max_length:
                result = result[:max_length]
            return result if result else None
        except Exception:
            return None
    
    def safe_int(self, value: Any) -> Optional[int]:
        """安全转换为整数"""
        if value is None or value == '' or value == 'null':
            return None
        
        try:
            if isinstance(value, str):
                # 移除可能的千分位分隔符
                value = value.replace(',', '').replace(' ', '')
            return int(float(str(value)))
        except (ValueError, TypeError):
            return None
    
    def safe_decimal(self, value: Any, precision: int = 2) -> Optional[Decimal]:
        """安全转换为Decimal"""
        if value is None or value == '' or value == 'null':
            return None
        
        try:
            if isinstance(value, str):
                # 移除可能的千分位分隔符和货币符号
                value = value.replace(',', '').replace('$', '').replace('¥', '').replace(' ', '')
            
            decimal_value = Decimal(str(value))
            return decimal_value.quantize(Decimal('0.' + '0' * precision))
        except (ValueError, TypeError, InvalidOperation):
            return None
    
    def safe_float(self, value: Any) -> Optional[float]:
        """安全转换为浮点数"""
        if value is None or value == '' or value == 'null':
            return None
        
        try:
            if isinstance(value, str):
                value = value.replace(',', '').replace(' ', '')
            return float(str(value))
        except (ValueError, TypeError):
            return None
    
    def clean_item_data(self, item_info: Dict[str, Any]) -> Dict[str, Any]:
        """清洗饰品基础信息"""
        return {
            'item_id': self.safe_str(item_info.get('itemId')),
            'item_type': self.safe_str(item_info.get('itemType'), 50),
            'def_index_name': self.safe_str(item_info.get('defIndexName'), 100),
            'name': self.safe_str(item_info.get('name'), 200),
            'market_hash_name': self.safe_str(item_info.get('marketHashName'), 200),
            'image_url': self.safe_str(item_info.get('imageUrl')),
            'quality': self.safe_str(item_info.get('quality'), 50),
            'rarity': self.safe_str(item_info.get('rarity'), 50),
            'exterior': self.safe_str(item_info.get('exterior'), 50),
        }
    
    def clean_price_data(self, price_info: Dict[str, Any]) -> Dict[str, Any]:
        """清洗价格信息"""
        return {
            'current_price': self.safe_decimal(price_info.get('price')),
            'diff_1d': self.safe_decimal(price_info.get('diff1Days')),
            'diff_1d_price': self.safe_decimal(price_info.get('diff1DaysPrice')),
            'before_1d_price': self.safe_decimal(price_info.get('before1DaysPrice')),
            'diff_3d': self.safe_decimal(price_info.get('diff3Days')),
            'diff_3d_price': self.safe_decimal(price_info.get('diff3DaysPrice')),
            'before_3d_price': self.safe_decimal(price_info.get('before3DaysPrice')),
            'diff_7d': self.safe_decimal(price_info.get('diff7Days')),
            'diff_7d_price': self.safe_decimal(price_info.get('diff7DaysPrice')),
            'before_7d_price': self.safe_decimal(price_info.get('before7DaysPrice')),
            'diff_15d': self.safe_decimal(price_info.get('diff15Days')),
            'diff_15d_price': self.safe_decimal(price_info.get('diff15DaysPrice')),
            'before_15d_price': self.safe_decimal(price_info.get('before15DaysPrice')),
            'diff_1m': self.safe_decimal(price_info.get('diff1Months')),
            'diff_1m_price': self.safe_decimal(price_info.get('diff1MonthsPrice')),
            'before_1m_price': self.safe_decimal(price_info.get('before1MonthsPrice')),
            'diff_3m': self.safe_decimal(price_info.get('diff3Months')),
            'diff_3m_price': self.safe_decimal(price_info.get('diff3MonthsPrice')),
            'before_3m_price': self.safe_decimal(price_info.get('before3MonthsPrice')),
            'diff_6m': self.safe_decimal(price_info.get('diff6Months')),
            'diff_6m_price': self.safe_decimal(price_info.get('diff6MonthsPrice')),
            'before_6m_price': self.safe_decimal(price_info.get('before6MonthsPrice')),
            'diff_1y': self.safe_decimal(price_info.get('diff1Years')),
            'diff_1y_price': self.safe_decimal(price_info.get('diff1YearsPrice')),
            'before_1y_price': self.safe_decimal(price_info.get('before1YearsPrice')),
        }
    
    def clean_sell_nums_data(self, sell_nums_info: Dict[str, Any]) -> Dict[str, Any]:
        """清洗在售数量信息"""
        return {
            'sell_nums': self.safe_int(sell_nums_info.get('sellNums')),
            'sell_nums_1d': self.safe_int(sell_nums_info.get('sellNums1Days')),
            'sell_nums_1d_diff': self.safe_int(sell_nums_info.get('sellNums1DaysDiff')),
            'sell_nums_1d_rate': self.safe_decimal(sell_nums_info.get('sellNums1DaysRate')),
            'sell_nums_3d': self.safe_int(sell_nums_info.get('sellNums3Days')),
            'sell_nums_3d_diff': self.safe_int(sell_nums_info.get('sellNums3DaysDiff')),
            'sell_nums_3d_rate': self.safe_decimal(sell_nums_info.get('sellNums3DaysRate')),
            'sell_nums_7d': self.safe_int(sell_nums_info.get('sellNums7Days')),
            'sell_nums_7d_diff': self.safe_int(sell_nums_info.get('sellNums7DaysDiff')),
            'sell_nums_7d_rate': self.safe_decimal(sell_nums_info.get('sellNums7DaysRate')),
            'sell_nums_15d': self.safe_int(sell_nums_info.get('sellNums15Days')),
            'sell_nums_15d_diff': self.safe_int(sell_nums_info.get('sellNums15DaysDiff')),
            'sell_nums_15d_rate': self.safe_decimal(sell_nums_info.get('sellNums15DaysRate')),
            'sell_nums_1m': self.safe_int(sell_nums_info.get('sellNums1Months')),
            'sell_nums_1m_diff': self.safe_int(sell_nums_info.get('sellNums1MonthsDiff')),
            'sell_nums_1m_rate': self.safe_decimal(sell_nums_info.get('sellNums1MonthsRate')),
            'sell_nums_3m': self.safe_int(sell_nums_info.get('sellNums3Months')),
            'sell_nums_3m_diff': self.safe_int(sell_nums_info.get('sellNums3MonthsDiff')),
            'sell_nums_3m_rate': self.safe_decimal(sell_nums_info.get('sellNums3MonthsRate')),
            'sell_nums_6m': self.safe_int(sell_nums_info.get('sellNums6Months')),
            'sell_nums_6m_diff': self.safe_int(sell_nums_info.get('sellNums6MonthsDiff')),
            'sell_nums_6m_rate': self.safe_decimal(sell_nums_info.get('sellNums6MonthsRate')),
            'sell_nums_1y': self.safe_int(sell_nums_info.get('sellNums1Years')),
            'sell_nums_1y_diff': self.safe_int(sell_nums_info.get('sellNums1YearsDiff')),
            'sell_nums_1y_rate': self.safe_decimal(sell_nums_info.get('sellNums1YearsRate')),
        }
    
    def clean_transaction_count_data(self, trans_count_info: Dict[str, Any]) -> Dict[str, Any]:
        """清洗成交量信息"""
        return {
            'trans_count_1d': self.safe_int(trans_count_info.get('transactionCount1Days')),
            'trans_count_24h': self.safe_int(trans_count_info.get('transactionCount24Hours')),
            'trans_count_48h': self.safe_int(trans_count_info.get('transactionCount48Hours')),
            'trans_count_48h_diff': self.safe_int(trans_count_info.get('transactionCount48HoursDiff')),
            'trans_count_48h_rate': self.safe_decimal(trans_count_info.get('transactionCountRate48Hours')),
            'trans_count_3d': self.safe_int(trans_count_info.get('transactionCount3Days')),
            'trans_count_7d': self.safe_int(trans_count_info.get('transactionCount7Days')),
            'trans_count_15d': self.safe_int(trans_count_info.get('transactionCount15Days')),
            'trans_count_1m': self.safe_int(trans_count_info.get('transactionCount1Months')),
            'trans_count_3m': self.safe_int(trans_count_info.get('transactionCount3Months')),
        }
    
    def clean_transaction_amount_data(self, trans_amount_info: Dict[str, Any]) -> Dict[str, Any]:
        """清洗成交额信息"""
        return {
            'trans_amount_1d': self.safe_decimal(trans_amount_info.get('transactionAmount1Days')),
            'trans_amount_24h': self.safe_decimal(trans_amount_info.get('transactionAmount24Hours')),
            'trans_amount_48h': self.safe_decimal(trans_amount_info.get('transactionAmount48Hours')),
            'trans_amount_3d': self.safe_decimal(trans_amount_info.get('transactionAmount3Days')),
            'trans_amount_7d': self.safe_decimal(trans_amount_info.get('transactionAmount7Days')),
            'trans_amount_15d': self.safe_decimal(trans_amount_info.get('transactionAmount15Days')),
            'trans_amount_1m': self.safe_decimal(trans_amount_info.get('transactionAmount1Months')),
            'trans_amount_3m': self.safe_decimal(trans_amount_info.get('transactionAmount3Months')),
        }
    
    def clean_hot_data(self, hot_info: Dict[str, Any]) -> Dict[str, Any]:
        """清洗热度信息"""
        return {
            'hot_count': self.safe_int(hot_info.get('hotCount')),
            'hot_rank': self.safe_int(hot_info.get('hotRank')),
            'hot_keep_days': self.safe_int(hot_info.get('hotKeepDays')),
            'hot_rank_change': self.safe_int(hot_info.get('hotRankChange')),
        }
    
    def clean_platform_price_data(self, platform_info: Dict[str, Any], 
                                 item_id: str, snapshot_time: datetime) -> Dict[str, Any]:
        """清洗平台价格信息"""
        return {
            'item_id': item_id,
            'snapshot_time': snapshot_time,
            'platform_enum': self.safe_str(platform_info.get('platformEnum'), 50),
            'platform_name': self.safe_str(platform_info.get('platformName'), 100),
            'price': self.safe_decimal(platform_info.get('price')),
            'update_time': self.safe_int(platform_info.get('updateTime')),
            'link_url': self.safe_str(platform_info.get('linkUrl')),
        }
    
    def validate_item_data(self, item_data: Dict[str, Any]) -> bool:
        """验证饰品数据的完整性"""
        # 必须有item_id和name
        if not item_data.get('item_id') or not item_data.get('name'):
            return False
        
        # item_id长度检查
        if len(item_data['item_id']) > 50:
            return False
        
        return True
    
    def validate_snapshot_data(self, snapshot_data: Dict[str, Any]) -> bool:
        """验证快照数据的完整性"""
        # 必须有item_id、snapshot_time和data_source
        required_fields = ['item_id', 'snapshot_time', 'data_source']
        for field in required_fields:
            if not snapshot_data.get(field):
                return False
        
        return True
