"""
应用调度器服务

管理应用启动时的定时任务，包括价格更新等后台任务。
"""

import streamlit as st
import logging
import threading
import atexit
from typing import Optional, Dict, Any
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.services.integrated_scheduler import IntegratedScheduler


class AppScheduler:
    """应用调度器服务"""
    
    _instance: Optional['AppScheduler'] = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化应用调度器"""
        if hasattr(self, '_initialized'):
            return
        
        self.logger = logging.getLogger(__name__)
        self.scheduler: Optional[IntegratedScheduler] = None
        self.is_enabled = False
        self._initialized = True
        
        # 注册应用退出时的清理函数
        atexit.register(self.cleanup)
    
    def initialize(self, api_key: str, enable_scheduler: bool = True) -> bool:
        """
        初始化调度器
        
        Args:
            api_key: SteamDT API密钥
            enable_scheduler: 是否启用调度器
            
        Returns:
            bool: 初始化是否成功
        """
        if self.scheduler is not None:
            self.logger.warning("调度器已经初始化")
            return True
        
        try:
            if not api_key:
                self.logger.warning("API密钥为空，跳过调度器初始化")
                return False
            
            if not enable_scheduler:
                self.logger.info("调度器被禁用")
                return False
            
            self.logger.info("🚀 初始化应用调度器")
            
            # 创建集成调度器
            self.scheduler = IntegratedScheduler(api_key)
            
            # 启动调度器（兼容Streamlit环境）
            import asyncio
            import threading

            # 检查是否已有事件循环在运行
            try:
                loop = asyncio.get_running_loop()
                # 如果有运行中的循环，在新线程中启动调度器
                self.logger.info("检测到运行中的事件循环，在新线程中启动调度器")

                def start_scheduler_in_thread():
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        new_loop.run_until_complete(self.scheduler.start())
                        self.is_enabled = True
                        self.logger.info("✅ 调度器在后台线程中启动成功")
                    except Exception as e:
                        self.logger.error(f"后台线程启动调度器失败: {e}")
                    finally:
                        new_loop.close()

                # 在守护线程中启动
                scheduler_thread = threading.Thread(target=start_scheduler_in_thread, daemon=True)
                scheduler_thread.start()

                # 等待一小段时间确保启动
                import time
                time.sleep(2)

            except RuntimeError:
                # 没有运行中的循环，可以直接启动
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(self.scheduler.start())
                    self.is_enabled = True
                finally:
                    loop.close()
            
            self.logger.info("✅ 应用调度器初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 应用调度器初始化失败: {e}")
            self.scheduler = None
            self.is_enabled = False
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        if not self.scheduler:
            return {
                'enabled': False,
                'running': False,
                'message': '调度器未初始化'
            }

        try:
            status = self.scheduler.get_status()
            status['enabled'] = self.is_enabled

            # 检查智能更新池状态
            if hasattr(self.scheduler, 'smart_update_pool'):
                pool_status = self.scheduler.smart_update_pool.get_status()
                status['pool_running'] = pool_status.get('is_running', False)
                status['queue_status'] = pool_status.get('queue_status', {})
                status['update_stats'] = pool_status.get('update_stats', {})

            return status
        except Exception as e:
            return {
                'enabled': self.is_enabled,
                'running': False,
                'error': str(e)
            }
    
    def trigger_price_update(self) -> bool:
        """手动触发智能价格更新"""
        if not self.scheduler:
            self.logger.error("调度器未初始化")
            return False

        try:
            # 兼容Streamlit环境的异步调用
            import asyncio
            import threading

            result = {'success': False}

            def run_update():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    new_loop.run_until_complete(self.scheduler.trigger_smart_price_update_now())
                    result['success'] = True
                except Exception as e:
                    self.logger.error(f"手动更新执行失败: {e}")
                    result['error'] = str(e)
                finally:
                    new_loop.close()

            # 在新线程中执行
            update_thread = threading.Thread(target=run_update)
            update_thread.start()
            update_thread.join(timeout=30)  # 最多等待30秒

            return result['success']

        except Exception as e:
            self.logger.error(f"触发智能价格更新失败: {e}")
            return False
    
    def update_config(self, config: Dict[str, Any]) -> bool:
        """更新调度器配置"""
        if not self.scheduler:
            self.logger.error("调度器未初始化")
            return False
        
        try:
            self.scheduler.update_config(config)
            return True
        except Exception as e:
            self.logger.error(f"更新配置失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        if self.scheduler:
            try:
                self.logger.info("🧹 清理应用调度器资源")

                # 确保在主线程中停止调度器
                import threading
                if threading.current_thread() is threading.main_thread():
                    self.scheduler.stop()
                else:
                    # 如果不在主线程，记录日志但不强制停止
                    self.logger.warning("在非主线程中清理调度器，跳过停止操作")

                self.scheduler = None
                self.is_enabled = False
            except Exception as e:
                self.logger.error(f"清理调度器资源失败: {e}")


# 全局调度器实例
_app_scheduler = AppScheduler()


def get_app_scheduler() -> AppScheduler:
    """获取应用调度器实例"""
    return _app_scheduler


def initialize_scheduler_on_startup():
    """在应用启动时初始化调度器"""
    try:
        # 从Streamlit secrets获取API密钥
        api_key = st.secrets.get("steamdt_api_key", "")
        
        # 检查是否启用调度器
        enable_scheduler = st.secrets.get("enable_scheduler", True)
        
        if not api_key:
            st.warning("⚠️ 未配置SteamDT API密钥，定时价格更新功能将不可用")
            return False
        
        # 初始化调度器
        scheduler = get_app_scheduler()
        success = scheduler.initialize(api_key, enable_scheduler)
        
        if success:
            st.success("✅ 定时价格更新服务已启动")
        else:
            st.warning("⚠️ 定时价格更新服务启动失败")
        
        return success
        
    except Exception as e:
        st.error(f"❌ 初始化定时任务失败: {e}")
        return False


def show_scheduler_status():
    """显示调度器状态（用于侧边栏）"""
    scheduler = get_app_scheduler()
    status = scheduler.get_status()
    
    if status.get('enabled') and status.get('running'):
        st.sidebar.success("🟢 定时更新: 运行中")
        
        # 显示下次更新时间
        jobs = status.get('jobs', [])
        price_update_job = next((job for job in jobs if 'price_update' in job['id']), None)
        
        if price_update_job and price_update_job.get('next_run'):
            from datetime import datetime
            try:
                next_run = datetime.fromisoformat(price_update_job['next_run'].replace('Z', '+00:00'))
                time_diff = next_run.replace(tzinfo=None) - datetime.now()
                hours_left = max(0, int(time_diff.total_seconds() / 3600))
                st.sidebar.info(f"⏰ 下次更新: {hours_left}小时后")
            except:
                pass
        
        # 显示最后更新状态
        task_status = status.get('task_status', {})
        last_result = task_status.get('last_price_update_result')
        
        if last_result:
            if last_result.get('success'):
                update_count = last_result.get('updated_items', 0)
                st.sidebar.info(f"📊 上次更新: {update_count}个饰品")
            else:
                st.sidebar.error("❌ 上次更新失败")
    
    elif status.get('enabled'):
        st.sidebar.warning("🟡 定时更新: 启动中")
    else:
        st.sidebar.error("🔴 定时更新: 未启用")


def add_scheduler_controls():
    """添加调度器控制按钮（用于价格管理页面）"""
    scheduler = get_app_scheduler()
    status = scheduler.get_status()
    
    st.subheader("🤖 自动更新服务")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if status.get('enabled') and status.get('running'):
            st.success("✅ 服务运行中")
        elif status.get('enabled'):
            st.warning("⚠️ 服务启动中")
        else:
            st.error("❌ 服务未启用")
    
    with col2:
        if st.button("🚀 立即更新", disabled=not status.get('running')):
            with st.spinner("触发价格更新..."):
                success = scheduler.trigger_price_update()
                if success:
                    st.success("✅ 价格更新任务已触发")
                else:
                    st.error("❌ 触发失败")
    
    with col3:
        if st.button("🔄 刷新状态"):
            st.rerun()
    
    # 显示详细状态
    if status.get('running'):
        st.subheader("📊 服务状态")
        
        # 任务状态
        task_status = status.get('task_status', {})
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            update_count = task_status.get('price_update_count', 0)
            st.metric("总更新次数", update_count)
        
        with col2:
            error_count = task_status.get('price_update_errors', 0)
            st.metric("更新错误", error_count)
        
        with col3:
            last_update = task_status.get('last_price_update')
            if last_update:
                from datetime import datetime
                try:
                    last_time = datetime.fromisoformat(last_update.replace('Z', '+00:00'))
                    time_diff = datetime.now() - last_time.replace(tzinfo=None)
                    hours_ago = int(time_diff.total_seconds() / 3600)
                    st.metric("上次更新", f"{hours_ago}小时前")
                except:
                    st.metric("上次更新", "未知")
            else:
                st.metric("上次更新", "从未")
        
        with col4:
            last_result = task_status.get('last_price_update_result')
            if last_result and last_result.get('success'):
                updated_items = last_result.get('updated_items', 0)
                st.metric("上次更新数量", f"{updated_items}个")
            else:
                st.metric("上次更新数量", "失败")
        
        # 计划任务
        st.subheader("📅 计划任务")
        jobs = status.get('jobs', [])
        
        if jobs:
            job_data = []
            for job in jobs:
                next_run = job.get('next_run')
                if next_run:
                    try:
                        next_time = datetime.fromisoformat(next_run.replace('Z', '+00:00'))
                        formatted_time = next_time.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        formatted_time = next_run
                else:
                    formatted_time = "未计划"
                
                job_data.append({
                    '任务名称': job.get('name', job.get('id')),
                    '下次运行': formatted_time,
                    '触发器': job.get('trigger', '未知')
                })
            
            import pandas as pd
            df = pd.DataFrame(job_data)
            st.dataframe(df, use_container_width=True, hide_index=True)
        else:
            st.info("暂无计划任务")
    
    # 配置选项
    with st.expander("⚙️ 配置选项"):
        config = status.get('config', {})
        
        col1, col2 = st.columns(2)
        
        with col1:
            new_interval = st.number_input(
                "更新间隔(小时)",
                min_value=1,
                max_value=24,
                value=config.get('price_update_interval_hours', 2),
                help="价格更新的间隔时间"
            )
        
        with col2:
            new_cleanup_days = st.number_input(
                "数据保留天数",
                min_value=7,
                max_value=365,
                value=90,
                help="历史数据保留天数"
            )
        
        if st.button("💾 保存配置"):
            new_config = {
                'price_update_interval_hours': new_interval,
                'price_update_cron': f'0 */{new_interval} * * *',
            }
            
            success = scheduler.update_config(new_config)
            if success:
                st.success("✅ 配置已保存")
                st.rerun()
            else:
                st.error("❌ 保存配置失败")
