"""
搬砖数据服务

提供CS2饰品搬砖数据的导入、查询和分析服务。
"""

import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
from loguru import logger

from ..dao.arbitrage_item_dao import ArbitrageItemDAO
from ..dao.item_dao import ItemDAO
from ..models.arbitrage_item import ArbitrageItem
from ..services.data_import_service import DataImportService


class ArbitrageDataService:
    """搬砖数据服务"""
    
    def __init__(self):
        self.arbitrage_dao = ArbitrageItemDAO()
        self.item_dao = ItemDAO()
        self.data_import_service = DataImportService()
        self.logger = logger.bind(service="ArbitrageDataService")
    
    def import_bzdata_files(self, file_paths: List[str]) -> Dict[str, Any]:
        """导入搬砖数据文件"""
        try:
            total_stats = {
                'total_files': len(file_paths),
                'success_files': 0,
                'error_files': 0,
                'total_imported': 0,
                'total_errors': 0,
                'file_results': []
            }
            
            for file_path in file_paths:
                try:
                    self.logger.info(f"开始导入文件: {file_path}")
                    result = self._import_single_bzdata_file(file_path)
                    
                    total_stats['success_files'] += 1
                    total_stats['total_imported'] += result['imported_count']
                    total_stats['file_results'].append({
                        'file': Path(file_path).name,
                        'status': 'success',
                        'imported_count': result['imported_count'],
                        'error_count': result['error_count']
                    })
                    
                    self.logger.info(f"文件导入成功: {Path(file_path).name}, 导入{result['imported_count']}条")
                    
                except Exception as e:
                    total_stats['error_files'] += 1
                    total_stats['file_results'].append({
                        'file': Path(file_path).name,
                        'status': 'error',
                        'error': str(e)
                    })
                    
                    self.logger.error(f"文件导入失败: {Path(file_path).name}, 错误: {e}")
            
            self.logger.info(f"批量导入完成: 成功{total_stats['success_files']}个文件, "
                           f"失败{total_stats['error_files']}个文件, "
                           f"总计导入{total_stats['total_imported']}条记录")
            
            return total_stats
            
        except Exception as e:
            self.logger.error(f"批量导入失败: {e}")
            raise
    
    def _import_single_bzdata_file(self, file_path: str) -> Dict[str, int]:
        """导入单个搬砖数据文件"""
        try:
            # 使用现有的JSON加载方法
            json_data = self.data_import_service.load_json_file(Path(file_path))
            if not json_data:
                return {'imported_count': 0, 'error_count': 1}
            
            # 提取搬砖数据
            arbitrage_items = []
            error_count = 0
            
            for i, item_data in enumerate(json_data):
                try:
                    # 只提取Steam和Youpin平台数据
                    extracted_data = self._extract_platform_data(item_data)
                    if extracted_data:
                        arbitrage_items.append(extracted_data)
                    else:
                        self.logger.debug(f"记录 {i+1} 没有Steam或Youpin数据，跳过")
                        error_count += 1
                except Exception as e:
                    self.logger.warning(f"提取数据失败 (记录 {i+1}): {e}")
                    error_count += 1
            
            # 批量导入数据库
            imported_count = self.arbitrage_dao.batch_import(arbitrage_items)
            
            return {
                'imported_count': imported_count,
                'error_count': error_count
            }
            
        except Exception as e:
            self.logger.error(f"导入单个文件失败: {e}")
            raise
    
    def _extract_platform_data(self, item_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """提取Steam和Youpin平台数据"""
        try:
            # 检查是否有Steam或Youpin数据
            has_steam = any(key.startswith('steam') for key in item_data.keys())
            has_youpin = any(key.startswith('youpin') for key in item_data.keys())

            if not (has_steam or has_youpin):
                self.logger.debug(f"记录缺少Steam和Youpin数据: {item_data.get('marketName', 'Unknown')}")
                return None
            
            # 提取基础信息
            extracted = {
                # 基础信息字段
                'market_name': item_data.get('marketName', ''),
                'market_hash_name': item_data.get('marketHashName', ''),
                'goods_name': item_data.get('goodsName', ''),
                'goods_type01_name': item_data.get('goodsType01Name', ''),
                'goods_level_name': item_data.get('goodsLevelName', ''),
                'goods_quality_name': item_data.get('goodsQualityName', ''),
            }
            
            # 尝试关联到items表（需求3：通过hashname查询itemid）
            if extracted['market_hash_name']:
                try:
                    # 通过market_hash_name查找对应的item_id
                    items = self.item_dao.get_by_filter(market_hash_name=extracted['market_hash_name'])
                    if items:
                        extracted['item_id'] = items[0].item_id
                    # 如果没有查到，允许为空（item_id保持None）
                except Exception as e:
                    self.logger.warning(f"查询item_id失败: {e}")
                    # 允许item_id为空，继续处理
            
            # 提取Steam平台数据
            if has_steam:
                extracted.update({
                    'steam_item_name_id': item_data.get('steamItemNameId'),
                    'steam_price': self._safe_decimal(item_data.get('steamPrice')),
                    'steam_buy_price': self._safe_decimal(item_data.get('steamBuyPrice')),
                    'steam_buy_count': item_data.get('steamBuyCount'),
                    'steam_sell_count': item_data.get('steamSellCount'),
                    'steam_24_sell_count': item_data.get('steam24SellCount'),
                    'steam_buy_count_5': item_data.get('steamBuyCount5'),
                    'steam_buy_price_list': item_data.get('steamBuyPriceList'),
                    'steam_last_update_time': item_data.get('steamLastUpdateTime'),
                    'steam_last_check_time': item_data.get('steamLastCheckTime'),
                    'steam_sell_count_last_update_time': item_data.get('steamSellCountLastUpdateTime'),
                    'steam_sell_count_last_check_time': item_data.get('steamSellCountLastCheckTime'),
                    'steam_update_interval': item_data.get('steamUpdateInterval'),
                    'steam_last_update_time_desc': item_data.get('steamLastUpdateTimeDesc'),
                })
            
            # 提取Youpin平台数据
            if has_youpin:
                extracted.update({
                    'youpin_item_id': item_data.get('youpinItemId'),
                    'youpin_price': self._safe_decimal(item_data.get('youpinPrice')),
                    'youpin_sell_count': item_data.get('youpinSellCount'),
                    'youpin_purchase_price': self._safe_decimal(item_data.get('youpinPurchasePrice')),
                    'youpin_purchase_count': item_data.get('youpinPurchaseCount'),
                    'youpin_last_update_time': item_data.get('youpinLastUpdateTime'),
                    'youpin_update_interval': item_data.get('youpinUpdateInterval'),
                    'youpin_last_check_time': item_data.get('youpinLastCheckTime'),
                    'youpin_last_transaction_update_time': item_data.get('youpinLastTransactionUpdateTime'),
                    'youpin_last_update_time_desc': item_data.get('youpinLastUpdateTimeDesc'),
                    'youpin_profit': self._safe_decimal(item_data.get('youpinProfit')),
                    'youpin_profit_rate': self._safe_decimal(item_data.get('youpinProfitRate')),
                })
            
            # 提取其他有用字段
            extracted.update({
                'collect_id': item_data.get('collectId'),
                'buy_cart_id': item_data.get('buyCartId'),
                'inventory_num': item_data.get('inventoryNum'),
                'slow_closing_flag': item_data.get('slowClosingFlag'),
                'fast_closing_flag': item_data.get('fastClosingFlag'),
                'takeover_flag': item_data.get('takeoverFlag'),
                'blood_earned_flag': item_data.get('bloodEarnedFlag'),
                'blood_loss_flag': item_data.get('bloodLossFlag'),
                'has_buy_desc': item_data.get('hasBuyDesc'),
                'is_black': item_data.get('isBlack'),
            })
            
            return extracted
            
        except Exception as e:
            self.logger.error(f"提取平台数据失败: {e}")
            return None
    
    def _safe_decimal(self, value: Any) -> Optional[Decimal]:
        """安全转换为Decimal类型"""
        if value is None:
            return None
        try:
            return Decimal(str(value))
        except (ValueError, TypeError):
            return None
    
    def query_arbitrage_items(self, **filters) -> Tuple[List[ArbitrageItem], int]:
        """查询搬砖数据"""
        try:
            return self.arbitrage_dao.find_by_conditions(**filters)
        except Exception as e:
            self.logger.error(f"查询搬砖数据失败: {e}")
            raise
    
    def find_arbitrage_opportunities(self, min_profit_rate: float = 0.1, 
                                   min_profit_amount: Optional[float] = None,
                                   limit: int = 50) -> List[ArbitrageItem]:
        """查找搬砖机会"""
        try:
            min_profit_rate_decimal = Decimal(str(min_profit_rate))
            min_profit_amount_decimal = Decimal(str(min_profit_amount)) if min_profit_amount else None
            
            return self.arbitrage_dao.find_arbitrage_opportunities(
                min_profit_rate=min_profit_rate_decimal,
                min_profit_amount=min_profit_amount_decimal,
                limit=limit
            )
        except Exception as e:
            self.logger.error(f"查找搬砖机会失败: {e}")
            raise
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取搬砖数据统计"""
        try:
            return self.arbitrage_dao.get_statistics()
        except Exception as e:
            self.logger.error(f"获取统计数据失败: {e}")
            raise
    
    def get_filter_options(self) -> Dict[str, List[str]]:
        """获取筛选选项"""
        try:
            return {
                'goods_types': self.arbitrage_dao.get_distinct_values('goods_type01_name'),
                'goods_levels': self.arbitrage_dao.get_distinct_values('goods_level_name'),
                'goods_qualities': self.arbitrage_dao.get_distinct_values('goods_quality_name'),
            }
        except Exception as e:
            self.logger.error(f"获取筛选选项失败: {e}")
            raise
    
    def validate_bzdata_file(self, file_path: str) -> Dict[str, Any]:
        """验证搬砖数据文件格式"""
        try:
            json_data = self.data_import_service.load_json_file(Path(file_path))
            if not json_data:
                return {'valid': False, 'error': '文件加载失败'}
            
            # 检查数据格式
            if not isinstance(json_data, list):
                return {'valid': False, 'error': '数据格式错误，期望数组格式'}
            
            if len(json_data) == 0:
                return {'valid': False, 'error': '文件为空'}
            
            # 检查是否包含Steam或Youpin数据
            sample_item = json_data[0]
            has_steam = any(key.startswith('steam') for key in sample_item.keys())
            has_youpin = any(key.startswith('youpin') for key in sample_item.keys())
            
            if not (has_steam or has_youpin):
                return {'valid': False, 'error': '文件不包含Steam或Youpin平台数据'}
            
            return {
                'valid': True,
                'total_items': len(json_data),
                'has_steam': has_steam,
                'has_youpin': has_youpin,
                'sample_fields': list(sample_item.keys())[:10]  # 显示前10个字段作为示例
            }
            
        except Exception as e:
            return {'valid': False, 'error': str(e)}
