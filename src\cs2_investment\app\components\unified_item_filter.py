"""
统一饰品过滤组件

提供统一的饰品过滤功能，支持不同页面的过滤需求差异化。
通过配置驱动的设计模式，整合现有的过滤逻辑，实现代码复用。
"""

import streamlit as st
from typing import Dict, Any, List, Optional, Callable, Tuple
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.app.services.item_service import ItemService
from src.cs2_investment.config.streamlit_config import get_streamlit_config


# 预设配置字典
FILTER_TYPE_CONFIGS = {
    'query': {
        'enabled_filters': ['name', 'item_type', 'quality', 'rarity', 'exterior', 'price_range', 'sell_count_range', 'arbitrage_card_price', 'arbitrage_threshold', 'sort'],
        'layout_columns': 4,
        'show_arbitrage_calculation': False,
        'button_text': '🔍 查询',
        'button_type': 'primary'
    },
    'custom': {
        'enabled_filters': ['name'],
        'layout_columns': 3,
        'show_arbitrage_calculation': False,
        'button_text': '🔍 筛选',
        'button_type': 'primary'
    }
}


def render_item_filter(
    filter_type: str = 'custom',
    enabled_filters: Optional[List[str]] = None,
    layout_columns: Optional[int] = None,
    on_filter_change: Optional[Callable] = None,
    key_suffix: str = "",
    **kwargs
) -> Dict[str, Any]:
    """
    渲染统一的饰品过滤组件
    
    Args:
        filter_type: 过滤器类型，可选值：'query', 'favorite', 'custom'
        enabled_filters: 启用的过滤项列表，None时使用预设配置
        layout_columns: 布局列数，None时使用预设配置
        on_filter_change: 过滤条件改变时的回调函数
        key_suffix: 唯一标识后缀，用于避免不同页面间的状态冲突
        **kwargs: 其他自定义参数
    
    Returns:
        Dict[str, Any]: 标准化的查询参数字典
    """
    # 获取预设配置
    config = FILTER_TYPE_CONFIGS.get(filter_type, FILTER_TYPE_CONFIGS['custom'])

    # 配置合并机制：自定义参数覆盖预设配置
    final_enabled_filters = enabled_filters if enabled_filters is not None else config.get('enabled_filters', ['name'])
    final_layout_columns = layout_columns if layout_columns is not None else config.get('layout_columns', 3)
    final_show_arbitrage = kwargs.get('show_arbitrage_calculation', config.get('show_arbitrage_calculation', False))
    final_button_text = kwargs.get('button_text', config.get('button_text', '🔍 筛选'))
    final_button_type = kwargs.get('button_type', config.get('button_type', 'primary'))

    # 验证配置参数
    if not isinstance(final_enabled_filters, list):
        st.error("enabled_filters 必须是列表类型")
        final_enabled_filters = ['name']

    if not isinstance(final_layout_columns, int) or final_layout_columns < 1:
        st.warning(f"layout_columns 必须是正整数，当前值: {final_layout_columns}，使用默认值 3")
        final_layout_columns = 3
    
    # 初始化服务和过滤选项（带性能优化）
    filter_options = _init_filter_options(key_suffix)

    # 初始化查询参数
    query_params = {
        'name_query': None,
        'item_types': None,
        'qualities': None,
        'rarities': None,
        'price_range': None,
        'arbitrage_threshold': None,
        'sort_by': 'updated_desc',
        'button_clicked': False
    }

    # 智能布局渲染逻辑
    columns = _create_layout_columns(final_layout_columns)

    # 验证过滤项配置
    valid_filters = ['name', 'item_type', 'quality', 'rarity', 'exterior', 'price_range', 'sell_count_range', 'arbitrage_card_price', 'arbitrage_threshold', 'sort']
    final_enabled_filters = [f for f in final_enabled_filters if f in valid_filters]

    if not final_enabled_filters:
        st.warning("没有有效的过滤项，使用默认的名称搜索")
        final_enabled_filters = ['name']
    
    # 渲染过滤项
    col_index = 0
    
    # 名称搜索
    if 'name' in final_enabled_filters:
        with columns[col_index % len(columns)]:
            query_params['name_query'] = _render_name_filter(key_suffix)
        col_index += 1
    
    # 饰品类型
    if 'item_type' in final_enabled_filters:
        with columns[col_index % len(columns)]:
            query_params['item_types'] = _render_item_type_filter(filter_options, key_suffix)
        col_index += 1
    
    # 品质选择
    if 'quality' in final_enabled_filters:
        with columns[col_index % len(columns)]:
            query_params['qualities'] = _render_quality_filter(filter_options, key_suffix)
        col_index += 1
    
    # 稀有度选择
    if 'rarity' in final_enabled_filters:
        with columns[col_index % len(columns)]:
            query_params['rarities'] = _render_rarity_filter(filter_options, key_suffix)
        col_index += 1

    # 磨损选择
    if 'exterior' in final_enabled_filters:
        with columns[col_index % len(columns)]:
            query_params['exteriors'] = _render_exterior_filter(filter_options, key_suffix)
        col_index += 1

    # 价格范围
    if 'price_range' in final_enabled_filters:
        with columns[col_index % len(columns)]:
            query_params['price_range'] = _render_price_range_filter(key_suffix)
        col_index += 1

    # 在售数量范围
    if 'sell_count_range' in final_enabled_filters:
        with columns[col_index % len(columns)]:
            query_params['sell_count_range'] = _render_sell_count_range_filter(key_suffix)
        col_index += 1

    # 搬砖卡价
    arbitrage_card_price_value = None
    if 'arbitrage_card_price' in final_enabled_filters:
        with columns[col_index % len(columns)]:
            arbitrage_card_price_value = _render_arbitrage_card_price_filter(key_suffix)
            if arbitrage_card_price_value is not None and arbitrage_card_price_value > 0:
                query_params['arbitrage_card_price'] = arbitrage_card_price_value
        col_index += 1

    # 搬砖率阈值
    if 'arbitrage_threshold' in final_enabled_filters:
        with columns[col_index % len(columns)]:
            arbitrage_threshold_value = _render_arbitrage_threshold_filter(key_suffix)
            if arbitrage_threshold_value is not None and arbitrage_threshold_value > 0:
                query_params['arbitrage_threshold'] = arbitrage_threshold_value
        col_index += 1

    # 排序方式
    if 'sort' in final_enabled_filters:
        with columns[col_index % len(columns)]:
            query_params['sort_by'] = _render_sort_filter(key_suffix)
        col_index += 1

    # 查询按钮和重置按钮
    _render_filter_buttons(final_button_text, final_button_type, key_suffix, query_params)

    # 构建标准化查询参数
    standardized_params = _build_query_params(query_params)

    # 处理回调机制
    if on_filter_change:
        # 支持实时回调（任何过滤条件改变时）
        # 注意：在streamlit中，组件值改变会触发重新运行，所以这里会被调用
        if query_params.get('button_clicked'):
            # 按钮回调：用户点击查询按钮时
            on_filter_change(standardized_params)
        # 可以在这里添加实时回调逻辑，但需要小心避免无限循环

    # 返回标准化的查询参数
    return standardized_params


def _build_query_params(raw_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    构建标准化的查询参数

    Args:
        raw_params: 原始查询参数字典

    Returns:
        Dict[str, Any]: 标准化的查询参数，兼容ItemService和FavoriteService接口
    """
    # 基础参数
    standardized_params = {
        'name_query': raw_params.get('name_query'),
        'item_types': raw_params.get('item_types'),
        'qualities': raw_params.get('qualities'),
        'rarities': raw_params.get('rarities'),
        'exteriors': raw_params.get('exteriors'),
        'arbitrage_threshold': raw_params.get('arbitrage_threshold'),
        'arbitrage_card_price': raw_params.get('arbitrage_card_price'),
        'sort_by': raw_params.get('sort_by', 'updated_desc'),
        'button_clicked': raw_params.get('button_clicked', False)
    }

    # 处理价格范围：从tuple转换为price_min和price_max
    price_range = raw_params.get('price_range')
    if price_range and isinstance(price_range, tuple) and len(price_range) == 2:
        standardized_params['price_min'] = price_range[0]
        standardized_params['price_max'] = price_range[1]
        standardized_params['price_range'] = price_range  # 保留原始格式以便兼容
    else:
        standardized_params['price_min'] = None
        standardized_params['price_max'] = None
        standardized_params['price_range'] = None

    # 处理在售数量范围：从tuple转换为sell_count_min和sell_count_max
    sell_count_range = raw_params.get('sell_count_range')
    if sell_count_range and isinstance(sell_count_range, tuple) and len(sell_count_range) == 2:
        standardized_params['sell_count_min'] = sell_count_range[0]
        standardized_params['sell_count_max'] = sell_count_range[1]
        standardized_params['sell_count_range'] = sell_count_range  # 保留原始格式以便兼容
    else:
        standardized_params['sell_count_min'] = None
        standardized_params['sell_count_max'] = None
        standardized_params['sell_count_range'] = None

    # 清理None值和空列表（可选，根据服务接口需求）
    cleaned_params = {}
    for key, value in standardized_params.items():
        if value is not None:
            # 对于列表类型，只保留非空列表
            if isinstance(value, list) and len(value) == 0:
                continue
            # 对于字符串类型，只保留非空字符串
            if isinstance(value, str) and value.strip() == '':
                continue
            cleaned_params[key] = value

    return cleaned_params


def _init_filter_options(key_suffix: str) -> Dict[str, List[str]]:
    """
    初始化过滤选项（带性能优化和缓存机制）

    Args:
        key_suffix: 状态隔离后缀

    Returns:
        Dict[str, List[str]]: 过滤选项字典
    """
    try:
        # 服务初始化（单例模式）
        service_key = f'item_service_{key_suffix}' if key_suffix else 'item_service'
        if service_key not in st.session_state:
            with st.spinner("初始化服务..."):
                st.session_state[service_key] = ItemService()

        # 过滤选项缓存（带时间戳）
        options_key = f'filter_options_{key_suffix}' if key_suffix else 'filter_options'
        cache_time_key = f'filter_options_cache_time_{key_suffix}' if key_suffix else 'filter_options_cache_time'

        # 检查缓存是否存在且有效（缓存5分钟）
        import time
        current_time = time.time()
        cache_valid = (
            options_key in st.session_state and
            cache_time_key in st.session_state and
            current_time - st.session_state[cache_time_key] < 300  # 5分钟缓存
        )

        if not cache_valid:
            with st.spinner("加载筛选选项..."):
                filter_options = st.session_state[service_key].get_filter_options()
                # 调试信息
                print(f"🔍 [过滤器调试] 获取到的过滤选项: {filter_options}")
                st.session_state[options_key] = filter_options
                st.session_state[cache_time_key] = current_time

                # 验证数据完整性
                if not isinstance(filter_options, dict):
                    raise ValueError("过滤选项格式错误")

                required_keys = ['item_types', 'qualities', 'rarities', 'exteriors']
                for key in required_keys:
                    if key not in filter_options:
                        st.warning(f"缺少过滤选项: {key}")
                        filter_options[key] = []

        return st.session_state[options_key]

    except Exception as e:
        st.error(f"初始化服务失败: {str(e)}")
        st.info("请检查数据库连接配置，使用默认选项")

        # 返回默认选项
        default_options = {
            'item_types': ['武器', '手套', '刀具', '印花', '音乐盒'],
            'qualities': ['崭新出厂', '略有磨损', '久经沙场', '破损不堪', '战痕累累'],
            'rarities': ['消费级', '工业级', '军规级', '受限', '保密', '隐秘', '非凡', '特殊物品'],
            'exteriors': ['Factory New', 'Minimal Wear', 'Field-Tested', 'Well-Worn', 'Battle-Scarred']
        }

        # 缓存默认选项
        options_key = f'filter_options_{key_suffix}' if key_suffix else 'filter_options'
        st.session_state[options_key] = default_options

        return default_options


def _render_name_filter(key_suffix: str) -> str:
    """渲染名称搜索过滤器"""
    return st.text_input(
        "饰品名称",
        placeholder="输入饰品名称关键词...",
        help="支持模糊搜索",
        key=f"name_filter_{key_suffix}"
    )


def _render_item_type_filter(filter_options: Dict, key_suffix: str) -> List[str]:
    """渲染饰品类型过滤器"""
    item_types = filter_options.get('item_types', [])
    return st.multiselect(
        "饰品类型",
        options=item_types,
        help="选择一个或多个饰品类型",
        key=f"item_type_filter_{key_suffix}"
    )


def _render_quality_filter(filter_options: Dict, key_suffix: str) -> List[str]:
    """渲染品质过滤器"""
    qualities = filter_options.get('qualities', [])
    return st.multiselect(
        "品质",
        options=qualities,
        help="选择饰品品质",
        key=f"quality_filter_{key_suffix}"
    )


def _render_rarity_filter(filter_options: Dict, key_suffix: str) -> List[str]:
    """渲染稀有度过滤器"""
    rarities = filter_options.get('rarities', [])
    return st.multiselect(
        "稀有度",
        options=rarities,
        help="选择稀有度等级",
        key=f"rarity_filter_{key_suffix}"
    )


def _render_exterior_filter(filter_options: Dict, key_suffix: str) -> List[str]:
    """渲染磨损过滤器"""
    exteriors = filter_options.get('exteriors', [])
    return st.multiselect(
        "磨损",
        options=exteriors,
        help="选择饰品磨损程度",
        key=f"exterior_filter_{key_suffix}"
    )


def _render_price_range_filter(key_suffix: str) -> Tuple[float, float]:
    """渲染价格范围过滤器"""
    st.write("价格范围 (¥)")
    col1, col2 = st.columns(2)

    with col1:
        min_price = st.number_input(
            "最低价格",
            min_value=0.0,
            max_value=100000.0,
            value=0.0,
            step=1.0,
            help="设置最低价格",
            key=f"price_min_filter_{key_suffix}"
        )

    with col2:
        max_price = st.number_input(
            "最高价格",
            min_value=0.0,
            max_value=100000.0,
            value=10000.0,
            step=1.0,
            help="设置最高价格",
            key=f"price_max_filter_{key_suffix}"
        )

    return (min_price, max_price)


def _render_sell_count_range_filter(key_suffix: str) -> Tuple[int, int]:
    """渲染在售数量范围过滤器"""
    st.write("在售数量范围")
    col1, col2 = st.columns(2)

    with col1:
        min_count = st.number_input(
            "最少在售数量",
            min_value=0,
            max_value=100000,
            value=0,
            step=1,
            help="设置最少在售数量（steamdt数据源）",
            key=f"sell_count_min_filter_{key_suffix}"
        )

    with col2:
        max_count = st.number_input(
            "最多在售数量",
            min_value=0,
            max_value=100000,
            value=100000,
            step=1,
            help="设置最多在售数量（steamdt数据源）",
            key=f"sell_count_max_filter_{key_suffix}"
        )

    return (min_count, max_count)


def _render_arbitrage_card_price_filter(key_suffix: str) -> Optional[float]:
    """渲染搬砖卡价过滤器"""
    return st.number_input(
        "搬砖卡价",
        min_value=0.0,
        max_value=20.0,
        value=None,
        step=0.01,
        placeholder="请输入卡价",
        help="输入搬砖卡价用于计算求购利润",
        key=f"arbitrage_card_price_filter_{key_suffix}"
    )


def _render_arbitrage_threshold_filter(key_suffix: str) -> Optional[float]:
    """渲染搬砖率过滤器"""
    return st.number_input(
        "搬砖率阈值",
        min_value=0.0,
        max_value=10.0,
        value=None,
        step=0.01,
        placeholder="请输入搬砖率",
        help="输入搬砖率阈值（如1.05），查询搬砖率大于此值的饰品",
        key=f"arbitrage_threshold_filter_{key_suffix}"
    )


def _render_sort_filter(key_suffix: str) -> str:
    """渲染排序过滤器"""
    sort_options = {
        "name_asc": "名称 (A-Z)",
        "name_desc": "名称 (Z-A)",
        "price_asc": "价格 (低到高)",
        "price_desc": "价格 (高到低)",
        "updated_desc": "最新更新",
        "arbitrage_desc": "搬砖率 (高到低)",
        "purchase_profit_desc": "求购利润 (高到低)",
        "purchase_profit_rate_desc": "求购利润率 (高到低)"
    }
    return st.selectbox(
        "排序方式",
        options=list(sort_options.keys()),
        format_func=lambda x: sort_options[x],
        index=4,  # 默认按最新更新排序
        key=f"sort_filter_{key_suffix}"
    )


def _calculate_arbitrage_threshold(arbitrage_card_price: float) -> Optional[float]:
    """计算搬砖率阈值"""
    try:
        streamlit_config = get_streamlit_config()
        real_rate = streamlit_config.real_exchange_rate
        return arbitrage_card_price / real_rate
    except Exception as e:
        st.error(f"搬砖卡价计算失败: {e}")
        return None


def _show_arbitrage_calculation_info(arbitrage_threshold: Optional[float], arbitrage_card_price: Optional[float] = None):
    """显示搬砖卡价计算信息"""
    if arbitrage_threshold is not None:
        try:
            streamlit_config = get_streamlit_config()
            real_rate = streamlit_config.real_exchange_rate
            # 如果没有传入原始卡价，则从阈值反推
            display_card_price = arbitrage_card_price if arbitrage_card_price is not None else arbitrage_threshold * real_rate

            st.info(f"💡 搬砖卡价计算: {display_card_price:.2f} ÷ {real_rate} = {arbitrage_threshold:.6f}")
            st.caption(f"将查询搬砖率 ≥ {arbitrage_threshold:.6f} 的饰品")
        except Exception as e:
            st.error(f"搬砖卡价计算显示失败: {e}")


def _create_layout_columns(layout_columns: int):
    """
    创建布局列

    Args:
        layout_columns: 列数

    Returns:
        List: streamlit列对象列表
    """
    try:
        if layout_columns == 1:
            return [st.container()]
        elif layout_columns == 2:
            return st.columns(2)
        elif layout_columns == 3:
            return st.columns(3)
        elif layout_columns == 4:
            return st.columns(4)
        elif layout_columns <= 6:
            return st.columns(layout_columns)
        else:
            # 对于超过6列的情况，使用等宽列
            st.warning(f"列数过多 ({layout_columns})，建议不超过6列以保证用户体验")
            return st.columns(min(layout_columns, 6))
    except Exception as e:
        st.error(f"创建布局列失败: {e}")
        # 返回默认的3列布局
        return st.columns(3)


def _render_filter_button(button_text: str, button_type: str, key_suffix: str, query_params: Dict[str, Any]):
    """渲染过滤按钮（保留兼容性）"""
    col1, col2, col3 = st.columns([1, 1, 1])
    with col2:
        if st.button(button_text, type=button_type, use_container_width=True, key=f"filter_button_{key_suffix}"):
            query_params['button_clicked'] = True


def _render_filter_buttons(button_text: str, button_type: str, key_suffix: str, query_params: Dict[str, Any]):
    """渲染查询和重置按钮"""
    col1, col2, col3 = st.columns([1, 1, 1])

    # 查询按钮
    with col1:
        if st.button(button_text, type=button_type, use_container_width=True, key=f"filter_button_{key_suffix}"):
            query_params['button_clicked'] = True

    # 重置按钮
    with col3:
        if st.button("🔄 重置", type="secondary", use_container_width=True, key=f"reset_button_{key_suffix}"):
            # 清除所有过滤器相关的session_state
            keys_to_clear = []
            filter_patterns = [
                'name_filter', 'item_type_filter', 'quality_filter', 'rarity_filter',
                'price_min_filter', 'price_max_filter', 'sell_count_min_filter', 'sell_count_max_filter',
                'arbitrage_card_price_filter', 'arbitrage_threshold_filter', 'sort_filter'
            ]

            # 调试信息：显示当前session_state中的所有keys
            st.write(f"🔍 调试信息 - key_suffix: {key_suffix}")
            st.write(f"🔍 当前session_state keys: {list(st.session_state.keys())}")

            # 如果有key_suffix，优先清除带suffix的keys
            if key_suffix:
                for pattern in filter_patterns:
                    key_to_clear = f"{pattern}_{key_suffix}"
                    if key_to_clear in st.session_state:
                        keys_to_clear.append(key_to_clear)
                        st.write(f"✅ 找到要清除的key: {key_to_clear}")
                    else:
                        st.write(f"❌ 未找到key: {key_to_clear}")
            else:
                # 如果没有key_suffix，清除所有匹配的keys
                for key in list(st.session_state.keys()):
                    if any(pattern in key for pattern in filter_patterns):
                        keys_to_clear.append(key)

            st.write(f"🗑️ 将要清除的keys: {keys_to_clear}")

            # 清除找到的keys
            for key in keys_to_clear:
                if key in st.session_state:
                    del st.session_state[key]
                    st.write(f"✅ 已清除key: {key}")

            # 重置后自动执行查询
            query_params['button_clicked'] = True

            # 触发页面重新渲染
            st.rerun()
