"""
持仓交易记录数据访问对象

提供交易记录相关的数据库操作。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import desc, and_, or_, func
from decimal import Decimal

from .base_dao import BaseDAO
from ..models.holding_transaction import HoldingTransaction
from ..config.database import get_db_session


class HoldingTransactionDAO(BaseDAO[HoldingTransaction]):
    """交易记录DAO"""
    
    def __init__(self):
        super().__init__(HoldingTransaction)
    
    def add_transaction(self, user_id: str, item_id: str, item_name: str,
                       transaction_type: str, quantity: int, price: Decimal,
                       transaction_date: datetime, fee: Decimal = None,
                       notes: str = None) -> dict:
        """添加交易记录"""
        try:
            with get_db_session() as session:
                total_amount = price * quantity

                transaction = HoldingTransaction(
                    user_id=user_id,
                    item_id=item_id,
                    item_name=item_name,
                    transaction_type=transaction_type,
                    quantity=quantity,
                    price=price,
                    total_amount=total_amount,
                    fee=fee or Decimal('0'),
                    transaction_date=transaction_date,
                    notes=notes
                )

                session.add(transaction)
                session.flush()
                session.refresh(transaction)

                # 在会话内提取所有需要的数据
                result = {
                    'id': transaction.id,
                    'user_id': transaction.user_id,
                    'item_id': transaction.item_id,
                    'item_name': transaction.item_name,
                    'transaction_type': transaction.transaction_type,
                    'quantity': transaction.quantity,
                    'price': transaction.price,
                    'total_amount': transaction.total_amount,
                    'fee': transaction.fee,
                    'transaction_date': transaction.transaction_date,
                    'notes': transaction.notes,
                    'created_at': transaction.created_at
                }

                self.logger.info(f"添加交易记录成功: user_id={user_id}, item_id={item_id}, type={transaction_type}, quantity={quantity}")
                return result

        except SQLAlchemyError as e:
            self.logger.error(f"添加交易记录失败: {e}")
            raise
    
    def get_user_transactions(self, user_id: str, limit: int = 1000, offset: int = 0,
                             transaction_type: str = None, item_id: str = None,
                             start_date: datetime = None, end_date: datetime = None) -> List[Dict[str, Any]]:
        """获取用户交易记录列表，支持条件筛选"""
        try:
            with get_db_session() as session:
                query = session.query(HoldingTransaction).filter(
                    HoldingTransaction.user_id == user_id
                )

                # 添加筛选条件
                if transaction_type:
                    query = query.filter(HoldingTransaction.transaction_type == transaction_type)
                
                if item_id:
                    query = query.filter(HoldingTransaction.item_id == item_id)
                
                if start_date:
                    query = query.filter(HoldingTransaction.transaction_date >= start_date)
                
                if end_date:
                    query = query.filter(HoldingTransaction.transaction_date <= end_date)

                transactions = query.order_by(desc(HoldingTransaction.transaction_date))\
                    .offset(offset).limit(limit).all()

                # 立即转换为字典格式
                results = []
                for trans in transactions:
                    trans_dict = {
                        'id': trans.id,
                        'user_id': trans.user_id,
                        'item_id': trans.item_id,
                        'item_name': trans.item_name,
                        'transaction_type': trans.transaction_type,
                        'quantity': trans.quantity,
                        'price': float(trans.price) if trans.price else 0,
                        'total_amount': float(trans.total_amount) if trans.total_amount else 0,
                        'fee': float(trans.fee) if trans.fee else 0,
                        'transaction_date': trans.transaction_date,
                        'notes': trans.notes,
                        'created_at': trans.created_at,
                        'updated_at': trans.updated_at
                    }
                    results.append(trans_dict)

                return results

        except SQLAlchemyError as e:
            self.logger.error(f"获取用户交易记录失败: {e}")
            raise
    
    def get_transactions_by_item(self, user_id: str, item_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """获取特定饰品的交易记录"""
        try:
            with get_db_session() as session:
                transactions = session.query(HoldingTransaction).filter(
                    and_(
                        HoldingTransaction.user_id == user_id,
                        HoldingTransaction.item_id == item_id
                    )
                ).order_by(desc(HoldingTransaction.transaction_date))\
                .limit(limit).all()

                # 立即转换为字典格式
                results = []
                for trans in transactions:
                    trans_dict = {
                        'id': trans.id,
                        'transaction_type': trans.transaction_type,
                        'quantity': trans.quantity,
                        'price': float(trans.price) if trans.price else 0,
                        'total_amount': float(trans.total_amount) if trans.total_amount else 0,
                        'fee': float(trans.fee) if trans.fee else 0,
                        'transaction_date': trans.transaction_date,
                        'notes': trans.notes
                    }
                    results.append(trans_dict)

                return results

        except SQLAlchemyError as e:
            self.logger.error(f"获取饰品交易记录失败: {e}")
            raise
    
    def get_transaction_statistics(self, user_id: str, days: int = 30) -> Dict[str, Any]:
        """获取交易统计信息"""
        try:
            with get_db_session() as session:
                # 计算时间范围
                start_date = datetime.now() - timedelta(days=days)
                
                # 统计买入和卖出
                buy_stats = session.query(
                    func.count(HoldingTransaction.id).label('count'),
                    func.sum(HoldingTransaction.quantity).label('quantity'),
                    func.sum(HoldingTransaction.total_amount).label('amount')
                ).filter(
                    and_(
                        HoldingTransaction.user_id == user_id,
                        HoldingTransaction.transaction_type == 'BUY',
                        HoldingTransaction.transaction_date >= start_date
                    )
                ).first()

                sell_stats = session.query(
                    func.count(HoldingTransaction.id).label('count'),
                    func.sum(HoldingTransaction.quantity).label('quantity'),
                    func.sum(HoldingTransaction.total_amount).label('amount')
                ).filter(
                    and_(
                        HoldingTransaction.user_id == user_id,
                        HoldingTransaction.transaction_type == 'SELL',
                        HoldingTransaction.transaction_date >= start_date
                    )
                ).first()

                return {
                    'period_days': days,
                    'buy_transactions': buy_stats.count or 0,
                    'buy_quantity': buy_stats.quantity or 0,
                    'buy_amount': float(buy_stats.amount) if buy_stats.amount else 0.0,
                    'sell_transactions': sell_stats.count or 0,
                    'sell_quantity': sell_stats.quantity or 0,
                    'sell_amount': float(sell_stats.amount) if sell_stats.amount else 0.0
                }

        except SQLAlchemyError as e:
            self.logger.error(f"获取交易统计失败: {e}")
            return {
                'period_days': days,
                'buy_transactions': 0,
                'buy_quantity': 0,
                'buy_amount': 0.0,
                'sell_transactions': 0,
                'sell_quantity': 0,
                'sell_amount': 0.0
            }
    
    def count_user_transactions(self, user_id: str, transaction_type: str = None) -> int:
        """统计用户交易记录数量"""
        try:
            with get_db_session() as session:
                query = session.query(HoldingTransaction).filter(
                    HoldingTransaction.user_id == user_id
                )
                
                if transaction_type:
                    query = query.filter(HoldingTransaction.transaction_type == transaction_type)
                
                return query.count()

        except SQLAlchemyError as e:
            self.logger.error(f"统计交易记录数量失败: {e}")
            return 0

    def delete_transactions_by_user_item(self, user_id: str, item_id: str) -> int:
        """删除指定用户和饰品的所有交易记录"""
        try:
            with get_db_session() as session:
                result = session.query(HoldingTransaction).filter(
                    HoldingTransaction.user_id == user_id,
                    HoldingTransaction.item_id == item_id
                ).delete()

                session.commit()
                return result

        except SQLAlchemyError as e:
            self.logger.error(f"删除交易记录失败: {e}")
            raise
