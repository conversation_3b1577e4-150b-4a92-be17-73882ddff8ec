#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版技术指标图表生成器
在原有基础上添加更多有价值的指标和可视化元素
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class EnhancedTechnicalChartGenerator:
    """增强版技术指标图表生成器"""
    
    def __init__(self, monitor):
        """
        初始化图表生成器
        
        Args:
            monitor: 实时监控系统实例
        """
        self.monitor = monitor
        self.data = monitor.hourly_data.copy()
        
        # 增强版配色方案
        self.colors = {
            # 基础颜色
            'up_candle': '#00ff88',      # 上涨蜡烛
            'down_candle': '#ff4444',    # 下跌蜡烛
            'background': '#1e1e1e',     # 背景色
            
            # 技术指标颜色
            'ema12': '#ffff00',          # EMA12 黄色
            'ema26': '#00ffff',          # EMA26 青色
            'rsi_line': '#ff6600',       # RSI线 橙色
            'rsi_overbought': '#ff0000', # RSI超买 红色
            'rsi_oversold': '#00ff00',   # RSI超卖 绿色
            'macd_line': '#0099ff',      # MACD线 蓝色
            'signal_line': '#ff9900',    # 信号线 橙色
            'histogram_pos': '#00ff00',  # MACD柱正值 绿色
            'histogram_neg': '#ff0000',  # MACD柱负值 红色
            
            # 成交量颜色
            'volume_normal': '#666666',      # 正常成交量
            'volume_anomaly': '#ff00ff',     # 异常成交量 紫色
            'volume_extreme': '#ffff00',     # 极端成交量 黄色
            
            # 支撑阻力位颜色
            'support': '#00ff00',        # 支撑位 绿色
            'resistance': '#ff0000',     # 阻力位 红色
            'stop_loss': '#ff6666',      # 止损位 浅红色
            'take_profit': '#66ff66',    # 止盈位 浅绿色
            
            # 信号标注颜色
            'tech_signal': '#ffff00',    # 技术指标信号 黄色
            'base_signal': '#ffffff',    # 基础信号 白色
            'combo_signal': '#ff00ff',   # 组合信号 紫色
        }
        
        # 准备时间索引
        self.data['datetime'] = pd.to_datetime(self.data.index)
        
    def generate_enhanced_chart(self, save_path=None, show_chart=True):
        """
        生成增强版综合技术分析图表
        
        Args:
            save_path: 保存路径，如果为None则不保存
            show_chart: 是否显示图表
        """
        # 创建图表布局 - 增加更多副图
        fig = plt.figure(figsize=(20, 16))
        
        # 设置专业样式
        plt.style.use('dark_background')
        fig.patch.set_facecolor(self.colors['background'])
        
        # 创建子图 - 5个子图的布局
        gs = fig.add_gridspec(5, 3, height_ratios=[3, 1, 1, 1, 0.8], 
                             width_ratios=[3, 1, 1], hspace=0.4, wspace=0.3)
        
        # 主图：增强版K线图 (占用2列)
        ax1 = fig.add_subplot(gs[0, :2])
        self._plot_enhanced_candlestick_chart(ax1)
        
        # 右侧信息面板
        ax_info = fig.add_subplot(gs[0, 2])
        self._plot_info_panel(ax_info)
        
        # 副图1：增强版RSI指标
        ax2 = fig.add_subplot(gs[1, :])
        self._plot_enhanced_rsi_chart(ax2)
        
        # 副图2：增强版MACD指标
        ax3 = fig.add_subplot(gs[2, :])
        self._plot_enhanced_macd_chart(ax3)
        
        # 副图3：增强版成交量
        ax4 = fig.add_subplot(gs[3, :])
        self._plot_enhanced_volume_chart(ax4)
        
        # 副图4：新增KDJ指标
        ax5 = fig.add_subplot(gs[4, :])
        self._plot_kdj_chart(ax5)
        
        # 设置整体标题
        display_name = self._extract_id_from_path(self.monitor.skin_name)
        current_price = self.data['close'].iloc[-1]

        # 获取当前信号信息
        enhanced_signals = self.monitor.generate_enhanced_real_time_signals()
        signal_emoji = {'BUY': '🟢', 'SELL': '🔴', 'HOLD': '🟡'}
        signal_text = f"{signal_emoji.get(enhanced_signals['signal'], '⚪')} {enhanced_signals['signal']} ({enhanced_signals['confidence']:.0f}%)"

        fig.suptitle(f'{display_name} - 增强版专业技术分析图表\n当前价格: ¥{current_price:.2f} | 信号: {signal_text}',
                    fontsize=16, color='white', y=0.98)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor=self.colors['background'], edgecolor='none')
            print(f"📊 增强版图表已保存: {save_path}")
        
        # 显示图表
        if show_chart:
            plt.show()
        else:
            plt.close()
        
        return fig
    
    def _plot_enhanced_candlestick_chart(self, ax):
        """绘制增强版K线图"""
        # 绘制基础K线
        for i in range(len(self.data)):
            row = self.data.iloc[i]
            
            # 确定颜色
            color = self.colors['up_candle'] if row['close'] >= row['open'] else self.colors['down_candle']
            
            # 绘制影线
            ax.plot([i, i], [row['low'], row['high']], color=color, linewidth=1)
            
            # 绘制实体
            body_height = abs(row['close'] - row['open'])
            body_bottom = min(row['open'], row['close'])
            ax.bar(i, body_height, bottom=body_bottom, color=color, width=0.8, alpha=0.8)
        
        # 添加EMA线 - 使用监控系统的MACD计算结果，确保数据一致性
        try:
            macd_data = self.monitor._calculate_macd_real_time()
            if len(macd_data['macd']) >= 26:
                # 从MACD计算中获取EMA12和EMA26，确保与分析系统一致
                prices = self.data['close']
                ema12 = prices.ewm(span=12).mean()
                ema26 = prices.ewm(span=26).mean()

                # 验证与MACD计算的一致性
                calculated_macd = ema12 - ema26

                ax.plot(range(len(ema12)), ema12, color=self.colors['ema12'], linewidth=2, alpha=0.8, label='EMA12')
                ax.plot(range(len(ema26)), ema26, color=self.colors['ema26'], linewidth=2, alpha=0.8, label='EMA26')
        except Exception as e:
            print(f"EMA线绘制错误: {e}")
        
        # 添加支撑阻力位
        self._add_support_resistance_lines(ax)
        
        # 添加止损止盈位
        self._add_risk_management_lines(ax)
        
        # 添加增强版交易信号
        self._add_enhanced_trading_signals(ax)
        
        # 添加当前价格线
        current_price = self.data['close'].iloc[-1]
        ax.axhline(y=current_price, color='white', linestyle='-', linewidth=2, alpha=0.8)
        ax.text(len(self.data)-1, current_price, f' ¥{current_price:.2f}', 
               color='white', fontsize=12, fontweight='bold',
               bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.8))
        
        ax.set_title('增强版K线图 + 技术指标 + 风险管理', color='white', fontsize=14)
        ax.set_ylabel('价格 (¥)', color='white')
        ax.legend(loc='upper left', fontsize=10)
        ax.grid(True, alpha=0.3)
        
        # 设置x轴标签
        self._set_time_labels(ax)
    
    def _add_support_resistance_lines(self, ax):
        """添加支撑阻力位线（只显示最近的，与实时分析面板一致）"""
        try:
            # 获取支撑阻力位数据
            sr_data = self.monitor.detect_support_resistance_real_time()

            # 只显示最近的支撑位（第一个）
            support_levels = sr_data.get('support_levels', [])
            if support_levels:
                support = support_levels[0]  # 只取最近的
                level = support['level']
                strength = support['strength']
                alpha = 0.8 if strength == '强' else 0.6 if strength == '中' else 0.4

                # 绘制支撑线
                ax.axhline(y=level, color=self.colors['support'],
                         linestyle='--', alpha=alpha, linewidth=2)
                # 绘制支撑位标签
                ax.text(len(self.data)*0.02, level, f'支撑 ¥{level:.2f}',
                       color=self.colors['support'], fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='black', alpha=0.7),
                       ha='left', va='center')

            # 只显示最近的阻力位（第一个）
            resistance_levels = sr_data.get('resistance_levels', [])
            if resistance_levels:
                resistance = resistance_levels[0]  # 只取最近的
                level = resistance['level']
                strength = resistance['strength']
                alpha = 0.8 if strength == '强' else 0.6 if strength == '中' else 0.4

                # 绘制阻力线
                ax.axhline(y=level, color=self.colors['resistance'],
                         linestyle='--', alpha=alpha, linewidth=2)
                # 绘制阻力位标签
                ax.text(len(self.data)*0.98, level, f'阻力 ¥{level:.2f}',
                       color=self.colors['resistance'], fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='black', alpha=0.7),
                       ha='right', va='center')

        except Exception as e:
            print(f"支撑阻力位绘制错误: {e}")
    
    def _add_risk_management_lines(self, ax):
        """添加风险管理线（止损止盈位）"""
        try:
            # 获取最新的交易信号和风险管理数据
            enhanced_signals = self.monitor.generate_enhanced_real_time_signals()
            sr_data = self.monitor.detect_support_resistance_real_time()
            
            # 计算风险管理参数
            risk_mgmt = self.monitor.calculate_risk_management(enhanced_signals, sr_data)
            
            if risk_mgmt.get('stop_loss'):
                stop_loss = risk_mgmt['stop_loss']
                ax.axhline(y=stop_loss, color=self.colors['stop_loss'], 
                         linestyle='-', alpha=0.8, linewidth=2)
                ax.text(len(self.data)*0.7, stop_loss, f'止损 ¥{stop_loss:.2f}', 
                       color=self.colors['stop_loss'], fontsize=10, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.8))
            
            if risk_mgmt.get('take_profit'):
                take_profit = risk_mgmt['take_profit']
                ax.axhline(y=take_profit, color=self.colors['take_profit'], 
                         linestyle='-', alpha=0.8, linewidth=2)
                ax.text(len(self.data)*0.7, take_profit, f'止盈 ¥{take_profit:.2f}', 
                       color=self.colors['take_profit'], fontsize=10, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.8))
        except Exception as e:
            print(f"风险管理线绘制错误: {e}")
    
    def _add_enhanced_trading_signals(self, ax):
        """添加增强版交易信号标注"""
        try:
            # 获取增强版信号
            enhanced_signals = self.monitor.generate_enhanced_real_time_signals()
            
            if enhanced_signals.get('all_signals'):
                for i, (action, confidence, reason) in enumerate(enhanced_signals['all_signals']):
                    # 确定信号位置（最近几个数据点）
                    signal_x = len(self.data) - len(enhanced_signals['all_signals']) + i
                    signal_y = self.data['close'].iloc[signal_x] if signal_x < len(self.data) else self.data['close'].iloc[-1]
                    
                    # 确定信号颜色和形状
                    if '[技术指标]' in reason:
                        color = self.colors['tech_signal']
                        marker = '*'
                        size = 150
                    elif '[基础分析]' in reason:
                        color = self.colors['base_signal']
                        marker = 'o'
                        size = 100
                    else:
                        color = self.colors['combo_signal']
                        marker = 's'
                        size = 120
                    
                    # 绘制信号点
                    if action == 'BUY':
                        ax.scatter(signal_x, signal_y * 0.98, color=color, marker=marker, 
                                 s=size, alpha=0.8, edgecolors='white', linewidth=1)
                        ax.annotate(f'{action}\n{confidence:.0f}%', 
                                   xy=(signal_x, signal_y * 0.98), 
                                   xytext=(signal_x, signal_y * 0.92),
                                   ha='center', va='top', color=color, fontsize=8,
                                   bbox=dict(boxstyle='round,pad=0.2', facecolor='black', alpha=0.8))
                    elif action == 'SELL':
                        ax.scatter(signal_x, signal_y * 1.02, color=color, marker=marker, 
                                 s=size, alpha=0.8, edgecolors='white', linewidth=1)
                        ax.annotate(f'{action}\n{confidence:.0f}%', 
                                   xy=(signal_x, signal_y * 1.02), 
                                   xytext=(signal_x, signal_y * 1.08),
                                   ha='center', va='bottom', color=color, fontsize=8,
                                   bbox=dict(boxstyle='round,pad=0.2', facecolor='black', alpha=0.8))
        except Exception as e:
            print(f"交易信号标注错误: {e}")
    
    def _set_time_labels(self, ax):
        """设置时间轴标签"""
        try:
            # 设置x轴标签为时间
            if len(self.data) > 0 and 'datetime' in self.data.columns:
                step = max(1, len(self.data) // 10)  # 显示大约10个时间点
                indices = range(0, len(self.data), step)

                # 直接从监控系统获取正确的datetime数据
                monitor_data = self.monitor.hourly_data



                # 使用监控系统的datetime数据生成标签
                labels = []
                for i in indices:
                    if i < len(monitor_data):
                        dt = monitor_data.iloc[i]['datetime']
                        if hasattr(dt, 'strftime'):
                            labels.append(dt.strftime('%m-%d %H:%M'))
                        else:
                            labels.append(f"#{i}")

                ax.set_xticks(indices)
                ax.set_xticklabels(labels, rotation=45, ha='right')
        except Exception as e:
            print(f"时间标签设置错误: {e}")
            # 使用简单的数字标签作为备用
            if len(self.data) > 0:
                step = max(1, len(self.data) // 10)
                indices = range(0, len(self.data), step)
                labels = [f"#{i}" for i in indices]
                ax.set_xticks(indices)
                ax.set_xticklabels(labels, rotation=45, ha='right')

    def _plot_info_panel(self, ax):
        """绘制信息面板"""
        try:
            ax.axis('off')  # 隐藏坐标轴

            # 获取关键信息
            enhanced_signals = self.monitor.generate_enhanced_real_time_signals()
            sr_data = self.monitor.detect_support_resistance_real_time()
            volume_data = self.monitor.detect_volume_anomaly_real_time()
            risk_mgmt = self.monitor.calculate_risk_management(enhanced_signals, sr_data)

            # 创建信息文本
            info_text = []
            info_text.append("📊 实时分析面板")
            info_text.append("=" * 20)

            # 主要信号
            signal_emoji = {'BUY': '🟢', 'SELL': '🔴', 'HOLD': '🟡'}
            info_text.append(f"🎯 主要信号:")
            info_text.append(f"   {signal_emoji.get(enhanced_signals['signal'], '⚪')} {enhanced_signals['signal']}")
            info_text.append(f"   置信度: {enhanced_signals['confidence']:.0f}%")

            # 技术指标状态
            try:
                rsi = self.monitor._calculate_rsi_real_time()
                macd_data = self.monitor._calculate_macd_real_time()
                if len(rsi) > 0 and len(macd_data['macd']) > 0:
                    current_rsi = rsi.iloc[-1]
                    current_macd = macd_data['macd'].iloc[-1]
                    current_signal = macd_data['signal'].iloc[-1]

                    info_text.append(f"\n📈 技术指标:")
                    info_text.append(f"   RSI: {current_rsi:.1f}")
                    rsi_status = "超买" if current_rsi > 70 else "超卖" if current_rsi < 30 else "正常"
                    info_text.append(f"   状态: {rsi_status}")

                    macd_trend = "金叉" if current_macd > current_signal else "死叉"
                    info_text.append(f"   MACD: {macd_trend}")
            except:
                info_text.append(f"\n📈 技术指标: 计算中...")

            # 风险管理
            info_text.append(f"\n⚖️ 风险管理:")
            if risk_mgmt.get('stop_loss'):
                info_text.append(f"   止损: ¥{risk_mgmt['stop_loss']:.2f}")
            if risk_mgmt.get('take_profit'):
                info_text.append(f"   止盈: ¥{risk_mgmt['take_profit']:.2f}")
            if risk_mgmt.get('position_size'):
                info_text.append(f"   建议仓位: {risk_mgmt['position_size']:.1f}%")

            # 成交量状态
            info_text.append(f"\n📦 成交量:")
            info_text.append(f"   状态: {volume_data['alert']}")
            info_text.append(f"   比率: {volume_data['ratio']:.2f}倍")

            # 支撑阻力位
            info_text.append(f"\n🎯 关键位置:")
            support_levels = sr_data.get('support_levels', [])
            resistance_levels = sr_data.get('resistance_levels', [])

            if support_levels:
                nearest_support = support_levels[0]['level']
                info_text.append(f"   支撑: ¥{nearest_support:.2f}")
            else:
                info_text.append(f"   支撑: 未检测到")

            if resistance_levels:
                nearest_resistance = resistance_levels[0]['level']
                info_text.append(f"   阻力: ¥{nearest_resistance:.2f}")
            else:
                info_text.append(f"   阻力: 未检测到")

            # 显示信息
            info_str = '\n'.join(info_text)
            ax.text(0.05, 0.95, info_str, transform=ax.transAxes,
                   fontsize=10, color='white', va='top', ha='left',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='black', alpha=0.8))

        except Exception as e:
            ax.text(0.5, 0.5, f'信息面板错误: {str(e)}',
                   transform=ax.transAxes, ha='center', va='center', color='red')

    def _plot_enhanced_rsi_chart(self, ax):
        """绘制增强版RSI指标图"""
        try:
            rsi = self.monitor._calculate_rsi_real_time()

            # 绘制RSI线
            ax.plot(range(len(rsi)), rsi, color=self.colors['rsi_line'], linewidth=2, label='RSI(14)')

            # 添加超买超卖线
            ax.axhline(y=70, color=self.colors['rsi_overbought'], linestyle='--', alpha=0.7, label='超买线(70)')
            ax.axhline(y=30, color=self.colors['rsi_oversold'], linestyle='--', alpha=0.7, label='超卖线(30)')
            ax.axhline(y=50, color='white', linestyle='-', alpha=0.5, label='中线(50)')

            # 填充超买超卖区域
            ax.fill_between(range(len(rsi)), 70, 100, alpha=0.2, color=self.colors['rsi_overbought'])
            ax.fill_between(range(len(rsi)), 0, 30, alpha=0.2, color=self.colors['rsi_oversold'])

            # 标注RSI背离
            self._add_rsi_divergence(ax, rsi)

            # 标注当前RSI值
            current_rsi = rsi.iloc[-1]
            rsi_status = "超买" if current_rsi > 70 else "超卖" if current_rsi < 30 else "正常"
            status_color = self.colors['rsi_overbought'] if current_rsi > 70 else self.colors['rsi_oversold'] if current_rsi < 30 else 'white'

            ax.annotate(f'RSI: {current_rsi:.1f} ({rsi_status})',
                       xy=(len(rsi)-1, current_rsi),
                       xytext=(len(rsi)-20, current_rsi+10),
                       arrowprops=dict(arrowstyle='->', color=status_color),
                       color=status_color, fontsize=10, fontweight='bold')

            ax.set_title('增强版RSI相对强弱指标', color='white', fontsize=12)
            ax.set_ylabel('RSI', color='white')
            ax.set_ylim(0, 100)
            ax.legend(loc='upper left', fontsize=9)
            ax.grid(True, alpha=0.3)

        except Exception as e:
            ax.text(0.5, 0.5, f'RSI图表错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_enhanced_macd_chart(self, ax):
        """绘制增强版MACD指标图"""
        try:
            macd_data = self.monitor._calculate_macd_real_time()

            # 绘制MACD线和信号线
            ax.plot(range(len(macd_data['macd'])), macd_data['macd'],
                   color=self.colors['macd_line'], linewidth=2, label='MACD')
            ax.plot(range(len(macd_data['signal'])), macd_data['signal'],
                   color=self.colors['signal_line'], linewidth=2, label='信号线')

            # 绘制柱状图
            histogram = macd_data['histogram']
            colors = [self.colors['histogram_pos'] if x >= 0 else self.colors['histogram_neg'] for x in histogram]
            ax.bar(range(len(histogram)), histogram, color=colors, alpha=0.7, label='MACD柱状图')

            # 添加零轴线
            ax.axhline(y=0, color='white', linestyle='-', alpha=0.5)

            # 标注金叉死叉点
            self._add_macd_crossover_signals(ax, macd_data)

            # 标注MACD背离
            self._add_macd_divergence(ax, macd_data)

            # 标注当前值
            current_macd = macd_data['macd'].iloc[-1]
            current_signal = macd_data['signal'].iloc[-1]
            trend = "金叉" if current_macd > current_signal else "死叉"
            trend_color = self.colors['histogram_pos'] if current_macd > current_signal else self.colors['histogram_neg']

            ax.annotate(f'MACD {trend}\n{current_macd:.4f}',
                       xy=(len(macd_data['macd'])-1, current_macd),
                       xytext=(len(macd_data['macd'])-30, current_macd),
                       arrowprops=dict(arrowstyle='->', color=trend_color),
                       color=trend_color, fontsize=10, fontweight='bold')

            ax.set_title('增强版MACD指标', color='white', fontsize=12)
            ax.set_ylabel('MACD', color='white')
            ax.legend(loc='upper left', fontsize=9)
            ax.grid(True, alpha=0.3)

        except Exception as e:
            ax.text(0.5, 0.5, f'MACD图表错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_enhanced_volume_chart(self, ax):
        """绘制增强版成交量图"""
        try:
            volume = self.data['volume']

            # 检测成交量异常
            volume_anomaly = self.monitor.detect_volume_anomaly_real_time()

            # 根据成交量状态设置颜色
            colors = []
            for i, vol in enumerate(volume):
                current_ratio = vol / volume.tail(24).mean() if len(volume) >= 24 else 1
                if current_ratio >= 3:
                    colors.append(self.colors['volume_extreme'])
                elif current_ratio >= 1.5:
                    colors.append(self.colors['volume_anomaly'])
                else:
                    colors.append(self.colors['volume_normal'])

            # 绘制成交量柱状图
            bars = ax.bar(range(len(volume)), volume, color=colors, alpha=0.7)

            # 添加成交量移动平均线
            if len(volume) >= 24:
                volume_ma24 = volume.rolling(24).mean()
                ax.plot(range(len(volume_ma24)), volume_ma24,
                       color='yellow', linestyle='--', alpha=0.8, linewidth=2, label='24h平均')

            # 添加成交量能量线
            volume_energy = volume.rolling(6).sum()  # 6小时成交量总和
            ax2 = ax.twinx()
            ax2.plot(range(len(volume_energy)), volume_energy,
                    color='cyan', alpha=0.6, linewidth=1, label='6h能量')
            ax2.set_ylabel('成交量能量', color='cyan')
            ax2.tick_params(axis='y', labelcolor='cyan')

            # 标注异常成交量
            for i, (vol, color) in enumerate(zip(volume, colors)):
                if color != self.colors['volume_normal']:
                    ratio = vol / volume.tail(24).mean() if len(volume) >= 24 else 1
                    ax.annotate(f'{ratio:.1f}x', xy=(i, vol), xytext=(i, vol*1.2),
                               ha='center', va='bottom', color=color, fontsize=8,
                               bbox=dict(boxstyle='round,pad=0.2', facecolor='black', alpha=0.7))

            # 当前成交量信息
            current_volume = volume.iloc[-1]
            ratio = volume_anomaly['ratio']

            ax.annotate(f'当前: {current_volume:.0f}\n状态: {volume_anomaly["alert"]}\n比率: {ratio:.2f}倍',
                       xy=(len(volume)-1, current_volume),
                       xytext=(len(volume)-20, current_volume*1.3),
                       arrowprops=dict(arrowstyle='->', color='white'),
                       color='white', fontsize=10, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.8))

            ax.set_title(f'增强版成交量分析 - {volume_anomaly["alert"]}', color='white', fontsize=12)
            ax.set_ylabel('成交量', color='white')
            ax.legend(loc='upper left', fontsize=9)
            ax.grid(True, alpha=0.3)

        except Exception as e:
            ax.text(0.5, 0.5, f'成交量图表错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_kdj_chart(self, ax):
        """绘制KDJ指标图 - 使用监控系统的计算结果确保数据一致性"""
        try:
            # 使用监控系统的KDJ计算结果，确保与分析系统一致
            kdj_data = self.monitor._calculate_kdj_real_time()

            k = kdj_data['k']
            d = kdj_data['d']
            j = kdj_data['j']

            # 绘制KDJ线
            ax.plot(range(len(k)), k, color='#ff6600', linewidth=2, label='K线')
            ax.plot(range(len(d)), d, color='#0066ff', linewidth=2, label='D线')
            ax.plot(range(len(j)), j, color='#ff00ff', linewidth=2, label='J线')

            # 添加超买超卖线
            ax.axhline(y=80, color='red', linestyle='--', alpha=0.7, label='超买线(80)')
            ax.axhline(y=20, color='green', linestyle='--', alpha=0.7, label='超卖线(20)')
            ax.axhline(y=50, color='white', linestyle=':', alpha=0.5, label='中线(50)')

            # 填充超买超卖区域
            ax.fill_between(range(len(k)), 80, 100, alpha=0.1, color='red')
            ax.fill_between(range(len(k)), 0, 20, alpha=0.1, color='green')

            # 标注KDJ金叉死叉
            for i in range(1, len(k)):
                if k.iloc[i-1] <= d.iloc[i-1] and k.iloc[i] > d.iloc[i]:  # 金叉
                    ax.scatter(i, k.iloc[i], color='gold', marker='^', s=100, alpha=0.8, zorder=5)
                elif k.iloc[i-1] >= d.iloc[i-1] and k.iloc[i] < d.iloc[i]:  # 死叉
                    ax.scatter(i, k.iloc[i], color='red', marker='v', s=100, alpha=0.8, zorder=5)

            # 标注当前KDJ值
            current_k = k.iloc[-1]
            current_d = d.iloc[-1]
            current_j = j.iloc[-1]

            kdj_status = "超买" if current_k > 80 and current_d > 80 else "超卖" if current_k < 20 and current_d < 20 else "正常"

            ax.annotate(f'K: {current_k:.1f}\nD: {current_d:.1f}\nJ: {current_j:.1f}\n状态: {kdj_status}',
                       xy=(len(k)-1, current_k),
                       xytext=(len(k)-25, current_k+15),
                       arrowprops=dict(arrowstyle='->', color='white'),
                       color='white', fontsize=10, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.8))

            ax.set_title('KDJ随机指标', color='white', fontsize=12)
            ax.set_ylabel('KDJ值', color='white')
            ax.set_xlabel('时间', color='white')
            ax.set_ylim(0, 100)
            ax.legend(loc='upper left', fontsize=9)
            ax.grid(True, alpha=0.3)

            # 设置x轴标签
            self._set_time_labels(ax)

        except Exception as e:
            ax.text(0.5, 0.5, f'KDJ图表错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    def _add_rsi_divergence(self, ax, rsi):
        """添加RSI背离标注"""
        # 这里可以添加RSI背离检测逻辑
        # 简化实现，实际应该检测价格高点/低点与RSI高点/低点的背离
        pass

    def _add_macd_crossover_signals(self, ax, macd_data):
        """添加MACD金叉死叉信号"""
        try:
            macd = macd_data['macd']
            signal = macd_data['signal']

            for i in range(1, len(macd)):
                if macd.iloc[i-1] <= signal.iloc[i-1] and macd.iloc[i] > signal.iloc[i]:  # 金叉
                    ax.scatter(i, macd.iloc[i], color='gold', marker='^', s=100, alpha=0.8, zorder=5)
                elif macd.iloc[i-1] >= signal.iloc[i-1] and macd.iloc[i] < signal.iloc[i]:  # 死叉
                    ax.scatter(i, macd.iloc[i], color='red', marker='v', s=100, alpha=0.8, zorder=5)
        except Exception as e:
            print(f"MACD金叉死叉标注错误: {e}")

    def _add_macd_divergence(self, ax, macd_data):
        """添加MACD背离标注"""
        # 这里可以添加MACD背离检测逻辑
        pass

    def _extract_id_from_path(self, path):
        """从路径中提取ID"""
        try:
            from pathlib import Path

            path_obj = Path(path)
            name = path_obj.name if path_obj.is_absolute() else path

            # 如果是数字ID，直接返回
            if name.isdigit():
                return name

            # 如果包含其他字符，尝试提取数字部分
            import re
            numbers = re.findall(r'\d+', name)
            if numbers:
                # 返回最长的数字串
                return max(numbers, key=len)

            # 如果没有数字，返回原始名称
            return name

        except:
            return path
