"""
搬砖数据管理页面

提供CS2饰品搬砖数据的导入和查询功能。
"""

import streamlit as st
import pandas as pd
import json
import io
from typing import List, Dict, Any, Optional
from datetime import datetime, date
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.services.arbitrage_data_service import ArbitrageDataService
from src.cs2_investment.app.components.item_analysis_component import item_analysis_component


def show_page():
    """显示搬砖数据管理页面"""
    try:
        # 初始化服务
        if 'arbitrage_service' not in st.session_state:
            st.session_state.arbitrage_service = ArbitrageDataService()

        st.title("🔄 搬砖数据管理")
        st.markdown("导入和查询CS2饰品搬砖数据（Steam & Youpin平台）")

        # 创建标签页
        tab1, tab2 = st.tabs(["📥 数据导入", "🔍 数据查询"])

        with tab1:
            show_import_section()

        with tab2:
            show_query_section()

    except Exception as e:
        st.error(f"加载搬砖数据管理页面失败: {e}")


def show_import_section():
    """显示数据导入区域"""
    st.subheader("📥 数据导入")
    st.markdown("支持导入bzdata目录下的JSON文件，只处理Steam和Youpin平台数据")

    # 文件上传区域
    with st.expander("📁 文件上传", expanded=True):
        uploaded_files = st.file_uploader(
            "选择JSON文件",
            type=['json'],
            accept_multiple_files=True,
            help="支持选择多个搬砖数据JSON文件（如1.steam.json, 2.steam.json等）"
        )

        if uploaded_files:
            st.success(f"已选择 {len(uploaded_files)} 个文件")
            
            # 显示文件列表
            for i, file in enumerate(uploaded_files, 1):
                st.write(f"{i}. {file.name} ({file.size} bytes)")

            # 文件验证
            if st.button("🔍 验证文件格式", type="secondary"):
                validate_uploaded_files(uploaded_files)

            # 导入按钮
            col1, col2, col3 = st.columns([1, 1, 1])
            with col2:
                if st.button("🚀 开始导入", type="primary", use_container_width=True):
                    import_uploaded_files(uploaded_files)




def validate_uploaded_files(uploaded_files):
    """验证上传的文件"""
    try:
        with st.spinner("正在验证文件格式..."):
            validation_results = []
            
            for file in uploaded_files:
                # 重置文件指针
                file.seek(0)
                
                try:
                    # 读取文件内容
                    content = file.read().decode('utf-8')
                    data = json.loads(content)
                    
                    # 基本格式检查
                    if not isinstance(data, list):
                        validation_results.append({
                            'file': file.name,
                            'status': '❌ 失败',
                            'error': '数据格式错误，期望数组格式'
                        })
                        continue
                    
                    if len(data) == 0:
                        validation_results.append({
                            'file': file.name,
                            'status': '⚠️ 警告',
                            'error': '文件为空'
                        })
                        continue
                    
                    # 检查是否包含Steam或Youpin数据
                    sample_item = data[0]
                    has_steam = any(key.startswith('steam') for key in sample_item.keys())
                    has_youpin = any(key.startswith('youpin') for key in sample_item.keys())
                    
                    if not (has_steam or has_youpin):
                        validation_results.append({
                            'file': file.name,
                            'status': '❌ 失败',
                            'error': '文件不包含Steam或Youpin平台数据'
                        })
                        continue
                    
                    # 验证成功
                    platforms = []
                    if has_steam:
                        platforms.append("Steam")
                    if has_youpin:
                        platforms.append("Youpin")
                    
                    validation_results.append({
                        'file': file.name,
                        'status': '✅ 通过',
                        'items': len(data),
                        'platforms': " + ".join(platforms)
                    })
                    
                except json.JSONDecodeError as e:
                    validation_results.append({
                        'file': file.name,
                        'status': '❌ 失败',
                        'error': f'JSON解析错误: {str(e)[:50]}...'
                    })
                except Exception as e:
                    validation_results.append({
                        'file': file.name,
                        'status': '❌ 失败',
                        'error': f'验证失败: {str(e)[:50]}...'
                    })
            
            # 显示验证结果
            st.subheader("📋 验证结果")
            for result in validation_results:
                if result['status'].startswith('✅'):
                    st.success(f"{result['status']} {result['file']} - {result['items']}条记录 - 平台: {result['platforms']}")
                elif result['status'].startswith('⚠️'):
                    st.warning(f"{result['status']} {result['file']} - {result['error']}")
                else:
                    st.error(f"{result['status']} {result['file']} - {result['error']}")
            
    except Exception as e:
        st.error(f"文件验证失败: {e}")


def import_uploaded_files(uploaded_files):
    """导入上传的文件"""
    try:
        # 保存临时文件
        import tempfile
        import os

        temp_files = []
        temp_dir = tempfile.gettempdir()

        for file in uploaded_files:
            file.seek(0)
            temp_path = os.path.join(temp_dir, file.name)
            with open(temp_path, 'wb') as f:
                f.write(file.read())
            temp_files.append(temp_path)
        
        # 执行导入
        with st.spinner("正在导入数据..."):
            service = st.session_state.arbitrage_service
            result = service.import_bzdata_files(temp_files)
        
        # 显示导入结果
        st.subheader("📊 导入结果")
        
        # 总体统计
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("总文件数", result['total_files'])
        with col2:
            st.metric("成功文件", result['success_files'], delta=result['success_files'])
        with col3:
            st.metric("失败文件", result['error_files'], delta=-result['error_files'] if result['error_files'] > 0 else 0)
        with col4:
            st.metric("导入记录", result['total_imported'])
        
        # 详细结果
        if result['file_results']:
            st.subheader("📋 详细结果")
            for file_result in result['file_results']:
                if file_result['status'] == 'success':
                    st.success(f"✅ {file_result['file']} - 导入 {file_result['imported_count']} 条记录")
                else:
                    st.error(f"❌ {file_result['file']} - {file_result['error']}")
        
        # 清理临时文件
        for temp_path in temp_files:
            try:
                Path(temp_path).unlink()
            except:
                pass
        
        # 刷新统计数据
        if 'data_statistics' in st.session_state:
            del st.session_state.data_statistics
        
        st.success("🎉 数据导入完成！")
        
    except Exception as e:
        st.error(f"导入失败: {e}")



def show_query_section():
    """显示数据查询区域"""
    st.subheader("🔍 数据查询")
    st.markdown("查询和分析搬砖数据")
    
    # 查询条件
    with st.expander("🔧 查询条件", expanded=True):
        show_query_filters()
    
    # 查询结果
    show_query_results()


def show_query_filters():
    """显示查询筛选条件"""
    try:
        # 获取筛选选项
        if 'filter_options' not in st.session_state:
            with st.spinner("加载筛选选项..."):
                service = st.session_state.arbitrage_service
                st.session_state.filter_options = service.get_filter_options()

        filter_options = st.session_state.filter_options

        # 基础筛选条件
        st.markdown("### 🔍 基础筛选")
        col1, col2, col3 = st.columns(3)

        with col1:
            # 饰品名称搜索
            market_name = st.text_input("饰品名称", placeholder="输入饰品名称进行搜索")

        with col2:
            # 武器类型
            goods_types = ["全部"] + filter_options.get('goods_types', [])
            selected_type = st.selectbox("武器类型", goods_types)

        with col3:
            # 品质等级
            goods_levels = ["全部"] + filter_options.get('goods_levels', [])
            selected_level = st.selectbox("品质等级", goods_levels)

        # Steam平台筛选条件
        st.markdown("### 🎮 Steam平台筛选")
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**💰 Steam价格范围**")
            steam_sell_price_range = st.slider(
                "Steam在售价范围(美元)",
                0.0, 10000.0, (0.0, 10000.0),
                step=1.0,
                format="$%.1f",
                key="steam_sell_price"
            )
            steam_buy_price_range = st.slider(
                "Steam求购价范围(美元)",
                0.0, 10000.0, (0.0, 10000.0),
                step=1.0,
                format="$%.1f",
                key="steam_buy_price"
            )

        with col2:
            st.markdown("**📦 Steam数量范围**")
            steam_sell_count_range = st.slider(
                "Steam在售量范围",
                0, 10000, (0, 10000),
                step=1,
                key="steam_sell_count"
            )
            steam_buy_count_range = st.slider(
                "Steam求购数范围",
                0, 10000, (0, 10000),
                step=1,
                key="steam_buy_count"
            )

        # Youpin平台筛选条件
        st.markdown("### 💎 悠品平台筛选")
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**💰 悠品价格范围**")
            youpin_sell_price_range = st.slider(
                "悠品在售价范围(人民币)",
                0.0, 100000.0, (0.0, 100000.0),
                step=10.0,
                format="¥%.0f",
                key="youpin_sell_price"
            )
            youpin_buy_price_range = st.slider(
                "悠品求购价范围(人民币)",
                0.0, 100000.0, (0.0, 100000.0),
                step=10.0,
                format="¥%.0f",
                key="youpin_buy_price"
            )

        with col2:
            st.markdown("**📦 悠品数量范围**")
            youpin_sell_count_range = st.slider(
                "悠品在售数范围",
                0, 1000, (0, 1000),
                step=1,
                key="youpin_sell_count"
            )
            youpin_buy_count_range = st.slider(
                "悠品求购数范围",
                0, 1000, (0, 1000),
                step=1,
                key="youpin_buy_count"
            )

        # 搬砖分析筛选
        st.markdown("### 📊 搬砖分析筛选")
        # 利润率范围
        profit_range = st.slider("利润率范围", -100.0, 500.0, (-100.0, 500.0), step=1.0, format="%d%%")

        # 排序选项
        st.markdown("### 🔄 排序设置")
        col1, col2 = st.columns(2)
        with col1:
            sort_by = st.selectbox(
                "排序字段",
                ["youpin_profit_rate", "youpin_profit", "steam_price", "youpin_price", "market_name"]
            )
        with col2:
            sort_order = st.selectbox("排序方式", ["降序", "升序"])
        
        # 查询按钮
        if st.button("🔍 执行查询", type="primary", use_container_width=True):
            # 构建查询参数 - 先获取总数，确保类型转换
            from decimal import Decimal
            base_query_params = {
                'market_name': market_name if market_name else None,
                'goods_type': selected_type if selected_type != "全部" else None,
                'goods_level': selected_level if selected_level != "全部" else None,
                'profit_rate_min': Decimal(str(profit_range[0] / 100)),
                'profit_rate_max': Decimal(str(profit_range[1] / 100)),
                'order_by': sort_by,
                'order_desc': sort_order == "降序"
            }

            # Steam平台价格范围
            if steam_sell_price_range[0] > 0 or steam_sell_price_range[1] < 10000:
                base_query_params['steam_price_min'] = Decimal(str(steam_sell_price_range[0]))
                base_query_params['steam_price_max'] = Decimal(str(steam_sell_price_range[1]))

            if steam_buy_price_range[0] > 0 or steam_buy_price_range[1] < 10000:
                base_query_params['steam_buy_price_min'] = Decimal(str(steam_buy_price_range[0]))
                base_query_params['steam_buy_price_max'] = Decimal(str(steam_buy_price_range[1]))

            # Steam平台数量范围
            if steam_sell_count_range[0] > 0 or steam_sell_count_range[1] < 10000:
                base_query_params['steam_sell_count_min'] = steam_sell_count_range[0]
                base_query_params['steam_sell_count_max'] = steam_sell_count_range[1]

            if steam_buy_count_range[0] > 0 or steam_buy_count_range[1] < 10000:
                base_query_params['steam_buy_count_min'] = steam_buy_count_range[0]
                base_query_params['steam_buy_count_max'] = steam_buy_count_range[1]

            # Youpin平台价格范围
            if youpin_sell_price_range[0] > 0 or youpin_sell_price_range[1] < 100000:
                base_query_params['youpin_price_min'] = Decimal(str(youpin_sell_price_range[0]))
                base_query_params['youpin_price_max'] = Decimal(str(youpin_sell_price_range[1]))

            if youpin_buy_price_range[0] > 0 or youpin_buy_price_range[1] < 100000:
                base_query_params['youpin_purchase_price_min'] = Decimal(str(youpin_buy_price_range[0]))
                base_query_params['youpin_purchase_price_max'] = Decimal(str(youpin_buy_price_range[1]))

            # Youpin平台数量范围
            if youpin_sell_count_range[0] > 0 or youpin_sell_count_range[1] < 1000:
                base_query_params['youpin_sell_count_min'] = youpin_sell_count_range[0]
                base_query_params['youpin_sell_count_max'] = youpin_sell_count_range[1]

            if youpin_buy_count_range[0] > 0 or youpin_buy_count_range[1] < 1000:
                base_query_params['youpin_purchase_count_min'] = youpin_buy_count_range[0]
                base_query_params['youpin_purchase_count_max'] = youpin_buy_count_range[1]

            # 保存查询参数
            st.session_state.query_params = base_query_params
            st.session_state.query_executed = True
    
    except Exception as e:
        st.error(f"加载筛选条件失败: {e}")


def show_query_results():
    """显示查询结果 - 参考其他页面的前端分页实现"""
    if not st.session_state.get('query_executed', False):
        st.info("👆 请设置查询条件并点击查询按钮")
        return

    try:
        # 执行查询
        with st.spinner("正在查询数据..."):
            service = st.session_state.arbitrage_service
            results, total_count = service.query_arbitrage_items(**st.session_state.query_params)

        if not results:
            st.warning("未找到符合条件的搬砖数据")
            return

        # 显示结果统计
        st.subheader(f"查询结果 ({len(results)} 个搬砖数据)")

        # 分页显示 - 参考其他页面的实现
        items_per_page = 20
        total_pages = (len(results) - 1) // items_per_page + 1

        if total_pages > 1:
            page = st.selectbox(
                "页码",
                range(1, total_pages + 1),
                format_func=lambda x: f"第 {x} 页"
            )
        else:
            page = 1

        # 计算当前页的数据范围
        start_idx = (page - 1) * items_per_page
        end_idx = min(start_idx + items_per_page, len(results))
        current_page_results = results[start_idx:end_idx]

        # 显示结果（默认卡片模式）
        show_card_view_results(current_page_results)

    except Exception as e:
        st.error(f"查询失败: {e}")



def export_results_to_csv(results):
    """导出查询结果为CSV"""
    try:
        # 准备数据
        data = []
        for item in results:
            data.append({
                '饰品名称': item.market_name,
                '武器类型': item.goods_type01_name or '',
                '品质等级': item.goods_level_name or '',
                'Steam价格': float(item.steam_price) if item.steam_price else 0,
                'Youpin价格': float(item.youpin_price) if item.youpin_price else 0,
                '利润率': float(item.youpin_profit_rate) * 100 if item.youpin_profit_rate else 0,
                '预计利润': float(item.youpin_profit) if item.youpin_profit else 0,
                'Steam在售数': item.steam_sell_count or 0,
                'Youpin在售数': item.youpin_sell_count or 0,
                '是否搬砖机会': '是' if item.youpin_profit_rate and float(item.youpin_profit_rate) >= 0.1 else '否'
            })

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 转换为CSV
        csv_buffer = io.StringIO()
        df.to_csv(csv_buffer, index=False, encoding='utf-8-sig')
        csv_data = csv_buffer.getvalue()

        # 提供下载
        st.download_button(
            label="📥 下载CSV文件",
            data=csv_data,
            file_name=f"arbitrage_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv",
            help="下载当前查询结果的CSV文件"
        )

        st.success(f"✅ 已准备 {len(results)} 条记录的CSV文件")

    except Exception as e:
        st.error(f"导出失败: {e}")


def show_card_view_results(results):
    """显示卡片视图结果"""
    for item in results:
        show_arbitrage_item_card(item)


def show_table_view_results(results):
    """显示表格视图结果 - 搬砖专用格式"""
    if not results:
        return

    # 准备表格数据 - 更多搬砖相关信息
    table_data = []
    for item in results:
        steam_price = float(item.steam_price) if item.steam_price else 0
        youpin_price = float(item.youpin_price) if item.youpin_price else 0
        profit = float(item.youpin_profit) if item.youpin_profit else 0
        profit_rate = float(item.youpin_profit_rate) * 100 if item.youpin_profit_rate else 0

        # 计算价差
        price_diff = youpin_price - steam_price if steam_price > 0 and youpin_price > 0 else 0

        # 判断搬砖机会等级
        if profit_rate >= 30:
            opportunity_level = "🔥 超高"
        elif profit_rate >= 20:
            opportunity_level = "⭐ 高"
        elif profit_rate >= 10:
            opportunity_level = "✅ 中"
        else:
            opportunity_level = "❌ 低"

        table_data.append({
            '饰品': item.market_name[:30] + "..." if len(item.market_name) > 30 else item.market_name,
            '武器类型/品质等级': f"{item.goods_type01_name or '-'} | {item.goods_level_name or '-'}",
            'Steam在售价(美元)': f"{steam_price:.2f}" if steam_price > 0 else '-',
            '悠品在售价': f"{youpin_price:.2f}" if youpin_price > 0 else '-',
            '价差': f"{price_diff:.2f}" if price_diff > 0 else '-',
            '利润': f"{profit:.2f}" if profit > 0 else '-',
            '利润率': f"{profit_rate:.1f}%" if profit_rate > 0 else '-',
            '机会等级': opportunity_level,
            'Steam在售量': item.steam_sell_count or 0,
            '悠品在售数': item.youpin_sell_count or 0
        })

    # 显示表格
    df = pd.DataFrame(table_data)

    # 使用样式突出显示不同等级的搬砖机会
    def highlight_opportunities(row):
        if "🔥 超高" in row['机会等级']:
            return ['background-color: #ffebee; color: #c62828'] * len(row)  # 红色背景
        elif "⭐ 高" in row['机会等级']:
            return ['background-color: #fff3e0; color: #ef6c00'] * len(row)  # 橙色背景
        elif "✅ 中" in row['机会等级']:
            return ['background-color: #e8f5e8; color: #2e7d32'] * len(row)  # 绿色背景
        return [''] * len(row)

    styled_df = df.style.apply(highlight_opportunities, axis=1)
    st.dataframe(styled_df, use_container_width=True, hide_index=True)


def show_arbitrage_item_card(item):
    """显示搬砖数据卡片 - 搬砖专用格式"""
    # 计算搬砖相关数据
    steam_price = float(item.steam_price) if item.steam_price else 0
    youpin_price = float(item.youpin_price) if item.youpin_price else 0
    profit = float(item.youpin_profit) if item.youpin_profit else 0
    profit_rate = float(item.youpin_profit_rate) * 100 if item.youpin_profit_rate else 0

    # 计算价差
    price_diff = youpin_price - steam_price if steam_price > 0 and youpin_price > 0 else 0

    # 设置卡片样式（简化版）
    border_color = "#e0e0e0"  # 统一的边框颜色
    bg_color = "#fafafa"  # 统一的背景颜色

    with st.container():

        # 第一行：饰品基础信息
        st.markdown(f"### {item.market_name}")

        # 基础信息行：武器类型 | 品质等级 | 品质类型 | Hash Name | Item ID
        info_parts = []
        if item.goods_type01_name:
            info_parts.append(f"**{item.goods_type01_name}**")
        if item.goods_level_name:
            info_parts.append(f"**{item.goods_level_name}**")
        if item.goods_quality_name:
            info_parts.append(f"*{item.goods_quality_name}*")
        if item.market_hash_name:
            info_parts.append(f"`{item.market_hash_name}`")
        if item.item_id:
            info_parts.append(f"🆔 `{item.item_id}`")

        if info_parts:
            st.markdown(" | ".join(info_parts))

        st.divider()

        # 平台数据对比表格
        st.markdown("**📊 平台数据对比**")

        # 准备表格数据
        steam_buy_price = float(item.steam_buy_price) if item.steam_buy_price else 0
        youpin_purchase_price = float(item.youpin_purchase_price) if item.youpin_purchase_price else 0
        steam_inventory = item.steam_sell_count or 0
        youpin_inventory = item.youpin_sell_count or 0
        steam_buy_count = item.steam_buy_count or 0
        youpin_purchase_count = item.youpin_purchase_count or 0
        steam_24_sell = item.steam_24_sell_count or 0

        # 创建对比表格数据
        # 美元汇率
        usd_to_cny_rate = 7.21

        comparison_data = {
            "平台": ["Steam", "悠品"],
            "在售价": [
                f"¥{steam_price * usd_to_cny_rate:.2f} (${steam_price:.2f})" if steam_price > 0 else "-",
                f"¥{youpin_price:.2f}" if youpin_price > 0 else "-"
            ],
            "求购价": [
                f"¥{steam_buy_price * usd_to_cny_rate:.2f} (${steam_buy_price:.2f})" if steam_buy_price > 0 else "-",
                f"¥{youpin_purchase_price:.2f}" if youpin_purchase_price > 0 else "-"
            ],
            "在售量": [
                str(steam_inventory) if steam_inventory > 0 else "-",
                str(youpin_inventory) if youpin_inventory > 0 else "-"
            ],
            "求购数": [
                str(steam_buy_count) if steam_buy_count > 0 else "-",
                str(youpin_purchase_count) if youpin_purchase_count > 0 else "-"
            ],
            "今日销量": [
                str(steam_24_sell) if steam_24_sell > 0 else "-",
                "-"  # 悠品没有今日销量数据
            ]
        }

        # 显示对比表格
        comparison_df = pd.DataFrame(comparison_data)
        st.dataframe(comparison_df, use_container_width=True, hide_index=True)

        # 搬砖分析和操作按钮
        col1, col2 = st.columns([4, 1])

        with col1:
            st.markdown("**💰 搬砖分析**")
            analysis_parts = []

            if price_diff > 0:
                analysis_parts.append(f"价差: ¥{price_diff:.2f}")
            if profit_rate > 0:
                analysis_parts.append(f"利润率: {profit_rate:.1f}%")
            if profit > 0:
                analysis_parts.append(f"预计利润: ¥{profit:.2f}")

            if analysis_parts:
                st.markdown(" | ".join(analysis_parts))
            else:
                st.markdown("暂无搬砖分析数据")

        with col2:
            # 分析按钮 - 只有当item_id存在时才显示
            if item.item_id:
                item_analysis_component.render_analysis_button(
                    item_id=item.item_id,
                    button_key=f"arbitrage_analysis_{item.item_id}",
                    help_text="查看饰品分析数据",
                    dialog_key_suffix="_arbitrage"
                )
            else:
                st.caption("无item_id")
                st.caption("无法分析")

        # 第六行：其他有用信息
        other_info = []
        if item.has_buy_desc:
            other_info.append(f"💬 {item.has_buy_desc}")
        if item.inventory_num:
            other_info.append(f"📋 库存号: {item.inventory_num}")
        if item.collect_id:
            other_info.append(f"⭐ 收藏ID: {item.collect_id}")

        if other_info:
            st.caption(" | ".join(other_info))

        # 分析对话框 - 只有当item_id存在时才渲染
        if item.item_id:
            item_analysis_component.render_analysis_dialog(
                item_data={
                    'item_id': item.item_id,
                    'item_name': item.market_name
                },
                dialog_key_suffix="_arbitrage"
            )


if __name__ == "__main__":
    show_page()
