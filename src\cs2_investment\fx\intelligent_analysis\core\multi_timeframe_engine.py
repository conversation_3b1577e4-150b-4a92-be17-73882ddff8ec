"""
多时间框架分析引擎

协调战略(周K)、战术(日K)、执行(时K)、供需(走势数据)四个维度的分析，
实现多时间框架数据的统一管理和分析协调。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from loguru import logger

from .unified_data_manager import UnifiedDataManager
from ..strategies.strategic_analyzer import StrategicAnalyzer
from ..strategies.tactical_analyzer import TacticalAnalyzer
from ..strategies.execution_analyzer import ExecutionAnalyzer
from ..indicators.supply_demand_analyzer import SupplyDemandAnalyzer


class MultiTimeframeEngine:
    """多时间框架分析引擎"""
    
    def __init__(self, item_id: str, data_base_path: str = "data/scraped_data"):
        """
        初始化多时间框架分析引擎
        
        Args:
            item_id: 饰品ID
            data_base_path: 数据基础路径
        """
        self.item_id = item_id
        self.data_base_path = data_base_path
        self.logger = logger.bind(engine=self.__class__.__name__, item_id=item_id)
        
        # 初始化数据管理器
        self.data_manager = UnifiedDataManager(item_id, data_base_path)
        
        # 分析器实例
        self.strategic_analyzer = None
        self.tactical_analyzer = None
        self.execution_analyzer = None
        self.supply_demand_analyzer = None
        
        # 分析结果缓存
        self.analysis_cache = {}
        
        # 数据状态
        self.data_loaded = False
        self.multi_timeframe_data = {}
    
    def initialize_analyzers(self) -> bool:
        """初始化各个分析器"""
        try:
            self.logger.info("开始初始化多时间框架分析器...")
            
            # 加载多时间框架数据
            self.multi_timeframe_data = self.data_manager.load_multi_timeframe_data()
            
            if not self.multi_timeframe_data.get('success', False):
                self.logger.error("多时间框架数据加载失败")
                return False
            
            # 提取各时间框架数据
            kline_data = self.multi_timeframe_data['data_sources'].get('kline', {}).get('data', {})
            supply_demand_data = self.multi_timeframe_data['data_sources'].get('supply_demand', {}).get('data', {})
            
            # 保存数据引用
            self.weekly_data = kline_data.get('strategic', pd.DataFrame())
            self.daily_data = kline_data.get('tactical', pd.DataFrame())
            self.hourly_data = kline_data.get('execution', pd.DataFrame())

            # 初始化战略分析器 (周K)
            if self.weekly_data is not None and not self.weekly_data.empty:
                self.strategic_analyzer = StrategicAnalyzer(self.weekly_data)
                self.logger.info("战略分析器初始化成功")
            else:
                self.logger.warning("周K数据不足，战略分析器初始化失败")

            # 初始化战术分析器 (日K)
            if self.daily_data is not None and not self.daily_data.empty:
                self.tactical_analyzer = TacticalAnalyzer(self.daily_data, self.weekly_data)
                self.logger.info("战术分析器初始化成功")
            else:
                self.logger.warning("日K数据不足，战术分析器初始化失败")

            # 初始化执行分析器 (时K)
            if self.hourly_data is not None and not self.hourly_data.empty:
                self.execution_analyzer = ExecutionAnalyzer(self.hourly_data, self.daily_data)
                self.logger.info("执行分析器初始化成功")
            else:
                self.logger.warning("时K数据不足，执行分析器初始化失败")
            
            # 初始化供需分析器
            if supply_demand_data:
                self.supply_demand_analyzer = SupplyDemandAnalyzer(supply_demand_data)
                self.logger.info("供需分析器初始化成功")
            else:
                self.logger.warning("供需数据不足，供需分析器初始化失败")
            
            self.data_loaded = True
            self.logger.info("多时间框架分析器初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"分析器初始化失败: {e}")
            return False
    
    def execute_comprehensive_analysis(self) -> Dict[str, Any]:
        """执行综合分析"""
        try:
            if not self.data_loaded:
                if not self.initialize_analyzers():
                    return {
                        'success': False,
                        'error': '分析器初始化失败',
                        'analysis_timestamp': datetime.now()
                    }
            
            self.logger.info("开始执行多时间框架综合分析...")
            
            analysis_result = {
                'item_id': self.item_id,
                'analysis_timestamp': datetime.now(),
                'success': True,
                'timeframe_analyses': {},
                'coordination_analysis': {},
                'comprehensive_conclusion': {}
            }
            
            # 执行各时间框架分析
            timeframe_results = self._execute_timeframe_analyses()
            analysis_result['timeframe_analyses'] = timeframe_results
            
            # 执行协调分析
            coordination_results = self._execute_coordination_analysis(timeframe_results)
            analysis_result['coordination_analysis'] = coordination_results
            
            # 生成综合结论
            comprehensive_conclusion = self._generate_comprehensive_conclusion(
                timeframe_results, coordination_results
            )
            analysis_result['comprehensive_conclusion'] = comprehensive_conclusion
            
            # 缓存结果
            self.analysis_cache['latest_comprehensive'] = analysis_result
            
            self.logger.info("多时间框架综合分析完成")
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"综合分析执行失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'analysis_timestamp': datetime.now()
            }
    
    def _execute_timeframe_analyses(self) -> Dict[str, Any]:
        """执行各时间框架分析"""
        timeframe_results = {
            'strategic': {'success': False, 'data': {}},
            'tactical': {'success': False, 'data': {}},
            'execution': {'success': False, 'data': {}},
            'supply_demand': {'success': False, 'data': {}}
        }
        
        # 战略分析
        if self.strategic_analyzer:
            try:
                strategic_report = self.strategic_analyzer.generate_strategic_analysis_report()
                timeframe_results['strategic'] = {
                    'success': True,
                    'data': strategic_report
                }
                self.logger.info("战略分析执行成功")
            except Exception as e:
                self.logger.error(f"战略分析执行失败: {e}")
                timeframe_results['strategic']['error'] = str(e)
        
        # 战术分析
        if self.tactical_analyzer:
            try:
                tactical_report = self.tactical_analyzer.generate_tactical_analysis_report()
                timeframe_results['tactical'] = {
                    'success': True,
                    'data': tactical_report
                }
                self.logger.info("战术分析执行成功")
            except Exception as e:
                self.logger.error(f"战术分析执行失败: {e}")
                timeframe_results['tactical']['error'] = str(e)
        
        # 执行分析
        if self.execution_analyzer:
            try:
                execution_report = self.execution_analyzer.generate_execution_analysis_report()
                timeframe_results['execution'] = {
                    'success': True,
                    'data': execution_report
                }
                self.logger.info("执行分析执行成功")
            except Exception as e:
                self.logger.error(f"执行分析执行失败: {e}")
                timeframe_results['execution']['error'] = str(e)
        
        # 供需分析
        if self.supply_demand_analyzer:
            try:
                supply_demand_report = self.supply_demand_analyzer.analyze_comprehensive()
                timeframe_results['supply_demand'] = {
                    'success': True,
                    'data': supply_demand_report
                }
                self.logger.info("供需分析执行成功")
            except Exception as e:
                self.logger.error(f"供需分析执行失败: {e}")
                timeframe_results['supply_demand']['error'] = str(e)
        
        return timeframe_results
    
    def _execute_coordination_analysis(self, timeframe_results: Dict) -> Dict[str, Any]:
        """执行协调分析"""
        try:
            coordination_analysis = {
                'trend_consistency': self._analyze_trend_consistency(timeframe_results),
                'signal_alignment': self._analyze_signal_alignment(timeframe_results),
                'risk_coordination': self._analyze_risk_coordination(timeframe_results),
                'timing_analysis': self._analyze_timing_coordination(timeframe_results)
            }
            
            return coordination_analysis
            
        except Exception as e:
            self.logger.error(f"协调分析失败: {e}")
            return {
                'trend_consistency': {'status': 'ERROR', 'error': str(e)},
                'signal_alignment': {'status': 'ERROR', 'error': str(e)},
                'risk_coordination': {'status': 'ERROR', 'error': str(e)},
                'timing_analysis': {'status': 'ERROR', 'error': str(e)}
            }
    
    def _analyze_trend_consistency(self, timeframe_results: Dict) -> Dict[str, Any]:
        """分析趋势一致性"""
        try:
            trends = {}
            
            # 提取各时间框架趋势
            if timeframe_results['strategic']['success']:
                strategic_data = timeframe_results['strategic']['data']
                trend_direction = strategic_data.get('trend_analysis', {}).get('trend_direction', 'UNKNOWN')
                trends['strategic'] = self._normalize_trend_direction(trend_direction)

            if timeframe_results['tactical']['success']:
                tactical_data = timeframe_results['tactical']['data']
                trend_status = tactical_data.get('trend_analysis', {}).get('trend_status', 'UNKNOWN')
                trends['tactical'] = self._normalize_trend_direction(trend_status)
            
            if timeframe_results['execution']['success']:
                execution_data = timeframe_results['execution']['data']
                immediate_action = execution_data.get('immediate_action', {}).get('immediate_action', 'UNKNOWN')
                # 将执行动作转换为趋势
                if immediate_action in ['STRONG_BUY', 'BUY']:
                    trends['execution'] = 'UPTREND'
                elif immediate_action in ['STRONG_SELL', 'SELL']:
                    trends['execution'] = 'DOWNTREND'
                else:
                    trends['execution'] = 'SIDEWAYS'
            
            # 分析一致性
            trend_values = list(trends.values())
            valid_trends = [t for t in trend_values if t != 'UNKNOWN']
            
            if not valid_trends:
                return {
                    'status': 'NO_DATA',
                    'consistency_score': 0,
                    'trends': trends
                }
            
            # 计算一致性评分
            uptrend_count = sum(1 for t in valid_trends if 'UP' in t.upper())
            downtrend_count = sum(1 for t in valid_trends if 'DOWN' in t.upper())
            sideways_count = sum(1 for t in valid_trends if 'SIDE' in t.upper())
            
            total_count = len(valid_trends)
            max_count = max(uptrend_count, downtrend_count, sideways_count)
            consistency_score = (max_count / total_count) * 100
            
            # 确定主导趋势
            if uptrend_count == max_count:
                dominant_trend = 'UPTREND'
            elif downtrend_count == max_count:
                dominant_trend = 'DOWNTREND'
            else:
                dominant_trend = 'SIDEWAYS'
            
            return {
                'status': 'SUCCESS',
                'consistency_score': consistency_score,
                'dominant_trend': dominant_trend,
                'trends': trends,
                'trend_distribution': {
                    'uptrend': uptrend_count,
                    'downtrend': downtrend_count,
                    'sideways': sideways_count
                }
            }
            
        except Exception as e:
            return {
                'status': 'ERROR',
                'error': str(e),
                'consistency_score': 0
            }
    
    def _analyze_signal_alignment(self, timeframe_results: Dict) -> Dict[str, Any]:
        """分析信号一致性"""
        try:
            signals = {}
            
            # 提取各时间框架信号
            if timeframe_results['tactical']['success']:
                tactical_data = timeframe_results['tactical']['data']
                entry_signal = tactical_data.get('entry_signals', {}).get('entry_signal', 'NO_SIGNAL')
                signals['tactical_entry'] = entry_signal
                
                exit_signal = tactical_data.get('exit_signals', {}).get('exit_signal', 'NO_SIGNAL')
                signals['tactical_exit'] = exit_signal
            
            if timeframe_results['execution']['success']:
                execution_data = timeframe_results['execution']['data']
                immediate_action = execution_data.get('immediate_action', {}).get('immediate_action', 'NO_SIGNAL')
                signals['execution_action'] = immediate_action
            
            if timeframe_results['supply_demand']['success']:
                sd_data = timeframe_results['supply_demand']['data']
                combined_analysis = sd_data.get('combined_analysis', {})
                sd_status = combined_analysis.get('combined_supply_demand_status', 'UNKNOWN')
                signals['supply_demand'] = sd_status
            
            # 分析信号一致性
            buy_signals = []
            sell_signals = []
            
            for signal_type, signal_value in signals.items():
                if signal_value in ['BUY', 'STRONG_BUY', 'DEMAND_EXCESS']:
                    buy_signals.append(signal_type)
                elif signal_value in ['SELL', 'STRONG_SELL', 'SUPPLY_EXCESS']:
                    sell_signals.append(signal_type)
            
            total_signals = len([s for s in signals.values() if s not in ['NO_SIGNAL', 'UNKNOWN', 'HOLD']])
            
            if total_signals == 0:
                alignment_score = 0
                alignment_direction = 'NO_CLEAR_SIGNAL'
            else:
                buy_ratio = len(buy_signals) / total_signals
                sell_ratio = len(sell_signals) / total_signals
                
                if buy_ratio > 0.6:
                    alignment_direction = 'BUY'
                    alignment_score = buy_ratio * 100
                elif sell_ratio > 0.6:
                    alignment_direction = 'SELL'
                    alignment_score = sell_ratio * 100
                else:
                    alignment_direction = 'MIXED'
                    alignment_score = 50
            
            return {
                'status': 'SUCCESS',
                'alignment_score': alignment_score,
                'alignment_direction': alignment_direction,
                'signals': signals,
                'buy_signals': buy_signals,
                'sell_signals': sell_signals
            }
            
        except Exception as e:
            return {
                'status': 'ERROR',
                'error': str(e),
                'alignment_score': 0
            }

    def _analyze_risk_coordination(self, timeframe_results: Dict) -> Dict[str, Any]:
        """分析风险协调"""
        try:
            risk_levels = {}

            # 提取各时间框架风险等级
            if timeframe_results['strategic']['success']:
                strategic_data = timeframe_results['strategic']['data']
                risk_level_raw = strategic_data.get('risk_assessment', {}).get('risk_level', 'UNKNOWN')
                risk_levels['strategic'] = self._normalize_risk_level(risk_level_raw)

            if timeframe_results['tactical']['success']:
                tactical_data = timeframe_results['tactical']['data']
                risk_levels['tactical'] = tactical_data.get('risk_assessment', {}).get('tactical_risk_level', 'UNKNOWN')

            if timeframe_results['execution']['success']:
                execution_data = timeframe_results['execution']['data']
                risk_levels['execution'] = execution_data.get('risk_assessment', {}).get('execution_risk_level', 'UNKNOWN')

            # 风险等级数值化
            risk_scores = {}
            risk_mapping = {'LOW': 20, 'MEDIUM': 50, 'HIGH': 80, 'UNKNOWN': 50}

            for timeframe, risk_level in risk_levels.items():
                risk_scores[timeframe] = risk_mapping.get(risk_level, 50)

            # 计算综合风险
            if risk_scores:
                avg_risk_score = np.mean(list(risk_scores.values()))
                max_risk_score = max(risk_scores.values())

                # 综合风险等级
                if max_risk_score >= 80:
                    overall_risk = 'HIGH'
                elif avg_risk_score >= 60:
                    overall_risk = 'MEDIUM'
                else:
                    overall_risk = 'LOW'

                # 风险一致性
                risk_std = np.std(list(risk_scores.values()))
                risk_consistency = max(0, 100 - risk_std)
            else:
                avg_risk_score = 50
                overall_risk = 'UNKNOWN'
                risk_consistency = 0

            return {
                'status': 'SUCCESS',
                'overall_risk': overall_risk,
                'avg_risk_score': avg_risk_score,
                'risk_consistency': risk_consistency,
                'risk_levels': risk_levels,
                'risk_scores': risk_scores
            }

        except Exception as e:
            return {
                'status': 'ERROR',
                'error': str(e),
                'overall_risk': 'UNKNOWN'
            }

    def _analyze_timing_coordination(self, timeframe_results: Dict) -> Dict[str, Any]:
        """分析时机协调"""
        try:
            timing_factors = {}

            # 战略时机
            if timeframe_results['strategic']['success']:
                strategic_data = timeframe_results['strategic']['data']
                investment_value_raw = strategic_data.get('investment_value', {}).get('investment_value_score', 0)

                # 确保investment_value是数值类型
                try:
                    investment_value = float(investment_value_raw) if investment_value_raw is not None else 50
                except (ValueError, TypeError):
                    investment_value = 50

                timing_factors['strategic_value'] = investment_value

            # 战术时机
            if timeframe_results['tactical']['success']:
                tactical_data = timeframe_results['tactical']['data']
                entry_strength_raw = tactical_data.get('entry_signals', {}).get('signal_strength', 0)

                # 确保entry_strength是数值类型
                if isinstance(entry_strength_raw, str):
                    # 将字符串强度转换为数值
                    strength_mapping = {
                        'WEAK': 20, 'LOW': 20,
                        'MODERATE': 50, 'MEDIUM': 50,
                        'STRONG': 80, 'HIGH': 80,
                        'VERY_STRONG': 90, 'VERY_HIGH': 90,
                        'NONE': 0, 'ERROR': 0, 'UNKNOWN': 30
                    }
                    entry_strength = strength_mapping.get(entry_strength_raw.upper(), 30)
                else:
                    try:
                        entry_strength = float(entry_strength_raw) if entry_strength_raw is not None else 30
                    except (ValueError, TypeError):
                        entry_strength = 30

                timing_factors['tactical_entry'] = entry_strength

            # 执行时机
            if timeframe_results['execution']['success']:
                execution_data = timeframe_results['execution']['data']
                urgency = execution_data.get('immediate_action', {}).get('urgency_level', 'LOW')
                urgency_score = {'LOW': 20, 'MEDIUM': 60, 'HIGH': 90}.get(urgency, 20)
                timing_factors['execution_urgency'] = urgency_score

            # 供需时机
            if timeframe_results['supply_demand']['success']:
                sd_data = timeframe_results['supply_demand']['data']
                balance_score_raw = sd_data.get('market_balance_score', 50)

                # 确保balance_score是数值类型
                try:
                    balance_score = float(balance_score_raw) if balance_score_raw is not None else 50
                except (ValueError, TypeError):
                    balance_score = 50

                timing_factors['supply_demand_balance'] = balance_score

            # 综合时机评分
            if timing_factors:
                timing_score = np.mean(list(timing_factors.values()))

                if timing_score >= 80:
                    timing_quality = 'EXCELLENT'
                elif timing_score >= 60:
                    timing_quality = 'GOOD'
                elif timing_score >= 40:
                    timing_quality = 'FAIR'
                else:
                    timing_quality = 'POOR'
            else:
                timing_score = 50
                timing_quality = 'UNKNOWN'

            return {
                'status': 'SUCCESS',
                'timing_score': timing_score,
                'timing_quality': timing_quality,
                'timing_factors': timing_factors
            }

        except Exception as e:
            return {
                'status': 'ERROR',
                'error': str(e),
                'timing_score': 50
            }

    def _generate_comprehensive_conclusion(self, timeframe_results: Dict, coordination_results: Dict) -> Dict[str, Any]:
        """生成综合结论"""
        try:
            conclusion = {
                'final_recommendation': 'HOLD',
                'confidence_level': 50,
                'investment_horizon': 'MEDIUM_TERM',
                'key_insights': [],
                'risk_warnings': [],
                'action_plan': {}
            }

            # 基于趋势一致性
            trend_consistency = coordination_results.get('trend_consistency', {})
            if trend_consistency.get('status') == 'SUCCESS':
                consistency_score = trend_consistency.get('consistency_score', 0)
                dominant_trend = trend_consistency.get('dominant_trend', 'SIDEWAYS')

                if consistency_score > 70:
                    if dominant_trend == 'UPTREND':
                        conclusion['final_recommendation'] = 'BUY'
                        conclusion['confidence_level'] += 20
                        conclusion['key_insights'].append(f"多时间框架趋势高度一致({consistency_score:.1f}%)，呈上升趋势")
                    elif dominant_trend == 'DOWNTREND':
                        conclusion['final_recommendation'] = 'SELL'
                        conclusion['confidence_level'] += 20
                        conclusion['key_insights'].append(f"多时间框架趋势高度一致({consistency_score:.1f}%)，呈下降趋势")
                elif consistency_score < 40:
                    conclusion['risk_warnings'].append("多时间框架趋势不一致，市场方向不明确")
                    conclusion['confidence_level'] -= 15

            # 基于信号一致性
            signal_alignment = coordination_results.get('signal_alignment', {})
            if signal_alignment.get('status') == 'SUCCESS':
                alignment_score = signal_alignment.get('alignment_score', 0)
                alignment_direction = signal_alignment.get('alignment_direction', 'MIXED')

                if alignment_score > 70:
                    if alignment_direction == 'BUY':
                        conclusion['final_recommendation'] = 'BUY'
                        conclusion['confidence_level'] += 15
                        conclusion['key_insights'].append(f"多维度信号高度一致({alignment_score:.1f}%)，支持买入")
                    elif alignment_direction == 'SELL':
                        conclusion['final_recommendation'] = 'SELL'
                        conclusion['confidence_level'] += 15
                        conclusion['key_insights'].append(f"多维度信号高度一致({alignment_score:.1f}%)，支持卖出")

            # 基于风险协调
            risk_coordination = coordination_results.get('risk_coordination', {})
            if risk_coordination.get('status') == 'SUCCESS':
                overall_risk = risk_coordination.get('overall_risk', 'MEDIUM')

                if overall_risk == 'HIGH':
                    conclusion['confidence_level'] -= 20
                    conclusion['risk_warnings'].append("整体风险水平较高，建议谨慎操作")
                elif overall_risk == 'LOW':
                    conclusion['confidence_level'] += 10
                    conclusion['key_insights'].append("整体风险水平较低，操作环境良好")

            # 基于时机协调
            timing_analysis = coordination_results.get('timing_analysis', {})
            if timing_analysis.get('status') == 'SUCCESS':
                timing_quality = timing_analysis.get('timing_quality', 'FAIR')
                timing_score = timing_analysis.get('timing_score', 50)

                if timing_quality == 'EXCELLENT':
                    conclusion['confidence_level'] += 15
                    conclusion['key_insights'].append(f"时机选择优秀({timing_score:.1f}分)")
                elif timing_quality == 'POOR':
                    conclusion['confidence_level'] -= 10
                    conclusion['risk_warnings'].append(f"时机选择较差({timing_score:.1f}分)")

            # 确定投资期限
            if timeframe_results['strategic']['success']:
                strategic_data = timeframe_results['strategic']['data']
                investment_value = strategic_data.get('investment_value', {}).get('investment_value_score', 0)

                if investment_value > 70:
                    conclusion['investment_horizon'] = 'LONG_TERM'
                elif investment_value < 40:
                    conclusion['investment_horizon'] = 'SHORT_TERM'

            # 生成行动计划
            conclusion['action_plan'] = self._generate_action_plan(
                conclusion['final_recommendation'],
                timeframe_results,
                coordination_results
            )

            # 限制置信度范围
            conclusion['confidence_level'] = max(0, min(100, conclusion['confidence_level']))

            return conclusion

        except Exception as e:
            self.logger.error(f"综合结论生成失败: {e}")
            return {
                'final_recommendation': 'HOLD',
                'confidence_level': 50,
                'investment_horizon': 'MEDIUM_TERM',
                'key_insights': [],
                'risk_warnings': [f"结论生成异常: {str(e)}"],
                'action_plan': {}
            }

    def _generate_action_plan(self, recommendation: str, timeframe_results: Dict, coordination_results: Dict) -> Dict[str, Any]:
        """生成行动计划"""
        try:
            action_plan = {
                'immediate_actions': [],
                'short_term_plan': [],
                'medium_term_plan': [],
                'long_term_plan': [],
                'risk_management': {}
            }

            # 基于推荐行动生成计划
            if recommendation == 'BUY':
                action_plan['immediate_actions'].append("寻找合适的买入时机")
                action_plan['short_term_plan'].append("监控入场信号确认")
                action_plan['medium_term_plan'].append("持有并观察趋势发展")
                action_plan['long_term_plan'].append("根据基本面变化调整持仓")

            elif recommendation == 'SELL':
                action_plan['immediate_actions'].append("考虑减仓或清仓")
                action_plan['short_term_plan'].append("监控反弹机会")
                action_plan['medium_term_plan'].append("等待趋势反转信号")
                action_plan['long_term_plan'].append("重新评估投资价值")

            else:  # HOLD
                action_plan['immediate_actions'].append("维持当前仓位")
                action_plan['short_term_plan'].append("密切关注市场变化")
                action_plan['medium_term_plan'].append("等待明确方向信号")
                action_plan['long_term_plan'].append("定期重新评估")

            # 风险管理建议
            if timeframe_results['execution']['success']:
                execution_data = timeframe_results['execution']['data']
                stop_loss_tp = execution_data.get('stop_loss_take_profit', {})

                action_plan['risk_management'] = {
                    'stop_loss': stop_loss_tp.get('stop_loss_price'),
                    'take_profit_1': stop_loss_tp.get('take_profit_price_1'),
                    'take_profit_2': stop_loss_tp.get('take_profit_price_2'),
                    'position_sizing': '根据风险承受能力确定仓位大小'
                }

            return action_plan

        except Exception as e:
            return {
                'immediate_actions': ['维持观望'],
                'short_term_plan': ['等待更多信息'],
                'medium_term_plan': ['定期重新评估'],
                'long_term_plan': ['关注基本面变化'],
                'risk_management': {'error': str(e)}
            }

    def get_latest_analysis(self) -> Optional[Dict[str, Any]]:
        """获取最新分析结果"""
        return self.analysis_cache.get('latest_comprehensive')

    def get_data_quality_report(self) -> Dict[str, Any]:
        """获取数据质量报告"""
        if not self.multi_timeframe_data:
            return {'status': 'NO_DATA'}

        return self.multi_timeframe_data.get('quality_report', {'status': 'UNKNOWN'})

    def _normalize_trend_direction(self, trend_direction: str) -> str:
        """将中文趋势描述转换为标准化值"""
        if not trend_direction or trend_direction == 'UNKNOWN':
            return 'UNKNOWN'

        trend_lower = trend_direction.lower()

        # 强烈看涨
        if any(keyword in trend_lower for keyword in ['强烈看涨', '强烈上涨', '大涨']):
            return 'STRONG_UP'
        # 看涨
        elif any(keyword in trend_lower for keyword in ['看涨', '上涨', '涨', '向上', '偏涨']):
            return 'UP'
        # 强烈看跌
        elif any(keyword in trend_lower for keyword in ['强烈看跌', '强烈下跌', '大跌']):
            return 'STRONG_DOWN'
        # 看跌
        elif any(keyword in trend_lower for keyword in ['看跌', '下跌', '跌', '向下', '偏跌']):
            return 'DOWN'
        # 中性/横盘
        elif any(keyword in trend_lower for keyword in ['中性', '横盘', '震荡', '盘整', '平稳']):
            return 'SIDEWAYS'
        else:
            return 'UNKNOWN'

    def _normalize_risk_level(self, risk_level: str) -> str:
        """将中文风险等级转换为标准化值"""
        if not risk_level or risk_level == 'UNKNOWN':
            return 'UNKNOWN'

        risk_lower = risk_level.lower()

        # 高风险
        if any(keyword in risk_lower for keyword in ['高', 'high', '很高', '极高']):
            return 'HIGH'
        # 中等风险
        elif any(keyword in risk_lower for keyword in ['中', 'medium', '中等', '适中']):
            return 'MEDIUM'
        # 低风险
        elif any(keyword in risk_lower for keyword in ['低', 'low', '很低', '极低']):
            return 'LOW'
        else:
            return 'UNKNOWN'
