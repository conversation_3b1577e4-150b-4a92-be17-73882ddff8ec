"""
投资推荐卡片组件

用于显示单个投资推荐的详细信息。
"""

import streamlit as st
import json
from datetime import datetime
from typing import Optional, Dict, Any
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.models.investment_recommendation import InvestmentRecommendation
from src.cs2_investment.dao.item_dao import item_dao


def show_recommendation_card(recommendation: InvestmentRecommendation, expanded: bool = False):
    """显示推荐卡片"""
    
    # 获取饰品信息
    item = item_dao.get_by_item_id(recommendation.item_id)
    item_name = item.get('name') if item else recommendation.item_id
    
    # 推荐类型样式
    type_styles = {
        'BUY': {'color': '#00C851', 'icon': '📈', 'text': '买入'},
        'HOLD': {'color': '#ffbb33', 'icon': '⏸️', 'text': '持有'},
        'SELL': {'color': '#ff4444', 'icon': '📉', 'text': '卖出'},
        'AVOID': {'color': '#9E9E9E', 'icon': '⛔', 'text': '避免'}
    }
    
    # 风险等级样式
    risk_styles = {
        'LOW': {'color': '#00C851', 'icon': '🟢', 'text': '低风险'},
        'MEDIUM': {'color': '#ffbb33', 'icon': '🟡', 'text': '中等风险'},
        'HIGH': {'color': '#ff4444', 'icon': '🔴', 'text': '高风险'}
    }
    
    type_style = type_styles.get(recommendation.recommendation_type, type_styles['AVOID'])
    risk_style = risk_styles.get(recommendation.risk_level, risk_styles['HIGH'])
    
    # 卡片容器
    with st.container():
        # 卡片头部
        col1, col2, col3, col4 = st.columns([3, 1, 1, 1])
        
        with col1:
            st.markdown(f"### {item_name}")
            st.caption(f"饰品ID: {recommendation.item_id}")
        
        with col2:
            st.markdown(f"""
            <div style="text-align: center; padding: 10px; background-color: {type_style['color']}20; 
                        border-radius: 10px; border: 2px solid {type_style['color']};">
                <div style="font-size: 24px;">{type_style['icon']}</div>
                <div style="font-weight: bold; color: {type_style['color']};">{type_style['text']}</div>
            </div>
            """, unsafe_allow_html=True)
        
        with col3:
            # 根据评分显示不同的样式
            if recommendation.total_score > 0:
                st.metric("综合评分", f"{recommendation.total_score:.1f}",
                         delta=f"{recommendation.total_score - 70:.1f}" if recommendation.total_score > 70 else None)
            else:
                st.markdown(f"""
                <div style="text-align: center; padding: 10px; background-color: #ffebee;
                            border-radius: 10px; border: 2px solid #ff4444;">
                    <div style="font-size: 24px;">❌</div>
                    <div style="font-weight: bold; color: #ff4444;">不推荐</div>
                    <div style="font-size: 12px; color: #666;">评分: 0.0</div>
                </div>
                """, unsafe_allow_html=True)
        
        with col4:
            st.markdown(f"""
            <div style="text-align: center; padding: 10px;">
                <div style="font-size: 20px;">{risk_style['icon']}</div>
                <div style="font-size: 12px; color: {risk_style['color']};">{risk_style['text']}</div>
            </div>
            """, unsafe_allow_html=True)
        
        # 基本信息行
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if recommendation.confidence_level:
                st.metric("置信度", f"{recommendation.confidence_level:.1f}%")
            else:
                st.metric("置信度", "N/A")
        
        with col2:
            st.metric("算法数量", recommendation.algorithm_count)
        
        with col3:
            if recommendation.current_price:
                st.metric("当前价格", f"¥{recommendation.current_price:.2f}")
            else:
                st.metric("当前价格", "N/A")
        
        with col4:
            if recommendation.hot_rank:
                st.metric("热度排名", f"#{recommendation.hot_rank}")
            else:
                st.metric("热度排名", "N/A")
        
        # 价格变化信息
        if recommendation.price_change_7d or recommendation.price_change_30d:
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if recommendation.price_change_7d:
                    delta_7d = recommendation.price_change_7d
                    st.metric("7天变化", f"{delta_7d:+.2f}%", delta=f"{delta_7d:+.2f}%")
            
            with col2:
                if recommendation.price_change_30d:
                    delta_30d = recommendation.price_change_30d
                    st.metric("30天变化", f"{delta_30d:+.2f}%", delta=f"{delta_30d:+.2f}%")
            
            with col3:
                if recommendation.volume_30d:
                    st.metric("30天成交量", f"{recommendation.volume_30d:,}")
        
        # 推荐理由
        st.markdown("**推荐理由:**")
        st.info(recommendation.recommendation_reason)
        
        # 详细信息（可展开）
        if expanded or st.button(f"查看详情", key=f"detail_{recommendation.id}"):
            show_recommendation_details(recommendation)
        
        # 操作按钮
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("📊 查看分析", key=f"analysis_{recommendation.id}"):
                show_item_analysis(recommendation.item_id)
        
        with col2:
            if st.button("⭐ 添加收藏", key=f"favorite_{recommendation.id}"):
                add_to_favorites(recommendation.item_id)
        
        with col3:
            if st.button("💼 添加持仓", key=f"holding_{recommendation.id}"):
                add_to_holdings(recommendation.item_id)
        
        with col4:
            if st.button("📈 查看趋势", key=f"trend_{recommendation.id}"):
                show_price_trend(recommendation.item_id)
        
        st.markdown("---")


def show_recommendation_details(recommendation: InvestmentRecommendation):
    """显示推荐详细信息"""
    
    with st.expander("详细信息", expanded=True):
        # 基本信息
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**基本信息**")
            st.write(f"推荐日期: {recommendation.recommendation_date}")
            st.write(f"推荐时间: {recommendation.recommendation_time.strftime('%Y-%m-%d %H:%M:%S')}")
            st.write(f"会话ID: {recommendation.recommendation_session_id}")
            st.write(f"状态: {recommendation.status}")
            
            if recommendation.valid_until:
                st.write(f"有效期至: {recommendation.valid_until.strftime('%Y-%m-%d %H:%M:%S')}")
        
        with col2:
            st.markdown("**市场数据**")
            if recommendation.amount_30d:
                st.write(f"30天成交额: ¥{recommendation.amount_30d:,.2f}")
            
            st.write(f"创建时间: {recommendation.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            st.write(f"更新时间: {recommendation.updated_at.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 算法详情
        if recommendation.algorithm_details:
            st.markdown("**算法详情**")
            try:
                if isinstance(recommendation.algorithm_details, str):
                    algorithm_data = json.loads(recommendation.algorithm_details)
                else:
                    algorithm_data = recommendation.algorithm_details
                
                if isinstance(algorithm_data, list):
                    for i, algo in enumerate(algorithm_data, 1):
                        with st.expander(f"算法 {i}: {algo.get('investment_type', 'Unknown')}", expanded=False):
                            st.json(algo)
                else:
                    st.json(algorithm_data)
                    
            except (json.JSONDecodeError, TypeError) as e:
                st.error(f"算法详情解析失败: {e}")
                st.text(str(recommendation.algorithm_details))


def show_item_analysis(item_id: str):
    """显示饰品分析"""
    st.info(f"正在跳转到饰品 {item_id} 的详细分析页面...")
    # 这里可以实现跳转到饰品详情页面的逻辑
    st.session_state.selected_item_id = item_id
    st.session_state.current_page = "饰品查询"
    st.session_state.current_view = "detail"
    st.rerun()


def add_to_favorites(item_id: str):
    """添加到收藏"""
    try:
        from src.cs2_investment.dao.favorite_dao import favorite_dao
        
        # 这里需要用户ID，暂时使用默认用户
        user_id = "default_user"
        
        result = favorite_dao.add_favorite(user_id, item_id)
        if result:
            st.success(f"已添加到收藏: {item_id}")
        else:
            st.warning("该饰品已在收藏列表中")
    except Exception as e:
        st.error(f"添加收藏失败: {e}")


def add_to_holdings(item_id: str):
    """添加到持仓"""
    st.info(f"正在添加 {item_id} 到持仓管理...")
    # 这里可以实现添加到持仓的逻辑


def show_price_trend(item_id: str):
    """显示价格趋势"""
    st.info(f"正在显示 {item_id} 的价格趋势...")
    # 这里可以实现价格趋势图表的逻辑


def show_recommendation_summary_card(recommendation: InvestmentRecommendation):
    """显示推荐摘要卡片（用于列表视图）"""
    
    # 获取饰品信息
    item = item_dao.get_by_item_id(recommendation.item_id)
    item_name = item.get('name') if item else recommendation.item_id
    
    # 推荐类型图标
    type_icons = {
        'BUY': '📈', 'HOLD': '⏸️', 'SELL': '📉', 'AVOID': '⛔'
    }
    
    risk_icons = {
        'LOW': '🟢', 'MEDIUM': '🟡', 'HIGH': '🔴'
    }
    
    # 摘要卡片
    with st.container():
        col1, col2, col3, col4, col5 = st.columns([3, 1, 1, 1, 1])
        
        with col1:
            st.markdown(f"**{item_name}**")
            st.caption(f"ID: {recommendation.item_id}")
        
        with col2:
            st.markdown(f"{type_icons.get(recommendation.recommendation_type, '❓')} {recommendation.recommendation_type}")
        
        with col3:
            if recommendation.total_score > 0:
                st.markdown(f"**{recommendation.total_score:.1f}**")
                st.caption("评分")
            else:
                st.markdown("**❌ 0.0**")
                st.caption("不推荐")
        
        with col4:
            st.markdown(f"{risk_icons.get(recommendation.risk_level, '❓')} {recommendation.risk_level}")
        
        with col5:
            if recommendation.current_price:
                st.markdown(f"**¥{recommendation.current_price:.2f}**")
            else:
                st.markdown("**N/A**")
            st.caption("价格")
        
        # 简短的推荐理由
        reason_short = recommendation.recommendation_reason[:100] + "..." if len(recommendation.recommendation_reason) > 100 else recommendation.recommendation_reason
        st.caption(reason_short)
        
        st.markdown("---")
