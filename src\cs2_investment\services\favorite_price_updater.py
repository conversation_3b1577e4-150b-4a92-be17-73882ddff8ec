"""
收藏列表价格更新服务

专门用于更新收藏列表中饰品的价格，控制请求频率并保存到平台价格表。
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.favorite_dao import FavoriteDAO
from src.cs2_investment.dao.item_dao import ItemDAO
from src.cs2_investment.dao.platform_price_dao import PlatformPriceDAO
from src.cs2_investment.services.steamdt_api_client import SteamDTAPIClient
from src.cs2_investment.config.database import get_db_session


class FavoritePriceUpdater:
    """收藏列表价格更新器"""
    
    def __init__(self, api_key: str, user_id: str = "default_user"):
        """
        初始化收藏列表价格更新器
        
        Args:
            api_key: SteamDT API密钥
            user_id: 用户ID，默认为default_user
        """
        self.api_key = api_key
        self.user_id = user_id
        self.logger = logging.getLogger(__name__)
        
        # 初始化DAO
        self.favorite_dao = FavoriteDAO()
        self.item_dao = ItemDAO()
        self.platform_price_dao = PlatformPriceDAO()
        
        # 初始化API客户端
        self.api_client = SteamDTAPIClient(api_key)
        
        # 频率控制配置（参考price_data_collector.py）
        self.batch_size = 100  # 每批处理的饰品数量
        self.request_delay = 0.1  # 每次请求间隔（秒）
        self.max_retry_attempts = 3  # 最大重试次数
        
        # 统计信息
        self.stats = {
            'total_favorites': 0,
            'processed_items': 0,
            'successful_updates': 0,
            'failed_updates': 0,
            'api_calls': 0,
            'start_time': None,
            'end_time': None
        }
    
    async def update_favorite_prices(self) -> Dict[str, Any]:
        """
        更新收藏列表中所有饰品的价格
        
        Returns:
            Dict[str, Any]: 更新结果统计
        """
        self.stats['start_time'] = datetime.now()
        self.logger.info(f"开始更新用户 {self.user_id} 的收藏列表价格")
        
        try:
            # 1. 获取收藏列表
            favorites = self.favorite_dao.get_user_favorites(self.user_id)
            self.stats['total_favorites'] = len(favorites)
            
            if not favorites:
                self.logger.warning(f"用户 {self.user_id} 没有收藏任何饰品")
                return self._build_result(success=True, message="没有收藏的饰品需要更新")
            
            self.logger.info(f"找到 {len(favorites)} 个收藏的饰品")
            
            # 2. 获取饰品详细信息（包含market_hash_name）
            item_details = await self._get_item_details(favorites)
            
            if not item_details:
                return self._build_result(success=False, message="无法获取饰品详细信息")
            
            # 3. 提取有效的market_hash_name
            valid_items = [
                item for item in item_details 
                if item.get('market_hash_name')
            ]
            
            if not valid_items:
                return self._build_result(success=False, message="没有找到有效的市场哈希名称")
            
            self.logger.info(f"找到 {len(valid_items)} 个有效的饰品需要更新价格")
            
            # 4. 分批更新价格
            await self._batch_update_prices(valid_items)
            
            # 5. 清理过期数据
            await self._cleanup_old_prices()
            
            self.stats['end_time'] = datetime.now()
            duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
            
            result = self._build_result(
                success=True,
                message=f"收藏列表价格更新完成，耗时 {duration:.1f} 秒"
            )
            
            self.logger.info(f"收藏列表价格更新完成: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"更新收藏列表价格失败: {e}", exc_info=True)
            return self._build_result(success=False, message=f"更新失败: {str(e)}")
    
    async def _get_item_details(self, favorites: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """获取饰品详细信息"""
        item_details = []
        
        for favorite in favorites:
            try:
                item_id = favorite['item_id']
                
                # 从items表获取详细信息
                item_data = self.item_dao.get_by_item_id(item_id)

                if item_data:
                    # item_data已经是字典格式
                    item_data['favorite_info'] = favorite  # 保留收藏信息
                    item_details.append(item_data)
                else:
                    self.logger.warning(f"未找到饰品详细信息: {item_id}")
                    
            except Exception as e:
                self.logger.error(f"获取饰品详细信息失败: {favorite.get('item_id')}, 错误: {e}")
                continue
        
        return item_details
    
    async def _batch_update_prices(self, items: List[Dict[str, Any]]):
        """分批更新价格"""
        total_batches = (len(items) + self.batch_size - 1) // self.batch_size
        
        for batch_index in range(0, len(items), self.batch_size):
            batch_items = items[batch_index:batch_index + self.batch_size]
            batch_num = (batch_index // self.batch_size) + 1
            
            self.logger.info(f"处理第 {batch_num}/{total_batches} 批，包含 {len(batch_items)} 个饰品")
            
            # 提取market_hash_name
            market_hash_names = [item['market_hash_name'] for item in batch_items]
            
            try:
                # 调用API获取价格
                self.stats['api_calls'] += 1
                price_responses = await self.api_client.get_batch_prices(market_hash_names)
                
                # 保存价格数据
                await self._save_batch_prices(batch_items, price_responses)
                
                # 控制请求频率
                if batch_index + self.batch_size < len(items):  # 不是最后一批
                    await asyncio.sleep(self.request_delay)
                    
            except Exception as e:
                self.logger.error(f"处理第 {batch_num} 批失败: {e}")
                self.stats['failed_updates'] += len(batch_items)
                continue
    
    async def _save_batch_prices(self, items: List[Dict[str, Any]], 
                                price_responses: Dict[str, Any]):
        """保存批量价格数据"""
        for item in items:
            try:
                market_hash_name = item['market_hash_name']
                response = price_responses.get(market_hash_name)
                
                if not response or not response.success:
                    self.logger.warning(f"获取价格失败: {market_hash_name}")
                    self.stats['failed_updates'] += 1
                    continue
                
                # 保存每个平台的价格数据（过滤零价格）
                platform_count = 0
                for platform_price in response.platform_prices:
                    # 过滤零价格数据
                    if platform_price.sell_price <= 0 and platform_price.bidding_price <= 0:
                        continue

                    try:
                        # 确定数据源：收藏价格更新器使用的都是SteamDT API数据
                        data_source = 'steamdt'

                        # 标准化平台名称：确保Steam平台统一为大写STEAM
                        platform_name = platform_price.platform
                        if platform_name.upper() == 'STEAM':
                            platform_name = 'STEAM'

                        self.platform_price_dao.save_platform_price(
                            item_id=item['item_id'],
                            market_hash_name=market_hash_name,
                            platform=platform_name,  # 使用标准化后的平台名称
                            platform_item_id=platform_price.platform_item_id,
                            sell_price=platform_price.sell_price,
                            sell_count=platform_price.sell_count,
                            bidding_price=platform_price.bidding_price,
                            bidding_count=platform_price.bidding_count,
                            steamdt_update_time=platform_price.update_time,
                            query_time=response.query_time,
                            data_source=data_source  # 明确设置数据源
                        )
                        platform_count += 1
                    except Exception as e:
                        self.logger.error(f"保存平台价格失败: {market_hash_name} - {platform_price.platform}, 错误: {e}")
                        continue
                
                if platform_count > 0:
                    self.stats['successful_updates'] += 1
                    self.logger.debug(f"成功更新 {market_hash_name} 的 {platform_count} 个平台价格")
                else:
                    self.stats['failed_updates'] += 1
                
                self.stats['processed_items'] += 1
                
            except Exception as e:
                self.logger.error(f"处理饰品价格失败: {item.get('name')}, 错误: {e}")
                self.stats['failed_updates'] += 1
                continue
    
    async def _cleanup_old_prices(self):
        """清理过期价格数据"""
        try:
            # 标记24小时前的数据为无效
            deactivated_count = self.platform_price_dao.deactivate_old_prices(hours=24)
            self.logger.info(f"清理了 {deactivated_count} 条过期价格记录")
        except Exception as e:
            self.logger.error(f"清理过期价格失败: {e}")
    
    def _build_result(self, success: bool, message: str) -> Dict[str, Any]:
        """构建结果字典"""
        duration = 0
        if self.stats['start_time'] and self.stats['end_time']:
            duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
        elif self.stats['start_time']:
            duration = (datetime.now() - self.stats['start_time']).total_seconds()
        
        return {
            'success': success,
            'message': message,
            'user_id': self.user_id,
            'statistics': {
                'total_favorites': self.stats['total_favorites'],
                'processed_items': self.stats['processed_items'],
                'successful_updates': self.stats['successful_updates'],
                'failed_updates': self.stats['failed_updates'],
                'api_calls': self.stats['api_calls'],
                'duration_seconds': round(duration, 2)
            },
            'timestamp': datetime.now().isoformat()
        }
    
    async def test_api_connection(self) -> bool:
        """测试API连接"""
        try:
            return await self.api_client.test_connection()
        except Exception as e:
            self.logger.error(f"API连接测试失败: {e}")
            return False
