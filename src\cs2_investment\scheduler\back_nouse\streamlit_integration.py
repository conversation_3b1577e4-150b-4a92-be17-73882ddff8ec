#!/usr/bin/env python3
"""
Streamlit集成启动器

在Streamlit应用启动时自动启动定时任务调度器
"""

import asyncio
import threading
import logging
import atexit
from datetime import datetime
from typing import Optional

from src.cs2_investment.scheduler.back_nouse.smart_scheduler import smart_scheduler
from src.cs2_investment.api.clients.api_client import api_client

logger = logging.getLogger(__name__)


class StreamlitSchedulerIntegration:
    """Streamlit调度器集成"""
    
    def __init__(self):
        """初始化集成器"""
        self.scheduler_thread: Optional[threading.Thread] = None
        self.event_loop: Optional[asyncio.AbstractEventLoop] = None
        self.is_started = False
    
    def start_scheduler(self):
        """启动调度器（在Streamlit应用启动时调用）"""
        if self.is_started:
            logger.warning("调度器已启动")
            return
        
        logger.info("🚀 Streamlit应用启动，同时启动定时任务调度器")
        
        # 在新线程中运行异步调度器
        self.scheduler_thread = threading.Thread(
            target=self._run_scheduler_in_thread,
            daemon=True,
            name="SchedulerThread"
        )
        self.scheduler_thread.start()
        
        # 注册退出处理
        atexit.register(self.stop_scheduler)
        
        self.is_started = True
        logger.info("✅ 定时任务调度器已在后台启动")
    
    def _run_scheduler_in_thread(self):
        """在线程中运行调度器"""
        try:
            # 创建新的事件循环
            self.event_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.event_loop)
            
            # 运行调度器
            self.event_loop.run_until_complete(smart_scheduler.start())
            
        except Exception as e:
            logger.error(f"调度器线程运行出错: {e}")
        finally:
            if self.event_loop:
                self.event_loop.close()
    
    def stop_scheduler(self):
        """停止调度器"""
        if not self.is_started:
            return
        
        logger.info("🛑 停止定时任务调度器")
        
        try:
            if self.event_loop and not self.event_loop.is_closed():
                # 在事件循环中停止调度器
                future = asyncio.run_coroutine_threadsafe(
                    smart_scheduler.stop(), 
                    self.event_loop
                )
                future.result(timeout=10)  # 等待最多10秒
            
            if self.scheduler_thread and self.scheduler_thread.is_alive():
                self.scheduler_thread.join(timeout=5)
            
        except Exception as e:
            logger.error(f"停止调度器时出错: {e}")
        
        self.is_started = False
        logger.info("✅ 定时任务调度器已停止")
    
    def get_status(self) -> dict:
        """获取调度器状态"""
        if not self.is_started:
            return {
                'integration_status': 'stopped',
                'scheduler_status': None
            }
        
        try:
            scheduler_status = smart_scheduler.get_status()
            return {
                'integration_status': 'running',
                'scheduler_thread_alive': self.scheduler_thread.is_alive() if self.scheduler_thread else False,
                'event_loop_running': self.event_loop and not self.event_loop.is_closed(),
                'scheduler_status': scheduler_status
            }
        except Exception as e:
            return {
                'integration_status': 'error',
                'error': str(e)
            }

    def check_api_service(self) -> dict:
        """检查API服务状态"""
        try:
            is_healthy = api_client.check_service_health()
            service_info = api_client.get_service_info() if is_healthy else None

            return {
                'api_available': is_healthy,
                'api_url': api_client.base_url,
                'service_info': service_info,
                'check_time': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"检查API服务状态失败: {e}")
            return {
                'api_available': False,
                'api_url': api_client.base_url,
                'error': str(e),
                'check_time': datetime.now().isoformat()
            }


# 全局集成实例
streamlit_integration = StreamlitSchedulerIntegration()


def init_scheduler_with_streamlit():
    """初始化调度器与Streamlit集成（在Streamlit应用中调用）"""
    streamlit_integration.start_scheduler()


def get_scheduler_status():
    """获取调度器状态（供Streamlit页面使用）"""
    return streamlit_integration.get_status()


def stop_scheduler():
    """停止调度器（供Streamlit页面使用）"""
    streamlit_integration.stop_scheduler()


# Streamlit应用启动时的自动初始化（已禁用）
def auto_init():
    """自动初始化（已禁用 - 定时任务已迁移至API服务）"""
    try:
        # 检查是否在Streamlit环境中
        import streamlit as st

        # 显示迁移通知而不是启动调度器
        if 'scheduler_migration_notice' not in st.session_state:
            st.session_state.scheduler_migration_notice = True
            logger.info("📱 Streamlit环境检测到，但定时任务已迁移至API服务")
            logger.info("💡 请确保API服务正在运行以获得完整功能")

    except ImportError:
        # 不在Streamlit环境中
        logger.debug("非Streamlit环境")
    except Exception as e:
        logger.error(f"检查Streamlit环境失败: {e}")


# 当模块被导入时自动执行（已禁用自动启动）
if __name__ != "__main__":
    auto_init()
