"""
价格管理页面

提供饰品价格更新的管理界面。
"""

import streamlit as st
import asyncio
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.services.price_update_service import PriceUpdateService
from src.cs2_investment.config.database import get_db_session
from src.cs2_investment.dao.platform_price_dao import PlatformPriceDAO
from src.cs2_investment.models.platform_price import PlatformPrice
from src.cs2_investment.app.utils.data_formatter import format_price, format_number
from src.cs2_investment.app.services.app_scheduler import add_scheduler_controls


def show_page():
    """显示价格管理页面"""
    st.title("💰 价格管理")
    
    # 检查API密钥配置
    api_key = st.secrets.get("steamdt_api_key", "")
    if not api_key:
        st.error("⚠️ 请在 .streamlit/secrets.toml 中配置 steamdt_api_key")
        st.code("""
# .streamlit/secrets.toml
steamdt_api_key = "your_api_key_here"
        """)
        return
    
    # 初始化价格更新服务
    if 'price_service' not in st.session_state:
        st.session_state.price_service = PriceUpdateService(api_key)
    
    price_service = st.session_state.price_service
    
    # 页面布局
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["📊 状态概览", "🤖 自动更新", "🔄 手动更新", "📈 价格数据", "⚙️ 系统设置"])

    with tab1:
        show_status_overview(price_service)

    with tab2:
        add_scheduler_controls()

    with tab3:
        show_manual_update(price_service)

    with tab4:
        show_price_data()

    with tab5:
        show_system_settings(price_service)


def show_status_overview(price_service: PriceUpdateService):
    """显示状态概览"""
    st.subheader("📊 系统状态")
    
    # 获取更新状态
    with st.spinner("获取系统状态..."):
        try:
            status = asyncio.run(price_service.get_update_status())
            
            if 'error' in status:
                st.error(f"获取状态失败: {status['error']}")
                return
            
            # 状态卡片
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                api_status = "🟢 正常" if status.get('api_status') else "🔴 异常"
                st.metric("API状态", api_status)
            
            with col2:
                st.metric("平台数量", status.get('total_platforms', 0))
            
            with col3:
                latest_update = status.get('latest_update')
                if latest_update:
                    update_time = datetime.fromisoformat(latest_update.replace('Z', '+00:00'))
                    time_diff = datetime.now() - update_time.replace(tzinfo=None)
                    hours_ago = int(time_diff.total_seconds() / 3600)
                    st.metric("最后更新", f"{hours_ago}小时前")
                else:
                    st.metric("最后更新", "从未更新")
            
            with col4:
                next_update = status.get('next_update')
                if next_update:
                    next_time = datetime.fromisoformat(next_update.replace('Z', '+00:00'))
                    time_diff = next_time.replace(tzinfo=None) - datetime.now()
                    hours_left = max(0, int(time_diff.total_seconds() / 3600))
                    st.metric("下次更新", f"{hours_left}小时后")
                else:
                    st.metric("下次更新", "未设定")
            
            # 平台统计
            st.subheader("📈 平台统计")
            platform_stats = status.get('platform_stats', [])
            
            if platform_stats:
                df_data = []
                for stats in platform_stats:
                    df_data.append({
                        '平台': stats['platform'],
                        '饰品数量': stats['total_items'],
                        '平均价格': format_price(stats['avg_price']),
                        '最低价格': format_price(stats['min_price']),
                        '最高价格': format_price(stats['max_price']),
                        '在售总数': format_number(stats['total_sell_count']),
                        '求购总数': format_number(stats['total_bidding_count'])
                    })
                
                df = pd.DataFrame(df_data)
                st.dataframe(df, use_container_width=True, hide_index=True)
            else:
                st.info("暂无平台统计数据")
                
        except Exception as e:
            st.error(f"获取状态失败: {e}")


def show_manual_update(price_service: PriceUpdateService):
    """显示手动更新界面"""
    st.subheader("🔄 手动更新价格")
    
    # 更新选项
    update_type = st.radio(
        "更新类型",
        ["全量更新", "指定饰品更新"],
        help="全量更新会更新所有饰品价格，指定饰品更新只更新选定的饰品"
    )
    
    if update_type == "全量更新":
        st.warning("⚠️ 全量更新可能需要较长时间，请耐心等待")
        
        if st.button("🚀 开始全量更新", type="primary"):
            with st.spinner("正在更新所有饰品价格..."):
                try:
                    result = asyncio.run(price_service.update_all_item_prices())
                    
                    if result['success']:
                        st.success("✅ 价格更新完成！")
                        
                        # 显示更新结果
                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            st.metric("总饰品数", result['total_items'])
                        with col2:
                            st.metric("更新成功", result['updated_items'])
                        with col3:
                            st.metric("更新失败", result['failed_items'])
                        with col4:
                            st.metric("耗时(秒)", f"{result['duration_seconds']:.1f}")
                        
                        if result.get('total_platforms'):
                            st.info(f"📊 共更新了 {result['total_platforms']} 个平台的价格数据")
                        
                        if result.get('deactivated_old_records'):
                            st.info(f"🗑️ 清理了 {result['deactivated_old_records']} 条过期记录")
                    else:
                        st.error(f"❌ 更新失败: {result['message']}")
                        
                except Exception as e:
                    st.error(f"❌ 更新过程中发生错误: {e}")
    
    else:  # 指定饰品更新
        st.info("💡 请输入要更新的饰品市场哈希名称，每行一个")
        
        market_hash_names_text = st.text_area(
            "饰品列表",
            placeholder="例如:\nAK-47 | Redline (Field-Tested)\nAWP | Dragon Lore (Factory New)",
            height=150
        )
        
        if st.button("🎯 更新指定饰品", type="primary"):
            if not market_hash_names_text.strip():
                st.warning("请输入要更新的饰品名称")
                return
            
            # 解析饰品名称
            market_hash_names = [
                name.strip() 
                for name in market_hash_names_text.split('\n') 
                if name.strip()
            ]
            
            if not market_hash_names:
                st.warning("请输入有效的饰品名称")
                return
            
            st.info(f"准备更新 {len(market_hash_names)} 个饰品的价格")
            
            with st.spinner("正在更新指定饰品价格..."):
                try:
                    result = asyncio.run(price_service.update_specific_items(market_hash_names))
                    
                    if result['success']:
                        st.success("✅ 指定饰品价格更新完成！")
                        
                        # 显示更新结果
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("总饰品数", result['total_items'])
                        with col2:
                            st.metric("更新成功", result['updated_items'])
                        with col3:
                            st.metric("更新失败", result['failed_items'])
                        
                        if result.get('total_platforms'):
                            st.info(f"📊 共更新了 {result['total_platforms']} 个平台的价格数据")
                    else:
                        st.error(f"❌ 更新失败: {result['message']}")
                        
                except Exception as e:
                    st.error(f"❌ 更新过程中发生错误: {e}")


def show_price_data():
    """显示价格数据"""
    st.subheader("📈 价格数据查看")
    
    # 数据筛选
    col1, col2 = st.columns(2)
    
    with col1:
        # 获取所有平台
        try:
            with get_db_session() as session:
                platform_dao = PlatformPriceDAO(session)
                platforms = platform_dao.get_all_platforms()
                
                selected_platform = st.selectbox(
                    "选择平台",
                    ["全部"] + platforms,
                    help="选择要查看的平台"
                )
        except Exception as e:
            st.error(f"获取平台列表失败: {e}")
            return
    
    with col2:
        days = st.selectbox(
            "时间范围",
            [1, 3, 7, 15, 30],
            index=2,
            help="选择要查看的天数"
        )
    
    # 查询数据
    if st.button("🔍 查询数据"):
        try:
            with get_db_session() as session:
                platform_dao = PlatformPriceDAO(session)
                
                if selected_platform == "全部":
                    # 显示所有平台的统计
                    st.subheader("📊 所有平台统计")
                    
                    stats_data = []
                    for platform in platforms:
                        stats = platform_dao.get_platform_statistics(platform, days)
                        stats_data.append(stats)
                    
                    if stats_data:
                        df = pd.DataFrame(stats_data)
                        df['平均价格'] = df['avg_price'].apply(lambda x: format_price(x))
                        df['最低价格'] = df['min_price'].apply(lambda x: format_price(x))
                        df['最高价格'] = df['max_price'].apply(lambda x: format_price(x))
                        df['在售总数'] = df['total_sell_count'].apply(lambda x: format_number(x))
                        df['求购总数'] = df['total_bidding_count'].apply(lambda x: format_number(x))
                        
                        display_df = df[['platform', '平均价格', '最低价格', '最高价格', '在售总数', '求购总数']]
                        display_df.columns = ['平台', '平均价格', '最低价格', '最高价格', '在售总数', '求购总数']
                        
                        st.dataframe(display_df, use_container_width=True, hide_index=True)
                    else:
                        st.info("暂无数据")
                else:
                    # 显示指定平台的详细统计
                    st.subheader(f"📊 {selected_platform} 平台统计")
                    
                    stats = platform_dao.get_platform_statistics(selected_platform, days)
                    
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("饰品数量", stats['total_items'])
                    with col2:
                        st.metric("平均价格", format_price(stats['avg_price']))
                    with col3:
                        st.metric("价格区间", f"{format_price(stats['min_price'])} - {format_price(stats['max_price'])}")
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("在售总数", format_number(stats['total_sell_count']))
                    with col2:
                        st.metric("求购总数", format_number(stats['total_bidding_count']))
                        
        except Exception as e:
            st.error(f"查询数据失败: {e}")


def show_system_settings(price_service: PriceUpdateService):
    """显示系统设置"""
    st.subheader("⚙️ 系统设置")
    
    # API连接测试
    st.subheader("🔗 API连接测试")
    if st.button("测试API连接"):
        with st.spinner("测试API连接..."):
            try:
                is_connected = asyncio.run(price_service.test_api_connection())
                if is_connected:
                    st.success("✅ API连接正常")
                else:
                    st.error("❌ API连接失败")
            except Exception as e:
                st.error(f"❌ 连接测试失败: {e}")
    
    # 数据清理
    st.subheader("🗑️ 数据清理")
    
    col1, col2 = st.columns(2)
    
    with col1:
        cleanup_days = st.number_input(
            "保留天数",
            min_value=7,
            max_value=365,
            value=90,
            help="删除指定天数之前的历史数据"
        )
        
        if st.button("清理历史数据", type="secondary"):
            with st.spinner("清理历史数据..."):
                try:
                    deleted_count = price_service.cleanup_old_data(cleanup_days)
                    st.success(f"✅ 清理完成，删除了 {deleted_count} 条记录")
                except Exception as e:
                    st.error(f"❌ 清理失败: {e}")
    
    with col2:
        if st.button("清理过期价格", type="secondary"):
            with st.spinner("清理过期价格..."):
                try:
                    with get_db_session() as session:
                        platform_dao = PlatformPriceDAO(session)
                        deactivated_count = platform_dao.deactivate_old_prices(hours=24)
                        st.success(f"✅ 清理完成，标记了 {deactivated_count} 条过期记录")
                except Exception as e:
                    st.error(f"❌ 清理失败: {e}")
    
    # 系统信息
    st.subheader("ℹ️ 系统信息")
    
    try:
        with get_db_session() as session:
            platform_dao = PlatformPriceDAO(session)
            
            # 统计信息
            from sqlalchemy import func
            total_records = session.query(func.count(PlatformPrice.id)).scalar()
            active_records = session.query(func.count(PlatformPrice.id)).filter(
                PlatformPrice.is_active == True
            ).scalar()
            
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("总记录数", format_number(total_records))
            with col2:
                st.metric("有效记录数", format_number(active_records))
            with col3:
                inactive_records = total_records - active_records
                st.metric("无效记录数", format_number(inactive_records))
                
    except Exception as e:
        st.error(f"获取系统信息失败: {e}")
