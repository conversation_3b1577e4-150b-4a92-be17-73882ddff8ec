# Steam饰品数据字段属性对照表

## 基础信息字段（已调整到前面）
| 中文字段名 | 英文字段名 | 数据类型 | 说明 |
|-----------|-----------|---------|------|
| 页面 | page | 整数 | 数据来源页面编号 |
| 饰品 | marketName | 字符串 | 饰品市场显示名称(包含磨损等级) |
| HashName | marketHashName | 字符串 | Steam市场哈希名称(英文) |
| 武器类型 | goodsType01Name | 字符串 | 武器分类(如步枪、手枪等) |
| 品质等级 | goodsLevelName | 字符串 | 饰品品质等级(如军规级、受限等) |
| 品质类型 | goodsQualityName | 字符串 | 饰品品质类型(如纪念品、StatTrak等) |
| Steam在售量 | steamSellCount | 整数 | Steam平台在售数量 |
| Steam今日销量 | steam24SellCount | 整数 | Steam平台24小时销量 |

## Steam平台数据字段
| 中文字段名 | 英文字段名 | 数据类型 | 说明 |
|-----------|-----------|---------|------|
| Steam在售价(美元) | steamPrice | 浮点数 | Steam平台当前在售价格(美元) |
| Steam求购价(美元) | steamBuyPrice | 浮点数 | Steam平台当前求购价格(美元) |
| Steam求购数 | steamBuyCount | 整数 | Steam平台求购订单数量 |
| Steam前5档求购数 | steamBuyCount5 | 整数 | Steam平台前5个价格档位的求购总数量 |
| Steam求购价格列表 | steamBuyPriceList | 字符串 | Steam平台求购价格梯度信息，JSON格式[[价格,数量],...] |

## 悠品平台数据字段
| 中文字段名 | 英文字段名 | 数据类型 | 说明 |
|-----------|-----------|---------|------|
| 悠品在售价 | youpinPrice | 浮点数 | 悠品平台当前在售价格(人民币) |
| 悠品求购价 | youpinPurchasePrice | 浮点数 | 悠品平台当前求购价格(人民币) |
| 悠品求购数 | youpinPurchaseCount | 整数 | 悠品平台求购订单数量 |
| 悠品在售数 | youpinSellCount | 整数 | 悠品平台在售数量 |

## 利润分析字段
| 中文字段名 | 英文字段名 | 数据类型 | 说明 |
|-----------|-----------|---------|------|
| 利润 | youpinProfit | 浮点数 | 预计利润金额(人民币) |
| 利润率 | youpinProfitRate | 浮点数 | 预计利润率(小数形式，如0.3表示30%) |



## 更新时间字段
| 中文字段名 | 英文字段名 | 数据类型 | 说明 |
|-----------|-----------|---------|------|
| Steam更新时间 | steamLastUpdateTimeDesc | 字符串 | Steam数据最后更新时间描述 |
| 悠品更新时间 | youpinLastUpdateTimeDesc | 字符串 | 悠品数据最后更新时间描述 |

## 原始JSON字段完整对照

### 基础饰品信息
- `id`: 饰品唯一标识符
- `goodsName`: 饰品中文名称
- `marketName`: 市场显示名称
- `goodsType01`: 武器类型编码
- `goodsType02`: 武器子类型编码
- `goodsLevel`: 品质等级编码
- `goodsQuality`: 品质类型编码
- `goodsWear`: 磨损程度编码
- `goodsPicture`: 饰品图片URL
- `marketHashName`: Steam市场哈希名称

### Steam平台完整字段
- `steamItemNameId`: Steam物品名称ID
- `steamPrice`: Steam在售价格
- `steamBuyPrice`: Steam求购价格
- `steamBuyCount`: Steam求购数量
- `steamSellCount`: Steam在售数量
- `steam24SellCount`: Steam 24小时销量
- `steamBuyPriceList`: Steam求购价格列表(JSON字符串)
- `steamBuyCount5`: Steam前5档求购数量
- `steamLastUpdateTime`: Steam数据最后更新时间戳
- `steamLastCheckTime`: Steam数据最后检查时间戳
- `steamSellCountLastUpdateTime`: Steam销量最后更新时间戳
- `steamSellCountLastCheckTime`: Steam销量最后检查时间戳
- `steamUpdateInterval`: Steam更新间隔

### Buff平台字段
- `buffItemId`: Buff物品ID
- `buffPrice`: Buff在售价格
- `buffBuyPrice`: Buff求购价格
- `buffBuyCount`: Buff求购数量
- `buffSellCount`: Buff在售数量
- `buffLastUpdateTime`: Buff最后更新时间戳
- `buffLastTransactionUpdateTime`: Buff最后交易更新时间戳
- `buffLastCheckTime`: Buff最后检查时间戳
- `buffUpdateInterval`: Buff更新间隔

### C5平台字段
- `c5ItemId`: C5物品ID
- `c5Price`: C5在售价格
- `c5BuyPrice`: C5求购价格
- `c5BuyCount`: C5求购数量
- `c5ManualPrice`: C5手动定价
- `c5AutoPrice`: C5自动定价
- `c5ManualCount`: C5手动定价数量
- `c5AutoCount`: C5自动定价数量
- `c5TotalCount`: C5总数量
- `c5LastTransactionTime`: C5最后交易时间
- `c5LastTransactionPrice`: C5最后交易价格
- `c5LastUpdateTime`: C5最后更新时间戳
- `c5LastTransactionUpdateTime`: C5最后交易更新时间戳
- `c5LastCheckTime`: C5最后检查时间戳
- `c5TodayTransactionCount`: C5今日交易数量
- `c57dayTransactionCount`: C5七日交易数量
- `c530dayTransactionCount`: C5三十日交易数量
- `c57dayTransactionAveragePrice`: C5七日平均交易价格
- `c524hourTransactionAveragePrice`: C5二十四小时平均交易价格
- `c5UpdateInterval`: C5更新间隔

### IGXE平台字段
- `igxeItemId`: IGXE物品ID
- `igxePrice`: IGXE在售价格
- `igxeBuyPrice`: IGXE求购价格
- `igxeBuyCount`: IGXE求购数量
- `igxeManualPrice`: IGXE手动定价
- `igxeAutoPrice`: IGXE自动定价
- `igxeManualCount`: IGXE手动定价数量
- `igxeAutoCount`: IGXE自动定价数量
- `igxeTotalCount`: IGXE总数量
- `igxeLastUpdateTime`: IGXE最后更新时间戳
- `igxeLastCheckTime`: IGXE最后检查时间戳
- `igxeLastTransactionUpdateTime`: IGXE最后交易更新时间戳
- `igxeTodayTransactionCount`: IGXE今日交易数量
- `igxe7dayTransactionCount`: IGXE七日交易数量
- `igxe30dayTransactionCount`: IGXE三十日交易数量
- `igxeUpdateInterval`: IGXE更新间隔

### 悠品平台字段
- `youpinItemId`: 悠品物品ID
- `youpinPrice`: 悠品在售价格
- `youpinSellCount`: 悠品在售数量
- `youpinPurchasePrice`: 悠品求购价格
- `youpinPurchaseCount`: 悠品求购数量
- `youpinLastUpdateTime`: 悠品最后更新时间戳
- `youpinUpdateInterval`: 悠品更新间隔
- `youpinLastCheckTime`: 悠品最后检查时间戳
- `youpinLastTransactionUpdateTime`: 悠品最后交易更新时间戳

### ECO平台字段
- `ecoItemId`: ECO物品ID
- `ecoPrice`: ECO在售价格
- `ecoSellCount`: ECO在售数量
- `ecoPurchasePrice`: ECO求购价格
- `ecoPurchaseCount`: ECO求购数量
- `ecoLastUpdateTime`: ECO最后更新时间戳
- `ecoUpdateInterval`: ECO更新间隔
- `ecoLastCheckTime`: ECO最后检查时间戳

### 利润计算字段
- `buffProfit`: Buff利润
- `buffProfitRate`: Buff利润率
- `c5Profit`: C5利润
- `c5ProfitRate`: C5利润率
- `c5ProfitAuto`: C5自动定价利润
- `c5ProfitManual`: C5手动定价利润
- `c5ProfitRateAuto`: C5自动定价利润率
- `c5ProfitRateManual`: C5手动定价利润率
- `igxeProfit`: IGXE利润
- `igxeProfitRate`: IGXE利润率
- `igxeProfitAuto`: IGXE自动定价利润
- `igxeProfitManual`: IGXE手动定价利润
- `igxeProfitRateAuto`: IGXE自动定价利润率
- `igxeProfitRateManual`: IGXE手动定价利润率
- `youpinProfit`: 悠品利润
- `youpinProfitRate`: 悠品利润率

### 时间描述字段
- `steamLastUpdateTimeDesc`: Steam最后更新时间描述
- `buffLastUpdateTimeDesc`: Buff最后更新时间描述
- `c5LastUpdateTimeDesc`: C5最后更新时间描述
- `igxeLastUpdateTimeDesc`: IGXE最后更新时间描述
- `youpinLastUpdateTimeDesc`: 悠品最后更新时间描述
- `ecoLastUpdateTimeDesc`: ECO最后更新时间描述

### 其他字段
- `collectId`: 收藏ID
- `buyCartId`: 购物车ID
- `inventoryNum`: 库存编号
- `slowClosingFlag`: 慢速关闭标志
- `fastClosingFlag`: 快速关闭标志
- `takeoverFlag`: 接管标志
- `bloodEarnedFlag`: 血赚标志
- `bloodLossFlag`: 血亏标志
- `hasBuyDesc`: 购买描述
- `isBlack`: 黑名单标志
- `assetsPasterVoList`: 资产贴纸列表
- `allGoodsPrices`: 所有商品价格

## 注意事项
1. 价格字段通常为浮点数，可能为null或0
2. 数量字段通常为整数，可能为null或0
3. 时间戳字段为毫秒级Unix时间戳
4. 时间描述字段为人类可读的相对时间描述
5. 利润率字段为小数形式，需要乘以100转换为百分比
6. 部分字段可能为null，处理时需要进行空值检查
