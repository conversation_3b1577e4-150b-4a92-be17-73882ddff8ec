"""
统一过滤组件测试页面

用于测试unified_item_filter组件的各种功能和配置。
"""

import streamlit as st
from typing import Dict, Any
import time

from src.cs2_investment.app.components.unified_item_filter import render_item_filter


def show_page():
    """显示测试页面"""
    st.title("🧪 统一过滤组件测试")
    
    st.markdown("""
    这个页面用于测试统一过滤组件的各种功能和配置。
    包括预设模式测试、自定义配置测试、回调功能测试等。
    """)
    
    # 创建标签页
    tab1, tab2, tab3, tab4, tab5, tab6, tab7 = st.tabs([
        "预设模式测试",
        "自定义配置测试",
        "回调功能测试",
        "状态隔离测试",
        "性能测试",
        "边界情况测试",
        "使用示例"
    ])
    
    with tab1:
        test_preset_modes()
    
    with tab2:
        test_custom_config()
    
    with tab3:
        test_callback_functionality()
    
    with tab4:
        test_state_isolation()
    
    with tab5:
        test_performance()

    with tab6:
        test_edge_cases()

    with tab7:
        show_usage_examples()


def test_preset_modes():
    """测试预设模式"""
    st.header("📋 预设模式测试")
    
    st.subheader("1. Query模式（完整过滤功能）")
    st.markdown("包含所有7个过滤项的完整查询界面")
    
    def handle_query_callback(query_params: Dict[str, Any]):
        if query_params.get('button_clicked'):
            st.success("Query模式回调触发成功！")
            st.json(query_params)
    
    render_item_filter(
        filter_type='query',
        key_suffix='test_query',
        on_filter_change=handle_query_callback
    )
    



def test_custom_config():
    """测试自定义配置"""
    st.header("⚙️ 自定义配置测试")
    
    st.subheader("1. 自定义过滤项组合")
    st.markdown("测试自定义选择的过滤项组合")
    
    custom_config_1 = {
        'filters': ['name_query', 'item_type', 'price_range'],
        'layout_columns': [2, 1, 1],
        'show_arbitrage_threshold': False,
        'show_arbitrage_info': False,
        'button_text': '🔍 自定义搜索',
        'button_type': 'secondary'
    }
    
    def handle_custom_callback_1(query_params: Dict[str, Any]):
        if query_params.get('button_clicked'):
            st.success("自定义配置1回调触发成功！")
            st.json(query_params)
    
    render_item_filter(
        filter_type='custom',
        config=custom_config_1,
        key_suffix='test_custom_1',
        on_filter_change=handle_custom_callback_1
    )
    
    st.divider()
    
    st.subheader("2. 单列布局测试")
    st.markdown("测试单列垂直布局")
    
    custom_config_2 = {
        'filters': ['name_query', 'quality', 'rarity', 'sort_by'],
        'layout_columns': [1],
        'show_arbitrage_threshold': False,
        'show_arbitrage_info': False,
        'button_text': '🔍 垂直搜索',
        'button_type': 'primary'
    }
    
    def handle_custom_callback_2(query_params: Dict[str, Any]):
        if query_params.get('button_clicked'):
            st.success("自定义配置2回调触发成功！")
            st.json(query_params)
    
    render_item_filter(
        filter_type='custom',
        config=custom_config_2,
        key_suffix='test_custom_2',
        on_filter_change=handle_custom_callback_2
    )


def test_callback_functionality():
    """测试回调功能"""
    st.header("🔄 回调功能测试")
    
    st.subheader("1. 回调参数验证")
    st.markdown("验证回调函数接收到的参数格式和内容")
    
    # 初始化回调历史
    if 'callback_history' not in st.session_state:
        st.session_state.callback_history = []
    
    def detailed_callback(query_params: Dict[str, Any]):
        # 记录所有回调
        timestamp = time.strftime("%H:%M:%S")
        st.session_state.callback_history.append({
            'timestamp': timestamp,
            'params': query_params.copy()
        })
        
        if query_params.get('button_clicked'):
            st.success(f"[{timestamp}] 查询按钮点击回调触发！")
            
            # 详细分析参数
            st.write("**回调参数分析：**")
            for key, value in query_params.items():
                if value is not None and value != "" and value != (0, 100000):
                    st.write(f"- {key}: {value} ({type(value).__name__})")
        else:
            st.info(f"[{timestamp}] 组件状态更新（非按钮点击）")
    
    render_item_filter(
        filter_type='query',
        key_suffix='test_callback',
        on_filter_change=detailed_callback
    )
    
    # 显示回调历史
    if st.session_state.callback_history:
        st.subheader("回调历史记录")
        for i, record in enumerate(reversed(st.session_state.callback_history[-10:])):
            with st.expander(f"[{record['timestamp']}] 回调 #{len(st.session_state.callback_history)-i}"):
                st.json(record['params'])
    
    if st.button("清除回调历史"):
        st.session_state.callback_history = []
        st.rerun()


def test_state_isolation():
    """测试状态隔离"""
    st.header("🔒 状态隔离测试")
    
    st.markdown("""
    测试不同key_suffix的组件是否能正确隔离状态，避免相互影响。
    在两个组件中输入不同的内容，验证它们的状态是否独立。
    """)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("组件A (key_suffix='test_a')")
        
        def callback_a(query_params: Dict[str, Any]):
            if query_params.get('button_clicked'):
                st.success("组件A查询触发！")
                st.json(query_params)
        
        render_item_filter(
            filter_type='query',
            key_suffix='test_a',
            on_filter_change=callback_a
        )
    
    with col2:
        st.subheader("组件B (key_suffix='test_b')")
        
        def callback_b(query_params: Dict[str, Any]):
            if query_params.get('button_clicked'):
                st.success("组件B查询触发！")
                st.json(query_params)
        
        render_item_filter(
            filter_type='query',
            key_suffix='test_b',
            on_filter_change=callback_b
        )
    
    st.info("💡 提示：在两个组件中输入不同的内容，验证它们的状态是否独立。")


def test_performance():
    """测试性能"""
    st.header("⚡ 性能测试")
    
    st.subheader("1. 渲染性能测试")
    st.markdown("测试组件的渲染速度和响应性能")
    
    # 性能计时
    if st.button("开始性能测试"):
        start_time = time.time()
        
        # 渲染多个组件
        for i in range(3):
            st.write(f"**测试组件 {i+1}**")
            
            def perf_callback(query_params: Dict[str, Any]):
                if query_params.get('button_clicked'):
                    st.write(f"组件{i+1}查询完成")
            
            render_item_filter(
                filter_type='query',
                key_suffix=f'perf_test_{i}',
                on_filter_change=perf_callback
            )
        
        end_time = time.time()
        render_time = end_time - start_time
        
        st.success(f"✅ 渲染完成！用时: {render_time:.3f}秒")
        
        # 性能评估
        if render_time < 0.5:
            st.success("🚀 性能优秀")
        elif render_time < 1.0:
            st.warning("⚠️ 性能良好")
        else:
            st.error("🐌 性能需要优化")
    
    st.subheader("2. 内存使用测试")
    st.markdown("检查组件的内存使用情况")
    
    if st.button("检查Session State"):
        filter_keys = [key for key in st.session_state.keys() if 'filter' in key.lower()]
        st.write(f"过滤相关的session state键数量: {len(filter_keys)}")
        
        if filter_keys:
            st.write("**过滤相关的键：**")
            for key in sorted(filter_keys):
                st.write(f"- {key}")


def test_edge_cases():
    """测试边界情况"""
    st.header("🔍 边界情况测试")

    st.subheader("1. 无效配置测试")
    st.markdown("测试组件对无效配置的处理能力")

    # 测试空配置
    st.write("**空配置测试：**")
    try:
        def empty_callback(query_params: Dict[str, Any]):
            if query_params.get('button_clicked'):
                st.success("空配置测试成功！")
                st.json(query_params)

        render_item_filter(
            filter_type='custom',
            config={},
            key_suffix='test_empty',
            on_filter_change=empty_callback
        )
        st.success("✅ 空配置处理正常")
    except Exception as e:
        st.error(f"❌ 空配置处理失败: {e}")

    st.divider()

    # 测试无效过滤项
    st.write("**无效过滤项测试：**")
    try:
        invalid_config = {
            'filters': ['invalid_filter', 'name_query'],
            'layout_columns': [1, 1],
            'button_text': '🔍 测试无效过滤项'
        }

        def invalid_callback(query_params: Dict[str, Any]):
            if query_params.get('button_clicked'):
                st.success("无效过滤项测试成功！")
                st.json(query_params)

        render_item_filter(
            filter_type='custom',
            config=invalid_config,
            key_suffix='test_invalid',
            on_filter_change=invalid_callback
        )
        st.success("✅ 无效过滤项处理正常")
    except Exception as e:
        st.error(f"❌ 无效过滤项处理失败: {e}")

    st.subheader("2. 极端值测试")
    st.markdown("测试极端参数值的处理")

    # 测试极大布局列数
    extreme_config = {
        'filters': ['name_query'],
        'layout_columns': [1] * 10,  # 10列布局
        'button_text': '🔍 极端布局测试'
    }

    def extreme_callback(query_params: Dict[str, Any]):
        if query_params.get('button_clicked'):
            st.success("极端布局测试成功！")
            st.json(query_params)

    try:
        render_item_filter(
            filter_type='custom',
            config=extreme_config,
            key_suffix='test_extreme',
            on_filter_change=extreme_callback
        )
        st.success("✅ 极端布局处理正常")
    except Exception as e:
        st.error(f"❌ 极端布局处理失败: {e}")

    st.subheader("3. 无回调函数测试")
    st.markdown("测试不提供回调函数时的行为")

    try:
        render_item_filter(
            filter_type='favorite',
            key_suffix='test_no_callback'
            # 不提供on_filter_change参数
        )
        st.success("✅ 无回调函数处理正常")
    except Exception as e:
        st.error(f"❌ 无回调函数处理失败: {e}")


def show_usage_examples():
    """显示使用示例"""
    st.header("📚 使用示例")

    st.subheader("1. 查询页面集成示例")
    st.markdown("展示如何在查询页面中集成统一过滤组件")

    with st.expander("查看查询页面代码示例", expanded=False):
        st.code("""
# 查询页面集成示例
def handle_query_callback(query_params: Dict[str, Any]):
    \"\"\"处理查询回调\"\"\"
    if query_params.get('button_clicked'):
        # 保存查询参数到session_state
        st.session_state.query_params = query_params
        st.session_state.query_executed = True
        st.session_state.current_page = 1  # 重置到第一页

def show_query_filters():
    \"\"\"显示查询过滤器\"\"\"
    render_item_filter(
        filter_type='query',
        key_suffix='query_page',
        on_filter_change=handle_query_callback
    )

def execute_query(params: Dict[str, Any]) -> List[Dict]:
    \"\"\"执行查询\"\"\"
    try:
        item_service = st.session_state.item_service_query_page
        return item_service.search_items_with_prices(**params)
    except Exception as e:
        st.error(f"查询失败: {e}")
        return []
        """, language='python')

    st.subheader("2. 收藏页面集成示例")
    st.markdown("展示如何在收藏页面中集成统一过滤组件")

    with st.expander("查看收藏页面代码示例", expanded=False):
        st.code("""
# 收藏页面集成示例
def handle_search_callback(query_params: Dict[str, Any]):
    \"\"\"处理搜索回调\"\"\"
    if query_params.get('button_clicked'):
        name_query = query_params.get('name_query')
        if name_query and name_query.strip():
            st.session_state.favorite_search_executed = True
            st.session_state.favorite_search_query = name_query.strip()
            st.session_state.favorite_page = 1  # 重置到第一页
        else:
            st.session_state.favorite_search_executed = False
            st.session_state.favorite_search_query = ""

def show_search_filters():
    \"\"\"显示搜索过滤器\"\"\"
    render_item_filter(
        filter_type='favorite',
        key_suffix='favorite_page',
        on_filter_change=handle_search_callback
    )
        """, language='python')

    st.subheader("3. 自定义配置示例")
    st.markdown("展示如何创建自定义的过滤配置")

    with st.expander("查看自定义配置代码示例", expanded=False):
        st.code("""
# 自定义配置示例
def create_custom_filter():
    \"\"\"创建自定义过滤器\"\"\"
    custom_config = {
        'filters': ['name_query', 'item_type', 'price_range'],
        'layout_columns': [2, 1, 1],
        'show_arbitrage_threshold': False,
        'show_arbitrage_info': False,
        'button_text': '🔍 自定义搜索',
        'button_type': 'secondary',
        'price_range_min': 0,
        'price_range_max': 50000
    }

    def handle_custom_callback(query_params: Dict[str, Any]):
        if query_params.get('button_clicked'):
            # 处理自定义查询逻辑
            process_custom_search(query_params)

    render_item_filter(
        filter_type='custom',
        config=custom_config,
        key_suffix='custom_page',
        on_filter_change=handle_custom_callback
    )
        """, language='python')

    st.subheader("4. 最佳实践示例")
    st.markdown("展示使用组件的最佳实践")

    with st.expander("查看最佳实践代码示例", expanded=False):
        st.code("""
# 最佳实践示例
def setup_services():
    \"\"\"初始化服务（在页面加载时调用）\"\"\"
    if 'item_service' not in st.session_state:
        st.session_state.item_service = ItemService()
    if 'favorite_service' not in st.session_state:
        st.session_state.favorite_service = FavoriteService()

def handle_filter_with_validation(query_params: Dict[str, Any]):
    \"\"\"带验证的过滤处理\"\"\"
    if not query_params.get('button_clicked'):
        return  # 只处理按钮点击事件

    # 参数验证
    name_query = query_params.get('name_query', '').strip()
    if not name_query and not any(query_params.get(key) for key in
                                 ['item_type', 'quality', 'rarity']):
        st.warning("请至少输入一个搜索条件")
        return

    # 执行查询
    try:
        with st.spinner("正在搜索..."):
            results = execute_search(query_params)
            display_results(results)
    except Exception as e:
        st.error(f"搜索失败: {e}")

def show_filter_with_best_practices():
    \"\"\"展示最佳实践的过滤器\"\"\"
    setup_services()  # 确保服务已初始化

    render_item_filter(
        filter_type='query',
        key_suffix='best_practice_page',
        on_filter_change=handle_filter_with_validation
    )
        """, language='python')

    st.info("💡 **提示**：这些示例展示了如何在实际项目中正确使用统一过滤组件。")


if __name__ == "__main__":
    show_page()
