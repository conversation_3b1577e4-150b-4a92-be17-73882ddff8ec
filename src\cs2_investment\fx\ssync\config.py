#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品实时分析系统 - 配置文件
系统参数和设置的集中管理
"""

# 系统版本信息
VERSION = "2.0"
RELEASE_DATE = "2025-07-25"
AUTHOR = "CS2饰品分析团队"

# 技术指标参数
TECHNICAL_INDICATORS = {
    # RSI参数
    'RSI': {
        'period': 14,           # RSI计算周期
        'overbought': 70,       # 超买线
        'oversold': 30,         # 超卖线
        'extreme_overbought': 80,  # 极度超买
        'extreme_oversold': 20     # 极度超卖
    },
    
    # MACD参数
    'MACD': {
        'fast_period': 12,      # 快线周期
        'slow_period': 26,      # 慢线周期
        'signal_period': 9      # 信号线周期
    },
    
    # 移动平均线参数
    'MA': {
        'short_period': 20,     # 短期均线
        'long_period': 50       # 长期均线
    }
}

# 交易信号参数
TRADING_SIGNALS = {
    # 置信度阈值
    'confidence_thresholds': {
        'high': 80,             # 高置信度
        'medium': 60,           # 中等置信度
        'low': 40               # 低置信度
    },
    
    # 信号有效期（小时）
    'signal_validity': 2,
    
    # 组合策略权重
    'combo_weights': {
        'rsi': 0.4,            # RSI权重
        'macd': 0.4,           # MACD权重
        'volume': 0.2          # 成交量权重
    }
}

# 异常检测参数
ANOMALY_DETECTION = {
    # 价格异常阈值
    'price_anomaly': {
        'large_move_threshold': 0.05,      # 大幅波动阈值（5%）
        'extreme_move_threshold': 0.10,    # 极端波动阈值（10%）
        'volatility_multiplier': 2.0       # 波动率倍数
    },
    
    # 成交量异常阈值
    'volume_anomaly': {
        'surge_threshold': 2.0,            # 激增阈值（2倍）
        'extreme_surge_threshold': 5.0,    # 极端激增阈值（5倍）
        'low_volume_threshold': 0.3,       # 低成交量阈值（0.3倍）
        'analysis_period': 24               # 分析周期（小时）
    },
    
    # 时间异常参数
    'time_anomaly': {
        'night_hours': [0, 1, 2, 3, 4, 5], # 深夜时段
        'large_trade_threshold': 1000       # 大额交易阈值
    }
}

# 风险管理参数
RISK_MANAGEMENT = {
    # 止损参数
    'stop_loss': {
        'default_percentage': 0.05,        # 默认止损比例（5%）
        'atr_multiplier': 2.0,             # ATR倍数
        'max_loss_percentage': 0.10        # 最大损失比例（10%）
    },
    
    # 止盈参数
    'take_profit': {
        'default_ratio': 2.0,              # 默认风险收益比
        'conservative_ratio': 1.5,         # 保守风险收益比
        'aggressive_ratio': 3.0            # 激进风险收益比
    },
    
    # 仓位管理
    'position_sizing': {
        'max_position_percentage': 0.20,   # 最大仓位比例（20%）
        'conservative_percentage': 0.10,   # 保守仓位比例（10%）
        'aggressive_percentage': 0.30      # 激进仓位比例（30%）
    }
}

# 图表生成参数
CHART_SETTINGS = {
    # 图表尺寸
    'figure_size': {
        'width': 16,                       # 图表宽度
        'height': 12                       # 图表高度
    },
    
    # 图表质量
    'dpi': 300,                           # 分辨率
    
    # 颜色配置
    'colors': {
        'up_candle': '#00ff88',           # 阳线颜色
        'down_candle': '#ff4444',         # 阴线颜色
        'rsi_line': '#1f77b4',            # RSI线颜色
        'macd_line': '#1f77b4',           # MACD线颜色
        'signal_line': '#ff7f0e',         # 信号线颜色
        'volume_normal': '#888888',       # 正常成交量颜色
        'volume_anomaly': '#ff0000',      # 异常成交量颜色
        'buy_signal': '#00ff00',          # 买入信号颜色
        'sell_signal': '#ff0000',         # 卖出信号颜色
        'background': '#1e1e1e',          # 背景颜色
        'grid': '#404040'                 # 网格颜色
    },
    
    # 字体设置
    'fonts': {
        'family': ['SimHei', 'Microsoft YaHei'],  # 中文字体
        'title_size': 16,                 # 标题字体大小
        'label_size': 12,                 # 标签字体大小
        'legend_size': 10                 # 图例字体大小
    }
}

# 数据处理参数
DATA_PROCESSING = {
    # 数据清洗
    'data_cleaning': {
        'remove_last_hour': True,         # 是否移除最后一小时数据
        'min_data_points': 50,            # 最少数据点数
        'outlier_threshold': 3.0          # 异常值阈值（标准差倍数）
    },
    
    # 数据验证
    'data_validation': {
        'required_columns': ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'amount'],
        'price_range_check': True,        # 价格范围检查
        'volume_range_check': True        # 成交量范围检查
    }
}

# 系统性能参数
PERFORMANCE = {
    # 计算优化
    'calculation': {
        'use_vectorization': True,        # 使用向量化计算
        'cache_results': True,            # 缓存计算结果
        'parallel_processing': False      # 并行处理（暂未实现）
    },
    
    # 内存管理
    'memory': {
        'max_data_points': 10000,         # 最大数据点数
        'garbage_collection': True        # 垃圾回收
    }
}

# 日志配置
LOGGING = {
    'level': 'INFO',                      # 日志级别
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_enabled': False,                # 是否启用文件日志
    'file_path': 'ssync.log'             # 日志文件路径
}

# 文件路径配置
FILE_PATHS = {
    # 数据文件
    'data_files': {
        'hourly': '时k.json',            # 时K数据文件
        'daily1': '日k1.json',           # 日K数据文件1
        'daily2': '日k2.json',           # 日K数据文件2
        'weekly': '周k.json',            # 周K数据文件
        'trend': '走势.json'             # 走势数据文件
    },
    
    # 输出文件
    'output_files': {
        'chart_suffix': '_技术分析图表.png',      # 图表文件后缀
        'report_suffix': '_分析报告.txt',        # 报告文件后缀
        'log_suffix': '_运行日志.log'            # 日志文件后缀
    }
}

# API配置（预留）
API_CONFIG = {
    'enabled': False,                     # 是否启用API
    'base_url': '',                       # API基础URL
    'timeout': 30,                        # 超时时间（秒）
    'retry_times': 3                      # 重试次数
}

# 用户界面配置
UI_CONFIG = {
    # 控制台输出
    'console': {
        'use_colors': True,               # 使用颜色输出
        'use_emojis': True,               # 使用表情符号
        'line_width': 80,                 # 行宽
        'decimal_places': 2               # 小数位数
    },
    
    # 进度显示
    'progress': {
        'show_progress_bar': True,        # 显示进度条
        'update_interval': 0.1            # 更新间隔（秒）
    }
}

# 安全配置
SECURITY = {
    'data_encryption': False,             # 数据加密（暂未实现）
    'access_control': False,              # 访问控制（暂未实现）
    'audit_log': False                    # 审计日志（暂未实现）
}

# 实验性功能
EXPERIMENTAL = {
    'machine_learning': False,            # 机器学习功能
    'sentiment_analysis': False,          # 情绪分析
    'news_integration': False,            # 新闻集成
    'social_media_monitoring': False      # 社交媒体监控
}

def get_config(section=None, key=None):
    """
    获取配置参数
    
    Args:
        section: 配置节名称
        key: 配置键名称
        
    Returns:
        配置值或配置字典
    """
    config_map = {
        'technical': TECHNICAL_INDICATORS,
        'signals': TRADING_SIGNALS,
        'anomaly': ANOMALY_DETECTION,
        'risk': RISK_MANAGEMENT,
        'chart': CHART_SETTINGS,
        'data': DATA_PROCESSING,
        'performance': PERFORMANCE,
        'logging': LOGGING,
        'files': FILE_PATHS,
        'api': API_CONFIG,
        'ui': UI_CONFIG,
        'security': SECURITY,
        'experimental': EXPERIMENTAL
    }
    
    if section is None:
        return config_map
    
    if section not in config_map:
        raise ValueError(f"Unknown config section: {section}")
    
    section_config = config_map[section]
    
    if key is None:
        return section_config
    
    if key not in section_config:
        raise ValueError(f"Unknown config key: {key} in section: {section}")
    
    return section_config[key]

def update_config(section, key, value):
    """
    更新配置参数
    
    Args:
        section: 配置节名称
        key: 配置键名称
        value: 新的配置值
    """
    config_map = {
        'technical': TECHNICAL_INDICATORS,
        'signals': TRADING_SIGNALS,
        'anomaly': ANOMALY_DETECTION,
        'risk': RISK_MANAGEMENT,
        'chart': CHART_SETTINGS,
        'data': DATA_PROCESSING,
        'performance': PERFORMANCE,
        'logging': LOGGING,
        'files': FILE_PATHS,
        'api': API_CONFIG,
        'ui': UI_CONFIG,
        'security': SECURITY,
        'experimental': EXPERIMENTAL
    }
    
    if section not in config_map:
        raise ValueError(f"Unknown config section: {section}")
    
    if key not in config_map[section]:
        raise ValueError(f"Unknown config key: {key} in section: {section}")
    
    config_map[section][key] = value

if __name__ == "__main__":
    # 配置测试
    print(f"CS2饰品实时分析系统配置 v{VERSION}")
    print(f"发布日期: {RELEASE_DATE}")
    print(f"作者: {AUTHOR}")
    
    # 显示主要配置
    print("\n主要配置参数:")
    print(f"RSI周期: {get_config('technical', 'RSI')['period']}")
    print(f"MACD参数: {get_config('technical', 'MACD')}")
    print(f"图表尺寸: {get_config('chart', 'figure_size')}")
    print(f"数据处理: 移除最后一小时 = {get_config('data', 'data_cleaning')['remove_last_hour']}")
