#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品分析系统 - 技术分析报告生成器
面向专业用户，提供详细的技术指标分析和客观的市场数据报告
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class TechnicalAnalysisReportGenerator:
    """技术分析报告生成器"""
    
    def __init__(self, item_name: str, analysis_results: Dict, analysis_system=None):
        """
        初始化技术分析报告生成器

        Args:
            item_name: 饰品名称
            analysis_results: 完整的分析结果字典
            analysis_system: 可选的分析系统实例，用于访问数据管理器
        """
        self.item_name = item_name
        self.analysis_results = analysis_results
        self.analysis_system = analysis_system  # 用于访问数据管理器
        self.chart_system = None  # 延迟初始化专业图表系统
        self.report_data = {}
        
    def generate_technical_report(self, save_path: Optional[str] = None) -> str:
        """
        生成技术分析报告
        
        Args:
            save_path: 可选的报告保存路径
            
        Returns:
            str: Markdown格式的技术分析报告
        """
        try:
            print("📊 开始生成技术分析报告...")
            
            # 1. 提取饰品基础数据
            basic_data = self._extract_basic_data()
            
            # 2. 分析技术指标
            technical_data = self._analyze_technical_indicators()
            
            # 3. 评估交易信号
            evaluation_data = self._evaluate_trading_signals()
            
            # 4. 风险评估
            risk_data = self._assess_risk_factors()
            
            # 5. 生成技术面总结
            summary_data = self._generate_technical_summary()
            
            # 6. 格式化为Markdown报告
            report_content = self._format_markdown_report(
                basic_data, technical_data, evaluation_data, risk_data, summary_data
            )
            
            # 7. 保存报告（如果指定路径）
            if save_path:
                with open(save_path, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                print(f"✅ 技术分析报告已保存至: {save_path}")
            
            print("✅ 技术分析报告生成完成")
            return report_content

        except Exception as e:
            error_msg = f"❌ 技术分析报告生成失败: {str(e)}"
            print(error_msg)
            return self._generate_error_report(error_msg)

    def get_technical_indicators_data(self) -> Dict:
        """
        获取技术指标数据（用于数据库保存）

        Returns:
            Dict: 包含所有技术指标的数据
        """
        try:
            # 分析技术指标
            technical_data = self._analyze_technical_indicators()

            return {
                'technical_indicators': technical_data,
                'basic_data': self._extract_basic_data(),
                'evaluation_data': self._evaluate_trading_signals(),
                'risk_data': self._assess_risk_factors()
            }

        except Exception as e:
            print(f"⚠️ 获取技术指标数据失败: {e}")
            return {
                'technical_indicators': self._get_default_technical_indicators(),
                'basic_data': self._get_default_basic_data(),
                'evaluation_data': {},
                'risk_data': {}
            }
    
    def _extract_basic_data(self) -> Dict:
        """
        提取饰品基础数据

        Returns:
            Dict: 包含价格、成交量、价格区间等基础数据
        """
        try:
            # 获取当前价格
            current_price = self._get_current_price()

            # 获取价格变化数据
            price_changes = self._calculate_price_changes()

            # 获取成交量数据
            volume_data = self._get_volume_data()

            # 获取价格区间
            price_range = self._get_price_range()

            # 获取支撑阻力位
            support_resistance = self._get_support_resistance()

            return {
                'current_price': current_price,
                'price_changes': price_changes,
                'volume_data': volume_data,
                'price_range': price_range,
                'support_resistance': support_resistance,
                'last_trade_time': self._get_last_trade_time()
            }

        except Exception as e:
            print(f"⚠️ 基础数据提取错误: {e}")
            return self._get_default_basic_data()
    
    def _analyze_technical_indicators(self) -> Dict:
        """
        分析九大技术指标

        Returns:
            Dict: 包含所有技术指标的数值和状态
        """
        try:
            # 从现有分析结果获取技术指标
            indicators = {}

            # 获取已计算的技术指标
            tech_indicators = self.analysis_results.get('technical_indicators', {})
            current_signals = self.analysis_results.get('current_signals', {})

            # 1. 价格与移动平均线
            indicators['price_ma'] = self._analyze_price_ma()

            # 2. MACD指标
            indicators['macd'] = self._analyze_macd(tech_indicators, current_signals)

            # 3. RSI指标
            indicators['rsi'] = self._analyze_rsi(tech_indicators, current_signals)

            # 4. KDJ指标（需要从图表系统获取）
            indicators['kdj'] = self._analyze_kdj()

            # 5. 成交量分析
            indicators['volume'] = self._analyze_volume()

            # 6. OBV指标（需要从图表系统获取）
            indicators['obv'] = self._analyze_obv()

            # 7. 资金流向
            indicators['money_flow'] = self._analyze_money_flow()

            # 8. 布林带指标
            indicators['bollinger'] = self._analyze_bollinger(tech_indicators, current_signals)

            # 9. ATR指标
            indicators['atr'] = self._analyze_atr()

            return indicators

        except Exception as e:
            print(f"⚠️ 技术指标分析错误: {e}")
            return self._get_default_technical_indicators()
    
    def _evaluate_trading_signals(self) -> Dict:
        """
        评估交易信号（三级决策框架）

        Returns:
            Dict: 包含强买入、强卖出、观望条件的评估结果
        """
        try:
            # 获取技术指标数据
            indicators = self._analyze_technical_indicators()

            # 强买入信号检查（需满足5-6个，共9个条件）
            buy_signals = self._evaluate_strong_buy_signals(indicators)

            # 强卖出信号检查（出现4-5个，共8个条件）
            sell_signals = self._evaluate_strong_sell_signals(indicators)

            # 观望条件检查（共5个条件）
            watch_signals = self._evaluate_watch_signals(indicators)

            # 综合评估
            buy_count = sum(1 for signal in buy_signals.values() if signal['status'])
            sell_count = sum(1 for signal in sell_signals.values() if signal['status'])
            watch_count = sum(1 for signal in watch_signals.values() if signal['status'])

            # 决策判断
            if buy_count >= 5 and sell_count <= 2:
                main_signal = '强买入'
                signal_strength = '强'
            elif sell_count >= 4 and buy_count <= 2:
                main_signal = '强卖出'
                signal_strength = '强'
            elif watch_count >= 3 or (buy_count >= 3 and sell_count >= 3):
                main_signal = '观望'
                signal_strength = '中性'
            elif buy_count >= 3:
                main_signal = '偏多'
                signal_strength = '弱'
            elif sell_count >= 3:
                main_signal = '偏空'
                signal_strength = '弱'
            else:
                main_signal = '观望'
                signal_strength = '中性'

            return {
                'strong_buy_signals': buy_signals,
                'strong_sell_signals': sell_signals,
                'watch_signals': watch_signals,
                'buy_count': buy_count,
                'sell_count': sell_count,
                'watch_count': watch_count,
                'main_signal': main_signal,
                'signal_strength': signal_strength,
                'key_confirmation': self._get_key_confirmation_indicators(indicators)
            }

        except Exception as e:
            print(f"⚠️ 交易信号评估错误: {e}")
            return self._get_default_evaluation_result()
    
    def _assess_risk_factors(self) -> Dict:
        """
        评估风险因素

        Returns:
            Dict: 包含ATR风险评估和止损建议
        """
        try:
            # 获取ATR数据
            atr_data = self._get_atr_data()
            current_price = self._get_current_price()

            # 波动率分析
            volatility_analysis = self._analyze_volatility_risk(atr_data)

            # 止损计算
            stop_loss_levels = self._calculate_stop_loss_levels(atr_data, current_price)

            # 关键价位识别
            key_levels = self._identify_key_price_levels()

            return {
                'volatility_analysis': volatility_analysis,
                'stop_loss_levels': stop_loss_levels,
                'key_levels': key_levels,
                'risk_summary': self._generate_risk_summary(volatility_analysis, stop_loss_levels)
            }

        except Exception as e:
            print(f"⚠️ 风险评估错误: {e}")
            return self._get_default_risk_assessment()
    
    def _generate_technical_summary(self) -> Dict:
        """
        生成技术面总结
        
        Returns:
            Dict: 包含市场状态、关键观察点、风险提示
        """
        # 占位方法，将在后续任务中实现
        return {}
    
    def _format_markdown_report(self, basic_data: Dict, technical_data: Dict,
                               evaluation_data: Dict, risk_data: Dict,
                               summary_data: Dict) -> str:
        """
        格式化为Markdown报告

        Args:
            basic_data: 基础数据
            technical_data: 技术指标数据
            evaluation_data: 评估数据
            risk_data: 风险数据
            summary_data: 总结数据

        Returns:
            str: Markdown格式的报告
        """
        try:
            # 生成报告头部
            header_section = self._format_header_section(basic_data)

            # 生成五大部分
            basic_section = self._format_basic_data_section(basic_data)
            technical_section = self._format_technical_indicators_section(technical_data)
            evaluation_section = self._format_evaluation_section(evaluation_data)
            risk_section = self._format_risk_assessment_section(risk_data)
            summary_section = self._format_technical_summary_section(summary_data)

            # 组合完整报告
            report = f"""{header_section}

---

## 📈 **一、饰品基础数据**

{basic_section}

---

## 🔍 **二、技术指标分析**

{technical_section}

---

## ⚖️ **三、综合技术评估**

{evaluation_section}

---

## ⚠️ **四、风险评估**

{risk_section}

---

## 📋 **五、技术面总结**

{summary_section}

---

**免责声明：** 本报告仅基于技术分析提供市场数据和标准参考，不构成任何投资建议。投资决策应基于个人风险承受能力和投资目标，请谨慎决策。
"""

            return report

        except Exception as e:
            print(f"⚠️ Markdown报告格式化错误: {e}")
            return self._generate_error_report(f"报告格式化失败: {str(e)}")
    
    def _generate_error_report(self, error_msg: str) -> str:
        """
        生成错误报告
        
        Args:
            error_msg: 错误信息
            
        Returns:
            str: 错误报告内容
        """
        return f"""# {self.item_name} - 技术分析报告

**报告生成时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## ❌ 报告生成失败

{error_msg}

请检查数据完整性后重新生成报告。

---
**免责声明:** 本报告生成过程中遇到技术问题，请联系技术支持。
"""
    
    # 辅助方法 - 基础数据获取
    def _get_current_price(self) -> float:
        """获取当前价格"""
        try:
            return self.analysis_results['current_signals']['price']
        except (KeyError, TypeError):
            # 尝试从支撑阻力位获取
            try:
                return self.analysis_results['support_resistance']['current_price']
            except (KeyError, TypeError):
                print("⚠️ 无法获取当前价格，使用默认值")
                return 0.0

    def _calculate_price_changes(self) -> Dict:
        """计算价格变化"""
        try:
            # 从数据管理器获取日K数据来计算价格变化
            if hasattr(self, 'analysis_system') and hasattr(self.analysis_system, 'data_manager'):
                daily_data = self.analysis_system.data_manager.get_daily_data()
                current_price = daily_data['close'].iloc[-1]

                # 计算不同周期的价格变化
                changes = {}

                # 24小时变化（1天前）
                if len(daily_data) >= 2:
                    prev_price_24h = daily_data['close'].iloc[-2]
                    changes['24h'] = ((current_price - prev_price_24h) / prev_price_24h * 100)
                else:
                    changes['24h'] = 0.0

                # 7日变化
                if len(daily_data) >= 8:
                    prev_price_7d = daily_data['close'].iloc[-8]
                    changes['7d'] = ((current_price - prev_price_7d) / prev_price_7d * 100)
                else:
                    changes['7d'] = 0.0

                # 30日变化
                if len(daily_data) >= 31:
                    prev_price_30d = daily_data['close'].iloc[-31]
                    changes['30d'] = ((current_price - prev_price_30d) / prev_price_30d * 100)
                else:
                    changes['30d'] = 0.0

                return changes
            else:
                return {'24h': 0.0, '7d': 0.0, '30d': 0.0}

        except Exception as e:
            print(f"⚠️ 价格变化计算错误: {e}")
            return {'24h': 0.0, '7d': 0.0, '30d': 0.0}

    def _get_volume_data(self) -> Dict:
        """获取成交量数据"""
        try:
            # 从市场特征获取成交量数据
            market_chars = self.analysis_results.get('market_characteristics', {})

            current_volume = market_chars.get('avg_daily_volume', 0)

            # 注：基本面快照数据可用于未来扩展

            # 计算成交量比率（当前相对于平均）
            volume_ratio = 1.0
            if hasattr(self, 'analysis_system') and hasattr(self.analysis_system, 'data_manager'):
                try:
                    daily_data = self.analysis_system.data_manager.get_daily_data()
                    if len(daily_data) >= 20:
                        recent_volume = daily_data['volume'].iloc[-1]
                        avg_volume = daily_data['volume'].tail(20).mean()
                        volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1.0
                        current_volume = recent_volume
                except:
                    pass

            # 安全处理NaN值
            safe_current_volume = 0 if pd.isna(current_volume) else int(current_volume)
            safe_avg_volume = market_chars.get('avg_daily_volume', current_volume)
            safe_avg_volume = 0 if pd.isna(safe_avg_volume) else int(safe_avg_volume)

            return {
                'current': safe_current_volume,
                'average': safe_avg_volume,
                'ratio': round(volume_ratio, 2)
            }

        except Exception as e:
            print(f"⚠️ 成交量数据获取错误: {e}")
            return {'current': 0, 'average': 0, 'ratio': 1.0}

    def _get_price_range(self) -> Dict:
        """获取价格区间（52周最高最低）"""
        try:
            # 从支撑阻力位数据获取长期高低点
            support_resistance = self.analysis_results.get('support_resistance', {})

            # 使用长期高低点作为52周数据的近似
            high_52w = support_resistance.get('long_term_resistance', 0.0)
            low_52w = support_resistance.get('long_term_support', 0.0)

            # 如果没有长期数据，使用近期数据
            if high_52w == 0.0:
                high_52w = support_resistance.get('recent_resistance', 0.0)
            if low_52w == 0.0:
                low_52w = support_resistance.get('recent_support', 0.0)

            return {
                'high_52w': high_52w,
                'low_52w': low_52w
            }

        except Exception as e:
            print(f"⚠️ 价格区间获取错误: {e}")
            return {'high_52w': 0.0, 'low_52w': 0.0}

    def _get_support_resistance(self) -> Dict:
        """获取支撑阻力位"""
        try:
            support_resistance = self.analysis_results.get('support_resistance', {})

            return {
                'support': support_resistance.get('recent_support', 0.0),
                'resistance': support_resistance.get('recent_resistance', 0.0),
                'support_distance': support_resistance.get('support_distance', 0.0),
                'resistance_distance': support_resistance.get('resistance_distance', 0.0)
            }

        except Exception as e:
            print(f"⚠️ 支撑阻力位获取错误: {e}")
            return {'support': 0.0, 'resistance': 0.0, 'support_distance': 0.0, 'resistance_distance': 0.0}

    def _get_last_trade_time(self) -> str:
        """获取最近交易时间"""
        try:
            # 尝试从数据中获取最新时间
            if hasattr(self, 'analysis_system') and hasattr(self.analysis_system, 'data_manager'):
                daily_data = self.analysis_system.data_manager.get_daily_data()
                if not daily_data.empty:
                    last_time = daily_data['datetime'].iloc[-1]
                    # 计算距离现在的时间
                    from datetime import datetime
                    now = datetime.now()
                    if hasattr(last_time, 'to_pydatetime'):
                        last_time = last_time.to_pydatetime()

                    time_diff = now - last_time

                    if time_diff.days > 0:
                        return f"{time_diff.days}天前"
                    elif time_diff.seconds > 3600:
                        hours = time_diff.seconds // 3600
                        return f"{hours}小时前"
                    elif time_diff.seconds > 60:
                        minutes = time_diff.seconds // 60
                        return f"{minutes}分钟前"
                    else:
                        return "刚刚"

            return "未知"

        except Exception as e:
            print(f"⚠️ 最近交易时间获取错误: {e}")
            return "数据获取失败"
    
    def _get_default_basic_data(self) -> Dict:
        """获取默认基础数据"""
        return {
            'current_price': 0.0,
            'price_changes': {'24h': 0.0, '7d': 0.0, '30d': 0.0},
            'volume_data': {'current': 0, 'average': 0, 'ratio': 1.0},
            'price_range': {'high_52w': 0.0, 'low_52w': 0.0},
            'support_resistance': {'support': 0.0, 'resistance': 0.0},
            'last_trade_time': "数据获取失败"
        }
    
    def _get_default_technical_indicators(self) -> Dict:
        """获取默认技术指标"""
        return {
            'price_ma': {},
            'macd': {},
            'rsi': {},
            'kdj': {},
            'volume': {},
            'obv': {},
            'money_flow': {},
            'bollinger': {},
            'atr': {}
        }
    
    # 技术指标分析方法
    def _analyze_price_ma(self) -> Dict:
        """分析价格与移动平均线"""
        try:
            tech_indicators = self.analysis_results.get('technical_indicators', {})
            current_signals = self.analysis_results.get('current_signals', {})

            current_price = current_signals.get('price', 0.0)
            ema_12 = tech_indicators.get('ema_12')
            ema_26 = tech_indicators.get('ema_26')
            sma_20 = tech_indicators.get('sma_20')

            # 计算价格相对于均线的位置
            position_info = {}
            if ema_12 is not None and not ema_12.empty:
                current_ema_12 = ema_12.iloc[-1]
                position_info['ema_12_distance'] = ((current_price - current_ema_12) / current_ema_12 * 100)

            if ema_26 is not None and not ema_26.empty:
                current_ema_26 = ema_26.iloc[-1]
                position_info['ema_26_distance'] = ((current_price - current_ema_26) / current_ema_26 * 100)

            if sma_20 is not None and not sma_20.empty:
                current_sma_20 = sma_20.iloc[-1]
                position_info['sma_20_distance'] = ((current_price - current_sma_20) / current_sma_20 * 100)

            # 判断均线排列
            ema_trend = current_signals.get('ema_trend', 'UNKNOWN')
            ma_arrangement = '多头排列' if ema_trend == 'UP' else '空头排列' if ema_trend == 'DOWN' else '震荡'

            # 价格位置判断
            above_all_ma = all([
                position_info.get('ema_12_distance', -1) > 0,
                position_info.get('ema_26_distance', -1) > 0,
                position_info.get('sma_20_distance', -1) > 0
            ])

            price_position = '在所有均线之上' if above_all_ma else '部分均线之下'

            return {
                'current_price': current_price,
                'price_position': price_position,
                'ma_arrangement': ma_arrangement,
                'ema_12_distance': position_info.get('ema_12_distance', 0.0),
                'ema_26_distance': position_info.get('ema_26_distance', 0.0),
                'sma_20_distance': position_info.get('sma_20_distance', 0.0)
            }

        except Exception as e:
            print(f"⚠️ 价格均线分析错误: {e}")
            return {'current_price': 0.0, 'price_position': '未知', 'ma_arrangement': '未知'}

    def _analyze_macd(self, tech_indicators: Dict, current_signals: Dict) -> Dict:
        """分析MACD指标"""
        try:
            macd = tech_indicators.get('macd')
            macd_signal = tech_indicators.get('macd_signal')
            macd_histogram = tech_indicators.get('macd_histogram')

            if macd is None or macd.empty:
                return {'macd_value': 0.0, 'signal_line': 0.0, 'histogram': 0.0, 'trend': '未知', 'cross_status': '未知'}

            current_macd = macd.iloc[-1]
            current_signal = macd_signal.iloc[-1] if macd_signal is not None and not macd_signal.empty else 0.0
            current_histogram = macd_histogram.iloc[-1] if macd_histogram is not None and not macd_histogram.empty else 0.0

            # 趋势判断
            macd_trend = current_signals.get('macd_trend', 'UNKNOWN')
            trend_status = '多头' if macd_trend == 'BULLISH' else '空头' if macd_trend == 'BEARISH' else '震荡'

            # 金叉死叉判断
            cross_status = '金叉' if current_macd > current_signal else '死叉'

            # 零轴位置
            zero_position = '在零轴上方' if current_macd > 0 else '在零轴下方'

            return {
                'macd_value': round(current_macd, 4),
                'signal_line': round(current_signal, 4),
                'histogram': round(current_histogram, 4),
                'trend': trend_status,
                'cross_status': cross_status,
                'zero_position': zero_position
            }

        except Exception as e:
            print(f"⚠️ MACD分析错误: {e}")
            return {'macd_value': 0.0, 'signal_line': 0.0, 'histogram': 0.0, 'trend': '未知', 'cross_status': '未知'}

    def _analyze_rsi(self, tech_indicators: Dict, current_signals: Dict) -> Dict:
        """分析RSI指标"""
        try:
            rsi = tech_indicators.get('rsi')

            if rsi is None or rsi.empty:
                return {'rsi_value': 50.0, 'zone': '正常区', 'trend': '未知', 'signal': 'NEUTRAL'}

            current_rsi = rsi.iloc[-1]
            rsi_signal = current_signals.get('rsi_signal', 'NEUTRAL')

            # RSI区间判断
            if current_rsi > 70:
                zone = '超买区(70-100)'
            elif current_rsi < 30:
                zone = '超卖区(0-30)'
            else:
                zone = '正常区(30-70)'

            # 趋势方向
            if len(rsi) >= 5:
                recent_trend = rsi.iloc[-1] - rsi.iloc[-5]
                trend_direction = '向上' if recent_trend > 0 else '向下' if recent_trend < 0 else '横盘'
            else:
                trend_direction = '数据不足'

            # 信号转换
            signal_map = {
                'OVERBOUGHT': '超买',
                'OVERSOLD': '超卖',
                'NEUTRAL': '中性'
            }
            signal_status = signal_map.get(rsi_signal, '未知')

            return {
                'rsi_value': round(current_rsi, 1),
                'zone': zone,
                'trend': trend_direction,
                'signal': signal_status,
                'divergence': '无背离'  # 简化处理，实际需要复杂计算
            }

        except Exception as e:
            print(f"⚠️ RSI分析错误: {e}")
            return {'rsi_value': 50.0, 'zone': '正常区', 'trend': '未知', 'signal': 'NEUTRAL'}

    def _analyze_kdj(self) -> Dict:
        """分析KDJ指标"""
        try:
            # 获取日K数据并计算KDJ
            if self.analysis_system and hasattr(self.analysis_system, 'data_manager'):
                data = self.analysis_system.data_manager.get_daily_data()

                # 复用professional_chart_system的KDJ计算逻辑
                high = data['high']
                low = data['low']
                close = data['close']

                # 计算RSV
                period = 9
                lowest_low = low.rolling(window=period).min()
                highest_high = high.rolling(window=period).max()
                rsv = (close - lowest_low) / (highest_high - lowest_low) * 100

                # 计算K、D、J值
                k = rsv.ewm(com=2).mean()
                d = k.ewm(com=2).mean()
                j = 3 * k - 2 * d

                if not k.empty and not d.empty and not j.empty:
                    current_k = k.iloc[-1]
                    current_d = d.iloc[-1]
                    current_j = j.iloc[-1]

                    # 判断KDJ区间
                    if current_k > 80 and current_d > 80:
                        zone = '超买区(80-100)'
                    elif current_k < 20 and current_d < 20:
                        zone = '超卖区(0-20)'
                    else:
                        zone = '正常区(20-80)'

                    # 金叉死叉判断
                    cross_status = 'K线上穿D线' if current_k > current_d else 'K线下穿D线'

                    return {
                        'k_value': round(current_k, 1),
                        'd_value': round(current_d, 1),
                        'j_value': round(current_j, 1),
                        'zone': zone,
                        'cross_status': cross_status
                    }

            return {'k_value': 50.0, 'd_value': 50.0, 'j_value': 50.0, 'zone': '数据不足', 'cross_status': '未知'}

        except Exception as e:
            print(f"⚠️ KDJ分析错误: {e}")
            return {'k_value': 50.0, 'd_value': 50.0, 'j_value': 50.0, 'zone': '数据不足', 'cross_status': '未知'}

    def _analyze_volume(self) -> Dict:
        """分析成交量"""
        try:
            # 从基础数据获取成交量信息
            volume_data = self._get_volume_data()

            current_volume = volume_data['current']
            average_volume = volume_data['average']
            volume_ratio = volume_data['ratio']

            # 成交量状态判断
            if volume_ratio > 1.5:
                volume_status = '放量'
            elif volume_ratio < 0.7:
                volume_status = '缩量'
            else:
                volume_status = '正常'

            # 价量配合分析
            current_signals = self.analysis_results.get('current_signals', {})
            price_trend = current_signals.get('ema_trend', 'UNKNOWN')

            if price_trend == 'UP' and volume_ratio > 1.2:
                price_volume_match = '价涨量增'
            elif price_trend == 'DOWN' and volume_ratio > 1.2:
                price_volume_match = '价跌量增'
            elif price_trend == 'UP' and volume_ratio < 0.8:
                price_volume_match = '价涨量缩'
            elif price_trend == 'DOWN' and volume_ratio < 0.8:
                price_volume_match = '价跌量缩'
            else:
                price_volume_match = '价量平衡'

            return {
                'current_volume': current_volume,
                'average_volume': average_volume,
                'volume_ratio': volume_ratio,
                'volume_status': volume_status,
                'price_volume_match': price_volume_match
            }

        except Exception as e:
            print(f"⚠️ 成交量分析错误: {e}")
            return {'current_volume': 0, 'average_volume': 0, 'volume_ratio': 1.0, 'volume_status': '未知', 'price_volume_match': '未知'}

    def _analyze_obv(self) -> Dict:
        """分析OBV指标"""
        try:
            # 获取日K数据并计算OBV
            if self.analysis_system and hasattr(self.analysis_system, 'data_manager'):
                data = self.analysis_system.data_manager.get_daily_data()

                # 复用professional_chart_system的OBV计算逻辑，安全处理NaN值
                obv = [0]
                for i in range(1, len(data)):
                    # 安全获取成交量，如果是NaN则使用0
                    volume = data['volume'].iloc[i]
                    safe_volume = 0 if pd.isna(volume) else volume

                    if data['close'].iloc[i] > data['close'].iloc[i-1]:
                        obv.append(obv[-1] + safe_volume)
                    elif data['close'].iloc[i] < data['close'].iloc[i-1]:
                        obv.append(obv[-1] - safe_volume)
                    else:
                        obv.append(obv[-1])

                if len(obv) > 0:
                    current_obv = obv[-1]

                    # OBV趋势判断
                    if len(obv) >= 5:
                        recent_obv_change = obv[-1] - obv[-5]
                        obv_trend = '上升' if recent_obv_change > 0 else '下降' if recent_obv_change < 0 else '平稳'
                    else:
                        obv_trend = '数据不足'

                    # 价格配合度分析
                    current_price = data['close'].iloc[-1]
                    if len(data) >= 5:
                        price_change = current_price - data['close'].iloc[-5]
                        price_trend = '上涨' if price_change > 0 else '下跌' if price_change < 0 else '平稳'

                        # 判断是否同步
                        if (price_trend == '上涨' and obv_trend == '上升') or (price_trend == '下跌' and obv_trend == '下降'):
                            sync_status = '同步'
                            signal_type = '真实突破'
                        elif price_trend != '平稳' and obv_trend != price_trend.replace('上涨', '上升').replace('下跌', '下降'):
                            sync_status = '背离'
                            signal_type = '虚假突破'
                        else:
                            sync_status = '中性'
                            signal_type = '观望'
                    else:
                        sync_status = '数据不足'
                        signal_type = '未知'

                    # 安全处理NaN值
                    safe_obv_value = 0 if pd.isna(current_obv) else int(current_obv)

                    return {
                        'obv_value': safe_obv_value,
                        'obv_trend': obv_trend,
                        'sync_status': sync_status,
                        'signal_type': signal_type
                    }

            return {'obv_value': 0, 'obv_trend': '未知', 'sync_status': '未知', 'signal_type': '未知'}

        except Exception as e:
            print(f"⚠️ OBV分析错误: {e}")
            return {'obv_value': 0, 'obv_trend': '未知', 'sync_status': '未知', 'signal_type': '未知'}

    def _analyze_money_flow(self) -> Dict:
        """分析资金流向"""
        try:
            # 获取日K数据并计算资金流向
            if self.analysis_system and hasattr(self.analysis_system, 'data_manager'):
                data = self.analysis_system.data_manager.get_daily_data()

                # 复用professional_chart_system的资金流向计算逻辑
                typical_price = (data['high'] + data['low'] + data['close']) / 3
                money_flow = typical_price * data['volume']

                # 计算正负资金流
                price_change = typical_price.diff()
                positive_flow = money_flow.where(price_change > 0, 0)
                negative_flow = money_flow.where(price_change < 0, 0)

                # 计算资金流向比率
                period = 14
                positive_sum = positive_flow.rolling(period).sum()
                negative_sum = negative_flow.rolling(period).sum().abs()
                money_flow_ratio = positive_sum / (positive_sum + negative_sum) * 100

                if not money_flow_ratio.empty:
                    current_ratio = money_flow_ratio.iloc[-1]

                    # 流向状态判断
                    if current_ratio > 80:
                        flow_status = '强势流入'
                    elif current_ratio < 20:
                        flow_status = '强势流出'
                    else:
                        flow_status = '平衡(20-80)'

                    # 绿红区域比例
                    green_ratio = current_ratio
                    red_ratio = 100 - current_ratio

                    return {
                        'flow_ratio': round(current_ratio, 1),
                        'flow_status': flow_status,
                        'green_ratio': round(green_ratio, 1),
                        'red_ratio': round(red_ratio, 1)
                    }

            return {'flow_ratio': 50.0, 'flow_status': '数据不足', 'green_ratio': 50.0, 'red_ratio': 50.0}

        except Exception as e:
            print(f"⚠️ 资金流向分析错误: {e}")
            return {'flow_ratio': 50.0, 'flow_status': '数据不足', 'green_ratio': 50.0, 'red_ratio': 50.0}

    def _analyze_bollinger(self, tech_indicators: Dict, current_signals: Dict) -> Dict:
        """分析布林带指标"""
        try:
            bb_upper = tech_indicators.get('bb_upper')
            bb_middle = tech_indicators.get('bb_middle')
            bb_lower = tech_indicators.get('bb_lower')
            bb_position = tech_indicators.get('bb_position')

            if bb_position is None or bb_position.empty:
                return {'price_position': 50.0, 'zone': '中轨附近', 'band_status': '正常', 'breakthrough': '在轨道内'}

            current_position = bb_position.iloc[-1]

            # 布林带位置判断
            if current_position > 80:
                zone = '上轨附近(80-100%)'
                band_status = '接近上轨'
            elif current_position < 20:
                zone = '下轨附近(0-20%)'
                band_status = '接近下轨'
            else:
                zone = '中轨附近(20-80%)'
                band_status = '正常'

            # 突破状态
            if current_position > 95:
                breakthrough = '突破上轨'
            elif current_position < 5:
                breakthrough = '跌破下轨'
            else:
                breakthrough = '在轨道内'

            # 获取具体数值
            upper_value = bb_upper.iloc[-1] if bb_upper is not None and not bb_upper.empty else 0.0
            middle_value = bb_middle.iloc[-1] if bb_middle is not None and not bb_middle.empty else 0.0
            lower_value = bb_lower.iloc[-1] if bb_lower is not None and not bb_lower.empty else 0.0

            return {
                'price_position': round(current_position, 1),
                'zone': zone,
                'band_status': band_status,
                'breakthrough': breakthrough,
                'upper_band': round(upper_value, 2),
                'middle_band': round(middle_value, 2),
                'lower_band': round(lower_value, 2)
            }

        except Exception as e:
            print(f"⚠️ 布林带分析错误: {e}")
            return {'price_position': 50.0, 'zone': '中轨附近', 'band_status': '正常', 'breakthrough': '在轨道内'}

    def _analyze_atr(self) -> Dict:
        """分析ATR指标"""
        try:
            # 获取日K数据并计算ATR
            if self.analysis_system and hasattr(self.analysis_system, 'data_manager'):
                data = self.analysis_system.data_manager.get_daily_data()

                # 复用professional_chart_system的ATR计算逻辑
                high = data['high']
                low = data['low']
                close = data['close']

                # 计算真实波幅
                tr1 = high - low
                tr2 = abs(high - close.shift(1))
                tr3 = abs(low - close.shift(1))

                # 取最大值作为真实波幅
                true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

                # 计算ATR（真实波幅的移动平均）
                atr = true_range.rolling(window=14).mean()

                # 计算波动率百分比
                volatility_pct = (atr / close) * 100

                if not atr.empty and not volatility_pct.empty:
                    current_atr = atr.iloc[-1]
                    current_volatility = volatility_pct.iloc[-1]

                    # 波动率等级判断
                    if current_volatility > 7:
                        volatility_level = '极高波动(>7%)'
                        color_display = '红色'
                    elif current_volatility > 5:
                        volatility_level = '高波动(5-7%)'
                        color_display = '橙色'
                    elif current_volatility > 3:
                        volatility_level = '中等波动(3-5%)'
                        color_display = '黄色'
                    elif current_volatility > 2:
                        volatility_level = '低波动(2-3%)'
                        color_display = '绿色'
                    else:
                        volatility_level = '极低波动(<2%)'
                        color_display = '深绿'

                    # 趋势方向
                    if len(atr) >= 5:
                        atr_trend = atr.iloc[-1] - atr.iloc[-5]
                        trend_direction = '上升' if atr_trend > 0 else '下降' if atr_trend < 0 else '平稳'
                    else:
                        trend_direction = '数据不足'

                    return {
                        'atr_value': round(current_atr, 2),
                        'volatility_pct': round(current_volatility, 2),
                        'volatility_level': volatility_level,
                        'color_display': color_display,
                        'trend_direction': trend_direction
                    }

            return {'atr_value': 0.0, 'volatility_pct': 0.0, 'volatility_level': '数据不足', 'color_display': '灰色', 'trend_direction': '未知'}

        except Exception as e:
            print(f"⚠️ ATR分析错误: {e}")
            return {'atr_value': 0.0, 'volatility_pct': 0.0, 'volatility_level': '数据不足', 'color_display': '灰色', 'trend_direction': '未知'}

    def _evaluate_strong_buy_signals(self, indicators: Dict) -> Dict:
        """
        评估强买入信号（需满足5-6个，共9个条件）

        Args:
            indicators: 技术指标数据

        Returns:
            Dict: 强买入信号评估结果
        """
        try:
            signals = {}

            # 基础技术条件（4个）
            signals['price_above_ma'] = self._check_price_above_ma(indicators)
            signals['macd_golden_cross'] = self._check_macd_golden_cross(indicators)
            signals['rsi_healthy'] = self._check_rsi_healthy_range(indicators)
            signals['kdj_golden_cross'] = self._check_kdj_golden_cross(indicators)

            # 成交量与资金确认（3个）
            signals['volume_increase'] = self._check_volume_increase(indicators)
            signals['money_flow_in'] = self._check_money_flow_in(indicators)
            signals['obv_sync'] = self._check_obv_sync(indicators)

            # 风险控制指标（2个）
            signals['bb_position_good'] = self._check_bb_position_good(indicators)
            signals['atr_moderate'] = self._check_atr_moderate(indicators)

            return signals

        except Exception as e:
            print(f"⚠️ 强买入信号评估错误: {e}")
            return self._get_default_buy_signals()

    def _evaluate_strong_sell_signals(self, indicators: Dict) -> Dict:
        """
        评估强卖出信号（出现4-5个，共8个条件）

        Args:
            indicators: 技术指标数据

        Returns:
            Dict: 强卖出信号评估结果
        """
        try:
            signals = {}

            # 基础技术条件（4个）
            signals['price_below_ma'] = self._check_price_below_ma(indicators)
            signals['macd_death_cross'] = self._check_macd_death_cross(indicators)
            signals['rsi_overbought'] = self._check_rsi_overbought(indicators)
            signals['kdj_death_cross'] = self._check_kdj_death_cross(indicators)

            # 成交量与资金警示（2个）
            signals['volume_fake'] = self._check_volume_fake(indicators)
            signals['money_flow_out'] = self._check_money_flow_out(indicators)
            signals['obv_divergence'] = self._check_obv_divergence(indicators)

            # 风险警示指标（2个）
            signals['bb_position_high'] = self._check_bb_position_high(indicators)
            signals['atr_high'] = self._check_atr_high(indicators)

            return signals

        except Exception as e:
            print(f"⚠️ 强卖出信号评估错误: {e}")
            return self._get_default_sell_signals()

    def _evaluate_watch_signals(self, indicators: Dict) -> Dict:
        """
        评估观望条件（共5个条件）

        Args:
            indicators: 技术指标数据

        Returns:
            Dict: 观望信号评估结果
        """
        try:
            signals = {}

            signals['signal_conflict'] = self._check_signal_conflict(indicators)
            signals['volume_shrink'] = self._check_volume_shrink_obv_flat(indicators)
            signals['rsi_sideways'] = self._check_rsi_sideways(indicators)
            signals['bb_middle_range'] = self._check_bb_middle_range(indicators)
            signals['atr_very_low'] = self._check_atr_very_low(indicators)

            return signals

        except Exception as e:
            print(f"⚠️ 观望信号评估错误: {e}")
            return self._get_default_watch_signals()

    # 强买入信号检查方法
    def _check_price_above_ma(self, indicators: Dict) -> Dict:
        """检查价格在主要均线之上且均线多头排列"""
        try:
            price_ma = indicators.get('price_ma', {})
            price_position = price_ma.get('price_position', '')
            ma_arrangement = price_ma.get('ma_arrangement', '')

            status = (price_position == '在所有均线之上' and ma_arrangement == '多头排列')

            return {
                'status': status,
                'description': f"价格{price_position}，均线{ma_arrangement}",
                'detail': f"EMA12距离: {price_ma.get('ema_12_distance', 0):.2f}%"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_macd_golden_cross(self, indicators: Dict) -> Dict:
        """检查MACD金叉或在零轴上方且柱状图转红"""
        try:
            macd = indicators.get('macd', {})
            cross_status = macd.get('cross_status', '')
            zero_position = macd.get('zero_position', '')
            histogram = macd.get('histogram', 0)

            status = (cross_status == '金叉' or (zero_position == '在零轴上方' and histogram > 0))

            return {
                'status': status,
                'description': f"MACD{cross_status}，{zero_position}",
                'detail': f"柱状图: {histogram:.4f}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_rsi_healthy_range(self, indicators: Dict) -> Dict:
        """检查RSI在30-70之间且向上突破50"""
        try:
            rsi = indicators.get('rsi', {})
            rsi_value = rsi.get('rsi_value', 50)
            trend = rsi.get('trend', '')

            status = (30 <= rsi_value <= 70 and rsi_value > 50 and trend == '向上')

            return {
                'status': status,
                'description': f"RSI: {rsi_value}，趋势{trend}",
                'detail': f"区间: {rsi.get('zone', '未知')}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_kdj_golden_cross(self, indicators: Dict) -> Dict:
        """检查KDJ出现金叉且K、D线在20-80区间"""
        try:
            kdj = indicators.get('kdj', {})
            k_value = kdj.get('k_value', 50)
            d_value = kdj.get('d_value', 50)
            cross_status = kdj.get('cross_status', '')

            status = (cross_status == 'K线上穿D线' and 20 <= k_value <= 80 and 20 <= d_value <= 80)

            return {
                'status': status,
                'description': f"KDJ {cross_status}，K:{k_value} D:{d_value}",
                'detail': f"区间: {kdj.get('zone', '未知')}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_volume_increase(self, indicators: Dict) -> Dict:
        """检查成交量明显放大（超过20日均量1.5倍）"""
        try:
            volume = indicators.get('volume', {})
            volume_ratio = volume.get('volume_ratio', 1.0)
            volume_status = volume.get('volume_status', '')

            status = (volume_ratio > 1.5)

            return {
                'status': status,
                'description': f"成交量{volume_status}，比率{volume_ratio:.2f}倍",
                'detail': f"当前: {volume.get('current_volume', 0)}件"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_money_flow_in(self, indicators: Dict) -> Dict:
        """检查资金净流入且绿色区域明显扩大"""
        try:
            money_flow = indicators.get('money_flow', {})
            flow_ratio = money_flow.get('flow_ratio', 50)
            green_ratio = money_flow.get('green_ratio', 50)

            status = (flow_ratio > 60 and green_ratio > 60)

            return {
                'status': status,
                'description': f"资金流向比率{flow_ratio:.1f}%",
                'detail': f"绿色区域: {green_ratio:.1f}%"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_obv_sync(self, indicators: Dict) -> Dict:
        """检查OBV与价格同步上升（关键确认）"""
        try:
            obv = indicators.get('obv', {})
            obv_trend = obv.get('obv_trend', '')
            sync_status = obv.get('sync_status', '')
            signal_type = obv.get('signal_type', '')

            status = (obv_trend == '上升' and sync_status == '同步' and signal_type == '真实突破')

            return {
                'status': status,
                'description': f"OBV{obv_trend}，{sync_status}",
                'detail': f"信号类型: {signal_type}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_bb_position_good(self, indicators: Dict) -> Dict:
        """检查布林带在中下轨区域（价格位置<80%）"""
        try:
            bollinger = indicators.get('bollinger', {})
            price_position = bollinger.get('price_position', 50)
            zone = bollinger.get('zone', '')

            status = (price_position < 80)

            return {
                'status': status,
                'description': f"布林带位置{price_position:.1f}%",
                'detail': f"区间: {zone}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_atr_moderate(self, indicators: Dict) -> Dict:
        """检查ATR波动率适中（<5%，显示绿色或黄色）"""
        try:
            atr = indicators.get('atr', {})
            volatility_pct = atr.get('volatility_pct', 0)
            color_display = atr.get('color_display', '')

            status = (volatility_pct < 5 and color_display in ['绿色', '黄色', '深绿'])

            return {
                'status': status,
                'description': f"ATR波动率{volatility_pct:.2f}%",
                'detail': f"颜色: {color_display}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    # 强卖出信号检查方法
    def _check_price_below_ma(self, indicators: Dict) -> Dict:
        """检查价格跌破关键均线或均线空头排列"""
        try:
            price_ma = indicators.get('price_ma', {})
            price_position = price_ma.get('price_position', '')
            ma_arrangement = price_ma.get('ma_arrangement', '')

            status = (price_position == '部分均线之下' or ma_arrangement == '空头排列')

            return {
                'status': status,
                'description': f"价格{price_position}，均线{ma_arrangement}",
                'detail': f"EMA26距离: {price_ma.get('ema_26_distance', 0):.2f}%"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_macd_death_cross(self, indicators: Dict) -> Dict:
        """检查MACD死叉或跌破零轴且柱状图转绿"""
        try:
            macd = indicators.get('macd', {})
            cross_status = macd.get('cross_status', '')
            zero_position = macd.get('zero_position', '')
            histogram = macd.get('histogram', 0)

            status = (cross_status == '死叉' or (zero_position == '在零轴下方' and histogram < 0))

            return {
                'status': status,
                'description': f"MACD{cross_status}，{zero_position}",
                'detail': f"柱状图: {histogram:.4f}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_rsi_overbought(self, indicators: Dict) -> Dict:
        """检查RSI超过70或出现顶背离"""
        try:
            rsi = indicators.get('rsi', {})
            rsi_value = rsi.get('rsi_value', 50)
            zone = rsi.get('zone', '')
            divergence = rsi.get('divergence', '')

            status = (rsi_value > 70 or '背离' in divergence)

            return {
                'status': status,
                'description': f"RSI: {rsi_value}，{zone}",
                'detail': f"背离状态: {divergence}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_kdj_death_cross(self, indicators: Dict) -> Dict:
        """检查KDJ死叉且在高位（>80）"""
        try:
            kdj = indicators.get('kdj', {})
            k_value = kdj.get('k_value', 50)
            d_value = kdj.get('d_value', 50)
            cross_status = kdj.get('cross_status', '')

            status = (cross_status == 'K线下穿D线' and (k_value > 80 or d_value > 80))

            return {
                'status': status,
                'description': f"KDJ {cross_status}，K:{k_value} D:{d_value}",
                'detail': f"区间: {kdj.get('zone', '未知')}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_volume_fake(self, indicators: Dict) -> Dict:
        """检查成交量萎缩或虚假放量（OBV不跟随）"""
        try:
            volume = indicators.get('volume', {})
            obv = indicators.get('obv', {})
            volume_ratio = volume.get('volume_ratio', 1.0)
            sync_status = obv.get('sync_status', '')

            status = (volume_ratio < 0.7 or (volume_ratio > 1.5 and sync_status == '背离'))

            return {
                'status': status,
                'description': f"成交量比率{volume_ratio:.2f}，OBV{sync_status}",
                'detail': f"成交量状态: {volume.get('volume_status', '未知')}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_money_flow_out(self, indicators: Dict) -> Dict:
        """检查资金大幅流出且红色区域扩大"""
        try:
            money_flow = indicators.get('money_flow', {})
            flow_ratio = money_flow.get('flow_ratio', 50)
            red_ratio = money_flow.get('red_ratio', 50)

            status = (flow_ratio < 40 and red_ratio > 60)

            return {
                'status': status,
                'description': f"资金流向比率{flow_ratio:.1f}%",
                'detail': f"红色区域: {red_ratio:.1f}%"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_obv_divergence(self, indicators: Dict) -> Dict:
        """检查OBV出现明显背离（价格涨但OBV跌）"""
        try:
            obv = indicators.get('obv', {})
            sync_status = obv.get('sync_status', '')
            signal_type = obv.get('signal_type', '')

            status = (sync_status == '背离' or signal_type == '虚假突破')

            return {
                'status': status,
                'description': f"OBV{sync_status}",
                'detail': f"信号类型: {signal_type}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_bb_position_high(self, indicators: Dict) -> Dict:
        """检查布林带接近或突破上轨（价格位置>80%）"""
        try:
            bollinger = indicators.get('bollinger', {})
            price_position = bollinger.get('price_position', 50)
            breakthrough = bollinger.get('breakthrough', '')

            status = (price_position > 80 or breakthrough == '突破上轨')

            return {
                'status': status,
                'description': f"布林带位置{price_position:.1f}%",
                'detail': f"突破状态: {breakthrough}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_atr_high(self, indicators: Dict) -> Dict:
        """检查ATR波动率过高（>5%，显示红色）"""
        try:
            atr = indicators.get('atr', {})
            volatility_pct = atr.get('volatility_pct', 0)
            color_display = atr.get('color_display', '')

            status = (volatility_pct > 5 or color_display == '红色')

            return {
                'status': status,
                'description': f"ATR波动率{volatility_pct:.2f}%",
                'detail': f"颜色: {color_display}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    # 观望信号检查方法
    def _check_signal_conflict(self, indicators: Dict) -> Dict:
        """检查技术指标信号冲突（买入卖出信号各半）"""
        try:
            # 简化判断：检查主要指标的冲突情况
            macd = indicators.get('macd', {})
            rsi = indicators.get('rsi', {})
            kdj = indicators.get('kdj', {})

            macd_bullish = macd.get('cross_status', '') == '金叉'
            rsi_bullish = rsi.get('rsi_value', 50) > 50
            kdj_bullish = kdj.get('cross_status', '') == 'K线上穿D线'

            bullish_count = sum([macd_bullish, rsi_bullish, kdj_bullish])
            status = (bullish_count == 1 or bullish_count == 2)  # 信号不一致

            return {
                'status': status,
                'description': f"多头信号{bullish_count}/3个",
                'detail': f"MACD:{macd_bullish}, RSI:{rsi_bullish}, KDJ:{kdj_bullish}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_volume_shrink_obv_flat(self, indicators: Dict) -> Dict:
        """检查成交量持续萎缩且OBV平稳"""
        try:
            volume = indicators.get('volume', {})
            obv = indicators.get('obv', {})
            volume_ratio = volume.get('volume_ratio', 1.0)
            obv_trend = obv.get('obv_trend', '')

            status = (volume_ratio < 0.8 and obv_trend == '平稳')

            return {
                'status': status,
                'description': f"成交量比率{volume_ratio:.2f}，OBV{obv_trend}",
                'detail': f"成交量状态: {volume.get('volume_status', '未知')}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_rsi_sideways(self, indicators: Dict) -> Dict:
        """检查RSI在45-55区间横盘"""
        try:
            rsi = indicators.get('rsi', {})
            rsi_value = rsi.get('rsi_value', 50)
            trend = rsi.get('trend', '')

            status = (45 <= rsi_value <= 55 and trend in ['横盘', '平稳'])

            return {
                'status': status,
                'description': f"RSI: {rsi_value}，趋势{trend}",
                'detail': f"区间: {rsi.get('zone', '未知')}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_bb_middle_range(self, indicators: Dict) -> Dict:
        """检查价格在布林带中轨附近震荡"""
        try:
            bollinger = indicators.get('bollinger', {})
            price_position = bollinger.get('price_position', 50)
            zone = bollinger.get('zone', '')

            status = (40 <= price_position <= 60 and '中轨附近' in zone)

            return {
                'status': status,
                'description': f"布林带位置{price_position:.1f}%",
                'detail': f"区间: {zone}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _check_atr_very_low(self, indicators: Dict) -> Dict:
        """检查ATR波动率极低（<2%）且无明确趋势"""
        try:
            atr = indicators.get('atr', {})
            volatility_pct = atr.get('volatility_pct', 0)
            trend_direction = atr.get('trend_direction', '')

            status = (volatility_pct < 2 and trend_direction in ['平稳', '数据不足'])

            return {
                'status': status,
                'description': f"ATR波动率{volatility_pct:.2f}%",
                'detail': f"趋势: {trend_direction}"
            }
        except:
            return {'status': False, 'description': '数据获取失败', 'detail': ''}

    def _get_key_confirmation_indicators(self, indicators: Dict) -> Dict:
        """获取关键确认指标"""
        try:
            obv = indicators.get('obv', {})
            volume = indicators.get('volume', {})

            return {
                'obv_sync': obv.get('sync_status', '未知'),
                'volume_match': volume.get('price_volume_match', '未知'),
                'signal_type': obv.get('signal_type', '未知')
            }
        except:
            return {'obv_sync': '未知', 'volume_match': '未知', 'signal_type': '未知'}

    def _get_default_evaluation_result(self) -> Dict:
        """获取默认评估结果"""
        return {
            'strong_buy_signals': self._get_default_buy_signals(),
            'strong_sell_signals': self._get_default_sell_signals(),
            'watch_signals': self._get_default_watch_signals(),
            'buy_count': 0,
            'sell_count': 0,
            'watch_count': 0,
            'main_signal': '观望',
            'signal_strength': '数据不足',
            'key_confirmation': {'obv_sync': '未知', 'volume_match': '未知', 'signal_type': '未知'}
        }

    def _get_default_buy_signals(self) -> Dict:
        """获取默认强买入信号"""
        default_signal = {'status': False, 'description': '数据获取失败', 'detail': ''}
        return {
            'price_above_ma': default_signal.copy(),
            'macd_golden_cross': default_signal.copy(),
            'rsi_healthy': default_signal.copy(),
            'kdj_golden_cross': default_signal.copy(),
            'volume_increase': default_signal.copy(),
            'money_flow_in': default_signal.copy(),
            'obv_sync': default_signal.copy(),
            'bb_position_good': default_signal.copy(),
            'atr_moderate': default_signal.copy()
        }

    def _get_default_sell_signals(self) -> Dict:
        """获取默认强卖出信号"""
        default_signal = {'status': False, 'description': '数据获取失败', 'detail': ''}
        return {
            'price_below_ma': default_signal.copy(),
            'macd_death_cross': default_signal.copy(),
            'rsi_overbought': default_signal.copy(),
            'kdj_death_cross': default_signal.copy(),
            'volume_fake': default_signal.copy(),
            'money_flow_out': default_signal.copy(),
            'obv_divergence': default_signal.copy(),
            'bb_position_high': default_signal.copy(),
            'atr_high': default_signal.copy()
        }

    def _get_default_watch_signals(self) -> Dict:
        """获取默认观望信号"""
        default_signal = {'status': False, 'description': '数据获取失败', 'detail': ''}
        return {
            'signal_conflict': default_signal.copy(),
            'volume_shrink': default_signal.copy(),
            'rsi_sideways': default_signal.copy(),
            'bb_middle_range': default_signal.copy(),
            'atr_very_low': default_signal.copy()
        }

    # ATR风险评估方法
    def _get_atr_data(self) -> Dict:
        """获取ATR数据"""
        try:
            # 从技术指标分析中获取ATR数据
            indicators = self._analyze_technical_indicators()
            atr_data = indicators.get('atr', {})

            return {
                'current_value': atr_data.get('atr_value', 0.0),
                'volatility_percentage': atr_data.get('volatility_pct', 0.0),
                'volatility_level': atr_data.get('volatility_level', '数据不足'),
                'color_display': atr_data.get('color_display', '灰色'),
                'trend_direction': atr_data.get('trend_direction', '未知')
            }
        except Exception as e:
            print(f"⚠️ ATR数据获取错误: {e}")
            return {
                'current_value': 0.0,
                'volatility_percentage': 0.0,
                'volatility_level': '数据不足',
                'color_display': '灰色',
                'trend_direction': '未知'
            }

    def _analyze_volatility_risk(self, atr_data: Dict) -> Dict:
        """分析波动率风险"""
        try:
            volatility_pct = atr_data['volatility_percentage']
            current_atr = atr_data['current_value']

            # 波动率等级分类（基于professional_chart_system的标准）
            if volatility_pct > 7:
                risk_level = '极高风险'
                risk_color = '红色'
                risk_description = '极高波动(>7%)'
                risk_advice = '建议暂停交易或大幅降低仓位'
            elif volatility_pct > 5:
                risk_level = '高风险'
                risk_color = '橙色'
                risk_description = '高波动(5-7%)'
                risk_advice = '谨慎操作，严格止损'
            elif volatility_pct > 3:
                risk_level = '中等风险'
                risk_color = '黄色'
                risk_description = '中等波动(3-5%)'
                risk_advice = '正常操作，注意风控'
            elif volatility_pct > 2:
                risk_level = '低风险'
                risk_color = '绿色'
                risk_description = '低波动(2-3%)'
                risk_advice = '相对安全，可适当加仓'
            else:
                risk_level = '极低风险'
                risk_color = '深绿'
                risk_description = '极低波动(<2%)'
                risk_advice = '市场平静，等待机会'

            # 趋势分析
            trend_direction = atr_data['trend_direction']
            if trend_direction == '上升':
                trend_warning = '波动率上升，风险增加'
            elif trend_direction == '下降':
                trend_warning = '波动率下降，风险降低'
            else:
                trend_warning = '波动率平稳'

            return {
                'current_atr': current_atr,
                'volatility_pct': volatility_pct,
                'risk_level': risk_level,
                'risk_color': risk_color,
                'risk_description': risk_description,
                'risk_advice': risk_advice,
                'trend_direction': trend_direction,
                'trend_warning': trend_warning
            }

        except Exception as e:
            print(f"⚠️ 波动率风险分析错误: {e}")
            return self._get_default_volatility_analysis()

    def _calculate_stop_loss_levels(self, atr_data: Dict, current_price: float) -> Dict:
        """计算止损水平"""
        try:
            current_atr = atr_data['current_value']

            if current_atr <= 0 or current_price <= 0:
                return self._get_default_stop_loss_levels(current_price)

            # 多头止损（价格下跌时的止损）
            long_stop_loss = {
                'conservative': current_price - (current_atr * 2),    # 保守止损
                'standard': current_price - (current_atr * 2.5),     # 标准止损
                'loose': current_price - (current_atr * 3)           # 宽松止损
            }

            # 空头止损（价格上涨时的止损）
            short_stop_loss = {
                'conservative': current_price + (current_atr * 2),    # 保守止损
                'standard': current_price + (current_atr * 2.5),     # 标准止损
                'loose': current_price + (current_atr * 3)           # 宽松止损
            }

            # 计算止损幅度百分比
            long_stop_pct = {
                'conservative': (current_atr * 2 / current_price) * 100,
                'standard': (current_atr * 2.5 / current_price) * 100,
                'loose': (current_atr * 3 / current_price) * 100
            }

            return {
                'current_price': current_price,
                'current_atr': current_atr,
                'long_positions': long_stop_loss,
                'short_positions': short_stop_loss,
                'stop_loss_percentages': long_stop_pct,
                'recommended_level': self._get_recommended_stop_level(atr_data),
                'stop_loss_advice': self._get_stop_loss_advice(atr_data)
            }

        except Exception as e:
            print(f"⚠️ 止损计算错误: {e}")
            return self._get_default_stop_loss_levels(current_price)

    def _identify_key_price_levels(self) -> Dict:
        """识别关键价位"""
        try:
            # 获取支撑阻力位数据
            support_resistance = self.analysis_results.get('support_resistance', {})
            current_price = self._get_current_price()

            # 上方阻力位
            resistance = support_resistance.get('recent_resistance', 0.0)
            resistance_distance = 0.0
            if resistance > 0 and current_price > 0:
                resistance_distance = ((resistance - current_price) / current_price) * 100

            # 下方支撑位
            support = support_resistance.get('recent_support', 0.0)
            support_distance = 0.0
            if support > 0 and current_price > 0:
                support_distance = ((current_price - support) / current_price) * 100

            # 关键突破位和跌破位
            key_breakthrough = resistance  # 上方阻力位作为关键突破位
            key_breakdown = support       # 下方支撑位作为关键跌破位

            return {
                'current_price': current_price,
                'upper_resistance': resistance,
                'resistance_distance_pct': resistance_distance,
                'lower_support': support,
                'support_distance_pct': support_distance,
                'key_breakthrough': key_breakthrough,
                'key_breakdown': key_breakdown,
                'price_range_analysis': self._analyze_price_range(current_price, support, resistance)
            }

        except Exception as e:
            print(f"⚠️ 关键价位识别错误: {e}")
            return self._get_default_key_levels()

    def _generate_risk_summary(self, volatility_analysis: Dict, stop_loss_levels: Dict) -> Dict:
        """生成风险摘要"""
        try:
            risk_level = volatility_analysis.get('risk_level', '未知')
            volatility_pct = volatility_analysis.get('volatility_pct', 0.0)
            recommended_stop = stop_loss_levels.get('recommended_level', 'standard')

            # 综合风险评级
            if '极高' in risk_level:
                overall_risk = '极高风险'
                risk_score = 90
            elif '高' in risk_level:
                overall_risk = '高风险'
                risk_score = 75
            elif '中等' in risk_level:
                overall_risk = '中等风险'
                risk_score = 50
            elif '低' in risk_level:
                overall_risk = '低风险'
                risk_score = 25
            else:
                overall_risk = '极低风险'
                risk_score = 10

            # 风险提示
            risk_warnings = []
            if volatility_pct > 5:
                risk_warnings.append('波动率过高，建议降低仓位')
            if volatility_pct < 2:
                risk_warnings.append('波动率极低，市场可能酝酿大行情')

            return {
                'overall_risk': overall_risk,
                'risk_score': risk_score,
                'recommended_stop_level': recommended_stop,
                'risk_warnings': risk_warnings,
                'trading_advice': volatility_analysis.get('risk_advice', '正常操作')
            }

        except Exception as e:
            print(f"⚠️ 风险摘要生成错误: {e}")
            return self._get_default_risk_summary()

    # 辅助方法
    def _get_recommended_stop_level(self, atr_data: Dict) -> str:
        """获取推荐止损级别"""
        try:
            volatility_pct = atr_data.get('volatility_percentage', 0.0)

            if volatility_pct > 5:
                return 'loose'      # 高波动时使用宽松止损
            elif volatility_pct > 3:
                return 'standard'   # 中等波动时使用标准止损
            else:
                return 'conservative'  # 低波动时使用保守止损

        except:
            return 'standard'

    def _get_stop_loss_advice(self, atr_data: Dict) -> str:
        """获取止损建议"""
        try:
            volatility_pct = atr_data.get('volatility_percentage', 0.0)

            if volatility_pct > 7:
                return '极高波动期间，建议使用3×ATR宽松止损或暂停交易'
            elif volatility_pct > 5:
                return '高波动期间，建议使用2.5-3×ATR止损，严格执行'
            elif volatility_pct > 3:
                return '中等波动期间，建议使用2.5×ATR标准止损'
            elif volatility_pct > 2:
                return '低波动期间，可使用2×ATR保守止损'
            else:
                return '极低波动期间，使用2×ATR止损，注意突破信号'

        except:
            return '建议使用2.5×ATR标准止损'

    def _analyze_price_range(self, current_price: float, support: float, resistance: float) -> str:
        """分析价格区间位置"""
        try:
            if support <= 0 or resistance <= 0 or current_price <= 0:
                return '数据不足，无法分析'

            if resistance <= support:
                return '支撑阻力位数据异常'

            # 计算价格在支撑阻力区间的位置
            range_size = resistance - support
            price_position = (current_price - support) / range_size

            if price_position > 0.8:
                return '接近阻力位，注意回调风险'
            elif price_position > 0.6:
                return '偏向阻力位，可考虑减仓'
            elif price_position > 0.4:
                return '区间中部，相对均衡'
            elif price_position > 0.2:
                return '偏向支撑位，可考虑加仓'
            else:
                return '接近支撑位，注意破位风险'

        except:
            return '价格区间分析失败'

    # 默认值方法
    def _get_default_risk_assessment(self) -> Dict:
        """获取默认风险评估"""
        return {
            'volatility_analysis': self._get_default_volatility_analysis(),
            'stop_loss_levels': self._get_default_stop_loss_levels(0.0),
            'key_levels': self._get_default_key_levels(),
            'risk_summary': self._get_default_risk_summary()
        }

    def _get_default_volatility_analysis(self) -> Dict:
        """获取默认波动率分析"""
        return {
            'current_atr': 0.0,
            'volatility_pct': 0.0,
            'risk_level': '数据不足',
            'risk_color': '灰色',
            'risk_description': '数据不足',
            'risk_advice': '请检查数据完整性',
            'trend_direction': '未知',
            'trend_warning': '数据不足'
        }

    def _get_default_stop_loss_levels(self, current_price: float) -> Dict:
        """获取默认止损水平"""
        return {
            'current_price': current_price,
            'current_atr': 0.0,
            'long_positions': {
                'conservative': current_price * 0.95,
                'standard': current_price * 0.93,
                'loose': current_price * 0.90
            },
            'short_positions': {
                'conservative': current_price * 1.05,
                'standard': current_price * 1.07,
                'loose': current_price * 1.10
            },
            'stop_loss_percentages': {
                'conservative': 5.0,
                'standard': 7.0,
                'loose': 10.0
            },
            'recommended_level': 'standard',
            'stop_loss_advice': '数据不足，建议使用固定百分比止损'
        }

    def _get_default_key_levels(self) -> Dict:
        """获取默认关键价位"""
        return {
            'current_price': 0.0,
            'upper_resistance': 0.0,
            'resistance_distance_pct': 0.0,
            'lower_support': 0.0,
            'support_distance_pct': 0.0,
            'key_breakthrough': 0.0,
            'key_breakdown': 0.0,
            'price_range_analysis': '数据不足'
        }

    def _get_default_risk_summary(self) -> Dict:
        """获取默认风险摘要"""
        return {
            'overall_risk': '数据不足',
            'risk_score': 50,
            'recommended_stop_level': 'standard',
            'risk_warnings': ['数据不足，请检查数据完整性'],
            'trading_advice': '等待数据完整后再进行分析'
        }

    # Markdown格式化方法
    def _format_header_section(self, basic_data: Dict) -> str:
        """格式化报告头部"""
        try:
            from datetime import datetime
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M')
            current_price = basic_data.get('current_price', 0.0)

            return f"""# CS2饰品技术分析报告
**饰品名称：** {self.item_name}
**分析时间：** {current_time}
**当前价格：** ¥{current_price:,.2f}"""

        except Exception as e:
            print(f"⚠️ 头部格式化错误: {e}")
            return f"# {self.item_name} - 技术分析报告"

    def _format_basic_data_section(self, basic_data: Dict) -> str:
        """格式化基础数据部分"""
        try:
            current_price = basic_data.get('current_price', 0.0)
            price_changes = basic_data.get('price_changes', {})
            volume_data = basic_data.get('volume_data', {})
            price_range = basic_data.get('price_range', {})
            support_resistance = basic_data.get('support_resistance', {})
            last_trade_time = basic_data.get('last_trade_time', '未知')

            # 价格信息
            price_info = f"""### 价格信息
- **当前价格：** ¥{current_price:,.2f}
- **24小时变化：** {price_changes.get('24h', 0.0):+.2f}% (¥{current_price * price_changes.get('24h', 0.0) / 100:+.2f})
- **7日变化：** {price_changes.get('7d', 0.0):+.2f}% (¥{current_price * price_changes.get('7d', 0.0) / 100:+.2f})
- **30日变化：** {price_changes.get('30d', 0.0):+.2f}% (¥{current_price * price_changes.get('30d', 0.0) / 100:+.2f})"""

            # 交易数据
            trading_info = f"""### 交易数据
- **24小时成交量：** {volume_data.get('current', 0)}件
- **平均成交量(20日)：** {volume_data.get('average', 0)}件
- **成交量比率：** {volume_data.get('ratio', 1.0):.2f}倍
- **最近交易时间：** {last_trade_time}"""

            # 价格区间
            price_range_info = f"""### 价格区间
- **52周最高：** ¥{price_range.get('high_52w', 0.0):,.2f}
- **52周最低：** ¥{price_range.get('low_52w', 0.0):,.2f}
- **支撑位：** ¥{support_resistance.get('support', 0.0):,.2f}
- **阻力位：** ¥{support_resistance.get('resistance', 0.0):,.2f}"""

            return f"{price_info}\n\n{trading_info}\n\n{price_range_info}"

        except Exception as e:
            print(f"⚠️ 基础数据格式化错误: {e}")
            return "基础数据格式化失败"

    def _format_technical_indicators_section(self, technical_data: Dict) -> str:
        """格式化技术指标部分"""
        try:
            sections = []

            # 2.1 价格与移动平均线
            price_ma = technical_data.get('price_ma', {})
            sections.append(f"""### 2.1 价格与移动平均线
- **当前价格位置：** {price_ma.get('price_position', '未知')}
- **均线排列：** {price_ma.get('ma_arrangement', '未知')}
- **关键均线距离：**
  - 距离EMA12：{price_ma.get('ema_12_distance', 0.0):+.2f}%
  - 距离EMA26：{price_ma.get('ema_26_distance', 0.0):+.2f}%
  - 距离SMA20：{price_ma.get('sma_20_distance', 0.0):+.2f}%""")

            # 2.2 MACD指标
            macd = technical_data.get('macd', {})
            sections.append(f"""### 2.2 MACD指标
- **MACD值：** {macd.get('macd_value', 0.0):.4f}
- **信号线：** {macd.get('signal_line', 0.0):.4f}
- **柱状图：** {macd.get('histogram', 0.0):.4f}
- **零轴位置：** {macd.get('zero_position', '未知')}
- **交叉状态：** {macd.get('cross_status', '未知')}
- **趋势状态：** {macd.get('trend', '未知')}""")

            # 2.3 RSI相对强弱指标
            rsi = technical_data.get('rsi', {})
            sections.append(f"""### 2.3 RSI相对强弱指标
- **RSI值：** {rsi.get('rsi_value', 50.0):.1f}
- **所处区间：** {rsi.get('zone', '未知')}
- **背离状态：** {rsi.get('divergence', '未知')}
- **趋势方向：** {rsi.get('trend', '未知')}""")

            # 2.4 KDJ随机指标
            kdj = technical_data.get('kdj', {})
            sections.append(f"""### 2.4 KDJ随机指标
- **K值：** {kdj.get('k_value', 50.0):.1f}
- **D值：** {kdj.get('d_value', 50.0):.1f}
- **J值：** {kdj.get('j_value', 50.0):.1f}
- **所处区间：** {kdj.get('zone', '未知')}
- **交叉状态：** {kdj.get('cross_status', '未知')}""")

            # 2.5 成交量分析
            volume = technical_data.get('volume', {})
            sections.append(f"""### 2.5 成交量分析
- **当前成交量：** {volume.get('current_volume', 0)}件
- **成交量状态：** {volume.get('volume_status', '未知')}
- **成交量比率：** {volume.get('volume_ratio', 1.0):.2f}倍 (相对20日均量)
- **价量配合：** {volume.get('price_volume_match', '未知')}""")

            # 继续添加其他技术指标
            # 2.6 OBV能量潮指标
            obv = technical_data.get('obv', {})
            sections.append(f"""### 2.6 OBV能量潮指标
- **OBV值：** {obv.get('obv_value', 0):,}
- **OBV趋势：** {obv.get('obv_trend', '未知')}
- **价格配合度：** {obv.get('sync_status', '未知')}
- **信号类型：** {obv.get('signal_type', '未知')}""")

            # 2.7 资金流向
            money_flow = technical_data.get('money_flow', {})
            sections.append(f"""### 2.7 资金流向
- **资金流向比率：** {money_flow.get('flow_ratio', 50.0):.1f}%
- **流向状态：** {money_flow.get('flow_status', '未知')}
- **绿红区域比例：** 绿色{money_flow.get('green_ratio', 50.0):.1f}% / 红色{money_flow.get('red_ratio', 50.0):.1f}%""")

            # 2.8 布林带指标
            bollinger = technical_data.get('bollinger', {})
            sections.append(f"""### 2.8 布林带指标
- **价格位置：** {bollinger.get('price_position', 50.0):.1f}% (相对布林带)
- **所处区间：** {bollinger.get('zone', '未知')}
- **轨道状态：** {bollinger.get('band_status', '未知')}
- **突破状态：** {bollinger.get('breakthrough', '未知')}""")

            # 2.9 ATR波动率指标
            atr = technical_data.get('atr', {})
            sections.append(f"""### 2.9 ATR波动率指标
- **ATR值：** {atr.get('atr_value', 0.0):.2f}元
- **波动率：** {atr.get('volatility_pct', 0.0):.2f}%
- **波动等级：** {atr.get('volatility_level', '未知')}
- **颜色显示：** {atr.get('color_display', '未知')}
- **趋势方向：** {atr.get('trend_direction', '未知')}""")

            return "\n\n".join(sections)

        except Exception as e:
            print(f"⚠️ 技术指标格式化错误: {e}")
            return "技术指标格式化失败"

    def _format_evaluation_section(self, evaluation_data: Dict) -> str:
        """格式化综合技术评估部分"""
        try:
            buy_signals = evaluation_data.get('strong_buy_signals', {})
            sell_signals = evaluation_data.get('strong_sell_signals', {})
            watch_signals = evaluation_data.get('watch_signals', {})

            buy_count = evaluation_data.get('buy_count', 0)
            sell_count = evaluation_data.get('sell_count', 0)
            watch_count = evaluation_data.get('watch_count', 0)

            main_signal = evaluation_data.get('main_signal', '观望')
            signal_strength = evaluation_data.get('signal_strength', '中性')
            key_confirmation = evaluation_data.get('key_confirmation', {})

            # 3.1 强买入信号检查
            buy_section = f"""### 3.1 强买入信号检查 (需满足5-6个)
**基础技术条件：**
- [{self._get_checkbox(buy_signals.get('price_above_ma', {}).get('status', False))}] {buy_signals.get('price_above_ma', {}).get('description', '数据获取失败')}
- [{self._get_checkbox(buy_signals.get('macd_golden_cross', {}).get('status', False))}] {buy_signals.get('macd_golden_cross', {}).get('description', '数据获取失败')}
- [{self._get_checkbox(buy_signals.get('rsi_healthy', {}).get('status', False))}] {buy_signals.get('rsi_healthy', {}).get('description', '数据获取失败')}
- [{self._get_checkbox(buy_signals.get('kdj_golden_cross', {}).get('status', False))}] {buy_signals.get('kdj_golden_cross', {}).get('description', '数据获取失败')}

**成交量与资金确认：**
- [{self._get_checkbox(buy_signals.get('volume_increase', {}).get('status', False))}] {buy_signals.get('volume_increase', {}).get('description', '数据获取失败')}
- [{self._get_checkbox(buy_signals.get('money_flow_in', {}).get('status', False))}] {buy_signals.get('money_flow_in', {}).get('description', '数据获取失败')}
- [{self._get_checkbox(buy_signals.get('obv_sync', {}).get('status', False))}] {buy_signals.get('obv_sync', {}).get('description', '数据获取失败')}

**风险控制指标：**
- [{self._get_checkbox(buy_signals.get('bb_position_good', {}).get('status', False))}] {buy_signals.get('bb_position_good', {}).get('description', '数据获取失败')}
- [{self._get_checkbox(buy_signals.get('atr_moderate', {}).get('status', False))}] {buy_signals.get('atr_moderate', {}).get('description', '数据获取失败')}

**满足条件：** {buy_count}/9 个"""

            # 3.2 强卖出信号检查
            sell_section = f"""### 3.2 强卖出信号检查 (出现4-5个)
**基础技术条件：**
- [{self._get_checkbox(sell_signals.get('price_below_ma', {}).get('status', False))}] {sell_signals.get('price_below_ma', {}).get('description', '数据获取失败')}
- [{self._get_checkbox(sell_signals.get('macd_death_cross', {}).get('status', False))}] {sell_signals.get('macd_death_cross', {}).get('description', '数据获取失败')}
- [{self._get_checkbox(sell_signals.get('rsi_overbought', {}).get('status', False))}] {sell_signals.get('rsi_overbought', {}).get('description', '数据获取失败')}
- [{self._get_checkbox(sell_signals.get('kdj_death_cross', {}).get('status', False))}] {sell_signals.get('kdj_death_cross', {}).get('description', '数据获取失败')}

**成交量与资金警示：**
- [{self._get_checkbox(sell_signals.get('volume_fake', {}).get('status', False))}] {sell_signals.get('volume_fake', {}).get('description', '数据获取失败')}
- [{self._get_checkbox(sell_signals.get('money_flow_out', {}).get('status', False))}] {sell_signals.get('money_flow_out', {}).get('description', '数据获取失败')}
- [{self._get_checkbox(sell_signals.get('obv_divergence', {}).get('status', False))}] {sell_signals.get('obv_divergence', {}).get('description', '数据获取失败')}

**风险警示指标：**
- [{self._get_checkbox(sell_signals.get('bb_position_high', {}).get('status', False))}] {sell_signals.get('bb_position_high', {}).get('description', '数据获取失败')}
- [{self._get_checkbox(sell_signals.get('atr_high', {}).get('status', False))}] {sell_signals.get('atr_high', {}).get('description', '数据获取失败')}

**出现条件：** {sell_count}/8 个"""

            # 3.3 观望条件检查
            watch_section = f"""### 3.3 观望条件检查
- [{self._get_checkbox(watch_signals.get('signal_conflict', {}).get('status', False))}] {watch_signals.get('signal_conflict', {}).get('description', '数据获取失败')}
- [{self._get_checkbox(watch_signals.get('volume_shrink', {}).get('status', False))}] {watch_signals.get('volume_shrink', {}).get('description', '数据获取失败')}
- [{self._get_checkbox(watch_signals.get('rsi_sideways', {}).get('status', False))}] {watch_signals.get('rsi_sideways', {}).get('description', '数据获取失败')}
- [{self._get_checkbox(watch_signals.get('bb_middle_range', {}).get('status', False))}] {watch_signals.get('bb_middle_range', {}).get('description', '数据获取失败')}
- [{self._get_checkbox(watch_signals.get('atr_very_low', {}).get('status', False))}] {watch_signals.get('atr_very_low', {}).get('description', '数据获取失败')}

**出现条件：** {watch_count}/5 个"""

            # 3.4 综合技术状态
            status_section = f"""### 3.4 综合技术状态
**主导信号：** {main_signal}
**信号强度：** {signal_strength}
**关键确认指标：** {key_confirmation.get('obv_sync', '未知')}、{key_confirmation.get('volume_match', '未知')}"""

            return f"{buy_section}\n\n{sell_section}\n\n{watch_section}\n\n{status_section}"

        except Exception as e:
            print(f"⚠️ 评估部分格式化错误: {e}")
            return "评估部分格式化失败"

    def _format_risk_assessment_section(self, risk_data: Dict) -> str:
        """格式化风险评估部分"""
        try:
            volatility_analysis = risk_data.get('volatility_analysis', {})
            stop_loss_levels = risk_data.get('stop_loss_levels', {})
            key_levels = risk_data.get('key_levels', {})

            # 4.1 波动率分析
            volatility_section = f"""### 4.1 波动率分析
- **当前波动率：** {volatility_analysis.get('volatility_pct', 0.0):.2f}%
- **风险等级：** {volatility_analysis.get('risk_level', '未知')}
- **波动率趋势：** {volatility_analysis.get('trend_direction', '未知')}"""

            # 4.2 止损参考
            stop_loss_section = f"""### 4.2 止损参考
- **保守止损：** ¥{stop_loss_levels.get('long_positions', {}).get('conservative', 0.0):,.2f} (当前价格 - ATR×2)
- **标准止损：** ¥{stop_loss_levels.get('long_positions', {}).get('standard', 0.0):,.2f} (当前价格 - ATR×2.5)
- **宽松止损：** ¥{stop_loss_levels.get('long_positions', {}).get('loose', 0.0):,.2f} (当前价格 - ATR×3)"""

            # 4.3 关键价位
            key_levels_section = f"""### 4.3 关键价位
- **上方阻力：** ¥{key_levels.get('upper_resistance', 0.0):,.2f} (距离{key_levels.get('resistance_distance_pct', 0.0):.1f}%)
- **下方支撑：** ¥{key_levels.get('lower_support', 0.0):,.2f} (距离{key_levels.get('support_distance_pct', 0.0):.1f}%)
- **关键突破位：** ¥{key_levels.get('key_breakthrough', 0.0):,.2f}
- **关键跌破位：** ¥{key_levels.get('key_breakdown', 0.0):,.2f}"""

            return f"{volatility_section}\n\n{stop_loss_section}\n\n{key_levels_section}"

        except Exception as e:
            print(f"⚠️ 风险评估格式化错误: {e}")
            return "风险评估格式化失败"

    def _format_technical_summary_section(self, summary_data: Dict) -> str:
        """格式化技术面总结部分"""
        try:
            # 由于summary_data在当前实现中为空，我们基于其他数据生成总结
            current_signals = self.analysis_results.get('current_signals', {})
            overall_signal = current_signals.get('overall_signal', 'NEUTRAL')

            # 5.1 当前市场状态
            if overall_signal == 'BULLISH':
                market_state = "市场处于明显的上升趋势中，技术指标显示多头力量占优。价格站稳主要均线之上，各项指标配合良好，显示资金持续流入。当前为正常的上涨行情。"
            elif overall_signal == 'BEARISH':
                market_state = "市场处于下降趋势中，技术指标显示空头力量占优。价格跌破关键均线，各项指标显示卖压较重，资金流出明显。当前为下跌行情。"
            else:
                market_state = "市场处于震荡整理状态，技术指标信号不明确。价格在关键位置附近波动，多空力量相对均衡，等待方向选择。"

            market_section = f"""### 5.1 当前市场状态
{market_state}"""

            # 5.2 关键观察点
            observation_section = f"""### 5.2 关键观察点
1. **价格关键位：** 关注支撑阻力位突破情况
2. **成交量变化：** 需要关注成交量是否配合价格变化
3. **指标背离：** 密切观察技术指标背离情况
4. **波动率变化：** 关注市场波动率的变化趋势"""

            # 5.3 技术面风险提示
            risk_tips_section = f"""### 5.3 技术面风险提示
- 注意关键价位的突破有效性，避免虚假突破
- 关注成交量与价格的配合情况，防范量价背离
- 密切观察技术指标的背离信号，及时调整策略"""

            return f"{market_section}\n\n{observation_section}\n\n{risk_tips_section}"

        except Exception as e:
            print(f"⚠️ 技术面总结格式化错误: {e}")
            return "技术面总结格式化失败"

    def _get_checkbox(self, status: bool) -> str:
        """获取复选框标记"""
        return "x" if status else " "
