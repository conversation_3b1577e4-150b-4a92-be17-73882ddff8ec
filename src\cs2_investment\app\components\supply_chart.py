"""
供需分析图表组件

提供饰品供需数据的可视化图表。
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, Any
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.app.utils.data_formatter import format_number, format_percentage


def render_supply_demand_chart(snapshot: Dict[str, Any]) -> None:
    """渲染供需分析图表"""
    if not snapshot:
        st.info("📊 暂无供需数据")
        return

    st.subheader("⚖️ 供需分析")

    # 添加数据说明
    st.caption("💡 在售数量反映市场供应情况，热度数据体现市场关注度。供需平衡是价格稳定的重要因素。")

    # 创建两列布局
    col1, col2 = st.columns(2)

    with col1:
        render_supply_trend_chart(snapshot)

    with col2:
        render_popularity_trend_chart(snapshot)


def render_supply_trend_chart(snapshot: Dict[str, Any]) -> None:
    """渲染在售数量变化图"""
    st.write("**📦 在售数量变化**")
    
    # 准备在售数量数据
    supply_data = [
        {
            "period": "当前", 
            "sell_nums": snapshot.get('sell_nums', 0),
            "change_rate": 0
        },
        {
            "period": "1天前", 
            "sell_nums": snapshot.get('sell_nums_1d', 0),
            "change_rate": snapshot.get('sell_nums_1d_rate', 0)
        },
        {
            "period": "7天前", 
            "sell_nums": snapshot.get('sell_nums_7d', 0),
            "change_rate": snapshot.get('sell_nums_7d_rate', 0)
        },
        {
            "period": "1月前", 
            "sell_nums": snapshot.get('sell_nums_1m', 0),
            "change_rate": snapshot.get('sell_nums_1m_rate', 0)
        }
    ]
    
    # 过滤掉无数据的项
    supply_data = [item for item in supply_data if item["sell_nums"] > 0]
    
    if not supply_data:
        st.info("暂无在售数量数据")
        return
    
    df = pd.DataFrame(supply_data)
    
    # 创建双轴图表
    fig = make_subplots(
        specs=[[{"secondary_y": True}]],
        subplot_titles=["在售数量与变化率"]
    )
    
    # 在售数量柱状图
    fig.add_trace(
        go.Bar(
            x=df['period'],
            y=df['sell_nums'],
            name='在售数量',
            marker_color='lightblue',
            hovertemplate='<b>%{x}</b><br>在售数量: %{y}个<extra></extra>'
        ),
        secondary_y=False
    )
    
    # 变化率线图
    fig.add_trace(
        go.Scatter(
            x=df['period'],
            y=df['change_rate'],
            mode='lines+markers',
            name='变化率',
            line=dict(color='red', width=2),
            marker=dict(size=6),
            hovertemplate='<b>%{x}</b><br>变化率: %{y:.1f}%<extra></extra>'
        ),
        secondary_y=True
    )
    
    # 设置y轴标题
    fig.update_yaxes(title_text="在售数量", secondary_y=False)
    fig.update_yaxes(title_text="变化率 (%)", secondary_y=True)
    
    fig.update_layout(
        height=300,
        margin=dict(l=20, r=20, t=40, b=20),
        hovermode='x unified'
    )
    
    st.plotly_chart(fig, use_container_width=True)


def render_popularity_trend_chart(snapshot: Dict[str, Any]) -> None:
    """渲染热度排名变化图"""
    st.write("**🔥 热度分析**")
    
    hot_rank = snapshot.get('hot_rank')
    hot_count = snapshot.get('hot_count', 0)
    hot_keep_days = snapshot.get('hot_keep_days', 0)
    hot_rank_change = snapshot.get('hot_rank_change', 0)
    
    if not hot_rank and hot_count == 0:
        st.info("暂无热度数据")
        return
    
    # 创建热度指标图表
    fig = go.Figure()
    
    # 热度计数
    if hot_count > 0:
        fig.add_trace(go.Indicator(
            mode="gauge+number",
            value=hot_count,
            domain={'x': [0, 1], 'y': [0.5, 1]},
            title={'text': "热度计数"},
            gauge={
                'axis': {'range': [None, max(hot_count * 1.2, 10000)]},
                'bar': {'color': "orange"},
                'steps': [
                    {'range': [0, hot_count * 0.5], 'color': "lightgray"},
                    {'range': [hot_count * 0.5, hot_count], 'color': "gray"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': hot_count * 0.9
                }
            }
        ))
    
    # 热度排名（如果有）
    if hot_rank:
        # 排名越小越好，所以用倒数来显示
        rank_score = max(0, 1000 - hot_rank) if hot_rank <= 1000 else 0
        
        fig.add_trace(go.Indicator(
            mode="gauge+number+delta",
            value=rank_score,
            domain={'x': [0, 1], 'y': [0, 0.5]},
            title={'text': f"热度排名 (#{hot_rank})"},
            delta={'reference': rank_score + hot_rank_change if hot_rank_change else rank_score},
            gauge={
                'axis': {'range': [None, 1000]},
                'bar': {'color': "red"},
                'steps': [
                    {'range': [0, 300], 'color': "lightgray"},
                    {'range': [300, 700], 'color': "gray"},
                    {'range': [700, 1000], 'color': "darkgray"}
                ],
                'threshold': {
                    'line': {'color': "green", 'width': 4},
                    'thickness': 0.75,
                    'value': 800
                }
            }
        ))
    
    fig.update_layout(
        height=300,
        margin=dict(l=20, r=20, t=20, b=20)
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 显示热度保持天数
    if hot_keep_days > 0:
        st.info(f"🔥 热度已保持 {hot_keep_days} 天")


def render_supply_demand_summary(snapshot: Dict[str, Any]) -> None:
    """渲染供需摘要指标"""
    st.subheader("📊 供需摘要")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        sell_nums = snapshot.get('sell_nums', 0)
        sell_nums_7d_rate = snapshot.get('sell_nums_7d_rate', 0)
        
        st.metric(
            "当前在售",
            format_number(sell_nums, "个"),
            delta=f"{sell_nums_7d_rate:+.1f}%" if sell_nums_7d_rate != 0 else None
        )
    
    with col2:
        survive_num = snapshot.get('survive_num', 0)
        
        # 计算流动性比率
        liquidity_ratio = 0
        if survive_num > 0:
            liquidity_ratio = (sell_nums / survive_num) * 100
        
        st.metric(
            "存世量",
            format_number(survive_num, "个"),
            delta=f"流动性: {liquidity_ratio:.1f}%"
        )
    
    with col3:
        hot_rank = snapshot.get('hot_rank')
        hot_rank_change = snapshot.get('hot_rank_change', 0)
        
        if hot_rank:
            st.metric(
                "热度排名",
                f"#{hot_rank}",
                delta=f"{hot_rank_change:+d}" if hot_rank_change != 0 else None,
                delta_color="inverse"  # 排名下降是好事
            )
        else:
            st.metric("热度排名", "未上榜")
    
    with col4:
        # 供需平衡度评估（修正评估逻辑）
        if sell_nums > 0 and survive_num > 0:
            # 修正供应压力评估：考虑流动性比率和绝对数量
            if liquidity_ratio > 15 or sell_nums > 1000:
                supply_pressure = "高"
                pressure_color = "🔴"
                pressure_desc = "供应充足，价格承压"
            elif liquidity_ratio > 8 or sell_nums > 500:
                supply_pressure = "中"
                pressure_color = "🟡"
                pressure_desc = "供应适中，价格相对稳定"
            else:
                supply_pressure = "低"
                pressure_color = "🟢"
                pressure_desc = "供应紧张，价格支撑较强"

            st.metric(
                "供应压力",
                f"{pressure_color} {supply_pressure}",
                help=pressure_desc
            )
        else:
            st.metric("供应压力", "⚪ 数据不足", help="缺少存世量或在售数量数据")
