"""
智能投资分析调度器

负责监听排行榜数据更新，按热度顺序进行常规分析，运行投资筛选算法，并保存结果。
采用asyncio异步模式，与现有SmartTaskScheduler保持架构一致性。
"""

import asyncio
import sys
from datetime import datetime, timedelta, date
from typing import List, Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass, asdict

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.market_snapshot_dao import MarketSnapshotDAO
from src.cs2_investment.dao.item_dao import ItemDAO
from src.cs2_investment.algorithms.algorithm_manager import AlgorithmManager
from src.cs2_investment.api.services.task_engine import task_engine, TaskInfo, TaskType
from src.cs2_investment.dao.analysis_log_dao import analysis_log_dao

# 使用统一日志组件
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)


@dataclass
class InvestmentAnalysisConfig:
    """投资分析调度器配置"""

    # 基本配置
    analysis_interval_hours: int = 6        # 分析间隔时间（小时）
    batch_size: int = 50                   # 每批处理的饰品数量
    max_ranking_items: int = 500           # 热度排行榜最大处理数量
    algorithm_limit_per_type: int = 30     # 每种算法的结果数量限制

    # 重试和超时配置
    max_retry_count: int = 3               # 最大重试次数
    task_timeout_seconds: int = 300        # 单个任务超时时间（秒）
    batch_interval_seconds: int = 15       # 饰品间隔时间（秒，避免IP封禁）
    batch_group_interval_seconds: int = 8  # 批次组间隔时间（秒）

    # 数据检查配置
    data_check_interval_minutes: int = 30  # 数据检查间隔（分钟）
    time_tolerance_seconds: int = 60       # 时间容差（秒）

    # 性能配置
    enable_detailed_logging: bool = True   # 启用详细日志
    enable_performance_monitoring: bool = True  # 启用性能监控
    max_concurrent_tasks: int = 5          # 最大并发任务数

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'InvestmentAnalysisConfig':
        """从字典创建配置"""
        return cls(**data)


class InvestmentAnalysisScheduler:
    """智能投资分析调度器"""

    def __init__(self, config: Optional[InvestmentAnalysisConfig] = None):
        """初始化调度器"""
        self.is_running = False
        self.main_task = None

        # 配置参数
        self.config = config or InvestmentAnalysisConfig()

        # 为了向后兼容，保留原有属性
        self.analysis_interval_hours = self.config.analysis_interval_hours
        self.batch_size = self.config.batch_size
        self.max_ranking_items = self.config.max_ranking_items
        self.max_retry_count = self.config.max_retry_count
        self.algorithm_limit_per_type = self.config.algorithm_limit_per_type
        
        # 状态记录
        self.last_processed_time = None
        self.current_batch = 0
        self.total_batches = 0
        self.start_time = None
        self.current_cycle_start_time = None

        # 增强的统计信息
        self.stats = {
            # 基本统计
            'total_cycles': 0,
            'successful_cycles': 0,
            'failed_cycles': 0,
            'total_items_analyzed': 0,
            'total_algorithms_run': 0,

            # 时间统计
            'last_cycle_time': None,
            'last_cycle_duration': 0.0,
            'average_cycle_duration': 0.0,
            'total_runtime_seconds': 0.0,
            'uptime_percentage': 0.0,

            # 批次统计
            'last_batch_size': 0,
            'last_batch_rank_range': '',
            'last_batch_duration': 0.0,
            'average_batch_duration': 0.0,
            'total_batches_processed': 0,

            # 算法统计
            'last_screening_stats': {},
            'last_screening_duration': 0.0,
            'last_screening_total_results': 0,
            'average_screening_duration': 0.0,
            'total_screening_results': 0,

            # 错误统计
            'last_error': None,
            'last_error_time': None,
            'total_errors': 0,
            'error_rate': 0.0,

            # 性能统计
            'items_per_hour': 0.0,
            'cycles_per_day': 0.0,
            'success_rate': 0.0,

            # 数据统计
            'last_data_check_time': None,
            'data_updates_detected': 0,
            'data_check_failures': 0
        }
        
        # 依赖组件
        self.market_dao = MarketSnapshotDAO()
        self.item_dao = ItemDAO()
        self.algorithm_manager = AlgorithmManager()
        self.task_engine = task_engine
        
        logger.info("🚀 智能投资分析调度器初始化完成")
    
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("投资分析调度器已在运行中")
            return
        
        self.is_running = True
        logger.info("🚀 启动智能投资分析调度器")
        
        # 启动主循环
        self.main_task = asyncio.create_task(self._investment_analysis_loop())
        
        logger.info("✅ 智能投资分析调度器启动完成")
        
        # 等待任务完成
        try:
            await self.main_task
        except asyncio.CancelledError:
            logger.info("投资分析调度器被取消")
    
    async def stop(self):
        """停止调度器"""
        if not self.is_running:
            logger.warning("投资分析调度器未在运行")
            return
        
        logger.info("🛑 停止智能投资分析调度器")
        self.is_running = False
        
        # 取消主任务
        if self.main_task:
            self.main_task.cancel()
        
        # 等待任务完全停止
        try:
            if self.main_task:
                await self.main_task
        except asyncio.CancelledError:
            pass
        
        logger.info("✅ 智能投资分析调度器已停止")
    
    async def _investment_analysis_loop(self):
        """投资分析主循环"""
        logger.info("📊 投资分析主循环启动")
        
        while self.is_running:
            try:
                # 记录周期开始
                self._record_cycle_start()
                cycle_start_time = self.current_cycle_start_time
                logger.info(f"🔄 开始新的投资分析周期: {cycle_start_time}")

                # 检查排行榜数据是否更新
                data_updated = await self._check_ranking_data_update()
                self.stats['last_data_check_time'] = datetime.now()

                if data_updated:
                    logger.info("📈 检测到排行榜数据更新，开始分析流程")
                    self.stats['data_updates_detected'] += 1

                    # 运行完整的投资分析周期
                    success = await self._run_investment_analysis_cycle()

                    # 记录周期结束
                    self._record_cycle_end(success)

                    if success:
                        logger.info("✅ 投资分析周期完成")
                    else:
                        logger.error("❌ 投资分析周期失败")
                else:
                    logger.info("📊 排行榜数据无更新，跳过本次分析")
                    # 即使跳过也记录为成功的周期
                    self._record_cycle_end(True)
                
                # 等待下次分析
                logger.info(f"⏰ 等待 {self.analysis_interval_hours} 小时后进行下次分析")
                await asyncio.sleep(self.analysis_interval_hours * 3600)
                
            except asyncio.CancelledError:
                logger.info("投资分析循环被取消")
                break
            except Exception as e:
                error_msg = f"投资分析循环异常: {e}"
                logger.error(error_msg)

                # 记录错误和周期结束
                self._record_error(error_msg)
                self._record_cycle_end(False)

                # 异常后等待较短时间再重试
                await asyncio.sleep(1800)  # 30分钟
        
        logger.info("📊 投资分析主循环结束")
    
    async def _check_ranking_data_update(self) -> bool:
        """检查排行榜数据是否更新"""
        try:
            # 获取最新快照时间（使用更安全的方式）
            from src.cs2_investment.config.database import get_db_session
            from src.cs2_investment.models.market_snapshot import MarketSnapshot
            from sqlalchemy import func, and_

            logger.debug("🔍 开始检查排行榜数据更新...")

            with get_db_session() as session:
                # 获取当天的日期范围
                today = datetime.now().date()
                today_start = datetime.combine(today, datetime.min.time())
                today_end = datetime.combine(today, datetime.max.time())

                # 查询当天最新快照时间
                latest_time = session.query(func.max(MarketSnapshot.snapshot_time))\
                    .filter(
                        and_(
                            MarketSnapshot.snapshot_time >= today_start,
                            MarketSnapshot.snapshot_time <= today_end
                        )
                    ).scalar()

                if not latest_time:
                    logger.warning(f"⚠️ 没有找到当天({today})的快照数据")
                    return False

                # 检查当天最新数据的有效性（确保有热度排名数据）
                valid_data_count = session.query(func.count(MarketSnapshot.item_id))\
                    .filter(
                        and_(
                            MarketSnapshot.snapshot_time >= latest_time - timedelta(hours=1),
                            MarketSnapshot.snapshot_time <= today_end,
                            MarketSnapshot.hot_rank.isnot(None),
                            MarketSnapshot.hot_rank > 0
                        )
                    ).scalar()

                if valid_data_count == 0:
                    logger.warning(f"⚠️ 当天最新快照时间 {latest_time} 没有有效的热度排名数据")
                    return False

                logger.debug(f"📊 当天最新快照时间: {latest_time}, 有效热度数据: {valid_data_count} 条")

                # 判断是否需要处理当天数据
                if self.last_processed_time is None:
                    logger.info(f"🆕 首次运行，发现当天数据: 最新时间 {latest_time}")
                    return True

                # 检查当天数据是否有更新（允许一定的时间容差，避免微小时间差异）
                time_diff = (latest_time - self.last_processed_time).total_seconds()

                if time_diff > 60:  # 超过1分钟的差异才认为是新数据
                    logger.info(f"📈 发现当天数据更新: 最新时间 {latest_time}, 上次处理时间 {self.last_processed_time}, 时间差 {time_diff:.0f} 秒")
                    return True
                else:
                    logger.debug(f"📊 当天数据无更新: 时间差仅 {time_diff:.0f} 秒")
                    return False

        except Exception as e:
            logger.error(f"❌ 检查排行榜数据更新失败: {e}")
            self.stats['data_check_failures'] += 1
            # 在异常情况下，为了安全起见，返回False避免重复处理
            return False

    def update_last_processed_time(self, processed_time: Optional[datetime] = None):
        """更新上次处理时间"""
        if processed_time is None:
            processed_time = datetime.now()

        old_time = self.last_processed_time
        self.last_processed_time = processed_time

        logger.info(f"⏰ 更新处理时间: {old_time} → {processed_time}")

    def reset_processed_time(self):
        """重置处理时间（强制下次检查时认为有更新）"""
        old_time = self.last_processed_time
        self.last_processed_time = None

        logger.info(f"🔄 重置处理时间: {old_time} → None（将强制处理下次数据）")
    
    async def _run_investment_analysis_cycle(self) -> bool:
        """运行完整的投资分析周期"""
        try:
            # 1. 获取热度排行榜
            ranking_items = await self._get_ranking_items()
            if not ranking_items:
                logger.warning("没有获取到热度排行榜数据")
                return False
            
            logger.info(f"📊 获取到 {len(ranking_items)} 个热度排行榜饰品")
            
            # 2. 批量运行常规分析
            logger.info("🔄 开始批量常规分析阶段...")
            analysis_success = False
            try:
                analysis_success = await self._run_regular_analysis_batch(ranking_items)
                if analysis_success:
                    logger.info("✅ 常规分析批量处理完成")
                else:
                    logger.warning("⚠️ 常规分析批量处理部分失败，但继续执行投资筛选")
            except Exception as analysis_error:
                logger.error(f"❌ 常规分析批量处理异常: {analysis_error}")
                logger.info("🔄 尽管常规分析失败，仍尝试运行投资筛选算法")

            # 3. 运行投资筛选算法（即使常规分析失败也要尝试）
            logger.info("🧠 开始投资筛选算法阶段...")
            screening_success = False
            try:
                screening_success = await self._run_investment_screening()
                if screening_success:
                    logger.info("✅ 投资筛选算法运行完成")
                else:
                    logger.warning("⚠️ 投资筛选算法运行失败")
            except Exception as screening_error:
                logger.error(f"❌ 投资筛选算法异常: {screening_error}")
                import traceback
                logger.debug(f"投资筛选异常详情: {traceback.format_exc()}")

            # 4. 评估整体成功率
            overall_success = analysis_success or screening_success
            if overall_success:
                logger.info("✅ 投资分析周期整体成功（至少一个阶段成功）")
            else:
                logger.error("❌ 投资分析周期整体失败（所有阶段都失败）")
            
            # 5. 更新处理时间（只有在有任何成功时才更新）
            if overall_success:
                self.last_processed_time = datetime.now()

            return overall_success
            
        except Exception as e:
            logger.error(f"投资分析周期执行失败: {e}")
            return False
    
    async def _get_ranking_items(self) -> List:
        """获取当天所有排行榜饰品列表（去重）"""
        try:
            today = datetime.now().date()

            # 获取所有排行榜的饰品
            all_ranking_items = []

            # 1. 热度排行榜饰品
            hot_items = self.market_dao.get_hot_ranking_items_by_date(today, limit=self.max_ranking_items)
            all_ranking_items.extend(hot_items)
            logger.info(f"获取当天热度排行榜饰品: {len(hot_items)} 个")

            # 2. 涨幅榜饰品（多个时间周期）
            for period in ['1d', '3d', '7d', '1m']:
                gainers = self.market_dao.get_top_gainers_by_date(today, period=period, limit=200)
                all_ranking_items.extend(gainers)
                logger.info(f"获取当天{period}涨幅榜饰品: {len(gainers)} 个")

            # 3. 跌幅榜饰品（多个时间周期）
            for period in ['1d', '3d', '7d', '1m']:
                losers = self.market_dao.get_top_losers_by_date(today, period=period, limit=200)
                all_ranking_items.extend(losers)
                logger.info(f"获取当天{period}跌幅榜饰品: {len(losers)} 个")

            # 4. 高成交量饰品（多个时间周期）
            for period in ['1d', '3d', '7d', '1m']:
                volume_items = self.market_dao.get_high_volume_items_by_date(today, period=period, limit=200)
                all_ranking_items.extend(volume_items)
                logger.info(f"获取当天{period}高成交量饰品: {len(volume_items)} 个")

            # 5. 去重处理：基于item_id去重
            unique_items = {}
            for item in all_ranking_items:
                if item.item_id not in unique_items:
                    unique_items[item.item_id] = item

            unique_ranking_items = list(unique_items.values())
            logger.info(f"去重前总数: {len(all_ranking_items)}, 去重后: {len(unique_ranking_items)}")

            # 6. 过滤当天已分析的饰品
            unanalyzed_items = await self._filter_unanalyzed_items(unique_ranking_items, today)

            logger.info(f"成功获取当天({today})所有排行榜饰品: 去重后 {len(unique_ranking_items)} 个, 未分析 {len(unanalyzed_items)} 个")
            return unanalyzed_items

        except Exception as e:
            logger.error(f"获取当天所有排行榜饰品失败: {e}")
            return []

    async def _filter_unanalyzed_items(self, ranking_items: List, target_date: date) -> List:
        """过滤当天已分析的饰品，只返回未分析的饰品"""
        try:
            from src.cs2_investment.dao.analysis_log_dao import analysis_log_dao

            if not ranking_items:
                return []

            # 获取所有饰品ID
            item_ids = [item.item_id for item in ranking_items]

            # 查询当天已分析的饰品ID
            analyzed_item_ids = analysis_log_dao.get_analyzed_item_ids_by_date(target_date, item_ids)
            analyzed_set = set(analyzed_item_ids)

            # 过滤出未分析的饰品
            unanalyzed_items = [item for item in ranking_items if item.item_id not in analyzed_set]

            logger.info(f"📊 当天分析状态: 总饰品 {len(ranking_items)} 个, 已分析 {len(analyzed_set)} 个, 待分析 {len(unanalyzed_items)} 个")

            if analyzed_set:
                logger.info(f"⏭️ 跳过当天已分析的 {len(analyzed_set)} 个饰品")

            return unanalyzed_items

        except Exception as e:
            logger.error(f"过滤已分析饰品失败: {e}")
            # 如果过滤失败，返回原始列表，避免丢失数据
            return ranking_items

    async def _run_regular_analysis_batch(self, ranking_items: List) -> bool:
        """批量运行常规分析"""
        try:
            if not ranking_items:
                logger.warning("⚠️ 没有饰品需要分析")
                return True

            # 按热度排名排序（确保按排名顺序处理）
            sorted_items = sorted(ranking_items, key=lambda x: x.hot_rank if x.hot_rank else 999999)

            # 计算批次数量
            self.total_batches = (len(sorted_items) + self.batch_size - 1) // self.batch_size
            logger.info(f"📦 开始批量分析: {len(sorted_items)} 个饰品, {self.total_batches} 个批次")

            # 显示排名范围
            first_rank = sorted_items[0].hot_rank if sorted_items[0].hot_rank else "未知"
            last_rank = sorted_items[-1].hot_rank if sorted_items[-1].hot_rank else "未知"
            logger.info(f"🏆 热度排名范围: #{first_rank} - #{last_rank}")

            successful_items = 0
            failed_items = 0
            start_time = datetime.now()

            # 分批处理
            for batch_idx in range(self.total_batches):
                if not self.is_running:
                    logger.info("🛑 调度器已停止，中断批量分析")
                    break

                self.current_batch = batch_idx + 1
                start_idx = batch_idx * self.batch_size
                end_idx = min(start_idx + self.batch_size, len(sorted_items))
                batch_items = sorted_items[start_idx:end_idx]

                # 显示当前批次的排名范围
                batch_first_rank = batch_items[0].hot_rank if batch_items[0].hot_rank else "未知"
                batch_last_rank = batch_items[-1].hot_rank if batch_items[-1].hot_rank else "未知"

                logger.info(f"📋 处理第 {self.current_batch}/{self.total_batches} 批次: {len(batch_items)} 个饰品 (排名 #{batch_first_rank}-#{batch_last_rank})")

                # 串行处理当前批次（避免IP封禁）
                batch_start_time = datetime.now()
                batch_success, batch_failed = await self._process_analysis_batch_serial(batch_items)
                batch_duration = (datetime.now() - batch_start_time).total_seconds()

                successful_items += batch_success
                failed_items += batch_failed

                logger.info(f"⏱️ 批次 {self.current_batch} 完成: 耗时 {batch_duration:.1f}秒, 成功率 {batch_success}/{len(batch_items)}")

                # 批次组间隔，避免过载
                if batch_idx < self.total_batches - 1:
                    await asyncio.sleep(self.config.batch_group_interval_seconds)

            # 更新统计信息
            total_duration = (datetime.now() - start_time).total_seconds()
            self.stats['total_items_analyzed'] += successful_items
            self.stats['last_batch_duration'] = total_duration

            success_rate = (successful_items / len(sorted_items)) * 100 if sorted_items else 0
            logger.info(f"✅ 批量分析完成: 成功 {successful_items}, 失败 {failed_items}, 成功率 {success_rate:.1f}%, 总耗时 {total_duration:.1f}秒")

            return successful_items > 0

        except Exception as e:
            logger.error(f"❌ 批量分析失败: {e}")
            return False

    async def _process_analysis_batch(self, batch_items: List) -> tuple:
        """处理单个分析批次"""
        successful_count = 0
        failed_count = 0

        # 为批次中的每个饰品创建分析任务
        submitted_tasks = []

        logger.info(f"🔄 开始处理批次: {len(batch_items)} 个饰品")

        for snapshot in batch_items:
            try:
                # 构造SteamDT URL
                item_url = self._construct_steamdt_url(snapshot.item_id)
                if not item_url:
                    logger.warning(f"⚠️ 无法构造URL: {snapshot.item_id}")
                    failed_count += 1
                    continue

                # 获取饰品名称（用于更好的日志记录）
                item_name = self._get_item_name(snapshot.item_id)

                # 创建分析日志记录
                log = analysis_log_dao.create_analysis_log(
                    item_id=snapshot.item_id,
                    item_name=item_name,
                    analysis_type='regular',
                    scheduled_date=datetime.now()
                )

                # 创建任务信息
                task_info = TaskInfo(
                    task_id=log['id'],
                    item_id=snapshot.item_id,
                    item_name=item_name,
                    item_url=item_url,
                    analysis_type=TaskType.REGULAR
                )

                # 提交任务到执行引擎
                await self.task_engine.submit_task(task_info)
                submitted_tasks.append({
                    'task_info': task_info,
                    'hot_rank': snapshot.hot_rank,
                    'submit_time': datetime.now()
                })

                logger.debug(f"📋 任务已提交: {snapshot.item_id} (热度排名: {snapshot.hot_rank})")

            except Exception as e:
                logger.error(f"❌ 创建分析任务失败: {snapshot.item_id}, 错误: {e}")
                failed_count += 1

        successful_count = len(submitted_tasks)
        logger.info(f"📤 批次任务提交完成: 提交 {successful_count} 个任务, 失败 {failed_count} 个")

        # 等待任务开始执行（给TaskEngine一些时间处理）
        if submitted_tasks:
            logger.info(f"⏳ 等待任务开始执行...")
            await asyncio.sleep(15)  # 减少等待时间，提高效率

            # 记录任务提交统计
            self._record_batch_stats(submitted_tasks)

        return successful_count, failed_count

    async def _process_analysis_batch_serial(self, batch_items: List) -> tuple:
        """串行处理单个分析批次（避免IP封禁）"""
        successful_count = 0
        failed_count = 0

        logger.info(f"🔄 开始串行处理批次: {len(batch_items)} 个饰品")
        logger.info(f"⚠️ 注意: 为避免IP封禁，将逐个处理饰品，每个间隔 {self.config.batch_interval_seconds} 秒")

        for idx, snapshot in enumerate(batch_items):
            try:
                # 检查调度器是否仍在运行
                if not self.is_running:
                    logger.info("🛑 调度器已停止，中断批次处理")
                    break

                # 获取饰品名称
                item_name = self._get_item_name(snapshot.item_id)

                logger.info(f"📋 处理饰品 {idx+1}/{len(batch_items)}: {item_name} (排名: #{snapshot.hot_rank})")

                # 构造SteamDT URL
                item_url = self._construct_steamdt_url(snapshot.item_id)
                if not item_url:
                    logger.warning(f"⚠️ 无法构造URL: {snapshot.item_id}")
                    failed_count += 1
                    continue

                # 创建分析日志记录
                log = analysis_log_dao.create_analysis_log(
                    item_id=snapshot.item_id,
                    item_name=item_name,
                    analysis_type='regular',
                    scheduled_date=datetime.now()
                )

                # 创建任务信息
                task_info = TaskInfo(
                    task_id=log['id'],
                    item_id=snapshot.item_id,
                    item_name=item_name,
                    item_url=item_url,
                    analysis_type=TaskType.REGULAR
                )

                # 串行提交并等待任务完成
                start_time = datetime.now()
                try:
                    success = await self._submit_and_wait_task(task_info)
                    duration = (datetime.now() - start_time).total_seconds()

                    if success:
                        successful_count += 1
                        logger.info(f"✅ 饰品分析完成: {item_name} (耗时: {duration:.1f}秒)")
                    else:
                        failed_count += 1
                        logger.warning(f"❌ 饰品分析失败: {item_name} (可能超时或任务执行失败)")

                except Exception as task_error:
                    failed_count += 1
                    duration = (datetime.now() - start_time).total_seconds()
                    logger.error(f"❌ 饰品任务执行异常: {item_name} (耗时: {duration:.1f}秒), 错误: {task_error}")
                    import traceback
                    logger.debug(f"任务异常详情: {traceback.format_exc()}")

                # 添加间隔，避免请求过于频繁导致IP封禁
                if idx < len(batch_items) - 1:  # 不是最后一个
                    logger.info(f"⏳ 等待 {self.config.batch_interval_seconds} 秒后处理下一个饰品...")
                    await asyncio.sleep(self.config.batch_interval_seconds)

            except Exception as e:
                logger.error(f"❌ 处理饰品失败: {snapshot.item_id}, 错误: {e}")
                import traceback
                logger.debug(f"处理饰品异常详情: {traceback.format_exc()}")
                failed_count += 1

        logger.info(f"📤 批次串行处理完成: 成功 {successful_count} 个, 失败 {failed_count} 个")
        return successful_count, failed_count

    async def _submit_and_wait_task(self, task_info: TaskInfo) -> bool:
        """提交任务并等待完成（串行处理）"""
        try:
            # 提交任务到执行引擎
            await self.task_engine.submit_task(task_info)
            logger.debug(f"📤 任务已提交到队列: {task_info.task_id} - {task_info.item_name}")

            # 等待任务完成（使用超时机制）
            timeout = self.config.task_timeout_seconds
            start_time = datetime.now()
            check_interval = 2  # 每2秒检查一次

            logger.debug(f"⏳ 开始等待任务完成，超时时间: {timeout}秒")

            while (datetime.now() - start_time).total_seconds() < timeout:
                # 检查调度器是否仍在运行
                if not self.is_running:
                    logger.info("🛑 调度器已停止，中断任务等待")
                    return False

                # 检查任务是否完成（通过查询分析日志表）
                try:
                    task_completed = self._check_task_completion(task_info.task_id)
                    if task_completed:
                        duration = (datetime.now() - start_time).total_seconds()
                        logger.debug(f"✅ 任务完成确认: {task_info.item_name} (等待时间: {duration:.1f}秒)")
                        return True
                except Exception as check_error:
                    logger.debug(f"检查任务状态时出错: {check_error}")

                # 等待一段时间后再次检查
                await asyncio.sleep(check_interval)

            # 超时处理
            duration = (datetime.now() - start_time).total_seconds()
            logger.warning(f"⏰ 任务等待超时: {task_info.item_name} (等待时间: {duration:.1f}秒)")
            return False

        except Exception as e:
            logger.error(f"❌ 任务提交或等待失败: {task_info.item_name}, 错误: {e}")
            import traceback
            logger.debug(f"详细错误: {traceback.format_exc()}")
            return False

    def _check_task_completion(self, task_id: int) -> bool:
        """检查任务是否完成"""
        try:
            from src.cs2_investment.dao.analysis_log_dao import analysis_log_dao

            # 查询分析日志状态
            log = analysis_log_dao.get_analysis_log_by_id(task_id)
            if log and hasattr(log, 'status'):
                # 如果状态是completed或success，认为任务完成
                return log.status in ['completed', 'success', 'finished']

            # 如果没有状态字段，检查是否有对应的分析结果
            from src.cs2_investment.dao.analysis_result_dao import AnalysisResultDAO
            analysis_dao = AnalysisResultDAO()

            # 通过task_id查找对应的item_id，然后检查是否有分析结果
            if log and hasattr(log, 'item_id'):
                recent_results = analysis_dao.get_results_by_item_id(log.item_id, limit=1)
                if recent_results:
                    # 检查最近的分析结果是否是在任务提交之后创建的
                    latest_result = recent_results[0]
                    if hasattr(latest_result, 'created_at') and hasattr(log, 'created_at'):
                        return latest_result.created_at >= log.created_at
                    else:
                        # 如果没有时间戳，简单认为有结果就是完成了
                        return True

            return False

        except Exception as e:
            logger.debug(f"检查任务完成状态失败: {task_id}, 错误: {e}")
            return False

    def _get_item_name(self, item_id: str) -> str:
        """获取饰品名称"""
        try:
            from src.cs2_investment.config.database import get_db_session
            from src.cs2_investment.models.item import Item

            with get_db_session() as session:
                item = session.query(Item).filter(Item.item_id == item_id).first()

                if item and hasattr(item, 'market_hash_name') and item.market_hash_name:
                    return item.market_hash_name
                elif item and hasattr(item, 'name') and item.name:
                    return item.name
                else:
                    return f"Item_{item_id}"

        except Exception as e:
            logger.debug(f"获取饰品名称失败: {item_id}, 使用默认名称, 错误: {e}")
            return f"Item_{item_id}"

    def _record_batch_stats(self, submitted_tasks: List[Dict]):
        """记录批次统计信息"""
        try:
            if not submitted_tasks:
                return

            # 按热度排名排序
            sorted_tasks = sorted(submitted_tasks, key=lambda x: x.get('hot_rank', 999999))

            # 记录排名范围
            min_rank = sorted_tasks[0].get('hot_rank', 0)
            max_rank = sorted_tasks[-1].get('hot_rank', 0)

            logger.info(f"📊 批次统计: 热度排名范围 #{min_rank} - #{max_rank}")

            # 更新全局统计
            self.stats['last_batch_size'] = len(submitted_tasks)
            self.stats['last_batch_rank_range'] = f"#{min_rank}-#{max_rank}"

        except Exception as e:
            logger.debug(f"记录批次统计失败: {e}")

    def _construct_steamdt_url(self, item_id: str) -> Optional[str]:
        """构造SteamDT URL"""
        try:
            # 获取饰品信息（使用更安全的方式）
            from src.cs2_investment.config.database import get_db_session
            from src.cs2_investment.models.item import Item

            with get_db_session() as session:
                item = session.query(Item).filter(Item.item_id == item_id).first()

                if not item:
                    logger.warning(f"无法找到饰品: {item_id}")
                    return None

                # 强制加载market_hash_name属性
                market_hash_name = getattr(item, 'market_hash_name', None)

                if not market_hash_name:
                    logger.warning(f"饰品缺少market_hash_name: {item_id}")
                    return None

                # 构造URL（参考现有的URL构造逻辑）
                import urllib.parse
                encoded_name = urllib.parse.quote(market_hash_name)
                url = f"https://steamdt.com/cs2/{encoded_name}"

                return url

        except Exception as e:
            logger.error(f"构造URL失败: {item_id}, 错误: {e}")
            return None

    async def _run_investment_screening(self) -> bool:
        """运行投资筛选算法"""
        try:
            start_time = datetime.now()
            logger.info("🧠 开始运行投资筛选算法")

            # 获取可用算法列表
            available_algorithms = self.algorithm_manager.get_available_algorithms()
            logger.info(f"📋 可用算法: {len(available_algorithms)} 种 - {', '.join(available_algorithms)}")

            # 运行所有算法
            logger.info(f"⚙️ 开始执行算法，每种算法限制 {self.algorithm_limit_per_type} 个结果")
            all_results = self.algorithm_manager.run_all_algorithms(
                limit_per_type=self.algorithm_limit_per_type
            )

            # 详细统计每种算法的结果
            algorithm_stats = {}
            total_results = 0
            successful_algorithms = 0

            for investment_type, results in all_results.items():
                result_count = len(results)
                total_results += result_count
                algorithm_stats[investment_type] = result_count

                if result_count > 0:
                    successful_algorithms += 1
                    logger.info(f"   ✅ {investment_type}: {result_count} 个推荐")
                else:
                    logger.warning(f"   ⚠️ {investment_type}: 无推荐结果")

            algorithms_run = len(all_results)
            execution_time = (datetime.now() - start_time).total_seconds()

            logger.info(f"📊 算法执行完成: {successful_algorithms}/{algorithms_run} 种算法成功, 生成 {total_results} 个推荐, 耗时 {execution_time:.1f}秒")

            # 验证和保存结果
            if total_results > 0:
                # 验证结果有效性
                valid_results = self._validate_screening_results(all_results)

                if valid_results:
                    # 将所有结果合并为一个列表
                    all_screening_results = []
                    for investment_type, results in all_results.items():
                        all_screening_results.extend(results)

                    # 保存到数据库
                    save_start_time = datetime.now()
                    save_success = self.algorithm_manager.save_screening_results(all_screening_results)
                    save_duration = (datetime.now() - save_start_time).total_seconds()

                    if save_success:
                        logger.info(f"💾 投资推荐结果保存成功: {total_results} 条, 耗时 {save_duration:.1f}秒")
                    else:
                        logger.error("💾 投资推荐结果保存失败")
                        return False
                else:
                    logger.error("❌ 筛选结果验证失败")
                    return False
            else:
                logger.warning("⚠️ 没有生成任何投资推荐结果")

            # 更新统计信息
            self.stats['total_algorithms_run'] += algorithms_run
            self.stats['last_screening_stats'] = algorithm_stats
            self.stats['last_screening_duration'] = execution_time
            self.stats['last_screening_total_results'] = total_results

            return total_results > 0

        except Exception as e:
            logger.error(f"❌ 运行投资筛选算法失败: {e}")
            return False

    def _validate_screening_results(self, all_results: Dict[str, List]) -> bool:
        """验证筛选结果的有效性"""
        try:
            if not all_results:
                logger.warning("⚠️ 筛选结果为空")
                return False

            total_results = 0
            valid_algorithms = 0

            for investment_type, results in all_results.items():
                if not isinstance(results, list):
                    logger.error(f"❌ {investment_type} 结果格式错误: 不是列表类型")
                    return False

                if len(results) > 0:
                    valid_algorithms += 1
                    total_results += len(results)

                    # 验证第一个结果的基本结构
                    first_result = results[0]
                    if not hasattr(first_result, 'item_id'):
                        logger.error(f"❌ {investment_type} 结果缺少item_id字段")
                        return False

                    if not hasattr(first_result, 'score'):
                        logger.error(f"❌ {investment_type} 结果缺少score字段")
                        return False

                    logger.debug(f"✅ {investment_type} 结果验证通过: {len(results)} 个结果")

            if valid_algorithms == 0:
                logger.warning("⚠️ 没有算法产生有效结果")
                return False

            logger.info(f"✅ 筛选结果验证通过: {valid_algorithms} 种算法, {total_results} 个结果")
            return True

        except Exception as e:
            logger.error(f"❌ 筛选结果验证失败: {e}")
            return False

    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        # 计算运行时间
        runtime_seconds = 0.0
        if self.start_time:
            runtime_seconds = (datetime.now() - self.start_time).total_seconds()

        # 计算当前周期进度
        current_cycle_progress = 0.0
        if self.current_cycle_start_time:
            cycle_elapsed = (datetime.now() - self.current_cycle_start_time).total_seconds()
            cycle_total = self.config.analysis_interval_hours * 3600
            current_cycle_progress = min(cycle_elapsed / cycle_total * 100, 100.0)

        return {
            'is_running': self.is_running,
            'start_time': self.start_time,
            'runtime_seconds': runtime_seconds,
            'last_processed_time': self.last_processed_time,
            'current_batch': self.current_batch,
            'total_batches': self.total_batches,
            'current_cycle_progress': current_cycle_progress,
            'config': self.config.to_dict(),
            'stats': self._calculate_enhanced_stats()
        }

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self._calculate_enhanced_stats()

    def get_config(self) -> InvestmentAnalysisConfig:
        """获取配置对象"""
        return self.config

    def update_config(self, new_config: InvestmentAnalysisConfig):
        """更新配置"""
        old_config = self.config
        self.config = new_config

        # 更新向后兼容的属性
        self.analysis_interval_hours = self.config.analysis_interval_hours
        self.batch_size = self.config.batch_size
        self.max_ranking_items = self.config.max_ranking_items
        self.max_retry_count = self.config.max_retry_count
        self.algorithm_limit_per_type = self.config.algorithm_limit_per_type

        logger.info(f"📝 配置已更新: 分析间隔 {old_config.analysis_interval_hours}h → {new_config.analysis_interval_hours}h")

    def _calculate_enhanced_stats(self) -> Dict[str, Any]:
        """计算增强的统计信息"""
        stats = self.stats.copy()

        # 计算运行时间
        if self.start_time:
            stats['total_runtime_seconds'] = (datetime.now() - self.start_time).total_seconds()

        # 计算成功率
        total_cycles = stats['total_cycles']
        if total_cycles > 0:
            stats['success_rate'] = (stats['successful_cycles'] / total_cycles) * 100
            stats['error_rate'] = (stats['failed_cycles'] / total_cycles) * 100

        # 计算平均周期时间
        if stats['successful_cycles'] > 0 and stats['total_runtime_seconds'] > 0:
            stats['average_cycle_duration'] = stats['total_runtime_seconds'] / stats['successful_cycles']

        # 计算性能指标
        if stats['total_runtime_seconds'] > 0:
            hours = stats['total_runtime_seconds'] / 3600
            stats['items_per_hour'] = stats['total_items_analyzed'] / hours if hours > 0 else 0
            stats['cycles_per_day'] = (stats['total_cycles'] / hours) * 24 if hours > 0 else 0

        # 计算正常运行时间百分比
        if stats['total_runtime_seconds'] > 0:
            expected_cycles = stats['total_runtime_seconds'] / (self.config.analysis_interval_hours * 3600)
            if expected_cycles > 0:
                stats['uptime_percentage'] = min((stats['total_cycles'] / expected_cycles) * 100, 100)

        return stats

    def _record_error(self, error_message: str):
        """记录错误"""
        self.stats['last_error'] = error_message
        self.stats['last_error_time'] = datetime.now()
        self.stats['total_errors'] += 1

        if self.config.enable_detailed_logging:
            logger.error(f"📊 错误记录: {error_message}")

    def _record_cycle_start(self):
        """记录周期开始"""
        self.current_cycle_start_time = datetime.now()
        if not self.start_time:
            self.start_time = self.current_cycle_start_time

        if self.config.enable_detailed_logging:
            logger.info(f"📊 周期开始: {self.current_cycle_start_time}")

    def _record_cycle_end(self, success: bool):
        """记录周期结束"""
        if self.current_cycle_start_time:
            duration = (datetime.now() - self.current_cycle_start_time).total_seconds()
            self.stats['last_cycle_duration'] = duration
            self.stats['last_cycle_time'] = datetime.now()

            if success:
                self.stats['successful_cycles'] += 1
            else:
                self.stats['failed_cycles'] += 1

            self.stats['total_cycles'] += 1

            if self.config.enable_detailed_logging:
                status = "成功" if success else "失败"
                logger.info(f"📊 周期结束: {status}, 耗时 {duration:.1f}秒")

    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        stats = self._calculate_enhanced_stats()

        # 判断健康状态
        is_healthy = True
        health_issues = []

        # 检查错误率
        if stats['error_rate'] > 50:
            is_healthy = False
            health_issues.append(f"错误率过高: {stats['error_rate']:.1f}%")

        # 检查最近是否有活动
        if self.last_processed_time:
            hours_since_last = (datetime.now() - self.last_processed_time).total_seconds() / 3600
            if hours_since_last > self.config.analysis_interval_hours * 2:
                is_healthy = False
                health_issues.append(f"长时间无活动: {hours_since_last:.1f}小时")

        # 检查成功率
        if stats['success_rate'] < 50 and stats['total_cycles'] > 3:
            is_healthy = False
            health_issues.append(f"成功率过低: {stats['success_rate']:.1f}%")

        return {
            'is_healthy': is_healthy,
            'health_score': max(0, 100 - len(health_issues) * 25),
            'issues': health_issues,
            'last_check_time': datetime.now(),
            'uptime_percentage': stats['uptime_percentage'],
            'error_rate': stats['error_rate'],
            'success_rate': stats['success_rate']
        }
