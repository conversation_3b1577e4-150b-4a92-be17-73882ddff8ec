#!/usr/bin/env python3
"""
SteamDT集成分析系统

完整流程：
1. 抓取饰品趋势和K线数据
2. 转换数据格式为分析系统所需格式
3. 调用分析系统进行专业分析
4. 生成完整的投资分析报告
"""

import asyncio
import sys
import json
import os
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入抓取系统
from src.cs2_investment.scraper.scraper_factory import get_scraper_factory
from src.cs2_investment.scraper.data_storage import DataStorage

# 导入数据服务
from src.cs2_investment.services.analysis_data_service import AnalysisDataService

# 使用统一日志组件
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)


class IntegratedAnalysisSystem:
    """集成分析系统"""
    
    def __init__(self, analysis_system_path: str = None):
        """
        初始化集成分析系统

        Args:
            analysis_system_path: 分析系统路径，默认为相对路径
        """
        if analysis_system_path is None:
            # 使用正确的相对路径，指向syncps目录
            current_dir = Path(__file__).parent
            self.analysis_system_path = current_dir / "syncps"
        else:
            self.analysis_system_path = Path(analysis_system_path)

        # 确保分析系统路径存在
        if not self.analysis_system_path.exists():
            raise FileNotFoundError(f"分析系统路径不存在: {self.analysis_system_path}")

        # 初始化数据服务
        self.data_service = AnalysisDataService()
    
    async def run_complete_analysis(self, item_url: str, item_name: str = None) -> Dict:
        """
        运行完整的集成分析流程
        
        Args:
            item_url: 饰品URL
            item_name: 饰品名称（用于分析系统）
        
        Returns:
            Dict: 分析结果
        """
        print("=" * 80)
        print("🎯 SteamDT集成分析系统")
        print("📊 抓取 → 转换 → 分析 → 报告")
        print("=" * 80)
        
        try:
            # 第一步：抓取数据
            print(f"\n🚀 第一步：抓取饰品数据")
            print(f"📍 URL: {item_url}")
            
            scraping_result = await self._scrape_data(item_url)
            if not scraping_result['success']:
                return {'success': False, 'error': f"数据抓取失败: {scraping_result['error']}"}
            
            # 第二步：转换数据格式
            print(f"\n🔄 第二步：转换数据格式")
            
            if not item_name:
                item_name = self._extract_item_name_from_url(item_url)
            
            conversion_result = self._convert_data_format(scraping_result['data'], item_name)
            if not conversion_result['success']:
                return {'success': False, 'error': f"数据转换失败: {conversion_result['error']}"}
            
            # 第三步：运行分析系统
            print(f"\n📊 第三步：运行专业分析")

            analysis_result = self._run_analysis_system(item_name, conversion_result.get('data_path'))
            if not analysis_result['success']:
                return {'success': False, 'error': f"分析失败: {analysis_result['error']}"}

            # 第四步：生成专业图表
            print(f"\n📈 第四步：生成专业图表")

            chart_result = self._generate_professional_charts(analysis_result['analyzer'], item_name, conversion_result.get('data_path'))
            if not chart_result['success']:
                print(f"⚠️ 图表生成失败: {chart_result['error']}")
                # 图表生成失败不影响整体流程

            # 第五步：生成技术分析报告
            print(f"\n📝 第五步：生成技术分析报告")

            report_result = self._generate_technical_report(analysis_result['analyzer'], item_name, conversion_result.get('data_path'))
            if not report_result['success']:
                print(f"⚠️ 报告生成失败: {report_result['error']}")
                # 报告生成失败不影响整体流程

            # 第六步：保存分析结果到数据库
            print(f"\n💾 第六步：保存分析结果到数据库")

            database_result = self._save_analysis_to_database(
                analysis_result.get('result', {}),
                item_name,
                conversion_result.get('data_path', ''),
                chart_result.get('chart_path') if chart_result['success'] else None,
                report_result.get('report_path') if report_result['success'] else None,
                analysis_result.get('duration', 0)
            )

            # 第七步：整合分析结果
            print(f"\n📋 第七步：整合分析结果")

            # 注释掉投资推荐分析调用
            # print(f"\n🧠 第八步：运行投资推荐分析")
            # item_id = Path(conversion_result['data_path']).name if conversion_result.get('data_path') else "unknown"
            # investment_result = await self._run_investment_screening(item_id, analysis_result)

            # 设置空的投资结果
            investment_result = {
                'success': True,
                'message': '投资推荐分析已禁用',
                'recommendations_count': 0
            }

            final_result = {
                'success': True,
                'item_name': item_name,
                'item_url': item_url,
                'scraping_data': scraping_result['data'],
                'analysis_result': analysis_result['result'],
                'chart_result': chart_result if chart_result['success'] else None,
                'database_result': database_result,
                'investment_result': investment_result,
                'data_path': conversion_result['data_path'],
                'chart_path': chart_result.get('chart_path') if chart_result['success'] else None,
                'completed_at': datetime.now().isoformat()
            }

            print(f"\n🎉 集成分析完成！")
            print(f"✅ 数据抓取: 成功")
            print(f"✅ 数据转换: 成功")
            print(f"✅ 专业分析: 成功")
            print(f"✅ 图表生成: {'成功' if chart_result['success'] else '失败'}")
            print(f"✅ 报告生成: {'成功' if report_result['success'] else '失败'}")
            print(f"✅ 数据库保存: {'成功' if database_result['success'] else '失败'}")
            print(f"✅ 投资推荐: {'成功' if investment_result['success'] else '失败'}")
            print(f"📁 数据路径: {conversion_result['data_path']}")
            if chart_result['success']:
                print(f"📈 图表路径: {chart_result.get('chart_path', '未知')}")
            if report_result['success']:
                print(f"📝 报告路径: {report_result.get('report_path', '未知')}")
            if investment_result['success']:
                print(f"🎯 投资推荐: {investment_result.get('recommendations_count', 0)} 条")

            return final_result

        except Exception as e:
            logger.error(f"集成分析失败: {e}")
            return {'success': False, 'error': str(e)}

    async def _run_investment_screening(self, item_id: str, analysis_result: Dict) -> Dict:
        """运行投资推荐分析"""
        try:
            print(f"🔍 开始对饰品 {item_id} 进行投资推荐分析...")

            # 导入算法管理器和推荐服务
            from src.cs2_investment.algorithms.algorithm_manager import AlgorithmManager
            from src.cs2_investment.services.investment_recommendation_service import InvestmentRecommendationService

            # 创建算法管理器
            algorithm_manager = AlgorithmManager()

            # 运行所有投资筛选算法（针对单个饰品）
            screening_results = algorithm_manager.run_algorithms_for_single_item(
                item_id=item_id,
                analysis_data=analysis_result.get('result', {})
            )

            if screening_results:
                # 创建推荐服务
                recommendation_service = InvestmentRecommendationService()

                # 生成最终投资推荐
                final_recommendation = recommendation_service.generate_final_recommendation(
                    item_id=item_id,
                    screening_results=screening_results,
                    analysis_result=analysis_result
                )

                if final_recommendation:
                    # 在保存前获取需要显示的信息（避免会话问题）
                    recommendation_info = {
                        'recommendation_type': final_recommendation.recommendation_type,
                        'total_score': final_recommendation.total_score,
                        'risk_level': final_recommendation.risk_level,
                        'algorithm_count': final_recommendation.algorithm_count,
                        'recommendation_reason': final_recommendation.recommendation_reason
                    }

                    # 保存最终推荐到新表
                    save_success = recommendation_service.save_recommendation(final_recommendation)

                    print(f"✅ 投资推荐分析完成: 生成最终推荐")
                    print(f"📊 推荐详情:")
                    print(f"   推荐类型: {recommendation_info['recommendation_type']}")
                    print(f"   综合评分: {recommendation_info['total_score']}")
                    print(f"   风险等级: {recommendation_info['risk_level']}")
                    print(f"   参与算法: {recommendation_info['algorithm_count']} 个")
                    print(f"   推荐理由: {str(recommendation_info['recommendation_reason'])[:100]}...")

                    return {
                        'success': True,
                        'recommendations_count': 1,  # 最终推荐只有1条
                        'final_recommendation': recommendation_info,
                        'algorithm_results_count': len(screening_results),
                        'saved_to_database': save_success
                    }
                else:
                    print(f"⚠️ 无法生成最终投资推荐")

                    # 即使无法生成最终推荐，也要保存一条记录表示已分析
                    try:
                        from src.cs2_investment.services.investment_recommendation_service import InvestmentRecommendationService
                        from src.cs2_investment.models.investment_recommendation import InvestmentRecommendation
                        from datetime import datetime, date
                        from decimal import Decimal
                        import uuid

                        recommendation_service = InvestmentRecommendationService()
                        session_id = str(uuid.uuid4())[:8]

                        # 创建一个"AVOID"类型的推荐记录
                        no_recommendation = InvestmentRecommendation(
                            item_id=item_id,
                            recommendation_date=date.today(),
                            recommendation_time=datetime.now(),
                            recommendation_session_id=session_id,
                            recommendation_type='AVOID',
                            total_score=Decimal('0.00'),
                            algorithm_count=len(screening_results),
                            algorithm_details=None,
                            recommendation_reason=f'虽有{len(screening_results)}个算法筛选结果，但无法生成最终投资推荐',
                            risk_level='HIGH',
                            confidence_level=Decimal('80.00'),
                            status='ACTIVE'
                        )

                        # 保存到数据库
                        save_success = recommendation_service.save_recommendation(no_recommendation)

                        print(f"✅ 已保存'不推荐'记录到数据库")

                        return {
                            'success': True,
                            'recommendations_count': 0,
                            'algorithm_results_count': len(screening_results),
                            'saved_to_database': save_success,
                            'no_recommendation_saved': True
                        }

                    except Exception as e:
                        print(f"❌ 保存'不推荐'记录失败: {e}")
                        return {
                            'success': True,
                            'recommendations_count': 0,
                            'algorithm_results_count': len(screening_results),
                            'saved_to_database': False
                        }
            else:
                print(f"⚠️ 该饰品未通过任何投资筛选算法")

                # 即使没有推荐，也要保存一条记录表示已分析
                try:
                    from src.cs2_investment.services.investment_recommendation_service import InvestmentRecommendationService
                    from src.cs2_investment.models.investment_recommendation import InvestmentRecommendation
                    from datetime import datetime, date
                    from decimal import Decimal
                    import uuid

                    recommendation_service = InvestmentRecommendationService()
                    session_id = str(uuid.uuid4())[:8]

                    # 创建一个"AVOID"类型的推荐记录，表示该饰品已分析但不推荐
                    no_recommendation = InvestmentRecommendation(
                        item_id=item_id,
                        recommendation_date=date.today(),
                        recommendation_time=datetime.now(),
                        recommendation_session_id=session_id,
                        recommendation_type='AVOID',  # 使用AVOID表示不推荐
                        total_score=Decimal('0.00'),
                        algorithm_count=0,
                        algorithm_details=None,
                        recommendation_reason='该饰品未通过任何投资筛选算法，不建议投资',
                        risk_level='HIGH',  # 未通过筛选的饰品标记为高风险
                        confidence_level=Decimal('100.00'),  # 对"不推荐"的结论很确定
                        status='ACTIVE'
                    )

                    # 保存到数据库
                    save_success = recommendation_service.save_recommendation(no_recommendation)

                    print(f"✅ 已保存'不推荐'记录到数据库")

                    return {
                        'success': True,
                        'recommendations_count': 0,
                        'algorithm_results_count': 0,
                        'saved_to_database': save_success,
                        'no_recommendation_saved': True
                    }

                except Exception as e:
                    print(f"❌ 保存'不推荐'记录失败: {e}")
                    return {
                        'success': True,
                        'recommendations_count': 0,
                        'algorithm_results_count': 0,
                        'saved_to_database': False
                    }

        except Exception as e:
            print(f"❌ 投资推荐分析失败: {e}")
            logger.error(f"投资推荐分析失败: {e}")
            import traceback
            logger.debug(f"投资推荐分析异常详情: {traceback.format_exc()}")
            return {
                'success': False,
                'error': str(e),
                'recommendations_count': 0,
                'algorithm_results_count': 0
            }
    
    async def _scrape_data(self, item_url: str) -> Dict:
        """抓取数据（syncps系统按需抓取）"""
        try:
            # syncps系统数据需求：完整数据集
            data_requirements = {
                'daily_kline_1': True,
                'daily_kline_2': True,
                'weekly_kline': True,
                'trend_data_6m': True,
                'hourly_kline': True,   # 启用时K数据
                'trend_data_3m': True  # 不需要3个月数据
            }

            # 使用ScraperFactory创建抓取器
            factory = get_scraper_factory()
            scraper = await factory.create_scraper_with_fallback()

            try:
                result = await scraper.scrape_item_data(item_url, data_requirements)

                if result.success:
                    # 更新抓取器健康状态
                    scraper_info = scraper.get_scraper_info() if hasattr(scraper, 'get_scraper_info') else {'scraper_type': 'unknown'}
                    factory.update_scraper_health(scraper_info.get('scraper_type', 'unknown'), True)

                    return {
                        'success': True,
                        'data': result
                    }
                else:
                    # 更新抓取器健康状态
                    scraper_info = scraper.get_scraper_info() if hasattr(scraper, 'get_scraper_info') else {'scraper_type': 'unknown'}
                    factory.update_scraper_health(scraper_info.get('scraper_type', 'unknown'), False, result.error_message)

                    return {
                        'success': False,
                        'error': result.error_message
                    }
            finally:
                # 确保抓取器资源被正确清理
                if scraper.is_started:
                    await scraper.stop()

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _extract_item_name_from_url(self, item_url: str) -> str:
        """从URL提取饰品名称"""
        try:
            # 从URL中提取饰品名称
            # 例如: https://steamdt.com/cs2/Desert%20Eagle%20%7C%20Printstream%20(Field-Tested)
            import urllib.parse
            
            url_parts = item_url.split('/')
            if len(url_parts) >= 2:
                encoded_name = url_parts[-1]
                decoded_name = urllib.parse.unquote(encoded_name)
                # 替换特殊字符
                decoded_name = decoded_name.replace('%7C', '|').replace('%20', ' ')
                return decoded_name
            else:
                return f"Item_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        except Exception as e:
            logger.warning(f"提取饰品名称失败: {e}")
            return f"Item_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    def _get_user_report_content(self, analysis_result: dict) -> str:
        """获取用户报告的完整内容"""
        try:
            # 检查是否有用户友好报告数据
            if 'user_friendly_report' not in analysis_result:
                return None

            # 直接使用现有的用户友好报告数据
            user_report = analysis_result['user_friendly_report']

            # 格式化为字符串
            return self._format_report_as_string(user_report)

        except Exception as e:
            logger.error(f"获取用户报告内容失败: {e}")
            return None

    def _format_report_as_string(self, report: dict) -> str:
        """将报告数据格式化为字符串（模拟print_user_friendly_report的输出）"""
        try:
            lines = []

            # 标题和时间
            lines.append("=" * 60)
            lines.append(f"📋 {report['report_title']}")
            lines.append(f"📅 生成时间：{report['generation_time']}")
            lines.append("=" * 60)

            # 基本信息
            basic = report['basic_info']
            lines.append("")
            lines.append("📊 基本信息")
            lines.append(f"   饰品名称：{basic['item_name']}")
            lines.append(f"   当前价格：{basic['current_price']} (数据源: 基本面快照(最新市场价格))")
            lines.append(f"   价格趋势：{basic['price_trend']}")
            lines.append(f"   市场活跃度：{basic['market_activity']}")

            # 技术指标
            if 'technical_indicators' in basic:
                lines.append("   关键技术指标：")
                for indicator in basic['technical_indicators']:
                    lines.append(f"     • {indicator}")

            # 投资建议
            advice = report['investment_advice']
            lines.append("")
            lines.append("💡 投资建议")
            lines.append(f"   主要建议：{advice['main_advice']}")
            lines.append(f"   置信度：{advice['confidence_description']}")
            lines.append(f"   推荐策略：{advice['strategy_explanation']}")

            if 'key_reasons' in advice:
                lines.append("   关键原因：")
                for reason in advice['key_reasons']:
                    lines.append(f"     • {reason}")

            if 'action_plan' in advice:
                lines.append("   行动计划：")
                for i, action in enumerate(advice['action_plan'], 1):
                    lines.append(f"     {i}. {action}")

            if 'timing_advice' in advice:
                lines.append(f"   时间建议：{advice['timing_advice']}")

            # 风险评估
            risk = report['risk_summary']
            lines.append("")
            lines.append("⚠️ 风险评估")
            lines.append(f"   风险等级：{risk['overall_risk_level']}")
            lines.append(f"   风险评分：{risk['risk_score']}")
            lines.append(f"   评分说明：{risk['score_explanation']}")

            if 'main_risks' in risk:
                lines.append("   主要风险：")
                for risk_item in risk['main_risks']:
                    lines.append(f"     • {risk_item}")

            if 'risk_control_suggestions' in risk:
                lines.append("   风险控制建议：")
                for suggestion in risk['risk_control_suggestions']:
                    lines.append(f"     {suggestion}")

            if 'suitable_investors' in risk:
                lines.append(f"   适合投资者：{risk['suitable_investors']}")

            # 市场情况分析
            market = report['market_analysis']
            lines.append("")
            lines.append("📈 市场情况分析")
            lines.append(f"   供需情况：{market['supply_demand_analysis']}")
            lines.append(f"   价格走势：{market['price_trend_analysis']}")
            lines.append(f"   市场情绪：{market['market_sentiment_analysis']}")

            if 'anomaly_summary' in market:
                lines.append(f"   异常情况：{market['anomaly_summary']}")

            if 'overall_assessment' in market:
                lines.append(f"   整体评估：{market['overall_assessment']}")

            # 异常分析详情
            if 'anomaly_details' in report:
                anomaly = report['anomaly_details']
                lines.append("")
                lines.append("🚨 异常分析详情")
                lines.append(f"   异常总数：{anomaly['anomaly_summary']}")

                if 'anomaly_breakdown' in anomaly:
                    lines.append("   异常类型：")
                    for anomaly_type, count in anomaly['anomaly_breakdown'].items():
                        lines.append(f"     • {anomaly_type}: {count}个")

                lines.append(f"   风险影响：{anomaly['risk_impact']}")

                if 'recent_anomalies' in anomaly:
                    lines.append("   最近异常：")
                    for recent in anomaly['recent_anomalies']:
                        lines.append(f"     • {recent}")

                if 'monitoring_advice' in anomaly:
                    lines.append(f"   监控建议：{anomaly['monitoring_advice']}")

            # 免责声明
            lines.append("")
            lines.append("=" * 60)
            lines.append("📝 免责声明：投资有风险，入市需谨慎。本报告仅供参考，不构成投资建议。")
            lines.append("=" * 60)

            return "\n".join(lines)

        except Exception as e:
            logger.error(f"格式化报告字符串失败: {e}")
            return str(report)

    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除或替换无效字符"""
        import re

        # 定义无效字符的映射
        char_replacements = {
            '★': 'Star',
            '（': '(',
            '）': ')',
            '|': '_',
            '/': '_',
            '\\': '_',
            ':': '_',
            '*': '_',
            '?': '_',
            '"': '_',
            '<': '_',
            '>': '_',
            '™': 'TM',
            '®': 'R'
        }

        # 替换特殊字符
        safe_filename = filename
        for old_char, new_char in char_replacements.items():
            safe_filename = safe_filename.replace(old_char, new_char)

        # 移除其他可能的无效字符
        safe_filename = re.sub(r'[^\w\s\-_().]', '_', safe_filename)

        # 清理多余的空格和下划线
        safe_filename = re.sub(r'\s+', '_', safe_filename)
        safe_filename = re.sub(r'_+', '_', safe_filename)
        safe_filename = safe_filename.strip('_')

        return safe_filename

    def _save_analysis_to_database(self, analysis_result: Dict, item_name: str,
                                 data_path: str, chart_path: str = None,
                                 report_path: str = None, analysis_duration: float = None) -> Dict:
        """保存分析结果到数据库"""
        try:
            print(f"🔍 开始保存分析结果到数据库...")

            # 如果有用户报告内容，获取完整的报告内容
            if 'user_friendly_report' in analysis_result:
                # 获取完整的用户报告内容
                user_report_content = self._get_user_report_content(analysis_result)
                if user_report_content:
                    analysis_result['user_report_content'] = user_report_content

            # 保存或更新常规分析结果（每天每个饰品只保存一条）
            db_result_id = self.data_service.save_or_update_daily_analysis_result(
                analysis_result=analysis_result,
                item_name=item_name,
                data_path=data_path,
                chart_path=chart_path,
                report_path=report_path,
                analysis_duration=analysis_duration
            )

            if db_result_id:
                print(f"✅ 分析结果已保存到数据库: ID={db_result_id}")
                return {
                    'success': True,
                    'database_id': db_result_id,
                    'message': '分析结果保存成功'
                }
            else:
                print(f"❌ 分析结果保存失败")
                return {
                    'success': False,
                    'error': '数据库保存失败'
                }

        except Exception as e:
            print(f"❌ 保存分析结果到数据库时发生错误: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _convert_data_format(self, scraping_result, item_name: str) -> Dict:
        """转换数据格式为分析系统所需格式"""
        try:
            # 获取item_id作为目录名
            item_id = None
            if scraping_result.trend_data_6m:
                item_id = scraping_result.trend_data_6m.item_id
            elif scraping_result.trend_data_3m:
                item_id = scraping_result.trend_data_3m.item_id
            elif scraping_result.hourly_kline:
                item_id = scraping_result.hourly_kline.item_id
            elif scraping_result.daily_kline_1:
                item_id = scraping_result.daily_kline_1.item_id

            if not item_id:
                raise ValueError("无法获取饰品ID")

            # 创建数据目录结构：data/scraped_data/{item_id}/
            project_root = Path(__file__).parent.parent.parent.parent
            data_dir = project_root / "data" / "scraped_data" / str(item_id)
            data_dir.mkdir(parents=True, exist_ok=True)

            print(f"📁 创建数据目录: {data_dir}")
            print(f"🆔 饰品ID: {item_id}")
            print(f"📝 饰品名称: {item_name}")
            
            # 转换并保存各种数据
            conversion_success = 0
            total_conversions = 5
            
            # 1. 转换时K数据
            if scraping_result.hourly_kline and scraping_result.hourly_kline.raw_data:
                hourly_file = data_dir / "时k.json"
                with open(hourly_file, 'w', encoding='utf-8') as f:
                    json.dump(scraping_result.hourly_kline.raw_data, f, ensure_ascii=False, indent=2)
                print(f"✅ 时K数据: {len(scraping_result.hourly_kline.raw_data)} 条")
                conversion_success += 1
            else:
                print(f"❌ 时K数据转换失败: hourly_kline={scraping_result.hourly_kline is not None}, raw_data={scraping_result.hourly_kline.raw_data if scraping_result.hourly_kline else None}")
            
            # 2. 转换日K数据（第一次响应）
            if scraping_result.daily_kline_1 and scraping_result.daily_kline_1.raw_data:
                daily1_file = data_dir / "日k1.json"
                with open(daily1_file, 'w', encoding='utf-8') as f:
                    json.dump(scraping_result.daily_kline_1.raw_data, f, ensure_ascii=False, indent=2)
                print(f"✅ 日K1数据: {len(scraping_result.daily_kline_1.raw_data)} 条")
                conversion_success += 1
            
            # 3. 转换日K数据（第二次响应）
            if scraping_result.daily_kline_2 and scraping_result.daily_kline_2.raw_data:
                daily2_file = data_dir / "日k2.json"
                with open(daily2_file, 'w', encoding='utf-8') as f:
                    json.dump(scraping_result.daily_kline_2.raw_data, f, ensure_ascii=False, indent=2)
                print(f"✅ 日K2数据: {len(scraping_result.daily_kline_2.raw_data)} 条")
                conversion_success += 1
            
            # 4. 转换周K数据
            if scraping_result.weekly_kline and scraping_result.weekly_kline.raw_data:
                weekly_file = data_dir / "周k.json"
                with open(weekly_file, 'w', encoding='utf-8') as f:
                    json.dump(scraping_result.weekly_kline.raw_data, f, ensure_ascii=False, indent=2)
                print(f"✅ 周K数据: {len(scraping_result.weekly_kline.raw_data)} 条")
                conversion_success += 1
            else:
                print(f"❌ 周K数据转换失败: weekly_kline={scraping_result.weekly_kline is not None}, raw_data={scraping_result.weekly_kline.raw_data if scraping_result.weekly_kline else None}")
            
            # 5. 转换趋势数据（使用6个月数据作为走势数据）
            if scraping_result.trend_data_6m and scraping_result.trend_data_6m.raw_data:
                trend_file = data_dir / "走势.json"
                with open(trend_file, 'w', encoding='utf-8') as f:
                    json.dump(scraping_result.trend_data_6m.raw_data, f, ensure_ascii=False, indent=2)
                print(f"✅ 走势数据: {len(scraping_result.trend_data_6m.raw_data)} 条")
                conversion_success += 1
            
            print(f"📊 数据转换完成: {conversion_success}/{total_conversions}")
            
            if conversion_success >= 4:  # 至少需要4种数据
                return {
                    'success': True,
                    'data_path': str(data_dir),
                    'conversions': conversion_success
                }
            else:
                return {
                    'success': False,
                    'error': f"数据转换不完整: 只成功转换了 {conversion_success}/{total_conversions} 种数据"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _run_analysis_system(self, item_name: str, data_path: str = None) -> Dict:
        """运行分析系统"""
        try:
            # 切换到分析系统目录
            original_cwd = os.getcwd()
            original_path = sys.path.copy()

            os.chdir(self.analysis_system_path)

            # 添加分析系统路径到Python路径
            sys.path.insert(0, str(self.analysis_system_path))

            try:
                # 清除可能的模块缓存
                import importlib

                # 导入分析系统
                if 'main_analysis_system' in sys.modules:
                    importlib.reload(sys.modules['main_analysis_system'])

                from main_analysis_system import CS2AnalysisSystemV2

                # 创建分析系统实例
                if data_path:
                    # 如果提供了数据路径，使用该路径作为数据目录
                    analyzer = CS2AnalysisSystemV2(data_path)
                else:
                    # 否则使用默认的item_name
                    analyzer = CS2AnalysisSystemV2(item_name)

                # 运行完整分析
                analysis_result = analyzer.run_complete_analysis()

                return {
                    'success': True,
                    'result': analysis_result,
                    'analyzer': analyzer  # 返回分析器实例用于图表生成
                }

            finally:
                # 恢复原始工作目录和路径
                os.chdir(original_cwd)
                sys.path = original_path

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_professional_charts(self, analyzer, item_name: str, data_path: str = None) -> Dict:
        """生成专业图表"""
        try:
            # 切换到分析系统目录
            original_cwd = os.getcwd()
            original_path = sys.path.copy()

            os.chdir(self.analysis_system_path)
            sys.path.insert(0, str(self.analysis_system_path))

            try:
                # 导入图表生成系统
                import importlib

                if 'professional_chart_system' in sys.modules:
                    importlib.reload(sys.modules['professional_chart_system'])

                from professional_chart_system import ProfessionalChartSystem

                # 创建图表生成系统
                chart_system = ProfessionalChartSystem(analyzer)

                # 生成基于item_id和时间戳的图表保存路径
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                # 从data_path中提取item_id
                item_id = Path(data_path).name if data_path else "unknown"

                # 构建保存路径：data/analysis_results/{item_id}/
                project_root = Path(__file__).parent.parent.parent.parent
                analysis_dir = project_root / "data" / "analysis_results" / item_id
                analysis_dir.mkdir(parents=True, exist_ok=True)

                chart_filename = f"{item_id}_专业仪表板_{timestamp}.png"
                chart_path = analysis_dir / chart_filename

                print(f"🔍 饰品ID: {item_id}")
                print(f"🔍 时间戳: {timestamp}")
                print(f"🔍 图表保存路径: {chart_path}")

                # 生成综合仪表板
                chart_system.generate_comprehensive_dashboard(
                    save_path=str(chart_path),
                    show_chart=False
                )

                print(f"✅ 专业图表已生成: {chart_filename}")

                return {
                    'success': True,
                    'chart_path': str(chart_path),
                    'chart_filename': chart_filename
                }

            finally:
                # 恢复原始工作目录和路径
                os.chdir(original_cwd)
                sys.path = original_path

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_technical_report(self, analyzer, item_name: str, data_path: str = None) -> Dict:
        """生成技术分析报告"""
        try:
            # 切换到分析系统目录
            original_cwd = os.getcwd()
            original_path = sys.path.copy()

            os.chdir(self.analysis_system_path)
            sys.path.insert(0, str(self.analysis_system_path))

            try:
                # 导入技术分析报告生成器
                import importlib

                if 'technical_analysis_report_generator' in sys.modules:
                    importlib.reload(sys.modules['technical_analysis_report_generator'])

                from technical_analysis_report_generator import TechnicalAnalysisReportGenerator

                # 生成基于item_id和时间戳的报告保存路径
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                # 从data_path中提取item_id
                item_id = Path(data_path).name if data_path else "unknown"

                # 构建保存路径：data/analysis_results/{item_id}/
                project_root = Path(__file__).parent.parent.parent.parent
                analysis_dir = project_root / "data" / "analysis_results" / item_id
                analysis_dir.mkdir(parents=True, exist_ok=True)

                report_filename = f"{item_id}_技术分析报告_{timestamp}.md"
                report_path = analysis_dir / report_filename

                print(f"🔍 饰品ID: {item_id}")
                print(f"🔍 时间戳: {timestamp}")
                print(f"🔍 报告保存路径: {report_path}")

                # 运行完整分析获取结果
                analysis_result = analyzer.run_complete_analysis()

                # 创建技术分析报告生成器
                report_generator = TechnicalAnalysisReportGenerator(
                    item_name=item_name,
                    analysis_results=analysis_result,
                    analysis_system=analyzer
                )

                # 生成技术分析报告
                report_content = report_generator.generate_technical_report()

                # 保存报告到文件
                with open(report_path, 'w', encoding='utf-8') as f:
                    f.write(report_content)

                print(f"✅ 技术分析报告已生成: {report_filename}")

                return {
                    'success': True,
                    'report_path': str(report_path),
                    'report_filename': report_filename,
                    'report_content': report_content
                }

            finally:
                # 恢复原始工作目录和路径
                os.chdir(original_cwd)
                sys.path = original_path

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_comprehensive_report(self, scraping_data, analysis_result, chart_result, item_name: str, data_path: str = None) -> Dict:
        """生成完整的投资分析报告"""
        try:
            # 生成基于item_id和时间戳的报告保存路径
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 从data_path中提取item_id
            item_id = Path(data_path).name if data_path else "unknown"

            # 构建保存路径：data/analysis_results/{item_id}/
            project_root = Path(__file__).parent.parent.parent.parent
            analysis_dir = project_root / "data" / "analysis_results" / item_id
            analysis_dir.mkdir(parents=True, exist_ok=True)

            report_filename = f"{item_id}_投资分析报告_{timestamp}.md"
            report_path = analysis_dir / report_filename

            print(f"🔍 报告保存路径: {report_path}")

            # 生成报告内容
            report_content = self._create_report_content(scraping_data, analysis_result, chart_result, item_name)

            # 保存报告
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)

            print(f"✅ 投资分析报告已生成: {report_filename}")

            return {
                'success': True,
                'report_path': str(report_path),
                'report_filename': report_filename
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _create_report_content(self, scraping_data, analysis_result, chart_result, item_name: str) -> str:
        """创建报告内容"""
        from datetime import datetime

        # 获取数据统计
        data_stats = {
            'trend_3m_count': len(scraping_data.trend_data_3m.raw_data) if scraping_data.trend_data_3m and scraping_data.trend_data_3m.raw_data else 0,
            'trend_6m_count': len(scraping_data.trend_data_6m.raw_data) if scraping_data.trend_data_6m and scraping_data.trend_data_6m.raw_data else 0,
            'hourly_count': len(scraping_data.hourly_kline.raw_data) if scraping_data.hourly_kline and scraping_data.hourly_kline.raw_data else 0,
            'daily_1_count': len(scraping_data.daily_kline_1.raw_data) if scraping_data.daily_kline_1 and scraping_data.daily_kline_1.raw_data else 0,
            'daily_2_count': len(scraping_data.daily_kline_2.raw_data) if scraping_data.daily_kline_2 and scraping_data.daily_kline_2.raw_data else 0,
            'weekly_count': len(scraping_data.weekly_kline.raw_data) if scraping_data.weekly_kline and scraping_data.weekly_kline.raw_data else 0
        }

        # 获取分析结果摘要
        investment_recommendation = analysis_result.get('comprehensive_report', {}).get('investment_recommendation', {})
        technical_analysis = analysis_result.get('technical_analysis', {})
        risk_assessment = analysis_result.get('risk_assessment', {})

        report_content = f"""# {item_name} - 投资分析报告

## 📊 报告概览

**生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
**分析系统**: SteamDT集成分析系统 (syncps)
**数据来源**: SteamDT官方API
**分析周期**: 6个月长期投资分析

---

## 📈 数据统计

### 原始数据获取情况
- **3个月趋势数据**: {data_stats['trend_3m_count']:,} 条记录
- **6个月趋势数据**: {data_stats['trend_6m_count']:,} 条记录 ⭐ (主要分析数据)
- **时K线数据**: {data_stats['hourly_count']:,} 条记录
- **日K线数据1**: {data_stats['daily_1_count']:,} 条记录
- **日K线数据2**: {data_stats['daily_2_count']:,} 条记录
- **周K线数据**: {data_stats['weekly_count']:,} 条记录

### 数据质量评估
- ✅ **数据完整性**: 所有关键数据类型均已获取
- ✅ **时间跨度**: 覆盖6个月历史数据，适合长期投资分析
- ✅ **数据密度**: 日级别数据，适合投资决策参考

---

## 💡 投资建议

### 核心推荐
**投资行动**: {investment_recommendation.get('action', '数据分析中')}
**风险等级**: {investment_recommendation.get('risk_level', '评估中')}
**置信度**: {investment_recommendation.get('confidence', '计算中')}

### 详细建议
{investment_recommendation.get('reasoning', '正在进行深度分析，请查看技术分析部分获取更多信息。')}

---

## 📊 技术分析

### 趋势分析
{technical_analysis.get('trend_analysis', '基于6个月历史数据进行趋势分析，识别长期价格走势和关键转折点。')}

### 价格水平分析
{technical_analysis.get('price_levels', '分析关键支撑位和阻力位，为投资决策提供参考价位。')}

### 市场情绪
{technical_analysis.get('market_sentiment', '综合分析市场情绪指标，评估当前市场对该饰品的整体态度。')}

---

## ⚠️ 风险评估

### 风险等级
**整体风险**: {risk_assessment.get('overall_risk', '评估中')}

### 主要风险因素
{risk_assessment.get('risk_factors', '正在分析市场波动性、流动性风险、价格风险等关键因素。')}

### 风险控制建议
{risk_assessment.get('risk_mitigation', '建议采用分批建仓、设置止损位等风险控制措施。')}

---

## 📈 图表分析

### 专业仪表板
{f"✅ 已生成专业仪表板图表" if chart_result else "❌ 图表生成失败"}

图表包含：
- 📊 价格走势图（6个月）
- 📈 技术指标分析（MACD、RSI、KDJ等）
- 📉 成交量分析
- 🎯 支撑阻力位标注
- 💡 投资建议可视化

---

## 📋 数据文件

### 保存的数据文件
- `时k.json` - 时K线数据 ({data_stats['hourly_count']:,} 条)
- `日k1.json` - 日K线数据第1次响应 ({data_stats['daily_1_count']:,} 条)
- `日k2.json` - 日K线数据第2次响应 ({data_stats['daily_2_count']:,} 条)
- `周k.json` - 周K线数据 ({data_stats['weekly_count']:,} 条)
- `走势.json` - 走势数据，基于6个月趋势数据 ({data_stats['trend_6m_count']:,} 条)

### 图表文件
- `{item_name}_专业仪表板.png` - 24x24英寸专业投资分析图表

---

## 🎯 投资策略建议

### 长期投资策略
基于6个月数据分析，该饰品适合以下投资策略：

1. **价值投资**: 关注长期价值增长潜力
2. **趋势跟踪**: 跟随主要趋势方向进行投资
3. **分批建仓**: 降低单次投资风险
4. **定期评估**: 建议每月重新评估投资策略

### 关键价位参考
- **支撑位**: 基于技术分析确定的关键支撑价位
- **阻力位**: 需要关注的重要阻力水平
- **目标价位**: 基于分析模型预测的合理价位区间

---

## ⚡ 免责声明

本报告基于历史数据和技术分析生成，仅供投资参考，不构成投资建议。CS2饰品市场存在较高风险，投资者应：

1. **充分了解风险**: CS2饰品价格波动较大
2. **理性投资**: 不要投入超过承受能力的资金
3. **多元化投资**: 不要将所有资金投入单一饰品
4. **持续关注**: 市场情况可能快速变化

**投资有风险，入市需谨慎！**

---

*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*分析系统: SteamDT集成分析系统 v1.0*
"""

        return report_content


async def main():
    """主函数 - 示例用法"""
    if len(sys.argv) < 2:
        print("使用方法: python integrated_analysis_system.py <饰品URL> [饰品名称]")
        print("示例: python integrated_analysis_system.py \"https://steamdt.com/cs2/Desert%20Eagle%20%7C%20Printstream%20(Field-Tested)\"")
        return
    
    item_url = sys.argv[1]
    item_name = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 创建集成分析系统
    system = IntegratedAnalysisSystem()
    
    # 运行完整分析
    result = await system.run_complete_analysis(item_url, item_name)
    
    if result['success']:
        print(f"\n🎉 集成分析成功完成！")
        print(f"📊 分析结果已保存")
    else:
        print(f"\n❌ 集成分析失败: {result['error']}")


if __name__ == "__main__":
    asyncio.run(main())
