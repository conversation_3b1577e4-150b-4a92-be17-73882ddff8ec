"""
报告生成服务

提供投资分析报告的生成功能。
"""

import json
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from jinja2 import Template
from loguru import logger

from .screening_service import ScreeningService
from ..config.settings import get_settings


class ReportService:
    """报告生成服务"""
    
    def __init__(self):
        self.screening_service = ScreeningService()
        self.settings = get_settings()
        self.logger = logger.bind(service="ReportService")
    
    def generate_investment_report(self, output_format: str = 'html', 
                                 days: int = 7) -> Dict[str, Any]:
        """生成投资分析报告
        
        Args:
            output_format: 输出格式 (html/json/txt)
            days: 分析天数
            
        Returns:
            Dict[str, Any]: 报告生成结果
        """
        self.logger.info(f"开始生成投资报告: {output_format} 格式, {days} 天数据")
        
        try:
            # 收集报告数据
            report_data = self._collect_report_data(days)
            
            # 生成报告文件
            if output_format.lower() == 'html':
                file_path = self._generate_html_report(report_data)
            elif output_format.lower() == 'json':
                file_path = self._generate_json_report(report_data)
            elif output_format.lower() == 'txt':
                file_path = self._generate_text_report(report_data)
            else:
                raise ValueError(f"不支持的输出格式: {output_format}")
            
            result = {
                'success': True,
                'file_path': str(file_path),
                'format': output_format,
                'generated_at': datetime.now(),
                'data_period_days': days,
                'total_opportunities': len(report_data['opportunities']),
                'algorithms_count': len(report_data['summary']['type_distribution'])
            }
            
            self.logger.info(f"报告生成成功: {file_path}")
            return result
            
        except Exception as e:
            self.logger.error(f"生成投资报告失败: {e}")
            raise
    
    def _collect_report_data(self, days: int) -> Dict[str, Any]:
        """收集报告数据"""
        # 获取投资机会
        opportunities = self.screening_service.get_investment_opportunities(
            min_score=50.0,
            limit=100
        )
        
        # 获取筛选摘要
        summary = self.screening_service.get_screening_summary(days)
        
        # 获取高评分结果
        top_opportunities = self.screening_service.get_investment_opportunities(
            min_score=80.0,
            limit=20
        )
        
        # 按投资类型分组
        opportunities_by_type = {}
        for opp in opportunities:
            inv_type = opp['investment_type']
            if inv_type not in opportunities_by_type:
                opportunities_by_type[inv_type] = []
            opportunities_by_type[inv_type].append(opp)
        
        # 按风险等级分组
        opportunities_by_risk = {}
        for opp in opportunities:
            risk_level = opp['risk_level']
            if risk_level not in opportunities_by_risk:
                opportunities_by_risk[risk_level] = []
            opportunities_by_risk[risk_level].append(opp)
        
        return {
            'generated_at': datetime.now(),
            'period_days': days,
            'opportunities': opportunities,
            'top_opportunities': top_opportunities,
            'opportunities_by_type': opportunities_by_type,
            'opportunities_by_risk': opportunities_by_risk,
            'summary': summary,
            'statistics': {
                'total_opportunities': len(opportunities),
                'high_score_count': len(top_opportunities),
                'buy_recommendations': len([o for o in opportunities if o['recommendation'] == 'BUY']),
                'hold_recommendations': len([o for o in opportunities if o['recommendation'] == 'HOLD']),
                'low_risk_count': len([o for o in opportunities if o['risk_level'] == 'LOW']),
                'medium_risk_count': len([o for o in opportunities if o['risk_level'] == 'MEDIUM']),
                'high_risk_count': len([o for o in opportunities if o['risk_level'] == 'HIGH']),
            }
        }
    
    def _generate_html_report(self, report_data: Dict[str, Any]) -> Path:
        """生成HTML格式报告"""
        template_content = self._get_html_template()
        template = Template(template_content)
        
        html_content = template.render(**report_data)
        
        # 保存文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"investment_report_{timestamp}.html"
        file_path = self.settings.output_path / filename
        
        # 确保输出目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return file_path
    
    def _generate_json_report(self, report_data: Dict[str, Any]) -> Path:
        """生成JSON格式报告"""
        # 处理不能序列化的对象
        serializable_data = self._make_serializable(report_data)
        
        # 保存文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"investment_report_{timestamp}.json"
        file_path = self.settings.output_path / filename
        
        # 确保输出目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(serializable_data, f, ensure_ascii=False, indent=2, default=str)
        
        return file_path
    
    def _generate_text_report(self, report_data: Dict[str, Any]) -> Path:
        """生成文本格式报告"""
        lines = []
        
        # 报告标题
        lines.append("=" * 60)
        lines.append("CS2饰品投资分析报告")
        lines.append("=" * 60)
        lines.append(f"生成时间: {report_data['generated_at']}")
        lines.append(f"分析周期: {report_data['period_days']} 天")
        lines.append("")
        
        # 统计摘要
        stats = report_data['statistics']
        lines.append("📊 统计摘要")
        lines.append("-" * 30)
        lines.append(f"总投资机会: {stats['total_opportunities']} 个")
        lines.append(f"高分机会 (≥80分): {stats['high_score_count']} 个")
        lines.append(f"买入推荐: {stats['buy_recommendations']} 个")
        lines.append(f"持有推荐: {stats['hold_recommendations']} 个")
        lines.append("")
        lines.append("风险分布:")
        lines.append(f"  低风险: {stats['low_risk_count']} 个")
        lines.append(f"  中风险: {stats['medium_risk_count']} 个")
        lines.append(f"  高风险: {stats['high_risk_count']} 个")
        lines.append("")
        
        # 顶级投资机会
        lines.append("🎯 顶级投资机会 (评分≥80)")
        lines.append("-" * 30)
        for i, opp in enumerate(report_data['top_opportunities'][:10], 1):
            lines.append(f"{i}. {opp['item_name']}")
            lines.append(f"   类型: {opp['investment_type']}")
            lines.append(f"   评分: {opp['score']:.1f} | 风险: {opp['risk_level']} | 建议: {opp['recommendation']}")
            lines.append(f"   价格: ¥{opp['current_price']} | 7天变化: {opp['price_change_7d']:.1f}%")
            lines.append("")
        
        # 按投资类型分析
        lines.append("📈 按投资类型分析")
        lines.append("-" * 30)
        for inv_type, opportunities in report_data['opportunities_by_type'].items():
            lines.append(f"{inv_type}: {len(opportunities)} 个机会")
            # 显示前3个
            for i, opp in enumerate(opportunities[:3], 1):
                lines.append(f"  {i}. {opp['item_name']} (评分: {opp['score']:.1f})")
            lines.append("")
        
        # 保存文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"investment_report_{timestamp}.txt"
        file_path = self.settings.output_path / filename
        
        # 确保输出目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        return file_path
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {key: self._make_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, 'to_dict'):
            return obj.to_dict()
        elif isinstance(obj, (datetime, timedelta)):
            return str(obj)
        else:
            return obj
    
    def _get_html_template(self) -> str:
        """获取HTML报告模板"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CS2饰品投资分析报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 2px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .opportunity { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .opportunity h3 { margin: 0 0 10px 0; color: #333; }
        .score { font-weight: bold; color: #28a745; }
        .risk-low { color: #28a745; }
        .risk-medium { color: #ffc107; }
        .risk-high { color: #dc3545; }
        .recommendation-buy { background-color: #d4edda; }
        .recommendation-hold { background-color: #fff3cd; }
        .recommendation-avoid { background-color: #f8d7da; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 CS2饰品投资分析报告</h1>
            <p>生成时间: {{ generated_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
            <p>分析周期: {{ period_days }} 天</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{{ statistics.total_opportunities }}</div>
                <div>总投资机会</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ statistics.high_score_count }}</div>
                <div>高分机会 (≥80)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ statistics.buy_recommendations }}</div>
                <div>买入推荐</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ statistics.low_risk_count }}</div>
                <div>低风险机会</div>
            </div>
        </div>
        
        <h2>🏆 顶级投资机会</h2>
        {% for opp in top_opportunities[:10] %}
        <div class="opportunity recommendation-{{ opp.recommendation.lower() }}">
            <h3>{{ opp.item_name }}</h3>
            <p><strong>类型:</strong> {{ opp.investment_type }}</p>
            <p><strong>评分:</strong> <span class="score">{{ "%.1f"|format(opp.score) }}</span> | 
               <strong>排名:</strong> #{{ opp.rank }} | 
               <strong>风险:</strong> <span class="risk-{{ opp.risk_level.lower() }}">{{ opp.risk_level }}</span> | 
               <strong>建议:</strong> {{ opp.recommendation }}</p>
            <p><strong>价格:</strong> ¥{{ opp.current_price }} | 
               <strong>7天变化:</strong> {{ "%.1f"|format(opp.price_change_7d) }}%</p>
            <p><strong>分析:</strong> {{ opp.analysis_summary }}</p>
        </div>
        {% endfor %}
        
        <h2>📊 按投资类型分析</h2>
        {% for inv_type, opportunities in opportunities_by_type.items() %}
        <h3>{{ inv_type }} ({{ opportunities|length }} 个)</h3>
        {% for opp in opportunities[:5] %}
        <div class="opportunity">
            <strong>{{ opp.item_name }}</strong> - 评分: {{ "%.1f"|format(opp.score) }} | 
            风险: {{ opp.risk_level }} | 建议: {{ opp.recommendation }}
        </div>
        {% endfor %}
        {% endfor %}
    </div>
</body>
</html>
        """
