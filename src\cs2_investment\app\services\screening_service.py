"""
筛选结果服务层

提供投资筛选结果相关的业务逻辑。
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.screening_result_dao import ScreeningResultDAO
from src.cs2_investment.models.screening_result import ScreeningResult


class ScreeningService:
    """筛选结果服务类"""
    
    def __init__(self):
        self.screening_dao = ScreeningResultDAO()
    
    def search_screening_results(self,
                                investment_types: Optional[List[str]] = None,
                                score_min: Optional[float] = None,
                                score_max: Optional[float] = None,
                                risk_levels: Optional[List[str]] = None,
                                price_min: Optional[float] = None,
                                price_max: Optional[float] = None,
                                time_range: str = "7d",
                                sort_by: str = "score_desc",
                                limit: int = 1000,
                                offset: int = 0) -> List[Dict[str, Any]]:
        """搜索筛选结果"""
        try:
            # 计算时间范围
            days_map = {
                "1d": 1,
                "3d": 3,
                "7d": 7,
                "30d": 30
            }
            days = days_map.get(time_range, 7)
            
            # 获取筛选结果
            if investment_types and len(investment_types) == 1:
                # 单个投资类型
                results = self.screening_dao.get_results_by_type(
                    investment_types[0], days=days
                )
            else:
                # 多个投资类型或全部
                results = self.screening_dao.get_latest_results(limit=limit * 2)
            
            # 转换为字典格式并应用筛选
            filtered_results = []
            for result in results:
                # 投资类型筛选
                if investment_types and result.investment_type not in investment_types:
                    continue
                
                # 评分筛选
                if score_min is not None and (result.score is None or result.score < score_min):
                    continue
                if score_max is not None and (result.score is None or result.score > score_max):
                    continue
                
                # 风险等级筛选
                if risk_levels and result.risk_level not in risk_levels:
                    continue
                
                # 价格筛选
                if price_min is not None and (result.current_price is None or result.current_price < price_min):
                    continue
                if price_max is not None and (result.current_price is None or result.current_price > price_max):
                    continue
                
                # 构建结果字典 - 立即获取所有属性避免Session问题
                result_dict = {
                    'id': result.id,
                    'item_id': result.item_id,
                    'item_name': getattr(result.item, 'name', '未知饰品') if result.item else '未知饰品',
                    'investment_type': result.investment_type,
                    'score': float(result.score) if result.score else 0,
                    'rank': getattr(result, 'rank', 0),
                    'confidence': float(result.confidence) if result.confidence else 0,
                    'current_price': float(result.current_price) if result.current_price else 0,
                    'price_change_7d': float(result.price_change_7d) if result.price_change_7d else 0,
                    'price_change_30d': float(getattr(result, 'price_change_30d', 0)) if getattr(result, 'price_change_30d', None) else 0,
                    'volume_30d': getattr(result, 'volume_30d', 0),
                    'amount_30d': float(getattr(result, 'amount_30d', 0)) if getattr(result, 'amount_30d', None) else 0,
                    'hot_rank': getattr(result, 'hot_rank', None),
                    'risk_level': result.risk_level,
                    'recommendation': result.recommendation,
                    'analysis_summary': result.analysis_summary,
                    'screening_time': result.screening_time,
                    'algorithm_version': getattr(result, 'algorithm_version', None)
                }
                
                filtered_results.append(result_dict)
            
            # 排序
            if sort_by == "score_desc":
                filtered_results.sort(key=lambda x: x['score'], reverse=True)
            elif sort_by == "score_asc":
                filtered_results.sort(key=lambda x: x['score'])
            elif sort_by == "price_desc":
                filtered_results.sort(key=lambda x: x['current_price'], reverse=True)
            elif sort_by == "price_asc":
                filtered_results.sort(key=lambda x: x['current_price'])
            elif sort_by == "time_desc":
                filtered_results.sort(key=lambda x: x['screening_time'], reverse=True)
            
            # 限制结果数量
            return filtered_results[offset:offset + limit]
            
        except Exception as e:
            print(f"搜索筛选结果失败: {e}")
            return []
    
    def get_screening_detail(self, screening_id: int) -> Optional[Dict[str, Any]]:
        """获取筛选结果详情"""
        try:
            result = self.screening_dao.get_by_id(screening_id)
            if not result:
                return None
            
            return {
                'id': result.id,
                'item_id': result.item_id,
                'item_name': getattr(result.item, 'name', '未知饰品') if result.item else '未知饰品',
                'item_type': getattr(result.item, 'item_type', None) if result.item else None,
                'item_quality': getattr(result.item, 'quality', None) if result.item else None,
                'item_rarity': getattr(result.item, 'rarity', None) if result.item else None,
                'investment_type': result.investment_type,
                'score': float(result.score) if result.score else 0,
                'rank': getattr(result, 'rank', 0),
                'confidence': float(result.confidence) if result.confidence else 0,
                'current_price': float(result.current_price) if result.current_price else 0,
                'price_change_7d': float(result.price_change_7d) if result.price_change_7d else 0,
                'price_change_30d': float(getattr(result, 'price_change_30d', 0)) if getattr(result, 'price_change_30d', None) else 0,
                'volume_30d': getattr(result, 'volume_30d', 0),
                'amount_30d': float(getattr(result, 'amount_30d', 0)) if getattr(result, 'amount_30d', None) else 0,
                'hot_rank': getattr(result, 'hot_rank', None),
                'risk_level': result.risk_level,
                'recommendation': result.recommendation,
                'analysis_summary': result.analysis_summary,
                'screening_time': result.screening_time,
                'algorithm_version': getattr(result, 'algorithm_version', None)
            }
            
        except Exception as e:
            print(f"获取筛选结果详情失败: {e}")
            return None
    
    def get_investment_types(self) -> List[str]:
        """获取可用的投资类型"""
        try:
            # 从数据库获取不重复的投资类型
            results = self.screening_dao.get_latest_results(limit=1000)
            types = set()
            for result in results:
                if result.investment_type:
                    types.add(result.investment_type)
            return sorted(list(types))
        except Exception as e:
            print(f"获取投资类型失败: {e}")
            return [
                "蓝筹核心资产",
                "供应冲击恢复",
                "赛事周期资产",
                "热度上升资产",
                "价值洼地资产"
            ]
    
    def get_screening_statistics(self) -> Dict[str, Any]:
        """获取筛选结果统计信息"""
        try:
            # 获取最新结果
            latest_results = self.screening_dao.get_latest_results(limit=1000)
            
            if not latest_results:
                return {}
            
            # 计算统计信息
            total_count = len(latest_results)
            avg_score = sum(float(r.score) if r.score else 0 for r in latest_results) / total_count
            high_score_count = sum(1 for r in latest_results if r.score and r.score >= 85)
            
            # 按投资类型统计
            type_stats = {}
            for result in latest_results:
                type_name = result.investment_type
                if type_name not in type_stats:
                    type_stats[type_name] = 0
                type_stats[type_name] += 1
            
            # 按风险等级统计
            risk_stats = {}
            for result in latest_results:
                risk_level = result.risk_level or 'UNKNOWN'
                if risk_level not in risk_stats:
                    risk_stats[risk_level] = 0
                risk_stats[risk_level] += 1
            
            # 最新筛选时间
            latest_time = max(r.screening_time for r in latest_results if r.screening_time)
            
            return {
                'total_count': total_count,
                'average_score': avg_score,
                'high_score_count': high_score_count,
                'type_distribution': type_stats,
                'risk_distribution': risk_stats,
                'latest_screening_time': latest_time
            }
            
        except Exception as e:
            print(f"获取筛选统计失败: {e}")
            return {}
    
    def count_search_results(self,
                           investment_types: Optional[List[str]] = None,
                           score_min: Optional[float] = None,
                           score_max: Optional[float] = None,
                           risk_levels: Optional[List[str]] = None,
                           price_min: Optional[float] = None,
                           price_max: Optional[float] = None,
                           time_range: str = "7d") -> int:
        """统计搜索结果数量"""
        try:
            results = self.search_screening_results(
                investment_types=investment_types,
                score_min=score_min,
                score_max=score_max,
                risk_levels=risk_levels,
                price_min=price_min,
                price_max=price_max,
                time_range=time_range,
                limit=10000  # 设置一个较大的限制来获取总数
            )
            return len(results)
        except Exception as e:
            print(f"统计搜索结果失败: {e}")
            return 0
