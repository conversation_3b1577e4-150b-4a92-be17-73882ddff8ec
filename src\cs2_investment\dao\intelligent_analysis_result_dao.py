"""
智能分析结果数据访问对象

提供智能分析结果的数据库操作功能，包括CRUD操作、时间序列查询、
决策统计分析等业务特定方法。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy import desc, func, and_, or_
from sqlalchemy.exc import SQLAlchemyError
from loguru import logger

from .base_dao import BaseDAO
from ..models.intelligent_analysis_result import IntelligentAnalysisResult
from ..config.database import get_db_session


class IntelligentAnalysisResultDAO(BaseDAO[IntelligentAnalysisResult]):
    """智能分析结果DAO"""
    
    def __init__(self):
        super().__init__(IntelligentAnalysisResult)
        self.logger = logger.bind(dao=self.__class__.__name__)
    
    def create_analysis_result(self, analysis_data: Dict[str, Any]) -> Optional[IntelligentAnalysisResult]:
        """创建智能分析结果记录"""
        try:
            with get_db_session() as session:
                result = IntelligentAnalysisResult(**analysis_data)
                session.add(result)
                session.flush()
                session.refresh(result)
                self.logger.info(f"创建智能分析结果成功: {result.id} - {result.item_id}")
                
                # 分离对象避免Session绑定问题
                session.expunge(result)
                return result
        except SQLAlchemyError as e:
            self.logger.error(f"创建智能分析结果失败: {e}")
            raise
    
    def get_latest_analysis(self, item_id: str) -> Optional[IntelligentAnalysisResult]:
        """获取最新的智能分析结果"""
        try:
            with get_db_session() as session:
                result = session.query(IntelligentAnalysisResult).filter(
                    IntelligentAnalysisResult.item_id == item_id
                ).order_by(desc(IntelligentAnalysisResult.analysis_timestamp)).first()
                
                if result:
                    session.expunge(result)
                
                return result
        except SQLAlchemyError as e:
            self.logger.error(f"获取最新智能分析结果失败: {e}")
            raise
    
    def get_analysis_history(self, item_id: str, days: int = 30) -> List[IntelligentAnalysisResult]:
        """获取分析历史记录"""
        try:
            with get_db_session() as session:
                start_date = datetime.now() - timedelta(days=days)
                
                results = session.query(IntelligentAnalysisResult).filter(
                    and_(
                        IntelligentAnalysisResult.item_id == item_id,
                        IntelligentAnalysisResult.analysis_timestamp >= start_date
                    )
                ).order_by(desc(IntelligentAnalysisResult.analysis_timestamp)).all()
                
                for result in results:
                    session.expunge(result)
                
                return results
        except SQLAlchemyError as e:
            self.logger.error(f"获取分析历史失败: {e}")
            raise
    
    def get_by_final_action(self, action: str, limit: int = 100) -> List[IntelligentAnalysisResult]:
        """根据最终行动建议获取分析结果"""
        try:
            with get_db_session() as session:
                results = session.query(IntelligentAnalysisResult).filter(
                    IntelligentAnalysisResult.final_action == action
                ).order_by(desc(IntelligentAnalysisResult.analysis_timestamp)).limit(limit).all()
                
                for result in results:
                    session.expunge(result)
                
                return results
        except SQLAlchemyError as e:
            self.logger.error(f"根据最终行动获取分析结果失败: {e}")
            raise
    
    def get_high_confidence_signals(self, confidence_levels: List[str] = None, limit: int = 50) -> List[IntelligentAnalysisResult]:
        """获取高置信度信号"""
        if confidence_levels is None:
            confidence_levels = ['HIGH', 'VERY_HIGH']
        
        try:
            with get_db_session() as session:
                results = session.query(IntelligentAnalysisResult).filter(
                    IntelligentAnalysisResult.confidence_level.in_(confidence_levels)
                ).order_by(desc(IntelligentAnalysisResult.analysis_timestamp)).limit(limit).all()
                
                for result in results:
                    session.expunge(result)
                
                return results
        except SQLAlchemyError as e:
            self.logger.error(f"获取高置信度信号失败: {e}")
            raise
    
    def get_consistent_signals(self, consistency_level: str = 'HIGH', limit: int = 50) -> List[IntelligentAnalysisResult]:
        """获取信号一致性高的分析结果"""
        try:
            with get_db_session() as session:
                results = session.query(IntelligentAnalysisResult).filter(
                    IntelligentAnalysisResult.signal_consistency == consistency_level
                ).order_by(desc(IntelligentAnalysisResult.analysis_timestamp)).limit(limit).all()
                
                for result in results:
                    session.expunge(result)
                
                return results
        except SQLAlchemyError as e:
            self.logger.error(f"获取一致性信号失败: {e}")
            raise
    
    def get_analysis_statistics(self, days: int = 30) -> Dict[str, Any]:
        """获取分析统计信息"""
        try:
            with get_db_session() as session:
                start_date = datetime.now() - timedelta(days=days)
                
                # 总分析次数
                total_count = session.query(IntelligentAnalysisResult).filter(
                    IntelligentAnalysisResult.analysis_timestamp >= start_date
                ).count()
                
                # 按最终行动分组统计
                action_stats = session.query(
                    IntelligentAnalysisResult.final_action,
                    func.count(IntelligentAnalysisResult.id).label('count')
                ).filter(
                    IntelligentAnalysisResult.analysis_timestamp >= start_date
                ).group_by(IntelligentAnalysisResult.final_action).all()
                
                # 按置信度分组统计
                confidence_stats = session.query(
                    IntelligentAnalysisResult.confidence_level,
                    func.count(IntelligentAnalysisResult.id).label('count')
                ).filter(
                    IntelligentAnalysisResult.analysis_timestamp >= start_date
                ).group_by(IntelligentAnalysisResult.confidence_level).all()
                
                return {
                    'total_analyses': total_count,
                    'action_distribution': {action: count for action, count in action_stats},
                    'confidence_distribution': {level: count for level, count in confidence_stats},
                    'period_days': days
                }
        except SQLAlchemyError as e:
            self.logger.error(f"获取分析统计失败: {e}")
            raise
    
    def batch_create(self, analysis_data_list: List[Dict[str, Any]]) -> List[IntelligentAnalysisResult]:
        """批量创建分析结果"""
        try:
            with get_db_session() as session:
                results = []
                for data in analysis_data_list:
                    result = IntelligentAnalysisResult(**data)
                    results.append(result)
                
                session.add_all(results)
                session.flush()
                
                for result in results:
                    session.refresh(result)
                    session.expunge(result)
                
                self.logger.info(f"批量创建智能分析结果成功: {len(results)}条")
                return results
        except SQLAlchemyError as e:
            self.logger.error(f"批量创建分析结果失败: {e}")
            raise
    
    def delete_old_records(self, days: int = 90) -> int:
        """删除过期记录"""
        try:
            with get_db_session() as session:
                cutoff_date = datetime.now() - timedelta(days=days)
                
                deleted_count = session.query(IntelligentAnalysisResult).filter(
                    IntelligentAnalysisResult.analysis_timestamp < cutoff_date
                ).delete()
                
                self.logger.info(f"删除过期分析记录: {deleted_count}条")
                return deleted_count
        except SQLAlchemyError as e:
            self.logger.error(f"删除过期记录失败: {e}")
            raise
