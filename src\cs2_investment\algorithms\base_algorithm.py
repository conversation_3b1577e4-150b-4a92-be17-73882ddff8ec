"""
基础筛选算法

定义所有投资筛选算法的基础类和通用方法。
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from loguru import logger

from ..config.database import get_db_session
from ..models.market_snapshot import MarketSnapshot
from ..models.item import Item
from ..models.screening_result import ScreeningResult


class BaseScreeningAlgorithm(ABC):
    """基础筛选算法类"""
    
    def __init__(self, algorithm_name: str, investment_type: str, version: str = "1.0"):
        self.algorithm_name = algorithm_name
        self.investment_type = investment_type
        self.version = version
        self.logger = logger.bind(algorithm=algorithm_name)
    
    @abstractmethod
    def get_screening_query(self, session: Session) -> Any:
        """获取筛选查询
        
        子类必须实现此方法，返回SQLAlchemy查询对象
        """
        pass
    
    @abstractmethod
    def calculate_score(self, snapshot: MarketSnapshot) -> float:
        """计算投资评分
        
        Args:
            snapshot: 市场快照数据
            
        Returns:
            float: 投资评分 (0-100)
        """
        pass
    
    def calculate_confidence(self, snapshot: MarketSnapshot) -> float:
        """计算置信度
        
        Args:
            snapshot: 市场快照数据
            
        Returns:
            float: 置信度 (0-100)
        """
        # 默认置信度计算逻辑
        confidence = 50.0  # 基础置信度
        
        # 根据数据完整性调整置信度
        if snapshot.current_price:
            confidence += 10
        if snapshot.trans_count_1m and snapshot.trans_count_1m > 0:
            confidence += 10
        if snapshot.hot_rank and snapshot.hot_rank < 1000:
            confidence += 10
        if snapshot.survive_num:
            confidence += 10
        
        return min(confidence, 100.0)
    
    def determine_risk_level(self, snapshot: MarketSnapshot, score: float) -> str:
        """确定风险等级
        
        Args:
            snapshot: 市场快照数据
            score: 投资评分
            
        Returns:
            str: 风险等级 (LOW/MEDIUM/HIGH)
        """
        # 基于价格波动性判断风险
        volatility = 0
        if snapshot.diff_7d:
            volatility += abs(float(snapshot.diff_7d))
        if snapshot.diff_1m:
            volatility += abs(float(snapshot.diff_1m))
        
        # 基于流动性判断风险
        liquidity_risk = 0
        if not snapshot.trans_count_1m or snapshot.trans_count_1m < 10:
            liquidity_risk += 20
        if not snapshot.sell_nums or snapshot.sell_nums < 5:
            liquidity_risk += 20
        
        total_risk = volatility + liquidity_risk
        
        if total_risk < 30:
            return "LOW"
        elif total_risk < 60:
            return "MEDIUM"
        else:
            return "HIGH"
    
    def generate_recommendation(self, score: float, risk_level: str) -> str:
        """生成投资建议
        
        Args:
            score: 投资评分
            risk_level: 风险等级
            
        Returns:
            str: 投资建议 (BUY/HOLD/SELL/AVOID)
        """
        if score >= 80:
            return "BUY" if risk_level != "HIGH" else "HOLD"
        elif score >= 60:
            return "HOLD" if risk_level == "LOW" else "AVOID"
        elif score >= 40:
            return "HOLD" if risk_level == "LOW" else "AVOID"
        else:
            return "AVOID"
    
    def generate_analysis_summary(self, snapshot: MarketSnapshot, score: float) -> str:
        """生成分析摘要
        
        Args:
            snapshot: 市场快照数据
            score: 投资评分
            
        Returns:
            str: 分析摘要
        """
        summary_parts = []
        
        # 价格分析
        if snapshot.current_price:
            summary_parts.append(f"当前价格: ¥{snapshot.current_price}")
        
        if snapshot.diff_7d:
            change_desc = "上涨" if float(snapshot.diff_7d) > 0 else "下跌"
            summary_parts.append(f"7天{change_desc}: {abs(float(snapshot.diff_7d)):.1f}%")
        
        # 成交量分析
        if snapshot.trans_count_1m:
            summary_parts.append(f"月成交量: {snapshot.trans_count_1m}")
        
        # 热度分析
        if snapshot.hot_rank:
            summary_parts.append(f"热度排名: #{snapshot.hot_rank}")
        
        # 评分总结
        if score >= 80:
            summary_parts.append("综合评价: 优秀")
        elif score >= 60:
            summary_parts.append("综合评价: 良好")
        elif score >= 40:
            summary_parts.append("综合评价: 一般")
        else:
            summary_parts.append("综合评价: 较差")
        
        return "; ".join(summary_parts)
    
    def run_screening(self, limit: int = 100) -> List[ScreeningResult]:
        """运行筛选算法
        
        Args:
            limit: 返回结果数量限制
            
        Returns:
            List[ScreeningResult]: 筛选结果列表
        """
        try:
            with get_db_session() as session:
                # 获取筛选查询
                query = self.get_screening_query(session)
                snapshots = query.limit(limit * 2).all()  # 获取更多数据以便筛选
                
                if not snapshots:
                    self.logger.warning(f"没有找到符合条件的数据: {self.investment_type}")
                    return []
                
                results = []
                screening_time = datetime.now()
                
                for data in snapshots:
                    try:
                        # 检查数据类型，支持单表查询和联合查询
                        if isinstance(data, tuple):
                            # 联合查询结果 (MarketSnapshot, AnalysisResult) - 旧版本SQLAlchemy
                            snapshot = data[0]
                            item_data = data
                            item_id = snapshot.item_id
                        elif hasattr(data, '_fields') or hasattr(data, '_mapping'):
                            # SQLAlchemy Row对象 - 新版本联合查询结果
                            # 假设第一列是MarketSnapshot，第二列是AnalysisResult
                            snapshot = data[0]  # 第一列：MarketSnapshot
                            analysis_result = data[1] if len(data) > 1 else None  # 第二列：AnalysisResult
                            item_data = data
                            item_id = snapshot.item_id
                        else:
                            # 单表查询结果 (MarketSnapshot)
                            snapshot = data
                            item_data = data
                            item_id = snapshot.item_id

                        # 计算评分
                        score = self.calculate_score(item_data)
                        confidence = self.calculate_confidence(snapshot)
                        risk_level = self.determine_risk_level(item_data, score)
                        recommendation = self.generate_recommendation(score, risk_level)
                        analysis_summary = self.generate_analysis_summary(item_data, score)

                        # 创建筛选结果
                        result = ScreeningResult(
                            item_id=item_id,
                            screening_time=screening_time,
                            investment_type=self.investment_type,
                            algorithm_version=self.version,
                            score=score,
                            confidence=confidence,
                            current_price=snapshot.current_price,
                            price_change_7d=snapshot.diff_7d,
                            price_change_30d=snapshot.diff_1m,
                            volume_30d=snapshot.trans_count_1m,
                            amount_30d=snapshot.trans_amount_1m,
                            hot_rank=snapshot.hot_rank,
                            analysis_summary=analysis_summary,
                            risk_level=risk_level,
                            recommendation=recommendation
                        )

                        results.append(result)

                    except Exception as e:
                        item_id = data[0].item_id if isinstance(data, tuple) else data.item_id
                        self.logger.error(f"处理数据失败: {item_id}, 错误: {e}")
                        continue
                
                # 按评分排序并限制数量
                results.sort(key=lambda x: x.score, reverse=True)
                results = results[:limit]
                
                # 设置排名
                for rank, result in enumerate(results, 1):
                    result.rank = rank
                
                self.logger.info(f"筛选完成: {self.investment_type}, 结果数量: {len(results)}")
                return results
                
        except Exception as e:
            self.logger.error(f"运行筛选算法失败: {self.investment_type}, 错误: {e}")
            raise
    
    def get_latest_snapshots_query(self, session: Session, hours: int = 24):
        """获取最新快照的基础查询
        
        Args:
            session: 数据库会话
            hours: 时间范围（小时）
            
        Returns:
            Query: SQLAlchemy查询对象
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        return session.query(MarketSnapshot)\
            .join(Item)\
            .filter(MarketSnapshot.snapshot_time >= cutoff_time)\
            .filter(MarketSnapshot.current_price.isnot(None))
