"""
抽象数据抓取接口

定义统一的数据抓取接口规范，为不同的抓取实现（playwright、API等）提供统一的接口标准。
"""

from abc import ABC, abstractmethod
from typing import Dict, Optional, Any
from loguru import logger

from .data_models import ScrapingResult


class AbstractDataScraper(ABC):
    """抽象数据抓取器基类
    
    定义统一的抓取接口规范，所有具体的抓取器实现都应该继承此类。
    支持playwright抓取、API抓取等多种实现方式。
    """
    
    def __init__(self):
        """初始化抽象抓取器"""
        self.logger = logger.bind(scraper=self.__class__.__name__)
        self._is_started = False
    
    @abstractmethod
    async def start(self) -> None:
        """启动抓取器
        
        初始化抓取器所需的资源，如浏览器、API客户端等。
        子类必须实现此方法。
        
        Raises:
            Exception: 启动失败时抛出异常
        """
        pass
    
    @abstractmethod
    async def stop(self) -> None:
        """停止抓取器
        
        清理抓取器使用的资源，如关闭浏览器、断开连接等。
        子类必须实现此方法。
        
        Raises:
            Exception: 停止失败时抛出异常
        """
        pass
    
    @abstractmethod
    async def scrape_item_data(self, item_url: str, data_requirements: Optional[Dict[str, bool]] = None) -> ScrapingResult:
        """抓取单个饰品的数据
        
        这是核心抓取方法，所有抓取器实现都必须提供此功能。
        
        Args:
            item_url: 饰品页面URL，格式如 https://steamdt.com/cs2/AK-47%20%7C%20Redline%20(Field-Tested)
            data_requirements: 数据需求配置，指定需要抓取哪些类型的数据
                - daily_kline_1: bool - 是否抓取日K数据第一次响应
                - daily_kline_2: bool - 是否抓取日K数据第二次响应  
                - weekly_kline: bool - 是否抓取周K数据
                - hourly_kline: bool - 是否抓取时K数据
                - trend_data_3m: bool - 是否抓取3个月趋势数据（小时级）
                - trend_data_6m: bool - 是否抓取6个月趋势数据（日级）
                如果为None，则抓取所有类型的数据
        
        Returns:
            ScrapingResult: 抓取结果对象，包含以下字段：
                - success: bool - 抓取是否成功
                - item_info: Optional[ItemInfo] - 饰品基本信息
                - trend_data: Optional[TrendData] - 主要趋势数据（向后兼容）
                - trend_data_3m: Optional[TrendData] - 3个月趋势数据
                - trend_data_6m: Optional[TrendData] - 6个月趋势数据
                - hourly_kline: Optional[KlineData] - 时K数据
                - daily_kline: Optional[KlineData] - 日K数据（向后兼容）
                - daily_kline_1: Optional[KlineData] - 日K数据第一次响应
                - daily_kline_2: Optional[KlineData] - 日K数据第二次响应
                - weekly_kline: Optional[KlineData] - 周K数据
                - error_message: Optional[str] - 错误信息（失败时）
                - collected_at: Optional[datetime] - 数据收集时间
        
        Raises:
            Exception: 抓取过程中发生错误时抛出异常
        """
        pass
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.stop()
    
    @property
    def is_started(self) -> bool:
        """检查抓取器是否已启动"""
        return self._is_started
    
    def get_default_data_requirements(self) -> Dict[str, bool]:
        """获取默认的数据需求配置
        
        Returns:
            Dict[str, bool]: 默认配置，抓取所有类型的数据
        """
        return {
            'daily_kline_1': True,
            'daily_kline_2': True,
            'weekly_kline': True,
            'hourly_kline': True,
            'trend_data_3m': True,
            'trend_data_6m': True
        }
    
    def validate_data_requirements(self, data_requirements: Optional[Dict[str, bool]]) -> Dict[str, bool]:
        """验证并标准化数据需求配置
        
        Args:
            data_requirements: 用户提供的数据需求配置
            
        Returns:
            Dict[str, bool]: 验证后的标准化配置
        """
        if data_requirements is None:
            return self.get_default_data_requirements()
        
        # 获取默认配置作为基础
        validated_requirements = self.get_default_data_requirements()
        
        # 更新用户指定的配置
        for key, value in data_requirements.items():
            if key in validated_requirements:
                validated_requirements[key] = bool(value)
            else:
                self.logger.warning(f"未知的数据需求配置项: {key}")
        
        return validated_requirements
    
    def log_scraping_start(self, item_url: str, data_requirements: Dict[str, bool]) -> None:
        """记录抓取开始日志
        
        Args:
            item_url: 饰品URL
            data_requirements: 数据需求配置
        """
        enabled_requirements = [k for k, v in data_requirements.items() if v]
        self.logger.info(f"开始抓取饰品数据: {item_url}")
        self.logger.info(f"数据需求: {enabled_requirements}")
    
    def log_scraping_result(self, result: ScrapingResult, item_url: str) -> None:
        """记录抓取结果日志
        
        Args:
            result: 抓取结果
            item_url: 饰品URL
        """
        if result.success:
            data_summary = []
            if result.trend_data_3m:
                data_summary.append("3个月趋势")
            if result.trend_data_6m:
                data_summary.append("6个月趋势")
            if result.hourly_kline:
                data_summary.append("时K")
            if result.daily_kline_1:
                data_summary.append("日K1")
            if result.daily_kline_2:
                data_summary.append("日K2")
            if result.weekly_kline:
                data_summary.append("周K")
            
            self.logger.info(f"✅ 抓取成功: {item_url}")
            self.logger.info(f"📊 获取数据: {', '.join(data_summary)}")
        else:
            self.logger.error(f"❌ 抓取失败: {item_url}")
            if result.error_message:
                self.logger.error(f"错误信息: {result.error_message}")


class ScraperError(Exception):
    """抓取器异常基类"""
    pass


class ScraperStartupError(ScraperError):
    """抓取器启动异常"""
    pass


class ScraperShutdownError(ScraperError):
    """抓取器关闭异常"""
    pass


class ScrapingDataError(ScraperError):
    """数据抓取异常"""
    pass


class ScrapingTimeoutError(ScraperError):
    """抓取超时异常"""
    pass


class ScrapingValidationError(ScraperError):
    """数据验证异常"""
    pass
