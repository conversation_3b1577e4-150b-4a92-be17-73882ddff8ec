#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品图表化分析演示
展示如何使用专业技术指标图表提升分析说服力
"""

from real_time_monitor import FixedRealTimeMonitor

def demonstrate_chart_analysis():
    """演示图表化分析功能"""
    print("📊 CS2饰品专业图表化分析演示")
    print("=" * 60)
    
    # 分析的饰品列表
    skins = [
        'AK-47 传承 (久经沙场)',
        'M4A1 消音型印花集', 
        '沙漠之鹰 印花集 (久经沙场)'
    ]
    
    for i, skin_name in enumerate(skins, 1):
        print(f"\n{i}. 🎯 分析 {skin_name}")
        print("-" * 50)
        
        try:
            # 创建监控器
            monitor = FixedRealTimeMonitor(skin_name)
            
            if monitor.load_data():
                print("✅ 数据加载成功")
                
                # 显示基本分析
                print("\n📊 基础技术分析:")
                
                # 获取关键指标
                rsi = monitor._calculate_rsi_real_time()
                macd_data = monitor._calculate_macd_real_time()
                enhanced_signals = monitor.generate_enhanced_real_time_signals()
                volume_anomaly = monitor.detect_volume_anomaly_real_time()
                
                # 显示关键数据
                current_price = monitor.hourly_data['close'].iloc[-1]
                current_rsi = rsi.iloc[-1] if len(rsi) > 0 else 50
                current_macd = macd_data['macd'].iloc[-1] if len(macd_data['macd']) > 0 else 0
                current_signal = macd_data['signal'].iloc[-1] if len(macd_data['signal']) > 0 else 0
                macd_trend = "金叉" if current_macd > current_signal else "死叉"
                
                print(f"   💰 当前价格: ¥{current_price:.2f}")
                print(f"   📊 RSI(14): {current_rsi:.1f}")
                print(f"   📈 MACD: {macd_trend} ({current_macd:.3f})")
                print(f"   🚀 主要信号: {enhanced_signals['signal']} (置信度: {enhanced_signals['confidence']:.1f}%)")
                print(f"   📊 成交量状态: {volume_anomaly['alert']}")
                
                # 生成专业图表
                print(f"\n📈 生成专业技术指标图表...")
                chart_path = monitor.generate_technical_chart()
                
                if chart_path:
                    print(f"✅ 图表已生成: {chart_path}")
                    print(f"   📊 图表包含:")
                    print(f"     • K线图 + 移动平均线 + 买卖信号标注")
                    print(f"     • RSI指标 + 超买超卖区域标注")
                    print(f"     • MACD指标 + 金叉死叉点标注")
                    print(f"     • 成交量图 + 异常检测标注")
                else:
                    print("❌ 图表生成失败")
                
                print(f"\n💡 分析建议:")
                if enhanced_signals['signal'] == 'BUY':
                    print(f"   🟢 建议关注买入机会")
                    print(f"   📈 技术面支持上涨")
                elif enhanced_signals['signal'] == 'SELL':
                    print(f"   🔴 建议谨慎，考虑减仓")
                    print(f"   📉 技术面显示下跌风险")
                else:
                    print(f"   🟡 建议观望，等待更明确信号")
                
            else:
                print(f"❌ {skin_name} 数据加载失败")
                
        except Exception as e:
            print(f"❌ {skin_name} 分析失败: {e}")
        
        print("\n" + "="*50)
    
    print(f"\n🎯 图表化分析的优势:")
    print(f"   ✅ 直观展示价格走势和技术指标")
    print(f"   ✅ 清晰标注买卖信号和关键点位")
    print(f"   ✅ 专业的技术分析图表样式")
    print(f"   ✅ 提升分析报告的说服力")
    print(f"   ✅ 便于保存和分享分析结果")
    
    print(f"\n💡 使用建议:")
    print(f"   📊 定期生成图表，跟踪技术指标变化")
    print(f"   📈 结合图表和数值分析，提高决策准确性")
    print(f"   🎯 重点关注信号标注点，把握交易时机")
    print(f"   📋 保存历史图表，建立分析档案")

def quick_chart_demo(skin_name):
    """快速图表演示"""
    print(f"🚀 快速生成 {skin_name} 的技术分析图表")
    
    monitor = FixedRealTimeMonitor(skin_name)
    if monitor.load_data():
        chart_path = monitor.generate_technical_chart()
        if chart_path:
            print(f"✅ 图表已生成: {chart_path}")
            return chart_path
        else:
            print("❌ 图表生成失败")
            return None
    else:
        print("❌ 数据加载失败")
        return None

if __name__ == "__main__":
    # 运行完整演示
    demonstrate_chart_analysis()
    
    # 或者快速生成单个图表
    # quick_chart_demo('AK-47 传承 (久经沙场)')
