#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品投资分析系统 - 配置文件
系统参数和设置的集中管理
"""

# 系统版本信息
SYSTEM_VERSION = "2.0"
SYSTEM_NAME = "CS2饰品投资分析系统"
LAST_UPDATE = "2025-01-24"

# 数据文件配置
DATA_FILES = {
    'daily_k1': '日k1.json',
    'daily_k2': '日k2.json', 
    'weekly_k': '周k.json',
    'hourly_k': '时k.json',
    'trend_data': '走势.json'
}

# 技术指标参数
TECHNICAL_INDICATORS = {
    'rsi_period': 14,
    'ema_short': 12,
    'ema_long': 26,
    'macd_fast': 12,
    'macd_slow': 26,
    'macd_signal': 9,
    'bollinger_period': 20,
    'bollinger_std': 2,
    'atr_period': 14,
    'volume_ma_period': 20
}

# 风险评估参数
RISK_ASSESSMENT = {
    'volatility_threshold': {
        'low': 0.02,
        'medium': 0.05,
        'high': 0.08
    },
    'risk_score_weights': {
        'volatility': 0.3,
        'trend': 0.25,
        'liquidity': 0.2,
        'anomaly': 0.15,
        'sentiment': 0.1
    },
    'max_risk_score': 10
}

# 异常检测参数
ANOMALY_DETECTION = {
    'volume_spike_threshold': 2.0,
    'price_jump_threshold': 0.05,
    'trend_reversal_threshold': 0.03,
    'volatility_spike_threshold': 1.5,
    'lookback_period': 30
}

# 策略选择参数
STRATEGY_SELECTION = {
    'fitness_score_threshold': 70,
    'confidence_threshold': 0.6,
    'strategy_weights': {
        'market_fit': 0.3,
        'volatility_match': 0.2,
        'risk_tolerance': 0.2,
        'signal_availability': 0.15,
        'base_performance': 0.15
    }
}

# 报告生成配置
REPORT_CONFIG = {
    'max_recent_anomalies': 3,
    'confidence_levels': {
        'very_confident': 0.9,
        'confident': 0.75,
        'somewhat_confident': 0.6,
        'not_confident': 0.5
    },
    'risk_categories': {
        'low': (0, 3),
        'medium': (4, 6),
        'high': (7, 10)
    }
}

# 数据验证配置
DATA_VALIDATION = {
    'min_data_points': 30,
    'required_fields': ['price', 'volume', 'timestamp'],
    'data_quality_threshold': 0.8,
    'max_missing_ratio': 0.1
}

# 系统性能配置
PERFORMANCE_CONFIG = {
    'max_memory_usage': '1GB',
    'calculation_timeout': 300,  # 秒
    'max_concurrent_analysis': 1,
    'cache_enabled': True
}

# 输出格式配置
OUTPUT_FORMAT = {
    'decimal_places': 2,
    'percentage_format': '%.1f%%',
    'currency_symbol': '元',
    'date_format': '%Y年%m月%d日 %H:%M'
}

# 调试和日志配置
DEBUG_CONFIG = {
    'debug_mode': False,
    'log_level': 'INFO',
    'save_intermediate_results': False,
    'verbose_output': True
}

# 市场分类配置
MARKET_CLASSIFICATION = {
    'activity_levels': {
        'high': {'min_daily_volume': 100, 'description': '高活跃交易市场'},
        'medium': {'min_daily_volume': 50, 'description': '中等活跃市场'},
        'low': {'min_daily_volume': 0, 'description': '低活跃市场'}
    },
    'volatility_levels': {
        'high': {'min_volatility': 0.05, 'description': '高波动市场'},
        'medium': {'min_volatility': 0.02, 'description': '中等波动市场'},
        'low': {'min_volatility': 0, 'description': '低波动市场'}
    }
}

# 投资者类型配置
INVESTOR_TYPES = {
    'conservative': {
        'name': '保守型投资者',
        'max_risk_score': 3,
        'preferred_strategies': ['value_investing', 'mean_reversion'],
        'max_position_size': 0.2
    },
    'moderate': {
        'name': '稳健型投资者', 
        'max_risk_score': 6,
        'preferred_strategies': ['trend_following', 'value_investing'],
        'max_position_size': 0.35
    },
    'aggressive': {
        'name': '激进型投资者',
        'max_risk_score': 10,
        'preferred_strategies': ['momentum', 'trend_following'],
        'max_position_size': 0.5
    }
}

# 默认配置函数
def get_default_config():
    """获取默认配置"""
    return {
        'system': {
            'version': SYSTEM_VERSION,
            'name': SYSTEM_NAME,
            'last_update': LAST_UPDATE
        },
        'data_files': DATA_FILES,
        'technical_indicators': TECHNICAL_INDICATORS,
        'risk_assessment': RISK_ASSESSMENT,
        'anomaly_detection': ANOMALY_DETECTION,
        'strategy_selection': STRATEGY_SELECTION,
        'report_config': REPORT_CONFIG,
        'data_validation': DATA_VALIDATION,
        'performance': PERFORMANCE_CONFIG,
        'output_format': OUTPUT_FORMAT,
        'debug': DEBUG_CONFIG,
        'market_classification': MARKET_CLASSIFICATION,
        'investor_types': INVESTOR_TYPES
    }

# 配置验证函数
def validate_config(config):
    """验证配置参数的有效性"""
    
    errors = []
    
    # 验证技术指标参数
    if config['technical_indicators']['rsi_period'] < 1:
        errors.append("RSI周期必须大于0")
    
    if config['technical_indicators']['ema_short'] >= config['technical_indicators']['ema_long']:
        errors.append("EMA短期周期必须小于长期周期")
    
    # 验证风险评估参数
    weights_sum = sum(config['risk_assessment']['risk_score_weights'].values())
    if abs(weights_sum - 1.0) > 0.01:
        errors.append(f"风险评分权重总和必须为1.0，当前为{weights_sum}")
    
    # 验证异常检测参数
    if config['anomaly_detection']['lookback_period'] < 7:
        errors.append("异常检测回看期间不能少于7天")
    
    return errors

# 获取配置摘要
def get_config_summary():
    """获取配置摘要信息"""
    
    config = get_default_config()
    
    return {
        'system_info': f"{config['system']['name']} v{config['system']['version']}",
        'last_update': config['system']['last_update'],
        'technical_indicators_count': len(config['technical_indicators']),
        'supported_strategies': len(STRATEGY_SELECTION),
        'risk_levels': len(config['risk_assessment']['volatility_threshold']),
        'investor_types': len(config['investor_types']),
        'data_files_required': len(config['data_files'])
    }

if __name__ == "__main__":
    # 配置测试
    config = get_default_config()
    errors = validate_config(config)
    
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"   • {error}")
    else:
        print("✅ 配置验证通过")
        
    print("\n📊 配置摘要:")
    summary = get_config_summary()
    for key, value in summary.items():
        print(f"   {key}: {value}")
