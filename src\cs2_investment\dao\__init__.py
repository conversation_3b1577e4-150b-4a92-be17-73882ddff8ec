"""
数据访问层模块

包含所有数据库操作的DAO类。
"""

from .base_dao import BaseDAO
from .item_dao import ItemDAO
from .market_snapshot_dao import MarketSnapshotDAO
from .screening_result_dao import ScreeningResultDAO
from .database_manager import DatabaseManager
from .analysis_result_dao import AnalysisResultDAO, RealtimeAnalysisResultDAO
from .holding_dao import HoldingDAO
from .holding_transaction_dao import HoldingTransactionDAO
from .arbitrage_item_dao import ArbitrageItemDAO

# 智能分析模块DAO
from .intelligent_analysis_result_dao import IntelligentAnalysisResultDAO
from .enhanced_technical_indicators_dao import EnhancedTechnicalIndicatorsDAO
from .supply_demand_analysis_dao import SupplyDemandAnalysisDAO
from .intelligent_monitoring_config_dao import IntelligentMonitoringConfigDAO

__all__ = [
    'BaseDAO',
    'ItemDAO',
    'MarketSnapshotDAO',
    'ScreeningResultDAO',
    'DatabaseManager',
    'AnalysisResultDAO',
    'RealtimeAnalysisResultDAO',
    'HoldingDAO',
    'HoldingTransactionDAO',
    'ArbitrageItemDAO',

    # 智能分析模块DAO
    'IntelligentAnalysisResultDAO',
    'EnhancedTechnicalIndicatorsDAO',
    'SupplyDemandAnalysisDAO',
    'IntelligentMonitoringConfigDAO',
]
