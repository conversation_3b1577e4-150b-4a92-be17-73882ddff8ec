# SteamDT 数据抓取器

基于 Playwright 的 SteamDT 网站饰品数据抓取工具，支持抓取饰品的走势数据和 K 线数据。

## 功能特性

- 🎯 **饰品信息抓取**: 获取饰品基本信息（名称、价格、变化等）
- 📈 **走势数据抓取**: 获取近6个月的价格走势数据
- 📊 **K线数据抓取**: 支持时K、日K、周K三种类型
- 🔄 **智能合并**: 自动处理日K数据的两次响应合并
- 💾 **多格式存储**: 支持JSON和CSV格式数据导出
- 🚀 **批量处理**: 支持多个饰品的批量抓取
- 🛡️ **错误处理**: 完善的异常处理和重试机制

## 安装依赖

```bash
pip install playwright asyncio
playwright install chromium
```

## 快速开始

### 1. 单个饰品抓取

```python
import asyncio
from src.cs2_investment.scraper import SteamDTScraper
from src.cs2_investment.scraper.data_storage import DataStorage

async def scrape_single_item():
    # 饰品URL
    item_url = "https://steamdt.com/cs2/Desert%20Eagle%20%7C%20Printstream%20(Field-Tested)"
    
    # 创建抓取器和存储器
    storage = DataStorage()
    
    async with SteamDTScraper() as scraper:
        # 抓取数据
        result = await scraper.scrape_item_data(item_url)
        
        if result.success:
            print(f"饰品名称: {result.item_info.name}")
            print(f"当前价格: ¥{result.item_info.current_price}")
            
            # 保存数据
            storage.save_scraping_result(result)
        else:
            print(f"抓取失败: {result.error_message}")

# 运行
asyncio.run(scrape_single_item())
```

### 2. 批量抓取

```python
async def scrape_multiple_items():
    item_urls = [
        "https://steamdt.com/cs2/Desert%20Eagle%20%7C%20Printstream%20(Field-Tested)",
        "https://steamdt.com/cs2/AK-47%20%7C%20Redline%20(Field-Tested)"
    ]
    
    storage = DataStorage()
    
    async with SteamDTScraper() as scraper:
        results = await scraper.scrape_multiple_items(item_urls)
        storage.save_batch_results(results)

asyncio.run(scrape_multiple_items())
```

### 3. 使用示例脚本

```bash
cd src/cs2_investment/scraper
python example_usage.py
```

## 数据结构

### 饰品信息 (ItemInfo)
- `item_id`: 饰品ID
- `name`: 饰品名称
- `current_price`: 当前价格
- `price_change_percent`: 价格变化百分比
- `market_hash_name`: 市场哈希名称

### 走势数据 (TrendData)
- `item_id`: 饰品ID
- `time_range`: 时间范围（近6个月）
- `data_points`: 数据点列表
  - `timestamp`: 时间戳
  - `price`: 价格
  - `inventory_count`: 库存数量
  - `volume`: 成交量

### K线数据 (KlineData)
- `item_id`: 饰品ID
- `kline_type`: K线类型（时K/日K/周K）
- `data_points`: K线数据点列表
  - `timestamp`: 时间戳
  - `open_price`: 开盘价
  - `high_price`: 最高价
  - `low_price`: 最低价
  - `close_price`: 收盘价
  - `volume`: 成交量

## 输出文件

抓取的数据会保存在 `output/scraper_data/` 目录下：

```
output/scraper_data/
├── item_info/          # 饰品基本信息
├── trend_data/         # 走势数据
├── kline_data/         # K线数据
└── complete_*.json     # 完整抓取结果
```

每种数据都会同时保存为 JSON 和 CSV 格式。

## API 端点

抓取器使用以下 SteamDT API 端点：

1. **走势数据**: `https://sdt-api.ok-skins.com/user/steam/type-trend/v2/item/details`
2. **K线数据**: `https://sdt-api.ok-skins.com/user/steam/category/v1/kline`

### K线类型参数
- `type=1`: 时K数据
- `type=2`: 日K数据（需要两次请求合并）
- `type=3`: 周K数据

## 配置选项

可以通过修改 `config.py` 来调整抓取器行为：

```python
from src.cs2_investment.scraper.config import ScraperConfig

config = ScraperConfig(
    headless=False,          # 显示浏览器窗口
    request_delay=3.0,       # 增加请求间隔
    max_retries=5,           # 增加重试次数
    output_dir="my_data"     # 自定义输出目录
)
```

## 注意事项

1. **请求频率**: 为避免被反爬，建议设置适当的请求间隔
2. **日K数据**: 日K数据需要两次API请求，抓取器会自动合并结果
3. **网络稳定**: 确保网络连接稳定，避免请求超时
4. **数据时效**: 走势数据默认获取近6个月的数据
5. **浏览器依赖**: 需要安装 Chromium 浏览器

## 错误处理

抓取器包含完善的错误处理机制：

- 自动重试失败的请求
- 详细的错误日志记录
- 优雅的异常处理
- 部分失败时的数据保护

## 日志配置

抓取器会生成详细的日志文件 `scraper.log`，包含：

- 抓取进度信息
- API请求状态
- 错误和异常信息
- 数据保存状态

## 扩展开发

### 添加新的数据类型

1. 在 `data_models.py` 中定义新的数据模型
2. 在 `steamdt_scraper.py` 中添加抓取方法
3. 在 `data_storage.py` 中添加存储逻辑

### 自定义存储格式

继承 `DataStorage` 类并重写相应方法：

```python
class CustomDataStorage(DataStorage):
    def _save_trend_data(self, trend_data, timestamp):
        # 自定义存储逻辑
        pass
```

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款。
