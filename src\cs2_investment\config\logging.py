"""
日志配置管理

配置和管理应用程序的日志系统。
"""

import sys
from pathlib import Path
from loguru import logger

from .settings import get_settings


def setup_logging():
    """设置日志配置"""
    settings = get_settings()
    
    # 移除默认的日志处理器
    logger.remove()
    
    # 确保日志目录存在
    log_file_path = Path(settings.logging.file)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 添加控制台日志处理器
    logger.add(
        sys.stdout,
        format=settings.logging.format,
        level=settings.logging.level,
        colorize=True,
        backtrace=True,
        diagnose=True,
    )
    
    # 添加文件日志处理器
    logger.add(
        settings.logging.file,
        format=settings.logging.format,
        level=settings.logging.level,
        rotation=settings.logging.rotation,
        retention=settings.logging.retention,
        compression="zip",
        backtrace=True,
        diagnose=True,
    )
    
    logger.info("日志系统已初始化")


def get_logger(name: str = None):
    """获取日志记录器"""
    if name:
        return logger.bind(name=name)
    return logger
