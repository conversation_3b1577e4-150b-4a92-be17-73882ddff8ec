#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三个饰品简单分析脚本
"""

from real_time_monitor import FixedRealTimeMonitor

def analyze_three_skins():
    """分析三个饰品"""
    skin_names = [
        'AK-47 传承 (久经沙场)',
        'M4A1 消音型印花集', 
        '沙漠之鹰 印花集 (久经沙场)'
    ]
    
    print("🔍 CS2三饰品对比分析")
    print("=" * 60)
    
    for i, skin_name in enumerate(skin_names, 1):
        print(f"\n{i}. 📊 分析 {skin_name}")
        print("-" * 50)
        
        try:
            monitor = FixedRealTimeMonitor(skin_name)
            if monitor.load_data():
                # 直接调用原有的显示方法
                monitor.print_fixed_dashboard()
                print("\n" + "="*50)
            else:
                print(f"❌ {skin_name} 数据加载失败")
        except Exception as e:
            print(f"❌ {skin_name} 分析失败: {e}")

if __name__ == "__main__":
    analyze_three_skins()
