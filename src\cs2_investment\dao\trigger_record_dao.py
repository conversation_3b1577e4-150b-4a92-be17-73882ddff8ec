"""
触发记录数据访问对象

提供触发记录的数据库操作功能，包括创建、查询、更新、统计等。
支持触发历史分析和准确性验证。
"""

from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy import and_, or_, desc, asc, func
from sqlalchemy.orm import Session

from .base_dao import BaseDAO
from ..models.trigger_record import TriggerRecord, TriggerSignalType, TriggerRiskLevel, TriggerAlertLevel
from ..database.database_manager import DatabaseManager


class TriggerRecordDAO(BaseDAO):
    """触发记录数据访问对象"""
    
    def __init__(self):
        super().__init__(TriggerRecord)
        self.db_manager = DatabaseManager()
    
    def create_trigger_record(self, trigger_data: Dict[str, Any]) -> TriggerRecord:
        """
        创建触发记录
        
        Args:
            trigger_data: 触发数据字典
            
        Returns:
            TriggerRecord: 创建的触发记录
        """
        try:
            with self.db_manager.get_session() as session:
                trigger_record = TriggerRecord(**trigger_data)
                session.add(trigger_record)
                session.commit()
                session.refresh(trigger_record)
                return trigger_record
                
        except Exception as e:
            self.logger.error(f"创建触发记录失败: {e}")
            raise
    
    def get_user_trigger_history(self, user_id: str, limit: int = 50, 
                                signal_type: Optional[str] = None) -> List[TriggerRecord]:
        """
        获取用户触发历史
        
        Args:
            user_id: 用户ID
            limit: 限制数量
            signal_type: 信号类型过滤
            
        Returns:
            List[TriggerRecord]: 触发记录列表
        """
        try:
            with self.db_manager.get_session() as session:
                query = session.query(TriggerRecord).filter(TriggerRecord.user_id == user_id)
                
                if signal_type:
                    query = query.filter(TriggerRecord.signal_type == signal_type)
                
                records = query.order_by(desc(TriggerRecord.created_at)).limit(limit).all()
                return records
                
        except Exception as e:
            self.logger.error(f"获取用户触发历史失败: {e}")
            return []
    
    def get_item_trigger_history(self, item_id: str, days: int = 30) -> List[TriggerRecord]:
        """
        获取饰品触发历史
        
        Args:
            item_id: 饰品ID
            days: 查询天数
            
        Returns:
            List[TriggerRecord]: 触发记录列表
        """
        try:
            with self.db_manager.get_session() as session:
                start_date = datetime.now() - timedelta(days=days)
                
                records = session.query(TriggerRecord).filter(
                    and_(
                        TriggerRecord.item_id == item_id,
                        TriggerRecord.created_at >= start_date
                    )
                ).order_by(desc(TriggerRecord.created_at)).all()
                
                return records
                
        except Exception as e:
            self.logger.error(f"获取饰品触发历史失败: {e}")
            return []
    
    def get_unexecuted_triggers(self, user_id: str = None) -> List[TriggerRecord]:
        """
        获取未执行的触发记录
        
        Args:
            user_id: 用户ID（可选）
            
        Returns:
            List[TriggerRecord]: 未执行的触发记录
        """
        try:
            with self.db_manager.get_session() as session:
                query = session.query(TriggerRecord).filter(TriggerRecord.is_executed == False)
                
                if user_id:
                    query = query.filter(TriggerRecord.user_id == user_id)
                
                records = query.order_by(desc(TriggerRecord.created_at)).all()
                return records
                
        except Exception as e:
            self.logger.error(f"获取未执行触发记录失败: {e}")
            return []
    
    def mark_trigger_executed(self, trigger_id: int, execution_price: float, 
                            notes: str = "") -> bool:
        """
        标记触发记录为已执行
        
        Args:
            trigger_id: 触发记录ID
            execution_price: 执行价格
            notes: 执行备注
            
        Returns:
            bool: 是否成功
        """
        try:
            with self.db_manager.get_session() as session:
                trigger_record = session.query(TriggerRecord).filter(
                    TriggerRecord.id == trigger_id
                ).first()
                
                if trigger_record:
                    trigger_record.mark_as_executed(execution_price, notes)
                    session.commit()
                    return True
                
                return False
                
        except Exception as e:
            self.logger.error(f"标记触发执行失败: {e}")
            return False
    
    def verify_trigger_accuracy(self, trigger_id: int, result: str, notes: str = "") -> bool:
        """
        验证触发准确性
        
        Args:
            trigger_id: 触发记录ID
            result: 验证结果 ('correct', 'incorrect', 'pending')
            notes: 验证备注
            
        Returns:
            bool: 是否成功
        """
        try:
            with self.db_manager.get_session() as session:
                trigger_record = session.query(TriggerRecord).filter(
                    TriggerRecord.id == trigger_id
                ).first()
                
                if trigger_record:
                    trigger_record.verify_accuracy(result, notes)
                    session.commit()
                    return True
                
                return False
                
        except Exception as e:
            self.logger.error(f"验证触发准确性失败: {e}")
            return False
    
    def get_accuracy_statistics(self, user_id: str = None, days: int = 30) -> Dict[str, Any]:
        """
        获取准确性统计
        
        Args:
            user_id: 用户ID（可选）
            days: 统计天数
            
        Returns:
            Dict[str, Any]: 准确性统计
        """
        try:
            with self.db_manager.get_session() as session:
                start_date = datetime.now() - timedelta(days=days)
                
                query = session.query(TriggerRecord).filter(
                    and_(
                        TriggerRecord.created_at >= start_date,
                        TriggerRecord.is_verified == True
                    )
                )
                
                if user_id:
                    query = query.filter(TriggerRecord.user_id == user_id)
                
                # 总体统计
                total_verified = query.count()
                correct_count = query.filter(TriggerRecord.verification_result == 'correct').count()
                incorrect_count = query.filter(TriggerRecord.verification_result == 'incorrect').count()
                
                # 按信号类型统计
                signal_stats = {}
                for signal_type in TriggerSignalType:
                    signal_query = query.filter(TriggerRecord.signal_type == signal_type)
                    signal_total = signal_query.count()
                    signal_correct = signal_query.filter(TriggerRecord.verification_result == 'correct').count()
                    
                    signal_stats[signal_type.value] = {
                        'total': signal_total,
                        'correct': signal_correct,
                        'accuracy': (signal_correct / signal_total * 100) if signal_total > 0 else 0
                    }
                
                # 按风险等级统计
                risk_stats = {}
                for risk_level in TriggerRiskLevel:
                    risk_query = query.filter(TriggerRecord.risk_level == risk_level)
                    risk_total = risk_query.count()
                    risk_correct = risk_query.filter(TriggerRecord.verification_result == 'correct').count()
                    
                    risk_stats[risk_level.value] = {
                        'total': risk_total,
                        'correct': risk_correct,
                        'accuracy': (risk_correct / risk_total * 100) if risk_total > 0 else 0
                    }
                
                return {
                    'period_days': days,
                    'total_verified': total_verified,
                    'correct_count': correct_count,
                    'incorrect_count': incorrect_count,
                    'overall_accuracy': (correct_count / total_verified * 100) if total_verified > 0 else 0,
                    'signal_type_stats': signal_stats,
                    'risk_level_stats': risk_stats
                }
                
        except Exception as e:
            self.logger.error(f"获取准确性统计失败: {e}")
            return {}
    
    def get_recent_high_confidence_triggers(self, confidence_threshold: float = 0.8, 
                                          hours: int = 24) -> List[TriggerRecord]:
        """
        获取最近的高置信度触发
        
        Args:
            confidence_threshold: 置信度阈值
            hours: 查询小时数
            
        Returns:
            List[TriggerRecord]: 高置信度触发记录
        """
        try:
            with self.db_manager.get_session() as session:
                start_time = datetime.now() - timedelta(hours=hours)
                
                records = session.query(TriggerRecord).filter(
                    and_(
                        TriggerRecord.created_at >= start_time,
                        TriggerRecord.confidence >= confidence_threshold
                    )
                ).order_by(desc(TriggerRecord.confidence)).all()
                
                return records
                
        except Exception as e:
            self.logger.error(f"获取高置信度触发失败: {e}")
            return []
    
    def get_risk_alert_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        获取风险预警摘要
        
        Args:
            hours: 查询小时数
            
        Returns:
            Dict[str, Any]: 风险预警摘要
        """
        try:
            with self.db_manager.get_session() as session:
                start_time = datetime.now() - timedelta(hours=hours)
                
                # 风险预警统计
                risk_alerts = session.query(TriggerRecord).filter(
                    and_(
                        TriggerRecord.created_at >= start_time,
                        TriggerRecord.signal_type == TriggerSignalType.RISK_ALERT
                    )
                ).all()
                
                # 按预警级别分组
                alert_stats = {}
                for alert_level in TriggerAlertLevel:
                    count = len([r for r in risk_alerts if r.alert_level == alert_level])
                    alert_stats[alert_level.value] = count
                
                # 高风险饰品
                high_risk_items = session.query(TriggerRecord.item_id, TriggerRecord.item_name).filter(
                    and_(
                        TriggerRecord.created_at >= start_time,
                        TriggerRecord.risk_level == TriggerRiskLevel.HIGH
                    )
                ).distinct().all()
                
                return {
                    'period_hours': hours,
                    'total_risk_alerts': len(risk_alerts),
                    'alert_level_stats': alert_stats,
                    'high_risk_items': [{'item_id': item[0], 'item_name': item[1]} for item in high_risk_items]
                }
                
        except Exception as e:
            self.logger.error(f"获取风险预警摘要失败: {e}")
            return {}
    
    def cleanup_old_records(self, days: int = 90) -> int:
        """
        清理旧记录
        
        Args:
            days: 保留天数
            
        Returns:
            int: 删除的记录数
        """
        try:
            with self.db_manager.get_session() as session:
                cutoff_date = datetime.now() - timedelta(days=days)
                
                deleted_count = session.query(TriggerRecord).filter(
                    TriggerRecord.created_at < cutoff_date
                ).delete()
                
                session.commit()
                self.logger.info(f"清理了 {deleted_count} 条旧触发记录")
                return deleted_count
                
        except Exception as e:
            self.logger.error(f"清理旧记录失败: {e}")
            return 0
