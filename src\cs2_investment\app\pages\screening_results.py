"""
投资筛选结果页面

显示系统生成的投资建议和筛选结果。
"""

import streamlit as st
import pandas as pd
import plotly.express as px
from typing import List, Dict, Any
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.app.services.screening_service import ScreeningService
from src.cs2_investment.app.services.favorite_service import FavoriteService
from src.cs2_investment.app.utils.data_formatter import format_price, format_percentage


def show_page():
    """显示投资筛选结果页面"""
    st.title("🎯 投资筛选结果")

    try:
        # 初始化服务
        if 'screening_service' not in st.session_state:
            st.session_state.screening_service = ScreeningService()
        if 'favorite_service' not in st.session_state:
            st.session_state.favorite_service = FavoriteService()

        # 筛选条件
        with st.expander("筛选条件", expanded=True):
            show_screening_filters()

        # 显示结果
        show_screening_results()

    except Exception as e:
        st.error(f"加载投资筛选页面失败: {str(e)}")
        st.info("请检查数据库连接配置")
        st.markdown("### 📋 功能说明")
        st.write("此页面用于查看系统生成的投资建议和筛选结果。")
        st.write("请确保数据库连接正常，并且screening_results表中有数据。")


def show_screening_filters():
    """显示筛选条件"""
    # 获取投资类型选项
    if 'investment_types' not in st.session_state:
        with st.spinner("加载投资类型..."):
            st.session_state.investment_types = st.session_state.screening_service.get_investment_types()

    investment_types = st.session_state.investment_types

    col1, col2, col3 = st.columns(3)

    with col1:
        # 投资类型
        selected_types = st.multiselect(
            "投资类型",
            options=investment_types,
            default=investment_types[:2] if len(investment_types) >= 2 else investment_types,
            help="选择感兴趣的投资类型"
        )

        # 评分范围
        score_range = st.slider(
            "评分范围",
            min_value=0.0,
            max_value=100.0,
            value=(70.0, 100.0),
            step=1.0,
            help="设置最低评分要求"
        )
    
    with col2:
        # 风险等级
        risk_levels = ["LOW", "MEDIUM", "HIGH"]
        selected_risks = st.multiselect(
            "风险等级",
            options=risk_levels,
            default=["LOW", "MEDIUM"],
            help="选择可接受的风险等级"
        )
        
        # 价格范围
        price_range = st.slider(
            "价格范围 (¥)",
            min_value=0,
            max_value=50000,
            value=(1000, 20000),
            step=500,
            help="设置投资金额范围"
        )
    
    with col3:
        # 时间范围
        time_options = {
            "1d": "最近1天",
            "3d": "最近3天", 
            "7d": "最近7天",
            "30d": "最近30天"
        }
        time_range = st.selectbox(
            "筛选时间",
            options=list(time_options.keys()),
            format_func=lambda x: time_options[x],
            index=2,
            help="选择筛选结果的时间范围"
        )
        
        # 排序方式
        sort_options = {
            "score_desc": "评分 (高到低)",
            "score_asc": "评分 (低到高)",
            "price_desc": "价格 (高到低)",
            "price_asc": "价格 (低到高)",
            "time_desc": "时间 (最新)"
        }
        sort_by = st.selectbox(
            "排序方式",
            options=list(sort_options.keys()),
            format_func=lambda x: sort_options[x],
            index=0
        )
    
    # 查询按钮
    col1, col2, col3 = st.columns([1, 1, 1])
    with col2:
        if st.button("🔍 查询筛选结果", type="primary", use_container_width=True):
            query_params = {
                'investment_types': selected_types if selected_types else None,
                'score_min': score_range[0] if score_range[0] > 0 else None,
                'score_max': score_range[1] if score_range[1] < 100 else None,
                'risk_levels': selected_risks if selected_risks else None,
                'price_min': price_range[0] if price_range[0] > 0 else None,
                'price_max': price_range[1] if price_range[1] < 50000 else None,
                'time_range': time_range,
                'sort_by': sort_by,
                'limit': 5000  # 增加查询限制到5000条
            }
            st.session_state.screening_query_params = query_params
            st.session_state.screening_query_executed = True


def show_screening_results():
    """显示筛选结果"""
    if not hasattr(st.session_state, 'screening_query_executed') or not st.session_state.screening_query_executed:
        st.info("请设置筛选条件并点击查询按钮")
        return
    
    try:
        # 执行查询
        with st.spinner("正在查询筛选结果..."):
            results = st.session_state.screening_service.search_screening_results(
                **st.session_state.screening_query_params
            )

        if not results:
            st.warning("未找到符合条件的投资建议")
            return

        # 显示统计信息
        show_results_stats(results)

        # 显示图表分析
        show_results_charts(results)

        # 显示详细结果列表
        show_results_list(results)

    except Exception as e:
        st.error(f"查询失败: {str(e)}")


def show_results_stats(results: List[Dict]):
    """显示结果统计"""
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("筛选结果", len(results))
    
    with col2:
        avg_score = sum(r['score'] for r in results) / len(results)
        st.metric("平均评分", f"{avg_score:.1f}")
    
    with col3:
        high_score_count = sum(1 for r in results if r['score'] >= 85)
        st.metric("高分项目", f"{high_score_count}/{len(results)}")
    
    with col4:
        total_value = sum(r['current_price'] for r in results)
        st.metric("总投资额", f"¥{total_value:,.0f}")


def show_results_charts(results: List[Dict]):
    """显示图表分析"""
    st.subheader("📊 分析图表")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # 投资类型分布
        df = pd.DataFrame(results)
        type_counts = df['investment_type'].value_counts()
        
        fig = px.pie(
            values=type_counts.values,
            names=type_counts.index,
            title="投资类型分布"
        )
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # 评分分布
        fig = px.histogram(
            df,
            x='score',
            nbins=20,
            title="评分分布",
            labels={'score': '评分', 'count': '数量'}
        )
        st.plotly_chart(fig, use_container_width=True)


def show_results_list(results: List[Dict]):
    """显示结果列表"""
    st.subheader("📋 详细结果")
    
    # 分页
    items_per_page = 50
    total_pages = (len(results) - 1) // items_per_page + 1
    
    if total_pages > 1:
        page = st.selectbox(
            "页码",
            range(1, total_pages + 1),
            format_func=lambda x: f"第 {x} 页",
            key="screening_page"
        )
    else:
        page = 1
    
    start_idx = (page - 1) * items_per_page
    end_idx = min(start_idx + items_per_page, len(results))
    current_page_results = results[start_idx:end_idx]
    
    # 显示结果列表
    for result in current_page_results:
        show_screening_result_row(result)


def show_screening_result_row(result: Dict):
    """显示筛选结果行格式"""
    with st.container():
        col1, col2, col3, col4, col5 = st.columns([3, 2, 2, 2, 1])

        with col1:
            # 饰品名称和投资类型
            st.markdown(f"**{result['item_name']}**")
            st.caption(f"类型: {result['investment_type']}")

            # 投资建议标签
            recommendation = result['recommendation']
            color = {"BUY": "🟢", "HOLD": "🟡", "SELL": "🔴"}.get(recommendation, "⚪")
            st.markdown(f"{color} **{recommendation}**")

        with col2:
            # 评分和置信度
            st.metric("评分", f"{result['score']:.1f}")
            st.caption(f"置信度: {result['confidence']:.1f}%")

        with col3:
            # 当前价格
            st.metric("当前价格", f"¥{result['current_price']:,.2f}")

        with col4:
            # 7天变化
            change_7d = result.get('price_change_7d', 0)
            if change_7d != 0:
                color = "🔴" if change_7d < 0 else "🟢"
                st.write(f"{color} {change_7d:+.1f}% (7天)")
            else:
                st.write("📊 无变化")

        with col5:
            # 收藏饰品按钮
            item_id = result.get('item_id')
            if item_id:
                # 检查饰品是否已收藏
                is_favorited = st.session_state.favorite_service.is_favorited(
                    user_id="default_user",
                    item_id=item_id
                )

                button_text = "💖" if is_favorited else "🤍"
                if st.button(button_text, key=f"fav_item_{item_id}", help="收藏饰品"):
                    if is_favorited:
                        success = st.session_state.favorite_service.remove_favorite(
                            user_id="default_user",
                            item_id=item_id
                        )
                        if success:
                            st.success("已取消收藏")
                            st.rerun()
                    else:
                        success = st.session_state.favorite_service.add_favorite(
                            user_id="default_user",
                            item_id=item_id,
                            item_name=result['item_name']
                        )
                        if success:
                            st.success("已添加收藏")
                            st.rerun()
            else:
                st.write("📊 推荐")

        # 分析摘要
        if result.get('analysis_summary'):
            with st.expander("分析摘要", expanded=False):
                st.write(result['analysis_summary'])

        st.divider()
        
        st.divider()


def show_result_detail(result: Dict):
    """显示结果详情"""
    with st.modal(f"投资建议详情 - {result['item_name']}"):
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("基本信息")
            st.write(f"**饰品名称**: {result['item_name']}")
            st.write(f"**投资类型**: {result['investment_type']}")
            st.write(f"**风险等级**: {result['risk_level']}")
            st.write(f"**投资建议**: {result['recommendation']}")
        
        with col2:
            st.subheader("评分信息")
            st.write(f"**综合评分**: {result['score']:.1f}")
            st.write(f"**置信度**: {result['confidence']:.1f}%")
            st.write(f"**排名**: #{result['rank']}")
        
        st.subheader("价格信息")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("当前价格", f"¥{result['current_price']:,.2f}")
        with col2:
            st.metric("7天变化", f"{result.get('price_change_7d', 0):+.1f}%")
        with col3:
            st.metric("30天变化", f"{result.get('price_change_30d', 0):+.1f}%")
        
        if result.get('analysis_summary'):
            st.subheader("分析摘要")
            st.write(result['analysis_summary'])



