#!/usrbin/env python3
"""
分析日志DAO

管理分析状态和历史记录
"""

from datetime import datetime, date
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from src.cs2_investment.config.database import get_db_session
from src.cs2_investment.models.analysis_log import AnalysisLog, DailyAnalysisSchedule


class AnalysisLogDAO:
    """分析日志数据访问对象"""
    
    def create_analysis_log(self, item_id: str, item_name: str, analysis_type: str,
                           scheduled_date: datetime) -> Dict[str, Any]:
        """创建分析日志记录"""
        with get_db_session() as session:
            log = AnalysisLog(
                item_id=item_id,
                item_name=item_name,
                analysis_type=analysis_type,
                status='pending',
                scheduled_date=scheduled_date
            )
            session.add(log)
            session.commit()
            session.refresh(log)

            # 返回字典避免会话问题
            return {
                'id': log.id,
                'item_id': log.item_id,
                'item_name': log.item_name,
                'analysis_type': log.analysis_type,
                'status': log.status,
                'scheduled_date': log.scheduled_date,
                'created_at': log.created_at
            }
    
    def update_analysis_status(self, log_id: int, status: str, 
                              start_time: datetime = None, 
                              end_time: datetime = None,
                              error_message: str = None,
                              result_path: str = None) -> bool:
        """更新分析状态"""
        with get_db_session() as session:
            log = session.query(AnalysisLog).filter(AnalysisLog.id == log_id).first()
            if not log:
                return False
            
            log.status = status
            if start_time:
                log.start_time = start_time
            if end_time:
                log.end_time = end_time
                if log.start_time:
                    log.duration_seconds = (end_time - log.start_time).total_seconds()
            if error_message:
                log.error_message = error_message
            if result_path:
                log.result_path = result_path
            
            log.success = (status == 'success')
            session.commit()
            return True
    
    def get_pending_analysis(self, analysis_type: str, scheduled_date: date) -> List[AnalysisLog]:
        """获取待分析的记录"""
        with get_db_session() as session:
            logs = session.query(AnalysisLog).filter(
                and_(
                    AnalysisLog.analysis_type == analysis_type,
                    AnalysisLog.status == 'pending',
                    AnalysisLog.scheduled_date >= datetime.combine(scheduled_date, datetime.min.time()),
                    AnalysisLog.scheduled_date < datetime.combine(scheduled_date, datetime.max.time())
                )
            ).order_by(AnalysisLog.created_at).all()
            
            # 转换为字典避免会话问题
            result = []
            for log in logs:
                result.append({
                    'id': log.id,
                    'item_id': log.item_id,
                    'item_name': log.item_name,
                    'analysis_type': log.analysis_type,
                    'status': log.status,
                    'scheduled_date': log.scheduled_date
                })
            return result
    
    def get_failed_analysis(self, analysis_type: str, scheduled_date: date) -> List[Dict[str, Any]]:
        """获取失败的分析记录（用于重试）"""
        with get_db_session() as session:
            logs = session.query(AnalysisLog).filter(
                and_(
                    AnalysisLog.analysis_type == analysis_type,
                    AnalysisLog.status == 'failed',
                    AnalysisLog.scheduled_date >= datetime.combine(scheduled_date, datetime.min.time()),
                    AnalysisLog.scheduled_date < datetime.combine(scheduled_date, datetime.max.time())
                )
            ).order_by(AnalysisLog.updated_at).all()
            
            # 转换为字典避免会话问题
            result = []
            for log in logs:
                result.append({
                    'id': log.id,
                    'item_id': log.item_id,
                    'item_name': log.item_name,
                    'analysis_type': log.analysis_type,
                    'status': log.status,
                    'scheduled_date': log.scheduled_date,
                    'error_message': log.error_message
                })
            return result

    def get_analysis_log_by_id(self, log_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取分析日志记录"""
        with get_db_session() as session:
            log = session.query(AnalysisLog).filter(AnalysisLog.id == log_id).first()
            if not log:
                return None

            return {
                'id': log.id,
                'item_id': log.item_id,
                'item_name': log.item_name,
                'analysis_type': log.analysis_type,
                'status': log.status,
                'scheduled_date': log.scheduled_date,
                'start_time': log.start_time,
                'end_time': log.end_time,
                'duration_seconds': log.duration_seconds,
                'success': log.success,
                'error_message': log.error_message,
                'result_path': log.result_path,
                'created_at': log.created_at,
                'updated_at': log.updated_at
            }

    def get_analysis_logs_paginated(self, status: str = None, analysis_type: str = None,
                                   page: int = 1, page_size: int = 10) -> tuple[List[Dict[str, Any]], int]:
        """分页获取分析日志记录"""
        with get_db_session() as session:
            query = session.query(AnalysisLog)

            # 添加过滤条件
            if status:
                query = query.filter(AnalysisLog.status == status)
            if analysis_type:
                query = query.filter(AnalysisLog.analysis_type == analysis_type)

            # 获取总数
            total_count = query.count()

            # 分页查询
            offset = (page - 1) * page_size
            logs = query.order_by(desc(AnalysisLog.created_at)).offset(offset).limit(page_size).all()

            # 转换为字典
            result = []
            for log in logs:
                result.append({
                    'id': log.id,
                    'item_id': log.item_id,
                    'item_name': log.item_name,
                    'analysis_type': log.analysis_type,
                    'status': log.status,
                    'scheduled_date': log.scheduled_date,
                    'start_time': log.start_time,
                    'end_time': log.end_time,
                    'duration_seconds': log.duration_seconds,
                    'success': log.success,
                    'error_message': log.error_message,
                    'result_path': log.result_path,
                    'created_at': log.created_at,
                    'updated_at': log.updated_at
                })

            return result, total_count

    def get_analysis_progress(self, analysis_type: str, scheduled_date: date) -> Dict[str, int]:
        """获取分析进度统计"""
        with get_db_session() as session:
            total = session.query(AnalysisLog).filter(
                and_(
                    AnalysisLog.analysis_type == analysis_type,
                    AnalysisLog.scheduled_date >= datetime.combine(scheduled_date, datetime.min.time()),
                    AnalysisLog.scheduled_date < datetime.combine(scheduled_date, datetime.max.time())
                )
            ).count()
            
            completed = session.query(AnalysisLog).filter(
                and_(
                    AnalysisLog.analysis_type == analysis_type,
                    AnalysisLog.status == 'success',
                    AnalysisLog.scheduled_date >= datetime.combine(scheduled_date, datetime.min.time()),
                    AnalysisLog.scheduled_date < datetime.combine(scheduled_date, datetime.max.time())
                )
            ).count()
            
            failed = session.query(AnalysisLog).filter(
                and_(
                    AnalysisLog.analysis_type == analysis_type,
                    AnalysisLog.status == 'failed',
                    AnalysisLog.scheduled_date >= datetime.combine(scheduled_date, datetime.min.time()),
                    AnalysisLog.scheduled_date < datetime.combine(scheduled_date, datetime.max.time())
                )
            ).count()
            
            running = session.query(AnalysisLog).filter(
                and_(
                    AnalysisLog.analysis_type == analysis_type,
                    AnalysisLog.status == 'running',
                    AnalysisLog.scheduled_date >= datetime.combine(scheduled_date, datetime.min.time()),
                    AnalysisLog.scheduled_date < datetime.combine(scheduled_date, datetime.max.time())
                )
            ).count()
            
            pending = session.query(AnalysisLog).filter(
                and_(
                    AnalysisLog.analysis_type == analysis_type,
                    AnalysisLog.status == 'pending',
                    AnalysisLog.scheduled_date >= datetime.combine(scheduled_date, datetime.min.time()),
                    AnalysisLog.scheduled_date < datetime.combine(scheduled_date, datetime.max.time())
                )
            ).count()
            
            return {
                'total': total,
                'completed': completed,
                'failed': failed,
                'running': running,
                'pending': pending
            }


class DailyScheduleDAO:
    """每日计划DAO"""
    
    def create_daily_schedule(self, schedule_date: date, total_items: int) -> DailyAnalysisSchedule:
        """创建每日分析计划"""
        with get_db_session() as session:
            schedule = DailyAnalysisSchedule(
                schedule_date=datetime.combine(schedule_date, datetime.min.time()),
                total_items=total_items,
                status='pending'
            )
            session.add(schedule)
            session.commit()
            session.refresh(schedule)
            return schedule
    
    def get_daily_schedule(self, schedule_date: date) -> Optional[Dict[str, Any]]:
        """获取指定日期的分析计划"""
        with get_db_session() as session:
            schedule = session.query(DailyAnalysisSchedule).filter(
                and_(
                    DailyAnalysisSchedule.schedule_date >= datetime.combine(schedule_date, datetime.min.time()),
                    DailyAnalysisSchedule.schedule_date < datetime.combine(schedule_date, datetime.max.time())
                )
            ).first()
            
            if not schedule:
                return None
            
            return {
                'id': schedule.id,
                'schedule_date': schedule.schedule_date,
                'total_items': schedule.total_items,
                'completed_items': schedule.completed_items,
                'failed_items': schedule.failed_items,
                'status': schedule.status,
                'start_time': schedule.start_time,
                'end_time': schedule.end_time
            }
    
    def update_daily_schedule(self, schedule_id: int, **kwargs) -> bool:
        """更新每日计划"""
        with get_db_session() as session:
            schedule = session.query(DailyAnalysisSchedule).filter(
                DailyAnalysisSchedule.id == schedule_id
            ).first()
            
            if not schedule:
                return False
            
            for key, value in kwargs.items():
                if hasattr(schedule, key):
                    setattr(schedule, key, value)
            
            session.commit()
            return True

    def get_analyzed_item_ids_by_date(self, target_date: date, item_ids: List[str] = None) -> List[str]:
        """获取指定日期已分析的饰品ID列表"""
        with get_db_session() as session:
            # 获取指定日期的时间范围
            date_start = datetime.combine(target_date, datetime.min.time())
            date_end = datetime.combine(target_date, datetime.max.time())

            # 构建查询
            query = session.query(AnalysisLog.item_id).filter(
                and_(
                    AnalysisLog.scheduled_date >= date_start,
                    AnalysisLog.scheduled_date <= date_end,
                    AnalysisLog.status.in_(['completed', 'running', 'pending'])  # 包含已完成、运行中、待处理的
                )
            )

            # 如果提供了特定的item_ids，只查询这些ID
            if item_ids:
                query = query.filter(AnalysisLog.item_id.in_(item_ids))

            # 去重并返回
            analyzed_item_ids = [row[0] for row in query.distinct().all()]
            return analyzed_item_ids


# 全局实例
analysis_log_dao = AnalysisLogDAO()
daily_schedule_dao = DailyScheduleDAO()
