#!/usr/bin/env python3
"""
SteamDT数据抓取系统 - 主程序
集成定时任务和手动执行功能
"""

import asyncio
import sys
import os
from datetime import datetime
from pathlib import Path

# 添加项目根路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.scheduler.scheduler_manager import SchedulerManager
from src.cs2_investment.scraper.scraping_manager import ScrapingManager
from src.cs2_investment.scraper.steamdt_scraper_final import SteamDTScraperFinal

# 使用统一日志组件
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)


class SteamDTMainSystem:
    """SteamDT主系统"""
    
    def __init__(self):
        self.scheduler_manager = SchedulerManager()
        self.scraping_manager = ScrapingManager()
        self.scraper = SteamDTScraperFinal()
        
        logger.info("SteamDT主系统初始化完成")
    
    async def run_manual_scraping(self, item_url: str = None, item_name: str = None):
        """手动执行抓取"""
        logger.info("开始手动抓取")
        
        try:
            if item_url and item_name:
                # 抓取指定饰品
                logger.info(f"抓取指定饰品: {item_name}")
                result = await self.scraper.scrape_item_data(item_url, item_name)
                
                if result.get('success'):
                    logger.info(f"✅ 抓取成功: {item_name}")
                    return True
                else:
                    logger.error(f"❌ 抓取失败: {item_name} - {result.get('error')}")
                    return False
            else:
                # 执行完整抓取流程
                logger.info("执行完整抓取流程")
                await self.scraper.run()
                return True
                
        except Exception as e:
            logger.error(f"手动抓取失败: {e}")
            return False
    
    def start_scheduler(self):
        """启动定时任务"""
        logger.info("启动定时任务调度器")
        
        try:
            self.scheduler_manager.start()
            logger.info("✅ 定时任务调度器启动成功")
            return True
        except Exception as e:
            logger.error(f"❌ 定时任务调度器启动失败: {e}")
            return False
    
    def stop_scheduler(self):
        """停止定时任务"""
        logger.info("停止定时任务调度器")
        
        try:
            self.scheduler_manager.stop()
            logger.info("✅ 定时任务调度器已停止")
            return True
        except Exception as e:
            logger.error(f"❌ 停止定时任务调度器失败: {e}")
            return False
    
    def get_system_status(self):
        """获取系统状态"""
        try:
            scheduler_status = self.scheduler_manager.get_status()
            scraping_stats = self.scraping_manager.get_daily_statistics()
            
            status = {
                'scheduler': scheduler_status,
                'scraping_stats': scraping_stats,
                'system_time': datetime.now().isoformat()
            }
            
            return status
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {}
    
    def run_system_check(self):
        """运行系统检查"""
        logger.info("开始系统检查")
        
        checks = {
            'database_connection': False,
            'scraper_ready': False,
            'scheduler_ready': False
        }
        
        try:
            # 检查数据库连接
            try:
                with self.scraping_manager.get_session() as session:
                    session.execute("SELECT 1")
                checks['database_connection'] = True
                logger.info("✅ 数据库连接正常")
            except Exception as e:
                logger.error(f"❌ 数据库连接失败: {e}")
            
            # 检查抓取器
            try:
                # 这里可以添加抓取器的健康检查
                checks['scraper_ready'] = True
                logger.info("✅ 抓取器就绪")
            except Exception as e:
                logger.error(f"❌ 抓取器检查失败: {e}")
            
            # 检查调度器
            try:
                status = self.scheduler_manager.get_status()
                checks['scheduler_ready'] = True
                logger.info("✅ 调度器就绪")
            except Exception as e:
                logger.error(f"❌ 调度器检查失败: {e}")
            
            all_ok = all(checks.values())
            logger.info(f"系统检查完成: {'全部正常' if all_ok else '存在问题'}")
            
            return checks
            
        except Exception as e:
            logger.error(f"系统检查失败: {e}")
            return checks


def print_help():
    """打印帮助信息"""
    print("🎯 SteamDT数据抓取系统 - 主程序")
    print("=" * 50)
    print("使用方法:")
    print("  python steamdt_main.py <命令> [参数]")
    print()
    print("可用命令:")
    print("  scrape              - 执行完整抓取流程")
    print("  scrape-item <URL> <名称> - 抓取指定饰品")
    print("  scheduler           - 启动定时任务调度器")
    print("  status              - 查看系统状态")
    print("  check               - 运行系统检查")
    print("  help                - 显示此帮助信息")
    print()
    print("示例:")
    print("  python steamdt_main.py scrape")
    print("  python steamdt_main.py scrape-item \"https://steamdt.com/cs2/123\" \"AK-47\"")
    print("  python steamdt_main.py scheduler")
    print("  python steamdt_main.py status")


async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print_help()
        return 1
    
    command = sys.argv[1].lower()
    system = SteamDTMainSystem()
    
    if command == 'help':
        print_help()
        return 0
    
    elif command == 'scrape':
        logger.info("执行完整抓取流程")
        success = await system.run_manual_scraping()
        return 0 if success else 1
    
    elif command == 'scrape-item':
        if len(sys.argv) < 4:
            print("❌ 缺少参数: python steamdt_main.py scrape-item <URL> <名称>")
            return 1
        
        item_url = sys.argv[2]
        item_name = sys.argv[3]
        
        logger.info(f"抓取指定饰品: {item_name}")
        success = await system.run_manual_scraping(item_url, item_name)
        return 0 if success else 1
    
    elif command == 'scheduler':
        logger.info("启动定时任务调度器")
        success = system.start_scheduler()
        
        if success:
            try:
                # 保持运行
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                logger.info("收到停止信号")
                system.stop_scheduler()
        
        return 0 if success else 1
    
    elif command == 'status':
        status = system.get_system_status()
        
        print("📊 系统状态:")
        print(f"  系统时间: {status.get('system_time', 'unknown')}")
        
        scheduler = status.get('scheduler', {})
        print(f"  调度器状态: {'运行中' if scheduler.get('is_running') else '已停止'}")
        print(f"  计划任务数: {scheduler.get('scheduled_jobs', 0)}")
        
        stats = status.get('scraping_stats', {})
        print(f"  今日处理: {stats.get('processed_items', 0)} 个饰品")
        print(f"  成功率: {stats.get('success_rate', 0):.1f}%")
        
        return 0
    
    elif command == 'check':
        checks = system.run_system_check()
        
        print("🔍 系统检查结果:")
        for check_name, result in checks.items():
            status_icon = "✅" if result else "❌"
            print(f"  {status_icon} {check_name}")
        
        all_ok = all(checks.values())
        print(f"\n总体状态: {'正常' if all_ok else '异常'}")
        
        return 0 if all_ok else 1
    
    else:
        print(f"❌ 未知命令: {command}")
        print_help()
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        sys.exit(1)
