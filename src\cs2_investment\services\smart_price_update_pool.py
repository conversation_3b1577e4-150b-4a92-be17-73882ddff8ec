"""
智能价格更新调用池

实现两层更新策略：
1. 第一层：收藏饰品 - 分钟级更新，优先级最高
2. 第二层：其他饰品 - 利用剩余调用额度更新

基于SteamDT API频率限制优化调用策略。
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.favorite_dao import FavoriteDAO
from src.cs2_investment.dao.item_dao import ItemDAO
from src.cs2_investment.dao.platform_price_dao import PlatformPriceDAO
from src.cs2_investment.services.steamdt_api_client import SteamDTAPIClient
from src.cs2_investment.services.arbitrage_calculator import ArbitrageCalculator
from src.cs2_investment.config.database import get_db_session


class UpdatePriority(Enum):
    """更新优先级"""
    HIGH = 1    # 收藏饰品
    LOW = 2     # 其他饰品


@dataclass
class UpdateTask:
    """更新任务"""
    item_id: str
    market_hash_name: str
    priority: UpdatePriority
    last_update: Optional[datetime] = None
    retry_count: int = 0
    
    def should_update(self, now: datetime) -> bool:
        """判断是否需要更新"""
        if not self.last_update:
            return True
        
        if self.priority == UpdatePriority.HIGH:
            # 收藏饰品：5分钟内更新
            return now - self.last_update > timedelta(minutes=5)
        else:
            # 其他饰品：2小时内更新
            return now - self.last_update > timedelta(hours=2)


class SmartPriceUpdatePool:
    """智能价格更新调用池"""

    # 类级别的实例锁，确保全局只有一个实例在运行
    _running_instances = set()
    _instance_lock = asyncio.Lock() if 'asyncio' in globals() else None

    def __init__(self, api_key: str, user_id: str = "default_user"):
        """
        初始化智能价格更新池

        Args:
            api_key: SteamDT API密钥
            user_id: 用户ID
        """
        self.api_key = api_key
        self.user_id = user_id
        self.logger = logging.getLogger(__name__)

        # 初始化DAO和API客户端
        self.favorite_dao = FavoriteDAO()
        self.item_dao = ItemDAO()
        self.platform_price_dao = PlatformPriceDAO()
        self.api_client = SteamDTAPIClient(api_key)

        # 初始化搬砖比例计算器
        self.arbitrage_calculator = ArbitrageCalculator()

        # 从配置加载API限制
        self._load_api_limits_from_config()

        # API调用限制配置（严格遵守SteamDT官方文档）
        # 注意：这些是默认值，实际值从配置文件加载
        if not hasattr(self, 'api_limits'):
            self.api_limits = {
                'batch_size': 100,           # 批量请求最大数量
                'single_requests_per_minute': 50,   # 单个请求每分钟限制（保守设置）
                'batch_requests_per_minute': 1,     # 批量请求每分钟限制（官方限制）
                'batch_request_interval': 60.0,     # 批量请求间隔（60秒/1次=60秒）
                'single_request_interval': 1.2,     # 单个请求间隔（60秒/50次=1.2秒）
            }
        
        # 调用统计和控制
        self.call_stats = {
            'minute_single_calls': 0,
            'minute_batch_calls': 0,
            'last_minute_reset': datetime.now(),
            'last_batch_call': None,
            'last_single_call': None,
            'total_calls_today': 0
        }
        
        # 任务队列
        self.high_priority_queue: List[UpdateTask] = []
        self.low_priority_queue: List[UpdateTask] = []
        self.processing_tasks: Set[str] = set()
        
        # 运行状态
        self.is_running = False
        self.continuous_mode = True  # 持续运行模式
        self.continuous_task = None  # 持续更新任务引用
        self.update_stats = {
            'total_processed': 0,
            'successful_updates': 0,
            'failed_updates': 0,
            'high_priority_processed': 0,
            'low_priority_processed': 0
        }

    def _load_api_limits_from_config(self):
        """从配置文件加载API限制"""
        try:
            from ..config.timer_config import get_timer_config

            config = get_timer_config()

            # 从配置加载API限制
            single_rpm = config.price_update.single_requests_per_minute
            batch_rpm = config.price_update.batch_requests_per_minute
            batch_size = config.price_update.batch_size

            # 计算间隔时间
            single_interval = 60.0 / single_rpm if single_rpm > 0 else 1.2
            batch_interval = 60.0 / batch_rpm if batch_rpm > 0 else 60.0

            self.api_limits = {
                'batch_size': batch_size,
                'single_requests_per_minute': single_rpm,
                'batch_requests_per_minute': batch_rpm,
                'batch_request_interval': batch_interval,
                'single_request_interval': single_interval,
            }

            self.logger.info(f"从配置加载API限制: 单个请求{single_rpm}/分钟, 批量请求{batch_rpm}/分钟, 批量大小{batch_size}")

        except Exception as e:
            self.logger.warning(f"加载配置失败，使用默认API限制: {e}")
            # 使用默认值（在__init__中设置）

    def reset_api_limits(self):
        """重置API调用限制（用于测试或重新开始）"""
        self.call_stats = {
            'minute_single_calls': 0,
            'minute_batch_calls': 0,
            'last_minute_reset': datetime.now(),
            'last_batch_call': None,
            'last_single_call': None,
            'total_calls_today': 0
        }
        self.logger.info("🔄 API调用限制已重置")

    def _get_remaining_single_calls(self) -> int:
        """计算当前分钟内还能调用多少次单个接口"""
        # 使用现有的重置方法
        self._reset_minute_limits()

        used_calls = self.call_stats['minute_single_calls']
        max_calls = self.api_limits['single_requests_per_minute']

        return max(0, max_calls - used_calls)

    async def initialize_queues(self):
        """初始化任务队列"""
        self.logger.info("🔄 初始化价格更新任务队列...")
        
        # 1. 获取收藏列表（高优先级）
        favorites = self.favorite_dao.get_user_favorites(self.user_id)
        favorite_item_ids = set()
        
        for favorite in favorites:
            item_id = favorite['item_id']
            favorite_item_ids.add(item_id)
            
            # 获取饰品详细信息
            item_data = self.item_dao.get_by_item_id(item_id)
            if item_data and item_data.get('market_hash_name'):
                task = UpdateTask(
                    item_id=item_id,
                    market_hash_name=item_data['market_hash_name'],
                    priority=UpdatePriority.HIGH
                )
                self.high_priority_queue.append(task)
        
        # 2. 获取其他饰品（低优先级）
        all_items = self.item_dao.get_all_active_items(limit=5000)  # 限制数量避免过载

        for item_data in all_items:
            item_id = item_data['item_id']

            # 跳过已在收藏列表中的饰品
            if item_id in favorite_item_ids:
                continue

            if item_data.get('market_hash_name'):
                task = UpdateTask(
                    item_id=item_id,
                    market_hash_name=item_data['market_hash_name'],
                    priority=UpdatePriority.LOW
                )
                self.low_priority_queue.append(task)

        self.logger.info(f"📊 价格更新队列初始化完成: 收藏 {len(self.high_priority_queue)}, 其他 {len(self.low_priority_queue)}")
    
    def _reset_minute_limits(self):
        """重置分钟级限制"""
        now = datetime.now()
        if now - self.call_stats['last_minute_reset'] >= timedelta(minutes=1):
            self.call_stats['minute_single_calls'] = 0
            self.call_stats['minute_batch_calls'] = 0
            self.call_stats['last_minute_reset'] = now

    def _can_make_api_call(self, is_batch: bool = False) -> bool:
        """检查是否可以进行API调用"""
        now = datetime.now()
        self._reset_minute_limits()

        # 检查分钟级限制
        if is_batch:
            # 检查批量请求限制
            batch_calls = self.call_stats['minute_batch_calls']
            batch_limit = self.api_limits['batch_requests_per_minute']

            if batch_calls >= batch_limit:
                self.logger.warning(f"❌ 批量接口调用次数已达上限 ({batch_calls}/{batch_limit})")
                return False

            # 检查批量请求间隔
            if self.call_stats['last_batch_call']:
                time_since_last = (now - self.call_stats['last_batch_call']).total_seconds()
                required_interval = self.api_limits['batch_request_interval']

                if time_since_last < required_interval:
                    self.logger.warning(f"❌ 批量接口调用间隔不足 ({time_since_last:.1f}s < {required_interval}s)")
                    return False
        else:
            # 检查单个请求限制
            single_calls = self.call_stats['minute_single_calls']
            single_limit = self.api_limits['single_requests_per_minute']

            if single_calls >= single_limit:
                self.logger.warning(f"❌ 单个接口调用次数已达上限 ({single_calls}/{single_limit})")
                return False

            # 检查单个请求间隔
            if self.call_stats['last_single_call']:
                time_since_last = (now - self.call_stats['last_single_call']).total_seconds()
                required_interval = self.api_limits['single_request_interval']

                if time_since_last < required_interval:
                    self.logger.warning(f"❌ 单个接口调用间隔不足 ({time_since_last:.1f}s < {required_interval}s)")
                    return False

        return True

    def _get_wait_time(self, is_batch: bool = False) -> float:
        """获取需要等待的时间"""
        now = datetime.now()

        if is_batch and self.call_stats['last_batch_call']:
            elapsed = (now - self.call_stats['last_batch_call']).total_seconds()
            return max(0, self.api_limits['batch_request_interval'] - elapsed)
        elif not is_batch and self.call_stats['last_single_call']:
            elapsed = (now - self.call_stats['last_single_call']).total_seconds()
            return max(0, self.api_limits['single_request_interval'] - elapsed)

        return 0
    
    def _record_api_call(self, is_batch: bool = False, count: int = 1):
        """记录API调用"""
        now = datetime.now()
        self.call_stats['total_calls_today'] += count

        if is_batch:
            self.call_stats['minute_batch_calls'] += 1
            self.call_stats['last_batch_call'] = now
        else:
            self.call_stats['minute_single_calls'] += count
            self.call_stats['last_single_call'] = now
    
    async def _process_batch_tasks(self, tasks: List[UpdateTask]) -> Dict[str, Any]:
        """批量处理任务"""
        if not tasks:
            return {'processed': 0, 'successful': 0, 'failed': 0}

        # 检查是否可以调用API，如果不能则等待
        if not self._can_make_api_call(is_batch=True):
            wait_time = self._get_wait_time(is_batch=True)
            if wait_time > 0:
                self.logger.debug(f"等待 {wait_time:.1f} 秒后进行批量API调用")
                await asyncio.sleep(wait_time)

            # 重新检查
            if not self._can_make_api_call(is_batch=True):
                return {'processed': 0, 'successful': 0, 'failed': 0}
        
        # 限制批量大小
        batch_tasks = tasks[:self.api_limits['batch_size']]
        market_hash_names = [task.market_hash_name for task in batch_tasks]
        
        try:
            # 使用try-except包装API调用，确保连接正确关闭
            try:
                price_responses = await self.api_client.get_batch_prices(market_hash_names)

                # API调用成功后才记录
                self._record_api_call(is_batch=True, count=len(batch_tasks))

            except Exception as api_error:
                self.logger.error(f"批量API调用失败: {api_error}")
                raise
            
            successful = 0
            failed = 0
            
            for task in batch_tasks:
                try:
                    response = price_responses.get(task.market_hash_name)
                    if response and response.success:
                        # 保存价格数据（过滤零价格）
                        await self._save_price_data(task, response)
                        task.last_update = datetime.now()
                        successful += 1
                    else:
                        task.retry_count += 1
                        failed += 1
                        
                except Exception as e:
                    self.logger.error(f"处理任务失败: {task.market_hash_name}, 错误: {e}")
                    task.retry_count += 1
                    failed += 1
                finally:
                    self.processing_tasks.discard(task.item_id)
            
            return {'processed': len(batch_tasks), 'successful': successful, 'failed': failed}
            
        except Exception as e:
            self.logger.error(f"批量处理失败: {e}")
            for task in batch_tasks:
                task.retry_count += 1
                self.processing_tasks.discard(task.item_id)
            return {'processed': len(batch_tasks), 'successful': 0, 'failed': len(batch_tasks)}

    async def _process_single_tasks(self, tasks: List[UpdateTask]) -> Dict[str, Any]:
        """使用单个接口处理任务"""
        if not tasks:
            return {'processed': 0, 'successful': 0, 'failed': 0}

        processed = 0
        successful = 0
        failed = 0

        for task in tasks:
            try:
                # 标记为处理中
                self.processing_tasks.add(task.item_id)

                # 检查调用次数限制
                single_calls = self.call_stats['minute_single_calls']
                single_limit = self.api_limits['single_requests_per_minute']

                if single_calls >= single_limit:
                    self.logger.warning("单个接口调用次数已达上限，停止处理")
                    break

                # 检查间隔时间并等待
                now = datetime.now()
                if self.call_stats['last_single_call']:
                    time_since_last = (now - self.call_stats['last_single_call']).total_seconds()
                    required_interval = self.api_limits['single_request_interval']

                    if time_since_last < required_interval:
                        wait_time = required_interval - time_since_last
                        self.logger.debug(f"等待 {wait_time:.1f} 秒后进行单个API调用")
                        await asyncio.sleep(wait_time)

                try:
                    # 调用单个API
                    price_response = await self.api_client.get_single_price(task.market_hash_name)

                    # API调用成功后才记录
                    self._record_api_call(is_batch=False, count=1)

                    try:
                        self.logger.debug(f"开始保存价格数据: {task.market_hash_name}")

                        # 保存价格数据
                        await self._save_price_data(task, price_response)

                        # 更新任务状态
                        task.last_update = datetime.now()
                        task.retry_count = 0
                        successful += 1

                        self.logger.debug(f"单个接口成功更新: {task.market_hash_name}")

                    except Exception as save_error:
                        self.logger.error(f"保存价格数据失败: {task.market_hash_name} - {save_error}")
                        self.logger.error(f"保存错误详情: {type(save_error).__name__}: {save_error}")
                        import traceback
                        self.logger.error(f"保存错误堆栈: {traceback.format_exc()}")

                        # 即使保存失败，API调用也是成功的，不增加失败计数
                        # 但任务状态不更新，下次会重试
                        task.retry_count += 1
                        failed += 1

                except Exception as api_error:
                    self.logger.error(f"单个API调用失败: {task.market_hash_name} - {api_error}")
                    task.retry_count += 1
                    failed += 1

                processed += 1

            except Exception as e:
                self.logger.error(f"处理单个任务失败: {task.market_hash_name} - {e}")
                task.retry_count += 1
                failed += 1
                processed += 1
            finally:
                # 移除处理中标记
                self.processing_tasks.discard(task.item_id)

        return {'processed': processed, 'successful': successful, 'failed': failed}

    async def _save_price_data(self, task: UpdateTask, response):
        """保存价格数据，过滤零价格"""
        saved_count = 0

        self.logger.debug(f"开始保存价格数据: {task.market_hash_name}, 平台数: {len(response.platform_prices)}")

        for platform_price in response.platform_prices:
            self.logger.debug(f"处理平台: {platform_price.platform}, 售价: {platform_price.sell_price}, 求购: {platform_price.bidding_price}")

            # 过滤零价格数据
            if platform_price.sell_price <= 0 and platform_price.bidding_price <= 0:
                self.logger.debug(f"跳过零价格平台: {platform_price.platform}")
                continue

            try:
                # 确定数据源：智能价格更新池使用的都是SteamDT API数据
                data_source = 'steamdt'

                # 标准化平台名称：确保Steam平台统一为大写STEAM
                platform_name = platform_price.platform
                if platform_name.upper() == 'STEAM':
                    platform_name = 'STEAM'

                self.platform_price_dao.save_platform_price(
                    item_id=task.item_id,
                    market_hash_name=task.market_hash_name,
                    platform=platform_name,  # 使用标准化后的平台名称
                    platform_item_id=platform_price.platform_item_id,
                    sell_price=platform_price.sell_price,
                    sell_count=platform_price.sell_count,
                    bidding_price=platform_price.bidding_price,
                    bidding_count=platform_price.bidding_count,
                    steamdt_update_time=platform_price.update_time,
                    query_time=response.query_time,
                    data_source=data_source  # 明确设置数据源
                )
                saved_count += 1
                self.logger.debug(f"成功保存平台价格: {platform_price.platform}")
            except Exception as e:
                self.logger.error(f"保存价格数据失败: {task.market_hash_name} - {platform_price.platform}, 错误: {e}")
                self.logger.error(f"保存错误详情: {type(e).__name__}: {e}")
                import traceback
                self.logger.error(f"保存错误堆栈: {traceback.format_exc()}")
                raise  # 重新抛出异常，让上层处理

        # 无论是否保存了价格数据，都要更新items表的last_price_update字段
        try:
            self.item_dao.update_item_price_update_time(task.item_id)
            self.logger.debug(f"已更新饰品 {task.item_id} 的价格更新时间")
        except Exception as e:
            self.logger.error(f"更新饰品价格更新时间失败: {task.market_hash_name} - {e}")

        # 如果保存了有效价格数据，计算并更新搬砖比例
        if saved_count > 0:
            try:
                self.arbitrage_calculator.update_item_arbitrage_ratio(task.item_id)
                self.logger.debug(f"更新搬砖比例成功: {task.market_hash_name}")
            except Exception as e:
                self.logger.error(f"更新搬砖比例失败: {task.market_hash_name} - {e}")

        if saved_count > 0:
            self.logger.debug(f"保存了 {saved_count} 条有效价格记录: {task.market_hash_name}")
        else:
            self.logger.warning(f"没有保存任何价格记录: {task.market_hash_name}")
    
    async def run_continuous_update(self):
        """持续运行更新循环"""
        self.logger.info("🔄 开始持续价格更新循环")

        try:
            while self.is_running and self.continuous_mode:
                try:
                    cycle_stats = await self._run_single_cycle()

                    # 记录统计信息
                    if cycle_stats['total_processed'] > 0:
                        self.logger.info(f"📊 更新周期完成: 批量{cycle_stats['batch_processed']}个, "
                                       f"单个{cycle_stats['single_processed']}个, "
                                       f"总计{cycle_stats['total_processed']}个, "
                                       f"耗时{cycle_stats['duration']:.1f}秒")
                        # 有任务处理时，短暂休息
                        await asyncio.sleep(2)
                    else:
                        # 没有任务处理时，等待更长时间避免无效循环
                        await asyncio.sleep(10)

                except asyncio.CancelledError:
                    self.logger.info("🛑 持续更新循环被取消")
                    break
                except Exception as e:
                    self.logger.error(f"更新循环异常: {e}", exc_info=True)
                    await asyncio.sleep(5)  # 异常时等待更长时间

        except asyncio.CancelledError:
            self.logger.info("🛑 持续更新循环被取消")
        finally:
            self.logger.info("🛑 持续价格更新循环已停止")

    async def _run_single_cycle(self) -> Dict[str, Any]:
        """运行单次更新周期 - 充分利用批量和单个接口更新items表饰品"""
        cycle_stats = {
            'batch_processed': 0,
            'single_processed': 0,
            'total_processed': 0,
            'start_time': datetime.now()
        }

        now = datetime.now()

        # 获取所有需要更新的任务（不区分优先级，统一处理items表中的饰品）
        all_pending_tasks = []
        for task in self.high_priority_queue + self.low_priority_queue:
            if task.should_update(now) and task.item_id not in self.processing_tasks:
                all_pending_tasks.append(task)

        if not all_pending_tasks:
            cycle_stats['end_time'] = datetime.now()
            cycle_stats['duration'] = (cycle_stats['end_time'] - cycle_stats['start_time']).total_seconds()
            return cycle_stats

        # 策略1: 优先使用批量接口（效率最高：100个/次）
        can_batch = self._can_make_api_call(is_batch=True)
        has_enough_tasks = len(all_pending_tasks) >= 5

        self.logger.info(f"🔍 批量接口检查: 可调用={can_batch}, 任务数={len(all_pending_tasks)}, 需要>=5")

        if can_batch and has_enough_tasks:
            # 取最多100个任务进行批量处理
            batch_tasks = all_pending_tasks[:self.api_limits['batch_size']]

            # 标记为处理中
            for task in batch_tasks:
                self.processing_tasks.add(task.item_id)

            batch_result = await self._process_batch_tasks(batch_tasks)
            cycle_stats['batch_processed'] = batch_result['processed']
            cycle_stats['total_processed'] += batch_result['processed']

            self.logger.info(f"📦 批量接口处理了 {batch_result['processed']} 个饰品")

            # 从待处理列表中移除已处理的任务
            all_pending_tasks = all_pending_tasks[len(batch_tasks):]

        # 策略2: 使用单个接口处理剩余任务（充分利用60次/分钟）
        if all_pending_tasks and self._can_make_api_call(is_batch=False):
            # 计算还能调用多少次单个接口
            remaining_single_calls = self._get_remaining_single_calls()

            if remaining_single_calls > 0:
                # 取适量任务进行单个处理
                single_tasks = all_pending_tasks[:min(remaining_single_calls, len(all_pending_tasks))]

                single_result = await self._process_single_tasks(single_tasks)
                cycle_stats['single_processed'] = single_result['processed']
                cycle_stats['total_processed'] += single_result['processed']

                self.logger.info(f"🔍 单个接口处理了 {single_result['processed']} 个饰品")

        cycle_stats['end_time'] = datetime.now()
        cycle_stats['duration'] = (cycle_stats['end_time'] - cycle_stats['start_time']).total_seconds()

        # 更新总体统计
        self.update_stats['total_processed'] += cycle_stats['total_processed']

        return cycle_stats

    async def run_update_cycle(self) -> Dict[str, Any]:
        """运行一次更新周期（兼容性方法）"""
        if not self.is_running:
            return {'message': '更新池未运行'}

        return await self._run_single_cycle()
    
    async def start(self):
        """启动更新池"""
        if self.is_running:
            self.logger.warning("更新池已在运行")
            return

        # 检查是否已有相同API密钥的实例在运行（避免API调用冲突）
        instance_id = f"api_key_{self.api_key}"
        if instance_id in SmartPriceUpdatePool._running_instances:
            self.logger.warning(f"⚠️ 已有使用相同API密钥的更新池实例在运行，避免API调用冲突")
            self.logger.warning(f"   当前API密钥: {self.api_key[:10]}...")
            self.logger.warning(f"   建议：等待现有实例完成或使用不同的API密钥")
            return

        self.logger.info("🚀 启动智能价格更新池")

        # 添加到运行实例集合
        SmartPriceUpdatePool._running_instances.add(instance_id)
        self.instance_id = instance_id
        self.is_running = True

        try:
            # 初始化队列
            await self.initialize_queues()

            # 启动持续更新循环（在后台运行）
            if self.continuous_mode:
                self.continuous_task = asyncio.create_task(self.run_continuous_update())

            self.logger.info("✅ 智能价格更新池启动成功")

        except Exception as e:
            # 启动失败时清理
            SmartPriceUpdatePool._running_instances.discard(instance_id)
            self.is_running = False
            raise
    
    def stop(self):
        """停止更新池"""
        self.logger.info("⏹️ 停止智能价格更新池")
        self.is_running = False

        # 从运行实例集合中移除
        if hasattr(self, 'instance_id'):
            SmartPriceUpdatePool._running_instances.discard(self.instance_id)

        # 取消持续更新任务
        if self.continuous_task and not self.continuous_task.done():
            self.continuous_task.cancel()
            self.logger.info("✅ 持续更新任务已取消")

    @classmethod
    def get_running_instances(cls):
        """获取当前运行的实例列表"""
        return list(cls._running_instances)

    @classmethod
    def is_any_instance_running(cls):
        """检查是否有任何实例在运行"""
        return len(cls._running_instances) > 0
    
    def get_status(self) -> Dict[str, Any]:
        """获取更新池状态"""
        return {
            'is_running': self.is_running,
            'queue_status': {
                'high_priority_queue': len(self.high_priority_queue),
                'low_priority_queue': len(self.low_priority_queue),
                'processing_tasks': len(self.processing_tasks)
            },
            'api_limits': self.api_limits,
            'call_stats': self.call_stats,
            'update_stats': self.update_stats
        }
