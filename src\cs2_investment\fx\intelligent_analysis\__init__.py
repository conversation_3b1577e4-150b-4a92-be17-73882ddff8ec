"""
CS2饰品智能投资决策系统 - 智能分析模块

本模块实现基于多时间框架技术分析和供需关系分析的智能投资决策功能。
采用渐进式集成策略，与现有系统完全兼容。

模块结构：
- core: 核心分析引擎
- indicators: 技术指标计算
- strategies: 分层分析策略
- monitoring: 智能监控系统
- integration: 系统集成适配

版本: 1.0.0
作者: CS2投资分析系统
"""

import sys
from pathlib import Path
from loguru import logger

__version__ = "1.0.0"
__author__ = "CS2投资分析系统"

# 配置日志系统
def _configure_logger():
    """配置智能分析模块的日志系统"""
    try:
        # 移除默认的日志处理器
        logger.remove()

        # 添加控制台日志处理器
        logger.add(
            sys.stderr,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>intelligent_analysis</cyan> | <level>{message}</level>",
            level="INFO"
        )

        # 添加文件日志处理器
        log_path = Path("logs/intelligent_analysis.log")
        log_path.parent.mkdir(exist_ok=True)
        logger.add(
            log_path,
            rotation="10 MB",
            retention="30 days",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name} | {message}",
            level="DEBUG"
        )

        logger.info("智能分析模块日志系统初始化完成")

    except Exception as e:
        print(f"日志系统初始化失败: {e}")

# 初始化日志系统
_configure_logger()

# 导入核心模块 (使用安全导入)
_available_modules = []

# 导入核心模块
try:
    from .core import *
    _available_modules.append("core")
    logger.info("核心模块加载成功")
except ImportError as e:
    logger.warning(f"核心模块加载失败: {e}")

# 导入技术指标模块
try:
    from .indicators import *
    _available_modules.append("indicators")
    logger.info("技术指标计算模块加载成功")
except ImportError as e:
    logger.warning(f"技术指标模块加载失败: {e}")

# 导入分析策略模块
try:
    from .strategies import *
    _available_modules.append("strategies")
    logger.info("分层分析策略模块加载成功")
except ImportError as e:
    logger.warning(f"分析策略模块加载失败: {e}")

# 导入监控系统模块
try:
    from .monitoring import *
    _available_modules.append("monitoring")
    logger.info("智能监控系统模块加载成功")
except ImportError as e:
    logger.warning(f"监控系统模块加载失败: {e}")

# 导入系统集成模块
try:
    from .integration import *
    _available_modules.append("integration")
    logger.info("系统集成模块加载成功")
except ImportError as e:
    logger.warning(f"系统集成模块加载失败: {e}")

logger.info(f"智能分析核心模块加载成功，可用模块: {_available_modules}")

# 动态构建__all__列表，只包含实际可用的组件
__all__ = []

# 核心模块组件
try:
    from .core.unified_data_manager import UnifiedDataManager
    __all__.append('UnifiedDataManager')
except ImportError:
    pass

try:
    from .core.multi_timeframe_engine import MultiTimeframeEngine
    __all__.append('MultiTimeframeEngine')
except ImportError:
    pass

try:
    from .core.decision_fusion_engine import DecisionFusionEngine
    __all__.append('DecisionFusionEngine')
except ImportError:
    pass

# 技术指标模块组件
try:
    from .indicators.enhanced_technical_calculator import EnhancedTechnicalCalculator
    __all__.append('EnhancedTechnicalCalculator')
except ImportError:
    pass

try:
    from .indicators.supply_demand_analyzer import SupplyDemandAnalyzer
    __all__.append('SupplyDemandAnalyzer')
except ImportError:
    pass

# 分析策略模块组件
try:
    from .strategies.strategic_analyzer import StrategicAnalyzer
    __all__.append('StrategicAnalyzer')
except ImportError:
    pass

try:
    from .strategies.tactical_analyzer import TacticalAnalyzer
    __all__.append('TacticalAnalyzer')
except ImportError:
    pass

try:
    from .strategies.execution_analyzer import ExecutionAnalyzer
    __all__.append('ExecutionAnalyzer')
except ImportError:
    pass

# 监控系统模块组件（使用占位符类）
try:
    from .monitoring import IntelligentScheduler, SignalGenerator
    __all__.extend(['IntelligentScheduler', 'SignalGenerator'])
except ImportError:
    pass

# 系统集成模块组件
try:
    from .integration.unified_analysis_interface import UnifiedAnalysisInterface
    __all__.append('UnifiedAnalysisInterface')
except ImportError:
    pass

try:
    from .integration.legacy_system_bridge import LegacySystemAdapter
    __all__.append('LegacySystemAdapter')
except ImportError:
    pass

logger.info(f"智能分析模块 v{__version__} 初始化完成")
