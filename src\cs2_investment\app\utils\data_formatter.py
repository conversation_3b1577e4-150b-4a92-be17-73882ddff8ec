"""
数据格式化工具

提供各种数据格式化功能。
"""

from typing import Optional, Union
from decimal import Decimal


def format_price(price: Union[float, Decimal, None]) -> str:
    """格式化价格显示"""
    if price is None:
        return "N/A"
    
    try:
        price_float = float(price)
        if price_float >= 10000:
            return f"¥{price_float:,.0f}"
        elif price_float >= 100:
            return f"¥{price_float:,.1f}"
        else:
            return f"¥{price_float:.2f}"
    except (ValueError, TypeError):
        return "N/A"


def format_percentage(percentage: Union[float, Decimal, None]) -> str:
    """格式化百分比显示"""
    if percentage is None:
        return "N/A"
    
    try:
        pct_float = float(percentage)
        if pct_float > 0:
            return f"+{pct_float:.1f}%"
        else:
            return f"{pct_float:.1f}%"
    except (ValueError, TypeError):
        return "N/A"


def format_number(number: Union[int, float, None], unit: str = "") -> str:
    """格式化数字显示"""
    if number is None:
        return "N/A"
    
    try:
        if isinstance(number, float):
            if number >= 1000000:
                return f"{number/1000000:.1f}M{unit}"
            elif number >= 1000:
                return f"{number/1000:.1f}K{unit}"
            else:
                return f"{number:.1f}{unit}"
        else:
            if number >= 1000000:
                return f"{number//1000000}M{unit}"
            elif number >= 1000:
                return f"{number//1000}K{unit}"
            else:
                return f"{number}{unit}"
    except (ValueError, TypeError):
        return "N/A"


def format_item_type(item_type: Optional[str]) -> str:
    """格式化饰品类型显示"""
    if not item_type:
        return "未知"
    
    type_mapping = {
        "CSGO_Type_Knife": "刀具",
        "CSGO_Type_Pistol": "手枪",
        "CSGO_Type_SMG": "冲锋枪",
        "CSGO_Type_Rifle": "步枪",
        "CSGO_Type_SniperRifle": "狙击枪",
        "CSGO_Type_Shotgun": "霰弹枪",
        "CSGO_Type_Machinegun": "机枪",
        "CSGO_Tool_Sticker": "贴纸",
        "CSGO_Type_WeaponCase": "武器箱",
        "CSGO_Tool_Keychain": "钥匙链"
    }
    
    return type_mapping.get(item_type, item_type)


def format_quality(quality: Optional[str]) -> str:
    """格式化品质显示"""
    if not quality:
        return "未知"
    
    quality_mapping = {
        "Factory New": "崭新出厂",
        "Minimal Wear": "略有磨损",
        "Field-Tested": "久经沙场",
        "Well-Worn": "破损不堪",
        "Battle-Scarred": "战痕累累"
    }
    
    return quality_mapping.get(quality, quality)


def format_rarity(rarity: Optional[str]) -> str:
    """格式化稀有度显示"""
    if not rarity:
        return "未知"
    
    rarity_mapping = {
        "Consumer Grade": "消费级",
        "Industrial Grade": "工业级",
        "Mil-Spec Grade": "军规级",
        "Restricted": "受限",
        "Classified": "保密",
        "Covert": "隐秘",
        "Extraordinary": "非凡"
    }
    
    return rarity_mapping.get(rarity, rarity)


def get_rarity_color(rarity: Optional[str]) -> str:
    """获取稀有度对应的颜色"""
    if not rarity:
        return "#CCCCCC"
    
    color_mapping = {
        "Consumer Grade": "#B0C3D9",
        "Industrial Grade": "#5E98D9", 
        "Mil-Spec Grade": "#4B69FF",
        "Restricted": "#8847FF",
        "Classified": "#D32CE6",
        "Covert": "#EB4B4B",
        "Extraordinary": "#E4AE39"
    }
    
    return color_mapping.get(rarity, "#CCCCCC")


def format_time_ago(timestamp) -> str:
    """格式化时间显示（多久之前）"""
    from datetime import datetime, timedelta
    
    if not timestamp:
        return "未知"
    
    try:
        if isinstance(timestamp, str):
            timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        
        now = datetime.now()
        if timestamp.tzinfo:
            now = now.replace(tzinfo=timestamp.tzinfo)
        
        diff = now - timestamp
        
        if diff.days > 0:
            return f"{diff.days}天前"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours}小时前"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes}分钟前"
        else:
            return "刚刚"
    except Exception:
        return "未知"
