#!/usr/bin/env python3
"""
测试专业图表系统功能
"""

import sys
import traceback
from datetime import datetime

def test_imports():
    """测试关键模块导入"""
    print("🔍 测试关键模块导入...")
    
    try:
        import numpy as np
        print(f"✅ NumPy {np.__version__} 导入成功")
        
        import pandas as pd
        print(f"✅ Pandas {pd.__version__} 导入成功")
        
        import matplotlib.pyplot as plt
        print(f"✅ Matplotlib {plt.matplotlib.__version__} 导入成功")
        
        import streamlit as st
        print(f"✅ Streamlit {st.__version__} 导入成功")
        
        from src.cs2_investment.fx.syncps.professional_chart_system import ProfessionalChartSystem
        print("✅ 专业图表系统导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False

def test_chart_creation():
    """测试图表创建功能"""
    print("\n🧪 测试图表创建功能...")

    try:
        import pandas as pd
        import numpy as np
        import matplotlib.pyplot as plt

        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=30, freq='D')
        test_data = pd.DataFrame({
            'date': dates,
            'price': np.random.uniform(100, 200, 30),
            'volume': np.random.uniform(1000, 5000, 30)
        })

        print("✅ 测试数据创建成功")

        # 测试基本的 matplotlib 图表创建
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.plot(test_data['date'], test_data['price'], label='价格')
        ax.set_title('测试价格趋势图')
        ax.set_xlabel('日期')
        ax.set_ylabel('价格')
        ax.legend()
        plt.close(fig)  # 关闭图表以释放内存

        print("✅ 基本图表创建成功")

        # 测试专业图表系统类的导入（不实例化）
        from src.cs2_investment.fx.syncps.professional_chart_system import ProfessionalChartSystem
        print("✅ 专业图表系统类导入成功")

        return True

    except Exception as e:
        print(f"❌ 图表创建失败: {e}")
        traceback.print_exc()
        return False

def test_linkcolumn():
    """测试 LinkColumn 功能"""
    print("\n🔗 测试 LinkColumn 功能...")
    
    try:
        import streamlit as st
        import pandas as pd
        
        # 创建测试数据
        df = pd.DataFrame({
            '链接': ['https://example.com', 'https://google.com']
        })
        
        # 测试 LinkColumn 配置
        config = {
            "链接": st.column_config.LinkColumn(
                "链接",
                help="测试链接",
                display_text=":material/open_in_new:",
                width="small"
            )
        }
        
        print("✅ LinkColumn 配置创建成功")
        return True
        
    except Exception as e:
        print(f"❌ LinkColumn 测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 CS2 饰品投资分析系统 - 功能测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    all_tests_passed = True
    
    # 测试导入
    if not test_imports():
        all_tests_passed = False
    
    # 测试图表创建
    if not test_chart_creation():
        all_tests_passed = False
    
    # 测试 LinkColumn
    if not test_linkcolumn():
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    
    if all_tests_passed:
        print("🎉 所有测试通过！系统功能正常")
        print("\n📝 下一步:")
        print("1. 启动 Streamlit 应用: streamlit run start_streamlit_app.py")
        print("2. 访问收藏页面测试 LinkColumn 功能")
        print("3. 测试专业图表生成功能")
        return 0
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
