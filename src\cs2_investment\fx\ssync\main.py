#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ssync系统主入口程序
CS2饰品实时监控和技术分析系统

使用方法:
    python main.py "饰品名称"                    # 基础监控
    python main.py "饰品名称" --chart            # 监控+标准图表
    python main.py "饰品名称" --enhanced         # 监控+增强版图表
    python main.py "饰品名称" --dashboard        # 综合分析仪表板
    python main.py --help                       # 显示帮助
"""

import sys
import os
import argparse
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_banner():
    """打印系统横幅"""
    print("="*80)
    print("🎯 CS2饰品实时监控和技术分析系统 (ssync)")
    print("📊 专业级实时监控 | 增强版技术分析 | 智能交易信号")
    print("="*80)

def print_usage():
    """打印使用说明"""
    print("\n📖 使用说明:")
    print("1. 确保数据文件存在于正确的目录结构中")
    print("2. 运行命令: python main.py [饰品名称] [选项]")
    print("\n📁 数据目录结构:")
    print("   ../[饰品名称]/")
    print("   ├── 时k.json")
    print("   ├── 日k1.json")
    print("   ├── 日k2.json") 
    print("   ├── 周k.json")
    print("   └── 走势.json")
    print("\n💡 使用示例:")
    print('   python main.py "AK-47 传承 (久经沙场)"                    # 监控+增强版图表（默认）')
    print('   python main.py "AK-47 传承 (久经沙场)" --chart            # 明确指定生成图表')

def list_available_data():
    """列出可用的饰品数据"""
    print("\n📊 扫描可用的饰品数据...")
    print("-" * 60)
    
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    required_files = ['时k.json', '日k1.json', '日k2.json', '周k.json', '走势.json']
    available_items = []
    
    for item in os.listdir(parent_dir):
        item_path = os.path.join(parent_dir, item)
        if os.path.isdir(item_path) and not item.startswith('.'):
            # 检查必需文件
            missing_files = []
            for file_name in required_files:
                file_path = os.path.join(item_path, file_name)
                if not os.path.exists(file_path):
                    missing_files.append(file_name)
            
            if not missing_files:
                available_items.append(item)
                print(f"✅ {item}")
            else:
                print(f"❌ {item} (缺失: {', '.join(missing_files)})")
    
    print(f"\n📈 共找到 {len(available_items)} 个完整的饰品数据集")
    return available_items

def run_monitoring(skin_name: str, generate_chart: bool = True):
    """运行实时监控"""
    
    print(f"\n🚀 启动实时监控: {skin_name}")
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 60)
    
    try:
        # 初始化监控系统
        from real_time_monitor import FixedRealTimeMonitor
        monitor = FixedRealTimeMonitor(skin_name)
        
        # 加载数据
        print("📊 正在加载数据...")
        if not monitor.load_data():
            print("❌ 数据加载失败，请检查数据文件")
            return False
        
        print("✅ 数据加载成功")
        
        # 显示监控仪表板
        print("\n📈 运行实时监控分析...")
        monitor.print_fixed_dashboard()
        
        # 生成增强版图表（默认功能）
        if generate_chart:
            print(f"\n🎨 生成增强版技术分析图表...")
            chart_path = monitor.generate_technical_chart(
                enhanced=True,  # 固定使用增强版
                show_chart=False
            )
            if chart_path:
                print(f"✅ 增强版技术分析图表已生成: {chart_path}")
            else:
                print(f"❌ 增强版技术分析图表生成失败")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入错误: {e}")
        print("💡 请确保所有必需的模块文件都存在")
        return False
    except Exception as e:
        print(f"❌ 监控过程中发生错误: {str(e)}")
        print("💡 请检查:")
        print("   1. 数据文件格式是否正确")
        print("   2. 饰品名称是否准确")
        print("   3. 系统依赖是否完整")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 创建参数解析器
    parser = argparse.ArgumentParser(
        description='CS2饰品实时监控和技术分析系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py "AK-47 传承 (久经沙场)"                    # 监控+增强版图表（默认）
  python main.py "AK-47 传承 (久经沙场)" --chart            # 明确指定生成图表
  python main.py --list-available                        # 列出可用数据
        """
    )
    
    parser.add_argument(
        'skin_name',
        nargs='?',
        help='要分析的饰品名称'
    )
    
    parser.add_argument(
        '--chart',
        action='store_true',
        help='生成增强版技术分析图表（默认功能）'
    )
    
    parser.add_argument(
        '--list-available',
        action='store_true',
        help='列出可用的饰品数据'
    )
    
    # 解析参数
    args = parser.parse_args()
    
    # 处理列出可用数据
    if args.list_available:
        list_available_data()
        return
    
    # 检查饰品名称
    if not args.skin_name:
        print("❌ 请提供饰品名称")
        print_usage()
        return
    
    skin_name = args.skin_name
    
    # 确定图表生成选项 - 默认生成增强版图表
    generate_chart = True  # 总是生成图表

    # 运行监控
    success = run_monitoring(
        skin_name,
        generate_chart=generate_chart
    )
    
    if success:
        print(f"\n🎉 {skin_name} 监控完成!")
        if generate_chart:
            print("🎨 增强版技术分析图表已生成!")
        print("\n💡 提示:")
        print("   • 默认生成增强版技术分析图表")
        print("   • 使用 --chart 明确指定生成图表")
        print("   • 可以使用不同的饰品名称重复运行分析")
    else:
        print(f"\n💔 {skin_name} 监控失败")
        print("🔧 请检查系统配置和数据文件")

if __name__ == "__main__":
    main()
