"""
数据抓取服务

集成SteamDT数据抓取功能，直接保存到数据库
支持12小时检查机制和1000条数据抓取
"""

import asyncio
import json
import time
import random
import re
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path
from playwright.async_api import async_playwright
from loguru import logger

from ..dao.market_snapshot_dao import MarketSnapshotDAO
from ..dao.item_dao import ItemDAO
from ..services.data_import_service import DataImportService
from ..config.settings import get_settings


class DataScrapingService:
    """数据抓取服务"""
    
    def __init__(self):
        self.base_url = "https://steamdt.com/ladders"
        self.api_url = "https://sdt-api.ok-skins.com/user/ranking/v1/page"
        self.collected_data = []
        self.settings = get_settings()
        self.logger = logger.bind(service="DataScrapingService")

        # 加载配置
        from ..config.timer_config import get_timer_config
        self.config = get_timer_config()

        # DAO实例
        self.snapshot_dao = MarketSnapshotDAO()
        self.item_dao = ItemDAO()
        self.import_service = DataImportService()
        
        # 榜单配置
        self.rankings = {
            "price_up": {
                "name": "价格榜-上涨榜",
                "tab_name": "价格榜",
                "sub_options": ["涨跌榜(百分比)"],
                "sort_order": "desc",
                "time_periods": ["近1天", "近3天", "近7天", "近15天", "近一个月", "近三个月"]
            },
            "price_down": {
                "name": "价格榜-下跌榜",
                "tab_name": "价格榜",
                "sub_options": ["涨跌榜(百分比)"],
                "sort_order": "asc",
                "time_periods": ["近1天", "近3天", "近7天", "近15天", "近一个月", "近三个月"]
            },
            "inventory": {
                "name": "在售数榜",
                "tab_name": "在售数榜",
                "sub_options": ["在售数变化(百分比)"],
                "time_periods": ["近1天", "近3天", "近7天", "近15天", "近一个月", "近三个月"]
            },
            "transaction_volume": {
                "name": "成交榜-成交量",
                "tab_name": "成交榜",
                "sub_options": ["成交量"],
                "time_periods": ["近1天", "近3天", "近7天", "近15天", "近一个月", "近三个月"]
            },
            "transaction_amount": {
                "name": "成交榜-成交额",
                "tab_name": "成交榜",
                "sub_options": ["成交额"],
                "time_periods": ["近1天", "近3天", "近7天", "近15天", "近一个月", "近三个月"]
            },
            "popularity": {
                "name": "饰品热度榜",
                "tab_name": "饰品热度榜",
                "sub_options": ["热度榜单"],
                "time_periods": ["近1天"]
            },
            "popularity_rising": {
                "name": "热度上升榜",
                "tab_name": "饰品热度榜",
                "sub_options": ["热度上升榜单"],
                "time_periods": ["近1天"]
            }
        }
        
        self.max_items = 1000  # 每个榜单抓取前1000条
        self.delay_range = (15, 45)  # 滚动间隔15-45秒
        self.check_interval_hours = 12  # 12小时检查间隔
    
    def check_recent_data(self, ranking_key: str, sub_option: str, time_period: str) -> bool:
        """检查12小时内是否已有完整数据"""
        try:
            # 构建数据源名称
            data_source = f"{ranking_key}_{sub_option}_{time_period}".replace("(", "").replace(")", "").replace(" ", "_")
            
            # 检查12小时内的数据
            cutoff_time = datetime.now() - timedelta(hours=self.check_interval_hours)
            
            # 查询最近的快照数据
            recent_count = self.snapshot_dao.count_recent_snapshots(
                data_source=data_source,
                since_time=cutoff_time
            )
            
            # 如果有足够的数据（至少800条，考虑到可能的重复），则跳过
            if recent_count >= 800:
                self.logger.info(f"✅ 跳过 {data_source}: 12小时内已有 {recent_count} 条数据")
                return True
            else:
                self.logger.info(f"🔄 需要抓取 {data_source}: 12小时内只有 {recent_count} 条数据")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 检查最近数据失败: {e}")
            return False
    
    async def create_browser(self):
        """创建浏览器实例"""
        playwright = await async_playwright().start()

        # 从配置中读取headless设置
        headless_mode = self.config.scraping.playwright_headless
        self.logger.info(f"创建浏览器: headless={headless_mode}")

        browser = await playwright.chromium.launch(headless=headless_mode)
        context = await browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        page = await context.new_page()
        return playwright, browser, page
    
    async def close_popup_if_exists(self, page):
        """关闭可能存在的弹窗"""
        try:
            close_button = await page.query_selector('button[aria-label="关闭此对话框"]')
            if close_button:
                await close_button.click()
                await asyncio.sleep(2)
                self.logger.info("✅ 已关闭弹窗")
                return True
        except Exception as e:
            self.logger.debug(f"关闭弹窗时出现异常（可能不存在弹窗）: {e}")
        return False
    
    def setup_api_monitoring(self, page):
        """设置API监控"""
        def handle_response(response):
            if self.api_url in response.url:
                self.logger.info(f"🌐 捕获API响应: {response.status} {response.url}")
                asyncio.create_task(self.process_api_response(response))
        
        page.on('response', handle_response)
    
    async def process_api_response(self, response):
        """处理API响应数据"""
        try:
            if response.status == 200:
                data = await response.json()
                
                if isinstance(data, dict) and 'data' in data:
                    data_field = data['data']
                    if isinstance(data_field, dict) and 'list' in data_field:
                        items = data_field['list']
                        if isinstance(items, list) and items:
                            self.collected_data.extend(items)
                            self.logger.info(f"✅ 已收集{len(items)}条API数据，总计{len(self.collected_data)}条")
        
        except Exception as e:
            self.logger.error(f"❌ 处理API响应失败: {e}")
    
    async def scroll_and_load_data(self, page, target_count=1000):
        """滚动页面加载数据"""
        self.logger.info(f"🔄 开始滚动加载数据，目标数量: {target_count}")
        
        self.collected_data = []
        scroll_count = 0
        max_scrolls = 100  # 增加最大滚动次数以获取1000条数据
        no_new_data_count = 0
        
        while len(self.collected_data) < target_count and scroll_count < max_scrolls:
            current_count = len(self.collected_data)
            
            # 随机滚动方式
            scroll_type = random.choice(["full", "partial", "smooth"])
            if scroll_type == "full":
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            elif scroll_type == "partial":
                scroll_distance = random.randint(800, 1500)
                await page.evaluate(f"window.scrollBy(0, {scroll_distance})")
            else:
                await page.evaluate("window.scrollTo({top: document.body.scrollHeight, behavior: 'smooth'})")
            
            # 10%概率向上滚动
            if random.random() < 0.1:
                back_scroll = random.randint(200, 500)
                await page.evaluate(f"window.scrollBy(0, -{back_scroll})")
                await asyncio.sleep(random.uniform(2, 5))
            
            # 等待数据加载
            base_wait = random.uniform(8, 15)
            await asyncio.sleep(base_wait)
            
            new_count = len(self.collected_data)
            
            if new_count > current_count:
                no_new_data_count = 0
                self.logger.info(f"📊 第{scroll_count + 1}次滚动，API数据从{current_count}增加到{new_count}")
            else:
                no_new_data_count += 1
                self.logger.info(f"⚠️ 第{scroll_count + 1}次滚动，API数据未增加 ({no_new_data_count}/5)")
            
            scroll_count += 1
            
            # 连续5次没有新数据，停止滚动
            if no_new_data_count >= 5:
                self.logger.info("🛑 连续多次无新数据，停止滚动")
                break
            
            # 随机等待
            delay = random.uniform(*self.delay_range)
            
            # 20%概率额外长时间停顿
            if random.random() < 0.2:
                extra_delay = random.uniform(60, 180)
                self.logger.info(f"😴 触发额外长停顿: {extra_delay/60:.1f}分钟")
                await asyncio.sleep(extra_delay)
            
            self.logger.info(f"⏳ 等待{delay:.1f}秒后继续...")
            await asyncio.sleep(delay)
        
        self.logger.info(f"✅ 滚动完成，共获取{len(self.collected_data)}条API数据")
        return self.collected_data[:target_count]
    
    def save_to_database(self, data: List[Dict], ranking_key: str, sub_option: str, time_period: str) -> Dict[str, Any]:
        """保存数据到数据库"""
        if not data:
            self.logger.warning("⚠️ 没有数据可保存")
            return {"success_count": 0, "error_count": 0}
        
        try:
            # 构建数据源名称
            data_source = f"{ranking_key}_{sub_option}_{time_period}".replace("(", "").replace(")", "").replace(" ", "_")
            
            # 使用现有的数据导入服务
            result = self.import_service.import_json_data(data, data_source)
            
            self.logger.info(f"✅ 数据已保存到数据库: {result['success_count']} 条成功, {result['error_count']} 条失败")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 保存数据到数据库失败: {e}")
            return {"success_count": 0, "error_count": len(data)}
    
    async def select_tab(self, page, tab_name):
        """选择榜单标签"""
        try:
            await page.wait_for_selector('[role="tab"]', timeout=15000)
            await page.click(f'[role="tab"]:has-text("{tab_name}")')
            await asyncio.sleep(3)
            self.logger.info(f"✅ 已选择标签: {tab_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ 选择标签失败 {tab_name}: {e}")
            try:
                await page.click(f'text="{tab_name}"')
                await asyncio.sleep(3)
                self.logger.info(f"✅ 使用备用选择器成功选择标签: {tab_name}")
                return True
            except Exception as e2:
                self.logger.error(f"❌ 备用选择器也失败: {e2}")
                return False
    
    async def select_sub_option(self, page, option_name):
        """选择子选项"""
        try:
            await page.click(f'text="{option_name}"')
            await asyncio.sleep(2)
            self.logger.info(f"✅ 已选择子选项: {option_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ 选择子选项失败 {option_name}: {e}")
            return False
    
    async def select_sort_order(self, page, sort_order):
        """选择排序方式"""
        try:
            if sort_order == "desc":
                current_sort = await page.query_selector('text="升序"')
                if current_sort:
                    self.logger.info("🔄 当前是升序，切换到降序排序（上涨榜）")
                    await current_sort.click()
                    await asyncio.sleep(random.uniform(3, 8))
                    self.logger.info("✅ 已切换到降序排序（上涨榜）")
                else:
                    self.logger.info("✅ 当前已是降序排序（上涨榜）")
            
            elif sort_order == "asc":
                current_sort = await page.query_selector('text="降序"')
                if current_sort:
                    self.logger.info("🔄 当前是降序，切换到升序排序（下跌榜）")
                    await current_sort.click()
                    await asyncio.sleep(random.uniform(3, 8))
                    self.logger.info("✅ 已切换到升序排序（下跌榜）")
                else:
                    self.logger.info("✅ 当前已是升序排序（下跌榜）")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 选择排序方式失败 {sort_order}: {e}")
            return True  # 继续执行，排序失败不影响数据抓取
    
    async def select_time_period(self, page, time_period):
        """选择时间周期"""
        try:
            time_selector = page.locator('div').filter(has_text=re.compile(r'^近\d+天$|^近\d+个月$|^近一个月$|^近三个月$|^近六个月$|^近一年$')).nth(1)
            await time_selector.click()
            await asyncio.sleep(2)
            
            await page.get_by_role('option', name=time_period).click()
            await asyncio.sleep(3)
            self.logger.info(f"✅ 已选择时间周期: {time_period}")
            return True
        except Exception as e:
            self.logger.error(f"❌ 选择时间周期失败 {time_period}: {e}")
            try:
                await page.click('[role="combobox"]')
                await asyncio.sleep(2)
                await page.click(f'[role="option"]:has-text("{time_period}")')
                await asyncio.sleep(3)
                self.logger.info(f"✅ 使用备用选择器成功选择时间周期: {time_period}")
                return True
            except Exception as e2:
                self.logger.error(f"❌ 备用时间选择器也失败: {e2}")
                return False

    async def scrape_ranking(self, page, ranking_key: str, sub_option: str, time_period: str) -> Optional[Dict[str, Any]]:
        """抓取指定榜单数据"""
        ranking_config = self.rankings[ranking_key]
        self.logger.info(f"🎯 开始抓取: {ranking_config['name']} - {sub_option} - {time_period}")

        # 检查12小时内是否已有数据
        if self.check_recent_data(ranking_key, sub_option, time_period):
            return {"skipped": True, "reason": "12小时内已有完整数据"}

        try:
            # 1. 选择榜单标签
            if not await self.select_tab(page, ranking_config['tab_name']):
                return None

            # 2. 选择子选项（饰品热度榜不需要选择子选项）
            if ranking_config['tab_name'] != "饰品热度榜":
                if not await self.select_sub_option(page, sub_option):
                    return None
            else:
                self.logger.info("✅ 饰品热度榜无需选择子选项，直接使用默认选项")

            # 3. 选择时间周期（饰品热度榜不需要选择时间周期）
            if ranking_config['tab_name'] != "饰品热度榜":
                if not await self.select_time_period(page, time_period):
                    return None
            else:
                self.logger.info("✅ 饰品热度榜无需选择时间周期，默认为近1天")

            # 4. 选择排序方式（饰品热度榜不需要排序）
            if 'sort_order' in ranking_config and ranking_config['tab_name'] != "饰品热度榜":
                await self.select_sort_order(page, ranking_config['sort_order'])
            elif ranking_config['tab_name'] == "饰品热度榜":
                self.logger.info("✅ 饰品热度榜无需选择排序方式，使用默认排序")

            # 5. 滚动加载数据
            data = await self.scroll_and_load_data(page, self.max_items)

            if not data:
                self.logger.warning(f"⚠️ 没有获取到数据: {ranking_key}")
                return None

            # 6. 保存数据到数据库
            result = self.save_to_database(data, ranking_key, sub_option, time_period)

            return {
                "ranking_key": ranking_key,
                "sub_option": sub_option,
                "time_period": time_period,
                "data_count": len(data),
                "success_count": result["success_count"],
                "error_count": result["error_count"]
            }

        except Exception as e:
            self.logger.error(f"❌ 抓取榜单数据失败: {e}")
            return None

    async def run_scraping(self, selected_rankings: Optional[List[str]] = None) -> Dict[str, Any]:
        """运行数据抓取"""
        self.logger.info("🚀 开始SteamDT数据抓取")

        playwright, browser, page = await self.create_browser()

        results = {
            "total_rankings": 0,
            "successful_rankings": 0,
            "skipped_rankings": 0,
            "failed_rankings": 0,
            "total_data_count": 0,
            "details": []
        }

        try:
            # 设置API监控
            self.setup_api_monitoring(page)

            # 访问页面
            self.logger.info("🌐 正在访问SteamDT页面...")
            await page.goto(self.base_url)
            await asyncio.sleep(5)

            # 关闭可能存在的弹窗
            await self.close_popup_if_exists(page)

            # 确定要抓取的榜单
            rankings_to_scrape = selected_rankings or list(self.rankings.keys())

            # 遍历所有榜单配置
            for ranking_key in rankings_to_scrape:
                if ranking_key not in self.rankings:
                    self.logger.warning(f"⚠️ 未知的榜单类型: {ranking_key}")
                    continue

                config = self.rankings[ranking_key]

                for sub_option in config['sub_options']:
                    for time_period in config['time_periods']:
                        results["total_rankings"] += 1

                        result = await self.scrape_ranking(page, ranking_key, sub_option, time_period)

                        if result:
                            if result.get("skipped"):
                                results["skipped_rankings"] += 1
                                self.logger.info(f"⏭️ 跳过: {ranking_key} - {sub_option} - {time_period}")
                            else:
                                results["successful_rankings"] += 1
                                results["total_data_count"] += result.get("data_count", 0)
                                self.logger.info(f"✅ 完成: {ranking_key} - {sub_option} - {time_period} ({result.get('data_count', 0)}条)")
                        else:
                            results["failed_rankings"] += 1
                            self.logger.error(f"❌ 失败: {ranking_key} - {sub_option} - {time_period}")

                        results["details"].append(result)

                        # 榜单间等待
                        if not (result and result.get("skipped")):
                            delay = random.uniform(60, 180)  # 1-3分钟
                            self.logger.info(f"⏳ 榜单间等待{delay/60:.1f}分钟...")
                            await asyncio.sleep(delay)

                            # 30%概率额外超长停顿
                            if random.random() < 0.3:
                                super_long_delay = random.uniform(300, 600)  # 5-10分钟
                                self.logger.info(f"😴 触发超长停顿: {super_long_delay/60:.1f}分钟")
                                await asyncio.sleep(super_long_delay)

            self.logger.info(f"🎉 抓取完成！")
            self.logger.info(f"   总榜单: {results['total_rankings']}")
            self.logger.info(f"   成功: {results['successful_rankings']}")
            self.logger.info(f"   跳过: {results['skipped_rankings']}")
            self.logger.info(f"   失败: {results['failed_rankings']}")
            self.logger.info(f"   总数据: {results['total_data_count']} 条")

        except Exception as e:
            self.logger.error(f"❌ 抓取过程中发生错误: {e}")
            results["error"] = str(e)
        finally:
            await browser.close()
            await playwright.stop()

        return results

    def get_available_rankings(self) -> Dict[str, str]:
        """获取可用的榜单类型"""
        return {key: config["name"] for key, config in self.rankings.items()}

    def get_ranking_info(self, ranking_key: str) -> Optional[Dict[str, Any]]:
        """获取榜单详细信息"""
        if ranking_key not in self.rankings:
            return None

        config = self.rankings[ranking_key]
        return {
            "key": ranking_key,
            "name": config["name"],
            "tab_name": config["tab_name"],
            "sub_options": config["sub_options"],
            "time_periods": config["time_periods"],
            "sort_order": config.get("sort_order"),
            "estimated_items": self.max_items
        }
