#!/usr/bin/env python3
"""
HTTP API客户端

用于Streamlit前端调用FastAPI服务端的HTTP客户端
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Optional
from datetime import datetime

import aiohttp
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

logger = logging.getLogger(__name__)


class ApiClient:
    """HTTP API客户端"""
    
    def __init__(self, base_url: str = 'http://localhost:8000', timeout: int = 120):
        """
        初始化API客户端
        
        Args:
            base_url: API服务端基础URL
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        # 创建同步HTTP会话
        self.session = requests.Session()
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 设置默认headers
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        logger.info(f"🔗 API客户端初始化完成 - 服务端: {self.base_url}")
    
    def check_service_health(self) -> bool:
        """检查API服务状态"""
        try:
            response = self.session.get(
                f"{self.base_url}/health",
                timeout=5
            )
            return response.status_code == 200
        except Exception as e:
            logger.warning(f"API服务健康检查失败: {e}")
            return False
    
    def get_service_info(self) -> Optional[Dict[str, Any]]:
        """获取服务信息"""
        try:
            response = self.session.get(
                f"{self.base_url}/info",
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"获取服务信息失败: {e}")
            return None
    
    def start_regular_analysis(self, item_id: str, item_name: str, item_url: str) -> Optional[Dict[str, Any]]:
        """启动常规分析"""
        try:
            data = {
                "item_id": item_id,
                "item_name": item_name,
                "item_url": item_url
            }
            
            response = self.session.post(
                f"{self.base_url}/api/analysis/regular",
                json=data,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"📊 常规分析任务已提交: {result.get('task_id')} - {item_name}")
            return result
            
        except Exception as e:
            logger.error(f"启动常规分析失败: {e}")
            return None
    
    def start_realtime_analysis(self, item_id: str, item_name: str, item_url: str) -> Optional[Dict[str, Any]]:
        """启动实时监控分析"""
        try:
            data = {
                "item_id": item_id,
                "item_name": item_name,
                "item_url": item_url
            }
            
            response = self.session.post(
                f"{self.base_url}/api/analysis/realtime",
                json=data,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"📈 实时监控任务已提交: {result.get('task_id')} - {item_name}")
            return result
            
        except Exception as e:
            logger.error(f"启动实时监控分析失败: {e}")
            return None
    
    def start_batch_analysis(self, items: List[Dict[str, str]], analysis_type: str = "regular") -> Optional[Dict[str, Any]]:
        """启动批量分析"""
        try:
            data = {
                "items": items,
                "analysis_type": analysis_type,
                "priority": "normal"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/analysis/batch",
                json=data,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"📦 批量分析任务已提交: {result.get('batch_id')} - {len(items)} 个饰品")
            return result
            
        except Exception as e:
            logger.error(f"启动批量分析失败: {e}")
            return None
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        try:
            response = self.session.get(
                f"{self.base_url}/api/tasks/{task_id}/status",
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            logger.error(f"获取任务状态失败: {task_id} - {e}")
            return None
    
    def get_task_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务结果"""
        try:
            response = self.session.get(
                f"{self.base_url}/api/tasks/{task_id}/result",
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            logger.error(f"获取任务结果失败: {task_id} - {e}")
            return None
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            response = self.session.delete(
                f"{self.base_url}/api/tasks/{task_id}",
                timeout=self.timeout
            )
            response.raise_for_status()
            
            logger.info(f"❌ 任务已取消: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"取消任务失败: {task_id} - {e}")
            return False
    
    def get_task_list(self, status: str = None, analysis_type: str = None, 
                     page: int = 1, page_size: int = 10) -> Optional[Dict[str, Any]]:
        """获取任务列表"""
        try:
            params = {
                "page": page,
                "page_size": page_size
            }
            
            if status:
                params["status"] = status
            if analysis_type:
                params["analysis_type"] = analysis_type
            
            response = self.session.get(
                f"{self.base_url}/api/tasks",
                params=params,
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            return None
    
    def get_engine_stats(self) -> Optional[Dict[str, Any]]:
        """获取任务引擎统计信息"""
        try:
            response = self.session.get(
                f"{self.base_url}/api/tasks/engine/stats",
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            logger.error(f"获取引擎统计信息失败: {e}")
            return None
    
    def wait_for_task_completion(self, task_id: str, max_wait_time: int = 600,
                                poll_interval: int = 5) -> Optional[Dict[str, Any]]:
        """等待任务完成"""
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            status_info = self.get_task_status(task_id)
            
            if not status_info:
                logger.error(f"无法获取任务状态: {task_id}")
                return None
            
            status = status_info.get('status')
            
            if status in ['success', 'failed', 'cancelled']:
                logger.info(f"任务完成: {task_id} - 状态: {status}")
                return status_info
            
            logger.info(f"任务进行中: {task_id} - 状态: {status}")
            time.sleep(poll_interval)
        
        logger.warning(f"任务等待超时: {task_id}")
        return None
    
    def close(self):
        """关闭客户端"""
        if self.session:
            self.session.close()
            logger.info("🔗 API客户端已关闭")


# 全局API客户端实例
api_client = ApiClient()
