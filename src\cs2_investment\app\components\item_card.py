"""
饰品卡片组件

显示饰品信息的卡片组件。
"""

import streamlit as st
from typing import Dict, Any, List, Optional
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.app.utils.data_formatter import (
    format_price, format_percentage, format_item_type,
    format_quality, format_rarity, get_rarity_color, format_number
)
from src.cs2_investment.app.services.favorite_service import FavoriteService


def display_item_card(item: Dict[str, Any]):
    """显示饰品卡片"""
    with st.container():
        # 创建卡片样式
        rarity_color = get_rarity_color(item.get('rarity'))
        
        # 使用HTML和CSS创建更美观的卡片
        card_html = f"""
        <div style="
            border: 2px solid {rarity_color};
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        ">
            <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                <div style="flex: 1;">
                    <h4 style="margin: 0 0 5px 0; color: {rarity_color};">
                        {item.get('name', '未知饰品')}
                    </h4>
                    <p style="margin: 0; color: #666; font-size: 0.9em;">
                        {format_item_type(item.get('item_type'))} | {format_quality(item.get('quality'))}
                    </p>
                </div>
            </div>
        </div>
        """
        
        st.markdown(card_html, unsafe_allow_html=True)
        
        # 价格和变化信息
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            price = item.get('current_price', 0)
            st.metric("当前价格", format_price(price))
        
        with col2:
            change_7d = item.get('price_change_7d', 0)
            delta_color = "normal" if change_7d == 0 else ("inverse" if change_7d < 0 else "normal")
            st.metric("7天变化", format_percentage(change_7d), delta_color=delta_color)
        
        with col3:
            # 操作按钮
            col_fav, col_detail = st.columns(2)
            
            with col_fav:
                is_favorited = check_if_favorited(item.get('item_id'))
                fav_icon = "⭐" if is_favorited else "☆"
                fav_help = "取消收藏" if is_favorited else "添加收藏"
                
                if st.button(fav_icon, key=f"fav_{item.get('item_id')}", help=fav_help):
                    toggle_favorite(item.get('item_id'), is_favorited)
                    st.rerun()
            
            with col_detail:
                if st.button("📋", key=f"detail_{item.get('item_id')}", help="查看详情"):
                    show_item_detail(item)


def display_compact_item_card(item: Dict[str, Any]):
    """显示紧凑版饰品卡片"""
    with st.container():
        col1, col2, col3, col4 = st.columns([3, 2, 1, 1])
        
        with col1:
            st.markdown(f"**{item.get('name', '未知饰品')}**")
            st.caption(f"{format_item_type(item.get('item_type'))} | {format_quality(item.get('quality'))}")
        
        with col2:
            st.metric("价格", format_price(item.get('current_price', 0)))
        
        with col3:
            change_7d = item.get('price_change_7d', 0)
            delta_color = "normal" if change_7d == 0 else ("inverse" if change_7d < 0 else "normal")
            st.metric("7天", format_percentage(change_7d), delta_color=delta_color)
        
        with col4:
            is_favorited = check_if_favorited(item.get('item_id'))
            fav_icon = "⭐" if is_favorited else "☆"
            
            if st.button(fav_icon, key=f"compact_fav_{item.get('item_id')}"):
                toggle_favorite(item.get('item_id'), is_favorited)
                st.rerun()
        
        st.divider()


def show_item_detail(item: Dict[str, Any]):
    """显示饰品详情模态框（保留原有功能）"""
    with st.modal(f"饰品详情 - {item.get('name', '未知饰品')}"):
        col1, col2 = st.columns([1, 2])

        with col1:
            # 饰品图片（如果有的话）
            image_url = item.get('image_url')
            if image_url:
                st.image(image_url, caption=item.get('name'), use_container_width=True)
            else:
                st.info("暂无图片")

        with col2:
            # 基本信息
            st.subheader("基本信息")
            st.write(f"**名称**: {item.get('name', '未知')}")
            st.write(f"**类型**: {format_item_type(item.get('item_type'))}")
            st.write(f"**品质**: {format_quality(item.get('quality'))}")
            st.write(f"**稀有度**: {format_rarity(item.get('rarity'))}")

            if item.get('exterior'):
                st.write(f"**外观**: {item.get('exterior')}")

        # 价格信息
        st.subheader("价格信息")
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("当前价格", format_price(item.get('current_price')))

        with col2:
            change_7d = item.get('price_change_7d', 0)
            st.metric("7天变化", format_percentage(change_7d))

        with col3:
            change_30d = item.get('price_change_30d', 0)
            st.metric("30天变化", format_percentage(change_30d))

        # 市场信息
        if any(key in item for key in ['volume_30d', 'amount_30d', 'hot_rank']):
            st.subheader("市场信息")
            col1, col2, col3 = st.columns(3)

            with col1:
                if 'volume_30d' in item:
                    st.metric("30天成交量", item.get('volume_30d', 0))

            with col2:
                if 'amount_30d' in item:
                    st.metric("30天成交额", format_price(item.get('amount_30d')))

            with col3:
                if 'hot_rank' in item:
                    hot_rank = item.get('hot_rank')
                    if hot_rank and hot_rank > 0:
                        st.metric("热度排名", f"#{hot_rank}")

        # 操作按钮
        st.subheader("操作")
        col1, col2 = st.columns(2)

        with col1:
            is_favorited = check_if_favorited(item.get('item_id'))
            fav_text = "取消收藏" if is_favorited else "添加收藏"

            if st.button(fav_text, type="primary", use_container_width=True):
                toggle_favorite(item.get('item_id'), is_favorited)
                st.success(f"已{'取消' if is_favorited else '添加'}收藏")
                st.rerun()

        with col2:
            if st.button("查看价格历史", use_container_width=True):
                show_price_history(item.get('item_id'))


def show_item_detail_page(item_id: str):
    """显示完整的饰品详情页面

    采用三层信息架构：
    1. 顶部关键指标卡片
    2. 中部图表分析
    3. 底部详细数据表格
    """
    # 获取饰品详情数据
    from src.cs2_investment.app.services.item_detail_service import ItemDetailService

    detail_service = ItemDetailService()

    with st.spinner("加载饰品详情..."):
        data = detail_service.get_comprehensive_data(item_id)

    if not data:
        st.error("无法获取饰品详情数据")
        return

    item = data['item']
    snapshot = data['latest_snapshot']
    rating = data['investment_rating']
    metrics = data['key_metrics']

    # 页面标题、收藏和返回按钮
    col1, col2, col3 = st.columns([5, 1, 1])
    with col1:
        st.title(f"🎮 {item['name']}")
        st.caption(f"ID: {item['item_id']}")

    with col2:
        # 收藏按钮
        if 'favorite_service' in st.session_state:
            is_favorited = st.session_state.favorite_service.is_favorited(
                user_id="default_user",
                item_id=item_id
            )

            fav_icon = "💖" if is_favorited else "🤍"
            fav_text = "已收藏" if is_favorited else "收藏"

            if st.button(f"{fav_icon} {fav_text}", type="secondary"):
                if is_favorited:
                    success = st.session_state.favorite_service.remove_favorite(
                        user_id="default_user",
                        item_id=item_id
                    )
                    if success:
                        st.success("已取消收藏")
                        st.rerun()
                else:
                    success = st.session_state.favorite_service.add_favorite(
                        user_id="default_user",
                        item_id=item_id,
                        item_name=item['name']
                    )
                    if success:
                        st.success("已添加收藏")
                        st.rerun()

    with col3:
        if st.button("← 返回", type="secondary"):
            if 'current_view' in st.session_state:
                st.session_state.current_view = 'list'
                st.rerun()

    # 第一层：关键指标卡片
    st.subheader("📊 关键指标")
    render_key_metrics_cards(item, snapshot, rating, metrics)

    st.divider()

    # 第二层：图表分析（使用tabs）
    st.subheader("📈 数据分析")
    tab1, tab2, tab3, tab4 = st.tabs(["📈 概览", "💰 价格分析", "📊 交易分析", "🔥 热度分析"])

    with tab1:
        render_overview_tab(item, snapshot, rating)

    with tab2:
        render_price_analysis_tab(data['price_trend'], item['name'])

    with tab3:
        render_trading_analysis_tab(snapshot)

    with tab4:
        render_supply_demand_analysis_tab(snapshot)

    st.divider()

    # 第三层：详细数据表格
    from src.cs2_investment.app.components.data_table import render_detailed_data_tables
    render_detailed_data_tables(snapshot, item['name'])


def render_key_metrics_cards(item: Dict, snapshot: Dict, rating: Dict, metrics: Dict):
    """渲染关键指标卡片"""
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        # 当前价格卡片
        current_price = snapshot.get('current_price', 0)
        diff_1d = snapshot.get('diff_1d', 0)

        delta_color = "normal"
        if diff_1d > 0:
            delta_color = "normal"
        elif diff_1d < 0:
            delta_color = "inverse"

        st.metric(
            label="💰 当前价格",
            value=format_price(current_price),
            delta=format_percentage(diff_1d),
            delta_color=delta_color
        )

    with col2:
        # 投资评级卡片
        rating_score = rating.get('score', 0)
        rating_grade = rating.get('rating', 'F')

        # 评级颜色映射
        rating_colors = {
            'A': '🟢', 'B': '🟡', 'C': '🟠',
            'D': '🔴', 'E': '🔴', 'F': '⚫'
        }
        rating_icon = rating_colors.get(rating_grade, '⚫')

        st.metric(
            label="⭐ 综合评级 (仅供参考)",
            value=f"{rating_icon} {rating_grade}级",
            delta=f"评分: {rating_score:.1f}/100",
            help="基于价格趋势、流动性、热度、稳定性四个维度的综合评分，不构成投资建议"
        )

    with col3:
        # 热度排名卡片
        hot_rank = snapshot.get('hot_rank')
        hot_count = snapshot.get('hot_count', 0)

        if hot_rank and hot_rank > 0:
            st.metric(
                label="🔥 热度排名",
                value=f"#{hot_rank}",
                delta=f"热度: {format_number(hot_count)}"
            )
        else:
            st.metric(
                label="🔥 热度排名",
                value="未上榜",
                delta=f"热度: {format_number(hot_count)}"
            )

    with col4:
        # 存世量/稀有度卡片
        survive_num = snapshot.get('survive_num', 0)
        rarity_level = metrics.get('rarity_level', 'unknown')

        rarity_icons = {
            'extremely_rare': '💎',
            'very_rare': '💍',
            'rare': '🔸',
            'uncommon': '🔹',
            'common': '⚪',
            'unknown': '❓'
        }
        rarity_icon = rarity_icons.get(rarity_level, '❓')

        st.metric(
            label="💎 稀有度",
            value=f"{rarity_icon} {format_number(survive_num)}",
            delta="存世量"
        )


def render_overview_tab(item: Dict, snapshot: Dict, rating: Dict):
    """渲染概览标签页"""
    col1, col2 = st.columns([1, 1])

    with col1:
        # 基本信息
        st.subheader("🎮 基本信息")

        # 使用现有的格式化函数
        st.write(f"**类型**: {format_item_type(item.get('item_type'))}")
        st.write(f"**品质**: {format_quality(item.get('quality'))}")
        st.write(f"**稀有度**: {format_rarity(item.get('rarity'))}")

        if item.get('exterior'):
            st.write(f"**外观**: {item.get('exterior')}")

        if item.get('market_hash_name'):
            st.write(f"**市场名称**: {item.get('market_hash_name')}")

        # 价格变化信息
        st.subheader("💰 价格变化")
        price_data = [
            ("1天", snapshot.get('diff_1d', 0), snapshot.get('diff_1d_price', 0)),
            ("7天", snapshot.get('diff_7d', 0), snapshot.get('diff_7d_price', 0)),
            ("1月", snapshot.get('diff_1m', 0), snapshot.get('diff_1m_price', 0)),
            ("3月", snapshot.get('diff_3m', 0), snapshot.get('diff_3m_price', 0))
        ]

        for period, pct_change, price_change in price_data:
            if pct_change != 0 or price_change != 0:
                col_a, col_b = st.columns(2)
                with col_a:
                    st.write(f"**{period}变化**:")
                with col_b:
                    color = "🟢" if pct_change > 0 else "🔴" if pct_change < 0 else "⚪"
                    st.write(f"{color} {format_percentage(pct_change)} ({format_price(price_change)})")

    with col2:
        # 投资评级详情
        st.subheader("⭐ 综合评级分析")

        rating_desc = rating.get('description', '无评级信息')
        st.warning(rating_desc)  # 使用warning样式强调风险提示

        # 评级因子
        factors = rating.get('factors', {})
        if factors:
            st.write("**评分构成**:")

            factor_names = {
                'price_trend': '价格趋势 (长期表现更重要)',
                'liquidity': '流动性 (基于7天交易数据)',
                'popularity': '市场热度 (排名和关注度)',
                'volatility': '价格稳定性 (适度波动最佳)'
            }

            for factor, score in factors.items():
                factor_name = factor_names.get(factor, factor)
                progress_value = score / 25.0  # 转换为0-1范围
                st.write(f"{factor_name}: {score:.1f}/25")
                st.progress(progress_value)

        # 市场信息
        st.subheader("📊 市场信息")

        # 交易活跃度
        trans_count_7d = snapshot.get('trans_count_7d', 0)
        trans_amount_7d = snapshot.get('trans_amount_7d', 0)

        st.write(f"**7天成交量**: {format_number(trans_count_7d)}笔")
        st.write(f"**7天成交额**: {format_price(trans_amount_7d)}")

        # 供需信息
        sell_nums = snapshot.get('sell_nums', 0)
        survive_num = snapshot.get('survive_num', 0)

        if survive_num > 0:
            liquidity_ratio = (sell_nums / survive_num) * 100
            st.write(f"**在售数量**: {format_number(sell_nums)}个")
            st.write(f"**流动性比率**: {liquidity_ratio:.1f}%")
        else:
            st.write(f"**在售数量**: {format_number(sell_nums)}个")


def render_price_analysis_tab(price_trend: List[Dict], item_name: str = "饰品"):
    """渲染价格分析标签页"""
    if not price_trend:
        st.info("暂无价格趋势数据")
        return

    # 使用价格图表组件
    from src.cs2_investment.app.components.price_chart import render_price_trend_chart
    render_price_trend_chart(price_trend, item_name)

    # 显示最近的价格数据
    st.subheader("📋 最近价格记录")

    # 取最近10条记录
    recent_data = price_trend[-10:] if len(price_trend) > 10 else price_trend

    if recent_data:
        import pandas as pd

        df_data = []
        for record in recent_data:
            df_data.append({
                '日期': record['snapshot_date'],
                '价格': format_price(record['current_price']),
                '1天变化': format_percentage(record['diff_1d']),
                '7天变化': format_percentage(record['diff_7d']),
                '成交量': format_number(record['trans_count_1d'])
            })

        df = pd.DataFrame(df_data)
        st.dataframe(df, use_container_width=True)


def render_trading_analysis_tab(snapshot: Dict):
    """渲染交易分析标签页"""
    # 使用交易分析图表组件
    from src.cs2_investment.app.components.trading_chart import (
        render_trading_analysis_chart,
        render_trading_summary_metrics
    )

    render_trading_analysis_chart(snapshot)
    st.divider()
    render_trading_summary_metrics(snapshot)


def render_supply_demand_analysis_tab(snapshot: Dict):
    """渲染供需分析标签页"""
    # 使用供需分析图表组件
    from src.cs2_investment.app.components.supply_chart import (
        render_supply_demand_chart,
        render_supply_demand_summary
    )

    render_supply_demand_chart(snapshot)
    st.divider()
    render_supply_demand_summary(snapshot)


def render_detailed_data_section(snapshot: Dict):
    """渲染详细数据表格部分"""
    # 使用expander来组织不同类别的数据
    with st.expander("💰 价格数据", expanded=False):
        render_price_data_table(snapshot)

    with st.expander("📊 交易数据", expanded=False):
        render_trading_data_table(snapshot)

    with st.expander("⚖️ 供需数据", expanded=False):
        render_supply_data_table(snapshot)

    with st.expander("🔥 热度数据", expanded=False):
        render_popularity_data_table(snapshot)


def render_price_data_table(snapshot: Dict):
    """渲染价格数据表格"""
    import pandas as pd

    price_data = [
        {
            '时间段': '当前',
            '价格': format_price(snapshot.get('current_price', 0)),
            '变化百分比': '-',
            '变化金额': '-',
            '历史价格': '-'
        },
        {
            '时间段': '1天前',
            '价格': format_price(snapshot.get('before_1d_price', 0)),
            '变化百分比': format_percentage(snapshot.get('diff_1d', 0)),
            '变化金额': format_price(snapshot.get('diff_1d_price', 0)),
            '历史价格': format_price(snapshot.get('before_1d_price', 0))
        },
        {
            '时间段': '7天前',
            '价格': format_price(snapshot.get('before_7d_price', 0)),
            '变化百分比': format_percentage(snapshot.get('diff_7d', 0)),
            '变化金额': format_price(snapshot.get('diff_7d_price', 0)),
            '历史价格': format_price(snapshot.get('before_7d_price', 0))
        },
        {
            '时间段': '1月前',
            '价格': format_price(snapshot.get('before_1m_price', 0)),
            '变化百分比': format_percentage(snapshot.get('diff_1m', 0)),
            '变化金额': format_price(snapshot.get('diff_1m_price', 0)),
            '历史价格': format_price(snapshot.get('before_1m_price', 0))
        },
        {
            '时间段': '3月前',
            '价格': format_price(snapshot.get('before_3m_price', 0)),
            '变化百分比': format_percentage(snapshot.get('diff_3m', 0)),
            '变化金额': format_price(snapshot.get('diff_3m_price', 0)),
            '历史价格': format_price(snapshot.get('before_3m_price', 0))
        }
    ]

    df = pd.DataFrame(price_data)
    st.dataframe(df, use_container_width=True)


def render_trading_data_table(snapshot: Dict):
    """渲染交易数据表格"""
    import pandas as pd

    trading_data = [
        {
            '时间段': '24小时',
            '成交量': format_number(snapshot.get('trans_count_24h', 0), '笔'),
            '成交额': format_price(snapshot.get('trans_amount_24h', 0))
        },
        {
            '时间段': '1天',
            '成交量': format_number(snapshot.get('trans_count_1d', 0), '笔'),
            '成交额': format_price(snapshot.get('trans_amount_1d', 0))
        },
        {
            '时间段': '3天',
            '成交量': format_number(snapshot.get('trans_count_3d', 0), '笔'),
            '成交额': format_price(snapshot.get('trans_amount_3d', 0))
        },
        {
            '时间段': '7天',
            '成交量': format_number(snapshot.get('trans_count_7d', 0), '笔'),
            '成交额': format_price(snapshot.get('trans_amount_7d', 0))
        },
        {
            '时间段': '1月',
            '成交量': format_number(snapshot.get('trans_count_1m', 0), '笔'),
            '成交额': format_price(snapshot.get('trans_amount_1m', 0))
        },
        {
            '时间段': '3月',
            '成交量': format_number(snapshot.get('trans_count_3m', 0), '笔'),
            '成交额': format_price(snapshot.get('trans_amount_3m', 0))
        }
    ]

    df = pd.DataFrame(trading_data)
    st.dataframe(df, use_container_width=True)


def render_supply_data_table(snapshot: Dict):
    """渲染供需数据表格"""
    import pandas as pd

    supply_data = [
        {
            '时间段': '当前',
            '在售数量': format_number(snapshot.get('sell_nums', 0), '个'),
            '变化率': '-',
            '变化数量': '-'
        },
        {
            '时间段': '1天前',
            '在售数量': format_number(snapshot.get('sell_nums_1d', 0), '个'),
            '变化率': format_percentage(snapshot.get('sell_nums_1d_rate', 0)),
            '变化数量': f"{snapshot.get('sell_nums_1d_diff', 0):+d}个"
        },
        {
            '时间段': '7天前',
            '在售数量': format_number(snapshot.get('sell_nums_7d', 0), '个'),
            '变化率': format_percentage(snapshot.get('sell_nums_7d_rate', 0)),
            '变化数量': f"{snapshot.get('sell_nums_7d_diff', 0):+d}个"
        },
        {
            '时间段': '1月前',
            '在售数量': format_number(snapshot.get('sell_nums_1m', 0), '个'),
            '变化率': format_percentage(snapshot.get('sell_nums_1m_rate', 0)),
            '变化数量': f"{snapshot.get('sell_nums_1m_diff', 0):+d}个"
        }
    ]

    df = pd.DataFrame(supply_data)
    st.dataframe(df, use_container_width=True)

    # 显示存世量信息
    survive_num = snapshot.get('survive_num', 0)
    if survive_num > 0:
        st.info(f"💎 **存世量**: {format_number(survive_num)}个")


def render_popularity_data_table(snapshot: Dict):
    """渲染热度数据表格"""
    import pandas as pd

    popularity_data = [
        {
            '指标': '热度排名',
            '数值': f"#{snapshot.get('hot_rank')}" if snapshot.get('hot_rank') else "未上榜"
        },
        {
            '指标': '热度计数',
            '数值': format_number(snapshot.get('hot_count', 0))
        },
        {
            '指标': '热度保持天数',
            '数值': f"{snapshot.get('hot_keep_days', 0)}天"
        },
        {
            '指标': '排名变化',
            '数值': f"{snapshot.get('hot_rank_change', 0):+d}" if snapshot.get('hot_rank_change') else "无变化"
        }
    ]

    df = pd.DataFrame(popularity_data)
    st.dataframe(df, use_container_width=True, hide_index=True)


def show_price_history(item_id: str):
    """显示价格历史图表"""
    # 这里应该从数据库获取价格历史数据并绘制图表
    st.info("价格历史功能开发中...")


def check_if_favorited(item_id: str) -> bool:
    """检查饰品是否已收藏"""
    try:
        if 'favorite_service' not in st.session_state:
            st.session_state.favorite_service = FavoriteService()

        return st.session_state.favorite_service.is_item_favorited(item_id)
    except Exception as e:
        print(f"检查收藏状态失败: {e}")
        return False


def toggle_favorite(item_id: str, is_currently_favorited: bool):
    """切换收藏状态"""
    try:
        if 'favorite_service' not in st.session_state:
            st.session_state.favorite_service = FavoriteService()

        result = st.session_state.favorite_service.toggle_item_favorite(item_id)

        if result['success']:
            st.success(result['message'])
        else:
            st.error(result['message'])

    except Exception as e:
        st.error(f"操作失败: {str(e)}")
