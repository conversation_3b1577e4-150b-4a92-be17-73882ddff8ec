"""
基础数据访问对象

提供通用的CRUD操作和数据库访问功能。
"""

from typing import List, Optional, Type, TypeVar, Generic, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from loguru import logger

from ..config.database import get_db_session
from ..models.base import BaseModel

T = TypeVar('T', bound=BaseModel)


class BaseDAO(Generic[T]):
    """基础DAO类"""
    
    def __init__(self, model_class: Type[T]):
        self.model_class = model_class
        self.logger = logger.bind(dao=self.__class__.__name__)
    
    def create(self, **kwargs) -> Optional[T]:
        """创建单个记录"""
        try:
            with get_db_session() as session:
                instance = self.model_class(**kwargs)
                session.add(instance)
                session.flush()  # 获取ID
                session.refresh(instance)
                self.logger.info(f"创建记录成功: {instance}")
                return instance
        except SQLAlchemyError as e:
            self.logger.error(f"创建记录失败: {e}")
            raise
    
    def create_batch(self, data_list: List[Dict[str, Any]]) -> List[T]:
        """批量创建记录"""
        try:
            with get_db_session() as session:
                instances = [self.model_class(**data) for data in data_list]
                session.add_all(instances)
                session.flush()
                for instance in instances:
                    session.refresh(instance)
                self.logger.info(f"批量创建记录成功: {len(instances)}条")
                return instances
        except SQLAlchemyError as e:
            self.logger.error(f"批量创建记录失败: {e}")
            raise
    
    def get_by_id(self, id_value: Any) -> Optional[T]:
        """根据ID获取记录"""
        try:
            with get_db_session() as session:
                instance = session.query(self.model_class).filter(
                    self.model_class.id == id_value
                ).first()

                if instance:
                    # 分离对象避免Session绑定问题
                    session.expunge(instance)

                return instance
        except SQLAlchemyError as e:
            self.logger.error(f"根据ID获取记录失败: {e}")
            raise
    
    def get_all(self, limit: Optional[int] = None, offset: Optional[int] = None) -> List[T]:
        """获取所有记录"""
        try:
            with get_db_session() as session:
                query = session.query(self.model_class)
                if offset:
                    query = query.offset(offset)
                if limit:
                    query = query.limit(limit)
                results = query.all()

                # 分离所有对象避免Session绑定问题
                for result in results:
                    session.expunge(result)

                return results
        except SQLAlchemyError as e:
            self.logger.error(f"获取所有记录失败: {e}")
            raise
    
    def get_by_filter(self, **filters) -> List[T]:
        """根据条件过滤获取记录"""
        try:
            with get_db_session() as session:
                query = session.query(self.model_class)
                for key, value in filters.items():
                    if hasattr(self.model_class, key):
                        query = query.filter(getattr(self.model_class, key) == value)
                results = query.all()

                # 分离所有对象避免Session绑定问题
                for result in results:
                    session.expunge(result)

                return results
        except SQLAlchemyError as e:
            self.logger.error(f"根据条件获取记录失败: {e}")
            raise
    
    def update_by_id(self, id_value: Any, **kwargs) -> Optional[T]:
        """根据ID更新记录"""
        try:
            with get_db_session() as session:
                instance = session.query(self.model_class).filter(
                    self.model_class.id == id_value
                ).first()
                
                if instance:
                    for key, value in kwargs.items():
                        if hasattr(instance, key):
                            setattr(instance, key, value)
                    session.flush()
                    session.refresh(instance)
                    self.logger.info(f"更新记录成功: {instance}")
                    return instance
                else:
                    self.logger.warning(f"未找到ID为{id_value}的记录")
                    return None
        except SQLAlchemyError as e:
            self.logger.error(f"更新记录失败: {e}")
            raise
    
    def delete_by_id(self, id_value: Any) -> bool:
        """根据ID删除记录"""
        try:
            with get_db_session() as session:
                instance = session.query(self.model_class).filter(
                    self.model_class.id == id_value
                ).first()
                
                if instance:
                    session.delete(instance)
                    self.logger.info(f"删除记录成功: {instance}")
                    return True
                else:
                    self.logger.warning(f"未找到ID为{id_value}的记录")
                    return False
        except SQLAlchemyError as e:
            self.logger.error(f"删除记录失败: {e}")
            raise
    
    def count(self, **filters) -> int:
        """统计记录数量"""
        try:
            with get_db_session() as session:
                query = session.query(self.model_class)
                for key, value in filters.items():
                    if hasattr(self.model_class, key):
                        query = query.filter(getattr(self.model_class, key) == value)
                return query.count()
        except SQLAlchemyError as e:
            self.logger.error(f"统计记录数量失败: {e}")
            raise
    
    def exists(self, **filters) -> bool:
        """检查记录是否存在"""
        try:
            with get_db_session() as session:
                query = session.query(self.model_class)
                for key, value in filters.items():
                    if hasattr(self.model_class, key):
                        query = query.filter(getattr(self.model_class, key) == value)
                return query.first() is not None
        except SQLAlchemyError as e:
            self.logger.error(f"检查记录存在性失败: {e}")
            raise
