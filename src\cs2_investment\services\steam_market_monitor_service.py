"""
Steam市场监控服务

实现Steam市场监控的核心业务逻辑，负责获取收藏列表、调用Steam API、保存价格数据等功能。
使用data_source='steam_direct'区分Steam直接API数据和steamdt数据，避免数据冲突。
"""

import asyncio
import random
from typing import Dict, List, Optional, Any
from datetime import datetime
from loguru import logger

from ..dao.favorite_dao import FavoriteDAO
from ..dao.item_dao import ItemDAO
from ..dao.platform_price_dao import PlatformPriceDAO
from .steam_market_api_client import SteamMarketAPIClient


class SteamMarketMonitorService:
    """Steam市场监控服务"""

    def __init__(self, proxy_enabled: bool = None, proxy_url: str = None):
        """
        初始化Steam市场监控服务

        Args:
            proxy_enabled: 是否启用Steam代理，如果为None则从配置文件读取
            proxy_url: Steam代理URL，如果为None则从配置文件读取
        """
        self.logger = logger.bind(service="SteamMarketMonitor")

        # 初始化依赖组件
        self.favorite_dao = FavoriteDAO()
        self.item_dao = ItemDAO()
        self.platform_price_dao = PlatformPriceDAO()

        # 初始化Steam API客户端，传递Steam专用代理配置
        # 注意：这里的代理是用于访问国外Steam网站的，与SteamDT的国内代理不同
        self.steam_api_client = SteamMarketAPIClient(proxy_enabled=proxy_enabled, proxy_url=proxy_url)

        # 配置参数
        self.min_delay_seconds = 5
        self.max_delay_seconds = 15
        self.batch_size = 50

        self.logger.info("Steam市场监控服务初始化完成")
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """
        更新配置参数
        
        Args:
            config: 配置字典
        """
        self.min_delay_seconds = config.get('min_delay_seconds', 5)
        self.max_delay_seconds = config.get('max_delay_seconds', 15)
        self.batch_size = config.get('batch_size', 50)
        
        self.logger.info(f"配置已更新: 延迟{self.min_delay_seconds}-{self.max_delay_seconds}秒, 批量{self.batch_size}")
    
    async def monitor_favorite_items(self, user_id: str = "default_user") -> Dict[str, Any]:
        """
        监控收藏饰品的Steam价格（保持向后兼容）

        Args:
            user_id: 用户ID

        Returns:
            监控结果统计
        """
        return await self.monitor_items_steam_price(user_id)

    async def monitor_items_steam_price(self, user_id: str = "default_user") -> Dict[str, Any]:
        """
        监控饰品的Steam价格 - 优化版本，收藏饰品优先

        新的优先级策略：
        1. 收藏饰品中没有steam_direct数据的（最高优先级）
        2. 收藏饰品中有steam_direct数据但更新时间较久的
        3. 非收藏饰品中没有steam_direct数据的（需要有搬砖率）
        4. 非收藏饰品中有steam_direct数据但更新时间较久的（需要有搬砖率）

        Args:
            user_id: 用户ID

        Returns:
            监控结果统计
        """
        start_time = datetime.now()
        self.logger.info(f"开始监控饰品Steam价格: {user_id}")

        try:
            # 获取需要更新的饰品列表（按优先级排序）
            items_to_update = self._get_items_for_steam_update(user_id, limit=self.batch_size)

            if not items_to_update:
                self.logger.info("没有需要更新Steam价格的饰品")
                return {
                    'success': True,
                    'message': '没有需要更新Steam价格的饰品',
                    'total_items': 0,
                    'updated_items': 0,
                    'failed_items': 0,
                    'duration_seconds': 0
                }

            self.logger.info(f"找到{len(items_to_update)}个需要更新Steam价格的饰品")

            # 监控统计
            stats = {
                'total_items': len(items_to_update),
                'updated_items': 0,
                'failed_items': 0,
                'skipped_items': 0,
                'favorite_items': 0,
                'arbitrage_items': 0
            }

            # 逐个处理饰品
            for i, item_info in enumerate(items_to_update, 1):
                try:
                    item_type = item_info.get('type', 'arbitrage')
                    item_name = item_info.get('name', item_info.get('item_name', 'Unknown'))

                    self.logger.info(f"处理饰品 {i}/{len(items_to_update)} [{item_type}]: {item_name}")

                    # 统计类型
                    if item_type == 'favorite':
                        stats['favorite_items'] += 1
                    else:
                        stats['arbitrage_items'] += 1

                    market_hash_name = item_info.get('market_hash_name')
                    if not market_hash_name:
                        self.logger.warning(f"饰品缺少market_hash_name: {item_info.get('item_id')}")
                        stats['skipped_items'] += 1
                        continue

                    # 调用Steam API获取价格数据
                    success = await self._update_item_steam_price_new(
                        item_info['item_id'],
                        market_hash_name,
                        item_info.get('favorite_id')  # 收藏ID，如果是收藏饰品的话
                    )

                    if success:
                        stats['updated_items'] += 1
                        self.logger.debug(f"Steam价格更新成功: {item_name}")
                    else:
                        stats['failed_items'] += 1
                        self.logger.warning(f"Steam价格更新失败: {item_name}")

                    # 随机延迟，保护Steam API
                    if i < len(items_to_update):  # 最后一个不需要延迟
                        delay = random.randint(self.min_delay_seconds, self.max_delay_seconds)
                        self.logger.debug(f"等待{delay}秒后处理下一个饰品...")
                        await asyncio.sleep(delay)

                except Exception as e:
                    self.logger.error(f"处理饰品失败: {item_name}, 错误: {e}")
                    stats['failed_items'] += 1
                    continue

            # 计算总耗时
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            result = {
                'success': True,
                'message': f'Steam价格监控完成',
                'total_items': stats['total_items'],
                'updated_items': stats['updated_items'],
                'failed_items': stats['failed_items'],
                'skipped_items': stats['skipped_items'],
                'favorite_items': stats['favorite_items'],
                'arbitrage_items': stats['arbitrage_items'],
                'duration_seconds': duration
            }

            self.logger.info(f"Steam价格监控完成: {result}")
            return result

        except Exception as e:
            self.logger.error(f"Steam价格监控失败: {e}")
            return {
                'success': False,
                'message': f'Steam价格监控失败: {str(e)}',
                'total_items': 0,
                'updated_items': 0,
                'failed_items': 0,
                'duration_seconds': 0
            }
    
    def _get_items_for_steam_update(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取需要更新Steam价格的饰品列表，优先处理收藏饰品

        优化说明：
        - 使用单条SQL联表查询替代多次查询+内存筛选
        - 在SQL层面完成优先级计算和时间排序
        - 使用RAND()实现相同条件下的随机排序

        新的排序策略（按优先级）：
        1. 优先级1：收藏饰品中没有steam_direct数据的（最高优先级）
        2. 优先级2：收藏饰品中有steam_direct数据且更新时间超过1小时的（按时间排序，越久越优先）
        3. 优先级3：非收藏饰品中没有steam_direct数据的
        4. 优先级4：非收藏饰品中有steam_direct数据但更新时间较久的

        注意：收藏饰品中更新时间在1小时内的不会被查询出来

        Args:
            user_id: 用户ID
            limit: 返回数量限制

        Returns:
            需要更新的饰品列表，按优先级和时间排序
        """
        items_to_update = []

        try:
            from sqlalchemy import text

            # 使用原生SQL进行联表查询，按优先级和时间排序
            sql = """
            SELECT
                i.item_id,
                i.name,
                i.market_hash_name,
                i.arbitrage_ratio,
                pp.query_time as last_steam_update,
                f.id as favorite_id,
                f.user_id as favorite_user_id,
                CASE
                    -- 收藏饰品优先级更高
                    WHEN f.id IS NOT NULL AND pp.query_time IS NULL THEN 1  -- 收藏+无steam_direct数据，最高优先级
                    WHEN f.id IS NOT NULL AND pp.query_time IS NOT NULL THEN 2  -- 收藏+有steam_direct数据
                    WHEN f.id IS NULL AND pp.query_time IS NULL THEN 3  -- 非收藏+无steam_direct数据
                    WHEN f.id IS NULL AND pp.query_time IS NOT NULL THEN 4  -- 非收藏+有steam_direct数据
                    ELSE 5
                END as priority,
                CASE
                    WHEN pp.query_time IS NOT NULL THEN
                        TIMESTAMPDIFF(HOUR, pp.query_time, NOW())
                    ELSE NULL
                END as hours_since_update,
                CASE
                    WHEN f.id IS NOT NULL THEN 'favorite'
                    ELSE 'arbitrage'
                END as item_type
            FROM items i
            LEFT JOIN (
                SELECT
                    item_id,
                    MAX(query_time) as query_time
                FROM platform_prices
                WHERE data_source = 'steam_direct'
                  AND is_active = 1
                GROUP BY item_id
            ) pp ON i.item_id = pp.item_id
            LEFT JOIN favorites f ON i.item_id = f.item_id AND f.user_id = :user_id
            WHERE i.market_hash_name IS NOT NULL
              AND i.market_hash_name != ''
              AND (
                  -- 收藏饰品：无steam_direct数据 OR 有steam_direct数据但超过1小时
                  (f.id IS NOT NULL AND (
                      pp.query_time IS NULL
                      OR
                      TIMESTAMPDIFF(HOUR, pp.query_time, NOW()) >= 1
                  ))
                  OR
                  -- 非收藏饰品但有搬砖率
                  (f.id IS NULL AND i.arbitrage_ratio IS NOT NULL AND i.arbitrage_ratio != 0)
              )
            ORDER BY
                priority ASC,                    -- 优先级：1(收藏+无steam_direct) > 2(收藏+有steam_direct) > 3(非收藏+无steam_direct) > 4(非收藏+有steam_direct)
                pp.query_time ASC,              -- 时间升序：越久的越优先（NULL值会排在最前面）
                RAND()                          -- 相同时间的随机排序
            LIMIT :limit
            """

            with self.platform_price_dao._get_session() as session:
                result = session.execute(text(sql), {'limit': limit, 'user_id': user_id}).fetchall()

                items_to_update = []
                favorite_no_steam_count = 0
                favorite_old_steam_count = 0
                arbitrage_no_steam_count = 0
                arbitrage_old_steam_count = 0

                for row in result:
                    item_info = {
                        'item_id': row.item_id,
                        'name': row.name,
                        'market_hash_name': row.market_hash_name,
                        'type': row.item_type,
                        'arbitrage_ratio': float(row.arbitrage_ratio) if row.arbitrage_ratio else 0,
                        'favorite_id': row.favorite_id,
                        'priority': row.priority
                    }

                    # 统计不同类型的饰品数量
                    if row.priority == 1:  # 收藏+无steam_direct
                        favorite_no_steam_count += 1
                    elif row.priority == 2:  # 收藏+有steam_direct
                        favorite_old_steam_count += 1
                        item_info['hours_since_update'] = row.hours_since_update
                        item_info['last_update_time'] = row.last_steam_update
                    elif row.priority == 3:  # 非收藏+无steam_direct
                        arbitrage_no_steam_count += 1
                    elif row.priority == 4:  # 非收藏+有steam_direct
                        arbitrage_old_steam_count += 1
                        item_info['hours_since_update'] = row.hours_since_update
                        item_info['last_update_time'] = row.last_steam_update

                    items_to_update.append(item_info)

                self.logger.info(f"📊 Steam更新饰品选择结果 (收藏优先): "
                               f"总计 {len(items_to_update)} 个, "
                               f"收藏无steam_direct {favorite_no_steam_count} 个, "
                               f"收藏有steam_direct {favorite_old_steam_count} 个, "
                               f"搬砖无steam_direct {arbitrage_no_steam_count} 个, "
                               f"搬砖有steam_direct {arbitrage_old_steam_count} 个")

                return items_to_update

        except Exception as e:
            self.logger.error(f"获取需要更新的饰品列表失败: {e}")
            return []

    async def _update_item_steam_price(self, favorite_id: int, item_id: str, market_hash_name: str) -> bool:
        """
        更新单个饰品的Steam价格
        
        Args:
            favorite_id: 收藏记录ID
            item_id: 饰品ID
            market_hash_name: 市场哈希名称
            
        Returns:
            更新是否成功
        """
        try:
            # 调用Steam API获取完整市场数据（优化版本，传递item_id用于缓存查询）
            complete_data = self.steam_api_client.get_complete_market_data(market_hash_name, item_id=item_id)

            if not complete_data:
                self.logger.warning(f"获取Steam市场数据失败: {market_hash_name}")
                return False
            
            # 解析价格数据
            parsed_data = self.steam_api_client.parse_price_data(complete_data)
            
            if not parsed_data:
                self.logger.warning(f"解析Steam价格数据失败: {market_hash_name}")
                return False
            
            # 保存价格数据到platform_prices表
            # 关键：使用data_source='steam_direct'区分Steam直接API数据
            self.platform_price_dao.save_platform_price(
                item_id=item_id,
                market_hash_name=market_hash_name,
                platform="STEAM",  # 修复：使用大写STEAM保持一致性
                platform_item_id=str(parsed_data['item_nameid']),
                sell_price=parsed_data['lowest_sell_price'],
                sell_count=1,  # Steam API不提供具体数量
                bidding_price=parsed_data['highest_buy_price'],
                bidding_count=1,  # Steam API不提供具体数量
                steamdt_update_time=int(datetime.now().timestamp()),
                query_time=datetime.now(),
                data_source='steam_direct'  # 关键：区分数据源
            )
            
            # 更新收藏记录的Steam更新时间
            self.favorite_dao.update_favorite_steam_time(favorite_id)
            
            self.logger.debug(f"Steam价格数据保存成功: {market_hash_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新Steam价格失败: {market_hash_name}, 错误: {e}")
            return False

    async def _update_item_steam_price_new(self, item_id: str, market_hash_name: str, favorite_id: Optional[int] = None) -> bool:
        """
        更新单个饰品的Steam价格 - 新版本

        Args:
            item_id: 饰品ID
            market_hash_name: 市场哈希名称
            favorite_id: 收藏记录ID（如果是收藏饰品）

        Returns:
            更新是否成功
        """
        try:
            # 调用Steam API获取完整市场数据
            complete_data = self.steam_api_client.get_complete_market_data(market_hash_name, item_id=item_id)

            if not complete_data:
                self.logger.warning(f"获取Steam市场数据失败: {market_hash_name}")
                return False

            # 解析价格数据
            parsed_data = self.steam_api_client.parse_price_data(complete_data)

            if not parsed_data:
                self.logger.warning(f"解析Steam价格数据失败: {market_hash_name}")
                return False

            # 保存价格数据到platform_prices表
            # 关键：使用data_source='steam_direct'区分Steam直接API数据
            self.platform_price_dao.save_platform_price(
                item_id=item_id,
                market_hash_name=market_hash_name,
                platform="STEAM",  # 修复：使用大写STEAM保持一致性
                platform_item_id=str(parsed_data['item_nameid']),
                sell_price=parsed_data['lowest_sell_price'],
                sell_count=1,  # Steam API不提供具体数量
                bidding_price=parsed_data['highest_buy_price'],
                bidding_count=1,  # Steam API不提供具体数量
                steamdt_update_time=int(datetime.now().timestamp()),
                query_time=datetime.now(),
                data_source='steam_direct'  # 关键：区分数据源
            )

            # 如果是收藏饰品，更新收藏记录的Steam更新时间
            if favorite_id:
                self.favorite_dao.update_favorite_steam_time(favorite_id)

            self.logger.debug(f"Steam价格数据保存成功: {market_hash_name}")
            return True

        except Exception as e:
            self.logger.error(f"更新Steam价格失败: {market_hash_name}, 错误: {e}")
            return False

    def get_monitor_status(self) -> Dict[str, Any]:
        """
        获取监控状态信息
        
        Returns:
            监控状态字典
        """
        return {
            'service_name': 'Steam市场监控服务',
            'status': 'running',
            'config': {
                'min_delay_seconds': self.min_delay_seconds,
                'max_delay_seconds': self.max_delay_seconds,
                'batch_size': self.batch_size
            },
            'data_source': 'steam_direct'
        }
