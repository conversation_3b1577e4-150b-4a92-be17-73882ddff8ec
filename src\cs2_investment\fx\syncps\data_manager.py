#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品分析系统 - 重构数据管理器
统一数据粒度，规范数据使用
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

class DataManager:
    """统一数据管理器 - 解决数据粒度混用问题"""
    
    def __init__(self, skin_name: str):
        self.skin_name = skin_name

        # 判断是否为绝对路径
        from pathlib import Path
        if Path(skin_name).is_absolute():
            # 如果是绝对路径，直接使用
            self.data_path = skin_name
            print(f"🔍 使用绝对路径: {self.data_path}")
        else:
            # 如果是相对路径，从syncps目录向上一级，然后进入饰品目录
            self.data_path = f"../{skin_name}"
            print(f"🔍 使用相对路径: {self.data_path}")

        print(f"📁 数据路径设置: {self.data_path}")
        
        # 数据容器
        self.daily_data = None      # 主要分析数据 - 日K
        self.weekly_data = None     # 长期趋势数据 - 周K  
        self.trend_data = None      # 基本面数据 - 走势
        self.hourly_data = None     # 实时监控数据 - 时K
        
        # 数据状态
        self.data_loaded = False
        self.data_quality = {}
        
    def load_all_data(self) -> bool:
        """加载所有数据源"""
        try:
            print(f"🔄 加载 {self.skin_name} 数据...")
            
            # 1. 加载日K数据 (主要分析数据)
            self.daily_data = self._load_daily_data()
            print(f"✅ 日K数据: {len(self.daily_data)}条")
            
            # 2. 加载周K数据 (长期趋势)
            self.weekly_data = self._load_weekly_data()
            print(f"✅ 周K数据: {len(self.weekly_data)}条")
            
            # 3. 加载走势数据 (基本面)
            self.trend_data = self._load_trend_data()
            print(f"✅ 走势数据: {len(self.trend_data)}条")
            
            # 4. 加载时K数据 (实时监控)
            self.hourly_data = self._load_hourly_data()
            print(f"✅ 时K数据: {len(self.hourly_data)}条")
            
            # 5. 数据质量检查
            self._validate_data_quality()
            
            self.data_loaded = True
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def _load_daily_data(self) -> pd.DataFrame:
        """加载日K数据 - 策略分析主数据源"""
        from pathlib import Path

        # 构建文件路径
        daily1_path = Path(self.data_path) / "日k1.json"
        daily2_path = Path(self.data_path) / "日k2.json"

        print(f"🔍 尝试加载日K1数据: {daily1_path}")
        print(f"🔍 尝试加载日K2数据: {daily2_path}")

        # 检查文件是否存在
        if not daily1_path.exists():
            raise FileNotFoundError(f"日K1数据文件不存在: {daily1_path}")
        if not daily2_path.exists():
            raise FileNotFoundError(f"日K2数据文件不存在: {daily2_path}")

        # 合并日K数据
        with open(daily1_path, 'r', encoding='utf-8') as f:
            daily1_raw = json.load(f)
            print(f"✅ 日K1数据加载成功: {len(daily1_raw)} 条")

        with open(daily2_path, 'r', encoding='utf-8') as f:
            daily2_raw = json.load(f)
            print(f"✅ 日K2数据加载成功: {len(daily2_raw)} 条")
        
        daily_raw = daily1_raw + daily2_raw
        daily_raw.sort(key=lambda x: x[0])
        
        # 根据steamdt源数据结构说明：[时间戳, 开盘价, 收盘价, 最高价, 最低价, 交易量, 交易额]
        df = pd.DataFrame(daily_raw, columns=[
            'timestamp', 'open', 'close', 'high', 'low', 'volume', 'amount'
        ])
        
        # 数据预处理
        df['datetime'] = pd.to_datetime(df['timestamp'].astype(int), unit='s')
        df = df.sort_values('datetime').reset_index(drop=True)

        # 根据steamdt源数据结构说明：null值当作0处理
        df['volume'] = df['volume'].fillna(0)
        df['amount'] = df['amount'].fillna(0)

        # 数据类型转换
        for col in ['open', 'low', 'high', 'close', 'volume', 'amount']:
            df[col] = df[col].astype(float)
        
        return df
    
    def _load_weekly_data(self) -> pd.DataFrame:
        """加载周K数据 - 长期趋势分析"""
        from pathlib import Path

        weekly_path = Path(self.data_path) / "周k.json"
        print(f"🔍 尝试加载周K数据: {weekly_path}")

        if not weekly_path.exists():
            raise FileNotFoundError(f"周K数据文件不存在: {weekly_path}")

        with open(weekly_path, 'r', encoding='utf-8') as f:
            weekly_raw = json.load(f)
            print(f"✅ 周K数据加载成功: {len(weekly_raw)} 条")
        
        df = pd.DataFrame(weekly_raw, columns=[
            'timestamp', 'open', 'close', 'high', 'low', 'volume', 'amount'
        ])

        df['datetime'] = pd.to_datetime(df['timestamp'].astype(int), unit='s')
        df = df.sort_values('datetime').reset_index(drop=True)

        # 根据steamdt源数据结构说明：周K数据的交易量和交易额大部分为null，不用于分析
        # 先处理null值：交易量和交易额为null时设为0（按文档要求）
        df['volume'] = df['volume'].fillna(0)
        df['amount'] = df['amount'].fillna(0)

        # 处理价格字段
        for col in ['open', 'low', 'high', 'close']:
            df[col] = df[col].astype(float)

        # 交易量和交易额转换为float（但不用于分析）
        df['volume'] = df['volume'].astype(float)
        df['amount'] = df['amount'].astype(float)
        
        return df
    
    def _load_trend_data(self) -> pd.DataFrame:
        """加载走势数据 - 基本面分析"""
        from pathlib import Path

        trend_path = Path(self.data_path) / "走势_6m.json"
        print(f"🔍 尝试加载6个月走势数据: {trend_path}")

        if not trend_path.exists():
            raise FileNotFoundError(f"6个月走势数据文件不存在: {trend_path}")

        with open(trend_path, 'r', encoding='utf-8') as f:
            trend_raw = json.load(f)
            print(f"✅ 走势数据加载成功: {len(trend_raw)} 条")
        
        # 根据steamdt源数据结构说明：走势6m数据
        # [时间戳, 在售价格, 在售数量, 求购价格, 求购数量, 日交易额, 日交易量, 截至当日的存世量]
        df = pd.DataFrame(trend_raw, columns=[
            'timestamp', 'price', 'supply', 'bid_price', 'bid_quantity',
            'daily_amount', 'daily_count', 'total_supply'
        ])
        
        df['datetime'] = pd.to_datetime(df['timestamp'].astype(int), unit='s')
        df = df.sort_values('datetime').reset_index(drop=True)

        # 根据steamdt源数据结构说明：null值当作0处理
        df['daily_amount'] = df['daily_amount'].fillna(0)
        df['daily_count'] = df['daily_count'].fillna(0)

        for col in ['price', 'supply', 'bid_price', 'bid_quantity', 'daily_amount', 'daily_count', 'total_supply']:
            df[col] = df[col].astype(float)
        
        return df
    
    def _load_hourly_data(self) -> pd.DataFrame:
        """加载时K数据 - 实时监控专用"""
        from pathlib import Path

        hourly_path = Path(self.data_path) / "时k.json"
        print(f"🔍 尝试加载时K数据: {hourly_path}")

        if not hourly_path.exists():
            raise FileNotFoundError(f"时K数据文件不存在: {hourly_path}")

        with open(hourly_path, 'r', encoding='utf-8') as f:
            hourly_raw = json.load(f)
            print(f"✅ 时K数据加载成功: {len(hourly_raw)} 条")
        
        df = pd.DataFrame(hourly_raw, columns=[
            'timestamp', 'open', 'close', 'high', 'low', 'volume', 'amount'
        ])
        
        df['datetime'] = pd.to_datetime(df['timestamp'].astype(int), unit='s')
        df = df.sort_values('datetime').reset_index(drop=True)

        # 根据steamdt源数据结构说明：null值当作0处理
        df['volume'] = df['volume'].fillna(0)
        df['amount'] = df['amount'].fillna(0)

        for col in ['open', 'low', 'high', 'close', 'volume', 'amount']:
            df[col] = df[col].astype(float)
        
        return df
    
    def _validate_data_quality(self):
        """数据质量验证"""
        self.data_quality = {
            'daily_data': {
                'records': len(self.daily_data),
                'time_span_days': (self.daily_data['datetime'].max() - self.daily_data['datetime'].min()).days,
                'avg_daily_volume': self.daily_data['volume'].mean(),
                'data_completeness': len(self.daily_data[self.daily_data['volume'] > 0]) / len(self.daily_data) * 100
            },
            'weekly_data': {
                'records': len(self.weekly_data),
                'time_span_weeks': len(self.weekly_data),
            },
            'trend_data': {
                'records': len(self.trend_data),
                'time_span_days': (self.trend_data['datetime'].max() - self.trend_data['datetime'].min()).days,
            },
            'hourly_data': {
                'records': len(self.hourly_data),
                'time_span_days': (self.hourly_data['datetime'].max() - self.hourly_data['datetime'].min()).days,
                'coverage_rate': len(self.hourly_data[self.hourly_data['volume'] > 0]) / len(self.hourly_data) * 100
            }
        }
        
        print(f"📊 数据质量报告:")
        print(f"   日K数据: {self.data_quality['daily_data']['records']}条, {self.data_quality['daily_data']['time_span_days']}天")
        print(f"   周K数据: {self.data_quality['weekly_data']['records']}条")
        print(f"   走势数据: {self.data_quality['trend_data']['records']}条")
        print(f"   时K数据: {self.data_quality['hourly_data']['records']}条 (仅用于实时监控)")
    
    def get_daily_data(self) -> pd.DataFrame:
        """获取日K数据 - 策略分析专用"""
        if not self.data_loaded:
            raise ValueError("数据未加载，请先调用load_all_data()")
        return self.daily_data.copy()
    
    def get_weekly_data(self) -> pd.DataFrame:
        """获取周K数据 - 长期趋势分析专用"""
        if not self.data_loaded:
            raise ValueError("数据未加载，请先调用load_all_data()")
        return self.weekly_data.copy()
    
    def get_trend_data(self) -> pd.DataFrame:
        """获取走势数据 - 基本面分析专用"""
        if not self.data_loaded:
            raise ValueError("数据未加载，请先调用load_all_data()")
        return self.trend_data.copy()
    
    def get_hourly_data(self) -> pd.DataFrame:
        """获取时K数据 - 仅用于实时监控"""
        if not self.data_loaded:
            raise ValueError("数据未加载，请先调用load_all_data()")
        print("⚠️ 警告: 时K数据仅用于实时监控，不应用于策略分析")
        return self.hourly_data.copy()
    
    def get_market_characteristics(self) -> Dict:
        """基于日K数据计算市场特征 - 统一标准"""
        daily_data = self.get_daily_data()
        
        # 基于日K数据的统一计算
        daily_volumes = daily_data['volume']
        daily_prices = daily_data['close']
        
        # 市场活跃度
        avg_daily_volume = daily_volumes.mean()
        total_days = len(daily_data)
        
        # 波动率 (基于日收益率)
        daily_returns = daily_prices.pct_change().dropna()
        daily_volatility = daily_returns.std() * 100
        
        # 市场分类 (统一标准)
        if avg_daily_volume > 150 and daily_volatility > 2:
            market_type = "高活跃交易市场"
            liquidity_level = "优秀"
        elif avg_daily_volume > 50 and daily_volatility > 1:
            market_type = "中等活跃市场"
            liquidity_level = "良好"
        elif avg_daily_volume > 10:
            market_type = "低活跃市场"
            liquidity_level = "一般"
        else:
            market_type = "收藏品市场"
            liquidity_level = "较差"
        
        return {
            'market_type': market_type,
            'avg_daily_volume': avg_daily_volume,
            'daily_volatility': daily_volatility,
            'liquidity_level': liquidity_level,
            'total_trading_days': total_days,
            'data_source': '日K数据 (统一标准)'
        }
    
    def get_current_market_snapshot(self) -> Dict:
        """获取当前市场快照 - 基于走势数据"""
        trend_data = self.get_trend_data()
        latest_trend = trend_data.iloc[-1]
        
        return {
            'current_price': latest_trend['price'],
            'supply': latest_trend['supply'],
            'bid_price': latest_trend['bid_price'],
            'bid_quantity': latest_trend['bid_quantity'],
            'total_supply': latest_trend['total_supply'],
            'bid_premium': (latest_trend['bid_price'] - latest_trend['price']) / latest_trend['price'] * 100,
            'supply_ratio': latest_trend['supply'] / latest_trend['total_supply'] * 100,
            'bid_depth': latest_trend['bid_quantity'] / latest_trend['supply'] * 100,
            'data_source': '走势数据 (基本面快照)'
        }
    
    def validate_data_consistency(self) -> Dict:
        """数据一致性验证"""
        # 检查重叠期间的数据一致性
        daily_data = self.get_daily_data()
        hourly_data = self.hourly_data
        
        # 找到重叠时间段
        daily_start = daily_data['datetime'].min().date()
        daily_end = daily_data['datetime'].max().date()
        hourly_start = hourly_data['datetime'].min().date()
        hourly_end = hourly_data['datetime'].max().date()
        
        overlap_start = max(daily_start, hourly_start)
        overlap_end = min(daily_end, hourly_end)
        
        if overlap_start <= overlap_end:
            # 计算重叠期间的成交量
            overlap_daily = daily_data[
                (daily_data['datetime'].dt.date >= overlap_start) & 
                (daily_data['datetime'].dt.date <= overlap_end)
            ]
            
            overlap_hourly = hourly_data[
                (hourly_data['datetime'].dt.date >= overlap_start) & 
                (hourly_data['datetime'].dt.date <= overlap_end)
            ]
            
            daily_volume = overlap_daily['volume'].sum()
            hourly_volume = overlap_hourly['volume'].sum()
            
            consistency = abs(daily_volume - hourly_volume) / daily_volume * 100 if daily_volume > 0 else 0
            
            return {
                'overlap_period': f"{overlap_start} 到 {overlap_end}",
                'daily_volume': daily_volume,
                'hourly_volume': hourly_volume,
                'consistency_rate': 100 - consistency,
                'is_consistent': consistency < 5  # 5%以内认为一致
            }
        
        return {'error': '无重叠时间段'}
