"""
优化的饰品DAO - 使用正确的SQL JOIN查询

解决饰品查询页面的性能问题：
1. 一条SQL查询获取所有数据
2. 在数据库层面进行筛选
3. 避免Python层面的价格和在售量筛选
"""

from typing import List, Dict, Any, Optional
from sqlalchemy import and_, func, desc, text
from src.cs2_investment.config.database import get_db_session


class ItemDaoOptimized:
    """优化的饰品DAO - 使用正确的SQL设计"""
    
    def search_items_with_complete_data(self,
                                      name_query: Optional[str] = None,
                                      item_types: Optional[List[str]] = None,
                                      qualities: Optional[List[str]] = None,
                                      rarities: Optional[List[str]] = None,
                                      exteriors: Optional[List[str]] = None,
                                      price_min: Optional[float] = None,
                                      price_max: Optional[float] = None,
                                      sell_count_min: Optional[int] = None,
                                      sell_count_max: Optional[int] = None,
                                      arbitrage_threshold: Optional[float] = None,
                                      arbitrage_card_price: Optional[float] = None,
                                      item_ids: Optional[List[str]] = None,
                                      user_id: Optional[str] = None,
                                      sort_by: str = 'updated_desc',
                                      limit: int = 50,
                                      offset: int = 0) -> List[Dict[str, Any]]:
        """
        统一的饰品查询方法 - 支持全部饰品查询和收藏饰品查询

        Args:
            user_id: 如果提供，则只查询该用户收藏的饰品；如果为None，则查询所有饰品
            其他参数: 标准的筛选条件

        这是正确的实现方式：
        1. 使用JOIN关联所有相关表
        2. 在SQL层面进行所有筛选
        3. 一次性返回完整数据
        4. 支持收藏模式和全部模式
        """
        
        query_mode = "收藏饰品" if user_id else "全部饰品"
        print(f"🚀 [统一查询] 使用单条SQL查询{query_mode}数据")
        print(f"   👤 用户ID: {user_id}")
        print(f"   🔍 筛选条件: name={name_query}, types={item_types}")
        print(f"   🎨 品质和稀有度: qualities={qualities}, rarities={rarities}")
        print(f"   🔧 磨损条件: exteriors={exteriors}")
        print(f"   💰 价格范围: {price_min}-{price_max}")
        print(f"   📊 在售量范围: {sell_count_min}-{sell_count_max} (新逻辑：BUFF或YOUPIN任一平台满足条件)")
        
        try:
            with get_db_session() as session:
                # 构建核心SQL查询 - 支持收藏模式和全部模式
                if user_id:
                    # 收藏模式：只查询用户收藏的饰品，并包含收藏信息
                    sql_query = """
                    SELECT
                        -- 收藏信息
                        f.id as favorite_id,
                        f.user_id,
                        f.created_at as favorite_created_at,
                        f.updated_at as favorite_updated_at,
                        f.notes as favorite_notes,

                        -- 饰品基础信息
                        i.item_id,
                        i.name as item_name,
                        i.item_type,
                        i.quality,
                        i.rarity,
                        i.exterior,
                        i.image_url,
                        i.market_hash_name,
                        i.arbitrage_ratio,
                        i.last_price_update,
                        i.def_index_name,
                        i.platform_mappings,
                        i.created_at,
                        i.updated_at,

                        -- 平台价格信息（聚合）- 使用平台名称+数据源作为唯一标识
                        GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'))) as platforms,
                        GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'), ':', COALESCE(pp.sell_price, 0))) as sell_prices,
                        GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'), ':', COALESCE(pp.sell_count, 0))) as sell_counts,
                        GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'), ':', COALESCE(pp.bidding_price, 0))) as bidding_prices,
                        GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'), ':', COALESCE(pp.bidding_count, 0))) as bidding_counts,
                        GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'), ':', COALESCE(pp.steamdt_update_time, ''))) as update_times,

                        -- 计算总在售量（用于筛选）
                        COALESCE(SUM(CASE WHEN pp.data_source = 'steamdt' THEN pp.sell_count ELSE 0 END), 0) as total_sell_count,

                        -- 获取主要平台价格（用于价格筛选）
                        MAX(CASE WHEN UPPER(pp.platform_name) = 'BUFF' THEN pp.sell_price END) as buff_price,
                        MAX(CASE WHEN UPPER(pp.platform_name) = 'YOUPIN' THEN pp.sell_price END) as youpin_price,

                        -- 获取主要平台在售量（用于在售量筛选）
                        MAX(CASE WHEN UPPER(pp.platform_name) = 'BUFF' AND pp.data_source = 'steamdt' THEN pp.sell_count END) as buff_sell_count,
                        MAX(CASE WHEN UPPER(pp.platform_name) = 'YOUPIN' AND pp.data_source = 'steamdt' THEN pp.sell_count END) as youpin_sell_count,

                        -- 计算求购利润相关字段
                        MAX(CASE WHEN UPPER(pp.platform_name) = 'STEAM' AND pp.data_source = 'steam_direct' THEN pp.bidding_price END) as steam_direct_bidding_price,
                        LEAST(
                            COALESCE(MAX(CASE WHEN UPPER(pp.platform_name) = 'BUFF' THEN pp.sell_price END), 999999),
                            COALESCE(MAX(CASE WHEN UPPER(pp.platform_name) = 'YOUPIN' THEN pp.sell_price END), 999999)
                        ) as min_sell_price

                    FROM favorites f
                    INNER JOIN items i ON f.item_id = i.item_id
                    LEFT JOIN platform_prices pp ON i.item_id = pp.item_id AND pp.is_active = 1

                    WHERE f.user_id = :user_id
                    """
                    params = {'user_id': user_id}
                else:
                    # 全部模式：查询所有饰品
                    sql_query = """
                    SELECT
                        -- 饰品基础信息
                        i.item_id,
                        i.name as item_name,
                        i.item_type,
                        i.quality,
                        i.rarity,
                        i.exterior,
                        i.image_url,
                        i.market_hash_name,
                        i.arbitrage_ratio,
                        i.last_price_update,
                        i.def_index_name,
                        i.platform_mappings,
                        i.created_at,
                        i.updated_at,

                        -- 平台价格信息（聚合）- 使用平台名称+数据源作为唯一标识
                        GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'))) as platforms,
                        GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'), ':', COALESCE(pp.sell_price, 0))) as sell_prices,
                        GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'), ':', COALESCE(pp.sell_count, 0))) as sell_counts,
                        GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'), ':', COALESCE(pp.bidding_price, 0))) as bidding_prices,
                        GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'), ':', COALESCE(pp.bidding_count, 0))) as bidding_counts,
                        GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'), ':', COALESCE(pp.steamdt_update_time, ''))) as update_times,

                        -- 计算总在售量（用于筛选）
                        COALESCE(SUM(CASE WHEN pp.data_source = 'steamdt' THEN pp.sell_count ELSE 0 END), 0) as total_sell_count,

                        -- 获取主要平台价格（用于价格筛选）
                        MAX(CASE WHEN UPPER(pp.platform_name) = 'BUFF' THEN pp.sell_price END) as buff_price,
                        MAX(CASE WHEN UPPER(pp.platform_name) = 'YOUPIN' THEN pp.sell_price END) as youpin_price,

                        -- 获取主要平台在售量（用于在售量筛选）
                        MAX(CASE WHEN UPPER(pp.platform_name) = 'BUFF' AND pp.data_source = 'steamdt' THEN pp.sell_count END) as buff_sell_count,
                        MAX(CASE WHEN UPPER(pp.platform_name) = 'YOUPIN' AND pp.data_source = 'steamdt' THEN pp.sell_count END) as youpin_sell_count,

                        -- 计算求购利润相关字段
                        MAX(CASE WHEN UPPER(pp.platform_name) = 'STEAM' AND pp.data_source = 'steam_direct' THEN pp.bidding_price END) as steam_direct_bidding_price,
                        LEAST(
                            COALESCE(MAX(CASE WHEN UPPER(pp.platform_name) = 'BUFF' THEN pp.sell_price END), 999999),
                            COALESCE(MAX(CASE WHEN UPPER(pp.platform_name) = 'YOUPIN' THEN pp.sell_price END), 999999)
                        ) as min_sell_price

                    FROM items i
                    LEFT JOIN platform_prices pp ON i.item_id = pp.item_id AND pp.is_active = 1

                    WHERE 1=1
                    """
                    params = {}

                # 添加筛选条件
                if name_query:
                    sql_query += " AND (i.name LIKE :name_query OR i.market_hash_name LIKE :name_query)"
                    params['name_query'] = f"%{name_query}%"
                
                if item_types:
                    placeholders = ','.join([f":item_type_{i}" for i in range(len(item_types))])
                    sql_query += f" AND i.item_type IN ({placeholders})"
                    for i, item_type in enumerate(item_types):
                        params[f'item_type_{i}'] = item_type
                
                if qualities:
                    placeholders = ','.join([f":quality_{i}" for i in range(len(qualities))])
                    sql_query += f" AND i.quality IN ({placeholders})"
                    for i, quality in enumerate(qualities):
                        params[f'quality_{i}'] = quality
                
                if rarities:
                    placeholders = ','.join([f":rarity_{i}" for i in range(len(rarities))])
                    sql_query += f" AND i.rarity IN ({placeholders})"
                    for i, rarity in enumerate(rarities):
                        params[f'rarity_{i}'] = rarity

                if exteriors:
                    print(f"🔧 [SQL构建] 添加磨损过滤条件: {exteriors}")
                    placeholders = ','.join([f":exterior_{i}" for i in range(len(exteriors))])
                    sql_query += f" AND i.exterior IN ({placeholders})"
                    for i, exterior in enumerate(exteriors):
                        params[f'exterior_{i}'] = exterior
                    print(f"🔧 [SQL构建] 磨损参数: {[f'exterior_{i}' for i in range(len(exteriors))]}")

                if arbitrage_threshold is not None:
                    sql_query += " AND i.arbitrage_ratio >= :arbitrage_threshold"
                    params['arbitrage_threshold'] = arbitrage_threshold
                
                if item_ids:
                    placeholders = ','.join([f":item_id_{i}" for i in range(len(item_ids))])
                    sql_query += f" AND i.item_id IN ({placeholders})"
                    for i, item_id in enumerate(item_ids):
                        params[f'item_id_{i}'] = item_id
                
                # GROUP BY子句 - 根据模式添加不同的字段
                if user_id:
                    # 收藏模式：包含收藏信息字段
                    sql_query += """
                    GROUP BY f.id, f.user_id, f.created_at, f.updated_at, f.notes,
                             i.item_id, i.name, i.item_type, i.quality, i.rarity, i.exterior,
                             i.image_url, i.market_hash_name, i.arbitrage_ratio, i.last_price_update,
                             i.def_index_name, i.platform_mappings, i.created_at, i.updated_at
                    """
                else:
                    # 全部模式：只包含饰品字段
                    sql_query += """
                    GROUP BY i.item_id, i.name, i.item_type, i.quality, i.rarity, i.exterior,
                             i.image_url, i.market_hash_name, i.arbitrage_ratio, i.last_price_update,
                             i.def_index_name, i.platform_mappings, i.created_at, i.updated_at
                    """
                
                # HAVING子句（用于聚合后的筛选）
                having_conditions = []
                
                if price_min is not None or price_max is not None:
                    price_condition = []
                    if price_min is not None:
                        price_condition.append("(buff_price >= :price_min OR youpin_price >= :price_min)")
                        params['price_min'] = price_min
                    if price_max is not None:
                        price_condition.append("(buff_price <= :price_max OR youpin_price <= :price_max)")
                        params['price_max'] = price_max
                    
                    if price_condition:
                        having_conditions.append(f"({' AND '.join(price_condition)})")
                
                if sell_count_min is not None:
                    # 修改逻辑：只要BUFF或YOUPIN任意一个平台的在售量大于等于最小值即可
                    having_conditions.append("(COALESCE(buff_sell_count, 0) >= :sell_count_min OR COALESCE(youpin_sell_count, 0) >= :sell_count_min)")
                    params['sell_count_min'] = sell_count_min

                if sell_count_max is not None:
                    # 修改逻辑：BUFF和YOUPIN的在售量都要小于等于最大值
                    having_conditions.append("(COALESCE(buff_sell_count, 0) <= :sell_count_max AND COALESCE(youpin_sell_count, 0) <= :sell_count_max)")
                    params['sell_count_max'] = sell_count_max
                
                if having_conditions:
                    sql_query += f" HAVING {' AND '.join(having_conditions)}"
                
                # 排序 - 支持收藏模式的特殊排序
                if sort_by == 'name_asc':
                    sql_query += " ORDER BY i.name ASC"
                elif sort_by == 'name_desc':
                    sql_query += " ORDER BY i.name DESC"
                elif sort_by == 'arbitrage_desc':
                    sql_query += " ORDER BY i.arbitrage_ratio DESC"
                elif sort_by == 'created_desc' and user_id:
                    # 收藏模式：按收藏时间降序排序
                    sql_query += " ORDER BY f.created_at DESC"
                elif sort_by == 'price_asc':
                    sql_query += " ORDER BY COALESCE(buff_price, youpin_price, 0) ASC"
                elif sort_by == 'price_desc':
                    sql_query += " ORDER BY COALESCE(buff_price, youpin_price, 0) DESC"
                elif sort_by == 'purchase_profit_desc':
                    # 按求购利润降序排序：只对有steam_direct求购价的饰品排序
                    if arbitrage_card_price is not None:
                        card_price = arbitrage_card_price
                    else:
                        # 从配置中获取美元汇率
                        try:
                            from src.cs2_investment.config.streamlit_config import get_streamlit_config
                            streamlit_config = get_streamlit_config()
                            card_price = streamlit_config.real_exchange_rate
                        except Exception:
                            card_price = 7.21  # 最后的默认值
                    sql_query += f" ORDER BY CASE WHEN steam_direct_bidding_price IS NOT NULL AND steam_direct_bidding_price > 0 THEN (min_sell_price - ({card_price} * steam_direct_bidding_price)) ELSE -999999 END DESC"
                elif sort_by == 'purchase_profit_rate_desc':
                    # 按求购利润率降序排序：只对有steam_direct求购价的饰品排序
                    if arbitrage_card_price is not None:
                        card_price = arbitrage_card_price
                    else:
                        # 从配置中获取美元汇率
                        try:
                            from src.cs2_investment.config.streamlit_config import get_streamlit_config
                            streamlit_config = get_streamlit_config()
                            card_price = streamlit_config.real_exchange_rate
                        except Exception:
                            card_price = 7.21  # 最后的默认值
                    sql_query += f" ORDER BY CASE WHEN steam_direct_bidding_price IS NOT NULL AND steam_direct_bidding_price > 0 THEN ((min_sell_price - ({card_price} * steam_direct_bidding_price)) / ({card_price} * steam_direct_bidding_price) * 100) ELSE -999999 END DESC"
                else:  # updated_desc
                    sql_query += " ORDER BY i.last_price_update DESC"
                
                # 分页
                sql_query += " LIMIT :limit OFFSET :offset"
                params['limit'] = limit
                params['offset'] = offset
                
                # 执行查询
                print(f"🔍 [SQL查询] 排序: {sort_by}, 限制: {limit}")
                print(f"📊 [完整SQL] {sql_query}")
                print(f"📊 [SQL参数] {params}")

                result = session.execute(text(sql_query), params)
                rows = result.fetchall()
                
                print(f"✅ [SQL查询完成] 找到 {len(rows)} 个饰品")

                # 调试：检查前几个饰品的求购利润相关字段
                if sort_by in ['purchase_profit_desc', 'purchase_profit_rate_desc']:
                    print(f"🔍 [求购利润排序调试] 检查前5个饰品的数据:")
                    for i, row in enumerate(rows[:5]):
                        steam_bidding = getattr(row, 'steam_direct_bidding_price', None)
                        min_price = getattr(row, 'min_sell_price', None)
                        print(f"   {i+1}. {row.item_name[:30]}...")
                        print(f"      steam_direct_bidding_price: {steam_bidding}")
                        print(f"      min_sell_price: {min_price}")
                        if steam_bidding and min_price and steam_bidding > 0:
                            # 确保类型转换
                            profit_rate = ((float(min_price) - (5.0 * float(steam_bidding))) / (5.0 * float(steam_bidding)) * 100)
                            print(f"      计算的利润率: {profit_rate:.2f}%")
                        else:
                            print(f"      无法计算利润率")

                # 转换结果
                formatted_results = []
                for row in rows:
                    # 解析平台价格信息
                    platform_prices = self._parse_platform_prices(
                        row.platforms, row.sell_prices, row.sell_counts,
                        row.bidding_prices, row.bidding_counts, row.update_times
                    )
                    
                    # 计算求购利润（如果有相关数据）
                    purchase_profit_info = None
                    if hasattr(row, 'steam_direct_bidding_price') and hasattr(row, 'min_sell_price'):
                        steam_bidding_price = self._safe_float_convert(row.steam_direct_bidding_price)
                        min_sell_price = self._safe_float_convert(row.min_sell_price)



                        if steam_bidding_price and min_sell_price and steam_bidding_price > 0:
                            # 使用传入的搬砖卡价，如果没有则从配置获取美元汇率
                            if arbitrage_card_price is not None:
                                card_price = arbitrage_card_price
                            else:
                                try:
                                    from src.cs2_investment.config.streamlit_config import get_streamlit_config
                                    streamlit_config = get_streamlit_config()
                                    card_price = streamlit_config.real_exchange_rate
                                except Exception:
                                    card_price = 7.21  # 最后的默认值
                            # 确保所有数值都是float类型，避免Decimal和float混合运算
                            cost = float(card_price) * float(steam_bidding_price)
                            profit = float(min_sell_price) - cost
                            profit_rate = (profit / cost * 100) if cost > 0 else 0



                            purchase_profit_info = {
                                'profit': profit,
                                'profit_rate': profit_rate,
                                'purchase_profit': profit,  # 兼容饰品卡片的字段名
                                'purchase_profit_rate': profit_rate  # 兼容饰品卡片的字段名
                            }

                    # 构建结果 - 根据模式包含不同的字段
                    item_data = {
                        # 饰品信息
                        'item_id': row.item_id,
                        'name': row.item_name,
                        'item_type': row.item_type,
                        'quality': row.quality,
                        'rarity': row.rarity,
                        'exterior': row.exterior,
                        'image_url': row.image_url,
                        'market_hash_name': row.market_hash_name,
                        'arbitrage_ratio': self._safe_float_convert(row.arbitrage_ratio),
                        'last_price_update': row.last_price_update,
                        'def_index_name': row.def_index_name,
                        'platform_mappings': row.platform_mappings,
                        'created_at': row.created_at,
                        'updated_at': row.updated_at,

                        # 平台价格
                        'platform_prices': platform_prices,

                        # 统计信息
                        'total_sell_count': row.total_sell_count
                    }

                    # 如果是收藏模式，添加收藏信息
                    if user_id and hasattr(row, 'favorite_id'):
                        item_data.update({
                            'favorite_id': row.favorite_id,
                            'user_id': row.user_id,
                            'favorite_created_at': row.favorite_created_at,
                            'favorite_updated_at': row.favorite_updated_at,
                            'favorite_notes': row.favorite_notes,
                            'is_favorite': True
                        })

                    # 添加求购利润信息
                    if purchase_profit_info:
                        item_data.update(purchase_profit_info)

                    formatted_results.append(item_data)
                
                print(f"🎯 [最终结果] 返回 {len(formatted_results)} 个饰品")
                return formatted_results
                
        except Exception as e:
            print(f"❌ [SQL查询失败] {e}")
            raise
    
    def _parse_platform_prices(self, platforms_str, sell_prices_str, sell_counts_str,
                              bidding_prices_str, bidding_counts_str, update_times_str) -> Dict[str, Dict]:
        """解析聚合的平台价格信息"""

        platform_prices = {}

        # 安全的字符串处理
        platforms_str = str(platforms_str) if platforms_str is not None else ''
        sell_prices_str = str(sell_prices_str) if sell_prices_str is not None else ''
        sell_counts_str = str(sell_counts_str) if sell_counts_str is not None else ''
        bidding_prices_str = str(bidding_prices_str) if bidding_prices_str is not None else ''
        bidding_counts_str = str(bidding_counts_str) if bidding_counts_str is not None else ''
        update_times_str = str(update_times_str) if update_times_str is not None else ''

        if not platforms_str or platforms_str == 'None':
            return platform_prices

        # 解析平台信息（格式：平台名称|数据源）
        platform_entries = platforms_str.split(',') if platforms_str else []
        
        # 解析各种价格信息（新格式：平台名称|数据源:值）
        sell_price_dict = {}
        sell_count_dict = {}
        bidding_price_dict = {}
        bidding_count_dict = {}
        update_time_dict = {}

        for price_info in (sell_prices_str or '').split(','):
            if ':' in price_info:
                platform_source, price = price_info.split(':', 1)
                try:
                    sell_price_dict[platform_source] = float(price) if price and price != 'None' else 0
                except (ValueError, TypeError):
                    sell_price_dict[platform_source] = 0

        for count_info in (sell_counts_str or '').split(','):
            if ':' in count_info:
                platform_source, count = count_info.split(':', 1)
                try:
                    sell_count_dict[platform_source] = int(count) if count and count != 'None' else 0
                except (ValueError, TypeError):
                    sell_count_dict[platform_source] = 0

        for price_info in (bidding_prices_str or '').split(','):
            if ':' in price_info:
                platform_source, price = price_info.split(':', 1)
                try:
                    bidding_price_dict[platform_source] = float(price) if price and price != 'None' else 0
                except (ValueError, TypeError):
                    bidding_price_dict[platform_source] = 0

        for count_info in (bidding_counts_str or '').split(','):
            if ':' in count_info:
                platform_source, count = count_info.split(':', 1)
                try:
                    bidding_count_dict[platform_source] = int(count) if count and count != 'None' else 0
                except (ValueError, TypeError):
                    bidding_count_dict[platform_source] = 0

        for time_info in (update_times_str or '').split(','):
            if ':' in time_info:
                platform_source, update_time = time_info.split(':', 1)
                update_time_dict[platform_source] = update_time

        # 构建平台价格字典
        for platform_entry in platform_entries:
            if '|' in platform_entry:
                platform, data_source = platform_entry.split('|', 1)
            else:
                platform = platform_entry
                data_source = 'steamdt'

            # 根据数据源确定平台键名（处理大小写）
            platform_upper = platform.upper()
            if platform_upper == 'STEAM' and data_source == 'steam_direct':
                platform_key = 'steam_direct'
            elif platform_upper == 'STEAM' and data_source == 'steamdt':
                platform_key = 'steam'
            else:
                platform_key = platform.lower().replace(' ', '_')


            # 处理更新时间
            platform_source_key = f"{platform}|{data_source}"
            update_time_raw = update_time_dict.get(platform_source_key, '')
            update_time = None
            if update_time_raw and update_time_raw != '':
                try:
                    # 如果是时间戳，转换为datetime
                    if update_time_raw.isdigit():
                        from datetime import datetime
                        update_time = datetime.fromtimestamp(int(update_time_raw))
                    else:
                        update_time = update_time_raw
                except:
                    update_time = update_time_raw

            platform_prices[platform_key] = {
                'platform': platform,
                'sell_price': sell_price_dict.get(platform_source_key, 0),
                'sell_count': sell_count_dict.get(platform_source_key, 0),
                'bidding_price': bidding_price_dict.get(platform_source_key, 0),
                'bidding_count': bidding_count_dict.get(platform_source_key, 0),
                'data_source': data_source,
                'update_time': update_time
            }

        return platform_prices
    
    def _safe_float_convert(self, value) -> Optional[float]:
        """安全的float转换，处理Decimal类型"""
        if value is None:
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None
    
    def count_items_with_filters(self, **filters) -> int:
        """统计符合条件的饰品数量"""
        
        # 使用相同的查询逻辑，但只返回COUNT
        try:
            results = self.search_items_with_complete_data(
                limit=10000,  # 设置一个大的限制来获取总数
                **filters
            )
            return len(results)
        except Exception as e:
            print(f"❌ 统计饰品数量失败: {e}")
            return 0
