"""
蓝筹核心资产筛选算法

识别高流动性、稳定性的顶级资产。
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from .base_algorithm import BaseScreeningAlgorithm
from ..models.market_snapshot import MarketSnapshot


class BlueChipAlgorithm(BaseScreeningAlgorithm):
    """蓝筹核心资产筛选算法"""
    
    def __init__(self):
        super().__init__(
            algorithm_name="BlueChipAlgorithm",
            investment_type="蓝筹核心资产",
            version="1.0"
        )
    
    def get_screening_query(self, session: Session):
        """获取蓝筹资产筛选查询"""
        return self.get_latest_snapshots_query(session)\
            .filter(
                and_(
                    MarketSnapshot.trans_amount_3m > 10000000,  # 3个月成交额 > 1000万
                    MarketSnapshot.trans_count_3m > 1000,       # 3个月成交量 > 1000笔
                    MarketSnapshot.sell_nums > 50,              # 在售数量 > 50
                    MarketSnapshot.hot_rank.isnot(None),
                    MarketSnapshot.hot_rank < 100               # 热度排名前100
                )
            )\
            .order_by(desc(MarketSnapshot.trans_amount_3m))
    
    def calculate_score(self, snapshot: MarketSnapshot) -> float:
        """计算蓝筹资产评分"""
        score = 0.0
        
        # 成交额评分 (30分)
        if snapshot.trans_amount_3m:
            amount_score = min(float(snapshot.trans_amount_3m) / 50000000 * 30, 30)
            score += amount_score
        
        # 成交量评分 (25分)
        if snapshot.trans_count_3m:
            volume_score = min(snapshot.trans_count_3m / 5000 * 25, 25)
            score += volume_score
        
        # 热度评分 (20分)
        if snapshot.hot_rank:
            if snapshot.hot_rank <= 10:
                score += 20
            elif snapshot.hot_rank <= 50:
                score += 15
            elif snapshot.hot_rank <= 100:
                score += 10
            else:
                score += 5
        
        # 流动性评分 (15分)
        if snapshot.sell_nums:
            if snapshot.sell_nums >= 200:
                score += 15
            elif snapshot.sell_nums >= 100:
                score += 12
            elif snapshot.sell_nums >= 50:
                score += 8
            else:
                score += 4
        
        # 稳定性评分 (10分) - 基于价格波动
        stability_score = 10
        if snapshot.diff_7d:
            volatility = abs(float(snapshot.diff_7d))
            if volatility > 50:
                stability_score = 2
            elif volatility > 30:
                stability_score = 5
            elif volatility > 15:
                stability_score = 8
        
        score += stability_score
        
        return min(score, 100.0)
    
    def determine_risk_level(self, snapshot: MarketSnapshot, score: float) -> str:
        """确定蓝筹资产风险等级"""
        # 蓝筹资产通常风险较低
        risk_factors = 0
        
        # 价格波动风险
        if snapshot.diff_7d and abs(float(snapshot.diff_7d)) > 20:
            risk_factors += 1
        if snapshot.diff_1m and abs(float(snapshot.diff_1m)) > 30:
            risk_factors += 1
        
        # 流动性风险
        if not snapshot.sell_nums or snapshot.sell_nums < 50:
            risk_factors += 1
        
        # 热度风险
        if not snapshot.hot_rank or snapshot.hot_rank > 100:
            risk_factors += 1
        
        if risk_factors == 0:
            return "LOW"
        elif risk_factors <= 2:
            return "MEDIUM"
        else:
            return "HIGH"
    
    def generate_analysis_summary(self, snapshot: MarketSnapshot, score: float) -> str:
        """生成蓝筹资产分析摘要"""
        summary_parts = []
        
        # 基础信息
        if snapshot.current_price:
            summary_parts.append(f"价格: ¥{snapshot.current_price}")
        
        # 成交数据
        if snapshot.trans_amount_3m:
            summary_parts.append(f"3月成交额: ¥{snapshot.trans_amount_3m:,.0f}")
        
        if snapshot.trans_count_3m:
            summary_parts.append(f"3月成交量: {snapshot.trans_count_3m}")
        
        # 热度信息
        if snapshot.hot_rank:
            summary_parts.append(f"热度排名: #{snapshot.hot_rank}")
        
        # 流动性信息
        if snapshot.sell_nums:
            summary_parts.append(f"在售数量: {snapshot.sell_nums}")
        
        # 价格变化
        if snapshot.diff_7d:
            change_desc = "涨" if float(snapshot.diff_7d) > 0 else "跌"
            summary_parts.append(f"7天{change_desc}: {abs(float(snapshot.diff_7d)):.1f}%")
        
        # 投资建议
        if score >= 80:
            summary_parts.append("蓝筹优质资产，适合长期持有")
        elif score >= 60:
            summary_parts.append("蓝筹稳健资产，风险可控")
        else:
            summary_parts.append("蓝筹资产，但需关注风险")
        
        return "; ".join(summary_parts)
