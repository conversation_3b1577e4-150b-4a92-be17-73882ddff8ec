"""
增强技术指标数据访问对象

提供增强技术指标的数据库操作功能，支持多时间框架的技术指标存储、
批量插入、时间序列查询等功能。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy import desc, func, and_, or_
from sqlalchemy.exc import SQLAlchemyError
from loguru import logger

from .base_dao import BaseDAO
from ..models.enhanced_technical_indicators import EnhancedTechnicalIndicators
from ..config.database import get_db_session


class EnhancedTechnicalIndicatorsDAO(BaseDAO[EnhancedTechnicalIndicators]):
    """增强技术指标DAO"""
    
    def __init__(self):
        super().__init__(EnhancedTechnicalIndicators)
        self.logger = logger.bind(dao=self.__class__.__name__)
    
    def create_indicator_record(self, indicator_data: Dict[str, Any]) -> Optional[EnhancedTechnicalIndicators]:
        """创建技术指标记录"""
        try:
            with get_db_session() as session:
                indicator = EnhancedTechnicalIndicators(**indicator_data)
                session.add(indicator)
                session.flush()
                session.refresh(indicator)
                self.logger.info(f"创建技术指标记录成功: {indicator.id} - {indicator.item_id}")
                
                session.expunge(indicator)
                return indicator
        except SQLAlchemyError as e:
            self.logger.error(f"创建技术指标记录失败: {e}")
            raise
    
    def batch_insert_indicators(self, indicators_data: List[Dict[str, Any]]) -> List[EnhancedTechnicalIndicators]:
        """批量插入技术指标数据"""
        try:
            with get_db_session() as session:
                indicators = []
                for data in indicators_data:
                    indicator = EnhancedTechnicalIndicators(**data)
                    indicators.append(indicator)
                
                session.add_all(indicators)
                session.flush()
                
                for indicator in indicators:
                    session.refresh(indicator)
                    session.expunge(indicator)
                
                self.logger.info(f"批量插入技术指标成功: {len(indicators)}条")
                return indicators
        except SQLAlchemyError as e:
            self.logger.error(f"批量插入技术指标失败: {e}")
            raise
    
    def get_latest_indicators(self, item_id: str, timeframe: str) -> Optional[EnhancedTechnicalIndicators]:
        """获取最新的技术指标"""
        try:
            with get_db_session() as session:
                indicator = session.query(EnhancedTechnicalIndicators).filter(
                    and_(
                        EnhancedTechnicalIndicators.item_id == item_id,
                        EnhancedTechnicalIndicators.timeframe == timeframe
                    )
                ).order_by(desc(EnhancedTechnicalIndicators.indicator_timestamp)).first()
                
                if indicator:
                    session.expunge(indicator)
                
                return indicator
        except SQLAlchemyError as e:
            self.logger.error(f"获取最新技术指标失败: {e}")
            raise
    
    def get_indicators_by_timeframe(self, item_id: str, timeframe: str, days: int = 30) -> List[EnhancedTechnicalIndicators]:
        """根据时间框架获取技术指标历史数据"""
        try:
            with get_db_session() as session:
                start_date = datetime.now() - timedelta(days=days)
                
                indicators = session.query(EnhancedTechnicalIndicators).filter(
                    and_(
                        EnhancedTechnicalIndicators.item_id == item_id,
                        EnhancedTechnicalIndicators.timeframe == timeframe,
                        EnhancedTechnicalIndicators.indicator_timestamp >= start_date
                    )
                ).order_by(desc(EnhancedTechnicalIndicators.indicator_timestamp)).all()
                
                for indicator in indicators:
                    session.expunge(indicator)
                
                return indicators
        except SQLAlchemyError as e:
            self.logger.error(f"获取时间框架技术指标失败: {e}")
            raise
    
    def get_multi_timeframe_indicators(self, item_id: str, timeframes: List[str] = None) -> Dict[str, List[EnhancedTechnicalIndicators]]:
        """获取多时间框架的技术指标"""
        if timeframes is None:
            timeframes = ['weekly', 'daily', 'hourly']
        
        try:
            result = {}
            for timeframe in timeframes:
                result[timeframe] = self.get_indicators_by_timeframe(item_id, timeframe, days=7)
            
            return result
        except SQLAlchemyError as e:
            self.logger.error(f"获取多时间框架技术指标失败: {e}")
            raise
    
    def get_rsi_signals(self, signal_type: str, limit: int = 100) -> List[EnhancedTechnicalIndicators]:
        """获取RSI信号"""
        try:
            with get_db_session() as session:
                indicators = session.query(EnhancedTechnicalIndicators).filter(
                    EnhancedTechnicalIndicators.rsi_signal == signal_type
                ).order_by(desc(EnhancedTechnicalIndicators.indicator_timestamp)).limit(limit).all()
                
                for indicator in indicators:
                    session.expunge(indicator)
                
                return indicators
        except SQLAlchemyError as e:
            self.logger.error(f"获取RSI信号失败: {e}")
            raise
    
    def get_kdj_signals(self, signal_pattern: str, limit: int = 100) -> List[EnhancedTechnicalIndicators]:
        """获取KDJ信号"""
        try:
            with get_db_session() as session:
                indicators = session.query(EnhancedTechnicalIndicators).filter(
                    EnhancedTechnicalIndicators.kdj_signal.like(f'%{signal_pattern}%')
                ).order_by(desc(EnhancedTechnicalIndicators.indicator_timestamp)).limit(limit).all()
                
                for indicator in indicators:
                    session.expunge(indicator)
                
                return indicators
        except SQLAlchemyError as e:
            self.logger.error(f"获取KDJ信号失败: {e}")
            raise
    
    def get_volatility_analysis(self, item_id: str, days: int = 30) -> Dict[str, Any]:
        """获取波动率分析"""
        try:
            with get_db_session() as session:
                start_date = datetime.now() - timedelta(days=days)
                
                # 获取ATR统计
                atr_stats = session.query(
                    func.avg(EnhancedTechnicalIndicators.atr_14).label('avg_atr'),
                    func.max(EnhancedTechnicalIndicators.atr_14).label('max_atr'),
                    func.min(EnhancedTechnicalIndicators.atr_14).label('min_atr')
                ).filter(
                    and_(
                        EnhancedTechnicalIndicators.item_id == item_id,
                        EnhancedTechnicalIndicators.indicator_timestamp >= start_date,
                        EnhancedTechnicalIndicators.atr_14.isnot(None)
                    )
                ).first()
                
                # 获取波动率等级分布
                volatility_dist = session.query(
                    EnhancedTechnicalIndicators.volatility_level,
                    func.count(EnhancedTechnicalIndicators.id).label('count')
                ).filter(
                    and_(
                        EnhancedTechnicalIndicators.item_id == item_id,
                        EnhancedTechnicalIndicators.indicator_timestamp >= start_date
                    )
                ).group_by(EnhancedTechnicalIndicators.volatility_level).all()
                
                return {
                    'atr_statistics': {
                        'average': float(atr_stats.avg_atr) if atr_stats.avg_atr else None,
                        'maximum': float(atr_stats.max_atr) if atr_stats.max_atr else None,
                        'minimum': float(atr_stats.min_atr) if atr_stats.min_atr else None
                    },
                    'volatility_distribution': {level: count for level, count in volatility_dist},
                    'analysis_period_days': days
                }
        except SQLAlchemyError as e:
            self.logger.error(f"获取波动率分析失败: {e}")
            raise
    
    def get_trend_analysis(self, item_id: str, timeframe: str = 'daily', days: int = 30) -> Dict[str, Any]:
        """获取趋势分析"""
        try:
            with get_db_session() as session:
                start_date = datetime.now() - timedelta(days=days)
                
                indicators = session.query(EnhancedTechnicalIndicators).filter(
                    and_(
                        EnhancedTechnicalIndicators.item_id == item_id,
                        EnhancedTechnicalIndicators.timeframe == timeframe,
                        EnhancedTechnicalIndicators.indicator_timestamp >= start_date
                    )
                ).order_by(EnhancedTechnicalIndicators.indicator_timestamp).all()
                
                if not indicators:
                    return {'trend_direction': 'UNKNOWN', 'trend_strength': 0}
                
                # 计算趋势方向
                latest = indicators[-1]
                if latest.ema_12 and latest.ema_26:
                    trend_direction = 'UP' if latest.ema_12 > latest.ema_26 else 'DOWN'
                    trend_strength = abs(float(latest.ema_12 - latest.ema_26)) / float(latest.ema_26) * 100
                else:
                    trend_direction = 'UNKNOWN'
                    trend_strength = 0
                
                return {
                    'trend_direction': trend_direction,
                    'trend_strength': trend_strength,
                    'latest_ema_12': float(latest.ema_12) if latest.ema_12 else None,
                    'latest_ema_26': float(latest.ema_26) if latest.ema_26 else None,
                    'data_points': len(indicators)
                }
        except SQLAlchemyError as e:
            self.logger.error(f"获取趋势分析失败: {e}")
            raise
    
    def delete_old_indicators(self, days: int = 180) -> int:
        """删除过期的技术指标数据"""
        try:
            with get_db_session() as session:
                cutoff_date = datetime.now() - timedelta(days=days)
                
                deleted_count = session.query(EnhancedTechnicalIndicators).filter(
                    EnhancedTechnicalIndicators.indicator_timestamp < cutoff_date
                ).delete()
                
                self.logger.info(f"删除过期技术指标记录: {deleted_count}条")
                return deleted_count
        except SQLAlchemyError as e:
            self.logger.error(f"删除过期技术指标失败: {e}")
            raise
