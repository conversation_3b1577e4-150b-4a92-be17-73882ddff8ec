#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品投资分析系统 - 主入口文件
一键运行完整的饰品投资分析

使用方法:
    python main.py [饰品名称]
    
示例:
    python main.py "AK-47 传承 (久经沙场)"
    python main.py "M4A1 消音型印花集"
"""

import sys
import os
import argparse
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入核心分析系统
from main_analysis_system import CS2AnalysisSystemV2

def print_banner():
    """打印系统横幅"""
    print("=" * 80)
    print("🎯 CS2饰品投资分析系统 v2.0")
    print("📊 专业级投资分析 | 机构级风险管理 | 智能策略推荐")
    print("=" * 80)

def print_usage():
    """打印使用说明"""
    print("\n📖 使用说明:")
    print("1. 确保数据文件存在于正确的目录结构中")
    print("2. 运行命令: python main.py [饰品名称] [选项]")
    print("\n📁 数据目录结构:")
    print("   ../[饰品名称]/")
    print("   ├── 日k1.json")
    print("   ├── 日k2.json")
    print("   ├── 周k.json")
    print("   ├── 时k.json")
    print("   └── 走势.json")
    print("\n💡 示例:")
    print('   python main.py "AK-47 传承 (久经沙场)"                    # 基础分析')
    print('   python main.py "AK-47 传承 (久经沙场)" --generate-chart   # 分析+图表')
    print('   python main.py "AK-47 传承 (久经沙场)" --chart-only       # 仅生成图表')
    print('   python main.py --list-available                        # 列出可用数据')

def validate_data_files(skin_name: str) -> bool:
    """验证数据文件是否存在"""

    # 数据文件路径 (从syncps目录向上一级，然后进入饰品目录)
    data_dir = os.path.join("..", skin_name)
    required_files = ["日k1.json", "日k2.json", "周k.json", "时k.json", "走势.json"]
    
    if not os.path.exists(data_dir):
        print(f"❌ 错误: 数据目录不存在: {data_dir}")
        return False
    
    missing_files = []
    for file_name in required_files:
        file_path = os.path.join(data_dir, file_name)
        if not os.path.exists(file_path):
            missing_files.append(file_name)
    
    if missing_files:
        print(f"❌ 错误: 缺少数据文件: {', '.join(missing_files)}")
        print(f"📁 数据目录: {data_dir}")
        return False
    
    print(f"✅ 数据文件验证通过: {skin_name}")
    return True

def run_analysis(skin_name: str, generate_chart: bool = False, chart_only: bool = False):
    """运行完整分析"""

    print(f"\n🚀 开始分析: {skin_name}")
    print(f"⏰ 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 60)

    try:
        # 初始化分析系统
        analyzer = CS2AnalysisSystemV2(skin_name)

        # 运行完整分析
        result = analyzer.run_complete_analysis()

        if result:
            if not chart_only:
                print("\n" + "=" * 60)
                print("✅ 分析完成!")
                print("📊 报告已生成，请查看上方详细分析结果")
                print("=" * 60)

            # 生成图表（如果请求）
            if generate_chart or chart_only:
                print(f"\n🎨 正在生成专业图表仪表板...")
                dashboard_path = analyzer.generate_professional_dashboard()

                if dashboard_path:
                    print(f"✅ 专业图表仪表板已生成: {dashboard_path}")
                    print(f"📊 图表包含以下内容:")
                    print(f"   🔹 主价格图表 + 技术指标")
                    print(f"   🔹 MACD、RSI、KDJ副图")
                    print(f"   🔹 成交量和资金流向分析")
                    print(f"   🔹 市场情绪雷达图")
                    print(f"   🔹 风险评估和策略推荐")
                    print(f"   🔹 综合评分体系")

                    # 显示关键分析结果
                    if not chart_only:
                        print(f"\n💡 关键分析结果:")
                        analysis_results = analyzer.analysis_results

                        # 技术信号
                        signals = analysis_results.get('current_signals', {})
                        if signals:
                            print(f"   📊 技术信号: {signals.get('overall_signal', 'N/A')}")
                            print(f"   📈 RSI: {signals.get('rsi', 'N/A'):.2f}" if isinstance(signals.get('rsi'), (int, float)) else f"   📈 RSI: {signals.get('rsi', 'N/A')}")

                        # 风险评估
                        risk = analysis_results.get('risk_assessment', {})
                        if risk:
                            print(f"   ⚖️ 风险等级: {risk.get('overall_risk', 'N/A')}")

                        # 策略推荐
                        strategy = analysis_results.get('strategy_selection', {})
                        if strategy:
                            print(f"   🎯 推荐策略: {strategy.get('recommended_strategy', 'N/A')}")

                        # 交易建议
                        trading = analysis_results.get('trading_advice', {})
                        if trading:
                            print(f"   💡 交易建议: {trading.get('action', 'N/A')} (置信度: {trading.get('confidence', 'N/A')}%)")
                else:
                    print(f"❌ 专业图表仪表板生成失败")

            return True
        else:
            print("\n❌ 分析失败，请检查数据文件和系统配置")
            return False

    except Exception as e:
        print(f"\n❌ 分析过程中发生错误: {str(e)}")
        print("💡 请检查:")
        print("   1. 数据文件格式是否正确")
        print("   2. 饰品名称是否准确")
        print("   3. 系统依赖是否完整")
        return False

def main():
    """主函数"""
    
    # 打印横幅
    print_banner()
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(
        description="CS2饰品投资分析系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python main.py "AK-47 传承 (久经沙场)"
  python main.py "M4A1 消音型印花集"
  python main.py --help
        """
    )
    
    parser.add_argument(
        'skin_name',
        nargs='?',
        help='要分析的饰品名称 (必须与数据目录名称完全一致)'
    )
    
    parser.add_argument(
        '--list-available',
        action='store_true',
        help='列出可用的饰品数据'
    )

    parser.add_argument(
        '--generate-chart',
        action='store_true',
        help='生成专业图表仪表板'
    )

    parser.add_argument(
        '--chart-only',
        action='store_true',
        help='仅生成图表，不显示文字分析'
    )
    
    args = parser.parse_args()
    
    # 列出可用饰品
    if args.list_available:
        print("\n📋 可用的饰品数据:")
        parent_dir = ".."
        if os.path.exists(parent_dir):
            for item in os.listdir(parent_dir):
                item_path = os.path.join(parent_dir, item)
                if os.path.isdir(item_path) and not item.startswith('.'):
                    # 检查是否包含必要的数据文件
                    has_data = any(
                        os.path.exists(os.path.join(item_path, f))
                        for f in ["日k1.json", "日k2.json", "周k.json"]
                    )
                    if has_data:
                        print(f"   ✅ {item}")
                    else:
                        print(f"   ❌ {item} (数据不完整)")
        else:
            print("   ❌ 未找到数据目录")
        return
    
    # 检查是否提供了饰品名称
    if not args.skin_name:
        print("\n❌ 错误: 请提供要分析的饰品名称")
        print_usage()
        return
    
    skin_name = args.skin_name
    
    # 验证数据文件
    if not validate_data_files(skin_name):
        print_usage()
        return
    
    # 运行分析
    success = run_analysis(
        skin_name,
        generate_chart=args.generate_chart,
        chart_only=args.chart_only
    )

    if success:
        print(f"\n🎉 {skin_name} 分析完成!")
        if args.generate_chart or args.chart_only:
            print("🎨 专业图表仪表板已生成!")
        print("💡 提示:")
        print("   • 使用 --generate-chart 生成专业图表")
        print("   • 使用 --chart-only 仅生成图表")
        print("   • 可以使用不同的饰品名称重复运行分析")
    else:
        print(f"\n💔 {skin_name} 分析失败")
        print("🔧 请检查系统配置和数据文件")

if __name__ == "__main__":
    main()
