"""
持仓分析服务层

利用现有的风险评估和技术分析能力，为持仓提供风险分析和结构分析功能。
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import sys
from pathlib import Path
from collections import defaultdict
import statistics

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.app.services.holding_service import HoldingService
from src.cs2_investment.app.services.item_detail_service import ItemDetailService
from src.cs2_investment.dao.market_snapshot_dao import MarketSnapshotDAO


class HoldingAnalysisService:
    """持仓分析服务类"""
    
    def __init__(self):
        self.holding_service = HoldingService()
        self.item_detail_service = ItemDetailService()
        self.market_snapshot_dao = MarketSnapshotDAO()
        self.default_user_id = "default_user"
    
    def calculate_portfolio_risk(self, user_id: str) -> Dict[str, Any]:
        """计算投资组合风险"""
        try:
            holdings = self.holding_service.get_holdings_with_current_prices(user_id)
            if not holdings:
                return self._get_empty_risk_result()
            
            # 计算各种风险指标
            concentration_risk = self._calculate_concentration_risk(holdings)
            volatility_risk = self._calculate_portfolio_volatility_risk(holdings)
            liquidity_risk = self._calculate_liquidity_risk(holdings)
            market_risk = self._calculate_market_risk(holdings)
            
            # 综合风险评估
            overall_risk = self._calculate_overall_risk(
                concentration_risk, volatility_risk, liquidity_risk, market_risk
            )
            
            return {
                'success': True,
                'total_holdings': len(holdings),
                'concentration_risk': concentration_risk,
                'volatility_risk': volatility_risk,
                'liquidity_risk': liquidity_risk,
                'market_risk': market_risk,
                'overall_risk': overall_risk,
                'risk_summary': self._generate_risk_summary(overall_risk),
                'recommendations': self._generate_risk_recommendations(
                    concentration_risk, volatility_risk, liquidity_risk
                )
            }
            
        except Exception as e:
            print(f"计算投资组合风险失败: {e}")
            empty_result = self._get_empty_risk_result()
            empty_result.update({
                'success': False,
                'error': str(e)
            })
            return empty_result
    
    def _calculate_concentration_risk(self, holdings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算集中度风险"""
        if not holdings:
            return {'level': 'LOW', 'score': 0, 'details': {}}
        
        total_value = sum(h.get('current_value', h.get('total_cost', 0)) for h in holdings)
        if total_value <= 0:
            return {'level': 'LOW', 'score': 0, 'details': {}}
        
        # 计算单一饰品占比
        max_holding_ratio = 0
        top_holdings = []
        
        for holding in holdings:
            value = holding.get('current_value', holding.get('total_cost', 0))
            ratio = (value / total_value) * 100
            max_holding_ratio = max(max_holding_ratio, ratio)
            top_holdings.append({
                'item_name': holding.get('item_name', ''),
                'ratio': ratio,
                'value': value
            })
        
        # 按占比排序
        top_holdings.sort(key=lambda x: x['ratio'], reverse=True)
        
        # 计算前5大持仓占比
        top5_ratio = sum(h['ratio'] for h in top_holdings[:5])
        
        # 风险等级判断
        if max_holding_ratio > 50:
            risk_level = 'HIGH'
            risk_score = 80
        elif max_holding_ratio > 30:
            risk_level = 'MEDIUM'
            risk_score = 60
        elif top5_ratio > 80:
            risk_level = 'MEDIUM'
            risk_score = 50
        else:
            risk_level = 'LOW'
            risk_score = 20
        
        return {
            'level': risk_level,
            'score': risk_score,
            'max_holding_ratio': max_holding_ratio,
            'top5_ratio': top5_ratio,
            'top_holdings': top_holdings[:5],
            'description': f'最大单一持仓占比{max_holding_ratio:.1f}%，前5大持仓占比{top5_ratio:.1f}%'
        }
    
    def _calculate_portfolio_volatility_risk(self, holdings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算投资组合波动率风险"""
        if not holdings:
            return {'level': 'LOW', 'score': 0, 'details': {}}
        
        volatility_scores = []
        weighted_volatility = 0
        total_value = sum(h.get('current_value', h.get('total_cost', 0)) for h in holdings)
        
        for holding in holdings:
            item_id = holding.get('item_id')
            if not item_id:
                continue
                
            try:
                # 复用ItemDetailService的分析能力
                item_data = self.item_detail_service.get_comprehensive_data(item_id)
                if item_data and item_data.get('key_metrics'):
                    volatility = item_data['key_metrics'].get('price_volatility', 0)
                    volatility_scores.append(volatility)
                    
                    # 按持仓价值加权
                    value = holding.get('current_value', holding.get('total_cost', 0))
                    weight = value / total_value if total_value > 0 else 0
                    weighted_volatility += volatility * weight
                    
            except Exception as e:
                print(f"获取饰品{item_id}波动率失败: {e}")
                continue
        
        # 计算平均波动率
        avg_volatility = statistics.mean(volatility_scores) if volatility_scores else 0
        
        # 风险等级判断
        if weighted_volatility > 15:
            risk_level = 'HIGH'
            risk_score = 80
        elif weighted_volatility > 10:
            risk_level = 'MEDIUM'
            risk_score = 60
        elif weighted_volatility > 5:
            risk_level = 'MEDIUM'
            risk_score = 40
        else:
            risk_level = 'LOW'
            risk_score = 20
        
        return {
            'level': risk_level,
            'score': risk_score,
            'weighted_volatility': weighted_volatility,
            'average_volatility': avg_volatility,
            'volatility_range': [min(volatility_scores), max(volatility_scores)] if volatility_scores else [0, 0],
            'description': f'加权平均波动率{weighted_volatility:.1f}%，平均波动率{avg_volatility:.1f}%'
        }
    
    def _calculate_liquidity_risk(self, holdings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算流动性风险"""
        if not holdings:
            return {'level': 'LOW', 'score': 0, 'details': {}}
        
        liquidity_scores = []
        low_liquidity_count = 0
        total_value = sum(h.get('current_value', h.get('total_cost', 0)) for h in holdings)
        
        for holding in holdings:
            item_id = holding.get('item_id')
            if not item_id:
                continue
                
            try:
                # 获取最新快照数据
                snapshot = self.market_snapshot_dao.get_latest_snapshot_by_item(item_id)
                if snapshot:
                    # 计算流动性指标
                    trans_count_7d = snapshot.get('trans_count_7d', 0)
                    sell_nums = snapshot.get('sell_nums', 0)
                    survive_num = snapshot.get('survive_num', 1)
                    
                    # 流动性评分（基于交易频率和在售比例）
                    trading_score = min(trans_count_7d / 10, 10)  # 交易频率评分
                    availability_score = min((sell_nums / survive_num) * 100, 10) if survive_num > 0 else 0
                    liquidity_score = (trading_score + availability_score) / 2
                    
                    liquidity_scores.append(liquidity_score)
                    
                    # 统计低流动性持仓
                    if liquidity_score < 3:
                        low_liquidity_count += 1
                        
            except Exception as e:
                print(f"获取饰品{item_id}流动性数据失败: {e}")
                continue
        
        # 计算平均流动性
        avg_liquidity = statistics.mean(liquidity_scores) if liquidity_scores else 5
        low_liquidity_ratio = (low_liquidity_count / len(holdings)) * 100
        
        # 风险等级判断
        if avg_liquidity < 2 or low_liquidity_ratio > 50:
            risk_level = 'HIGH'
            risk_score = 80
        elif avg_liquidity < 4 or low_liquidity_ratio > 30:
            risk_level = 'MEDIUM'
            risk_score = 60
        elif avg_liquidity < 6 or low_liquidity_ratio > 15:
            risk_level = 'MEDIUM'
            risk_score = 40
        else:
            risk_level = 'LOW'
            risk_score = 20
        
        return {
            'level': risk_level,
            'score': risk_score,
            'average_liquidity': avg_liquidity,
            'low_liquidity_count': low_liquidity_count,
            'low_liquidity_ratio': low_liquidity_ratio,
            'description': f'平均流动性评分{avg_liquidity:.1f}，{low_liquidity_count}个低流动性持仓'
        }
    
    def _calculate_market_risk(self, holdings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算市场风险"""
        if not holdings:
            return {'level': 'LOW', 'score': 0, 'details': {}}
        
        # 计算整体盈亏情况
        total_cost = sum(h.get('total_cost', 0) for h in holdings)
        total_current_value = sum(h.get('current_value', 0) for h in holdings)
        
        if total_cost <= 0:
            return {'level': 'LOW', 'score': 0, 'details': {}}
        
        portfolio_return = ((total_current_value - total_cost) / total_cost) * 100
        
        # 计算亏损持仓比例
        losing_positions = sum(1 for h in holdings if h.get('pnl_amount', 0) < 0)
        losing_ratio = (losing_positions / len(holdings)) * 100
        
        # 风险等级判断
        if portfolio_return < -20 or losing_ratio > 70:
            risk_level = 'HIGH'
            risk_score = 80
        elif portfolio_return < -10 or losing_ratio > 50:
            risk_level = 'MEDIUM'
            risk_score = 60
        elif portfolio_return < -5 or losing_ratio > 30:
            risk_level = 'MEDIUM'
            risk_score = 40
        else:
            risk_level = 'LOW'
            risk_score = 20
        
        return {
            'level': risk_level,
            'score': risk_score,
            'portfolio_return': portfolio_return,
            'losing_positions': losing_positions,
            'losing_ratio': losing_ratio,
            'description': f'投资组合收益率{portfolio_return:+.1f}%，{losing_positions}个亏损持仓'
        }
    
    def _calculate_overall_risk(self, concentration_risk: Dict, volatility_risk: Dict,
                               liquidity_risk: Dict, market_risk: Dict) -> Dict[str, Any]:
        """计算综合风险"""
        # 权重分配：集中度30%，波动率25%，流动性25%，市场风险20%
        weights = {
            'concentration': 0.30,
            'volatility': 0.25,
            'liquidity': 0.25,
            'market': 0.20
        }
        
        # 计算加权风险评分
        weighted_score = (
            concentration_risk['score'] * weights['concentration'] +
            volatility_risk['score'] * weights['volatility'] +
            liquidity_risk['score'] * weights['liquidity'] +
            market_risk['score'] * weights['market']
        )
        
        # 综合风险等级
        if weighted_score > 70:
            overall_level = 'HIGH'
        elif weighted_score > 50:
            overall_level = 'MEDIUM'
        else:
            overall_level = 'LOW'
        
        return {
            'level': overall_level,
            'score': weighted_score,
            'weights': weights,
            'description': f'综合风险评分{weighted_score:.1f}分，风险等级{overall_level}'
        }
    
    def _get_empty_risk_result(self) -> Dict[str, Any]:
        """获取空的风险评估结果"""
        empty_risk = {'level': 'LOW', 'score': 0, 'description': '无持仓数据'}
        return {
            'total_holdings': 0,
            'concentration_risk': empty_risk,
            'volatility_risk': empty_risk,
            'liquidity_risk': empty_risk,
            'market_risk': empty_risk,
            'overall_risk': empty_risk,
            'risk_summary': '无风险',
            'recommendations': []
        }
    
    def _generate_risk_summary(self, overall_risk: Dict[str, Any]) -> str:
        """生成风险摘要"""
        level = overall_risk.get('level', 'LOW')
        score = overall_risk.get('score', 0)
        
        if level == 'HIGH':
            return f"高风险投资组合（{score:.0f}分），建议立即调整持仓结构"
        elif level == 'MEDIUM':
            return f"中等风险投资组合（{score:.0f}分），建议适度优化配置"
        else:
            return f"低风险投资组合（{score:.0f}分），当前配置相对安全"
    
    def _generate_risk_recommendations(self, concentration_risk: Dict, volatility_risk: Dict,
                                     liquidity_risk: Dict) -> List[str]:
        """生成风险建议"""
        recommendations = []
        
        if concentration_risk.get('level') == 'HIGH':
            recommendations.append("建议分散投资，减少单一饰品持仓比例")
        
        if volatility_risk.get('level') == 'HIGH':
            recommendations.append("建议增加低波动率饰品，降低整体波动风险")
        
        if liquidity_risk.get('level') == 'HIGH':
            recommendations.append("建议增加高流动性饰品，提高变现能力")
        
        if not recommendations:
            recommendations.append("当前风险控制良好，建议继续保持")
        
        return recommendations

    def calculate_portfolio_metrics(self, user_id: str) -> Dict[str, Any]:
        """计算投资组合指标"""
        try:
            holdings = self.holding_service.get_holdings_with_current_prices(user_id)
            if not holdings:
                return self._get_empty_metrics_result()

            # 基础指标
            total_cost = sum(h.get('total_cost', 0) for h in holdings)
            total_current_value = sum(h.get('current_value', 0) for h in holdings)
            total_quantity = sum(h.get('total_quantity', 0) for h in holdings)

            # 盈亏指标
            total_pnl = total_current_value - total_cost
            portfolio_return = (total_pnl / total_cost * 100) if total_cost > 0 else 0

            # 持仓分布
            profitable_positions = sum(1 for h in holdings if h.get('pnl_amount', 0) > 0)
            losing_positions = sum(1 for h in holdings if h.get('pnl_amount', 0) < 0)

            # 平均指标
            avg_cost_per_item = total_cost / len(holdings) if holdings else 0
            avg_return_per_item = portfolio_return / len(holdings) if holdings else 0

            # 最大回撤计算（基于当前数据的简化版本）
            max_drawdown = self._calculate_max_drawdown(holdings)

            return {
                'success': True,
                'basic_metrics': {
                    'total_holdings': len(holdings),
                    'total_quantity': total_quantity,
                    'total_cost': total_cost,
                    'current_value': total_current_value,
                    'total_pnl': total_pnl,
                    'portfolio_return': portfolio_return
                },
                'position_metrics': {
                    'profitable_positions': profitable_positions,
                    'losing_positions': losing_positions,
                    'neutral_positions': len(holdings) - profitable_positions - losing_positions,
                    'win_rate': (profitable_positions / len(holdings) * 100) if holdings else 0
                },
                'average_metrics': {
                    'avg_cost_per_item': avg_cost_per_item,
                    'avg_return_per_item': avg_return_per_item,
                    'avg_holding_value': total_current_value / len(holdings) if holdings else 0
                },
                'risk_metrics': {
                    'max_drawdown': max_drawdown,
                    'largest_loss': min((h.get('pnl_amount', 0) for h in holdings), default=0),
                    'largest_gain': max((h.get('pnl_amount', 0) for h in holdings), default=0)
                }
            }

        except Exception as e:
            print(f"计算投资组合指标失败: {e}")
            return {
                'success': False,
                'error': str(e),
                **self._get_empty_metrics_result()
            }

    def analyze_holding_structure(self, user_id: str) -> Dict[str, Any]:
        """分析持仓结构"""
        try:
            holdings = self.holding_service.get_holdings_with_current_prices(user_id)
            if not holdings:
                return self._get_empty_structure_result()

            # 按价格区间分布
            price_distribution = self._analyze_price_distribution(holdings)

            # 按品类分布（基于饰品名称分析）
            category_distribution = self._analyze_category_distribution(holdings)

            # 按稀有度分布
            rarity_distribution = self._analyze_rarity_distribution(holdings)

            # 按盈亏状态分布
            pnl_distribution = self._analyze_pnl_distribution(holdings)

            return {
                'success': True,
                'total_holdings': len(holdings),
                'price_distribution': price_distribution,
                'category_distribution': category_distribution,
                'rarity_distribution': rarity_distribution,
                'pnl_distribution': pnl_distribution,
                'structure_summary': self._generate_structure_summary(
                    price_distribution, category_distribution, pnl_distribution
                )
            }

        except Exception as e:
            print(f"分析持仓结构失败: {e}")
            return {
                'success': False,
                'error': str(e),
                **self._get_empty_structure_result()
            }

    def _calculate_max_drawdown(self, holdings: List[Dict[str, Any]]) -> float:
        """计算最大回撤（简化版本）"""
        if not holdings:
            return 0

        # 基于当前盈亏状态的简化计算
        negative_returns = [h.get('pnl_percentage', 0) for h in holdings if h.get('pnl_percentage', 0) < 0]
        return min(negative_returns) if negative_returns else 0

    def _analyze_price_distribution(self, holdings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析价格分布"""
        price_ranges = {
            'low': {'range': '0-100', 'count': 0, 'value': 0},
            'medium': {'range': '100-500', 'count': 0, 'value': 0},
            'high': {'range': '500-2000', 'count': 0, 'value': 0},
            'premium': {'range': '2000+', 'count': 0, 'value': 0}
        }

        for holding in holdings:
            current_price = holding.get('current_price', holding.get('average_cost', 0))
            value = holding.get('current_value', holding.get('total_cost', 0))

            if current_price < 100:
                price_ranges['low']['count'] += 1
                price_ranges['low']['value'] += value
            elif current_price < 500:
                price_ranges['medium']['count'] += 1
                price_ranges['medium']['value'] += value
            elif current_price < 2000:
                price_ranges['high']['count'] += 1
                price_ranges['high']['value'] += value
            else:
                price_ranges['premium']['count'] += 1
                price_ranges['premium']['value'] += value

        # 计算比例
        total_value = sum(r['value'] for r in price_ranges.values())
        for range_data in price_ranges.values():
            range_data['percentage'] = (range_data['value'] / total_value * 100) if total_value > 0 else 0

        return price_ranges

    def _analyze_category_distribution(self, holdings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析品类分布（基于饰品名称）"""
        categories = defaultdict(lambda: {'count': 0, 'value': 0})

        for holding in holdings:
            item_name = holding.get('item_name', '')
            value = holding.get('current_value', holding.get('total_cost', 0))

            # 简单的品类识别（基于关键词）
            category = self._identify_category(item_name)
            categories[category]['count'] += 1
            categories[category]['value'] += value

        # 计算比例
        total_value = sum(cat['value'] for cat in categories.values())
        for category_data in categories.values():
            category_data['percentage'] = (category_data['value'] / total_value * 100) if total_value > 0 else 0

        return dict(categories)

    def _identify_category(self, item_name: str) -> str:
        """识别饰品品类"""
        item_name_lower = item_name.lower()

        if 'ak-47' in item_name_lower or 'ak47' in item_name_lower:
            return 'AK-47'
        elif 'm4a4' in item_name_lower or 'm4a1' in item_name_lower:
            return 'M4'
        elif 'awp' in item_name_lower:
            return 'AWP'
        elif 'glock' in item_name_lower:
            return 'Glock'
        elif 'usp' in item_name_lower:
            return 'USP'
        elif 'knife' in item_name_lower or '刀' in item_name:
            return '刀具'
        elif 'gloves' in item_name_lower or '手套' in item_name:
            return '手套'
        else:
            return '其他'

    def _analyze_rarity_distribution(self, holdings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析稀有度分布（基于存世量）"""
        rarity_levels = {
            'legendary': {'range': '0-1000', 'count': 0, 'value': 0},
            'rare': {'range': '1000-10000', 'count': 0, 'value': 0},
            'uncommon': {'range': '10000-100000', 'count': 0, 'value': 0},
            'common': {'range': '100000+', 'count': 0, 'value': 0}
        }

        for holding in holdings:
            # 尝试获取存世量信息
            try:
                item_id = holding.get('item_id')
                snapshot = self.market_snapshot_dao.get_latest_snapshot_by_item(item_id)
                survive_num = snapshot.get('survive_num', 50000) if snapshot else 50000
            except:
                survive_num = 50000  # 默认值

            value = holding.get('current_value', holding.get('total_cost', 0))

            if survive_num < 1000:
                rarity_levels['legendary']['count'] += 1
                rarity_levels['legendary']['value'] += value
            elif survive_num < 10000:
                rarity_levels['rare']['count'] += 1
                rarity_levels['rare']['value'] += value
            elif survive_num < 100000:
                rarity_levels['uncommon']['count'] += 1
                rarity_levels['uncommon']['value'] += value
            else:
                rarity_levels['common']['count'] += 1
                rarity_levels['common']['value'] += value

        # 计算比例
        total_value = sum(r['value'] for r in rarity_levels.values())
        for rarity_data in rarity_levels.values():
            rarity_data['percentage'] = (rarity_data['value'] / total_value * 100) if total_value > 0 else 0

        return rarity_levels

    def _analyze_pnl_distribution(self, holdings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析盈亏分布"""
        pnl_ranges = {
            'big_profit': {'range': '+20%以上', 'count': 0, 'value': 0},
            'profit': {'range': '0%到+20%', 'count': 0, 'value': 0},
            'loss': {'range': '0%到-20%', 'count': 0, 'value': 0},
            'big_loss': {'range': '-20%以下', 'count': 0, 'value': 0}
        }

        for holding in holdings:
            pnl_percentage = holding.get('pnl_percentage', 0)
            value = holding.get('current_value', holding.get('total_cost', 0))

            if pnl_percentage > 20:
                pnl_ranges['big_profit']['count'] += 1
                pnl_ranges['big_profit']['value'] += value
            elif pnl_percentage > 0:
                pnl_ranges['profit']['count'] += 1
                pnl_ranges['profit']['value'] += value
            elif pnl_percentage > -20:
                pnl_ranges['loss']['count'] += 1
                pnl_ranges['loss']['value'] += value
            else:
                pnl_ranges['big_loss']['count'] += 1
                pnl_ranges['big_loss']['value'] += value

        # 计算比例
        total_value = sum(r['value'] for r in pnl_ranges.values())
        for pnl_data in pnl_ranges.values():
            pnl_data['percentage'] = (pnl_data['value'] / total_value * 100) if total_value > 0 else 0

        return pnl_ranges

    def _get_empty_metrics_result(self) -> Dict[str, Any]:
        """获取空的指标结果"""
        return {
            'basic_metrics': {
                'total_holdings': 0, 'total_quantity': 0, 'total_cost': 0,
                'current_value': 0, 'total_pnl': 0, 'portfolio_return': 0
            },
            'position_metrics': {
                'profitable_positions': 0, 'losing_positions': 0,
                'neutral_positions': 0, 'win_rate': 0
            },
            'average_metrics': {
                'avg_cost_per_item': 0, 'avg_return_per_item': 0, 'avg_holding_value': 0
            },
            'risk_metrics': {
                'max_drawdown': 0, 'largest_loss': 0, 'largest_gain': 0
            }
        }

    def _get_empty_structure_result(self) -> Dict[str, Any]:
        """获取空的结构分析结果"""
        return {
            'total_holdings': 0,
            'price_distribution': {},
            'category_distribution': {},
            'rarity_distribution': {},
            'pnl_distribution': {},
            'structure_summary': '无持仓数据'
        }

    def _generate_structure_summary(self, price_dist: Dict, category_dist: Dict, pnl_dist: Dict) -> str:
        """生成结构摘要"""
        # 找出主要价格区间
        main_price_range = max(price_dist.items(), key=lambda x: x[1]['percentage'])[0] if price_dist else 'unknown'

        # 找出主要品类
        main_category = max(category_dist.items(), key=lambda x: x[1]['percentage'])[0] if category_dist else 'unknown'

        # 盈亏状态
        profit_ratio = pnl_dist.get('big_profit', {}).get('percentage', 0) + pnl_dist.get('profit', {}).get('percentage', 0)

        return f"主要持仓集中在{main_price_range}价格区间的{main_category}类饰品，盈利持仓占比{profit_ratio:.1f}%"
