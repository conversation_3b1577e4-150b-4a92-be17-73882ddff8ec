"""
收藏服务层

提供统一收藏表的业务逻辑。
"""

from typing import List, Dict, Any, Optional
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.favorite_dao import FavoriteDAO


class FavoriteService:
    """收藏服务类 - 统一收藏表"""
    
    def __init__(self):
        self.favorite_dao = FavoriteDAO()
        self.default_user_id = "default_user"  # 简化实现，使用默认用户
    
    def add_favorite(self, user_id: str, item_id: str,
                    item_name: str = None, notes: str = None) -> bool:
        """添加饰品收藏"""
        try:
            favorite = self.favorite_dao.add_favorite(
                user_id=user_id,
                item_id=item_id,
                item_name=item_name,
                notes=notes
            )
            return favorite is not None
        except Exception as e:
            print(f"添加收藏失败: {e}")
            return False
    
    def remove_favorite(self, user_id: str, item_id: str) -> bool:
        """取消饰品收藏"""
        try:
            return self.favorite_dao.remove_favorite(user_id, item_id)
        except Exception as e:
            print(f"取消收藏失败: {e}")
            return False
    
    def is_favorited(self, user_id: str, item_id: str) -> bool:
        """检查饰品是否已收藏"""
        try:
            return self.favorite_dao.is_favorited(user_id, item_id)
        except Exception as e:
            print(f"检查收藏状态失败: {e}")
            return False
    
    def get_user_favorites(self, user_id: str, limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """获取用户收藏列表"""
        try:
            return self.favorite_dao.get_user_favorites(user_id, limit, offset)
        except Exception as e:
            print(f"获取收藏列表失败: {e}")
            return []
    
    def get_favorite_count(self, user_id: str) -> int:
        """获取收藏数量"""
        try:
            return self.favorite_dao.get_favorite_count(user_id)
        except Exception as e:
            print(f"获取收藏数量失败: {e}")
            return 0

    def search_user_favorites(self, user_id: str, name_query: str = None,
                             limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """搜索用户收藏列表"""
        try:
            return self.favorite_dao.search_user_favorites(user_id, name_query, limit, offset)
        except Exception as e:
            print(f"搜索收藏列表失败: {e}")
            return []

    def count_search_favorites(self, user_id: str, name_query: str = None) -> int:
        """统计搜索收藏结果数量"""
        try:
            return self.favorite_dao.count_search_favorites(user_id, name_query)
        except Exception as e:
            print(f"统计搜索收藏结果失败: {e}")
            return 0

    def filter_user_favorites(self, user_id: str, query_params: Dict[str, Any] = None,
                             limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """根据查询条件过滤用户收藏的饰品 - 使用正确的SQL实现"""

        print(f"🚀 [统一查询] 使用统一查询方法查询收藏数据")
        print(f"   👤 user_id: {user_id}")
        print(f"   📋 query_params: {query_params}")
        print(f"   📄 limit: {limit}, offset: {offset}")

        try:
            # 使用统一的ItemDaoOptimized实现
            from src.cs2_investment.dao.item_dao_optimized import ItemDaoOptimized
            optimized_dao = ItemDaoOptimized()

            # 构建查询参数
            filter_params = {
                'user_id': user_id,  # 关键：传入user_id启用收藏模式
                'limit': limit,
                'offset': offset
            }

            if query_params:
                if query_params.get('name_query'):
                    filter_params['name_query'] = query_params['name_query']
                if query_params.get('item_types'):
                    filter_params['item_types'] = query_params['item_types']
                if query_params.get('qualities'):
                    filter_params['qualities'] = query_params['qualities']
                if query_params.get('rarities'):
                    filter_params['rarities'] = query_params['rarities']
                if query_params.get('exteriors'):
                    filter_params['exteriors'] = query_params['exteriors']
                if query_params.get('price_min') is not None:
                    filter_params['price_min'] = query_params['price_min']
                if query_params.get('price_max') is not None:
                    filter_params['price_max'] = query_params['price_max']
                if query_params.get('sell_count_min') is not None:
                    filter_params['sell_count_min'] = query_params['sell_count_min']
                if query_params.get('sell_count_max') is not None:
                    filter_params['sell_count_max'] = query_params['sell_count_max']
                if query_params.get('arbitrage_threshold') is not None:
                    filter_params['arbitrage_threshold'] = query_params['arbitrage_threshold']
                if query_params.get('arbitrage_card_price') is not None:
                    filter_params['arbitrage_card_price'] = query_params['arbitrage_card_price']
                if query_params.get('sort_by'):
                    filter_params['sort_by'] = query_params['sort_by']

            # 使用统一的查询方法
            results = optimized_dao.search_items_with_complete_data(**filter_params)

            print(f"✅ [统一查询] 查询完成，返回 {len(results)} 个收藏饰品")
            return results

        except Exception as e:
            print(f"❌ [统一查询失败] 降级到原有方法: {e}")
            return self._filter_user_favorites_fallback(user_id, query_params, limit, offset)

    def _filter_user_favorites_fallback(self, user_id: str, query_params: Dict[str, Any] = None,
                                       limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """降级方法：使用原有的低效查询"""

        print(f"⚠️ [降级] 使用原有的低效查询方法")

        try:
            # 先获取用户收藏的所有饰品ID
            favorite_item_ids = self.favorite_dao.get_user_favorite_item_ids(user_id)

            if not favorite_item_ids:
                return []

            # 使用ItemService根据收藏的饰品ID和查询条件进行过滤
            from src.cs2_investment.app.services.item_service import ItemService
            item_service = ItemService()

            # 构建ItemService支持的查询参数
            search_params = {
                'item_ids': favorite_item_ids,
                'limit': limit,
                'offset': offset
            }

            # 只传递ItemService支持的参数
            if query_params:
                if query_params.get('name_query'):
                    search_params['name_query'] = query_params['name_query']
                if query_params.get('item_types'):
                    search_params['item_types'] = query_params['item_types']
                if query_params.get('qualities'):
                    search_params['qualities'] = query_params['qualities']
                if query_params.get('rarities'):
                    search_params['rarities'] = query_params['rarities']
                if query_params.get('price_min') is not None:
                    search_params['price_min'] = query_params['price_min']
                if query_params.get('price_max') is not None:
                    search_params['price_max'] = query_params['price_max']
                if query_params.get('sell_count_min') is not None:
                    search_params['sell_count_min'] = query_params['sell_count_min']
                if query_params.get('sell_count_max') is not None:
                    search_params['sell_count_max'] = query_params['sell_count_max']
                if query_params.get('arbitrage_threshold') is not None:
                    search_params['arbitrage_threshold'] = query_params['arbitrage_threshold']
                if query_params.get('arbitrage_card_price') is not None:
                    search_params['arbitrage_card_price'] = query_params['arbitrage_card_price']
                if query_params.get('sort_by'):
                    search_params['sort_by'] = query_params['sort_by']

            # 调用ItemService的搜索方法
            filtered_items = item_service.search_items_with_prices(**search_params)

            # 为每个饰品添加收藏信息
            results = []
            for item in filtered_items:
                # 获取收藏信息
                favorite_info = self.favorite_dao.get_favorite_by_item_id(user_id, item.get('item_id'))
                if favorite_info:
                    # 合并饰品信息和收藏信息
                    merged_item = {
                        **item,
                        'favorite_id': favorite_info.get('id'),
                        'favorite_created_at': favorite_info.get('created_at'),
                        'favorite_notes': favorite_info.get('notes')
                    }
                    results.append(merged_item)

            return results

        except Exception as e:
            print(f"过滤收藏饰品失败: {e}")
            return []

    def count_filtered_favorites(self, user_id: str, query_params: Dict[str, Any] = None) -> int:
        """统计过滤后的收藏饰品数量 - 使用统一查询实现"""
        try:
            # 使用统一的ItemDaoOptimized实现
            from src.cs2_investment.dao.item_dao_optimized import ItemDaoOptimized
            optimized_dao = ItemDaoOptimized()

            # 构建查询参数
            filter_params = {
                'user_id': user_id,  # 关键：传入user_id启用收藏模式
                'limit': 10000  # 设置一个大的限制来获取总数
            }

            if query_params:
                if query_params.get('name_query'):
                    filter_params['name_query'] = query_params['name_query']
                if query_params.get('item_types'):
                    filter_params['item_types'] = query_params['item_types']
                if query_params.get('qualities'):
                    filter_params['qualities'] = query_params['qualities']
                if query_params.get('rarities'):
                    filter_params['rarities'] = query_params['rarities']
                if query_params.get('exteriors'):
                    filter_params['exteriors'] = query_params['exteriors']
                if query_params.get('price_min') is not None:
                    filter_params['price_min'] = query_params['price_min']
                if query_params.get('price_max') is not None:
                    filter_params['price_max'] = query_params['price_max']
                if query_params.get('sell_count_min') is not None:
                    filter_params['sell_count_min'] = query_params['sell_count_min']
                if query_params.get('sell_count_max') is not None:
                    filter_params['sell_count_max'] = query_params['sell_count_max']
                if query_params.get('arbitrage_threshold') is not None:
                    filter_params['arbitrage_threshold'] = query_params['arbitrage_threshold']

            # 使用统一的查询方法进行计数
            results = optimized_dao.search_items_with_complete_data(**filter_params)
            return len(results)

        except Exception as e:
            print(f"❌ [正确实现失败] 降级到原有计数方法: {e}")
            return self._count_filtered_favorites_fallback(user_id, query_params)

    def _count_filtered_favorites_fallback(self, user_id: str, query_params: Dict[str, Any] = None) -> int:
        """降级方法：使用原有的低效计数"""
        try:
            # 先获取用户收藏的所有饰品ID
            favorite_item_ids = self.favorite_dao.get_user_favorite_item_ids(user_id)

            if not favorite_item_ids:
                return 0

            # 使用ItemService根据收藏的饰品ID和查询条件进行计数
            from src.cs2_investment.app.services.item_service import ItemService
            item_service = ItemService()

            # 构建ItemService支持的查询参数
            search_params = {
                'item_ids': favorite_item_ids
            }

            # 只传递ItemService支持的参数
            if query_params:
                if query_params.get('name_query'):
                    search_params['name_query'] = query_params['name_query']
                if query_params.get('item_types'):
                    search_params['item_types'] = query_params['item_types']
                if query_params.get('qualities'):
                    search_params['qualities'] = query_params['qualities']
                if query_params.get('rarities'):
                    search_params['rarities'] = query_params['rarities']
                if query_params.get('arbitrage_threshold') is not None:
                    search_params['arbitrage_threshold'] = query_params['arbitrage_threshold']
                if query_params.get('price_min') is not None:
                    search_params['price_min'] = query_params['price_min']
                if query_params.get('price_max') is not None:
                    search_params['price_max'] = query_params['price_max']
                if query_params.get('sell_count_min') is not None:
                    search_params['sell_count_min'] = query_params['sell_count_min']
                if query_params.get('sell_count_max') is not None:
                    search_params['sell_count_max'] = query_params['sell_count_max']

            # 调用ItemService的计数方法
            return item_service.count_items(**search_params)

        except Exception as e:
            print(f"统计过滤收藏饰品数量失败: {e}")
            return 0
    
    def get_favorite_statistics(self, user_id: str = None) -> Dict[str, Any]:
        """获取收藏统计信息"""
        if user_id is None:
            user_id = self.default_user_id

        try:
            total_count = self.get_favorite_count(user_id)

            return {
                'total_count': total_count,
                'user_id': user_id
            }
        except Exception as e:
            print(f"获取收藏统计失败: {e}")
            return {
                'total_count': 0,
                'user_id': user_id
            }
    
    # 兼容性方法 - 为了保持与现有代码的兼容性
    def toggle_item_favorite(self, item_id: str, user_id: str = None) -> Dict[str, Any]:
        """切换饰品收藏状态（兼容性方法）"""
        if user_id is None:
            user_id = self.default_user_id

        try:
            # 检查当前收藏状态
            is_favorited = self.is_favorited(user_id, item_id)

            if is_favorited:
                # 取消收藏
                success = self.remove_favorite(user_id, item_id)
                return {
                    'success': success,
                    'action': 'removed',
                    'is_favorited': False,
                    'message': '已取消收藏' if success else '取消收藏失败'
                }
            else:
                # 添加收藏
                success = self.add_favorite(user_id, item_id)
                return {
                    'success': success,
                    'action': 'added',
                    'is_favorited': True,
                    'message': '已添加收藏' if success else '添加收藏失败'
                }
        except Exception as e:
            return {
                'success': False,
                'action': 'error',
                'is_favorited': False,
                'message': f'操作失败: {str(e)}'
            }

    def is_item_favorited(self, item_id: str, user_id: str = None) -> bool:
        """检查饰品是否已收藏（兼容性方法）"""
        if user_id is None:
            user_id = self.default_user_id
        return self.is_favorited(user_id, item_id)
