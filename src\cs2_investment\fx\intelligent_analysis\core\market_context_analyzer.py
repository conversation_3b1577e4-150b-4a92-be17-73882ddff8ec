"""
市场环境分析器
提供价格历史区间位置、成交量变化趋势、具体操作建议等关键信息
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import logging


class MarketContextAnalyzer:
    """市场环境分析器"""
    
    def __init__(self, item_id: str):
        self.item_id = item_id
        self.logger = logging.getLogger(__name__)
        
    def analyze_market_context(self, weekly_data: pd.DataFrame, daily_data: pd.DataFrame, 
                             hourly_data: pd.DataFrame, current_price: float) -> Dict[str, Any]:
        """分析市场环境，提供关键信息"""
        try:
            context = {}
            
            # 1. 价格历史区间位置分析
            context['price_position'] = self._analyze_price_position(
                weekly_data, daily_data, current_price
            )
            
            # 2. 成交量变化趋势分析
            context['volume_trend'] = self._analyze_volume_trend(
                daily_data, hourly_data
            )
            
            # 3. 具体操作建议
            context['operation_advice'] = self._generate_operation_advice(
                context['price_position'], context['volume_trend'], current_price
            )
            
            # 4. 关键价位信息
            context['key_levels'] = self._identify_key_price_levels(
                weekly_data, daily_data, current_price
            )
            
            return context
            
        except Exception as e:
            self.logger.error(f"市场环境分析失败: {e}")
            return {
                'price_position': {'error': str(e)},
                'volume_trend': {'error': str(e)},
                'operation_advice': {'error': str(e)},
                'key_levels': {'error': str(e)}
            }
    
    def _analyze_price_position(self, weekly_data: pd.DataFrame, daily_data: pd.DataFrame, 
                               current_price: float) -> Dict[str, Any]:
        """分析当前价格在历史区间中的位置"""
        try:
            # 计算不同时间周期的价格区间
            weekly_high = weekly_data['high'].max()
            weekly_low = weekly_data['low'].min()
            
            # 近3个月价格区间
            recent_daily = daily_data.tail(90) if len(daily_data) > 90 else daily_data
            recent_high = recent_daily['high'].max()
            recent_low = recent_daily['low'].min()
            
            # 计算价格位置百分比
            weekly_position = (current_price - weekly_low) / (weekly_high - weekly_low) * 100
            recent_position = (current_price - recent_low) / (recent_high - recent_low) * 100
            
            # 判断价格位置
            if weekly_position >= 80:
                position_desc = "历史高位区域"
            elif weekly_position >= 60:
                position_desc = "中高位区域"
            elif weekly_position >= 40:
                position_desc = "中位区域"
            elif weekly_position >= 20:
                position_desc = "中低位区域"
            else:
                position_desc = "历史低位区域"
            
            return {
                'current_price': current_price,
                'weekly_range': {'high': weekly_high, 'low': weekly_low},
                'recent_range': {'high': recent_high, 'low': recent_low},
                'weekly_position_pct': round(weekly_position, 1),
                'recent_position_pct': round(recent_position, 1),
                'position_description': position_desc,
                'analysis': f"当前价格{current_price}元，处于历史价格区间的{weekly_position:.1f}%位置，属于{position_desc}"
            }
            
        except Exception as e:
            self.logger.error(f"价格位置分析失败: {e}")
            return {'error': str(e)}
    
    def _analyze_volume_trend(self, daily_data: pd.DataFrame, hourly_data: pd.DataFrame) -> Dict[str, Any]:
        """分析成交量变化趋势"""
        try:
            # 计算日成交量趋势
            recent_daily = daily_data.tail(20) if len(daily_data) > 20 else daily_data
            daily_volume_avg = recent_daily['volume'].mean()
            latest_daily_volume = recent_daily['volume'].iloc[-1] if len(recent_daily) > 0 else 0
            
            # 计算小时成交量趋势
            recent_hourly = hourly_data.tail(24) if len(hourly_data) > 24 else hourly_data
            hourly_volume_avg = recent_hourly['volume'].mean()
            latest_hourly_volume = recent_hourly['volume'].iloc[-1] if len(recent_hourly) > 0 else 0
            
            # 成交量变化分析
            daily_volume_change = (latest_daily_volume / daily_volume_avg - 1) * 100 if daily_volume_avg > 0 else 0
            
            # 判断成交量趋势
            if daily_volume_change >= 50:
                volume_trend = "成交量大幅放大"
            elif daily_volume_change >= 20:
                volume_trend = "成交量明显增加"
            elif daily_volume_change >= -20:
                volume_trend = "成交量基本稳定"
            elif daily_volume_change >= -50:
                volume_trend = "成交量明显萎缩"
            else:
                volume_trend = "成交量大幅萎缩"
            
            return {
                'daily_volume_avg': round(daily_volume_avg, 2),
                'latest_daily_volume': latest_daily_volume,
                'daily_volume_change_pct': round(daily_volume_change, 1),
                'hourly_volume_avg': round(hourly_volume_avg, 2),
                'latest_hourly_volume': latest_hourly_volume,
                'trend_description': volume_trend,
                'analysis': f"近期日均成交量{daily_volume_avg:.0f}笔，今日成交{latest_daily_volume}笔，{volume_trend}"
            }
            
        except Exception as e:
            self.logger.error(f"成交量趋势分析失败: {e}")
            return {'error': str(e)}
    
    def _identify_key_price_levels(self, weekly_data: pd.DataFrame, daily_data: pd.DataFrame, 
                                  current_price: float) -> Dict[str, Any]:
        """识别关键价位"""
        try:
            # 计算支撑阻力位
            recent_highs = daily_data['high'].tail(20).nlargest(5)
            recent_lows = daily_data['low'].tail(20).nsmallest(5)
            
            # 找出最接近当前价格的支撑和阻力位
            resistance_levels = recent_highs[recent_highs > current_price].sort_values()
            support_levels = recent_lows[recent_lows < current_price].sort_values(ascending=False)
            
            nearest_resistance = resistance_levels.iloc[0] if len(resistance_levels) > 0 else None
            nearest_support = support_levels.iloc[0] if len(support_levels) > 0 else None
            
            return {
                'nearest_support': nearest_support,
                'nearest_resistance': nearest_resistance,
                'support_distance': round((current_price - nearest_support) / current_price * 100, 1) if nearest_support else None,
                'resistance_distance': round((nearest_resistance - current_price) / current_price * 100, 1) if nearest_resistance else None,
                'analysis': f"最近支撑位{nearest_support}元，最近阻力位{nearest_resistance}元" if nearest_support and nearest_resistance else "关键价位识别中"
            }
            
        except Exception as e:
            self.logger.error(f"关键价位识别失败: {e}")
            return {'error': str(e)}
    
    def _generate_operation_advice(self, price_position: Dict, volume_trend: Dict, 
                                  current_price: float) -> Dict[str, Any]:
        """生成具体操作建议"""
        try:
            advice_parts = []
            
            # 基于价格位置的建议
            weekly_position = price_position.get('weekly_position_pct', 50)
            if weekly_position >= 80:
                advice_parts.append("当前处于历史高位，建议谨慎操作")
                suggested_action = "减仓或观望"
            elif weekly_position <= 20:
                advice_parts.append("当前处于历史低位，可考虑逢低布局")
                suggested_action = "分批买入"
            else:
                advice_parts.append("当前处于中位区域，可根据短期信号操作")
                suggested_action = "灵活操作"
            
            # 基于成交量的建议
            volume_change = volume_trend.get('daily_volume_change_pct', 0)
            if volume_change >= 20:
                advice_parts.append("成交量放大，关注价格突破")
            elif volume_change <= -20:
                advice_parts.append("成交量萎缩，谨防假突破")
            
            # 具体价位建议
            price_advice = f"当前价格{current_price}元"
            
            return {
                'suggested_action': suggested_action,
                'price_advice': price_advice,
                'detailed_advice': "，".join(advice_parts),
                'operation_summary': f"{suggested_action}：{price_advice}，{advice_parts[0] if advice_parts else '基于当前市场状况操作'}"
            }
            
        except Exception as e:
            self.logger.error(f"操作建议生成失败: {e}")
            return {'error': str(e)}
