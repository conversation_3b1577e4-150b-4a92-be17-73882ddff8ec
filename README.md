# CS2饰品投资分析系统

一个基于数据驱动的CS2饰品投资分析和筛选系统，帮助投资者识别优质投资机会。

## 功能特性

- 🔍 **多维度筛选**：支持8种投资画像的智能筛选
- 📊 **数据分析**：基于历史数据的趋势分析和预测
- 🎯 **投资建议**：自动生成投资建议和风险评估
- 💼 **持仓管理**：完整的持仓记录、盈亏计算和风险分析
- 📈 **报告生成**：生成详细的投资分析报告
- 🔄 **实时更新**：支持数据的实时导入和更新
- ⏰ **定时任务**：自动定时爬取和检查数据完整性
- 🔄 **断点续爬**：支持意外中断后从断点继续爬取
- 🎮 **交互界面**：友好的菜单式操作界面

## 投资画像

1. **蓝筹核心资产** - 高流动性、稳定性的顶级资产
2. **高增长潜力资产** - 具有强劲上涨动力的资产
3. **稀缺价值资产** - 存世量稀少、供应减少的资产
4. **周期性/消耗品资产** - 具有消耗属性的周期性投资机会
5. **潜力新品** - 新上市具有潜力的饰品
6. **趋势反转资产** - 技术面反转的投资机会
7. **供应冲击恢复型** - 短期供应冲击后的恢复机会
8. **赛事周期资产** - Major赛事相关的周期性投资

## 快速开始

### 环境要求

- Python 3.8+
- MySQL 8.0+

### 安装

1. 克隆项目
```bash
git clone <repository-url>
cd cs2-investment-analysis
```

2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，填入数据库配置
```

5. 初始化数据库
```bash
python -m cs2_investment.main init-db
```

### 使用方法

#### 🚀 启动主程序

**方法1: 使用启动脚本（推荐）**
```bash
# Windows
start_steamdt.bat

# 或直接运行
python steamdt_main.py
```

**方法2: 命令行方式**
```bash
# 查看系统状态
python steamdt_main.py status

# 手动执行续爬
python steamdt_main.py scrape resume

# 手动执行全新爬取
python steamdt_main.py scrape new

# 启动定时调度器
python steamdt_main.py scheduler

# 执行检查任务
python steamdt_main.py check
```

#### ⏰ 定时任务功能

系统支持自动定时任务：
- **每日爬取**: 凌晨2:00自动执行数据爬取
- **每日检查**: 早上9:00检查数据完整性，如有缺失自动补爬

**启动定时调度器：**
```bash
# 方法1: 使用主程序
python steamdt_main.py scheduler

# 方法2: 直接启动调度器
python scheduler_manager.py start

# 方法3: 使用批处理文件
start_scheduler.bat
```

**测试定时任务：**
```bash
# 测试爬取任务
python scheduler_manager.py test-scraping

# 测试检查任务
python scheduler_manager.py test-check

# 查看定时任务状态
python scheduler_manager.py status
```

#### 🔄 续爬功能

系统支持断点续爬，意外中断后可以从断点继续：

```bash
# 续爬模式（默认，推荐）
python steamdt_scraper_final.py

# 全新爬取模式
python steamdt_scraper_final.py new

# 查看爬取状态
python steamdt_scraper_final.py status
```

#### 📊 数据分析

1. **数据导入**
```bash
python -m cs2_investment.main import-data
```

2. **运行筛选**
```bash
python -m cs2_investment.main run-screening
```

3. **生成报告**
```bash
python -m cs2_investment.main generate-report
```

#### 💼 持仓管理

系统提供完整的持仓管理功能，帮助用户跟踪投资表现：

**核心功能：**
- **持仓记录管理**：记录买入/卖出交易，自动计算持仓汇总
- **实时盈亏计算**：基于最新市场价格计算投资收益率
- **投资组合分析**：多维度的持仓结构和风险分析
- **交易记录管理**：完整的交易历史记录和统计

**风险分析：**
- **集中度风险**：分析单一持仓和前5大持仓占比
- **波动率风险**：基于历史价格波动评估组合风险
- **流动性风险**：评估持仓的变现能力和交易活跃度
- **市场风险**：基于整体盈亏状况评估市场表现

**结构分析：**
- **价格分布**：按价格区间分析持仓分布
- **品类分布**：按饰品类型（AK-47、M4、AWP等）分析
- **稀有度分布**：按存世量评估持仓稀有度结构
- **盈亏分布**：按收益率区间分析持仓表现

## 项目结构

```
cs2-investment-analysis/
├── src/cs2_investment/          # 主要源代码
│   ├── models/                  # 数据模型
│   ├── dao/                     # 数据访问层
│   ├── services/                # 业务逻辑层
│   ├── algorithms/              # 筛选算法
│   ├── utils/                   # 工具类
│   └── config/                  # 配置管理
├── tests/                       # 测试代码
├── data/                        # 数据文件
├── docs/                        # 文档
├── logs/                        # 日志文件
└── output/                      # 输出文件
```

## 开发指南

### 代码规范

- 使用 Black 进行代码格式化
- 使用 Flake8 进行代码检查
- 使用 MyPy 进行类型检查

### 运行测试

```bash
pytest tests/
```

### 生成覆盖率报告

```bash
pytest --cov=src/cs2_investment tests/
```

## 系统特性

### 🎯 智能筛选算法
- **蓝筹核心资产**: 识别高流动性、稳定性的顶级资产
- **高增长潜力资产**: 发现具有强劲上涨动力的投资机会
- **稀缺价值资产**: 挖掘存世量稀少、供应减少的珍稀饰品
- **供应冲击恢复型**: 捕捉短期供应冲击后的恢复机会

### 📊 数据分析能力
- 多维度市场数据分析
- 价格趋势预测
- 风险评估模型
- 投资建议生成

### 🔧 技术架构
- 模块化设计，易于扩展
- 完整的测试覆盖
- 详细的日志记录
- 高性能数据处理

## 文档

- [📖 API文档](docs/API文档.md) - 详细的API接口说明
- [🚀 部署指南](docs/部署指南.md) - 完整的部署和配置指南
- [👨‍💻 开发指南](docs/开发指南.md) - 开发环境设置和代码规范
- [🧮 算法设计文档](docs/算法设计文档.md) - 投资算法的详细设计
- [📋 数据库设计方案](docs/数据库设计方案.md) - 数据库结构设计
- [🔍 初步筛选](docs/初步筛选.md) - 投资筛选策略说明

## 快速体验

### 1. 一键部署
```bash
# 克隆项目
git clone <repository-url>
cd cs2-investment-analysis

# 运行部署脚本
./deploy.sh
```

### 2. Docker部署
```bash
# 构建镜像
docker build -t cs2-investment .

# 运行容器
docker run -d --name cs2-investment \
  -e DB_PASSWORD=120130 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/output:/app/output \
  cs2-investment
```

### 3. 示例数据
项目包含示例数据，可以快速体验系统功能：
```bash
# 导入示例数据
python -m cs2_investment.main import-data

# 运行筛选
python -m cs2_investment.main run-screening

# 查看结果
python -m cs2_investment.main show-results --limit 10
```

## 性能指标

- **数据处理能力**: 支持百万级饰品数据
- **筛选速度**: 单次筛选 < 30秒
- **内存占用**: < 2GB
- **并发支持**: 支持多用户同时使用

## 更新日志

### v1.0.0 (2025-01-22)
- ✨ 初始版本发布
- 🎯 实现4种核心投资筛选算法
- 📊 完整的数据导入和分析功能
- 📈 HTML/JSON/TXT格式报告生成
- 🧪 完整的测试覆盖
- 📚 详细的文档说明

## 路线图

### v1.1.0 (计划中)
- [ ] 添加更多投资算法
- [ ] 实现Web界面
- [ ] 支持实时数据更新
- [ ] 添加邮件通知功能

### v1.2.0 (计划中)
- [ ] 机器学习算法集成
- [ ] 移动端应用
- [ ] 社区功能
- [ ] API接口开放

## 社区

- 💬 [讨论区](https://github.com/cs2-investment/discussions) - 交流讨论
- 🐛 [问题反馈](https://github.com/cs2-investment/issues) - 报告问题
- 📧 [邮件列表](mailto:<EMAIL>) - 获取更新

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 贡献

我们欢迎所有形式的贡献！

### 如何贡献
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 贡献者
感谢所有为项目做出贡献的开发者！

## 支持

如果这个项目对你有帮助，请给我们一个 ⭐️！

---

**免责声明**: 本系统仅供学习和研究使用，投资有风险，决策需谨慎。
