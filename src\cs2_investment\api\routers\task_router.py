#!/usr/bin/env python3
"""
任务管理API路由

提供任务状态查询、结果获取和任务管理的API接口
"""

import logging
import sys
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import FileResponse, JSONResponse

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.api.models.responses import (
    TaskStatusResponse, 
    TaskResultResponse
)
from src.cs2_investment.dao.analysis_log_dao import analysis_log_dao
from src.cs2_investment.api.services.task_engine import task_engine

# 使用统一日志系统
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/tasks", tags=["任务管理"])


def calculate_progress(log_data: Dict[str, Any]) -> float:
    """计算任务进度百分比"""
    status = log_data.get('status', 'pending')

    if status == 'pending':
        return 0.0
    elif status == 'running':
        # 如果有开始时间，可以根据预估时间计算进度
        # 这里简单返回50%
        return 50.0
    elif status == 'success':
        return 100.0
    elif status == 'failed':
        return 0.0
    elif status == 'cancelled':
        return 0.0
    else:
        return 0.0


@router.get("/{task_id}/status",
            response_model=TaskStatusResponse,
            summary="查询任务状态",
            description="根据任务ID查询任务的执行状态和进度")
async def get_task_status(task_id: str):
    """
    查询任务状态
    
    - **task_id**: 任务ID
    
    返回任务的详细状态信息，包括进度、开始时间、结束时间等
    """
    try:
        # 查询任务记录
        log_data = analysis_log_dao.get_analysis_log_by_id(int(task_id))
        
        if not log_data:
            raise HTTPException(status_code=404, detail=f"任务不存在: {task_id}")
        
        # 计算进度
        progress = calculate_progress(log_data)
        
        logger.info(f"📊 查询任务状态: {task_id} - {log_data.get('status')}")
        
        return TaskStatusResponse(
            task_id=task_id,
            status=log_data.get('status', 'unknown'),
            progress=progress,
            start_time=log_data.get('start_time'),
            end_time=log_data.get('end_time'),
            duration_seconds=log_data.get('duration_seconds'),
            error_message=log_data.get('error_message'),
            result_path=log_data.get('result_path')
        )
        
    except ValueError:
        raise HTTPException(status_code=400, detail="任务ID格式不正确")
    except Exception as e:
        logger.error(f"查询任务状态失败: {task_id} - {e}")
        raise HTTPException(status_code=500, detail=f"查询任务状态失败: {str(e)}")


@router.get("/{task_id}/result",
            response_model=TaskResultResponse,
            summary="获取任务结果",
            description="获取已完成任务的分析结果和文件")
async def get_task_result(task_id: str):
    """
    获取任务结果
    
    - **task_id**: 任务ID
    
    返回任务的分析结果数据和相关文件列表
    """
    try:
        # 查询任务记录
        log_data = analysis_log_dao.get_analysis_log_by_id(int(task_id))
        
        if not log_data:
            raise HTTPException(status_code=404, detail=f"任务不存在: {task_id}")
        
        # 检查任务状态
        if log_data.get('status') != 'success':
            raise HTTPException(
                status_code=400, 
                detail=f"任务尚未完成或执行失败，当前状态: {log_data.get('status')}"
            )
        
        # 获取结果文件
        result_files = []
        result_path = log_data.get('result_path')
        if result_path and os.path.exists(result_path):
            result_files.append(result_path)
            
            # 查找相关的图表文件
            result_dir = os.path.dirname(result_path)
            if os.path.exists(result_dir):
                for file in os.listdir(result_dir):
                    if file.endswith(('.png', '.jpg', '.jpeg', '.html', '.pdf')):
                        file_path = os.path.join(result_dir, file)
                        if file_path not in result_files:
                            result_files.append(file_path)
        
        # 构建结果数据
        result_data = {
            "item_id": log_data.get('item_id'),
            "item_name": log_data.get('item_name'),
            "analysis_type": log_data.get('analysis_type'),
            "duration_seconds": log_data.get('duration_seconds'),
            "completed_at": log_data.get('end_time').isoformat() if log_data.get('end_time') else None
        }
        
        logger.info(f"📋 获取任务结果: {task_id} - {len(result_files)} 个文件")
        
        return TaskResultResponse(
            task_id=task_id,
            status=log_data.get('status'),
            result_data=result_data,
            result_files=result_files,
            analysis_summary=f"{log_data.get('item_name')} 的{log_data.get('analysis_type')}分析已完成"
        )
        
    except ValueError:
        raise HTTPException(status_code=400, detail="任务ID格式不正确")
    except Exception as e:
        logger.error(f"获取任务结果失败: {task_id} - {e}")
        raise HTTPException(status_code=500, detail=f"获取任务结果失败: {str(e)}")


@router.delete("/{task_id}",
               summary="取消任务",
               description="取消正在执行或等待中的任务")
async def cancel_task(task_id: str):
    """
    取消任务
    
    - **task_id**: 任务ID
    
    只能取消状态为pending或running的任务
    """
    try:
        # 查询任务记录
        log_data = analysis_log_dao.get_analysis_log_by_id(int(task_id))
        
        if not log_data:
            raise HTTPException(status_code=404, detail=f"任务不存在: {task_id}")
        
        # 检查任务状态
        current_status = log_data.get('status')
        if current_status not in ['pending', 'running']:
            raise HTTPException(
                status_code=400, 
                detail=f"无法取消任务，当前状态: {current_status}"
            )
        
        # 更新任务状态为取消
        success = analysis_log_dao.update_analysis_status(
            int(task_id),
            'cancelled',
            end_time=datetime.now(),
            error_message="任务已被用户取消"
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="取消任务失败")
        
        logger.info(f"❌ 任务已取消: {task_id}")
        
        return {
            "success": True,
            "message": f"任务 {task_id} 已成功取消",
            "task_id": task_id,
            "cancelled_at": datetime.now().isoformat()
        }
        
    except ValueError:
        raise HTTPException(status_code=400, detail="任务ID格式不正确")
    except Exception as e:
        logger.error(f"取消任务失败: {task_id} - {e}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.get("",
            summary="获取任务列表",
            description="获取任务列表，支持分页和状态过滤")
async def get_task_list(
    status: Optional[str] = Query(None, description="任务状态过滤"),
    analysis_type: Optional[str] = Query(None, description="分析类型过滤"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量")
):
    """
    获取任务列表
    
    - **status**: 任务状态过滤 (pending/running/success/failed/cancelled)
    - **analysis_type**: 分析类型过滤 (regular/realtime)
    - **page**: 页码 (从1开始)
    - **page_size**: 每页数量 (1-100)
    """
    try:
        # 获取任务列表
        tasks, total_count = analysis_log_dao.get_analysis_logs_paginated(
            status=status,
            analysis_type=analysis_type,
            page=page,
            page_size=page_size
        )
        
        # 计算分页信息
        total_pages = (total_count + page_size - 1) // page_size
        
        logger.info(f"📋 获取任务列表: 第{page}页，共{total_count}个任务")
        
        return {
            "success": True,
            "data": {
                "tasks": tasks,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_prev": page > 1
                }
            },
            "message": f"获取任务列表成功，共{total_count}个任务",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")


@router.get("/{task_id}/download",
            summary="下载任务结果文件",
            description="下载指定任务的结果文件")
async def download_task_file(
    task_id: str,
    file_type: str = Query("report", description="文件类型 (report/chart)")
):
    """
    下载任务结果文件
    
    - **task_id**: 任务ID
    - **file_type**: 文件类型 (report/chart)
    """
    try:
        # 查询任务记录
        log_data = analysis_log_dao.get_analysis_log_by_id(int(task_id))
        
        if not log_data:
            raise HTTPException(status_code=404, detail=f"任务不存在: {task_id}")
        
        # 检查任务状态
        if log_data.get('status') != 'success':
            raise HTTPException(
                status_code=400, 
                detail=f"任务尚未完成，无法下载文件"
            )
        
        # 获取文件路径
        result_path = log_data.get('result_path')
        if not result_path or not os.path.exists(result_path):
            raise HTTPException(status_code=404, detail="结果文件不存在")
        
        # 根据文件类型选择文件
        if file_type == "report":
            file_path = result_path
        elif file_type == "chart":
            # 查找图表文件
            result_dir = os.path.dirname(result_path)
            chart_files = [f for f in os.listdir(result_dir) if f.endswith('.png')]
            if not chart_files:
                raise HTTPException(status_code=404, detail="图表文件不存在")
            file_path = os.path.join(result_dir, chart_files[0])
        else:
            raise HTTPException(status_code=400, detail="不支持的文件类型")
        
        logger.info(f"📥 下载任务文件: {task_id} - {file_type}")
        
        return FileResponse(
            path=file_path,
            filename=os.path.basename(file_path),
            media_type='application/octet-stream'
        )
        
    except ValueError:
        raise HTTPException(status_code=400, detail="任务ID格式不正确")
    except Exception as e:
        logger.error(f"下载任务文件失败: {task_id} - {e}")
        raise HTTPException(status_code=500, detail=f"下载任务文件失败: {str(e)}")


@router.get("/engine/stats",
            summary="获取任务引擎统计信息",
            description="获取任务执行引擎的运行状态和统计信息")
async def get_engine_stats():
    """
    获取任务引擎统计信息

    返回任务引擎的运行状态、队列大小、工作线程数等信息
    """
    try:
        stats = task_engine.get_stats()
        running_tasks = task_engine.get_running_tasks()

        logger.info("📊 获取任务引擎统计信息")

        return {
            "success": True,
            "data": {
                "engine_stats": stats,
                "running_tasks": running_tasks
            },
            "message": "获取任务引擎统计信息成功",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取任务引擎统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.post("/engine/cancel/{task_id}",
             summary="通过引擎取消任务",
             description="通过任务引擎取消正在执行的任务")
async def cancel_task_via_engine(task_id: str):
    """
    通过任务引擎取消任务

    - **task_id**: 任务ID

    直接通过任务引擎取消任务，比数据库更新更及时
    """
    try:
        # 通过引擎取消任务
        task_engine.cancel_task(int(task_id))

        # 同时更新数据库状态
        success = analysis_log_dao.update_analysis_status(
            int(task_id),
            'cancelled',
            end_time=datetime.now(),
            error_message="任务已通过引擎取消"
        )

        if not success:
            logger.warning(f"数据库状态更新失败，但引擎已取消任务: {task_id}")

        logger.info(f"❌ 任务已通过引擎取消: {task_id}")

        return {
            "success": True,
            "message": f"任务 {task_id} 已通过引擎成功取消",
            "task_id": task_id,
            "cancelled_at": datetime.now().isoformat()
        }

    except ValueError:
        raise HTTPException(status_code=400, detail="任务ID格式不正确")
    except Exception as e:
        logger.error(f"通过引擎取消任务失败: {task_id} - {e}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")
