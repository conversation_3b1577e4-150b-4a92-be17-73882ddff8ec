"""
日志组件使用示例

演示如何正确使用统一日志组件进行日志记录
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.utils.logger import (
    get_logger,
    api_logger,
    scheduler_logger,
    scraper_logger,
    analysis_logger,
    database_logger,
    set_log_level,
    get_log_files
)


def basic_usage_example():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 推荐方式：使用模块名
    logger = get_logger(__name__)
    
    # 记录不同级别的日志
    logger.debug("🔍 这是调试信息")
    logger.info("📊 这是一般信息")
    logger.warning("⚠️ 这是警告信息")
    logger.error("❌ 这是错误信息")
    logger.critical("💥 这是严重错误")


def custom_logger_example():
    """自定义日志记录器示例"""
    print("\n=== 自定义日志记录器示例 ===")
    
    # 指定自定义日志文件
    custom_logger = get_logger("custom_module", log_file="custom.log")
    custom_logger.info("📝 这条日志会保存到 custom.log 文件")
    
    # 只输出到控制台
    console_only_logger = get_logger("console_only", file_output=False)
    console_only_logger.info("🖥️ 这条日志只在控制台显示")
    
    # 只输出到文件
    file_only_logger = get_logger("file_only", console_output=False)
    file_only_logger.info("💾 这条日志只保存到文件")


def predefined_loggers_example():
    """预定义日志记录器示例"""
    print("\n=== 预定义日志记录器示例 ===")
    
    # 使用预定义的日志记录器
    api_logger.info("🔗 API请求处理完成")
    scheduler_logger.info("⏰ 定时任务执行成功")
    scraper_logger.info("🕷️ 数据抓取完成")
    analysis_logger.info("📈 技术分析完成")
    database_logger.info("💾 数据库操作成功")


def business_scenario_examples():
    """业务场景示例"""
    print("\n=== 业务场景示例 ===")
    
    logger = get_logger(__name__)
    
    # 1. 任务执行示例
    task_name = "AK-47 传承 (久经沙场)"
    logger.info(f"🚀 开始分析: {task_name}")
    
    try:
        # 模拟任务执行
        import time
        start_time = time.time()
        time.sleep(0.1)  # 模拟处理时间
        
        duration = time.time() - start_time
        logger.info(f"✅ 分析成功: {task_name} (耗时: {duration:.2f}秒)")
        
    except Exception as e:
        logger.error(f"❌ 分析失败: {task_name} - {str(e)}")
    
    # 2. 进度报告示例
    total_items = 100
    for i in range(1, 6):  # 只演示前5个
        logger.info(f"📊 [{i}/{total_items}] 处理中: 饰品_{i}")
    
    # 3. 数据统计示例
    success_count = 95
    failed_count = 5
    logger.info(f"📈 任务完成统计: 成功 {success_count} 个, 失败 {failed_count} 个")
    
    # 4. 警告示例
    missing_rate = 0.15
    if missing_rate > 0.1:
        logger.warning(f"⚠️ 数据缺失率较高: {missing_rate:.1%}")
    
    # 5. 错误处理示例
    try:
        # 模拟可能出错的操作
        result = 10 / 0
    except ZeroDivisionError as e:
        logger.error(f"❌ 计算错误: {str(e)}")
    except Exception as e:
        logger.critical(f"💥 未知错误: {str(e)}", exc_info=True)


def log_level_example():
    """日志级别调整示例"""
    print("\n=== 日志级别调整示例 ===")
    
    logger = get_logger(__name__)
    
    # 当前级别下的日志
    logger.info("📊 当前日志级别下的信息")
    logger.debug("🔍 当前日志级别下的调试信息（可能不显示）")
    
    # 调整到DEBUG级别
    print("调整日志级别到 DEBUG...")
    set_log_level('DEBUG')
    
    logger.info("📊 调整后的信息日志")
    logger.debug("🔍 调整后的调试信息（现在应该显示）")
    
    # 调整回INFO级别
    print("调整日志级别回 INFO...")
    set_log_level('INFO')
    
    logger.info("📊 恢复INFO级别的信息日志")
    logger.debug("🔍 恢复INFO级别的调试信息（又不显示了）")


def log_files_info_example():
    """日志文件信息示例"""
    print("\n=== 日志文件信息示例 ===")
    
    # 获取所有日志文件信息
    log_files = get_log_files()
    
    print(f"📁 共找到 {len(log_files)} 个日志文件:")
    for file_info in log_files:
        size_mb = file_info['size'] / (1024 * 1024)
        print(f"  📄 {file_info['name']}")
        print(f"     大小: {size_mb:.2f} MB")
        print(f"     修改时间: {file_info['modified']}")
        print()


def exception_handling_example():
    """异常处理中的日志记录示例"""
    print("\n=== 异常处理日志示例 ===")
    
    logger = get_logger(__name__)
    
    def risky_operation(data):
        """模拟可能出错的操作"""
        if not data:
            raise ValueError("数据不能为空")
        if len(data) < 5:
            raise ValueError("数据长度不足")
        return f"处理了 {len(data)} 条数据"
    
    # 测试不同的异常情况
    test_cases = [
        [],           # 空数据
        [1, 2],       # 数据不足
        [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]  # 正常数据
    ]
    
    for i, test_data in enumerate(test_cases, 1):
        try:
            logger.info(f"🚀 开始测试用例 {i}: {len(test_data)} 条数据")
            result = risky_operation(test_data)
            logger.info(f"✅ 测试用例 {i} 成功: {result}")
            
        except ValueError as e:
            logger.error(f"❌ 测试用例 {i} 数据错误: {str(e)}")
            
        except Exception as e:
            logger.critical(f"💥 测试用例 {i} 未知错误: {str(e)}", exc_info=True)


def performance_logging_example():
    """性能监控日志示例"""
    print("\n=== 性能监控日志示例 ===")
    
    logger = get_logger(__name__)
    
    import time
    
    # 模拟不同耗时的操作
    operations = [
        ("快速操作", 0.1),
        ("中等操作", 0.5),
        ("慢速操作", 1.0)
    ]
    
    for op_name, duration in operations:
        start_time = time.time()
        logger.info(f"🚀 开始执行: {op_name}")
        
        # 模拟操作执行
        time.sleep(duration)
        
        elapsed = time.time() - start_time
        
        # 根据耗时选择不同的日志级别
        if elapsed < 0.5:
            logger.info(f"✅ {op_name} 完成 (耗时: {elapsed:.2f}秒)")
        elif elapsed < 1.0:
            logger.warning(f"⚠️ {op_name} 完成，耗时较长 (耗时: {elapsed:.2f}秒)")
        else:
            logger.error(f"❌ {op_name} 完成，耗时过长 (耗时: {elapsed:.2f}秒)")


def main():
    """运行所有示例"""
    print("🎯 日志组件使用示例")
    print("=" * 50)
    
    # 运行各种示例
    basic_usage_example()
    custom_logger_example()
    predefined_loggers_example()
    business_scenario_examples()
    log_level_example()
    log_files_info_example()
    exception_handling_example()
    performance_logging_example()
    
    print("\n" + "=" * 50)
    print("✅ 所有示例运行完成！")
    print("📁 请查看 logs/ 目录下的日志文件")


if __name__ == "__main__":
    main()
