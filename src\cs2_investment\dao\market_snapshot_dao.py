"""
市场快照数据访问对象

提供市场快照相关的数据库操作。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta, date
from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import desc, asc, and_, or_, func
from loguru import logger

from .base_dao import BaseDAO
from ..models.market_snapshot import MarketSnapshot
from ..models.item import Item
from ..config.database import get_db_session


class MarketSnapshotDAO(BaseDAO[MarketSnapshot]):
    """市场快照DAO"""
    
    def __init__(self):
        super().__init__(MarketSnapshot)
    
    def get_by_item_and_time(self, item_id: str, snapshot_time: datetime, 
                           data_source: str) -> Optional[MarketSnapshot]:
        """根据饰品ID、时间和数据源获取快照"""
        try:
            with get_db_session() as session:
                return session.query(MarketSnapshot).filter(
                    and_(
                        MarketSnapshot.item_id == item_id,
                        MarketSnapshot.snapshot_time == snapshot_time,
                        MarketSnapshot.data_source == data_source
                    )
                ).first()
        except SQLAlchemyError as e:
            self.logger.error(f"根据条件获取市场快照失败: {e}")
            raise
    
    def get_latest_snapshots(self, limit: int = 100) -> List[MarketSnapshot]:
        """获取最新的市场快照"""
        try:
            with get_db_session() as session:
                return session.query(MarketSnapshot)\
                    .options(joinedload(MarketSnapshot.item))\
                    .order_by(desc(MarketSnapshot.snapshot_time))\
                    .limit(limit).all()
        except SQLAlchemyError as e:
            self.logger.error(f"获取最新市场快照失败: {e}")
            raise
    
    def get_snapshots_by_item(self, item_id: str, days: int = 30) -> List[MarketSnapshot]:
        """获取指定饰品的历史快照"""
        try:
            with get_db_session() as session:
                start_time = datetime.now() - timedelta(days=days)
                return session.query(MarketSnapshot)\
                    .filter(
                        and_(
                            MarketSnapshot.item_id == item_id,
                            MarketSnapshot.snapshot_time >= start_time
                        )
                    )\
                    .order_by(desc(MarketSnapshot.snapshot_time)).all()
        except SQLAlchemyError as e:
            self.logger.error(f"获取饰品历史快照失败: {e}")
            raise
    
    def get_snapshots_by_source(self, data_source: str, days: int = 7) -> List[MarketSnapshot]:
        """获取指定数据源的快照"""
        try:
            with get_db_session() as session:
                start_time = datetime.now() - timedelta(days=days)
                return session.query(MarketSnapshot)\
                    .options(joinedload(MarketSnapshot.item))\
                    .filter(
                        and_(
                            MarketSnapshot.data_source == data_source,
                            MarketSnapshot.snapshot_time >= start_time
                        )
                    )\
                    .order_by(desc(MarketSnapshot.snapshot_time)).all()
        except SQLAlchemyError as e:
            self.logger.error(f"获取数据源快照失败: {e}")
            raise
    
    def get_price_trend(self, item_id: str, days: int = 30) -> List[Dict[str, Any]]:
        """获取价格趋势数据"""
        try:
            with get_db_session() as session:
                start_time = datetime.now() - timedelta(days=days)
                snapshots = session.query(MarketSnapshot)\
                    .filter(
                        and_(
                            MarketSnapshot.item_id == item_id,
                            MarketSnapshot.snapshot_time >= start_time,
                            MarketSnapshot.current_price.isnot(None)
                        )
                    )\
                    .order_by(asc(MarketSnapshot.snapshot_time)).all()
                
                return [
                    {
                        'snapshot_time': snapshot.snapshot_time,
                        'current_price': float(snapshot.current_price) if snapshot.current_price else None,
                        'diff_1d': float(snapshot.diff_1d) if snapshot.diff_1d else None,
                        'diff_7d': float(snapshot.diff_7d) if snapshot.diff_7d else None,
                        'volume': snapshot.trans_count_1d
                    }
                    for snapshot in snapshots
                ]
        except SQLAlchemyError as e:
            self.logger.error(f"获取价格趋势失败: {e}")
            raise
    
    def get_top_gainers(self, period: str = '7d', limit: int = 20) -> List[MarketSnapshot]:
        """获取涨幅榜"""
        try:
            with get_db_session() as session:
                # 根据周期选择字段
                price_change_field = {
                    '1d': MarketSnapshot.diff_1d,
                    '3d': MarketSnapshot.diff_3d,
                    '7d': MarketSnapshot.diff_7d,
                    '1m': MarketSnapshot.diff_1m,
                    '3m': MarketSnapshot.diff_3m
                }.get(period, MarketSnapshot.diff_7d)
                
                # 获取最新快照
                latest_time = session.query(func.max(MarketSnapshot.snapshot_time)).scalar()
                if not latest_time:
                    return []
                
                return session.query(MarketSnapshot)\
                    .options(joinedload(MarketSnapshot.item))\
                    .filter(
                        and_(
                            MarketSnapshot.snapshot_time >= latest_time - timedelta(hours=1),
                            price_change_field.isnot(None),
                            price_change_field > 0
                        )
                    )\
                    .order_by(desc(price_change_field))\
                    .limit(limit).all()
        except SQLAlchemyError as e:
            self.logger.error(f"获取涨幅榜失败: {e}")
            raise
    
    def get_top_losers(self, period: str = '7d', limit: int = 20) -> List[MarketSnapshot]:
        """获取跌幅榜"""
        try:
            with get_db_session() as session:
                # 根据周期选择字段
                price_change_field = {
                    '1d': MarketSnapshot.diff_1d,
                    '3d': MarketSnapshot.diff_3d,
                    '7d': MarketSnapshot.diff_7d,
                    '1m': MarketSnapshot.diff_1m,
                    '3m': MarketSnapshot.diff_3m
                }.get(period, MarketSnapshot.diff_7d)
                
                # 获取最新快照
                latest_time = session.query(func.max(MarketSnapshot.snapshot_time)).scalar()
                if not latest_time:
                    return []
                
                return session.query(MarketSnapshot)\
                    .options(joinedload(MarketSnapshot.item))\
                    .filter(
                        and_(
                            MarketSnapshot.snapshot_time >= latest_time - timedelta(hours=1),
                            price_change_field.isnot(None),
                            price_change_field < 0
                        )
                    )\
                    .order_by(asc(price_change_field))\
                    .limit(limit).all()
        except SQLAlchemyError as e:
            self.logger.error(f"获取跌幅榜失败: {e}")
            raise
    
    def get_high_volume_items(self, period: str = '1d', limit: int = 20) -> List[MarketSnapshot]:
        """获取高成交量饰品"""
        try:
            with get_db_session() as session:
                # 根据周期选择字段
                volume_field = {
                    '1d': MarketSnapshot.trans_count_1d,
                    '3d': MarketSnapshot.trans_count_3d,
                    '7d': MarketSnapshot.trans_count_7d,
                    '1m': MarketSnapshot.trans_count_1m,
                    '3m': MarketSnapshot.trans_count_3m
                }.get(period, MarketSnapshot.trans_count_1d)
                
                # 获取最新快照
                latest_time = session.query(func.max(MarketSnapshot.snapshot_time)).scalar()
                if not latest_time:
                    return []
                
                return session.query(MarketSnapshot)\
                    .options(joinedload(MarketSnapshot.item))\
                    .filter(
                        and_(
                            MarketSnapshot.snapshot_time >= latest_time - timedelta(hours=1),
                            volume_field.isnot(None),
                            volume_field > 0
                        )
                    )\
                    .order_by(desc(volume_field))\
                    .limit(limit).all()
        except SQLAlchemyError as e:
            self.logger.error(f"获取高成交量饰品失败: {e}")
            raise
    
    def batch_insert_snapshots(self, snapshots_data: List[dict]) -> List[MarketSnapshot]:
        """批量插入市场快照"""
        try:
            with get_db_session() as session:
                snapshots = []
                for data in snapshots_data:
                    snapshot = MarketSnapshot(**data)
                    snapshots.append(snapshot)
                
                session.add_all(snapshots)
                session.flush()
                
                for snapshot in snapshots:
                    session.refresh(snapshot)
                
                self.logger.info(f"批量插入市场快照: {len(snapshots)}条")
                return snapshots
        except SQLAlchemyError as e:
            self.logger.error(f"批量插入市场快照失败: {e}")
            raise
    
    def cleanup_old_snapshots(self, days: int = 180) -> int:
        """清理旧的快照数据"""
        try:
            with get_db_session() as session:
                cutoff_time = datetime.now() - timedelta(days=days)
                deleted_count = session.query(MarketSnapshot)\
                    .filter(MarketSnapshot.snapshot_time < cutoff_time)\
                    .delete()
                
                self.logger.info(f"清理旧快照数据: {deleted_count}条")
                return deleted_count
        except SQLAlchemyError as e:
            self.logger.error(f"清理旧快照数据失败: {e}")
            raise

    def count_recent_snapshots(self, data_source: str, since_time: datetime) -> int:
        """统计指定时间后的快照数量"""
        try:
            with get_db_session() as session:
                count = session.query(MarketSnapshot)\
                    .filter(
                        MarketSnapshot.data_source == data_source,
                        MarketSnapshot.snapshot_time >= since_time
                    ).count()

                return count

        except SQLAlchemyError as e:
            self.logger.error(f"统计最近快照数量失败: {e}")
            return 0

    def get_latest_snapshots_by_items(self, item_ids: List[str]) -> List[MarketSnapshot]:
        """获取指定饰品列表的最新快照"""
        try:
            with get_db_session() as session:
                # 获取每个饰品的最新快照时间
                subquery = session.query(
                    MarketSnapshot.item_id,
                    func.max(MarketSnapshot.snapshot_time).label('max_time')
                ).filter(
                    MarketSnapshot.item_id.in_(item_ids)
                ).group_by(MarketSnapshot.item_id).subquery()

                # 获取最新快照
                snapshots = session.query(MarketSnapshot)\
                    .join(
                        subquery,
                        and_(
                            MarketSnapshot.item_id == subquery.c.item_id,
                            MarketSnapshot.snapshot_time == subquery.c.max_time
                        )
                    ).all()

                # 强制加载所有属性
                for snapshot in snapshots:
                    _ = snapshot.item_id
                    _ = snapshot.current_price
                    _ = snapshot.diff_7d
                    _ = snapshot.diff_1m
                    _ = snapshot.trans_count_1m
                    _ = snapshot.trans_amount_1m
                    _ = snapshot.hot_rank
                    _ = snapshot.snapshot_time

                return snapshots
        except SQLAlchemyError as e:
            self.logger.error(f"获取指定饰品最新快照失败: {e}")
            raise

    def get_latest_snapshot_by_item(self, item_id: str) -> Optional[dict]:
        """获取指定饰品的最新快照"""
        try:
            with get_db_session() as session:
                snapshot = session.query(MarketSnapshot)\
                    .filter(MarketSnapshot.item_id == item_id)\
                    .order_by(desc(MarketSnapshot.snapshot_time))\
                    .first()

                if snapshot:
                    # 在会话内提取所有需要的数据
                    return {
                        'item_id': snapshot.item_id,
                        'current_price': snapshot.current_price,
                        'diff_1d': snapshot.diff_1d,
                        'diff_7d': snapshot.diff_7d,
                        'diff_1m': snapshot.diff_1m,
                        'trans_count_1d': snapshot.trans_count_1d,
                        'trans_count_7d': snapshot.trans_count_7d,
                        'trans_count_1m': snapshot.trans_count_1m,
                        'trans_amount_1d': snapshot.trans_amount_1d,
                        'trans_amount_7d': snapshot.trans_amount_7d,
                        'trans_amount_1m': snapshot.trans_amount_1m,
                        'hot_rank': snapshot.hot_rank,
                        'sell_nums': snapshot.sell_nums,
                        'survive_num': snapshot.survive_num,
                        'snapshot_time': snapshot.snapshot_time,
                        'price_change_24h': getattr(snapshot, 'price_change_24h', None),
                        'price_change_7d': getattr(snapshot, 'price_change_7d', None),
                        'volume_24h': getattr(snapshot, 'volume_24h', None)
                    }

                return None
        except SQLAlchemyError as e:
            self.logger.error(f"获取饰品最新快照失败: {e}")
            raise

    def get_latest_snapshot(self) -> Optional[MarketSnapshot]:
        """获取最新的快照记录"""
        try:
            with get_db_session() as session:
                return session.query(MarketSnapshot)\
                    .order_by(desc(MarketSnapshot.snapshot_time))\
                    .first()
        except SQLAlchemyError as e:
            self.logger.error(f"获取最新快照失败: {e}")
            raise

    def get_hot_ranking_items(self, limit: int = 500) -> List[MarketSnapshot]:
        """获取热度排行榜饰品（按hot_rank排序）"""
        try:
            with get_db_session() as session:
                # 获取最新快照时间
                latest_time = session.query(func.max(MarketSnapshot.snapshot_time)).scalar()
                if not latest_time:
                    self.logger.warning("没有找到任何快照数据")
                    return []

                # 查询热度排行榜数据（不使用joinedload避免session问题）
                snapshots = session.query(MarketSnapshot)\
                    .filter(
                        and_(
                            # 获取最新快照（1小时内的数据）
                            MarketSnapshot.snapshot_time >= latest_time - timedelta(hours=1),
                            # 筛选有效的热度排名数据
                            MarketSnapshot.hot_rank.isnot(None),
                            MarketSnapshot.hot_rank > 0
                        )
                    )\
                    .order_by(asc(MarketSnapshot.hot_rank))\
                    .limit(limit).all()

                # 创建新的对象列表，避免session依赖
                result_snapshots = []
                for snapshot in snapshots:
                    # 创建新的MarketSnapshot对象，复制所有属性
                    new_snapshot = MarketSnapshot()
                    new_snapshot.item_id = snapshot.item_id
                    new_snapshot.current_price = snapshot.current_price
                    new_snapshot.hot_rank = snapshot.hot_rank
                    new_snapshot.snapshot_time = snapshot.snapshot_time
                    new_snapshot.diff_1d = snapshot.diff_1d
                    new_snapshot.diff_7d = snapshot.diff_7d
                    new_snapshot.diff_1m = snapshot.diff_1m
                    new_snapshot.trans_count_1d = snapshot.trans_count_1d
                    new_snapshot.trans_count_7d = snapshot.trans_count_7d
                    new_snapshot.trans_count_1m = snapshot.trans_count_1m
                    new_snapshot.trans_amount_1d = snapshot.trans_amount_1d
                    new_snapshot.trans_amount_1m = snapshot.trans_amount_1m
                    result_snapshots.append(new_snapshot)

                self.logger.info(f"成功获取 {len(result_snapshots)} 个热度排行榜饰品")
                return result_snapshots

        except SQLAlchemyError as e:
            self.logger.error(f"获取热度排行榜失败: {e}")
            raise

    def get_hot_ranking_items_by_date(self, target_date: date, limit: int = 500) -> List[MarketSnapshot]:
        """获取指定日期的热度排行榜饰品（按hot_rank排序）"""
        try:
            with get_db_session() as session:
                # 获取指定日期的时间范围
                date_start = datetime.combine(target_date, datetime.min.time())
                date_end = datetime.combine(target_date, datetime.max.time())

                # 获取指定日期最新快照时间
                latest_time = session.query(func.max(MarketSnapshot.snapshot_time))\
                    .filter(
                        and_(
                            MarketSnapshot.snapshot_time >= date_start,
                            MarketSnapshot.snapshot_time <= date_end
                        )
                    ).scalar()

                if not latest_time:
                    self.logger.warning(f"没有找到指定日期({target_date})的快照数据")
                    return []

                # 查询指定日期最新快照的热度排行榜数据，包含关联的饰品信息
                from sqlalchemy.orm import joinedload
                snapshots = session.query(MarketSnapshot)\
                    .options(joinedload(MarketSnapshot.item))\
                    .filter(
                        and_(
                            MarketSnapshot.snapshot_time == latest_time,
                            MarketSnapshot.hot_rank.isnot(None),
                            MarketSnapshot.hot_rank > 0
                        )
                    )\
                    .order_by(MarketSnapshot.hot_rank.asc())\
                    .limit(limit)\
                    .all()

                # 创建新的对象列表，避免session依赖，同时保留item关系
                result_snapshots = []
                for snapshot in snapshots:
                    # 创建新的MarketSnapshot对象，复制所有属性
                    new_snapshot = MarketSnapshot()
                    new_snapshot.item_id = snapshot.item_id
                    new_snapshot.current_price = snapshot.current_price
                    new_snapshot.hot_rank = snapshot.hot_rank
                    new_snapshot.snapshot_time = snapshot.snapshot_time
                    new_snapshot.snapshot_date = snapshot.snapshot_date
                    new_snapshot.diff_1d = snapshot.diff_1d
                    new_snapshot.diff_7d = snapshot.diff_7d
                    new_snapshot.diff_1m = snapshot.diff_1m
                    new_snapshot.trans_count_1d = snapshot.trans_count_1d
                    new_snapshot.trans_count_7d = snapshot.trans_count_7d
                    new_snapshot.trans_count_1m = snapshot.trans_count_1m
                    new_snapshot.trans_amount_1d = snapshot.trans_amount_1d
                    new_snapshot.trans_amount_1m = snapshot.trans_amount_1m

                    # 复制关联的item信息
                    if snapshot.item:
                        from ..models.item import Item
                        new_item = Item()
                        new_item.item_id = snapshot.item.item_id
                        new_item.name = snapshot.item.name
                        new_item.market_hash_name = snapshot.item.market_hash_name
                        new_item.item_type = getattr(snapshot.item, 'item_type', None)
                        new_item.quality = getattr(snapshot.item, 'quality', None)
                        new_item.rarity = getattr(snapshot.item, 'rarity', None)
                        new_snapshot.item = new_item

                    result_snapshots.append(new_snapshot)

                self.logger.info(f"成功获取指定日期({target_date}) {len(result_snapshots)} 个热度排行榜饰品")
                return result_snapshots

        except SQLAlchemyError as e:
            self.logger.error(f"获取指定日期热度排行榜失败: {e}")
            raise

    def get_latest_ranking_time_by_date(self, target_date: date) -> Optional[datetime]:
        """获取指定日期的最新排行榜时间"""
        try:
            with get_db_session() as session:
                # 获取指定日期的时间范围
                date_start = datetime.combine(target_date, datetime.min.time())
                date_end = datetime.combine(target_date, datetime.max.time())

                # 查询指定日期最新快照时间
                latest_time = session.query(func.max(MarketSnapshot.snapshot_time))\
                    .filter(
                        and_(
                            MarketSnapshot.snapshot_time >= date_start,
                            MarketSnapshot.snapshot_time <= date_end,
                            MarketSnapshot.hot_rank.isnot(None),
                            MarketSnapshot.hot_rank > 0
                        )
                    ).scalar()

                return latest_time

        except SQLAlchemyError as e:
            self.logger.error(f"获取指定日期最新排行榜时间失败: {e}")
            raise

    def get_top_gainers_by_date(self, target_date: date, period: str = '7d', limit: int = 200) -> List[MarketSnapshot]:
        """获取指定日期的涨幅榜"""
        try:
            with get_db_session() as session:
                # 获取指定日期的时间范围
                date_start = datetime.combine(target_date, datetime.min.time())
                date_end = datetime.combine(target_date, datetime.max.time())

                # 根据周期选择字段
                price_change_field = {
                    '1d': MarketSnapshot.diff_1d,
                    '3d': MarketSnapshot.diff_3d,
                    '7d': MarketSnapshot.diff_7d,
                    '1m': MarketSnapshot.diff_1m,
                    '3m': MarketSnapshot.diff_3m
                }.get(period, MarketSnapshot.diff_7d)

                # 获取指定日期最新快照时间
                latest_time = session.query(func.max(MarketSnapshot.snapshot_time))\
                    .filter(
                        and_(
                            MarketSnapshot.snapshot_time >= date_start,
                            MarketSnapshot.snapshot_time <= date_end
                        )
                    ).scalar()

                if not latest_time:
                    self.logger.warning(f"没有找到指定日期({target_date})的快照数据")
                    return []

                # 查询涨幅榜数据
                snapshots = session.query(MarketSnapshot)\
                    .filter(
                        and_(
                            MarketSnapshot.snapshot_time == latest_time,
                            price_change_field.isnot(None),
                            price_change_field > 0
                        )
                    )\
                    .order_by(desc(price_change_field))\
                    .limit(limit)\
                    .all()

                # 创建新的对象列表，避免session依赖
                result_snapshots = []
                for snapshot in snapshots:
                    new_snapshot = MarketSnapshot()
                    new_snapshot.item_id = snapshot.item_id
                    new_snapshot.current_price = snapshot.current_price
                    new_snapshot.hot_rank = snapshot.hot_rank
                    new_snapshot.snapshot_time = snapshot.snapshot_time
                    new_snapshot.diff_1d = snapshot.diff_1d
                    new_snapshot.diff_7d = snapshot.diff_7d
                    new_snapshot.diff_1m = snapshot.diff_1m
                    new_snapshot.trans_count_1d = snapshot.trans_count_1d
                    new_snapshot.trans_count_7d = snapshot.trans_count_7d
                    new_snapshot.trans_count_1m = snapshot.trans_count_1m
                    new_snapshot.trans_amount_1d = snapshot.trans_amount_1d
                    new_snapshot.trans_amount_1m = snapshot.trans_amount_1m
                    result_snapshots.append(new_snapshot)

                self.logger.info(f"成功获取指定日期({target_date}) {period}涨幅榜 {len(result_snapshots)} 个饰品")
                return result_snapshots

        except SQLAlchemyError as e:
            self.logger.error(f"获取指定日期涨幅榜失败: {e}")
            raise

    def get_top_losers_by_date(self, target_date: date, period: str = '7d', limit: int = 200) -> List[MarketSnapshot]:
        """获取指定日期的跌幅榜"""
        try:
            with get_db_session() as session:
                # 获取指定日期的时间范围
                date_start = datetime.combine(target_date, datetime.min.time())
                date_end = datetime.combine(target_date, datetime.max.time())

                # 根据周期选择字段
                price_change_field = {
                    '1d': MarketSnapshot.diff_1d,
                    '3d': MarketSnapshot.diff_3d,
                    '7d': MarketSnapshot.diff_7d,
                    '1m': MarketSnapshot.diff_1m,
                    '3m': MarketSnapshot.diff_3m
                }.get(period, MarketSnapshot.diff_7d)

                # 获取指定日期最新快照时间
                latest_time = session.query(func.max(MarketSnapshot.snapshot_time))\
                    .filter(
                        and_(
                            MarketSnapshot.snapshot_time >= date_start,
                            MarketSnapshot.snapshot_time <= date_end
                        )
                    ).scalar()

                if not latest_time:
                    self.logger.warning(f"没有找到指定日期({target_date})的快照数据")
                    return []

                # 查询跌幅榜数据
                snapshots = session.query(MarketSnapshot)\
                    .filter(
                        and_(
                            MarketSnapshot.snapshot_time == latest_time,
                            price_change_field.isnot(None),
                            price_change_field < 0
                        )
                    )\
                    .order_by(asc(price_change_field))\
                    .limit(limit)\
                    .all()

                # 创建新的对象列表，避免session依赖
                result_snapshots = []
                for snapshot in snapshots:
                    new_snapshot = MarketSnapshot()
                    new_snapshot.item_id = snapshot.item_id
                    new_snapshot.current_price = snapshot.current_price
                    new_snapshot.hot_rank = snapshot.hot_rank
                    new_snapshot.snapshot_time = snapshot.snapshot_time
                    new_snapshot.diff_1d = snapshot.diff_1d
                    new_snapshot.diff_7d = snapshot.diff_7d
                    new_snapshot.diff_1m = snapshot.diff_1m
                    new_snapshot.trans_count_1d = snapshot.trans_count_1d
                    new_snapshot.trans_count_7d = snapshot.trans_count_7d
                    new_snapshot.trans_count_1m = snapshot.trans_count_1m
                    new_snapshot.trans_amount_1d = snapshot.trans_amount_1d
                    new_snapshot.trans_amount_1m = snapshot.trans_amount_1m
                    result_snapshots.append(new_snapshot)

                self.logger.info(f"成功获取指定日期({target_date}) {period}跌幅榜 {len(result_snapshots)} 个饰品")
                return result_snapshots

        except SQLAlchemyError as e:
            self.logger.error(f"获取指定日期跌幅榜失败: {e}")
            raise

    def get_high_volume_items_by_date(self, target_date: date, period: str = '1d', limit: int = 200) -> List[MarketSnapshot]:
        """获取指定日期的高成交量饰品"""
        try:
            with get_db_session() as session:
                # 获取指定日期的时间范围
                date_start = datetime.combine(target_date, datetime.min.time())
                date_end = datetime.combine(target_date, datetime.max.time())

                # 根据周期选择字段
                volume_field = {
                    '1d': MarketSnapshot.trans_count_1d,
                    '3d': MarketSnapshot.trans_count_3d,
                    '7d': MarketSnapshot.trans_count_7d,
                    '1m': MarketSnapshot.trans_count_1m,
                    '3m': MarketSnapshot.trans_count_3m
                }.get(period, MarketSnapshot.trans_count_1d)

                # 获取指定日期最新快照时间
                latest_time = session.query(func.max(MarketSnapshot.snapshot_time))\
                    .filter(
                        and_(
                            MarketSnapshot.snapshot_time >= date_start,
                            MarketSnapshot.snapshot_time <= date_end
                        )
                    ).scalar()

                if not latest_time:
                    self.logger.warning(f"没有找到指定日期({target_date})的快照数据")
                    return []

                # 查询高成交量数据
                snapshots = session.query(MarketSnapshot)\
                    .filter(
                        and_(
                            MarketSnapshot.snapshot_time == latest_time,
                            volume_field.isnot(None),
                            volume_field > 0
                        )
                    )\
                    .order_by(desc(volume_field))\
                    .limit(limit)\
                    .all()

                # 创建新的对象列表，避免session依赖
                result_snapshots = []
                for snapshot in snapshots:
                    new_snapshot = MarketSnapshot()
                    new_snapshot.item_id = snapshot.item_id
                    new_snapshot.current_price = snapshot.current_price
                    new_snapshot.hot_rank = snapshot.hot_rank
                    new_snapshot.snapshot_time = snapshot.snapshot_time
                    new_snapshot.diff_1d = snapshot.diff_1d
                    new_snapshot.diff_7d = snapshot.diff_7d
                    new_snapshot.diff_1m = snapshot.diff_1m
                    new_snapshot.trans_count_1d = snapshot.trans_count_1d
                    new_snapshot.trans_count_7d = snapshot.trans_count_7d
                    new_snapshot.trans_count_1m = snapshot.trans_count_1m
                    new_snapshot.trans_amount_1d = snapshot.trans_amount_1d
                    new_snapshot.trans_amount_1m = snapshot.trans_amount_1m
                    result_snapshots.append(new_snapshot)

                self.logger.info(f"成功获取指定日期({target_date}) {period}高成交量 {len(result_snapshots)} 个饰品")
                return result_snapshots

        except SQLAlchemyError as e:
            self.logger.error(f"获取指定日期高成交量饰品失败: {e}")
            raise

    def get_items_by_ranking_count(self, min_count: int = 2, limit: int = 100) -> List[MarketSnapshot]:
        """获取出现在多个排行榜中的饰品（复用现有查询模式）"""
        try:
            with get_db_session() as session:
                return session.query(MarketSnapshot)\
                    .filter(func.json_length(MarketSnapshot.ranking_info) >= min_count)\
                    .order_by(desc(func.json_length(MarketSnapshot.ranking_info)))\
                    .limit(limit).all()
        except SQLAlchemyError as e:
            self.logger.error(f"获取多排行榜饰品失败: {e}")
            raise

    def get_items_by_ranking_category(self, category: str, max_rank: int = 100, limit: int = 100) -> List[MarketSnapshot]:
        """按排行榜类别查询饰品"""
        try:
            with get_db_session() as session:
                return session.query(MarketSnapshot)\
                    .filter(
                        and_(
                            MarketSnapshot.ranking_info.isnot(None),
                            func.json_extract(MarketSnapshot.ranking_info, f'$.{category}') <= max_rank
                        )
                    )\
                    .order_by(func.json_extract(MarketSnapshot.ranking_info, f'$.{category}'))\
                    .limit(limit).all()
        except SQLAlchemyError as e:
            self.logger.error(f"按排行榜类别查询失败: {e}")
            raise

    def get_ranking_statistics(self, days: int = 7) -> Dict[str, Any]:
        """获取排行榜统计信息"""
        try:
            with get_db_session() as session:
                start_time = datetime.now() - timedelta(days=days)

                # 统计出现次数分布
                count_stats = session.query(
                    func.json_length(MarketSnapshot.ranking_info).label('count'),
                    func.count(MarketSnapshot.item_id).label('items')
                ).filter(
                    and_(
                        MarketSnapshot.snapshot_time >= start_time,
                        MarketSnapshot.ranking_info.isnot(None)
                    )
                ).group_by(func.json_length(MarketSnapshot.ranking_info)).all()

                return {
                    'count_distribution': {stat[0]: stat[1] for stat in count_stats},
                    'total_items_with_rankings': sum(stat[1] for stat in count_stats)
                }
        except SQLAlchemyError as e:
            self.logger.error(f"获取排行榜统计失败: {e}")
            raise


# 创建全局实例
market_snapshot_dao = MarketSnapshotDAO()
