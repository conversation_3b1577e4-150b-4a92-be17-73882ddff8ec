"""
定时器监控功能模块

提供定时器系统的监控、性能指标收集、错误统计和告警功能。
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque
import threading
import time
import json
from pathlib import Path

from ..utils.logger import get_timer_logger, log_timer_performance, log_timer_error


@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    operation: str
    start_time: datetime
    end_time: datetime
    duration: float
    success: bool
    error_message: str = ""
    context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ErrorRecord:
    """错误记录数据类"""
    timestamp: datetime
    operation: str
    error_type: str
    error_message: str
    context: Dict[str, Any] = field(default_factory=dict)


class TimerMonitor:
    """定时器监控器"""
    
    def __init__(self, timer_name: str, max_history: int = 1000):
        """
        初始化定时器监控器
        
        Args:
            timer_name: 定时器名称
            max_history: 最大历史记录数
        """
        self.timer_name = timer_name
        self.max_history = max_history
        self.logger = get_timer_logger(f"monitor.{timer_name}", timer_name)
        
        # 性能指标
        self.performance_history: deque = deque(maxlen=max_history)
        self.error_history: deque = deque(maxlen=max_history)
        
        # 统计数据
        self.stats = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'total_duration': 0.0,
            'average_duration': 0.0,
            'min_duration': float('inf'),
            'max_duration': 0.0,
            'last_operation_time': None,
            'uptime_start': datetime.now(),
            'error_rate': 0.0
        }
        
        # 实时指标（最近1小时）
        self.recent_metrics = {
            'operations_per_hour': 0,
            'recent_errors': 0,
            'recent_success_rate': 100.0,
            'recent_avg_duration': 0.0
        }
        
        # 线程锁
        self._lock = threading.Lock()
    
    def record_operation(self, operation: str, duration: float, success: bool = True, 
                        error_message: str = "", **context):
        """
        记录操作性能
        
        Args:
            operation: 操作名称
            duration: 执行时间（秒）
            success: 是否成功
            error_message: 错误信息（如果失败）
            **context: 额外上下文信息
        """
        with self._lock:
            now = datetime.now()
            
            # 创建性能指标记录
            metric = PerformanceMetric(
                operation=operation,
                start_time=now - timedelta(seconds=duration),
                end_time=now,
                duration=duration,
                success=success,
                error_message=error_message,
                context=context
            )
            
            self.performance_history.append(metric)
            
            # 更新统计数据
            self.stats['total_operations'] += 1
            self.stats['total_duration'] += duration
            self.stats['last_operation_time'] = now
            
            if success:
                self.stats['successful_operations'] += 1
            else:
                self.stats['failed_operations'] += 1
                
                # 记录错误
                error_record = ErrorRecord(
                    timestamp=now,
                    operation=operation,
                    error_type=context.get('error_type', 'Unknown'),
                    error_message=error_message,
                    context=context
                )
                self.error_history.append(error_record)
            
            # 更新平均值和极值
            self.stats['average_duration'] = self.stats['total_duration'] / self.stats['total_operations']
            self.stats['min_duration'] = min(self.stats['min_duration'], duration)
            self.stats['max_duration'] = max(self.stats['max_duration'], duration)
            
            # 计算错误率
            if self.stats['total_operations'] > 0:
                self.stats['error_rate'] = (self.stats['failed_operations'] / 
                                          self.stats['total_operations']) * 100
            
            # 更新实时指标
            self._update_recent_metrics()
            
            # 记录性能日志
            log_timer_performance(
                self.logger, operation, duration, success, **context
            )
            
            # 如果失败，记录错误日志
            if not success:
                log_timer_error(
                    self.logger, operation, 
                    Exception(error_message), **context
                )
    
    def _update_recent_metrics(self):
        """更新最近1小时的指标"""
        now = datetime.now()
        one_hour_ago = now - timedelta(hours=1)
        
        # 筛选最近1小时的记录
        recent_operations = [
            metric for metric in self.performance_history
            if metric.end_time >= one_hour_ago
        ]
        
        if recent_operations:
            self.recent_metrics['operations_per_hour'] = len(recent_operations)
            
            successful_recent = [op for op in recent_operations if op.success]
            failed_recent = [op for op in recent_operations if not op.success]
            
            self.recent_metrics['recent_errors'] = len(failed_recent)
            self.recent_metrics['recent_success_rate'] = (
                len(successful_recent) / len(recent_operations) * 100
            )
            
            if recent_operations:
                total_duration = sum(op.duration for op in recent_operations)
                self.recent_metrics['recent_avg_duration'] = total_duration / len(recent_operations)
        else:
            self.recent_metrics = {
                'operations_per_hour': 0,
                'recent_errors': 0,
                'recent_success_rate': 100.0,
                'recent_avg_duration': 0.0
            }
    
    def get_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        with self._lock:
            uptime = datetime.now() - self.stats['uptime_start']
            
            return {
                'timer_name': self.timer_name,
                'uptime_seconds': uptime.total_seconds(),
                'uptime_formatted': str(uptime),
                'statistics': self.stats.copy(),
                'recent_metrics': self.recent_metrics.copy(),
                'health_score': self._calculate_health_score(),
                'last_errors': [
                    {
                        'timestamp': error.timestamp.isoformat(),
                        'operation': error.operation,
                        'error_type': error.error_type,
                        'message': error.error_message
                    }
                    for error in list(self.error_history)[-5:]  # 最近5个错误
                ]
            }
    
    def _calculate_health_score(self) -> float:
        """计算健康分数（0-100）"""
        score = 100.0
        
        # 基于错误率扣分
        if self.stats['error_rate'] > 0:
            score -= min(self.stats['error_rate'] * 2, 50)  # 错误率每1%扣2分，最多扣50分
        
        # 基于最近错误数扣分
        if self.recent_metrics['recent_errors'] > 0:
            score -= min(self.recent_metrics['recent_errors'] * 5, 30)  # 每个错误扣5分，最多扣30分
        
        # 基于成功率加分/扣分
        recent_success_rate = self.recent_metrics['recent_success_rate']
        if recent_success_rate < 90:
            score -= (90 - recent_success_rate) * 2  # 成功率低于90%时扣分
        
        return max(0.0, score)
    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取性能摘要"""
        with self._lock:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # 筛选指定时间范围内的记录
            filtered_metrics = [
                metric for metric in self.performance_history
                if metric.end_time >= cutoff_time
            ]
            
            if not filtered_metrics:
                return {
                    'period_hours': hours,
                    'total_operations': 0,
                    'success_rate': 100.0,
                    'average_duration': 0.0,
                    'operations_by_type': {}
                }
            
            # 按操作类型分组统计
            operations_by_type = defaultdict(list)
            for metric in filtered_metrics:
                operations_by_type[metric.operation].append(metric)
            
            type_stats = {}
            for op_type, metrics in operations_by_type.items():
                successful = [m for m in metrics if m.success]
                type_stats[op_type] = {
                    'count': len(metrics),
                    'success_count': len(successful),
                    'success_rate': len(successful) / len(metrics) * 100,
                    'avg_duration': sum(m.duration for m in metrics) / len(metrics)
                }
            
            successful_ops = [m for m in filtered_metrics if m.success]
            
            return {
                'period_hours': hours,
                'total_operations': len(filtered_metrics),
                'successful_operations': len(successful_ops),
                'success_rate': len(successful_ops) / len(filtered_metrics) * 100,
                'average_duration': sum(m.duration for m in filtered_metrics) / len(filtered_metrics),
                'operations_by_type': type_stats
            }
    
    def export_metrics(self, file_path: Path = None) -> str:
        """导出监控指标到JSON文件"""
        with self._lock:
            # 复制统计数据并处理datetime对象
            stats_copy = self.stats.copy()
            if 'last_operation_time' in stats_copy and stats_copy['last_operation_time']:
                stats_copy['last_operation_time'] = stats_copy['last_operation_time'].isoformat()
            if 'uptime_start' in stats_copy and stats_copy['uptime_start']:
                stats_copy['uptime_start'] = stats_copy['uptime_start'].isoformat()

            export_data = {
                'timer_name': self.timer_name,
                'export_time': datetime.now().isoformat(),
                'statistics': stats_copy,
                'recent_metrics': self.recent_metrics.copy(),
                'performance_history': [
                    {
                        'operation': metric.operation,
                        'start_time': metric.start_time.isoformat(),
                        'end_time': metric.end_time.isoformat(),
                        'duration': metric.duration,
                        'success': metric.success,
                        'error_message': metric.error_message,
                        'context': metric.context
                    }
                    for metric in self.performance_history
                ],
                'error_history': [
                    {
                        'timestamp': error.timestamp.isoformat(),
                        'operation': error.operation,
                        'error_type': error.error_type,
                        'error_message': error.error_message,
                        'context': error.context
                    }
                    for error in self.error_history
                ]
            }
            
            json_data = json.dumps(export_data, indent=2, ensure_ascii=False)
            
            if file_path:
                file_path.write_text(json_data, encoding='utf-8')
                self.logger.info(f"监控指标已导出到: {file_path}")
            
            return json_data


class MonitoringManager:
    """监控管理器 - 管理多个定时器的监控"""
    
    def __init__(self):
        self.monitors: Dict[str, TimerMonitor] = {}
        self.logger = get_timer_logger("monitoring_manager")
        self._lock = threading.Lock()
    
    def get_monitor(self, timer_name: str) -> TimerMonitor:
        """获取或创建定时器监控器"""
        with self._lock:
            if timer_name not in self.monitors:
                self.monitors[timer_name] = TimerMonitor(timer_name)
                self.logger.info(f"创建定时器监控器: {timer_name}")
            
            return self.monitors[timer_name]
    
    def get_all_status(self) -> Dict[str, Any]:
        """获取所有定时器的监控状态"""
        with self._lock:
            return {
                'monitoring_manager': {
                    'total_timers': len(self.monitors),
                    'active_timers': list(self.monitors.keys())
                },
                'timers': {
                    name: monitor.get_status()
                    for name, monitor in self.monitors.items()
                }
            }
    
    def cleanup_old_data(self, days: int = 7):
        """清理旧的监控数据"""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        for monitor in self.monitors.values():
            with monitor._lock:
                # 清理旧的性能记录
                monitor.performance_history = deque(
                    [m for m in monitor.performance_history if m.end_time >= cutoff_time],
                    maxlen=monitor.max_history
                )
                
                # 清理旧的错误记录
                monitor.error_history = deque(
                    [e for e in monitor.error_history if e.timestamp >= cutoff_time],
                    maxlen=monitor.max_history
                )
        
        self.logger.info(f"已清理 {days} 天前的监控数据")


# 全局监控管理器实例
_monitoring_manager = MonitoringManager()


def get_timer_monitor(timer_name: str) -> TimerMonitor:
    """获取定时器监控器的便捷函数"""
    return _monitoring_manager.get_monitor(timer_name)


def get_monitoring_status() -> Dict[str, Any]:
    """获取所有监控状态的便捷函数"""
    return _monitoring_manager.get_all_status()
