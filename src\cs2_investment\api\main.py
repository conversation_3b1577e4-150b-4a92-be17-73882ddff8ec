#!/usr/bin/env python3
"""
SteamDT抓取服务FastAPI主应用

独立的FastAPI服务端，提供RESTful API接口包装现有的抓取系统。
解决Playwright在Streamlit环境中的兼容性问题。
"""

import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Request
from pydantic import BaseModel
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
import uvicorn

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 使用统一日志系统
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)

# 导入路由
from src.cs2_investment.api.routers.analysis_router import router as analysis_router
from src.cs2_investment.api.routers.task_router import router as task_router

# 导入任务引擎
from src.cs2_investment.api.services.task_engine import task_engine



# 导入监控功能
from src.cs2_investment.api.monitoring import get_monitoring_status

# 全局变量
scheduler_manager = None
realtime_scheduler_thread = None
investment_analysis_scheduler = None
investment_scheduler_thread = None
unified_scheduler_manager = None  # 统一定时器管理器
integrated_scheduler = None  # 集成调度器（包含Steam监控服务）

# 使用统一日志组件
from src.cs2_investment.utils.logger import get_logger

logger = get_logger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="SteamDT抓取服务",
    description="独立的抓取服务端，提供RESTful API接口包装现有的抓取系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:8504",  # Streamlit前端
        "http://127.0.0.1:8504",
        "http://localhost:3000",  # 开发环境
        "http://127.0.0.1:3000"
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(analysis_router)
app.include_router(task_router)

# Pydantic模型定义
class ConfigUpdateRequest(BaseModel):
    """配置更新请求模型"""
    config_updates: Dict[str, Any]

# 统一响应格式
class APIResponse:
    """统一API响应格式"""
    
    @staticmethod
    def success(data: Any = None, message: str = "操作成功") -> Dict[str, Any]:
        """成功响应"""
        return {
            "success": True,
            "data": data,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "error": None
        }
    
    @staticmethod
    def error(message: str, error_code: str = "UNKNOWN_ERROR", data: Any = None) -> Dict[str, Any]:
        """错误响应"""
        return {
            "success": False,
            "data": data,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "error": {
                "code": error_code,
                "message": message
            }
        }


# 全局异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content=APIResponse.error(
            message=exc.detail,
            error_code=f"HTTP_{exc.status_code}"
        )
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理器"""
    logger.error(f"请求验证失败: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content=APIResponse.error(
            message="请求参数验证失败",
            error_code="VALIDATION_ERROR",
            data={"errors": exc.errors()}
        )
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {type(exc).__name__} - {str(exc)}")
    return JSONResponse(
        status_code=500,
        content=APIResponse.error(
            message="服务器内部错误",
            error_code="INTERNAL_SERVER_ERROR"
        )
    )


# 健康检查接口
@app.get("/health", summary="健康检查", tags=["系统"])
async def health_check():
    """
    健康检查接口
    
    返回服务状态和基本信息
    """
    try:
        # 检查数据库连接
        db_status = "connected"
        try:
            from src.cs2_investment.config.database import create_database_engine
            engine = create_database_engine()
            with engine.connect() as conn:
                conn.execute("SELECT 1")
            db_status = "connected"
        except Exception as e:
            logger.warning(f"数据库连接检查失败: {e}")
            db_status = "disconnected"
        
        return APIResponse.success({
            "service": "SteamDT抓取服务",
            "status": "healthy",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "database": db_status,
            "port": 8000
        })
    
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail="健康检查失败")


# 服务信息接口
@app.get("/info", summary="服务信息", tags=["系统"])
async def service_info():
    """
    获取服务详细信息
    """
    return APIResponse.success({
        "name": "SteamDT抓取服务",
        "description": "独立的抓取服务端，提供RESTful API接口包装现有的抓取系统",
        "version": "1.0.0",
        "author": "SteamDT Team",
        "endpoints": {
            "health": "/health",
            "info": "/info",
            "docs": "/docs",
            "redoc": "/redoc"
        },
        "features": [
            "多种抓取方式支持（Playwright/API）",
            "智能抓取器切换和回退",
            "异步任务处理",
            "任务状态管理",
            "批量分析支持",
            "完整错误处理",
            "抓取器健康监控"
        ]
    })


# 调度器状态接口
@app.get("/scheduler/status", summary="调度器状态", tags=["系统"])
async def scheduler_status():
    """
    获取所有调度器的状态信息
    """
    try:
        status = {
            "timestamp": datetime.now().isoformat(),
            "schedulers": {}
        }

        # 排行榜抓取调度器状态
        try:
            global scheduler_manager
            if scheduler_manager:
                ranking_status = scheduler_manager.get_status()
                status["schedulers"]["ranking_scraper"] = {
                    "name": "排行榜抓取调度器",
                    "status": "running" if ranking_status.get("is_running") else "stopped",
                    "is_scraping": ranking_status.get("is_scraping_running", False),
                    "daily_completed": ranking_status.get("daily_scraping_completed", False),
                    "last_scraping_date": ranking_status.get("last_scraping_date"),
                    "scheduled_jobs": ranking_status.get("scheduled_jobs", 0),
                    "next_run": ranking_status.get("next_run")
                }
            else:
                status["schedulers"]["ranking_scraper"] = {
                    "name": "排行榜抓取调度器",
                    "status": "not_initialized"
                }
        except Exception as e:
            status["schedulers"]["ranking_scraper"] = {
                "name": "排行榜抓取调度器",
                "status": "error",
                "error": str(e)
            }

        # 仅实时监控调度器状态
        try:
            from src.cs2_investment.scheduler.realtime_only_scheduler import realtime_only_scheduler
            realtime_status = realtime_only_scheduler.get_status()
            status["schedulers"]["realtime_monitor"] = {
                "name": "仅实时监控调度器",
                "status": "running" if realtime_status.get("is_running") else "stopped",
                "realtime_interval_hours": realtime_status.get("realtime_interval_hours"),
                "item_delay_seconds": realtime_status.get("item_delay_seconds"),
                "max_retry_count": realtime_status.get("max_retry_count"),
                "task_status": realtime_status.get("task_status", {}),
                "note": "已修复Playwright线程安全问题和弹窗处理"
            }
        except Exception as e:
            status["schedulers"]["realtime_monitor"] = {
                "name": "仅实时监控调度器",
                "status": "error",
                "error": str(e)
            }

        # 智能投资分析调度器状态
        try:
            global investment_analysis_scheduler
            if investment_analysis_scheduler:
                investment_status = investment_analysis_scheduler.get_status()
                status["schedulers"]["investment_analysis"] = {
                    "name": "智能投资分析调度器",
                    "status": "running" if investment_status.get("is_running") else "stopped",
                    "last_processed_time": investment_status.get("last_processed_time"),
                    "current_batch": investment_status.get("current_batch"),
                    "total_batches": investment_status.get("total_batches"),
                    "config": investment_status.get("config", {}),
                    "stats": investment_status.get("stats", {}),
                    "note": "自动化投资分析流程：排行榜数据监听 → 常规分析 → 投资筛选算法"
                }
            else:
                status["schedulers"]["investment_analysis"] = {
                    "name": "智能投资分析调度器",
                    "status": "not_initialized"
                }
        except Exception as e:
            status["schedulers"]["investment_analysis"] = {
                "name": "智能投资分析调度器",
                "status": "error",
                "error": str(e)
            }

        # 添加抓取器状态信息
        try:
            from src.cs2_investment.scraper.scraper_factory import get_scraper_factory
            from src.cs2_investment.config.timer_config import get_timer_config

            factory = get_scraper_factory()
            config = get_timer_config()

            # 获取抓取器工厂状态
            factory_info = factory.get_factory_info()
            health_status = factory.get_health_status()

            status["scraper_factory"] = {
                "factory_type": factory_info["factory_type"],
                "singleton_instance": factory_info["singleton_instance"],
                "fallback_enabled": factory_info["fallback_enabled"],
                "active_scrapers": factory_info["active_scrapers"],
                "config_method": factory_info["config_method"],
                "health_status": health_status,
                "scraping_config": {
                    "method": config.scraping.scraping_method,
                    "api_fallback_enabled": config.scraping.api_fallback_enabled,
                    "data_validation_enabled": config.scraping.data_validation_enabled,
                    "api_timeout": config.scraping.api_timeout,
                    "playwright_timeout": config.scraping.playwright_timeout,
                    "max_retry_attempts": config.scraping.max_retry_attempts
                }
            }
        except Exception as e:
            status["scraper_factory"] = {
                "status": "error",
                "error": str(e)
            }

        return APIResponse.success(status)

    except Exception as e:
        logger.error(f"获取调度器状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取调度器状态失败")


# 定时器监控接口
@app.get("/monitoring/status", summary="定时器监控状态", tags=["监控"])
async def get_monitoring_status_api():
    """
    获取所有定时器的监控状态

    返回性能指标、错误统计、健康状态等监控数据
    """
    try:
        monitoring_data = get_monitoring_status()
        return APIResponse.success(monitoring_data)
    except Exception as e:
        logger.error(f"获取监控状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取监控状态失败")


@app.get("/monitoring/performance/{timer_name}", summary="定时器性能摘要", tags=["监控"])
async def get_timer_performance(timer_name: str, hours: int = 24):
    """
    获取指定定时器的性能摘要

    Args:
        timer_name: 定时器名称
        hours: 统计时间范围（小时）
    """
    try:
        from src.cs2_investment.api.monitoring import _monitoring_manager

        if timer_name not in _monitoring_manager.monitors:
            raise HTTPException(status_code=404, detail=f"定时器 {timer_name} 不存在")

        monitor = _monitoring_manager.monitors[timer_name]
        performance_data = monitor.get_performance_summary(hours)

        return APIResponse.success(performance_data)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取定时器性能摘要失败: {e}")
        raise HTTPException(status_code=500, detail="获取性能摘要失败")


@app.get("/monitoring/integrated-scheduler", summary="集成定时器管理器监控", tags=["监控"])
async def get_integrated_scheduler_monitoring():
    """
    获取集成定时器管理器的详细监控数据
    """
    try:
        global unified_scheduler_manager
        if not unified_scheduler_manager:
            raise HTTPException(status_code=404, detail="集成定时器管理器未启动")

        status = unified_scheduler_manager.get_status()

        return APIResponse.success(status)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取集成定时器管理器监控数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取监控数据失败")


# 投资分析调度器专用接口
@app.get("/scheduler/investment/status", summary="投资分析调度器状态", tags=["调度器"])
async def get_investment_scheduler_status():
    """获取投资分析调度器详细状态"""
    try:
        global investment_analysis_scheduler
        if not investment_analysis_scheduler:
            raise HTTPException(status_code=404, detail="投资分析调度器未初始化")

        status = investment_analysis_scheduler.get_status()
        return APIResponse.success(status)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取投资分析调度器状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@app.get("/scheduler/investment/health", summary="投资分析调度器健康状态", tags=["调度器"])
async def get_investment_scheduler_health():
    """获取投资分析调度器健康状态"""
    try:
        global investment_analysis_scheduler
        if not investment_analysis_scheduler:
            raise HTTPException(status_code=404, detail="投资分析调度器未初始化")

        health = investment_analysis_scheduler.get_health_status()
        return APIResponse.success(health)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取投资分析调度器健康状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取健康状态失败: {str(e)}")


@app.get("/scheduler/investment/config", summary="投资分析调度器配置", tags=["调度器"])
async def get_investment_scheduler_config():
    """获取投资分析调度器配置"""
    try:
        global investment_analysis_scheduler
        if not investment_analysis_scheduler:
            raise HTTPException(status_code=404, detail="投资分析调度器未初始化")

        config = investment_analysis_scheduler.get_config().to_dict()
        return APIResponse.success(config)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取投资分析调度器配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


# 简化价格更新器接口
@app.get("/scheduler/simple-price/status", summary="简化价格更新器状态", tags=["调度器"])
async def get_simple_price_scheduler_status():
    """获取简化价格更新器状态"""
    try:
        global unified_scheduler_manager
        if not unified_scheduler_manager:
            raise HTTPException(status_code=404, detail="集成定时器管理器未启动")

        status = unified_scheduler_manager.get_status()
        return APIResponse.success(status)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取简化价格更新器状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@app.post("/scheduler/simple-price/trigger", summary="手动触发简化价格更新", tags=["调度器"])
async def trigger_simple_price_update():
    """手动触发简化价格更新"""
    try:
        global unified_scheduler_manager
        if not unified_scheduler_manager:
            raise HTTPException(status_code=404, detail="集成定时器管理器未启动")

        # 手动触发简化价格更新
        result = await unified_scheduler_manager.trigger_simple_price_update_now()

        return APIResponse.success(result, "简化价格更新已触发")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"手动触发简化价格更新失败: {e}")
        raise HTTPException(status_code=500, detail=f"触发失败: {str(e)}")


# Steam监控相关接口
@app.get("/scheduler/steam-monitor/status", summary="Steam监控状态", tags=["Steam监控"])
async def get_steam_monitor_status():
    """获取Steam监控状态"""
    try:
        global unified_scheduler_manager
        if not unified_scheduler_manager:
            raise HTTPException(status_code=404, detail="集成定时器管理器未启动")

        status = unified_scheduler_manager.get_status()

        # 提取Steam监控相关状态
        steam_status = {
            "service_status": "running" if unified_scheduler_manager.is_running else "stopped",
            "steam_monitor_running": getattr(unified_scheduler_manager, 'steam_monitor_running', False),
            "last_steam_monitor": status.get('task_status', {}).get('last_steam_monitor'),
            "last_steam_monitor_result": status.get('task_status', {}).get('last_steam_monitor_result'),
            "steam_monitor_count": status.get('task_status', {}).get('steam_monitor_count', 0),
            "steam_monitor_errors": status.get('task_status', {}).get('steam_monitor_errors', 0),
            "config": status.get('config', {}).get('steam_monitor', {}),
            "next_run_time": None  # 可以从调度器获取下次执行时间
        }

        # 获取下次执行时间
        try:
            jobs = unified_scheduler_manager.scheduler.get_jobs()
            for job in jobs:
                if job.id == 'steam_monitor':
                    steam_status["next_run_time"] = job.next_run_time.isoformat() if job.next_run_time else None
                    break
        except:
            pass

        return APIResponse.success(steam_status)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取Steam监控状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@app.post("/scheduler/steam-monitor/trigger", summary="手动触发Steam监控", tags=["Steam监控"])
async def trigger_steam_monitor():
    """手动触发Steam监控"""
    try:
        global unified_scheduler_manager
        if not unified_scheduler_manager:
            raise HTTPException(status_code=404, detail="集成定时器管理器未启动")

        # 手动触发Steam监控
        result = await unified_scheduler_manager.trigger_steam_monitor_now()

        return APIResponse.success(result, "Steam监控已触发")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"手动触发Steam监控失败: {e}")
        raise HTTPException(status_code=500, detail=f"触发失败: {str(e)}")


@app.get("/scheduler/steam-monitor/config", summary="Steam监控配置", tags=["Steam监控"])
async def get_steam_monitor_config():
    """获取Steam监控配置"""
    try:
        from src.cs2_investment.config.timer_config import get_timer_config

        config = get_timer_config()
        steam_config = {
            "enabled": config.steam_monitor.enabled,
            "monitor_interval_minutes": config.steam_monitor.monitor_interval_minutes,
            "batch_size": config.steam_monitor.batch_size,
            "min_delay_seconds": config.steam_monitor.min_delay_seconds,
            "max_delay_seconds": config.steam_monitor.max_delay_seconds,
            "data_source": "steam_direct"
        }

        return APIResponse.success(steam_config)
    except Exception as e:
        logger.error(f"获取Steam监控配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


# ===== SteamDT ID更新调度器接口 =====

@app.get("/scheduler/steamdt-id/status", summary="SteamDT ID更新调度器状态", tags=["SteamDT ID更新"])
async def get_steamdt_id_scheduler_status():
    """获取SteamDT ID更新调度器状态"""
    try:
        global unified_scheduler_manager
        if not unified_scheduler_manager:
            raise HTTPException(status_code=404, detail="集成定时器管理器未启动")

        # 获取统一管理器状态
        manager_status = unified_scheduler_manager.get_status()

        # 提取SteamDT ID调度器相关状态
        steamdt_id_status = manager_status.get('schedulers', {}).get('steamdt_id_update')
        scheduler_state = manager_status.get('scheduler_states', {}).get('steamdt_id_update', 'unknown')

        if steamdt_id_status is None:
            return APIResponse.success({
                'enabled': False,
                'status': 'disabled',
                'message': 'SteamDT ID更新调度器未启用'
            })

        # 构建详细状态响应
        detailed_status = {
            'enabled': True,
            'scheduler_state': scheduler_state,
            'is_running': steamdt_id_status.get('is_running', False),
            'config': steamdt_id_status.get('config', {}),
            'status': steamdt_id_status.get('status', {}),
            'manager_info': {
                'manager_running': manager_status.get('manager_status', {}).get('is_running', False),
                'manager_uptime': manager_status.get('manager_status', {}).get('uptime_seconds', 0)
            }
        }

        return APIResponse.success(detailed_status)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取SteamDT ID调度器状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@app.post("/scheduler/steamdt-id/trigger", summary="手动触发SteamDT ID更新", tags=["SteamDT ID更新"])
async def trigger_steamdt_id_update():
    """手动触发SteamDT ID更新"""
    try:
        global unified_scheduler_manager
        if not unified_scheduler_manager:
            raise HTTPException(status_code=404, detail="集成定时器管理器未启动")

        # 检查调度器是否存在且运行
        if not unified_scheduler_manager.steamdt_id_scheduler:
            raise HTTPException(status_code=404, detail="SteamDT ID更新调度器未启用")

        if not unified_scheduler_manager.steamdt_id_scheduler.is_running:
            raise HTTPException(status_code=400, detail="SteamDT ID更新调度器未运行")

        # 手动触发更新
        result = await unified_scheduler_manager.steamdt_id_scheduler.trigger_update_now()

        return APIResponse.success(result, "SteamDT ID更新已触发")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"手动触发SteamDT ID更新失败: {e}")
        raise HTTPException(status_code=500, detail=f"触发失败: {str(e)}")


@app.post("/scheduler/steamdt-id/stop", summary="停止SteamDT ID更新调度器", tags=["SteamDT ID更新"])
async def stop_steamdt_id_scheduler():
    """停止SteamDT ID更新调度器"""
    try:
        global unified_scheduler_manager
        if not unified_scheduler_manager:
            raise HTTPException(status_code=404, detail="集成定时器管理器未启动")

        # 检查调度器是否存在
        if not unified_scheduler_manager.steamdt_id_scheduler:
            raise HTTPException(status_code=404, detail="SteamDT ID更新调度器未启用")

        # 停止调度器
        await unified_scheduler_manager.steamdt_id_scheduler.stop()

        return APIResponse.success({"message": "SteamDT ID更新调度器已停止"}, "停止成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止SteamDT ID更新调度器失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止失败: {str(e)}")


@app.post("/scheduler/steamdt-id/start", summary="启动SteamDT ID更新调度器", tags=["SteamDT ID更新"])
async def start_steamdt_id_scheduler():
    """启动SteamDT ID更新调度器"""
    try:
        global unified_scheduler_manager
        if not unified_scheduler_manager:
            raise HTTPException(status_code=404, detail="集成定时器管理器未启动")

        # 检查调度器是否存在
        if not unified_scheduler_manager.steamdt_id_scheduler:
            raise HTTPException(status_code=404, detail="SteamDT ID更新调度器未启用")

        # 启动调度器
        await unified_scheduler_manager.steamdt_id_scheduler.start()

        return APIResponse.success({"message": "SteamDT ID更新调度器已启动"}, "启动成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动SteamDT ID更新调度器失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动失败: {str(e)}")


@app.get("/scheduler/steamdt-id/config", summary="SteamDT ID更新配置", tags=["SteamDT ID更新"])
async def get_steamdt_id_config():
    """获取SteamDT ID更新配置"""
    try:
        from src.cs2_investment.config.timer_config import get_timer_config

        config = get_timer_config()
        steamdt_config = {
            "enabled": config.steamdt_id_update.enabled,
            "continuous_mode": config.steamdt_id_update.continuous_mode,
            "cycle_interval_minutes": config.steamdt_id_update.cycle_interval_minutes,
            "batch_size": config.steamdt_id_update.batch_size,
            "min_wait_seconds": config.steamdt_id_update.min_wait_seconds,
            "max_wait_seconds": config.steamdt_id_update.max_wait_seconds,
            "max_retry_attempts": config.steamdt_id_update.max_retry_attempts,
            "health_check_interval": config.steamdt_id_update.health_check_interval
        }

        return APIResponse.success(steamdt_config)
    except Exception as e:
        logger.error(f"获取SteamDT ID更新配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


@app.get("/scheduler/steamdt-id/statistics", summary="SteamDT ID更新统计", tags=["SteamDT ID更新"])
async def get_steamdt_id_statistics():
    """获取SteamDT ID更新统计信息"""
    try:
        from src.cs2_investment.dao.item_dao import ItemDAO

        item_dao = ItemDAO()
        stats = item_dao.get_steamdt_id_statistics()

        return APIResponse.success(stats)
    except Exception as e:
        logger.error(f"获取SteamDT ID统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")


# 配置管理API端点
@app.post("/config/reload", summary="重载配置", tags=["配置管理"])
async def reload_config():
    """重载配置文件"""
    try:
        global unified_scheduler_manager
        if not unified_scheduler_manager:
            raise HTTPException(status_code=404, detail="调度器管理器未启动")

        result = unified_scheduler_manager.reload_config()
        return APIResponse.success(result, "配置重载完成")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"配置重载失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.put("/config/update", summary="更新配置", tags=["配置管理"])
async def update_config(request: ConfigUpdateRequest):
    """更新配置（热更新）"""
    try:
        global unified_scheduler_manager
        if not unified_scheduler_manager:
            raise HTTPException(status_code=404, detail="调度器管理器未启动")

        result = unified_scheduler_manager.update_config(request.config_updates)
        return APIResponse.success(result, "配置更新完成")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"配置更新失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/config/status", summary="配置状态", tags=["配置管理"])
async def get_config_status():
    """获取配置状态"""
    try:
        from src.cs2_investment.config.timer_config import get_timer_config
        config = get_timer_config()
        validation_result = config.validate_all()

        return APIResponse.success({
            "config_summary": config.get_summary(),
            "validation": validation_result
        })
    except Exception as e:
        logger.error(f"获取配置状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("🚀 SteamDT抓取服务启动中...")
    logger.info("📍 服务地址: http://localhost:8000")
    logger.info("📖 API文档: http://localhost:8000/docs")

    # 显示抓取器配置信息
    try:
        from src.cs2_investment.config.timer_config import get_timer_config
        from src.cs2_investment.scraper.scraper_factory import get_scraper_factory

        config = get_timer_config()
        factory = get_scraper_factory()

        logger.info("🔧 抓取器配置信息:")
        logger.info(f"   - 抓取方式: {config.scraping.scraping_method}")
        logger.info(f"   - API回退启用: {config.scraping.api_fallback_enabled}")
        logger.info(f"   - 数据验证启用: {config.scraping.data_validation_enabled}")
        logger.info(f"   - API超时: {config.scraping.api_timeout}秒")
        logger.info(f"   - Playwright超时: {config.scraping.playwright_timeout}秒")
        logger.info(f"   - 最大重试次数: {config.scraping.max_retry_attempts}")

        # 显示工厂状态
        factory_info = factory.get_factory_info()
        logger.info(f"🏭 抓取器工厂状态: {factory_info['factory_type']}")
        logger.info(f"   - 单例模式: {factory_info['singleton_instance']}")
        logger.info(f"   - 回退机制: {factory_info['fallback_enabled']}")

    except Exception as e:
        logger.warning(f"⚠️ 抓取器配置信息显示失败: {e}")

    # 启动任务执行引擎
    try:
        await task_engine.start()
        logger.info("🔧 任务执行引擎启动成功")
    except Exception as e:
        logger.error(f"❌ 任务执行引擎启动失败: {e}")



    # 启动仅实时监控调度器（饰品实时数据抓取）
    try:
        from src.cs2_investment.scheduler.realtime_only_scheduler import realtime_only_scheduler
        import threading
        import asyncio

        def start_realtime_scheduler():
            """在新线程中启动仅实时监控调度器（修复Playwright线程安全问题）"""
            try:
                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                # 设置事件循环策略，确保Playwright能正确工作
                if hasattr(asyncio, 'WindowsProactorEventLoopPolicy'):
                    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

                # 启动仅实时监控调度器
                loop.run_until_complete(realtime_only_scheduler.start())
            except Exception as e:
                logger.error(f"实时监控调度器运行异常: {e}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
            finally:
                loop.close()

        # 在守护线程中启动仅实时监控调度器
        global realtime_scheduler_thread
        realtime_scheduler_thread = threading.Thread(
            target=start_realtime_scheduler,
            daemon=True,
            name="RealtimeSchedulerThread"
        )
        realtime_scheduler_thread.start()
        logger.info("📈 仅实时监控调度器启动成功")
    except Exception as e:
        logger.error(f"❌ 仅实时监控调度器启动失败: {e}")

    # logger.info("ℹ️ 仅实时监控调度器已暂时禁用（手动注释）")

    

    # 启动集成调度器（包含Steam价格更新服务和简化价格更新）
    try:
        from src.cs2_investment.services.integrated_scheduler import IntegratedScheduler
        from src.cs2_investment.config.timer_config import get_timer_config

        # 加载定时器配置
        timer_config = get_timer_config()

        # 检查是否启用调度器
        if timer_config.scheduler.enabled:
            # 创建全局集成调度器实例
            global integrated_scheduler
            integrated_scheduler = IntegratedScheduler()

            # 在新线程中启动集成调度器
            def start_integrated_scheduler():
                """在新线程中启动集成调度器"""
                try:
                    # 创建新的事件循环
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    # 启动集成调度器
                    loop.run_until_complete(integrated_scheduler.start())

                    # 保持事件循环运行
                    loop.run_forever()
                except Exception as e:
                    logger.error(f"集成调度器运行异常: {e}")
                    import traceback
                    logger.error(f"详细错误: {traceback.format_exc()}")

            import threading
            integrated_scheduler_thread = threading.Thread(
                target=start_integrated_scheduler,
                daemon=True,
                name="IntegratedSchedulerThread"
            )
            integrated_scheduler_thread.start()

            logger.info("⏰ 集成调度器启动成功（包含Steam监控服务和简化价格更新）")

            # 显示配置摘要
            config_summary = timer_config.get_summary()
            logger.info(f"📋 定时器配置摘要: {config_summary}")

            # 显示启用的调度器
            enabled_schedulers = []
            if timer_config.simple_price_update.enabled:
                enabled_schedulers.append("简化价格更新")
            if timer_config.steam_monitor.enabled:
                enabled_schedulers.append("Steam监控服务")

            if enabled_schedulers:
                logger.info(f"🔧 已启用的集成调度器: {', '.join(enabled_schedulers)}")
            else:
                logger.warning("⚠️ 没有启用任何集成调度器")
        else:
            logger.info("ℹ️ 集成调度器已禁用（SCHEDULER_ENABLED=false）")
    except Exception as e:
        logger.error(f"❌ 集成调度器启动失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

    # 启动统一调度器管理器（包含SteamDT ID更新调度器）
    try:
        from src.cs2_investment.scheduler.unified_scheduler_manager import UnifiedSchedulerManager

        # 检查是否启用调度器
        if timer_config.scheduler.enabled:
            # 创建全局统一调度器管理器实例
            global unified_scheduler_manager
            unified_scheduler_manager = UnifiedSchedulerManager()

            # 在新线程中启动统一调度器管理器
            def start_unified_scheduler():
                """在新线程中启动统一调度器管理器"""
                try:
                    # 创建新的事件循环
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    # 启动统一调度器管理器
                    loop.run_until_complete(unified_scheduler_manager.start_all())

                    # 保持事件循环运行
                    loop.run_forever()
                except Exception as e:
                    logger.error(f"统一调度器管理器运行异常: {e}")
                    import traceback
                    logger.error(f"详细错误: {traceback.format_exc()}")

            import threading
            unified_scheduler_thread = threading.Thread(
                target=start_unified_scheduler,
                daemon=True,
                name="UnifiedSchedulerThread"
            )
            unified_scheduler_thread.start()

            logger.info("⏰ 统一调度器管理器启动成功（包含SteamDT ID更新调度器）")

            # 显示启用的调度器
            enabled_schedulers = []
            if timer_config.item_info_update.enabled:
                enabled_schedulers.append("饰品信息更新")
            # price_update已删除，使用simple_price_update替代
            # if timer_config.price_update.enabled:
            #     enabled_schedulers.append("价格更新")
            if timer_config.steamdt_id_update.enabled:
                enabled_schedulers.append("SteamDT ID更新")

            if enabled_schedulers:
                logger.info(f"🔧 已启用的统一调度器: {', '.join(enabled_schedulers)}")
            else:
                logger.warning("⚠️ 没有启用任何统一调度器")
        else:
            logger.info("ℹ️ 统一调度器管理器已禁用（SCHEDULER_ENABLED=false）")
    except Exception as e:
        logger.error(f"❌ 统一调度器管理器启动失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

    logger.info("✅ SteamDT抓取服务启动完成")


# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("🛑 SteamDT抓取服务正在关闭...")

    # 停止任务执行引擎
    try:
        await task_engine.stop()
        logger.info("🔧 任务执行引擎已停止")
    except Exception as e:
        logger.error(f"❌ 任务执行引擎停止失败: {e}")

    # 停止定时爬取调度器
    # try:
    #     global scheduler_manager
    #     if 'scheduler_manager' in globals() and scheduler_manager:
    #         scheduler_manager.stop()
    #         logger.info("⏰ 定时爬取调度器已停止")
    # except Exception as e:
    #     logger.error(f"❌ 定时爬取调度器停止失败: {e}")

    # 停止仅实时监控调度器
    try:
        from src.cs2_investment.scheduler.realtime_only_scheduler import realtime_only_scheduler
        realtime_only_scheduler.stop()
        logger.info("📈 仅实时监控调度器已停止")
    except Exception as e:
        logger.error(f"❌ 仅实时监控调度器停止失败: {e}")

    # logger.info("ℹ️ 仅实时监控调度器停止代码已暂时禁用（手动注释）")

    # 停止简化版投资分析调度器
    # try:
    #     global investment_analysis_scheduler
    #     if investment_analysis_scheduler:
    #         await investment_analysis_scheduler.stop()
    #         logger.info("🧠 简化版投资分析调度器已停止")
    # except Exception as e:
    #     logger.error(f"❌ 简化版投资分析调度器停止失败: {e}")

    # 停止集成定时器管理器 - 暂时注释掉
    # try:
    #     global unified_scheduler_manager
    #     if 'unified_scheduler_manager' in globals() and unified_scheduler_manager:
    #         unified_scheduler_manager.stop()
    #         logger.info("⏰ 集成定时器管理器已停止")
    # except Exception as e:
    #     logger.error(f"❌ 集成定时器管理器停止失败: {e}")

    logger.info("ℹ️ 集成定时器管理器停止代码已暂时禁用（测试分析定时器）")

    logger.info("✅ SteamDT抓取服务已关闭")


# 根路径重定向到文档
@app.get("/", summary="根路径", tags=["系统"])
async def root():
    """根路径，重定向到API文档"""
    return APIResponse.success({
        "message": "欢迎使用SteamDT抓取服务",
        "docs_url": "/docs",
        "health_check": "/health",
        "service_info": "/info"
    })


def main():
    """主函数，用于直接运行服务"""
    uvicorn.run(
        "src.cs2_investment.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        workers=1,
        log_level="info"
    )


if __name__ == "__main__":
    main()
