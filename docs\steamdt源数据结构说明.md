# SteamDT源数据结构说明

## 一、K线数据

### 数据格式

所有K线数据均为JSON数组格式，每个数组元素包含7个字段：

```json
[
  "时间戳",
  开盘价,
  收盘价,
  最高价,
  最低价,
  交易量,
  交易额
]
```

### 字段说明

| 索引 | 字段名称 | 数据类型 | 说明 |
|------|----------|----------|------|
| 0 | 时间戳 | String | Unix时间戳（秒），表示该K线周期的开始时间 |
| 1 | 开盘价 | Number | 该时间周期内的开盘价格（人民币） |
| 2 | 收盘价 | Number | 该时间周期内的收盘价格（人民币） |
| 3 | 最高价 | Number | 该时间周期内的最高价格（人民币） |
| 4 | 最低价 | Number | 该时间周期内的最低价格（人民币） |
| 5 | 交易量 | Number/null | 该时间周期内的交易数量，null表示无交易，使用时请当作0处理 |
| 6 | 交易额 | Number/null | 该时间周期内的交易金额（人民币），null表示无交易，使用时请当作0处理 |

### 1.1 时K数据（时k.json）

**时间周期：** 1小时

**数据特点：**
- 数据密度最高，包含详细的小时级别价格变动
- 交易量和交易额经常为null，表示该小时内无交易发生

**示例：**
```json
[
  "1751450400",  // 2025-05-02 00:00:00 UTC
  430.0,         // 开盘价
  430.0,         // 收盘价
  430.0,         // 最高价
  430.0,         // 最低价
  null,          // 小时交易量
  null           // 小时交易额
]
```

### 1.2 日K数据（日k1.json, 日k2.json）

**时间周期：** 1天

**数据特点：**
- 可能分为多个文件存储（如日k1.json, 日k2.json）
- **重要：使用时需要将日k1.json和日k2.json按照时间先后顺序进行拼接后作为整体使用**
- 交易量和交易额经常为null，表示该小时内无交易发生

**示例：**
```json
[
  "1746201600",  // 2025-01-03 00:00:00 UTC
  394.29,        // 开盘价
  406.0,         // 收盘价
  408.0,         // 最高价
  394.29,        // 最低价
  26,            // 日交易量
  11416.9        // 日交易额
]
```

### 1.3 周K数据（周k.json）

**时间周期：** 1周

**数据特点：**
- 早期数据可能缺少交易量和交易额信息（显示为null）
- 可能大部分的交易额和交易量都是null，尽量不要拿这两个字段的数据用作分析

**示例：**
```json
[
  "1656259200",  // 2022-06-27 00:00:00 UTC
  155.0,         // 开盘价
  155.0,         // 收盘价
  159.0,         // 最高价
  152.0,         // 最低价
  null,          // 交易量
  null           // 交易额
]
```

## 二、走势3m数据（走势_3m.json）

### 数据格式

8个字段的数组结构：

```json
[
  "时间戳",
  当前价格,
  在售数量,
  求购价,
  求购数量,
  小时交易额,
  小时交易量,
  "截至当前小时的存世量"
]
```

### 字段说明

| 索引 | 字段名称 | 数据类型 | 说明 |
|------|----------|----------|------|
| 0 | 时间戳 | String | Unix时间戳（秒） |
| 1 | 在售价格 | Number | **所有卖家挂单中的最低价格**，买家如果想立即买到，需要支付这个价格（人民币） |
| 2 | 在售数量 | Number | **愿意以在售价格或更高价格出售的总数量**，表示卖方挂单的积极程度 |
| 3 | 求购价格 | Number | **所有买家求购单中的最高价格**，卖家如果想立即卖出，可以接受这个价格（人民币） |
| 4 | 求购数量 | Number | **愿意以求购价格或更低价格购买的总数量**，表示买方挂单的积极程度 |
| 5 | 小时交易额 | Number/null | 小时成交额，该小时内的交易金额，null时当作0处理 |
| 6 | 小时交易量 | Number/null | 小时成交量，该小时内的交易数量，null时当作0处理 |
| 7 | 截至当前小时的存世量 | String | 该商品在Steam市场上的总存世数量 |

### 重要概念说明

#### 价差机制
- **价差 = 在售价格 - 求购价格**
- **价差越小，市场越活跃，流动性越好**
- **价差反映买卖双方的价格分歧程度**

#### 挂单机制
- **在售挂单**：卖家设定价格等待买家购买
- **求购挂单**：买家设定价格等待卖家接受
- **挂单数量比例反映买卖双方的积极性对比，而非简单的供需关系**

### 数据特点

- 数据时间跨度：3个月
- 时间间隔：1小时

### 示例

```json
[
  "1751389183",  // 2025-05-01 07:06:23 UTC
  430.0,         // 当前价格
  538,           // 在售数量
  419.0,         // 求购价
  43,            // 求购数量
  1761.99,       // 小时成交额
  4,             // 小时成交量
  "37057"        // 截至当前小时的存世量
]
```

## 三、走势6m数据（走势_6m.json）

### 数据格式

8个字段的数组结构：

```json
[
  "时间戳",
  当前价格,
  在售数量,
  求购价,
  求购数量,
  日交易额,
  日交易量,
  "截至当日的存世量"
]
```

### 字段说明

| 索引 | 字段名称 | 数据类型 | 说明 |
|------|----------|----------|------|
| 0 | 时间戳 | String | Unix时间戳（秒） |
| 1 | 在售价格 | Number | **所有卖家挂单中的最低价格**，买家如果想立即买到，需要支付这个价格（人民币） |
| 2 | 在售数量 | Number | **愿意以在售价格或更高价格出售的总数量**，表示卖方挂单的积极程度 |
| 3 | 求购价格 | Number | **所有买家求购单中的最高价格**，卖家如果想立即卖出，可以接受这个价格（人民币） |
| 4 | 求购数量 | Number | **愿意以求购价格或更低价格购买的总数量**，表示买方挂单的积极程度 |
| 5 | 日交易额 | Number/null | 日成交额，该日内的交易金额，null时当作0处理 |
| 6 | 日交易量 | Number/null | 日成交量，该日内的交易数量，null时当作0处理 |
| 7 | 截至当日的存世量 | String | 该商品在Steam市场上截至当日的总存世量 |

### 数据特点

- 数据时间跨度：6个月
- 时间间隔：1天

### 示例

```json
[
  "1738511983",  // 2025-02-02 23:06:23 UTC
  370.0,         // 当前价格
  645,           // 在售数量
  368.0,         // 求购价
  45,            // 求购数量
  5046.11,       // 日成交额
  12,            // 日成交量
  "35816"        // 截至当日的存世量
]
```
