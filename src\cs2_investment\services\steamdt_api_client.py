"""
SteamDT API客户端

提供与SteamDT开放平台的接口交互功能。
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))


@dataclass
class PlatformPriceData:
    """平台价格数据"""
    platform: str
    platform_item_id: str
    sell_price: float
    sell_count: int
    bidding_price: float
    bidding_count: int
    update_time: int


@dataclass
class ItemPriceResponse:
    """饰品价格响应"""
    market_hash_name: str
    platform_prices: List[PlatformPriceData]
    query_time: datetime
    success: bool
    error_message: str = ""


@dataclass
class PlatformMapping:
    """平台映射数据"""
    name: str
    item_id: str


@dataclass
class ItemBaseInfo:
    """饰品基础信息"""
    name: str
    market_hash_name: str
    platform_list: List[PlatformMapping]


@dataclass
class ItemBaseInfoResponse:
    """饰品基础信息响应"""
    items: List[ItemBaseInfo]
    query_time: datetime
    success: bool
    error_message: str = ""


class SteamDTAPIClient:
    """SteamDT API客户端"""
    
    def __init__(self, api_key: str):
        """
        初始化API客户端
        
        Args:
            api_key: SteamDT API密钥
        """
        self.api_key = api_key
        self.base_url = "https://open.steamdt.com"

        # 使用定时器专用logger以确保日志输出
        try:
            from ..utils.logger import get_timer_logger
            self.logger = get_timer_logger(__name__, "api_client")
        except:
            self.logger = logging.getLogger(__name__)
        
        # 请求配置
        self.timeout = aiohttp.ClientTimeout(total=30)
        self.max_retries = 3
        self.retry_delay = 1.0
        
        # 批量请求限制
        self.batch_size = 100  # SteamDT API限制
        
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'CS2-Investment-Analysis/1.0'
        }
    
    async def get_single_price(self, market_hash_name: str) -> ItemPriceResponse:
        """
        获取单个饰品价格
        
        Args:
            market_hash_name: 饰品市场哈希名称
            
        Returns:
            ItemPriceResponse: 价格响应数据
        """
        url = f"{self.base_url}/open/cs2/v1/price/single"
        params = {'marketHashName': market_hash_name}

        # 简化请求日志
        self.logger.debug(f"🔍 [单个接口] 请求: {market_hash_name}")

        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                for attempt in range(self.max_retries):
                    try:
                        start_time = datetime.now()
                        async with session.get(url, params=params, headers=self._get_headers()) as response:
                            end_time = datetime.now()
                            duration = (end_time - start_time).total_seconds()

                            if response.status == 200:
                                data = await response.json()
                                self.logger.debug(f"✅ [单个接口] 成功: {market_hash_name} ({duration:.2f}秒)")
                                return self._parse_single_response(market_hash_name, data)
                            else:
                                error_text = await response.text()
                                self.logger.error(f"❌ [单个接口] 请求失败: HTTP {response.status}")
                                self.logger.error(f"   响应内容: {error_text}")
                                
                    except asyncio.TimeoutError:
                        self.logger.warning(f"API请求超时 (尝试 {attempt + 1}/{self.max_retries}): {market_hash_name}")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(self.retry_delay * (attempt + 1))
                            continue
                        
                    except Exception as e:
                        self.logger.error(f"API请求异常 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(self.retry_delay * (attempt + 1))
                            continue
                
                # 所有重试都失败
                return ItemPriceResponse(
                    market_hash_name=market_hash_name,
                    platform_prices=[],
                    query_time=datetime.now(),
                    success=False,
                    error_message="API请求失败，已达到最大重试次数"
                )
                
        except Exception as e:
            self.logger.error(f"获取单个价格失败: {market_hash_name}, 错误: {e}")
            return ItemPriceResponse(
                market_hash_name=market_hash_name,
                platform_prices=[],
                query_time=datetime.now(),
                success=False,
                error_message=str(e)
            )
    
    async def get_batch_prices(self, market_hash_names: List[str]) -> Dict[str, ItemPriceResponse]:
        """
        批量获取饰品价格
        
        Args:
            market_hash_names: 饰品市场哈希名称列表
            
        Returns:
            Dict[str, ItemPriceResponse]: 饰品名称到价格响应的映射
        """
        results = {}
        
        # 分批处理，每批最多100个
        for i in range(0, len(market_hash_names), self.batch_size):
            batch = market_hash_names[i:i + self.batch_size]
            batch_results = await self._get_batch_chunk(batch)
            results.update(batch_results)
            
            # 避免API限制，添加延迟
            if i + self.batch_size < len(market_hash_names):
                await asyncio.sleep(0.5)
        
        return results
    
    async def _get_batch_chunk(self, market_hash_names: List[str]) -> Dict[str, ItemPriceResponse]:
        """
        获取一批饰品价格（内部方法）
        
        Args:
            market_hash_names: 饰品市场哈希名称列表（最多100个）
            
        Returns:
            Dict[str, ItemPriceResponse]: 饰品名称到价格响应的映射
        """
        url = f"{self.base_url}/open/cs2/v1/price/batch"
        payload = {'marketHashNames': market_hash_names}

        # 简化请求日志
        self.logger.info(f"📦 [批量接口] 请求 {len(market_hash_names)} 个饰品")

        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                for attempt in range(self.max_retries):
                    try:
                        start_time = datetime.now()
                        async with session.post(url, json=payload, headers=self._get_headers()) as response:
                            end_time = datetime.now()
                            duration = (end_time - start_time).total_seconds()

                            if response.status == 200:
                                data = await response.json()
                                self.logger.info(f"✅ [批量接口] 成功获取 {len(market_hash_names)} 个饰品价格 ({duration:.2f}秒)")
                                return self._parse_batch_response(data)
                            else:
                                error_text = await response.text()
                                self.logger.error(f"❌ [批量接口] 请求失败: HTTP {response.status}")
                                self.logger.error(f"   响应内容: {error_text}")
                                
                    except asyncio.TimeoutError:
                        self.logger.warning(f"批量API请求超时 (尝试 {attempt + 1}/{self.max_retries})")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(self.retry_delay * (attempt + 1))
                            continue
                            
                    except Exception as e:
                        self.logger.error(f"批量API请求异常 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(self.retry_delay * (attempt + 1))
                            continue
                
                # 所有重试都失败，返回失败响应
                return {
                    name: ItemPriceResponse(
                        market_hash_name=name,
                        platform_prices=[],
                        query_time=datetime.now(),
                        success=False,
                        error_message="批量API请求失败，已达到最大重试次数"
                    )
                    for name in market_hash_names
                }
                
        except Exception as e:
            self.logger.error(f"批量获取价格失败: {e}")
            return {
                name: ItemPriceResponse(
                    market_hash_name=name,
                    platform_prices=[],
                    query_time=datetime.now(),
                    success=False,
                    error_message=str(e)
                )
                for name in market_hash_names
            }
    
    def _parse_single_response(self, market_hash_name: str, data: Dict[str, Any]) -> ItemPriceResponse:
        """解析单个价格响应"""
        self.logger.debug(f"🔍 [响应解析] 开始解析: {market_hash_name}")

        query_time = datetime.now()

        if not data.get('success', False):
            self.logger.warning(f"❌ [响应解析] API返回失败: {market_hash_name}")
            self.logger.warning(f"   📄 错误信息: {data.get('errorMsg', '未知错误')}")
            return ItemPriceResponse(
                market_hash_name=market_hash_name,
                platform_prices=[],
                query_time=query_time,
                success=False,
                error_message=data.get('errorMsg', '未知错误')
            )
        
        platform_prices = []
        data_items = data.get('data', [])
        self.logger.debug(f"📦 [响应解析] 解析 {len(data_items)} 个平台数据")

        for i, item in enumerate(data_items):
            try:
                platform_price = PlatformPriceData(
                    platform=item.get('platform', ''),
                    platform_item_id=item.get('platformItemId', ''),
                    sell_price=float(item.get('sellPrice', 0)),
                    sell_count=int(item.get('sellCount', 0)),
                    bidding_price=float(item.get('biddingPrice', 0)),
                    bidding_count=int(item.get('biddingCount', 0)),
                    update_time=int(item.get('updateTime', 0))
                )
                platform_prices.append(platform_price)

            except (ValueError, TypeError) as e:
                self.logger.warning(f"❌ [响应解析] 解析平台价格数据失败: {e}")
                self.logger.warning(f"   📄 原始数据: {item}")
                continue

        self.logger.debug(f"✅ [响应解析] 完成: {market_hash_name} ({len(platform_prices)} 个平台)")

        return ItemPriceResponse(
            market_hash_name=market_hash_name,
            platform_prices=platform_prices,
            query_time=query_time,
            success=True
        )
    
    def _parse_batch_response(self, data: Dict[str, Any]) -> Dict[str, ItemPriceResponse]:
        """解析批量价格响应"""
        results = {}
        query_time = datetime.now()
        
        if not data.get('success', False):
            error_message = data.get('errorMsg', '未知错误')
            self.logger.error(f"批量API请求失败: {error_message}")
            return results
        
        for item in data.get('data', []):
            market_hash_name = item.get('marketHashName', '')
            if not market_hash_name:
                continue
                
            platform_prices = []
            for price_item in item.get('dataList', []):
                try:
                    platform_price = PlatformPriceData(
                        platform=price_item.get('platform', ''),
                        platform_item_id=price_item.get('platformItemId', ''),
                        sell_price=float(price_item.get('sellPrice', 0)),
                        sell_count=int(price_item.get('sellCount', 0)),
                        bidding_price=float(price_item.get('biddingPrice', 0)),
                        bidding_count=int(price_item.get('biddingCount', 0)),
                        update_time=int(price_item.get('updateTime', 0))
                    )
                    platform_prices.append(platform_price)
                except (ValueError, TypeError) as e:
                    self.logger.warning(f"解析平台价格数据失败: {e}, 数据: {price_item}")
                    continue
            
            results[market_hash_name] = ItemPriceResponse(
                market_hash_name=market_hash_name,
                platform_prices=platform_prices,
                query_time=query_time,
                success=True
            )
        
        return results

    async def get_item_base_info(self) -> ItemBaseInfoResponse:
        """
        获取饰品基础信息

        Returns:
            ItemBaseInfoResponse: 饰品基础信息响应数据
        """
        url = f"{self.base_url}/open/cs2/v1/base"
        query_time = datetime.now()

        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                for attempt in range(self.max_retries):
                    try:
                        async with session.get(url, headers=self._get_headers()) as response:
                            if response.status == 200:
                                data = await response.json()
                                return self._parse_base_info_response(data, query_time)
                            else:
                                error_text = await response.text()
                                self.logger.warning(f"获取饰品基础信息失败 (状态码: {response.status}): {error_text}")

                    except asyncio.TimeoutError:
                        self.logger.warning(f"获取饰品基础信息超时 (尝试 {attempt + 1}/{self.max_retries})")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(self.retry_delay)
                            continue

                    except Exception as e:
                        self.logger.error(f"获取饰品基础信息异常 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(self.retry_delay)
                            continue

                # 所有重试都失败
                return ItemBaseInfoResponse(
                    items=[],
                    query_time=query_time,
                    success=False,
                    error_message="获取饰品基础信息失败：所有重试都失败"
                )

        except Exception as e:
            self.logger.error(f"获取饰品基础信息异常: {e}")
            return ItemBaseInfoResponse(
                items=[],
                query_time=query_time,
                success=False,
                error_message=f"获取饰品基础信息异常: {str(e)}"
            )

    def _parse_base_info_response(self, data: Dict[str, Any], query_time: datetime) -> ItemBaseInfoResponse:
        """
        解析饰品基础信息响应

        Args:
            data: API响应数据
            query_time: 查询时间

        Returns:
            ItemBaseInfoResponse: 解析后的响应数据
        """
        try:
            if not data.get('success', False):
                error_msg = data.get('message', '未知错误')
                self.logger.error(f"API返回错误: {error_msg}")
                return ItemBaseInfoResponse(
                    items=[],
                    query_time=query_time,
                    success=False,
                    error_message=f"API返回错误: {error_msg}"
                )

            items = []
            data_list = data.get('data', [])

            for item_data in data_list:
                try:
                    # 解析平台映射列表
                    platform_list = []
                    for platform_data in item_data.get('platformList', []):
                        platform_mapping = PlatformMapping(
                            name=platform_data.get('name', ''),
                            item_id=platform_data.get('itemId', '')
                        )
                        platform_list.append(platform_mapping)

                    # 创建饰品基础信息对象
                    item_info = ItemBaseInfo(
                        name=item_data.get('name', ''),
                        market_hash_name=item_data.get('marketHashName', ''),
                        platform_list=platform_list
                    )
                    items.append(item_info)

                except Exception as e:
                    self.logger.warning(f"解析饰品数据失败: {e}, 数据: {item_data}")
                    continue

            self.logger.info(f"成功解析 {len(items)} 个饰品基础信息")
            return ItemBaseInfoResponse(
                items=items,
                query_time=query_time,
                success=True
            )

        except Exception as e:
            self.logger.error(f"解析饰品基础信息响应失败: {e}")
            return ItemBaseInfoResponse(
                items=[],
                query_time=query_time,
                success=False,
                error_message=f"解析响应失败: {str(e)}"
            )

    async def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 使用一个常见的饰品名称进行测试
            test_item = "AK-47 | Redline (Field-Tested)"
            response = await self.get_single_price(test_item)
            return response.success
        except Exception as e:
            self.logger.error(f"API连接测试失败: {e}")
            return False
