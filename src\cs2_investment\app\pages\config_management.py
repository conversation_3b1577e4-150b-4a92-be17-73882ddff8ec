"""
配置管理页面

提供用户友好的界面来管理调度器和系统配置。
"""

import streamlit as st
import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

from src.cs2_investment.config.timer_config import get_timer_config, reload_timer_config, TimerConfig
from src.cs2_investment.app.services.config_service import ConfigService


def save_config_with_reload(config_service: ConfigService, new_config: Dict[str, Any], config_name: str,
                           require_confirmation: bool = False):
    """保存配置并触发重载"""

    # 配置验证
    validation_result = config_service.validate_config_before_save(new_config)

    if not validation_result["valid"]:
        st.error("❌ 配置验证失败，无法保存")
        for error in validation_result["errors"]:
            st.error(f"• {error}")
        return

    # 显示验证警告
    if validation_result["warnings"]:
        st.warning("⚠️ 配置验证警告:")
        for warning in validation_result["warnings"]:
            st.warning(f"• {warning}")

    # 确认对话框
    if require_confirmation:
        confirm_key = f"confirm_save_{config_name.replace(' ', '_')}"
        if not st.session_state.get(confirm_key, False):
            st.session_state[confirm_key] = True
            st.warning(f"⚠️ 确认要保存 {config_name} 吗？这将覆盖当前配置。")
            col1, col2 = st.columns(2)
            with col1:
                if st.button("✅ 确认保存", key=f"confirm_yes_{confirm_key}"):
                    st.session_state[confirm_key] = False
                    st.rerun()
            with col2:
                if st.button("❌ 取消", key=f"confirm_no_{confirm_key}"):
                    st.session_state[confirm_key] = False
                    st.info("配置保存已取消")
            return

    # 使用ConfigService的组合方法
    result = config_service.save_config_with_reload(new_config)

    if result["file_saved"]:
        st.success(f"✅ {config_name}已保存到配置文件")

        if result["api_reloaded"]:
            st.success("🚀 配置已立即生效！")

            # 显示重载结果的警告信息
            reload_result = result.get("reload_result", {})
            if reload_result.get("warnings"):
                for warning in reload_result["warnings"]:
                    st.warning(f"⚠️ {warning}")

            # 重载本地配置并刷新页面
            try:
                reload_timer_config()  # 重载本地配置
                st.info("💡 配置已更新，页面将自动刷新以显示最新配置")

                # 延迟刷新页面以显示最新配置
                import time
                time.sleep(1)
                st.rerun()
            except Exception as e:
                st.warning(f"⚠️ 本地配置重载失败: {e}，请手动刷新页面")
        else:
            if result.get("warning"):
                st.warning(f"⚠️ 配置已保存，但{result['warning']}")
                st.info("💡 请重启API服务以应用配置更改")
            else:
                st.warning("⚠️ 配置已保存，但未能立即生效")
                st.info("💡 请重启API服务以应用配置更改")

            # 显示错误详情
            reload_result = result.get("reload_result", {})
            if reload_result.get("error"):
                with st.expander("查看错误详情"):
                    st.error(f"错误详情: {reload_result['error']}")

                    # 提供回滚选项
                    if st.button("🔄 回滚到上一个配置", key=f"rollback_after_error_{config_name}"):
                        rollback_result = config_service.rollback_config()
                        if rollback_result["success"]:
                            st.success("✅ 配置已回滚到上一个版本")
                            st.info("💡 请重载配置以应用更改")
                        else:
                            st.error(f"❌ 回滚失败: {rollback_result['error']}")
    else:
        st.error(f"❌ 保存{config_name}失败")

        # 提供故障排除建议
        with st.expander("💡 故障排除建议"):
            st.markdown("""
            **可能的原因和解决方案：**

            1. **文件权限问题**: 检查.env文件的写入权限
            2. **磁盘空间不足**: 检查磁盘剩余空间
            3. **文件被占用**: 确保没有其他程序正在使用配置文件
            4. **配置格式错误**: 检查配置参数的格式和类型

            **建议操作：**
            - 检查系统日志获取详细错误信息
            - 尝试重启应用程序
            - 联系系统管理员
            """)


def show_config_status_monitor(config_service: ConfigService):
    """显示配置状态监控"""
    st.subheader("📊 配置状态监控")

    # 添加刷新按钮
    col_refresh, col_auto = st.columns([1, 4])
    with col_refresh:
        if st.button("🔄 刷新状态", help="手动刷新状态信息"):
            st.rerun()

    with col_auto:
        auto_refresh = st.checkbox("自动刷新 (30秒)", value=False, help="每30秒自动刷新状态")
        if auto_refresh:
            # 使用Streamlit的自动刷新机制
            st.info("💡 自动刷新已启用，页面将每30秒更新一次")
            # 注意：实际的自动刷新需要在前端实现，这里只是提示

    # 状态指标显示
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        # API服务连接状态
        api_available = config_service.check_api_service_available()
        if api_available:
            st.metric("🌐 API服务", "已连接", "✅")
        else:
            st.metric("🌐 API服务", "未连接", "❌")

    with col2:
        # 配置文件状态
        config_status = config_service.get_config_status()
        if config_status["env_file_exists"]:
            file_size = config_status["env_file_size"]
            st.metric("📄 配置文件", f"{file_size} bytes", "✅")
        else:
            st.metric("📄 配置文件", "缺失", "❌")

    with col3:
        # 配置验证状态
        if config_status["validation_valid"]:
            st.metric("✅ 配置验证", "通过", "✅")
        else:
            error_count = len(config_status["validation_errors"])
            st.metric("❌ 配置验证", f"{error_count}个错误", "❌")

    with col4:
        # 备份文件数量
        backup_count = config_status["backup_count"]
        st.metric("💾 配置备份", f"{backup_count}个", "📁")

    # 详细状态信息
    with st.expander("📋 详细状态信息", expanded=False):
        col_left, col_right = st.columns(2)

        with col_left:
            st.markdown("#### 🔧 本地配置状态")
            if config_status["env_file_exists"]:
                st.success("✅ .env文件存在")
                if config_status["env_file_modified"]:
                    st.caption(f"最后修改: {config_status['env_file_modified']}")
            else:
                st.error("❌ .env文件不存在")

            if config_status["validation_valid"]:
                st.success("✅ 配置验证通过")
            else:
                st.error("❌ 配置验证失败")
                for error in config_status["validation_errors"]:
                    st.error(f"• {error}")

            if config_status["validation_warnings"]:
                st.warning("⚠️ 配置警告:")
                for warning in config_status["validation_warnings"]:
                    st.warning(f"• {warning}")

        with col_right:
            st.markdown("#### 🌐 后端服务状态")
            if api_available:
                st.success("✅ API服务连接正常")

                # 获取后端配置状态
                api_status = config_service.get_config_status_via_api()
                if api_status.get("success") != False:
                    st.success("✅ 后端配置状态正常")

                    # 显示后端配置摘要
                    if "config_summary" in api_status:
                        st.json(api_status["config_summary"])
                else:
                    st.error(f"❌ 后端配置状态异常: {api_status.get('error', '未知错误')}")
            else:
                st.error("❌ API服务连接失败")
                st.caption("配置更改将无法立即生效，需要重启服务")


def show_config_help():
    """显示配置帮助信息"""
    with st.expander("❓ 配置帮助", expanded=False):
        st.markdown("""
        ### 🚀 配置实时生效说明

        1. **保存配置**: 配置会自动保存到.env文件并创建备份
        2. **立即生效**: 如果API服务可用，配置会立即应用到运行中的调度器
        3. **状态反馈**: 页面会显示配置是否成功应用
        4. **错误处理**: 如果应用失败，可以查看错误详情或回滚配置

        ### 🔧 故障排除

        - **API服务未连接**: 请确保API服务正在运行 (http://localhost:8000)
        - **配置验证失败**: 请检查配置参数的有效性
        - **应用失败**: 可以尝试手动重载或重启服务
        - **配置冲突**: 可以使用配置回滚功能恢复到之前的状态

        ### 📋 操作指南

        1. **修改配置**: 在相应标签页中修改配置参数
        2. **保存配置**: 点击"💾 保存配置"按钮
        3. **查看状态**: 通过状态监控了解配置应用情况
        4. **处理错误**: 如果出现问题，查看错误详情并采取相应措施
        5. **回滚配置**: 如需要，可以回滚到之前的配置版本

        ### ⚠️ 注意事项

        - 配置修改前会自动创建备份
        - 重要配置更改建议先在测试环境验证
        - API服务重启后配置会自动重新加载
        - 配置文件支持环境变量覆盖
        """)


def show_config_backup_management(config_service: ConfigService):
    """显示配置备份管理"""
    st.subheader("💾 配置备份管理")

    # 获取备份列表
    backup_list = config_service.get_backup_list()

    if not backup_list:
        st.info("📝 暂无配置备份文件")
        return

    col1, col2 = st.columns([3, 1])

    with col1:
        st.markdown("#### 备份文件列表")

        for i, backup in enumerate(backup_list):
            with st.container():
                col_info, col_action = st.columns([4, 1])

                with col_info:
                    if backup["is_latest"]:
                        st.markdown(f"**🔥 {backup['filename']}** (最新)")
                    else:
                        st.markdown(f"📄 {backup['filename']}")

                    st.caption(f"大小: {backup['size']} bytes | 时间: {backup['modified_time']}")

                with col_action:
                    if st.button(f"回滚", key=f"rollback_{i}", help=f"回滚到 {backup['filename']}"):
                        # 配置回滚确认
                        if st.session_state.get(f"confirm_rollback_{i}", False):
                            with st.spinner("正在回滚配置..."):
                                # 这里需要实现特定备份的回滚功能
                                result = config_service.rollback_config()
                                if result["success"]:
                                    st.success(f"✅ 配置已回滚到 {backup['filename']}")
                                    st.info("💡 请重载配置以应用更改")
                                    st.rerun()
                                else:
                                    st.error(f"❌ 回滚失败: {result['error']}")
                            st.session_state[f"confirm_rollback_{i}"] = False
                        else:
                            st.session_state[f"confirm_rollback_{i}"] = True
                            st.warning(f"⚠️ 确认要回滚到 {backup['filename']} 吗？再次点击确认。")

                if i < len(backup_list) - 1:
                    st.divider()

    with col2:
        st.markdown("#### 备份操作")

        if st.button("🗑️ 清理旧备份", help="删除除最新3个外的所有备份"):
            # 实现清理旧备份的逻辑
            st.info("清理功能开发中...")

        if st.button("📥 手动备份", help="立即创建配置备份"):
            try:
                config_service._create_backup()
                st.success("✅ 手动备份已创建")
                st.rerun()
            except Exception as e:
                st.error(f"❌ 创建备份失败: {e}")


def show_page():
    """显示配置管理页面"""
    st.title("⚙️ 系统配置管理")
    st.markdown("管理调度器和系统参数配置")
    
    # 初始化配置服务
    if 'config_service' not in st.session_state:
        st.session_state.config_service = ConfigService()
    
    config_service = st.session_state.config_service
    
    # 加载当前配置
    try:
        current_config = get_timer_config()
    except Exception as e:
        st.error(f"加载配置失败: {e}")
        return

    # 显示配置状态监控
    show_config_status_monitor(config_service)

    st.divider()  # 添加分隔线

    # 创建按调度器分组的标签页
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
        "🔄 全局调度器配置",
        "🚀 饰品价格监控调度器",
        "⚡ 饰品基础信息同步调度器",
        "🌐 基础配置",
        "📊 饰品分析配置",
        "💾 配置管理 & 备份"
    ])

    with tab1:
        show_global_scheduler_config(current_config, config_service)

    with tab2:
        show_integrated_scheduler_config(current_config, config_service)

    with tab3:
        show_unified_scheduler_config(current_config, config_service)

    with tab4:
        show_basic_config(current_config, config_service)

    with tab5:
        show_analysis_config(current_config, config_service)

    with tab6:
        show_config_management(current_config, config_service)


def show_global_scheduler_config(config: TimerConfig, config_service: ConfigService):
    """显示全局调度器配置"""
    st.subheader("🔄 全局调度器配置")

    st.info("💡 控制各个调度器的启用状态")

    # 各调度器启用状态
    with st.expander("📋 各调度器启用状态", expanded=True):
        st.markdown("#### 饰品价格监控调度器")
        col1, col2 = st.columns(2)

        with col1:
            simple_price_enabled = st.checkbox(
                "SteamDT 各平台价格同步服务",
                value=config.simple_price_update.enabled,
                help="启用SteamDT各平台价格同步服务（推荐）"
            )

        with col2:
            steam_monitor_enabled = st.checkbox(
                "Steam价格同步服务",
                value=config.steam_monitor.enabled,
                help="启用Steam价格同步服务"
            )

        st.markdown("#### 饰品基础信息同步调度器")
        col3, col4 = st.columns(2)

        with col3:
            item_info_enabled = st.checkbox(
                "SteamDT饰品同步服务",
                value=config.item_info_update.enabled,
                help="启用SteamDT饰品同步服务"
            )

        with col4:
            steamdt_id_enabled = st.checkbox(
                "SteamDT饰品信息同步服务",
                value=config.steamdt_id_update.enabled,
                help="启用SteamDT饰品信息同步服务"
            )

    # 保存按钮
    if st.button("💾 保存调度器启用状态", type="primary"):
        new_config = {
            "ITEM_INFO_UPDATE_ENABLED": item_info_enabled,
            "SIMPLE_PRICE_UPDATE_ENABLED": simple_price_enabled,
            "STEAMDT_ID_UPDATE_ENABLED": steamdt_id_enabled,
            "STEAM_MONITOR_ENABLED": steam_monitor_enabled,
        }

        with st.spinner("正在保存配置并应用更改..."):
            save_config_with_reload(config_service, new_config, "调度器启用状态", require_confirmation=True)


def show_integrated_scheduler_config(config: TimerConfig, config_service: ConfigService):
    """显示饰品价格监控调度器配置"""
    st.subheader("🚀 饰品价格监控调度器配置")

    # 显示调度器状态
    if not config.scheduler.enabled:
        st.warning("⚠️ 全局调度器已禁用，此配置不会生效")

    st.info("💡 饰品价格监控调度器包含：SteamDT各平台价格同步服务 + Steam价格同步服务")

    # SteamDT各平台价格同步服务配置
    with st.expander("💰 SteamDT各平台价格同步服务配置", expanded=config.simple_price_update.enabled):
        if not config.simple_price_update.enabled:
            st.warning("⚠️ SteamDT各平台价格同步服务已禁用")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### 基础配置")

            update_interval = st.number_input(
                "更新间隔 (分钟)",
                min_value=1,
                max_value=60,
                value=config.simple_price_update.update_interval_minutes,
                help="SteamDT各平台价格同步的执行间隔"
            )

            simple_batch_size = st.number_input(
                "批量处理数量",
                min_value=1,
                max_value=100,
                value=config.simple_price_update.batch_size,
                help="批量接口处理的饰品数量"
            )

            single_size = st.number_input(
                "单个处理数量",
                min_value=1,
                max_value=100,
                value=config.simple_price_update.single_size,
                help="单个接口处理的饰品数量"
            )

        with col2:
            st.markdown("#### 高级配置")

            single_interval = st.number_input(
                "单个接口间隔 (秒)",
                min_value=0.1,
                max_value=10.0,
                value=config.simple_price_update.single_interval_seconds,
                step=0.1,
                help="单个接口调用间隔（避免API限制）"
            )

            items_limit = st.number_input(
                "饰品获取限制",
                min_value=50,
                max_value=1000,
                value=config.simple_price_update.items_limit,
                help="每次获取的饰品数量限制"
            )

            continuous_mode = st.checkbox(
                "持续运行模式",
                value=config.simple_price_update.continuous_mode,
                help="是否启用持续运行模式"
            )

            cycle_interval_minutes = st.number_input(
                "完整轮询间隔 (分钟)",
                min_value=5,
                max_value=1440,
                value=config.simple_price_update.cycle_interval_minutes,
                help="所有饰品同步完成后的等待时间"
            )

    # Steam价格同步服务配置
    with st.expander("🎮 Steam价格同步服务配置", expanded=config.steam_monitor.enabled):
        if not config.steam_monitor.enabled:
            st.warning("⚠️ Steam价格同步服务已禁用")

        col1, col2 = st.columns(2)

        with col1:
            steam_update_interval = st.number_input(
                "同步间隔 (分钟)",
                min_value=5,
                max_value=120,
                value=config.steam_monitor.monitor_interval_minutes,
                help="Steam价格同步的更新间隔"
            )

            steam_batch_size = st.number_input(
                "批量大小",
                min_value=1,
                max_value=100,
                value=config.steam_monitor.batch_size,
                help="Steam价格同步的批量处理大小"
            )

        with col2:
            min_delay = st.number_input(
                "最小延迟 (秒)",
                min_value=1,
                max_value=60,
                value=config.steam_monitor.min_delay_seconds,
                help="Steam请求之间的最小延迟时间"
            )

            max_delay = st.number_input(
                "最大延迟 (秒)",
                min_value=5,
                max_value=120,
                value=config.steam_monitor.max_delay_seconds,
                help="Steam请求之间的最大延迟时间"
            )

        # 新增：代理配置
        st.markdown("#### 代理配置")
        col3, col4 = st.columns(2)

        with col3:
            steam_proxy_enabled = st.checkbox(
                "启用代理",
                value=config.steam_monitor.proxy_enabled,
                help="为Steam监控启用代理服务"
            )

        with col4:
            if steam_proxy_enabled:
                steam_proxy_url = st.text_input(
                    "代理URL",
                    value=config.steam_monitor.proxy_url,
                    help="代理服务器URL，格式：http://host:port"
                )
            else:
                steam_proxy_url = config.steam_monitor.proxy_url

    # 保存按钮
    if st.button("💾 保存饰品价格监控调度器配置", type="primary"):
        new_config = {
            "SIMPLE_PRICE_UPDATE_INTERVAL": update_interval,
            "SIMPLE_PRICE_BATCH_SIZE": simple_batch_size,
            "SIMPLE_PRICE_SINGLE_SIZE": single_size,
            "SIMPLE_PRICE_SINGLE_INTERVAL": single_interval,
            "SIMPLE_PRICE_ITEMS_LIMIT": items_limit,
            "SIMPLE_PRICE_CONTINUOUS_MODE": continuous_mode,
            "SIMPLE_PRICE_CYCLE_INTERVAL": cycle_interval_minutes,
            "STEAM_MONITOR_INTERVAL": steam_update_interval,
            "STEAM_BATCH_SIZE": steam_batch_size,
            "STEAM_MIN_DELAY_SECONDS": min_delay,
            "STEAM_MAX_DELAY_SECONDS": max_delay,
            "STEAM_PROXY_ENABLED": steam_proxy_enabled,
            "STEAM_PROXY_URL": steam_proxy_url,
        }

        with st.spinner("正在保存配置并应用更改..."):
            save_config_with_reload(config_service, new_config, "饰品价格监控调度器配置")


def show_scheduler_config(config: TimerConfig, config_service: ConfigService):
    """显示调度器配置"""
    st.subheader("🔄 调度器全局配置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 基本设置")
        
        scheduler_enabled = st.checkbox(
            "启用调度器",
            value=config.scheduler.enabled,
            help="是否启用整个调度器系统"
        )
        
        startup_delay = st.number_input(
            "启动延迟 (秒)",
            min_value=0,
            max_value=300,
            value=config.scheduler.startup_delay,
            help="调度器启动延迟时间"
        )
        
        shutdown_timeout = st.number_input(
            "关闭超时 (秒)",
            min_value=0,
            max_value=300,
            value=config.scheduler.shutdown_timeout,
            help="调度器关闭超时时间"
        )
    
    with col2:
        st.markdown("#### 高级设置")
        
        timezone = st.selectbox(
            "时区",
            options=["Asia/Shanghai", "UTC", "America/New_York", "Europe/London"],
            index=0 if config.scheduler.timezone == "Asia/Shanghai" else 1,
            help="调度器使用的时区"
        )
        
        log_level = st.selectbox(
            "日志级别",
            options=["DEBUG", "INFO", "WARNING", "ERROR"],
            index=["DEBUG", "INFO", "WARNING", "ERROR"].index(config.scheduler.log_level),
            help="调度器日志输出级别"
        )
    
    # 子调度器开关
    st.markdown("#### 子调度器开关")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        item_info_enabled = st.checkbox(
            "饰品信息更新",
            value=config.item_info_update.enabled,
            help="是否启用饰品信息更新调度器"
        )
    
    with col2:
        simple_price_enabled = st.checkbox(
            "简化价格更新",
            value=config.simple_price_update.enabled,
            help="是否启用简化价格更新调度器"
        )
        
        steamdt_id_enabled = st.checkbox(
            "SteamDT ID更新",
            value=config.steamdt_id_update.enabled,
            help="是否启用SteamDT ID更新调度器"
        )
    
    with col3:
        steam_monitor_enabled = st.checkbox(
            "Steam监控",
            value=config.steam_monitor.enabled,
            help="是否启用Steam价格监控"
        )
    
    # 保存按钮
    if st.button("💾 保存调度器配置", type="primary"):
        new_config = {
            "SCHEDULER_ENABLED": scheduler_enabled,
            "SCHEDULER_STARTUP_DELAY": startup_delay,
            "SCHEDULER_SHUTDOWN_TIMEOUT": shutdown_timeout,
            "SCHEDULER_TIMEZONE": timezone,
            "SCHEDULER_LOG_LEVEL": log_level,
            "ITEM_INFO_UPDATE_ENABLED": item_info_enabled,
            "SIMPLE_PRICE_UPDATE_ENABLED": simple_price_enabled,
            "STEAMDT_ID_UPDATE_ENABLED": steamdt_id_enabled,
            "STEAM_MONITOR_ENABLED": steam_monitor_enabled,
        }

        with st.spinner("正在保存配置并应用更改..."):
            save_config_with_reload(config_service, new_config, "调度器配置", require_confirmation=True)


def show_unified_scheduler_config(config: TimerConfig, config_service: ConfigService):
    """显示饰品基础信息同步调度器配置"""
    st.subheader("⚡ 饰品基础信息同步调度器配置")

    # 显示调度器状态
    if not config.scheduler.enabled:
        st.warning("⚠️ 全局调度器已禁用，此配置不会生效")

    st.info("💡 饰品基础信息同步调度器包含：SteamDT饰品同步服务 + SteamDT饰品信息同步服务")

    # SteamDT饰品同步服务配置
    with st.expander("📦 SteamDT饰品同步服务配置", expanded=config.item_info_update.enabled):
        if not config.item_info_update.enabled:
            st.warning("⚠️ SteamDT饰品同步服务已禁用")

        col1, col2 = st.columns(2)

        with col1:
            cron_schedule = st.text_input(
                "执行时间 (Cron表达式)",
                value=config.item_info_update.cron_schedule,
                help="使用Cron表达式设置执行时间，默认每天凌晨2点"
            )

            health_check_interval = st.number_input(
                "健康检查间隔 (分钟)",
                min_value=1,
                max_value=1440,
                value=config.item_info_update.health_check_interval,
                help="健康检查的间隔时间"
            )

        with col2:
            max_retry = st.number_input(
                "最大重试次数",
                min_value=0,
                max_value=10,
                value=config.item_info_update.max_retry_attempts,
                help="任务失败时的最大重试次数"
            )


    # SteamDT饰品信息同步服务配置
    with st.expander("🆔 SteamDT饰品信息同步服务配置", expanded=config.steamdt_id_update.enabled):
        if not config.steamdt_id_update.enabled:
            st.warning("⚠️ SteamDT饰品信息同步服务已禁用")

        col1, col2 = st.columns(2)

        with col1:
            cycle_interval = st.number_input(
                "同步间隔 (分钟)",
                min_value=5,
                max_value=1440,
                value=config.steamdt_id_update.cycle_interval_minutes,
                help="SteamDT饰品信息同步的轮询间隔"
            )

            steamdt_batch_size = st.number_input(
                "批量大小",
                min_value=1,
                max_value=100,
                value=config.steamdt_id_update.batch_size,
                help="SteamDT饰品信息同步的批量处理大小"
            )

        with col2:
            min_wait = st.number_input(
                "最小等待时间 (秒)",
                min_value=1,
                max_value=60,
                value=config.steamdt_id_update.min_wait_seconds,
                help="请求之间的最小等待时间"
            )

            max_wait = st.number_input(
                "最大等待时间 (秒)",
                min_value=5,
                max_value=120,
                value=config.steamdt_id_update.max_wait_seconds,
                help="请求之间的最大等待时间"
            )

        # 新增：高级配置
        st.markdown("#### 高级配置")
        col3, col4 = st.columns(2)

        with col3:
            continuous_mode = st.checkbox(
                "持续运行模式",
                value=config.steamdt_id_update.continuous_mode,
                help="启用持续运行模式，自动循环处理"
            )

            max_retry = st.number_input(
                "最大重试次数",
                min_value=1,
                max_value=10,
                value=config.steamdt_id_update.max_retry_attempts,
                help="单个饰品处理失败时的最大重试次数"
            )

        with col4:
            health_check_interval = st.number_input(
                "健康检查间隔 (秒)",
                min_value=30,
                max_value=300,
                value=config.steamdt_id_update.health_check_interval,
                help="调度器健康检查的间隔时间"
            )

    # 保存按钮
    if st.button("💾 保存饰品基础信息同步调度器配置", type="primary"):
        new_config = {
            "ITEM_INFO_UPDATE_CRON": cron_schedule,
            "ITEM_INFO_HEALTH_CHECK_INTERVAL": health_check_interval,
            "ITEM_INFO_MAX_RETRY": max_retry,
            "STEAMDT_ID_CYCLE_INTERVAL": cycle_interval,
            "STEAMDT_ID_BATCH_SIZE": steamdt_batch_size,
            "STEAMDT_ID_MIN_WAIT_SECONDS": min_wait,
            "STEAMDT_ID_MAX_WAIT_SECONDS": max_wait,
            "STEAMDT_ID_CONTINUOUS_MODE": continuous_mode,
            "STEAMDT_ID_MAX_RETRY": max_retry,
            "STEAMDT_ID_HEALTH_CHECK_INTERVAL": health_check_interval,
        }

        with st.spinner("正在保存配置并应用更改..."):
            save_config_with_reload(config_service, new_config, "饰品基础信息同步调度器配置")


def show_basic_config(config: TimerConfig, config_service: ConfigService):
    """显示基础配置"""
    st.subheader("🌐 基础配置")

    st.info("💡 SteamDT API基础配置，影响所有调度器的API调用")

    # API配置
    with st.expander("🔑 SteamDT API配置", expanded=True):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### API密钥")

            # 显示当前API密钥状态
            if config.api.steamdt_api_key:
                st.success(f"✅ API密钥已配置: {config.api.steamdt_api_key[:10]}...")
            else:
                st.error("❌ 未配置API密钥")

            new_api_key = st.text_input(
                "新API密钥",
                value="",
                type="password",
                help="输入新的SteamDT API密钥（留空则不更改）"
            )

        with col2:
            st.markdown("#### API参数")

            api_timeout = st.number_input(
                "请求超时 (秒)",
                min_value=5,
                max_value=120,
                value=config.api.api_timeout,
                help="API请求超时时间"
            )

            max_retries = st.number_input(
                "最大重试次数",
                min_value=0,
                max_value=10,
                value=config.api.max_retries,
                help="API请求失败时的最大重试次数"
            )

            retry_delay = st.number_input(
                "重试延迟 (秒)",
                min_value=0.1,
                max_value=10.0,
                value=config.api.retry_delay,
                step=0.1,
                help="重试请求之间的延迟时间"
            )

    # 保存按钮
    if st.button("💾 保存基础配置", type="primary"):
        new_config = {
            "API_TIMEOUT": api_timeout,
            "API_MAX_RETRIES": max_retries,
            "API_RETRY_DELAY": retry_delay,
        }

        # 只有输入了新密钥才更新
        if new_api_key.strip():
            new_config["STEAMDT_API_KEY"] = new_api_key.strip()

        with st.spinner("正在保存配置并应用更改..."):
            save_config_with_reload(config_service, new_config, "基础配置")


def show_analysis_config(config: TimerConfig, config_service: ConfigService):
    """显示饰品分析配置"""
    st.subheader("📊 饰品分析配置")

    st.info("💡 数据抓取方式配置，用于饰品数据的获取和分析")

    # 数据抓取配置
    with st.expander("� 数据抓取配置", expanded=True):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### 抓取方式")

            scraping_method = st.selectbox(
                "抓取方式",
                ["api", "playwright"],
                index=0 if config.scraping.scraping_method == "api" else 1,
                help="选择数据抓取方式"
            )

            # 只有API方式才显示代理配置
            if scraping_method == "api":
                proxy_enabled = st.checkbox(
                    "启用代理",
                    value=config.scraping.proxy_enabled,
                    help="API方式需要代理访问SteamDT服务"
                )

                if proxy_enabled:
                    proxy_url = st.text_input(
                        "代理URL",
                        value=config.scraping.proxy_url,
                        help="代理服务器URL，格式：http://host:port"
                    )
            else:
                # Playwright方式不需要代理
                st.info("💡 Playwright方式不需要配置代理")
                proxy_enabled = False
                proxy_url = ""

        with col2:
            st.markdown("#### 高级配置")

            api_fallback_enabled = st.checkbox(
                "API回退机制",
                value=config.scraping.api_fallback_enabled,
                help="当主要抓取方式失败时，自动切换到备用方式"
            )

            data_validation_enabled = st.checkbox(
                "数据验证",
                value=config.scraping.data_validation_enabled,
                help="启用抓取数据的完整性验证"
            )

        # 超时和重试配置
        st.markdown("#### 超时和重试配置")
        col3, col4 = st.columns(2)

        with col3:
            api_timeout = st.number_input(
                "API超时 (秒)",
                min_value=30,
                max_value=1200,
                value=config.scraping.api_timeout,
                help="API请求的超时时间"
            )

            max_retry_attempts = st.number_input(
                "最大重试次数",
                min_value=1,
                max_value=10,
                value=config.scraping.max_retry_attempts,
                help="请求失败时的最大重试次数"
            )

        with col4:
            retry_delay = st.number_input(
                "重试延迟 (秒)",
                min_value=0.5,
                max_value=10.0,
                value=config.scraping.retry_delay,
                step=0.5,
                help="重试之间的延迟时间"
            )

            playwright_timeout = st.number_input(
                "Playwright超时 (秒)",
                min_value=10,
                max_value=300,
                value=config.scraping.playwright_timeout,
                help="Playwright操作超时时间"
            )

        # Playwright配置
        st.markdown("#### Playwright配置")
        playwright_headless = st.checkbox(
            "无头模式",
            value=config.scraping.playwright_headless,
            help="Playwright是否使用无头模式"
        )

    # 保存按钮
    if st.button("💾 保存饰品分析配置", type="primary"):
        new_config = {
            "SCRAPING_METHOD": scraping_method,
            "SCRAPING_API_FALLBACK_ENABLED": api_fallback_enabled,
            "SCRAPING_DATA_VALIDATION_ENABLED": data_validation_enabled,
            "SCRAPING_API_TIMEOUT": api_timeout,
            "SCRAPING_MAX_RETRY_ATTEMPTS": max_retry_attempts,
            "SCRAPING_RETRY_DELAY": retry_delay,
            "SCRAPING_PLAYWRIGHT_HEADLESS": playwright_headless,
            "SCRAPING_PLAYWRIGHT_TIMEOUT": playwright_timeout,
        }

        # 根据抓取方式设置代理配置
        if scraping_method == "api":
            new_config["SCRAPING_PROXY_ENABLED"] = proxy_enabled
            if proxy_enabled and 'proxy_url' in locals():
                new_config["SCRAPING_PROXY_URL"] = proxy_url
        else:
            # Playwright方式禁用代理
            new_config["SCRAPING_PROXY_ENABLED"] = False

        with st.spinner("正在保存配置并应用更改..."):
            save_config_with_reload(config_service, new_config, "饰品分析配置")


def show_config_management(config: TimerConfig, config_service: ConfigService):
    """显示配置管理"""

    # 添加配置帮助
    show_config_help()

    st.divider()

    # 配置文件管理
    st.subheader("💾 配置文件管理")

    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 配置操作")
        
        if st.button("🔄 重载本地配置", help="重新加载本地配置文件"):
            try:
                reload_timer_config()
                st.success("✅ 本地配置已重载")
                st.rerun()
            except Exception as e:
                st.error(f"❌ 重载本地配置失败: {e}")

        if st.button("🚀 通过API重载配置", help="通过API重载后端配置"):
            with st.spinner("正在通过API重载配置..."):
                result = config_service.reload_config_via_api()
                if result.get("success"):
                    st.success("✅ 后端配置重载成功")
                    if result.get("warnings"):
                        for warning in result["warnings"]:
                            st.warning(f"⚠️ {warning}")
                else:
                    st.error(f"❌ 后端配置重载失败: {result.get('error', '未知错误')}")
        
        if st.button("📋 导出配置", help="导出当前配置为JSON"):
            config_dict = config_service.export_config()
            st.download_button(
                label="💾 下载配置文件",
                data=json.dumps(config_dict, indent=2, ensure_ascii=False),
                file_name=f"timer_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )
        
        if st.button("🔧 生成.env模板", help="生成环境变量模板"):
            template = config_service.generate_env_template()
            st.download_button(
                label="💾 下载.env模板",
                data=template,
                file_name=".env.template",
                mime="text/plain"
            )
    
    with col2:
        st.markdown("#### 配置状态")

        # 显示API服务状态
        api_available = config_service.check_api_service_available()
        if api_available:
            st.success("✅ API服务已连接")

            # 获取API配置状态
            api_status = config_service.get_config_status_via_api()
            if api_status.get("success") != False:
                st.caption("后端配置状态正常")
            else:
                st.warning(f"⚠️ 后端配置状态异常: {api_status.get('error', '未知错误')}")
        else:
            st.error("❌ API服务未连接")
            st.caption("配置更改将无法立即生效")

        # 显示配置文件状态
        env_file = Path(".env")
        if env_file.exists():
            st.success(f"✅ .env文件存在 ({env_file.stat().st_size} bytes)")
            st.caption(f"最后修改: {datetime.fromtimestamp(env_file.stat().st_mtime)}")
        else:
            st.warning("⚠️ .env文件不存在")

        # 显示配置验证结果
        validation_result = config.validate_all()
        if validation_result['valid']:
            st.success("✅ 配置验证通过")
        else:
            st.error("❌ 配置验证失败")
            for error in validation_result['errors']:
                st.error(f"• {error}")

        if validation_result['warnings']:
            st.warning("⚠️ 配置警告:")
            for warning in validation_result['warnings']:
                st.warning(f"• {warning}")
    
    # 配置导入
    st.markdown("#### 配置导入")
    uploaded_file = st.file_uploader(
        "上传配置文件",
        type=['json', 'env'],
        help="支持JSON格式的配置文件或.env格式的环境变量文件"
    )
    
    if uploaded_file is not None:
        try:
            if uploaded_file.name.endswith('.json'):
                config_data = json.loads(uploaded_file.read().decode('utf-8'))
                if st.button("📥 导入JSON配置"):
                    if config_service.import_config(config_data):
                        st.success("✅ 配置导入成功")
                        st.info("💡 请重载配置以应用更改")
                    else:
                        st.error("❌ 配置导入失败")
            else:
                st.info("💡 .env文件导入功能开发中...")
        except Exception as e:
            st.error(f"❌ 解析配置文件失败: {e}")

    # 添加分隔线
    st.divider()

    # 配置备份管理
    show_config_backup_management(config_service)
