#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品实时分析系统 - 快速启动脚本
提供简单易用的交互式界面
"""

import sys
import os
from datetime import datetime

def show_welcome():
    """显示欢迎界面"""
    print("=" * 80)
    print("🎯 CS2饰品实时分析系统 (ssync)")
    print("=" * 80)
    print("📊 专业技术指标分析 | 🎨 图表可视化 | 🚨 异常检测")
    print("基于《技术分析在CS2虚拟饰品市场有效性的实证研究》报告优化")
    print("=" * 80)

def show_menu():
    """显示主菜单"""
    print("\n📋 功能菜单:")
    print("1. 🎯 单饰品实时分析")
    print("2. 📊 生成技术指标图表")
    print("3. 🔍 多饰品对比分析")
    print("4. 🚨 异常检测分析")
    print("5. 💰 投资决策分析")
    print("6. 📈 图表化分析演示")
    print("7. 📖 查看使用文档")
    print("8. 🧪 系统测试")
    print("0. 🚪 退出系统")
    print("-" * 50)

def get_available_skins():
    """获取可用的饰品列表"""
    skins = []
    parent_dir = os.path.dirname(os.getcwd())
    
    for item in os.listdir(parent_dir):
        item_path = os.path.join(parent_dir, item)
        if os.path.isdir(item_path) and item not in ['ssync', 'syncps', '__pycache__']:
            # 检查是否有时k.json文件
            hourly_file = os.path.join(item_path, '时k.json')
            if os.path.exists(hourly_file):
                skins.append(item)
    
    return skins

def select_skin(skins):
    """选择饰品"""
    if not skins:
        print("❌ 未找到可用的饰品数据")
        return None
    
    print("\n📦 可用饰品:")
    for i, skin in enumerate(skins, 1):
        print(f"{i}. {skin}")
    
    while True:
        try:
            choice = input(f"\n请选择饰品 (1-{len(skins)}): ").strip()
            if choice.isdigit():
                index = int(choice) - 1
                if 0 <= index < len(skins):
                    return skins[index]
            print("❌ 无效选择，请重新输入")
        except KeyboardInterrupt:
            return None

def single_skin_analysis():
    """单饰品实时分析"""
    print("\n🎯 单饰品实时分析")
    print("-" * 40)
    
    skins = get_available_skins()
    skin_name = select_skin(skins)
    
    if skin_name:
        try:
            from real_time_monitor import FixedRealTimeMonitor
            
            print(f"\n📊 正在分析 {skin_name}...")
            monitor = FixedRealTimeMonitor(skin_name)
            
            if monitor.load_data():
                monitor.print_fixed_dashboard()
                print("\n✅ 分析完成")
            else:
                print("❌ 数据加载失败")
                
        except Exception as e:
            print(f"❌ 分析失败: {e}")

def generate_chart():
    """生成技术指标图表"""
    print("\n📊 生成技术指标图表")
    print("-" * 40)
    
    skins = get_available_skins()
    skin_name = select_skin(skins)
    
    if skin_name:
        try:
            from real_time_monitor import FixedRealTimeMonitor
            
            print(f"\n🎨 正在为 {skin_name} 生成图表...")
            monitor = FixedRealTimeMonitor(skin_name)
            
            if monitor.load_data():
                chart_path = monitor.generate_technical_chart()
                if chart_path:
                    print(f"✅ 图表已保存: {chart_path}")
                else:
                    print("❌ 图表生成失败")
            else:
                print("❌ 数据加载失败")
                
        except Exception as e:
            print(f"❌ 图表生成失败: {e}")

def multi_skin_analysis():
    """多饰品对比分析"""
    print("\n🔍 多饰品对比分析")
    print("-" * 40)
    
    try:
        print("📊 正在运行三饰品对比分析...")
        os.system("python 三饰品分析.py")
        print("✅ 对比分析完成")
    except Exception as e:
        print(f"❌ 对比分析失败: {e}")

def anomaly_detection():
    """异常检测分析"""
    print("\n🚨 异常检测分析")
    print("-" * 40)
    
    skins = get_available_skins()
    skin_name = select_skin(skins)
    
    if skin_name:
        try:
            from real_time_monitor import FixedRealTimeMonitor
            
            print(f"\n🔍 正在检测 {skin_name} 的异常...")
            monitor = FixedRealTimeMonitor(skin_name)
            
            if monitor.load_data():
                # 获取异常检测结果
                anomalies = monitor.get_24h_anomalies()
                
                print(f"\n📊 异常检测结果:")
                print(f"总异常数: {anomalies['total_anomalies']}")
                
                high_risk = len(anomalies['by_severity']['HIGH'])
                medium_risk = len(anomalies['by_severity']['MEDIUM'])
                low_risk = len(anomalies['by_severity']['LOW'])
                
                print(f"高风险: {high_risk}个")
                print(f"中风险: {medium_risk}个")
                print(f"低风险: {low_risk}个")
                
                # 成交量异常检测
                volume_anomaly = monitor.detect_volume_anomaly_real_time()
                print(f"\n📊 成交量状态: {volume_anomaly['alert']}")
                print(f"成交量比率: {volume_anomaly['ratio']:.2f}倍")
                
                print("\n✅ 异常检测完成")
            else:
                print("❌ 数据加载失败")
                
        except Exception as e:
            print(f"❌ 异常检测失败: {e}")

def investment_analysis():
    """投资决策分析"""
    print("\n💰 投资决策分析")
    print("-" * 40)
    
    skins = get_available_skins()
    skin_name = select_skin(skins)
    
    if skin_name:
        try:
            # 获取投资金额
            while True:
                try:
                    amount = input("💵 请输入投资金额 (默认1000): ").strip()
                    if not amount:
                        amount = 1000
                    else:
                        amount = float(amount)
                    break
                except ValueError:
                    print("❌ 请输入有效的数字")
            
            from real_time_monitor import FixedRealTimeMonitor
            
            print(f"\n📊 正在分析 {skin_name} 的投资机会...")
            monitor = FixedRealTimeMonitor(skin_name)
            
            if monitor.load_data():
                # 获取关键指标
                current_price = monitor.hourly_data['close'].iloc[-1]
                rsi = monitor._calculate_rsi_real_time().iloc[-1]
                enhanced_signals = monitor.generate_enhanced_real_time_signals()
                risk_mgmt = monitor.analyze_risk_management()
                
                print(f"\n📊 投资分析结果:")
                print(f"当前价格: ¥{current_price:.2f}")
                print(f"RSI指标: {rsi:.1f}")
                print(f"主要信号: {enhanced_signals['signal']} (置信度: {enhanced_signals['confidence']:.1f}%)")
                print(f"风险收益比: 1:{risk_mgmt['risk_reward_ratio']:.2f}")
                
                # 简单投资建议
                if enhanced_signals['signal'] == 'BUY' and enhanced_signals['confidence'] >= 70:
                    max_position = amount * 0.7
                    quantity = int(max_position / current_price)
                    print(f"\n💡 投资建议: 适度买入")
                    print(f"建议仓位: ¥{max_position:.2f} (约{quantity}件)")
                    print(f"止损价格: ¥{risk_mgmt['stop_loss']:.2f}")
                    print(f"目标价格: ¥{risk_mgmt['target_price']:.2f}")
                elif enhanced_signals['signal'] == 'SELL':
                    print(f"\n💡 投资建议: 建议观望或减仓")
                else:
                    print(f"\n💡 投资建议: 继续观察，等待更明确信号")
                
                print("\n✅ 投资分析完成")
            else:
                print("❌ 数据加载失败")
                
        except Exception as e:
            print(f"❌ 投资分析失败: {e}")

def chart_demo():
    """图表化分析演示"""
    print("\n📈 图表化分析演示")
    print("-" * 40)
    
    try:
        print("🎨 正在运行图表化分析演示...")
        os.system("python 图表化分析演示.py")
        print("✅ 演示完成")
    except Exception as e:
        print(f"❌ 演示失败: {e}")

def show_documentation():
    """查看使用文档"""
    print("\n📖 使用文档")
    print("-" * 40)
    print("1. README.md - 系统概述和快速开始")
    print("2. USAGE.md - 详细使用指南")
    print("3. EXAMPLES.md - 使用示例")
    print("\n💡 建议使用文本编辑器或Markdown阅读器查看文档")

def system_test():
    """系统测试"""
    print("\n🧪 系统测试")
    print("-" * 40)
    
    print("📊 正在测试系统功能...")
    
    # 测试数据可用性
    skins = get_available_skins()
    print(f"✅ 发现 {len(skins)} 个可用饰品")
    
    # 测试模块导入
    try:
        from real_time_monitor import FixedRealTimeMonitor
        print("✅ 实时监控模块导入成功")
    except Exception as e:
        print(f"❌ 实时监控模块导入失败: {e}")
        return
    
    try:
        from 技术指标图表生成器 import TechnicalChartGenerator
        print("✅ 图表生成模块导入成功")
    except Exception as e:
        print(f"❌ 图表生成模块导入失败: {e}")
    
    # 测试数据加载
    if skins:
        test_skin = skins[0]
        try:
            monitor = FixedRealTimeMonitor(test_skin)
            if monitor.load_data():
                print(f"✅ 数据加载测试成功 ({test_skin})")
                print(f"   数据长度: {len(monitor.hourly_data)}条")
            else:
                print(f"❌ 数据加载测试失败 ({test_skin})")
        except Exception as e:
            print(f"❌ 数据加载测试异常: {e}")
    
    print("\n✅ 系统测试完成")

def main():
    """主程序"""
    show_welcome()
    
    while True:
        try:
            show_menu()
            choice = input("请选择功能 (0-8): ").strip()
            
            if choice == '0':
                print("\n👋 感谢使用CS2饰品实时分析系统！")
                break
            elif choice == '1':
                single_skin_analysis()
            elif choice == '2':
                generate_chart()
            elif choice == '3':
                multi_skin_analysis()
            elif choice == '4':
                anomaly_detection()
            elif choice == '5':
                investment_analysis()
            elif choice == '6':
                chart_demo()
            elif choice == '7':
                show_documentation()
            elif choice == '8':
                system_test()
            else:
                print("❌ 无效选择，请重新输入")
            
            input("\n按回车键继续...")
            
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出系统")
            break
        except Exception as e:
            print(f"\n❌ 系统错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
