"""
统一数据预处理标准

提供标准化的数据处理和验证机制，确保所有模块对数据的处理保持一致。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
import json
from loguru import logger


class StandardizedDataProcessor:
    """统一数据处理器"""
    
    @staticmethod
    def process_kline_data(raw_data: List, source: str = "unknown") -> pd.DataFrame:
        """
        处理K线数据，按照steamdt源数据结构说明进行标准化
        
        数据格式：[timestamp, open, close, high, low, volume, amount]
        
        Args:
            raw_data: 原始K线数据列表
            source: 数据源标识
            
        Returns:
            pd.DataFrame: 标准化的K线数据
        """
        try:
            if not raw_data:
                logger.warning(f"K线数据为空: {source}")
                return pd.DataFrame(columns=['datetime', 'open', 'close', 'high', 'low', 'volume', 'amount'])
            
            processed_data = []
            
            for i, item in enumerate(raw_data):
                try:
                    # 验证数据格式
                    if not isinstance(item, (list, tuple)) or len(item) < 5:
                        logger.warning(f"K线数据格式错误 {source}[{i}]: {item}")
                        continue
                    
                    # 标准化字段提取
                    row = {
                        'datetime': StandardizedDataProcessor._parse_datetime(item[0]),
                        'open': StandardizedDataProcessor._safe_float(item[1]),
                        'close': StandardizedDataProcessor._safe_float(item[2]),
                        'high': StandardizedDataProcessor._safe_float(item[3]),
                        'low': StandardizedDataProcessor._safe_float(item[4]),
                        'volume': StandardizedDataProcessor._safe_int(item[5] if len(item) > 5 else None),
                        'amount': StandardizedDataProcessor._safe_float(item[6] if len(item) > 6 else None)
                    }
                    
                    # 移除OHLC验证 - 这些数据都是正常的
                    processed_data.append(row)
                        
                except Exception as e:
                    logger.warning(f"处理K线数据项失败 {source}[{i}]: {e}")
                    continue
            
            df = pd.DataFrame(processed_data)
            logger.info(f"K线数据处理完成 {source}: {len(df)}/{len(raw_data)} 条有效记录")
            
            return df
            
        except Exception as e:
            logger.error(f"K线数据处理失败 {source}: {e}")
            return pd.DataFrame(columns=['datetime', 'open', 'close', 'high', 'low', 'volume', 'amount'])
    
    @staticmethod
    def process_trend_data(raw_data: List, source: str = "unknown") -> pd.DataFrame:
        """
        处理走势数据，按照steamdt源数据结构说明进行标准化
        
        数据格式：[timestamp, current_price, supply_quantity, bid_price, demand_quantity, amount, volume, circulation]
        
        Args:
            raw_data: 原始走势数据列表
            source: 数据源标识
            
        Returns:
            pd.DataFrame: 标准化的走势数据
        """
        try:
            if not raw_data:
                logger.warning(f"走势数据为空: {source}")
                return pd.DataFrame(columns=['datetime', 'current_price', 'supply_quantity', 'bid_price', 
                                           'demand_quantity', 'amount', 'volume', 'circulation'])
            
            processed_data = []
            
            for i, item in enumerate(raw_data):
                try:
                    # 验证数据格式
                    if not isinstance(item, (list, tuple)) or len(item) < 8:
                        logger.warning(f"走势数据格式错误 {source}[{i}]: 期望8字段，实际{len(item) if hasattr(item, '__len__') else 'N/A'}")
                        continue
                    
                    # 标准化字段提取
                    row = {
                        'datetime': StandardizedDataProcessor._parse_datetime(item[0]),
                        'current_price': StandardizedDataProcessor._safe_float(item[1]),
                        'supply_quantity': StandardizedDataProcessor._safe_int(item[2]),
                        'bid_price': StandardizedDataProcessor._safe_float(item[3]),
                        'demand_quantity': StandardizedDataProcessor._safe_int(item[4]),
                        'amount': StandardizedDataProcessor._safe_float(item[5]),
                        'volume': StandardizedDataProcessor._safe_int(item[6]),
                        'circulation': StandardizedDataProcessor._safe_string(item[7])
                    }
                    
                    # 验证数据合理性
                    if StandardizedDataProcessor._validate_trend_data(row):
                        processed_data.append(row)
                    else:
                        logger.warning(f"走势数据验证失败 {source}[{i}]: {row}")
                        
                except Exception as e:
                    logger.warning(f"处理走势数据项失败 {source}[{i}]: {e}")
                    continue
            
            df = pd.DataFrame(processed_data)
            logger.info(f"走势数据处理完成 {source}: {len(df)}/{len(raw_data)} 条有效记录")
            
            return df
            
        except Exception as e:
            logger.error(f"走势数据处理失败 {source}: {e}")
            return pd.DataFrame(columns=['datetime', 'current_price', 'supply_quantity', 'bid_price', 
                                       'demand_quantity', 'amount', 'volume', 'circulation'])
    
    @staticmethod
    def merge_daily_kline(daily1_data: List, daily2_data: List) -> pd.DataFrame:
        """
        合并日K数据，按时间排序
        
        Args:
            daily1_data: 日k1数据
            daily2_data: 日k2数据
            
        Returns:
            pd.DataFrame: 合并后的日K数据
        """
        try:
            # 合并原始数据
            merged_raw = daily1_data + daily2_data
            
            # 按时间戳排序
            merged_raw.sort(key=lambda x: x[0] if x and len(x) > 0 else "0")
            
            # 使用标准化处理
            return StandardizedDataProcessor.process_kline_data(merged_raw, "daily_merged")
            
        except Exception as e:
            logger.error(f"日K数据合并失败: {e}")
            return pd.DataFrame(columns=['datetime', 'open', 'close', 'high', 'low', 'volume', 'amount'])
    
    @staticmethod
    def _parse_datetime(timestamp: Any) -> Optional[datetime]:
        """解析时间戳为datetime对象"""
        try:
            if timestamp is None:
                return None
            
            # 转换为字符串处理
            timestamp_str = str(timestamp).strip()
            
            # Unix时间戳（秒）
            if timestamp_str.isdigit():
                return datetime.fromtimestamp(int(timestamp_str))
            
            # 其他格式可以在这里扩展
            logger.warning(f"无法解析时间戳: {timestamp}")
            return None
            
        except Exception as e:
            logger.warning(f"时间戳解析失败: {timestamp}, 错误: {e}")
            return None
    
    @staticmethod
    def _safe_float(value: Any) -> float:
        """安全转换为float，null值转换为0"""
        try:
            if value is None:
                return 0.0
            return float(value)
        except (ValueError, TypeError):
            return 0.0
    
    @staticmethod
    def _safe_int(value: Any) -> int:
        """安全转换为int，null值转换为0"""
        try:
            if value is None:
                return 0
            return int(float(value))  # 先转float再转int，处理小数字符串
        except (ValueError, TypeError):
            return 0
    
    @staticmethod
    def _safe_string(value: Any) -> str:
        """安全转换为string"""
        try:
            if value is None:
                return "0"
            return str(value)
        except Exception:
            return "0"
    
    @staticmethod
    def _validate_ohlc(row: Dict) -> bool:
        """验证OHLC数据逻辑关系"""
        try:
            open_price = row['open']
            close_price = row['close']
            high_price = row['high']
            low_price = row['low']

            # 检查负价格
            if any(price < 0 for price in [open_price, close_price, high_price, low_price]):
                return False

            # 如果包含0值（由null转换而来），采用宽松验证
            if 0 in [open_price, close_price, high_price, low_price]:
                # 只检查非零值的逻辑关系
                non_zero_prices = [p for p in [open_price, close_price, high_price, low_price] if p > 0]
                if len(non_zero_prices) < 2:
                    return True  # 如果只有一个或没有非零价格，接受该记录

                # 对非零价格进行基本验证
                if high_price > 0 and any(p > high_price for p in non_zero_prices if p != high_price):
                    return False
                if low_price > 0 and any(p < low_price for p in non_zero_prices if p != low_price):
                    return False

                return True

            # 宽松的OHLC逻辑验证（基于您的指正）
            # 最基本的约束：最高价应该大于等于最低价
            if high_price < low_price:
                return False

            # 最高价应该大于等于开盘价和收盘价（这是绝对的）
            if high_price < open_price or high_price < close_price:
                return False

            # 对于最低价，我们采用更宽松的验证
            # 在某些情况下（如跳空高开），最低价可能高于开盘价
            # 但最低价不应该高于收盘价和开盘价的最大值
            if low_price > max(open_price, close_price):
                return False

            return True

        except (KeyError, TypeError, ValueError):
            return False
    
    @staticmethod
    def _validate_trend_data(row: Dict) -> bool:
        """验证走势数据合理性"""
        try:
            # 价格应该为正数
            if row['current_price'] < 0 or row['bid_price'] < 0:
                return False
            
            # 数量应该为非负数
            if row['supply_quantity'] < 0 or row['demand_quantity'] < 0:
                return False
            
            # 求购价不应该高于当前价格太多（防止异常数据）
            if row['bid_price'] > row['current_price'] * 1.5:
                return False
            
            return True
            
        except (KeyError, TypeError, ValueError):
            return False


class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_kline_dataframe(df: pd.DataFrame) -> Dict[str, Any]:
        """验证K线DataFrame的完整性和正确性"""
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }
        
        try:
            # 检查必需列
            required_columns = ['datetime', 'open', 'close', 'high', 'low', 'volume', 'amount']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                validation_result['is_valid'] = False
                validation_result['errors'].append(f"缺少必需列: {missing_columns}")
            
            if df.empty:
                validation_result['warnings'].append("数据为空")
                return validation_result
            
            # 检查数据类型
            if not pd.api.types.is_datetime64_any_dtype(df['datetime']):
                validation_result['warnings'].append("datetime列不是datetime类型")
            
            # 移除OHLC逻辑检查 - 这些数据都是正常的
            
            # 统计信息
            validation_result['statistics'] = {
                'total_records': len(df),
                'date_range': {
                    'start': df['datetime'].min().isoformat() if not df['datetime'].isna().all() else None,
                    'end': df['datetime'].max().isoformat() if not df['datetime'].isna().all() else None
                },
                'price_range': {
                    'min': df['close'].min(),
                    'max': df['close'].max(),
                    'mean': df['close'].mean()
                },
                'null_counts': df.isnull().sum().to_dict()
            }
            
        except Exception as e:
            validation_result['is_valid'] = False
            validation_result['errors'].append(f"验证过程异常: {str(e)}")
        
        return validation_result
    
    @staticmethod
    def validate_trend_dataframe(df: pd.DataFrame) -> Dict[str, Any]:
        """验证走势DataFrame的完整性和正确性"""
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }
        
        try:
            # 检查必需列
            required_columns = ['datetime', 'current_price', 'supply_quantity', 'bid_price', 
                              'demand_quantity', 'amount', 'volume', 'circulation']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                validation_result['is_valid'] = False
                validation_result['errors'].append(f"缺少必需列: {missing_columns}")
            
            if df.empty:
                validation_result['warnings'].append("数据为空")
                return validation_result
            
            # 检查数据合理性
            invalid_records = 0
            for idx, row in df.iterrows():
                if not StandardizedDataProcessor._validate_trend_data(row.to_dict()):
                    invalid_records += 1
            
            if invalid_records > 0:
                validation_result['warnings'].append(f"发现{invalid_records}条数据合理性错误记录")
            
            # 统计信息
            validation_result['statistics'] = {
                'total_records': len(df),
                'date_range': {
                    'start': df['datetime'].min().isoformat() if not df['datetime'].isna().all() else None,
                    'end': df['datetime'].max().isoformat() if not df['datetime'].isna().all() else None
                },
                'price_statistics': {
                    'current_price_range': [df['current_price'].min(), df['current_price'].max()],
                    'bid_price_range': [df['bid_price'].min(), df['bid_price'].max()],
                    'avg_spread': (df['current_price'] - df['bid_price']).mean()
                },
                'supply_demand_statistics': {
                    'avg_supply': df['supply_quantity'].mean(),
                    'avg_demand': df['demand_quantity'].mean(),
                    'avg_ratio': (df['supply_quantity'] / df['demand_quantity'].replace(0, 1)).mean()
                },
                'null_counts': df.isnull().sum().to_dict()
            }
            
        except Exception as e:
            validation_result['is_valid'] = False
            validation_result['errors'].append(f"验证过程异常: {str(e)}")
        
        return validation_result
