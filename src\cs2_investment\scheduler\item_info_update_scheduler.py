"""
饰品信息更新定时器

使用APScheduler框架实现每天定时调用饰品信息更新服务。
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.services.item_info_update_service import ItemInfoUpdateService


class ItemInfoUpdateScheduler:
    """饰品信息更新定时器"""
    
    def __init__(self, api_key: str):
        """
        初始化饰品信息更新定时器
        
        Args:
            api_key: SteamDT API密钥
        """
        self.api_key = api_key
        self.logger = logging.getLogger(__name__)
        
        # 创建后台调度器
        self.scheduler = BackgroundScheduler(
            timezone='Asia/Shanghai',
            job_defaults={
                'coalesce': True,  # 合并错过的任务
                'max_instances': 1,  # 每个任务最多同时运行1个实例
                'misfire_grace_time': 1800  # 错过任务的宽限时间（30分钟）
            }
        )
        
        # 饰品信息更新服务
        self.update_service = ItemInfoUpdateService(api_key)
        
        # 任务状态
        self.is_running = False
        self.task_status = {
            'last_update': None,
            'last_update_result': None,
            'next_update': None,
            'update_count': 0,
            'update_errors': 0,
            'total_items_processed': 0,
            'total_new_items': 0,
            'total_updated_items': 0
        }
        
        # 配置
        self.config = {
            'update_cron': '0 2 * * *',  # 每天凌晨2点执行更新
            'status_check_interval_minutes': 60,  # 状态检查间隔（分钟）
        }
        
        # 设置事件监听器
        self.scheduler.add_listener(self._job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)
    
    def _job_listener(self, event):
        """任务执行监听器"""
        job_id = event.job_id
        
        if event.exception:
            self.logger.error(f"定时任务执行失败: {job_id}, 异常: {event.exception}")
            if 'item_info_update' in job_id:
                self.task_status['update_errors'] += 1
        else:
            self.logger.info(f"定时任务执行成功: {job_id}")
            if 'item_info_update' in job_id:
                self.task_status['update_count'] += 1
    
    async def start(self):
        """启动定时任务调度器"""
        if self.is_running:
            self.logger.warning("饰品信息更新定时器已经在运行")
            return

        try:
            self.logger.info("🚀 启动饰品信息更新定时器")

            # 添加饰品信息更新任务
            self._add_update_jobs()

            # 添加状态检查任务
            self._add_status_check_jobs()

            # 启动调度器
            self.scheduler.start()
            self.is_running = True

            # 显示任务信息
            self._log_scheduled_jobs()

            self.logger.info("✅ 饰品信息更新定时器启动成功")
            
        except Exception as e:
            self.logger.error(f"❌ 启动饰品信息更新定时器失败: {e}")
            raise
    
    def stop(self):
        """停止定时任务调度器"""
        if not self.is_running:
            return

        try:
            self.logger.info("⏹️ 停止饰品信息更新定时器")

            # 停止调度器
            self.scheduler.shutdown(wait=True)
            self.is_running = False
            self.logger.info("✅ 饰品信息更新定时器已停止")
        except Exception as e:
            self.logger.error(f"❌ 停止饰品信息更新定时器失败: {e}")
    
    def _add_update_jobs(self):
        """添加饰品信息更新任务"""
        # 每天凌晨2点执行饰品信息更新
        self.scheduler.add_job(
            func=self._run_item_info_update,
            trigger=CronTrigger.from_crontab(self.config['update_cron']),
            id='item_info_update_daily',
            name='每日饰品信息更新',
            replace_existing=True
        )
        
        self.logger.info(f"📅 已添加每日饰品信息更新任务: {self.config['update_cron']}")
    
    def _add_status_check_jobs(self):
        """添加状态检查任务"""
        # 每小时检查一次状态
        self.scheduler.add_job(
            func=self._check_status,
            trigger='interval',
            minutes=self.config['status_check_interval_minutes'],
            id='item_info_status_check',
            name='饰品信息更新状态检查',
            replace_existing=True
        )
        
        self.logger.info(f"📊 已添加状态检查任务: 每{self.config['status_check_interval_minutes']}分钟")
    
    def _run_item_info_update(self):
        """执行饰品信息更新任务"""
        self.logger.info("🔄 开始执行饰品信息更新任务")
        
        try:
            # 记录开始时间
            start_time = datetime.now()
            self.task_status['last_update'] = start_time
            
            # 在新的事件循环中运行异步任务
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                result = loop.run_until_complete(self.update_service.update_item_info())
                
                # 更新任务状态
                self.task_status['last_update_result'] = result
                
                if result.get('success'):
                    self.task_status['total_items_processed'] += result.get('total_items', 0)
                    self.task_status['total_new_items'] += result.get('new_items', 0)
                    self.task_status['total_updated_items'] += result.get('updated_items', 0)
                    
                    self.logger.info(f"✅ 饰品信息更新完成: 新增{result.get('new_items', 0)}个，更新{result.get('updated_items', 0)}个")
                else:
                    self.logger.error(f"❌ 饰品信息更新失败: {result.get('error', 'Unknown error')}")
                    
            finally:
                loop.close()
                
            # 计算下次执行时间
            self._calculate_next_update_time()
            
        except Exception as e:
            self.logger.error(f"❌ 执行饰品信息更新任务异常: {e}")
            self.task_status['update_errors'] += 1
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
    
    def _check_status(self):
        """检查任务状态"""
        try:
            self.logger.debug("📊 检查饰品信息更新状态")
            
            # 获取调度器状态
            jobs = self.scheduler.get_jobs()
            running_jobs = [job for job in jobs if job.next_run_time]
            
            self.logger.debug(f"当前活跃任务数: {len(running_jobs)}")
            
            # 记录统计信息
            if self.task_status['update_count'] > 0:
                success_rate = (self.task_status['update_count'] / 
                              (self.task_status['update_count'] + self.task_status['update_errors'])) * 100
                self.logger.debug(f"更新成功率: {success_rate:.1f}%")
                
        except Exception as e:
            self.logger.warning(f"状态检查异常: {e}")
    
    def _calculate_next_update_time(self):
        """计算下次更新时间"""
        try:
            jobs = self.scheduler.get_jobs()
            update_job = next((job for job in jobs if job.id == 'item_info_update_daily'), None)
            
            if update_job and update_job.next_run_time:
                self.task_status['next_update'] = update_job.next_run_time
                self.logger.info(f"⏰ 下次饰品信息更新时间: {update_job.next_run_time}")
                
        except Exception as e:
            self.logger.warning(f"计算下次更新时间失败: {e}")
    
    def _log_scheduled_jobs(self):
        """记录已调度的任务"""
        try:
            jobs = self.scheduler.get_jobs()
            self.logger.info(f"📋 已调度任务数量: {len(jobs)}")
            
            for job in jobs:
                self.logger.info(f"   - {job.name} (ID: {job.id})")
                if job.next_run_time:
                    self.logger.info(f"     下次执行: {job.next_run_time}")
                    
        except Exception as e:
            self.logger.warning(f"记录调度任务失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取定时器状态"""
        try:
            jobs = self.scheduler.get_jobs()
            
            return {
                'is_running': self.is_running,
                'scheduled_jobs': len(jobs),
                'task_status': self.task_status.copy(),
                'config': self.config.copy(),
                'next_jobs': [
                    {
                        'id': job.id,
                        'name': job.name,
                        'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None
                    }
                    for job in jobs if job.next_run_time
                ]
            }
        except Exception as e:
            self.logger.error(f"获取状态失败: {e}")
            return {
                'is_running': self.is_running,
                'error': str(e)
            }
    
    async def run_update_now(self) -> Dict[str, Any]:
        """立即执行一次更新（用于测试）"""
        self.logger.info("🔄 手动触发饰品信息更新")
        
        try:
            result = await self.update_service.update_item_info()
            self.logger.info(f"✅ 手动更新完成: {result}")
            return result
        except Exception as e:
            self.logger.error(f"❌ 手动更新失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
