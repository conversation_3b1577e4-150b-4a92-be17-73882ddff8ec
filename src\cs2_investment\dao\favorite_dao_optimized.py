"""
优化的收藏DAO - 使用正确的SQL JOIN查询

这个实现解决了原有设计的根本问题：
1. 一条SQL查询获取所有数据
2. 避免N+1查询问题
3. 在数据库层面进行筛选
"""

from typing import List, Dict, Any, Optional
from sqlalchemy import and_, func, desc, text
from src.cs2_investment.config.database import get_db_session
from src.cs2_investment.models.favorite import Favorite
from src.cs2_investment.models.item import Item
from src.cs2_investment.models.platform_price import PlatformPrice


class FavoriteDaoOptimized:
    """优化的收藏DAO - 使用正确的SQL设计"""
    
    def get_user_favorites_with_complete_data(self,
                                            user_id: str,
                                            name_query: Optional[str] = None,
                                            item_types: Optional[List[str]] = None,
                                            qualities: Optional[List[str]] = None,
                                            rarities: Optional[List[str]] = None,
                                            price_min: Optional[float] = None,
                                            price_max: Optional[float] = None,
                                            sell_count_min: Optional[int] = None,
                                            sell_count_max: Optional[int] = None,
                                            arbitrage_threshold: Optional[float] = None,
                                            arbitrage_card_price: Optional[float] = None,
                                            sort_by: str = 'created_desc',
                                            limit: int = 50,
                                            offset: int = 0) -> List[Dict[str, Any]]:
        """
        一条SQL查询获取用户收藏的完整数据
        
        这是正确的实现方式：
        1. 使用JOIN关联所有相关表
        2. 在SQL层面进行筛选
        3. 一次性返回完整数据
        """
        
        print(f"🚀 [正确实现] 使用单条SQL查询收藏数据")
        print(f"   👤 user_id: {user_id}")
        print(f"   🔍 筛选条件: name={name_query}, types={item_types}, sell_count_min={sell_count_min}")
        
        try:
            with get_db_session() as session:
                # 构建核心SQL查询
                sql_query = """
                SELECT 
                    -- 收藏信息
                    f.id as favorite_id,
                    f.user_id,
                    f.item_id,
                    f.item_name as favorite_item_name,
                    f.created_at as favorite_created_at,
                    f.updated_at as favorite_updated_at,
                    f.notes as favorite_notes,
                    
                    -- 饰品基础信息
                    i.name as item_name,
                    i.item_type,
                    i.quality,
                    i.rarity,
                    i.exterior,
                    i.image_url,
                    i.market_hash_name,
                    i.arbitrage_ratio,
                    i.last_price_update,
                    
                    -- 平台价格信息（聚合）- 使用平台名称+数据源作为唯一标识
                    GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'))) as platforms,
                    GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'), ':', COALESCE(pp.sell_price, 0))) as sell_prices,
                    GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'), ':', COALESCE(pp.sell_count, 0))) as sell_counts,
                    GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'), ':', COALESCE(pp.bidding_price, 0))) as bidding_prices,
                    GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'), ':', COALESCE(pp.bidding_count, 0))) as bidding_counts,
                    GROUP_CONCAT(DISTINCT CONCAT(pp.platform_name, '|', COALESCE(pp.data_source, 'steamdt'), ':', COALESCE(pp.steamdt_update_time, ''))) as update_times,
                    
                    -- 计算总在售量（用于筛选）
                    COALESCE(SUM(CASE WHEN pp.data_source = 'steamdt' THEN pp.sell_count ELSE 0 END), 0) as total_sell_count,
                    
                    -- 获取主要平台价格（用于价格筛选）
                    MAX(CASE WHEN UPPER(pp.platform_name) = 'BUFF' THEN pp.sell_price END) as buff_price,
                    MAX(CASE WHEN UPPER(pp.platform_name) = 'YOUPIN' THEN pp.sell_price END) as youpin_price,

                    -- 计算求购利润相关字段
                    MAX(CASE WHEN UPPER(pp.platform_name) = 'STEAM' AND pp.data_source = 'steam_direct' THEN pp.bidding_price END) as steam_direct_bidding_price,
                    LEAST(
                        COALESCE(MAX(CASE WHEN UPPER(pp.platform_name) = 'BUFF' THEN pp.sell_price END), 999999),
                        COALESCE(MAX(CASE WHEN UPPER(pp.platform_name) = 'YOUPIN' THEN pp.sell_price END), 999999)
                    ) as min_sell_price
                    
                FROM favorites f
                INNER JOIN items i ON f.item_id = i.item_id
                LEFT JOIN platform_prices pp ON i.item_id = pp.item_id AND pp.is_active = 1
                
                WHERE f.user_id = :user_id
                """
                
                # 构建参数字典
                params = {'user_id': user_id}
                
                # 添加筛选条件
                if name_query:
                    sql_query += " AND (i.name LIKE :name_query OR i.market_hash_name LIKE :name_query)"
                    params['name_query'] = f"%{name_query}%"
                
                if item_types:
                    placeholders = ','.join([f":item_type_{i}" for i in range(len(item_types))])
                    sql_query += f" AND i.item_type IN ({placeholders})"
                    for i, item_type in enumerate(item_types):
                        params[f'item_type_{i}'] = item_type
                
                if qualities:
                    placeholders = ','.join([f":quality_{i}" for i in range(len(qualities))])
                    sql_query += f" AND i.quality IN ({placeholders})"
                    for i, quality in enumerate(qualities):
                        params[f'quality_{i}'] = quality
                
                if rarities:
                    placeholders = ','.join([f":rarity_{i}" for i in range(len(rarities))])
                    sql_query += f" AND i.rarity IN ({placeholders})"
                    for i, rarity in enumerate(rarities):
                        params[f'rarity_{i}'] = rarity
                
                if arbitrage_threshold is not None:
                    sql_query += " AND i.arbitrage_ratio >= :arbitrage_threshold"
                    params['arbitrage_threshold'] = arbitrage_threshold
                
                # GROUP BY子句
                sql_query += """
                GROUP BY f.id, f.user_id, f.item_id, f.item_name, f.created_at, f.updated_at, f.notes,
                         i.name, i.item_type, i.quality, i.rarity, i.exterior, i.image_url, 
                         i.market_hash_name, i.arbitrage_ratio, i.last_price_update
                """
                
                # HAVING子句（用于聚合后的筛选）
                having_conditions = []
                
                if price_min is not None or price_max is not None:
                    price_condition = []
                    if price_min is not None:
                        price_condition.append("(buff_price >= :price_min OR youpin_price >= :price_min)")
                        params['price_min'] = price_min
                    if price_max is not None:
                        price_condition.append("(buff_price <= :price_max OR youpin_price <= :price_max)")
                        params['price_max'] = price_max
                    
                    if price_condition:
                        having_conditions.append(f"({' AND '.join(price_condition)})")
                
                if sell_count_min is not None:
                    having_conditions.append("total_sell_count >= :sell_count_min")
                    params['sell_count_min'] = sell_count_min
                
                if sell_count_max is not None:
                    having_conditions.append("total_sell_count <= :sell_count_max")
                    params['sell_count_max'] = sell_count_max
                
                if having_conditions:
                    sql_query += f" HAVING {' AND '.join(having_conditions)}"
                
                # 排序
                if sort_by == 'name_asc':
                    sql_query += " ORDER BY i.name ASC"
                elif sort_by == 'name_desc':
                    sql_query += " ORDER BY i.name DESC"
                elif sort_by == 'arbitrage_desc':
                    sql_query += " ORDER BY i.arbitrage_ratio DESC"
                else:  # created_desc
                    sql_query += " ORDER BY f.created_at DESC"
                
                # 分页
                sql_query += " LIMIT :limit OFFSET :offset"
                params['limit'] = limit
                params['offset'] = offset
                
                # 执行查询
                print(f"🔍 [SQL查询] {sql_query}")
                print(f"📊 [查询参数] {params}")
                
                result = session.execute(text(sql_query), params)
                rows = result.fetchall()
                
                print(f"✅ [SQL查询完成] 找到 {len(rows)} 个收藏饰品")
                
                # 转换结果
                formatted_results = []
                for row in rows:
                    # 解析平台价格信息
                    platform_prices = self._parse_platform_prices(
                        row.platforms, row.sell_prices, row.sell_counts,
                        row.bidding_prices, row.bidding_counts, row.update_times
                    )
                    
                    # 计算求购利润（如果有相关数据）
                    purchase_profit_info = None
                    if hasattr(row, 'steam_direct_bidding_price') and hasattr(row, 'min_sell_price'):
                        steam_bidding_price = self._safe_float_convert(row.steam_direct_bidding_price)
                        min_sell_price = self._safe_float_convert(row.min_sell_price)

                        if steam_bidding_price and min_sell_price and steam_bidding_price > 0:
                            # 使用传入的搬砖卡价，如果没有则从配置获取美元汇率
                            if arbitrage_card_price is not None:
                                card_price = arbitrage_card_price
                            else:
                                try:
                                    from src.cs2_investment.config.streamlit_config import get_streamlit_config
                                    streamlit_config = get_streamlit_config()
                                    card_price = streamlit_config.real_exchange_rate
                                except Exception:
                                    card_price = 7.21  # 最后的默认值
                            # 确保所有数值都是float类型，避免Decimal和float混合运算
                            cost = float(card_price) * float(steam_bidding_price)
                            profit = float(min_sell_price) - cost
                            profit_rate = (profit / cost * 100) if cost > 0 else 0

                            purchase_profit_info = {
                                'profit': profit,
                                'profit_rate': profit_rate,
                                'purchase_profit': profit,  # 兼容饰品卡片的字段名
                                'purchase_profit_rate': profit_rate  # 兼容饰品卡片的字段名
                            }

                    # 构建结果
                    item_data = {
                        # 收藏信息
                        'favorite_id': row.favorite_id,
                        'user_id': row.user_id,
                        'created_at': row.favorite_created_at,
                        'notes': row.favorite_notes,
                        'is_favorite': True,

                        # 饰品信息
                        'item_id': row.item_id,
                        'name': row.item_name,
                        'item_type': row.item_type,
                        'quality': row.quality,
                        'rarity': row.rarity,
                        'exterior': row.exterior,
                        'image_url': row.image_url,
                        'market_hash_name': row.market_hash_name,
                        'arbitrage_ratio': self._safe_float_convert(row.arbitrage_ratio),
                        'last_price_update': row.last_price_update,

                        # 平台价格
                        'platform_prices': platform_prices,

                        # 统计信息
                        'total_sell_count': row.total_sell_count
                    }

                    # 添加求购利润信息
                    if purchase_profit_info:
                        item_data.update(purchase_profit_info)
                    
                    formatted_results.append(item_data)
                
                print(f"🎯 [最终结果] 返回 {len(formatted_results)} 个收藏饰品")
                return formatted_results
                
        except Exception as e:
            print(f"❌ [SQL查询失败] {e}")
            raise
    
    def _parse_platform_prices(self, platforms_str, sell_prices_str, sell_counts_str,
                              bidding_prices_str, bidding_counts_str, update_times_str) -> Dict[str, Dict]:
        """解析聚合的平台价格信息"""
        
        platform_prices = {}
        
        if not platforms_str:
            return platform_prices
        
        # 安全的字符串处理
        platforms_str = str(platforms_str) if platforms_str is not None else ''
        sell_prices_str = str(sell_prices_str) if sell_prices_str is not None else ''
        sell_counts_str = str(sell_counts_str) if sell_counts_str is not None else ''
        bidding_prices_str = str(bidding_prices_str) if bidding_prices_str is not None else ''
        bidding_counts_str = str(bidding_counts_str) if bidding_counts_str is not None else ''
        update_times_str = str(update_times_str) if update_times_str is not None else ''

        if not platforms_str or platforms_str == 'None':
            return platform_prices

        # 解析平台信息（格式：平台名称|数据源）
        platform_entries = platforms_str.split(',') if platforms_str else []

        # 解析各种价格信息（新格式：平台名称|数据源:值）
        sell_price_dict = {}
        sell_count_dict = {}
        bidding_price_dict = {}
        bidding_count_dict = {}
        update_time_dict = {}

        for price_info in (sell_prices_str or '').split(','):
            if ':' in price_info:
                platform_source, price = price_info.split(':', 1)
                try:
                    sell_price_dict[platform_source] = float(price) if price and price != 'None' else 0
                except (ValueError, TypeError):
                    sell_price_dict[platform_source] = 0

        for count_info in (sell_counts_str or '').split(','):
            if ':' in count_info:
                platform_source, count = count_info.split(':', 1)
                try:
                    sell_count_dict[platform_source] = int(count) if count and count != 'None' else 0
                except (ValueError, TypeError):
                    sell_count_dict[platform_source] = 0

        for price_info in (bidding_prices_str or '').split(','):
            if ':' in price_info:
                platform_source, price = price_info.split(':', 1)
                try:
                    bidding_price_dict[platform_source] = float(price) if price and price != 'None' else 0
                except (ValueError, TypeError):
                    bidding_price_dict[platform_source] = 0

        for count_info in (bidding_counts_str or '').split(','):
            if ':' in count_info:
                platform_source, count = count_info.split(':', 1)
                try:
                    bidding_count_dict[platform_source] = int(count) if count and count != 'None' else 0
                except (ValueError, TypeError):
                    bidding_count_dict[platform_source] = 0

        for time_info in (update_times_str or '').split(','):
            if ':' in time_info:
                platform_source, update_time = time_info.split(':', 1)
                update_time_dict[platform_source] = update_time

        # 构建平台价格字典
        for platform_entry in platform_entries:
            if '|' in platform_entry:
                platform, data_source = platform_entry.split('|', 1)
            else:
                platform = platform_entry
                data_source = 'steamdt'

            # 根据数据源确定平台键名（处理大小写）
            platform_upper = platform.upper()
            if platform_upper == 'STEAM' and data_source == 'steam_direct':
                platform_key = 'steam_direct'
            elif platform_upper == 'STEAM' and data_source == 'steamdt':
                platform_key = 'steam'
            else:
                platform_key = platform.lower().replace(' ', '_')

            # 处理更新时间
            platform_source_key = f"{platform}|{data_source}"
            update_time_raw = update_time_dict.get(platform_source_key, '')
            update_time = None
            if update_time_raw and update_time_raw != '':
                try:
                    # 如果是时间戳，转换为datetime
                    if update_time_raw.isdigit():
                        from datetime import datetime
                        update_time = datetime.fromtimestamp(int(update_time_raw))
                    else:
                        update_time = update_time_raw
                except:
                    update_time = update_time_raw

            platform_prices[platform_key] = {
                'platform': platform,
                'sell_price': sell_price_dict.get(platform_source_key, 0),
                'sell_count': sell_count_dict.get(platform_source_key, 0),
                'bidding_price': bidding_price_dict.get(platform_source_key, 0),
                'bidding_count': bidding_count_dict.get(platform_source_key, 0),
                'data_source': data_source,
                'update_time': update_time
            }
        
        return platform_prices

    def _safe_float_convert(self, value) -> Optional[float]:
        """安全的float转换，处理Decimal类型"""
        if value is None:
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None

    def count_user_favorites_with_filters(self, user_id: str, **filters) -> int:
        """统计符合条件的收藏数量"""
        
        # 使用相同的查询逻辑，但只返回COUNT
        try:
            results = self.get_user_favorites_with_complete_data(
                user_id=user_id,
                limit=10000,  # 设置一个大的限制来获取总数
                **filters
            )
            return len(results)
        except Exception as e:
            print(f"❌ 统计收藏数量失败: {e}")
            return 0
