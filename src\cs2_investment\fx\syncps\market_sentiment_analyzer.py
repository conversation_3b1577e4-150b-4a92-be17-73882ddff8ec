#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品分析系统 - 市场情绪分析器
迁移原系统第七层情绪分析功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class MarketSentimentAnalyzer:
    """市场情绪分析器 - 迁移原系统情绪分析功能"""
    
    def __init__(self, daily_data: pd.DataFrame, trend_data: pd.DataFrame, 
                 technical_indicators: Dict, risk_assessment: Dict):
        """
        初始化市场情绪分析器
        
        Args:
            daily_data: 日K数据
            trend_data: 走势数据 (基本面)
            technical_indicators: 技术指标结果
            risk_assessment: 风险评估结果
        """
        self.daily_data = daily_data.copy()
        self.trend_data = trend_data.copy()
        self.technical_indicators = technical_indicators
        self.risk_assessment = risk_assessment
        
        # 情绪分析结果
        self.sentiment_results = {}
    
    def analyze_market_sentiment(self) -> Dict:
        """综合市场情绪分析"""
        
        print("😨 市场情绪分析 (迁移原系统第七层)")
        
        # 1. 供需情绪分析
        supply_demand_sentiment = self._analyze_supply_demand_sentiment()
        
        # 2. 动量情绪分析
        momentum_sentiment = self._analyze_momentum_sentiment()
        
        # 3. 恐慌贪婪指数
        fear_greed_index = self._calculate_fear_greed_index()
        
        # 4. 异常恐慌指数
        anomaly_panic_index = self._calculate_anomaly_panic_index()
        
        # 5. 综合情绪评分
        comprehensive_sentiment = self._calculate_comprehensive_sentiment(
            supply_demand_sentiment, momentum_sentiment, fear_greed_index, anomaly_panic_index
        )
        
        # 存储结果
        self.sentiment_results = {
            'supply_demand_sentiment': supply_demand_sentiment,
            'momentum_sentiment': momentum_sentiment,
            'fear_greed_index': fear_greed_index,
            'anomaly_panic_index': anomaly_panic_index,
            'comprehensive_sentiment': comprehensive_sentiment
        }
        
        return self.sentiment_results
    
    def _analyze_supply_demand_sentiment(self) -> Dict:
        """供需情绪分析 - 基于求购溢价和供给压力"""
        
        latest_trend = self.trend_data.iloc[-1]
        
        # 求购溢价情绪
        bid_premium = (latest_trend['bid_price'] - latest_trend['price']) / latest_trend['price'] * 100
        
        if bid_premium > 5:
            bid_sentiment = 'EXTREMELY_BULLISH'
            bid_score = 90
        elif bid_premium > 2:
            bid_sentiment = 'BULLISH'
            bid_score = 70
        elif bid_premium > 0:
            bid_sentiment = 'SLIGHTLY_BULLISH'
            bid_score = 60
        elif bid_premium > -2:
            bid_sentiment = 'NEUTRAL'
            bid_score = 50
        elif bid_premium > -5:
            bid_sentiment = 'BEARISH'
            bid_score = 30
        else:
            bid_sentiment = 'EXTREMELY_BEARISH'
            bid_score = 10
        
        # 供给压力情绪
        total_supply = latest_trend.get('total_supply', 0)
        supply_ratio = (latest_trend['supply'] / total_supply * 100) if total_supply > 0 else 0
        
        if supply_ratio < 2:
            supply_sentiment = 'EXTREMELY_BULLISH'
            supply_score = 90
        elif supply_ratio < 5:
            supply_sentiment = 'BULLISH'
            supply_score = 70
        elif supply_ratio < 10:
            supply_sentiment = 'SLIGHTLY_BULLISH'
            supply_score = 60
        elif supply_ratio < 15:
            supply_sentiment = 'NEUTRAL'
            supply_score = 50
        elif supply_ratio < 25:
            supply_sentiment = 'BEARISH'
            supply_score = 30
        else:
            supply_sentiment = 'EXTREMELY_BEARISH'
            supply_score = 10
        
        # 求购深度情绪
        supply = latest_trend.get('supply', 0)
        bid_depth = (latest_trend['bid_quantity'] / supply * 100) if supply > 0 else 0
        
        if bid_depth > 50:
            depth_sentiment = 'EXTREMELY_BULLISH'
            depth_score = 90
        elif bid_depth > 20:
            depth_sentiment = 'BULLISH'
            depth_score = 70
        elif bid_depth > 10:
            depth_sentiment = 'SLIGHTLY_BULLISH'
            depth_score = 60
        elif bid_depth > 5:
            depth_sentiment = 'NEUTRAL'
            depth_score = 50
        elif bid_depth > 2:
            depth_sentiment = 'BEARISH'
            depth_score = 30
        else:
            depth_sentiment = 'EXTREMELY_BEARISH'
            depth_score = 10
        
        # 综合供需情绪
        supply_demand_score = (bid_score * 0.4 + supply_score * 0.4 + depth_score * 0.2)
        
        if supply_demand_score >= 80:
            overall_sentiment = 'EXTREMELY_BULLISH'
        elif supply_demand_score >= 65:
            overall_sentiment = 'BULLISH'
        elif supply_demand_score >= 55:
            overall_sentiment = 'SLIGHTLY_BULLISH'
        elif supply_demand_score >= 45:
            overall_sentiment = 'NEUTRAL'
        elif supply_demand_score >= 30:
            overall_sentiment = 'BEARISH'
        else:
            overall_sentiment = 'EXTREMELY_BEARISH'
        
        return {
            'overall_sentiment': overall_sentiment,
            'overall_score': supply_demand_score,
            'bid_premium_sentiment': {
                'sentiment': bid_sentiment,
                'score': bid_score,
                'value': bid_premium,
                'description': f'求购溢价{bid_premium:+.2f}%'
            },
            'supply_pressure_sentiment': {
                'sentiment': supply_sentiment,
                'score': supply_score,
                'value': supply_ratio,
                'description': f'供给比例{supply_ratio:.2f}%'
            },
            'bid_depth_sentiment': {
                'sentiment': depth_sentiment,
                'score': depth_score,
                'value': bid_depth,
                'description': f'求购深度{bid_depth:.2f}%'
            },
            'data_source': '走势数据 (供需基本面)'
        }
    
    def _analyze_momentum_sentiment(self) -> Dict:
        """动量情绪分析 - 基于价格动量和成交量确认"""
        
        # 价格动量 (基于EMA趋势)
        current_ema12 = self.technical_indicators['ema_12'].iloc[-1]
        current_ema26 = self.technical_indicators['ema_26'].iloc[-1]
        
        # EMA动量强度
        ema_momentum = (current_ema12 - current_ema26) / current_ema26 * 100
        
        if ema_momentum > 5:
            price_momentum_sentiment = 'EXTREMELY_BULLISH'
            price_momentum_score = 90
        elif ema_momentum > 2:
            price_momentum_sentiment = 'BULLISH'
            price_momentum_score = 70
        elif ema_momentum > 0.5:
            price_momentum_sentiment = 'SLIGHTLY_BULLISH'
            price_momentum_score = 60
        elif ema_momentum > -0.5:
            price_momentum_sentiment = 'NEUTRAL'
            price_momentum_score = 50
        elif ema_momentum > -2:
            price_momentum_sentiment = 'BEARISH'
            price_momentum_score = 30
        else:
            price_momentum_sentiment = 'EXTREMELY_BEARISH'
            price_momentum_score = 10
        
        # 成交量确认 (基于成交量比率)
        recent_volume = self.daily_data['volume'].tail(5).mean()
        avg_volume = self.daily_data['volume'].mean()
        volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1
        
        if volume_ratio > 2:
            volume_confirmation = 'STRONG_CONFIRMATION'
            volume_score = 90
        elif volume_ratio > 1.5:
            volume_confirmation = 'CONFIRMATION'
            volume_score = 70
        elif volume_ratio > 1.2:
            volume_confirmation = 'WEAK_CONFIRMATION'
            volume_score = 60
        elif volume_ratio > 0.8:
            volume_confirmation = 'NEUTRAL'
            volume_score = 50
        elif volume_ratio > 0.5:
            volume_confirmation = 'WEAK_DIVERGENCE'
            volume_score = 30
        else:
            volume_confirmation = 'STRONG_DIVERGENCE'
            volume_score = 10
        
        # MACD动量确认
        current_macd = self.technical_indicators['macd'].iloc[-1]
        current_macd_signal = self.technical_indicators['macd_signal'].iloc[-1]
        
        macd_momentum = current_macd - current_macd_signal
        
        if macd_momentum > 0 and current_macd > 0:
            macd_sentiment = 'BULLISH'
            macd_score = 70
        elif macd_momentum > 0:
            macd_sentiment = 'SLIGHTLY_BULLISH'
            macd_score = 60
        elif macd_momentum < 0 and current_macd < 0:
            macd_sentiment = 'BEARISH'
            macd_score = 30
        elif macd_momentum < 0:
            macd_sentiment = 'SLIGHTLY_BEARISH'
            macd_score = 40
        else:
            macd_sentiment = 'NEUTRAL'
            macd_score = 50
        
        # 综合动量情绪
        momentum_score = (price_momentum_score * 0.5 + volume_score * 0.3 + macd_score * 0.2)
        
        if momentum_score >= 80:
            overall_momentum = 'EXTREMELY_BULLISH'
        elif momentum_score >= 65:
            overall_momentum = 'BULLISH'
        elif momentum_score >= 55:
            overall_momentum = 'SLIGHTLY_BULLISH'
        elif momentum_score >= 45:
            overall_momentum = 'NEUTRAL'
        elif momentum_score >= 30:
            overall_momentum = 'BEARISH'
        else:
            overall_momentum = 'EXTREMELY_BEARISH'
        
        return {
            'overall_momentum': overall_momentum,
            'overall_score': momentum_score,
            'price_momentum': {
                'sentiment': price_momentum_sentiment,
                'score': price_momentum_score,
                'value': ema_momentum,
                'description': f'EMA动量{ema_momentum:+.2f}%'
            },
            'volume_confirmation': {
                'confirmation': volume_confirmation,
                'score': volume_score,
                'value': volume_ratio,
                'description': f'成交量比率{volume_ratio:.2f}x'
            },
            'macd_momentum': {
                'sentiment': macd_sentiment,
                'score': macd_score,
                'value': macd_momentum,
                'description': f'MACD动量{macd_momentum:+.3f}'
            },
            'data_source': '日K数据 (技术动量)'
        }
    
    def _calculate_fear_greed_index(self) -> Dict:
        """恐慌贪婪指数 - 基于波动、异常、流动性等"""
        
        # 波动率恐慌 (基于日波动率)
        daily_returns = self.daily_data['close'].pct_change().dropna()
        volatility = daily_returns.std() * 100
        
        if volatility > 8:
            volatility_fear = 90  # 极度恐慌
        elif volatility > 5:
            volatility_fear = 70  # 恐慌
        elif volatility > 3:
            volatility_fear = 50  # 中性
        elif volatility > 1:
            volatility_fear = 30  # 贪婪
        else:
            volatility_fear = 10  # 极度贪婪
        
        # RSI极值恐慌
        current_rsi = self.technical_indicators['rsi'].iloc[-1]
        
        if current_rsi > 85:
            rsi_fear = 90  # 极度贪婪导致恐慌
        elif current_rsi > 75:
            rsi_fear = 70  # 贪婪
        elif current_rsi < 15:
            rsi_fear = 90  # 极度恐慌
        elif current_rsi < 25:
            rsi_fear = 70  # 恐慌
        else:
            rsi_fear = 30  # 中性偏贪婪
        
        # 流动性恐慌 (基于成交量变化)
        recent_volume = self.daily_data['volume'].tail(7).mean()
        avg_volume = self.daily_data['volume'].mean()
        volume_change = (recent_volume - avg_volume) / avg_volume * 100
        
        if abs(volume_change) > 100:
            liquidity_fear = 80  # 流动性剧变
        elif abs(volume_change) > 50:
            liquidity_fear = 60  # 流动性异常
        elif abs(volume_change) > 20:
            liquidity_fear = 40  # 轻微异常
        else:
            liquidity_fear = 20  # 流动性正常
        
        # 价格跳空恐慌
        price_gaps = []
        for i in range(1, min(7, len(self.daily_data))):
            gap = abs(self.daily_data['close'].iloc[-i] - self.daily_data['close'].iloc[-i-1]) / self.daily_data['close'].iloc[-i-1] * 100
            price_gaps.append(gap)
        
        max_gap = max(price_gaps) if price_gaps else 0
        
        if max_gap > 10:
            gap_fear = 90
        elif max_gap > 5:
            gap_fear = 70
        elif max_gap > 2:
            gap_fear = 50
        else:
            gap_fear = 20
        
        # 综合恐慌贪婪指数
        fear_greed_score = (volatility_fear * 0.3 + rsi_fear * 0.3 + liquidity_fear * 0.2 + gap_fear * 0.2)
        
        if fear_greed_score >= 80:
            fear_greed_level = 'EXTREME_FEAR'
        elif fear_greed_score >= 60:
            fear_greed_level = 'FEAR'
        elif fear_greed_score >= 40:
            fear_greed_level = 'NEUTRAL'
        elif fear_greed_score >= 20:
            fear_greed_level = 'GREED'
        else:
            fear_greed_level = 'EXTREME_GREED'
        
        return {
            'fear_greed_level': fear_greed_level,
            'fear_greed_score': fear_greed_score,
            'components': {
                'volatility_fear': {
                    'score': volatility_fear,
                    'value': volatility,
                    'description': f'波动率恐慌 ({volatility:.2f}%)'
                },
                'rsi_fear': {
                    'score': rsi_fear,
                    'value': current_rsi,
                    'description': f'RSI极值恐慌 ({current_rsi:.1f})'
                },
                'liquidity_fear': {
                    'score': liquidity_fear,
                    'value': volume_change,
                    'description': f'流动性恐慌 ({volume_change:+.1f}%)'
                },
                'gap_fear': {
                    'score': gap_fear,
                    'value': max_gap,
                    'description': f'跳空恐慌 ({max_gap:.1f}%)'
                }
            },
            'data_source': '日K数据 (恐慌贪婪综合)'
        }

    def _calculate_anomaly_panic_index(self) -> Dict:
        """异常恐慌指数 - 基于异常检测结果"""

        # 从风险评估中获取异常检测结果
        if 'enhanced_risk_assessment' in self.risk_assessment:
            anomaly_risks = self.risk_assessment['enhanced_risk_assessment']['anomaly_risks']

            total_anomalies = anomaly_risks['total_anomalies']
            high_risk_anomalies = anomaly_risks['high_risk_anomalies']
            medium_risk_anomalies = anomaly_risks['medium_risk_anomalies']

            # 异常恐慌评分
            if high_risk_anomalies > 3:
                anomaly_panic_score = 95
                panic_level = 'EXTREME_PANIC'
            elif high_risk_anomalies > 0:
                anomaly_panic_score = 80
                panic_level = 'HIGH_PANIC'
            elif medium_risk_anomalies > 5:
                anomaly_panic_score = 70
                panic_level = 'MEDIUM_PANIC'
            elif medium_risk_anomalies > 2:
                anomaly_panic_score = 50
                panic_level = 'LOW_PANIC'
            elif total_anomalies > 10:
                anomaly_panic_score = 30
                panic_level = 'SLIGHT_CONCERN'
            else:
                anomaly_panic_score = 10
                panic_level = 'CALM'

            return {
                'panic_level': panic_level,
                'panic_score': anomaly_panic_score,
                'total_anomalies': total_anomalies,
                'high_risk_anomalies': high_risk_anomalies,
                'medium_risk_anomalies': medium_risk_anomalies,
                'description': f'检测到{total_anomalies}个异常，其中{high_risk_anomalies}个高风险',
                'data_source': '异常检测结果'
            }
        else:
            # 如果没有异常检测结果，返回默认值
            return {
                'panic_level': 'UNKNOWN',
                'panic_score': 50,
                'total_anomalies': 0,
                'high_risk_anomalies': 0,
                'medium_risk_anomalies': 0,
                'description': '异常检测数据不可用',
                'data_source': '异常检测结果 (不可用)'
            }

    def _calculate_comprehensive_sentiment(self, supply_demand: Dict, momentum: Dict,
                                         fear_greed: Dict, anomaly_panic: Dict) -> Dict:
        """综合情绪评分 - 多维度情绪综合"""

        # 各维度权重
        weights = {
            'supply_demand': 0.35,  # 供需情绪权重35%
            'momentum': 0.25,       # 动量情绪权重25%
            'fear_greed': 0.25,     # 恐慌贪婪权重25%
            'anomaly_panic': 0.15   # 异常恐慌权重15%
        }

        # 计算综合评分
        comprehensive_score = (
            supply_demand['overall_score'] * weights['supply_demand'] +
            momentum['overall_score'] * weights['momentum'] +
            (100 - fear_greed['fear_greed_score']) * weights['fear_greed'] +  # 恐慌指数需要反转
            (100 - anomaly_panic['panic_score']) * weights['anomaly_panic']   # 异常恐慌需要反转
        )

        # 综合情绪等级
        if comprehensive_score >= 80:
            overall_sentiment = 'EXTREMELY_BULLISH'
            sentiment_description = '极度乐观'
        elif comprehensive_score >= 65:
            overall_sentiment = 'BULLISH'
            sentiment_description = '乐观'
        elif comprehensive_score >= 55:
            overall_sentiment = 'SLIGHTLY_BULLISH'
            sentiment_description = '偏乐观'
        elif comprehensive_score >= 45:
            overall_sentiment = 'NEUTRAL'
            sentiment_description = '中性'
        elif comprehensive_score >= 30:
            overall_sentiment = 'BEARISH'
            sentiment_description = '悲观'
        else:
            overall_sentiment = 'EXTREMELY_BEARISH'
            sentiment_description = '极度悲观'

        # 情绪建议
        sentiment_advice = self._generate_sentiment_advice(overall_sentiment, comprehensive_score)

        return {
            'overall_sentiment': overall_sentiment,
            'sentiment_description': sentiment_description,
            'comprehensive_score': comprehensive_score,
            'sentiment_advice': sentiment_advice,
            'component_scores': {
                'supply_demand_score': supply_demand['overall_score'],
                'momentum_score': momentum['overall_score'],
                'fear_greed_score': fear_greed['fear_greed_score'],
                'anomaly_panic_score': anomaly_panic['panic_score']
            },
            'weights': weights,
            'calculation_method': '供需35% + 动量25% + 恐慌贪婪25% + 异常恐慌15%'
        }

    def _generate_sentiment_advice(self, sentiment: str, score: float) -> str:
        """生成情绪建议"""

        if sentiment == 'EXTREMELY_BULLISH':
            return f"市场情绪极度乐观 (评分{score:.1f})，但需警惕过度乐观风险，建议适当获利了结"
        elif sentiment == 'BULLISH':
            return f"市场情绪乐观 (评分{score:.1f})，可考虑适量买入，但需设置止损"
        elif sentiment == 'SLIGHTLY_BULLISH':
            return f"市场情绪偏乐观 (评分{score:.1f})，可谨慎买入，密切关注情绪变化"
        elif sentiment == 'NEUTRAL':
            return f"市场情绪中性 (评分{score:.1f})，建议观望，等待明确信号"
        elif sentiment == 'BEARISH':
            return f"市场情绪悲观 (评分{score:.1f})，建议减仓或观望，避免抄底"
        else:
            return f"市场情绪极度悲观 (评分{score:.1f})，高风险期，建议空仓观望"

    def print_sentiment_summary(self):
        """打印情绪分析摘要"""

        if not self.sentiment_results:
            print("❌ 请先运行 analyze_market_sentiment()")
            return

        print(f"📊 供需情绪:")
        sd = self.sentiment_results['supply_demand_sentiment']
        print(f"   整体情绪: {sd['overall_sentiment']} (评分: {sd['overall_score']:.1f})")
        print(f"   求购溢价: {sd['bid_premium_sentiment']['description']}")
        print(f"   供给压力: {sd['supply_pressure_sentiment']['description']}")
        print(f"   求购深度: {sd['bid_depth_sentiment']['description']}")

        print(f"⚡ 动量情绪:")
        mom = self.sentiment_results['momentum_sentiment']
        print(f"   整体动量: {mom['overall_momentum']} (评分: {mom['overall_score']:.1f})")
        print(f"   价格动量: {mom['price_momentum']['description']}")
        print(f"   成交量确认: {mom['volume_confirmation']['description']}")
        print(f"   MACD动量: {mom['macd_momentum']['description']}")

        print(f"😱 恐慌贪婪指数:")
        fg = self.sentiment_results['fear_greed_index']
        print(f"   恐慌贪婪等级: {fg['fear_greed_level']} (评分: {fg['fear_greed_score']:.1f})")
        for component, data in fg['components'].items():
            print(f"   {data['description']}")

        print(f"🚨 异常恐慌指数:")
        ap = self.sentiment_results['anomaly_panic_index']
        print(f"   恐慌等级: {ap['panic_level']} (评分: {ap['panic_score']:.1f})")
        print(f"   {ap['description']}")

        print(f"🎯 综合情绪评估:")
        comp = self.sentiment_results['comprehensive_sentiment']
        print(f"   综合情绪: {comp['overall_sentiment']} - {comp['sentiment_description']}")
        print(f"   综合评分: {comp['comprehensive_score']:.1f}/100")
        print(f"   情绪建议: {comp['sentiment_advice']}")

    def get_sentiment_summary(self) -> Dict:
        """获取情绪分析摘要"""

        if not self.sentiment_results:
            return {'error': '请先运行 analyze_market_sentiment()'}

        comp = self.sentiment_results['comprehensive_sentiment']

        return {
            'overall_sentiment': comp['overall_sentiment'],
            'sentiment_description': comp['sentiment_description'],
            'comprehensive_score': comp['comprehensive_score'],
            'sentiment_advice': comp['sentiment_advice'],
            'key_factors': {
                '供需情绪': self.sentiment_results['supply_demand_sentiment']['overall_sentiment'],
                '动量情绪': self.sentiment_results['momentum_sentiment']['overall_momentum'],
                '恐慌贪婪': self.sentiment_results['fear_greed_index']['fear_greed_level'],
                '异常恐慌': self.sentiment_results['anomaly_panic_index']['panic_level']
            },
            'data_source': '综合情绪分析 (多维度)'
        }
