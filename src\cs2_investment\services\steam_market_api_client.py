"""
Steam市场API客户端

实现Steam市场数据获取的核心功能，包含两步流程：
1. 从Steam市场页面获取item_nameid
2. 调用itemordershistogram接口获取订单数据
"""

import requests
import json
import re
import time
import logging
from typing import Dict, Optional, Tuple
from urllib.parse import quote
from loguru import logger


class SteamMarketAPIClient:
    """Steam市场API客户端"""

    def __init__(self, proxy_enabled: bool = None, proxy_url: str = None):
        """
        初始化Steam市场API客户端

        Args:
            proxy_enabled: 是否启用代理，如果为None则从配置文件读取
            proxy_url: 代理URL，如果为None则从配置文件读取
        """
        self.session = requests.Session()
        self.logger = logger.bind(service="SteamMarketAPI")

        # Steam专用代理配置 - 如果没有传入参数，从Steam监控配置读取
        if proxy_enabled is None or proxy_url is None:
            try:
                from ..config.timer_config import get_timer_config
                config = get_timer_config()
                # 使用Steam专用的代理配置，而不是通用的scraping代理
                self.proxy_enabled = proxy_enabled if proxy_enabled is not None else config.steam_monitor.proxy_enabled
                self.proxy_url = proxy_url if proxy_url is not None else config.steam_monitor.proxy_url
            except Exception as e:
                self.logger.warning(f"无法加载Steam代理配置，使用默认设置: {e}")
                self.proxy_enabled = proxy_enabled if proxy_enabled is not None else False
                self.proxy_url = proxy_url if proxy_url is not None else ""
        else:
            self.proxy_enabled = proxy_enabled
            self.proxy_url = proxy_url

        # 配置代理
        if self.proxy_enabled and self.proxy_url:
            proxies = {
                'http': self.proxy_url,
                'https': self.proxy_url
            }
            self.session.proxies.update(proxies)
            self.logger.info(f"🌐 Steam API客户端已启用代理: {self.proxy_url}")
        else:
            self.logger.info("🚫 Steam API客户端代理已禁用，直接连接")

        # 设置请求头，模拟浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def get_item_nameid_from_market_page(self, market_hash_name: str, appid: int = 730) -> Optional[int]:
        """
        第一步：从Steam市场页面获取item_nameid
        
        Args:
            market_hash_name: 物品的市场哈希名称
            appid: 游戏ID，默认730(CS2)
            
        Returns:
            item_nameid，失败返回None
        """
        try:
            # 构造市场页面URL
            encoded_name = quote(market_hash_name)
            market_url = f"https://steamcommunity.com/market/listings/{appid}/{encoded_name}"
            
            self.logger.debug(f"访问市场页面: {market_url}")
            
            # 请求市场页面
            response = self.session.get(market_url, timeout=15)
            
            if response.status_code != 200:
                self.logger.error(f"市场页面请求失败: HTTP {response.status_code}")
                return None
            
            # 从页面源码中提取item_nameid
            html_content = response.text
            
            # 方法1: 查找 Market_LoadOrderSpread 函数调用
            pattern1 = r'Market_LoadOrderSpread\s*\(\s*(\d+)\s*\)'
            match1 = re.search(pattern1, html_content)
            
            if match1:
                item_nameid = int(match1.group(1))
                self.logger.debug(f"通过Market_LoadOrderSpread找到item_nameid: {item_nameid}")
                return item_nameid
            
            # 方法2: 查找 g_rgAssets 中的数据
            pattern2 = r'"item_nameid"\s*:\s*"?(\d+)"?'
            match2 = re.search(pattern2, html_content)
            
            if match2:
                item_nameid = int(match2.group(1))
                self.logger.debug(f"通过g_rgAssets找到item_nameid: {item_nameid}")
                return item_nameid
            
            # 方法3: 查找其他可能的模式
            pattern3 = r'ItemActivityTicker\.Start\s*\(\s*(\d+)\s*\)'
            match3 = re.search(pattern3, html_content)
            
            if match3:
                item_nameid = int(match3.group(1))
                self.logger.debug(f"通过ItemActivityTicker找到item_nameid: {item_nameid}")
                return item_nameid
            
            self.logger.error(f"未能从页面源码中找到item_nameid")
            return None
            
        except Exception as e:
            self.logger.error(f"获取item_nameid时发生异常: {e}")
            return None
    
    def get_item_orders_by_nameid(self, item_nameid: int, country: str = "US", 
                                 language: str = "schinese", currency: int = 1) -> Optional[Dict]:
        """
        第二步：使用item_nameid获取订单数据
        
        Args:
            item_nameid: 物品的内部ID
            country: 国家代码
            language: 语言
            currency: 货币代码
            
        Returns:
            订单数据，失败返回None
        """
        try:
            self.logger.debug(f"获取订单数据: item_nameid={item_nameid}")
            
            # 构造订单接口URL
            base_url = "https://steamcommunity.com/market/itemordershistogram"
            params = {
                'country': country,
                'language': language,
                'currency': currency,
                'item_nameid': item_nameid,
                'two_factor': 0
            }
            
            # 更新请求头为JSON请求
            headers = {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': f'https://steamcommunity.com/market/listings/730/',
            }
            
            response = self.session.get(base_url, params=params, headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.logger.debug(f"订单数据获取成功")
                    return data
                else:
                    self.logger.error(f"API返回失败: {data}")
                    return None
            else:
                self.logger.error(f"订单接口请求失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取订单数据时发生异常: {e}")
            return None
    
    def get_complete_market_data(self, market_hash_name: str, appid: int = 730, item_id: str = None) -> Optional[Dict]:
        """
        完整流程：获取item_nameid -> 获取订单数据（优化版本）

        Args:
            market_hash_name: 物品的市场哈希名称
            appid: 游戏ID
            item_id: 饰品ID（可选，用于查询缓存的Steam ID）

        Returns:
            完整的市场数据
        """
        self.logger.info(f"开始获取完整市场数据: {market_hash_name}")

        item_nameid = None

        # 优化：首先尝试从数据库获取已缓存的Steam ID
        if item_id:
            from ..dao.platform_price_dao import PlatformPriceDAO
            platform_price_dao = PlatformPriceDAO()
            cached_steam_id = platform_price_dao.get_steam_item_id(item_id)

            if cached_steam_id:
                try:
                    item_nameid = int(cached_steam_id)
                    self.logger.info(f"🚀 [优化] 使用缓存的Steam ID: {item_nameid}")
                except (ValueError, TypeError):
                    self.logger.warning(f"⚠️ [优化] 缓存的Steam ID格式无效: {cached_steam_id}")
                    item_nameid = None

        # 如果没有缓存的Steam ID，则通过页面获取
        if not item_nameid:
            self.logger.info(f"🌐 [常规流程] 通过页面获取Steam ID")
            item_nameid = self.get_item_nameid_from_market_page(market_hash_name, appid)

            if not item_nameid:
                self.logger.error(f"无法获取item_nameid")
                return None

        # 等待一下避免请求过快
        time.sleep(1)

        # 第二步：获取订单数据
        order_data = self.get_item_orders_by_nameid(item_nameid)

        if not order_data:
            self.logger.error(f"无法获取订单数据")
            return None

        # 组合完整数据
        complete_data = {
            'item_info': {
                'market_hash_name': market_hash_name,
                'appid': appid,
                'item_nameid': item_nameid
            },
            'order_data': order_data
        }

        self.logger.info(f"完整市场数据获取成功")
        return complete_data
    
    def parse_price_data(self, complete_data: Dict) -> Optional[Dict]:
        """
        解析价格数据，提取最高求购价和最低出售价
        
        Args:
            complete_data: 完整的市场数据
            
        Returns:
            解析后的价格数据
        """
        if not complete_data or not complete_data.get('order_data'):
            return None
        
        order_data = complete_data['order_data']
        
        try:
            # 提取价格信息
            highest_buy_order = order_data.get('highest_buy_order')
            lowest_sell_order = order_data.get('lowest_sell_order')
            
            # 转换为浮点数（Steam API返回的是分为单位的字符串）
            highest_buy_price = float(highest_buy_order) / 100 if highest_buy_order else 0.0
            lowest_sell_price = float(lowest_sell_order) / 100 if lowest_sell_order else 0.0
            
            return {
                'item_nameid': complete_data['item_info']['item_nameid'],
                'market_hash_name': complete_data['item_info']['market_hash_name'],
                'highest_buy_price': highest_buy_price,
                'lowest_sell_price': lowest_sell_price,
                'raw_data': order_data
            }
            
        except Exception as e:
            self.logger.error(f"解析价格数据失败: {e}")
            return None
