"""
饰品查询页面

提供饰品的多条件查询和展示功能。
"""

import streamlit as st
import pandas as pd
from typing import List, Dict, Any, Optional
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.app.services.item_service import ItemService
from src.cs2_investment.app.utils.data_formatter import format_price, format_percentage
from src.cs2_investment.app.components.item_card import display_item_card
from src.cs2_investment.app.components import item_analysis_component
from src.cs2_investment.app.components.unified_item_card import render_item_card
from src.cs2_investment.app.components.unified_item_filter import render_item_filter
from src.cs2_investment.config.streamlit_config import get_streamlit_config


def show_page():
    """显示饰品查询页面"""
    # 初始化页面状态
    if 'current_view' not in st.session_state:
        st.session_state.current_view = 'list'
    if 'selected_item_id' not in st.session_state:
        st.session_state.selected_item_id = None

    # 根据当前视图显示不同页面
    if st.session_state.current_view == 'detail' and st.session_state.selected_item_id:
        show_item_detail_page(st.session_state.selected_item_id)
    else:
        show_item_list_page()


def show_item_list_page():
    """显示饰品列表页面"""
    st.title("🔍 饰品查询")

    # 查询条件区域
    with st.expander("查询条件", expanded=True):
        show_query_filters()

    # 查询结果区域
    show_query_results()


def show_item_detail_page(item_id: str):
    """显示饰品详情页面"""
    from src.cs2_investment.app.components.item_card import show_item_detail_page
    show_item_detail_page(item_id)


def handle_query_callback(query_params: Dict[str, Any]):
    """
    处理统一过滤组件的查询回调

    Args:
        query_params: 标准化的查询参数字典
    """
    if query_params.get('button_clicked'):
        # 构建ItemService兼容的查询参数
        service_params = {
            'name_query': query_params.get('name_query') if query_params.get('name_query') else None,
            'item_types': query_params.get('item_types') if query_params.get('item_types') else None,
            'qualities': query_params.get('qualities') if query_params.get('qualities') else None,
            'rarities': query_params.get('rarities') if query_params.get('rarities') else None,
            'exteriors': query_params.get('exteriors') if query_params.get('exteriors') else None,
            'price_min': query_params.get('price_min') if query_params.get('price_min', 0) > 0 else None,
            'price_max': query_params.get('price_max') if query_params.get('price_max', 100000) < 100000 else None,
            'sell_count_min': query_params.get('sell_count_min') if query_params.get('sell_count_min', 0) > 0 else None,
            'sell_count_max': query_params.get('sell_count_max') if query_params.get('sell_count_max', 100000) < 100000 else None,
            'arbitrage_threshold': query_params.get('arbitrage_threshold'),
            'arbitrage_card_price': query_params.get('arbitrage_card_price'),
            'sort_by': query_params.get('sort_by', 'updated_desc'),
            'limit': 100000  # 增加查询限制到10万条
        }

        # 保存查询参数到session state
        st.session_state.query_params = service_params
        st.session_state.query_executed = True


def show_query_filters():
    """显示查询过滤条件（使用统一过滤组件）"""
    # 使用统一过滤组件
    render_item_filter(
        filter_type='query',
        key_suffix='query_page',
        on_filter_change=handle_query_callback
    )


def show_query_filters_legacy_DEPRECATED():
    """显示查询过滤条件（原有实现，已被统一组件替代）"""
    try:
        # 初始化服务
        if 'item_service' not in st.session_state:
            st.session_state.item_service = ItemService()

        # 获取筛选选项
        if 'filter_options' not in st.session_state:
            with st.spinner("加载筛选选项..."):
                st.session_state.filter_options = st.session_state.item_service.get_filter_options()

        filter_options = st.session_state.filter_options
    except Exception as e:
        st.error(f"初始化服务失败: {str(e)}")
        st.info("请检查数据库连接配置")
        # 使用默认选项
        filter_options = {
            'item_types': [],
            'qualities': [],
            'rarities': []
        }

    col1, col2, col3 = st.columns(3)

    with col1:
        # 名称搜索
        name_query = st.text_input(
            "饰品名称",
            placeholder="输入饰品名称关键词...",
            help="支持模糊搜索"
        )

        # 饰品类型
        item_types = filter_options.get('item_types', [])
        selected_types = st.multiselect(
            "饰品类型",
            options=item_types,
            help="选择一个或多个饰品类型"
        )

    with col2:
        # 品质选择
        qualities = filter_options.get('qualities', [])
        selected_qualities = st.multiselect(
            "品质",
            options=qualities,
            help="选择饰品品质"
        )

        # 稀有度选择
        rarities = filter_options.get('rarities', [])
        selected_rarities = st.multiselect(
            "稀有度",
            options=rarities,
            help="选择稀有度等级"
        )

    with col3:
        # 价格范围
        st.write("价格范围 (¥)")
        price_col1, price_col2 = st.columns(2)

        with price_col1:
            price_min = st.number_input(
                "最低价格",
                min_value=0.0,
                max_value=100000.0,
                value=0.0,
                step=1.0,
                help="设置最低价格"
            )

        with price_col2:
            price_max = st.number_input(
                "最高价格",
                min_value=0.0,
                max_value=100000.0,
                value=10000.0,
                step=1.0,
                help="设置最高价格"
            )

        price_range = (price_min, price_max)

        # 在售数量范围 - 测试标记
        st.write("🔥 在售数量范围 (steamdt数据源) - 新功能测试")
        sell_count_col1, sell_count_col2 = st.columns(2)

        with sell_count_col1:
            sell_count_min = st.number_input(
                "最少在售数量",
                min_value=0,
                max_value=100000,
                value=0,
                step=1,
                help="设置最少在售数量（steamdt数据源）"
            )

        with sell_count_col2:
            sell_count_max = st.number_input(
                "最多在售数量",
                min_value=0,
                max_value=100000,
                value=100000,
                step=1,
                help="设置最多在售数量（steamdt数据源）"
            )

        sell_count_range = (sell_count_min, sell_count_max)

        # 搬砖卡价过滤
        arbitrage_card_price = st.number_input(
            "搬砖卡价",
            min_value=0.0,
            max_value=20.0,
            value=None,
            step=0.01,
            placeholder="请输入卡价",
            help="输入搬砖卡价（如4.86），系统将计算搬砖率阈值并过滤符合条件的饰品"
        )

        # 排序方式
        sort_options = {
            "name_asc": "名称 (A-Z)",
            "name_desc": "名称 (Z-A)",
            "price_asc": "价格 (低到高)",
            "price_desc": "价格 (高到低)",
            "updated_desc": "最新更新",
            "arbitrage_desc": "搬砖率 (高到低)",
            "purchase_profit_desc": "求购利润 (高到低)",
            "purchase_profit_rate_desc": "求购利润率 (高到低)"
        }
        sort_by = st.selectbox(
            "排序方式",
            options=list(sort_options.keys()),
            format_func=lambda x: sort_options[x],
            index=4  # 默认按最新更新排序
        )
    
    # 搬砖卡价计算提示
    if arbitrage_card_price is not None and arbitrage_card_price > 0:
        try:
            streamlit_config = get_streamlit_config()
            real_rate = streamlit_config.real_exchange_rate
            arbitrage_threshold = arbitrage_card_price / real_rate

            st.info(f"💡 搬砖卡价计算: {arbitrage_card_price} ÷ {real_rate} = {arbitrage_threshold:.6f}")
            st.caption(f"将查询搬砖率 ≥ {arbitrage_threshold:.6f} 的饰品")
        except Exception as e:
            st.error(f"搬砖卡价计算失败: {e}")
            arbitrage_card_price = None

    # 查询按钮
    col1, col2, col3 = st.columns([1, 1, 1])
    with col2:
        if st.button("🔍 查询", type="primary", use_container_width=True):
            # 计算搬砖率阈值
            arbitrage_threshold = None
            if arbitrage_card_price is not None and arbitrage_card_price > 0:
                try:
                    streamlit_config = get_streamlit_config()
                    real_rate = streamlit_config.real_exchange_rate
                    arbitrage_threshold = arbitrage_card_price / real_rate
                except Exception as e:
                    st.error(f"搬砖卡价计算失败: {e}")

            # 构建查询参数
            query_params = {
                'name_query': name_query if name_query else None,
                'item_types': selected_types if selected_types else None,
                'qualities': selected_qualities if selected_qualities else None,
                'rarities': selected_rarities if selected_rarities else None,
                'price_min': price_range[0] if price_range[0] > 0 else None,
                'price_max': price_range[1] if price_range[1] < 100000 else None,
                'sell_count_min': sell_count_min if sell_count_min > 0 else None,
                'sell_count_max': sell_count_max if sell_count_max < 100000 else None,
                'arbitrage_threshold': arbitrage_threshold,
                'arbitrage_card_price': arbitrage_card_price,  # 保存搬砖卡价
                'sort_by': sort_by,
                'limit': 100000  # 增加查询限制到10万条
            }

            # 保存查询参数到session state
            st.session_state.query_params = query_params
            st.session_state.query_executed = True


def show_query_results():
    """显示查询结果"""
    if not hasattr(st.session_state, 'query_executed') or not st.session_state.query_executed:
        st.info("请设置查询条件并点击查询按钮")
        return
    
    try:
        # 执行查询
        with st.spinner("正在查询数据..."):
            results = execute_query(st.session_state.query_params)
        
        if not results:
            st.warning("未找到符合条件的饰品")
            return
        
        # 显示结果统计
        st.subheader(f"查询结果 ({len(results)} 个饰品)")
        
        # 分页显示
        items_per_page = 50
        total_pages = (len(results) - 1) // items_per_page + 1
        
        if total_pages > 1:
            page = st.selectbox(
                "页码",
                range(1, total_pages + 1),
                format_func=lambda x: f"第 {x} 页"
            )
        else:
            page = 1
        
        # 计算当前页的数据范围
        start_idx = (page - 1) * items_per_page
        end_idx = min(start_idx + items_per_page, len(results))
        current_page_results = results[start_idx:end_idx]
        
        # 🚀 性能优化：批量获取收藏状态
        if current_page_results:
            # 初始化收藏服务
            if 'favorite_service' not in st.session_state:
                from src.cs2_investment.app.services.favorite_service import FavoriteService
                st.session_state.favorite_service = FavoriteService()

            # 提取所有饰品ID
            item_ids = [item.get('item_id') for item in current_page_results if item.get('item_id')]

            # 批量获取收藏状态
            favorite_status_dict = {}
            if item_ids:
                try:
                    # 获取用户所有收藏的饰品ID
                    user_favorite_ids = st.session_state.favorite_service.favorite_dao.get_user_favorite_item_ids("default_user")
                    # 创建快速查找字典
                    favorite_status_dict = {item_id: item_id in user_favorite_ids for item_id in item_ids}
                    print(f"🚀 [性能优化] 批量获取 {len(item_ids)} 个饰品的收藏状态")
                except Exception as e:
                    print(f"⚠️ [收藏状态] 批量获取失败: {e}")
                    favorite_status_dict = {}

            # 将收藏状态缓存到session_state中，供收藏按钮使用
            if 'favorite_status_cache' not in st.session_state:
                st.session_state.favorite_status_cache = {}
            st.session_state.favorite_status_cache.update(favorite_status_dict)

        # 显示结果 - 列表格式
        for item in current_page_results:
            # 使用统一饰品卡片组件
            render_item_card(
                item_data=item,
                card_type='query',
                key_suffix='query_page'
            )
    
    except Exception as e:
        st.error(f"查询失败: {str(e)}")


# display_item_row 函数已被统一饰品卡片组件替代，不再需要 


def execute_query(params: Dict[str, Any]) -> List[Dict]:
    """执行查询"""
    try:
        # 尝试使用统一组件的服务实例 
        service_key = 'item_service_query_page'
        if service_key not in st.session_state:
            # 如果统一组件的服务不存在，创建一个新的
            st.session_state[service_key] = ItemService()

        item_service = st.session_state[service_key]
        return item_service.search_items_with_prices(**params)
    except Exception as e:
        st.error(f"查询失败: {str(e)}")
        return []



