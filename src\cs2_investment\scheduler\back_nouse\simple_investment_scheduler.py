"""
简化版投资分析调度器

直接调用分析系统，避免复杂的任务队列机制
"""

import asyncio
import random
import sys
from datetime import datetime, date
from typing import List, Dict, Any
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入原始投资分析系统（注释掉但保留）
# from src.cs2_investment.fx.integrated_analysis_system import IntegratedAnalysisSystem
# 导入新的统一分析系统
# 导入原始投资分析系统（注释掉但保留）
# from src.cs2_investment.fx.integrated_analysis_system import IntegratedAnalysisSystem
# 导入新的统一分析系统
from src.cs2_investment.fx.unified_analysis_system import UnifiedAnalysisSystem
from src.cs2_investment.utils.logger import get_logger

logger = get_logger(__name__)


class SimpleInvestmentScheduler:
    """简化版投资分析调度器"""
    
    def __init__(self):
        """初始化调度器"""
        self.is_running = False
        self.analysis_task = None

        # 配置参数（参考investment_analysis_scheduler.py）
        self.check_interval_minutes = 10  # 每10分钟检查一次
        self.item_delay_seconds = (10, 30)  # 饰品之间间隔10-30秒
        self.max_items_per_batch = 50  # 每批最多处理50个饰品（与investment_analysis_scheduler一致）
        self.max_ranking_items = 500  # 每个榜单最大处理数量

        # 状态跟踪
        self.last_processed_time = None  # 上次处理时间

        # 原始分析系统（注释掉但保留）
        # self.analysis_system = IntegratedAnalysisSystem()

        # 新的统一分析系统（包含实时分析和投资分析）
        self.analysis_system = UnifiedAnalysisSystem()

        # 数据访问对象
        from src.cs2_investment.dao.market_snapshot_dao import market_snapshot_dao
        self.market_dao = market_snapshot_dao

        logger.info("🚀 简化版投资分析调度器初始化完成（已升级为统一分析系统）")
    
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已在运行中")
            return
        
        self.is_running = True
        logger.info("🚀 启动简化版投资分析调度器")
        
        # 启动分析循环
        self.analysis_task = asyncio.create_task(self._analysis_loop())
        
        logger.info("✅ 简化版投资分析调度器启动完成")
    
    async def stop(self):
        """停止调度器"""
        if not self.is_running:
            return

        logger.info("🛑 停止简化版投资分析调度器")
        self.is_running = False

        # 取消任务
        if self.analysis_task:
            self.analysis_task.cancel()
            try:
                await asyncio.wait_for(self.analysis_task, timeout=5.0)
            except asyncio.CancelledError:
                logger.info("✅ 分析任务已被取消")
            except asyncio.TimeoutError:
                logger.warning("⚠️ 分析任务取消超时")
            except Exception as e:
                logger.warning(f"⚠️ 停止分析任务时出现异常: {e}")

        logger.info("✅ 简化版投资分析调度器已停止")

    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        return {
            "is_running": self.is_running,
            "check_interval_minutes": self.check_interval_minutes,
            "max_items_per_batch": self.max_items_per_batch,
            "item_delay_seconds": self.item_delay_seconds,
            "last_processed_time": datetime.now().isoformat(),
            "scheduler_type": "simple_investment_scheduler"
        }

    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        return {
            "status": "healthy" if self.is_running else "stopped",
            "uptime": "N/A",
            "memory_usage": "N/A",
            "last_error": None
        }

    def get_config(self) -> Dict[str, Any]:
        """获取配置信息"""
        return {
            "check_interval_minutes": self.check_interval_minutes,
            "item_delay_seconds": self.item_delay_seconds,
            "max_items_per_batch": self.max_items_per_batch
        }

    async def _analysis_loop(self):
        """投资分析循环任务 - 修正版：每次都检查今天的排行榜数据"""
        logger.info("📊 启动投资分析主循环")

        while self.is_running:
            try:
                logger.info(f"📊 周期开始: {datetime.now()}")

                # 直接获取今天需要分析的饰品，不再检查"数据是否更新"
                items_to_analyze = await self._get_items_to_analyze()

                if not items_to_analyze:
                    logger.info("✅ 今日所有排行榜饰品已完成分析")
                else:
                    logger.info(f"📋 发现 {len(items_to_analyze)} 个饰品需要分析")

                    # 限制批次大小
                    if len(items_to_analyze) > self.max_items_per_batch:
                        items_to_analyze = items_to_analyze[:self.max_items_per_batch]
                        logger.info(f"📦 限制批次大小为 {self.max_items_per_batch} 个饰品")

                    # 逐个分析饰品
                    success_count = 0
                    for i, item in enumerate(items_to_analyze, 1):
                        if not self.is_running:
                            break

                        try:
                            # 显示排名和时间信息
                            ranking_info = []
                            if item.get('hot_rank'):
                                ranking_info.append(f"热度#{item['hot_rank']}")

                            # 显示快照时间
                            snapshot_time = item.get('snapshot_time', 'N/A')
                            if snapshot_time != 'N/A':
                                time_str = snapshot_time.strftime('%H:%M:%S') if hasattr(snapshot_time, 'strftime') else str(snapshot_time)
                                ranking_info.append(f"时间:{time_str}")

                            ranking_str = " | ".join(ranking_info) if ranking_info else "信息缺失"

                            logger.info(f"📋 处理饰品 {i}/{len(items_to_analyze)}: {item['name']} ({ranking_str})")

                            # 直接调用分析系统
                            result = await self._run_single_analysis(item)

                            if result:
                                success_count += 1
                                logger.info(f"✅ 饰品分析成功: {item['name']}")
                            else:
                                logger.warning(f"❌ 饰品分析失败: {item['name']}")

                            # 饰品之间的间隔
                            if i < len(items_to_analyze):
                                delay = random.randint(*self.item_delay_seconds)
                                logger.info(f"⏳ 等待 {delay} 秒后处理下一个饰品...")
                                await asyncio.sleep(delay)

                        except Exception as e:
                            logger.error(f"❌ 处理饰品失败: {item['name']}, 错误: {e}")
                            continue

                    logger.info(f"✅ 本轮分析完成: 成功 {success_count}/{len(items_to_analyze)} 个饰品")

                # 等待下次检查
                logger.info(f"⏳ 等待 {self.check_interval_minutes} 分钟后进行下次检查...")
                await asyncio.sleep(self.check_interval_minutes * 60)
                
            except asyncio.CancelledError:
                logger.info("投资分析任务被取消")
                break
            except Exception as e:
                logger.error(f"投资分析循环出错: {e}")
                await asyncio.sleep(300)  # 5分钟后重试



    async def _check_today_ranking_data(self) -> bool:
        """检查今天是否有排行榜数据"""
        try:
            from src.cs2_investment.config.database import get_db_session
            from src.cs2_investment.models.market_snapshot import MarketSnapshot
            from sqlalchemy import func, and_

            with get_db_session() as session:
                today = date.today()
                today_start = datetime.combine(today, datetime.min.time())
                today_end = datetime.combine(today, datetime.max.time())

                # 检查今天是否有任何排行榜数据
                data_count = session.query(func.count(MarketSnapshot.item_id))\
                    .filter(
                        and_(
                            MarketSnapshot.snapshot_time >= today_start,
                            MarketSnapshot.snapshot_time <= today_end,
                            # 至少有一种有效数据（热度排名或价格变化或成交量）
                            func.coalesce(
                                MarketSnapshot.hot_rank,
                                MarketSnapshot.diff_1d,
                                MarketSnapshot.diff_3d,
                                MarketSnapshot.diff_7d,
                                MarketSnapshot.diff_1m,
                                MarketSnapshot.trans_count_1d,
                                MarketSnapshot.trans_count_3d,
                                MarketSnapshot.trans_count_7d,
                                MarketSnapshot.trans_count_1m
                            ).isnot(None)
                        )
                    ).scalar()

                logger.info(f"📊 今天({today})排行榜数据统计: {data_count} 条记录")
                return data_count > 0

        except Exception as e:
            logger.error(f"❌ 检查今天排行榜数据失败: {e}")
            return False

    async def _filter_unanalyzed_items(self, ranking_items: List, target_date: date) -> List:
        """过滤当天已有推荐记录的饰品，只返回需要处理的饰品"""
        try:
            if not ranking_items:
                return []

            # 查询当天已有推荐记录的饰品ID
            from src.cs2_investment.dao.investment_recommendation_dao import investment_recommendation_dao
            recommended_item_ids = investment_recommendation_dao.get_recommended_item_ids_by_date(target_date)
            recommended_set = set(recommended_item_ids)

            # 过滤出需要处理的饰品（排除已有推荐记录的饰品）
            # 现在ranking_items是字典列表，需要使用字典访问方式
            unprocessed_items = []
            for item in ranking_items:
                item_id = item.get('item_id') if isinstance(item, dict) else getattr(item, 'item_id', None)
                if item_id and item_id not in recommended_set:
                    unprocessed_items.append(item)

            logger.info(f"📊 当天处理状态: 总饰品 {len(ranking_items)} 个, 已推荐 {len(recommended_set)} 个, 待处理 {len(unprocessed_items)} 个")

            if recommended_set:
                logger.info(f"⏭️ 跳过当天已推荐的 {len(recommended_set)} 个饰品")

            return unprocessed_items

        except Exception as e:
            logger.error(f"❌ 过滤已处理饰品失败: {e}")
            # 异常情况下返回原列表，避免丢失数据
            return ranking_items

    async def _get_items_to_analyze(self) -> List[Dict[str, Any]]:
        """获取当天所有排行榜饰品列表，按时间顺序排序（越早越先分析）"""
        try:
            today = date.today()
            logger.info(f"📊 开始获取当天({today})排行榜数据（不包括选品表数据）...")

            # 检查今天是否有排行榜数据
            has_data = await self._check_today_ranking_data()
            if not has_data:
                logger.warning(f"⚠️ 今天({today})没有排行榜数据")
                return []

            # 直接获取今天所有有排行榜数据的饰品
            from src.cs2_investment.config.database import get_db_session
            from src.cs2_investment.models.market_snapshot import MarketSnapshot
            from sqlalchemy import and_, or_

            with get_db_session() as session:
                today_start = datetime.combine(today, datetime.min.time())
                today_end = datetime.combine(today, datetime.max.time())

                # 获取今天所有有排行榜相关数据的快照
                snapshots = session.query(MarketSnapshot)\
                    .filter(
                        and_(
                            MarketSnapshot.snapshot_time >= today_start,
                            MarketSnapshot.snapshot_time <= today_end,
                            # 至少有一种排行榜相关数据
                            or_(
                                MarketSnapshot.hot_rank.isnot(None),
                                MarketSnapshot.diff_1d.isnot(None),
                                MarketSnapshot.diff_3d.isnot(None),
                                MarketSnapshot.diff_7d.isnot(None),
                                MarketSnapshot.diff_1m.isnot(None),
                                MarketSnapshot.trans_count_1d.isnot(None),
                                MarketSnapshot.trans_count_3d.isnot(None),
                                MarketSnapshot.trans_count_7d.isnot(None),
                                MarketSnapshot.trans_count_1m.isnot(None)
                            )
                        )
                    )\
                    .order_by(MarketSnapshot.snapshot_time.asc())\
                    .all()

                logger.info(f"📊 获取今天所有排行榜数据: {len(snapshots)} 条记录")

                # 按item_id去重，保留最早的记录（因为已经按时间排序）
                # 直接提取需要的数据，避免Session绑定问题
                unique_items = {}
                for snapshot in snapshots:
                    item_id = snapshot.item_id
                    if item_id not in unique_items:
                        # 在Session内提取所有需要的数据
                        try:
                            item_name = snapshot.item.name if snapshot.item else item_id
                            market_hash_name = snapshot.item.market_hash_name if snapshot.item else None
                        except Exception as e:
                            logger.warning(f"处理快照数据失败: {item_id}, 错误: {e}")
                            continue

                        # 创建纯数据字典，不包含SQLAlchemy对象
                        item_data = {
                            'item_id': item_id,
                            'name': item_name,
                            'market_hash_name': market_hash_name,
                            'snapshot_date': snapshot.snapshot_date,
                            'snapshot_time': snapshot.snapshot_time,
                            'hot_rank': snapshot.hot_rank,
                            'current_price': snapshot.current_price,
                            'diff_1d': snapshot.diff_1d,
                            'diff_7d': snapshot.diff_7d,
                            'diff_1m': snapshot.diff_1m,
                            'trans_count_1d': snapshot.trans_count_1d,
                            'trans_count_7d': snapshot.trans_count_7d,
                            'trans_count_1m': snapshot.trans_count_1m
                        }

                        unique_items[item_id] = item_data

                unique_snapshots = list(unique_items.values())
                logger.info(f"📊 去重后饰品数量: {len(unique_snapshots)} 个")

            # 在Session外进行后续处理
            # 过滤当天已分析的饰品
            unanalyzed_items = await self._filter_unanalyzed_items(unique_snapshots, today)

            if not unanalyzed_items:
                logger.info("✅ 今日所有排行榜饰品已完成分析")
                return []

            logger.info(f"📋 待分析饰品: {len(unanalyzed_items)} 个")

            # 按snapshot_time排序（越早越先分析）
            unanalyzed_items.sort(key=lambda x: x['snapshot_time'])

            # 转换为分析格式
            items_to_analyze = []
            for item_data in unanalyzed_items:
                try:
                    market_hash_name = item_data.get('market_hash_name')
                    if not market_hash_name:
                        logger.warning(f"饰品 {item_data['item_id']} 缺少 market_hash_name，跳过")
                        continue

                    # 构建正确的URL，使用URL编码的market_hash_name
                    import urllib.parse
                    encoded_name = urllib.parse.quote(market_hash_name, safe='')
                    item_url = f"https://steamdt.com/cs2/{encoded_name}"

                    # 获取饰品名称
                    item_name = item_data.get('name') or market_hash_name.replace('%20', ' ').replace('%7C', '|').replace('%28', '(').replace('%29', ')')

                    items_to_analyze.append({
                        'item_id': item_data['item_id'],
                        'name': item_name,
                        'market_hash_name': market_hash_name,
                        'url': item_url,
                        'hot_rank': item_data.get('hot_rank'),
                        'snapshot_date': item_data.get('snapshot_date'),
                        'snapshot_time': item_data.get('snapshot_time')
                    })

                except Exception as e:
                    logger.warning(f"处理快照数据失败: {item_data['item_id']}, 错误: {e}")
                    continue

            # 按时间排序（越早越先分析）
            items_to_analyze.sort(key=lambda x: x.get('snapshot_time', datetime.max))
            
            logger.info(f"📊 获取到 {len(items_to_analyze)} 个排行榜饰品（按时间排序）")
            return items_to_analyze
            
        except Exception as e:
            logger.error(f"❌ 获取待分析饰品失败: {e}")
            return []
    
    async def _run_single_analysis(self, item: Dict[str, Any]) -> bool:
        """运行单个饰品的分析"""
        start_time = datetime.now()
        
        try:
            # 原始方式（注释掉但保留）
            # result = await self.analysis_system.run_complete_analysis(
            #     item_url=item['url'],
            #     item_name=item['name']
            # )

            # 新方式：使用统一分析系统，一次获得实时分析和投资分析结果
            result = await self.analysis_system.run_complete_analysis(
                item_url=item['url'],
                item_name=item['name']
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            if result.get('success'):
                logger.info(f"✅ 统一分析完成: {item['name']} (耗时: {duration:.1f}秒)")

                # 原始结果处理方式（注释掉但保留）
                # investment_result = result.get('investment_result', {})
                # if investment_result.get('success'):
                #     logger.info(f"📊 投资推荐: {investment_result.get('recommendations_count', 0)} 条")
                # else:
                #     logger.warning(f"⚠️ 投资推荐失败: {investment_result.get('error', '未知错误')}")

                # 新的结果处理方式：统一分析系统结果
                realtime_analysis = result.get('realtime_analysis', {})
                investment_analysis = result.get('investment_analysis', {})

                # 检查实时分析结果
                if realtime_analysis.get('success'):
                    logger.info(f"📊 实时分析成功: {item['name']}")
                else:
                    logger.warning(f"⚠️ 实时分析失败: {realtime_analysis.get('error', '未知错误')}")

                # 检查投资分析结果
                if investment_analysis.get('success'):
                    logger.info(f"📈 投资分析成功: {item['name']}")
                else:
                    logger.warning(f"⚠️ 投资分析失败: {investment_analysis.get('error', '未知错误')}")

                # 显示统一报告信息
                unified_report = result.get('unified_report', {})
                if unified_report:
                    summary = unified_report.get('summary', {})
                    logger.info(f"📋 统一报告: {summary.get('overall_status', '未知状态')}")

                return True
            else:
                logger.error(f"❌ 分析失败: {item['name']}, 错误: {result.get('error', '未知错误')}")
                return False
                
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.error(f"❌ 分析异常: {item['name']} (耗时: {duration:.1f}秒), 错误: {e}")
            return False


# 全局实例
simple_investment_scheduler = SimpleInvestmentScheduler()


async def main():
    """测试主函数"""
    try:
        await simple_investment_scheduler.start()
        
        # 运行一段时间
        await asyncio.sleep(3600)  # 运行1小时
        
    except KeyboardInterrupt:
        logger.info("收到中断信号")
    finally:
        await simple_investment_scheduler.stop()


if __name__ == "__main__":
    asyncio.run(main())
