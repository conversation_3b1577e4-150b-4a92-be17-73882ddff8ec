"""
战略分析器

基于周K数据进行长期投资价值分析，识别主要趋势方向、
支撑阻力位、投资价值评分等战略层面的分析。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from loguru import logger

from ...syncps.technical_indicator_calculator import TechnicalIndicatorCalculator


class StrategicAnalyzer:
    """战略分析器 - 基于周K数据"""
    
    def __init__(self, weekly_data: pd.DataFrame):
        """
        初始化战略分析器
        
        Args:
            weekly_data: 周K数据
        """
        self.weekly_data = weekly_data.copy() if weekly_data is not None else pd.DataFrame()
        self.logger = logger.bind(analyzer=self.__class__.__name__)
        
        # 初始化技术指标计算器
        if not self.weekly_data.empty:
            self.indicator_calculator = TechnicalIndicatorCalculator(
                daily_data=self.weekly_data,  # 使用周K数据作为日K数据输入
                weekly_data=self.weekly_data
            )
        else:
            self.indicator_calculator = None
        
        # 分析结果缓存
        self.analysis_cache = {}
    
    def analyze_strategic_trend(self) -> Dict[str, Any]:
        """
        分析战略趋势 - 重构版本

        Returns:
            Dict包含详细的文字描述分析结果，符合需求文档要求
        """
        try:
            if self.weekly_data.empty:
                return {
                    'trend_direction': '数据不足',
                    'trend_description': '缺少周K数据，无法进行战略趋势分析',
                    'trend_reasoning': '需要至少26周的历史数据才能进行可靠的长期趋势分析',
                    'error': '缺少周K数据'
                }

            self.logger.info("开始战略趋势分析...")

            # 计算长期移动平均线
            prices = self.weekly_data['close']

            # 长期趋势指标
            sma_13 = prices.rolling(window=13).mean()  # 13周移动平均
            sma_26 = prices.rolling(window=26).mean()  # 26周移动平均
            sma_52 = prices.rolling(window=52).mean()  # 52周移动平均

            # 当前价格相对位置
            current_price = prices.iloc[-1]

            # 趋势判断和描述生成
            if len(sma_13) >= 1 and len(sma_26) >= 1:
                latest_sma_13 = sma_13.iloc[-1]
                latest_sma_26 = sma_26.iloc[-1]

                if pd.isna(latest_sma_13) or pd.isna(latest_sma_26):
                    trend_direction = '数据不足'
                    trend_description = '移动平均线数据不完整，无法准确判断趋势方向'
                    trend_reasoning = '需要更多历史数据来计算可靠的移动平均线'
                else:
                    # 生成详细的趋势分析
                    trend_analysis = self._generate_trend_analysis(current_price, latest_sma_13, latest_sma_26, sma_52)
                    trend_direction = trend_analysis['direction']
                    trend_description = trend_analysis['description']
                    trend_reasoning = trend_analysis['reasoning']
            else:
                trend_direction = '数据不足'
                trend_description = '历史数据不足，无法计算长期移动平均线'
                trend_reasoning = '战略分析需要至少26周的历史数据'

            # 趋势持续时间分析
            trend_duration_weeks = self._calculate_trend_duration(prices, sma_13)
            duration_description = self._describe_trend_duration(trend_duration_weeks)

            result = {
                'trend_direction': trend_direction,
                'trend_description': trend_description,
                'trend_reasoning': trend_reasoning,
                'trend_duration_description': duration_description,
                'current_price': current_price,
                'sma_13': latest_sma_13 if 'latest_sma_13' in locals() and not pd.isna(latest_sma_13) else None,
                'sma_26': latest_sma_26 if 'latest_sma_26' in locals() and not pd.isna(latest_sma_26) else None,
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"战略趋势分析完成: {trend_direction}")
            return result

        except Exception as e:
            self.logger.error(f"战略趋势分析失败: {e}")
            return {
                'trend_direction': '分析异常',
                'trend_description': f'战略趋势分析过程中发生异常: {str(e)}',
                'trend_reasoning': '请检查数据质量或联系技术支持',
                'error': str(e)
            }
    
    def analyze_investment_value(self) -> Dict[str, Any]:
        """
        分析投资价值 - 重构版本

        Returns:
            Dict包含详细的投资价值分析文字描述
        """
        try:
            if self.weekly_data.empty:
                return {
                    'value_conclusion': '数据不足',
                    'value_analysis': '缺少周K数据，无法进行投资价值评估',
                    'value_reasoning': '投资价值分析需要充足的历史价格和成交量数据',
                    'error': '缺少周K数据'
                }

            self.logger.info("开始投资价值分析...")

            prices = self.weekly_data['close']
            volumes = self.weekly_data['volume']

            # 分析各个价值因素
            stability_analysis = self._analyze_price_stability(prices)
            trend_analysis = self.analyze_strategic_trend()
            volume_analysis = self._analyze_volume_health(volumes)
            technical_analysis = self._analyze_technical_health()

            # 生成综合投资价值评估
            value_assessment = self._generate_value_assessment(
                stability_analysis, trend_analysis, volume_analysis, technical_analysis
            )

            result = {
                'value_conclusion': value_assessment['conclusion'],
                'value_analysis': value_assessment['analysis'],
                'value_reasoning': value_assessment['reasoning'],
                'stability_factor': stability_analysis,
                'trend_factor': trend_analysis['trend_direction'],
                'volume_factor': volume_analysis,
                'technical_factor': technical_analysis,
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"投资价值分析完成: {value_assessment['conclusion']}")
            return result

        except Exception as e:
            self.logger.error(f"投资价值分析失败: {e}")
            return {
                'value_conclusion': '分析异常',
                'value_analysis': f'投资价值分析过程中发生异常: {str(e)}',
                'value_reasoning': '请检查数据质量或联系技术支持',
                'error': str(e)
            }
    
    def calculate_key_levels(self) -> Dict[str, Any]:
        """
        计算关键价位 - 重构版本

        Returns:
            Dict包含重要支撑位和阻力位的具体数值和说明
        """
        try:
            if self.weekly_data.empty:
                return {
                    'key_levels_description': '数据不足，无法计算关键价位',
                    'support_analysis': '缺少周K数据，无法识别支撑位',
                    'resistance_analysis': '缺少周K数据，无法识别阻力位',
                    'error': '缺少周K数据'
                }

            self.logger.info("开始计算关键价位...")

            highs = self.weekly_data['high']
            lows = self.weekly_data['low']
            closes = self.weekly_data['close']
            current_price = closes.iloc[-1]

            # 寻找重要的高点和低点
            resistance_levels = self._find_resistance_levels(highs, closes)
            support_levels = self._find_support_levels(lows, closes)

            # 生成关键价位分析
            levels_analysis = self._generate_key_levels_analysis(
                current_price, support_levels, resistance_levels
            )

            result = {
                'key_levels_description': levels_analysis['description'],
                'support_analysis': levels_analysis['support_analysis'],
                'resistance_analysis': levels_analysis['resistance_analysis'],
                'major_support_1': support_levels[0] if len(support_levels) > 0 else None,
                'major_support_2': support_levels[1] if len(support_levels) > 1 else None,
                'major_resistance_1': resistance_levels[0] if len(resistance_levels) > 0 else None,
                'major_resistance_2': resistance_levels[1] if len(resistance_levels) > 1 else None,
                'current_price': current_price,
                'analysis_timestamp': datetime.now()
            }

            self.logger.info("关键价位计算完成")
            return result

        except Exception as e:
            self.logger.error(f"关键价位计算失败: {e}")
            return {
                'key_levels_description': f'关键价位计算异常: {str(e)}',
                'support_analysis': '支撑位分析异常，请检查数据质量',
                'resistance_analysis': '阻力位分析异常，请检查数据质量',
                'error': str(e)
            }
    
    def assess_strategic_risk(self) -> Dict[str, Any]:
        """
        评估战略风险 - 重构版本

        Returns:
            Dict包含详细的风险等级和风险因素说明
        """
        try:
            if self.weekly_data.empty:
                return {
                    'risk_level': '无法评估',
                    'risk_description': '缺少周K数据，无法进行战略风险评估',
                    'risk_factors': '风险评估需要充足的历史价格数据',
                    'error': '缺少周K数据'
                }

            self.logger.info("开始战略风险评估...")

            prices = self.weekly_data['close']

            # 分析各类风险因素
            volatility_risk = self._analyze_volatility_risk(prices)
            trend_risk = self._analyze_trend_risk()
            liquidity_risk = self._analyze_liquidity_risk()

            # 生成综合风险评估
            risk_assessment = self._generate_risk_assessment(volatility_risk, trend_risk, liquidity_risk)

            result = {
                'risk_level': risk_assessment['level'],
                'risk_description': risk_assessment['description'],
                'risk_factors': risk_assessment['factors'],
                'risk_mitigation': risk_assessment['mitigation'],
                'volatility_analysis': volatility_risk,
                'trend_risk_analysis': trend_risk,
                'liquidity_risk_analysis': liquidity_risk,
                'analysis_timestamp': datetime.now()
            }

            self.logger.info(f"战略风险评估完成: {risk_assessment['level']}")
            return result

        except Exception as e:
            self.logger.error(f"战略风险评估失败: {e}")
            return {
                'risk_level': '评估异常',
                'risk_description': f'战略风险评估过程中发生异常: {str(e)}',
                'risk_factors': '请检查数据质量或联系技术支持',
                'error': str(e)
            }
            
        except Exception as e:
            self.logger.error(f"战略风险评估失败: {e}")
            return {
                'strategic_risk_level': 'ERROR',
                'risk_factors': {},
                'error': str(e)
            }
    
    def identify_major_support_resistance(self) -> Dict[str, Any]:
        """识别主要支撑阻力位"""
        try:
            if self.weekly_data.empty:
                return {
                    'major_support': None,
                    'major_resistance': None,
                    'support_strength': 'UNKNOWN',
                    'resistance_strength': 'UNKNOWN',
                    'description': '缺少周K数据，无法识别支撑阻力位',
                    'error': '缺少数据'
                }

            self.logger.info("开始识别主要支撑阻力位...")

            # 获取价格数据
            highs = self.weekly_data['high']
            lows = self.weekly_data['low']
            closes = self.weekly_data['close']

            # 计算支撑阻力位
            recent_high = highs.iloc[-26:].max()  # 最近26周最高价
            recent_low = lows.iloc[-26:].min()    # 最近26周最低价
            current_price = closes.iloc[-1]

            # 计算强度
            high_touches = sum(1 for h in highs.iloc[-26:] if abs(h - recent_high) / recent_high < 0.02)
            low_touches = sum(1 for l in lows.iloc[-26:] if abs(l - recent_low) / recent_low < 0.02)

            resistance_strength = 'STRONG' if high_touches >= 3 else 'MODERATE' if high_touches >= 2 else 'WEAK'
            support_strength = 'STRONG' if low_touches >= 3 else 'MODERATE' if low_touches >= 2 else 'WEAK'

            return {
                'major_support': recent_low,
                'major_resistance': recent_high,
                'support_strength': support_strength,
                'resistance_strength': resistance_strength,
                'current_price': current_price,
                'description': f'主要阻力位: {recent_high:.2f} ({resistance_strength}), 主要支撑位: {recent_low:.2f} ({support_strength})',
                'analysis_timestamp': datetime.now()
            }

        except Exception as e:
            self.logger.error(f"识别主要支撑阻力位失败: {e}")
            return {
                'major_support': None,
                'major_resistance': None,
                'support_strength': 'ERROR',
                'resistance_strength': 'ERROR',
                'description': f'支撑阻力位识别失败: {str(e)}',
                'error': str(e)
            }

    def generate_strategic_analysis_report(self) -> Dict[str, Any]:
        """生成战略分析报告"""
        try:
            self.logger.info("生成战略分析报告...")

            # 执行各项分析
            trend_analysis = self.analyze_strategic_trend()
            value_analysis = self.analyze_investment_value()
            support_resistance = self.identify_major_support_resistance()
            risk_assessment = self.assess_strategic_risk()
            
            # 综合报告
            report = {
                'analysis_type': 'STRATEGIC',
                'timeframe': 'WEEKLY',
                'analysis_timestamp': datetime.now(),
                'data_quality': 'GOOD' if not self.weekly_data.empty else 'NO_DATA',
                'data_points': len(self.weekly_data),
                
                # 各项分析结果
                'trend_analysis': trend_analysis,
                'investment_value': value_analysis,
                'support_resistance': support_resistance,
                'risk_assessment': risk_assessment,
                
                # 综合评估
                'overall_assessment': self._generate_overall_assessment(
                    trend_analysis, value_analysis, risk_assessment
                )
            }
            
            self.logger.info("战略分析报告生成完成")
            return report
            
        except Exception as e:
            self.logger.error(f"战略分析报告生成失败: {e}")
            return {
                'analysis_type': 'STRATEGIC',
                'error': str(e),
                'analysis_timestamp': datetime.now()
            }

    def _calculate_trend_duration(self, prices: pd.Series, sma: pd.Series) -> int:
        """计算趋势持续时间（周数）"""
        try:
            if len(prices) < 2 or len(sma) < 2:
                return 0

            current_trend = 'UP' if prices.iloc[-1] > sma.iloc[-1] else 'DOWN'
            duration = 1

            for i in range(len(prices) - 2, -1, -1):
                if pd.isna(sma.iloc[i]):
                    break

                trend = 'UP' if prices.iloc[i] > sma.iloc[i] else 'DOWN'
                if trend == current_trend:
                    duration += 1
                else:
                    break

            return duration

        except Exception:
            return 0

    def _calculate_technical_health_score(self) -> int:
        """计算技术指标健康度评分"""
        try:
            if self.indicator_calculator is None:
                return 10

            # 获取技术指标
            indicators = self.indicator_calculator.calculate_all_indicators()

            score = 0

            # RSI健康度
            if 'rsi' in indicators and not indicators['rsi'].empty:
                rsi_value = indicators['rsi'].iloc[-1]
                if 30 <= rsi_value <= 70:
                    score += 8  # RSI在健康区间
                elif 20 <= rsi_value <= 80:
                    score += 5
                else:
                    score += 2

            # MACD健康度
            if 'macd' in indicators and not indicators['macd'].empty:
                macd_value = indicators['macd'].iloc[-1]
                if abs(macd_value) < 0.5:
                    score += 8  # MACD相对稳定
                else:
                    score += 5

            # 布林带健康度
            if all(k in indicators for k in ['bb_upper', 'bb_lower']) and not indicators['bb_upper'].empty:
                current_price = self.weekly_data['close'].iloc[-1]
                bb_upper = indicators['bb_upper'].iloc[-1]
                bb_lower = indicators['bb_lower'].iloc[-1]

                bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
                if 0.2 <= bb_position <= 0.8:
                    score += 9  # 价格在布林带中部
                else:
                    score += 5

            return min(25, score)

        except Exception:
            return 10

    def _calculate_technical_risk_score(self) -> int:
        """计算技术指标风险评分"""
        try:
            if self.indicator_calculator is None:
                return 50

            indicators = self.indicator_calculator.calculate_all_indicators()
            risk_score = 0

            # RSI风险
            if 'rsi' in indicators and not indicators['rsi'].empty:
                rsi_value = indicators['rsi'].iloc[-1]
                if rsi_value > 80 or rsi_value < 20:
                    risk_score += 30  # 超买超卖风险
                elif rsi_value > 70 or rsi_value < 30:
                    risk_score += 20
                else:
                    risk_score += 10

            # MACD风险
            if 'macd' in indicators and not indicators['macd'].empty:
                macd_value = indicators['macd'].iloc[-1]
                if abs(macd_value) > 1.0:
                    risk_score += 25  # MACD极值风险
                else:
                    risk_score += 10

            # 价格位置风险
            if all(k in indicators for k in ['bb_upper', 'bb_lower']) and not indicators['bb_upper'].empty:
                current_price = self.weekly_data['close'].iloc[-1]
                bb_upper = indicators['bb_upper'].iloc[-1]
                bb_lower = indicators['bb_lower'].iloc[-1]

                bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
                if bb_position > 0.9 or bb_position < 0.1:
                    risk_score += 25  # 价格极端位置风险
                else:
                    risk_score += 10

            return min(100, risk_score)

        except Exception:
            return 50

    def _find_resistance_levels(self, highs: pd.Series, closes: pd.Series) -> List[float]:
        """寻找阻力位"""
        try:
            resistance_levels = []

            # 寻找局部高点
            for i in range(2, len(highs) - 2):
                if (highs.iloc[i] > highs.iloc[i-1] and highs.iloc[i] > highs.iloc[i-2] and
                    highs.iloc[i] > highs.iloc[i+1] and highs.iloc[i] > highs.iloc[i+2]):
                    resistance_levels.append(highs.iloc[i])

            # 按重要性排序（价格高低和出现频率）
            resistance_levels = sorted(set(resistance_levels), reverse=True)

            # 过滤掉过于接近的阻力位
            filtered_levels = []
            for level in resistance_levels:
                if not filtered_levels or abs(level - filtered_levels[-1]) / level > 0.02:
                    filtered_levels.append(level)

            return filtered_levels[:5]  # 返回最多5个阻力位

        except Exception:
            return []

    def _find_support_levels(self, lows: pd.Series, closes: pd.Series) -> List[float]:
        """寻找支撑位"""
        try:
            support_levels = []

            # 寻找局部低点
            for i in range(2, len(lows) - 2):
                if (lows.iloc[i] < lows.iloc[i-1] and lows.iloc[i] < lows.iloc[i-2] and
                    lows.iloc[i] < lows.iloc[i+1] and lows.iloc[i] < lows.iloc[i+2]):
                    support_levels.append(lows.iloc[i])

            # 按重要性排序（价格高低和出现频率）
            support_levels = sorted(set(support_levels), reverse=True)

            # 过滤掉过于接近的支撑位
            filtered_levels = []
            for level in support_levels:
                if not filtered_levels or abs(level - filtered_levels[-1]) / level > 0.02:
                    filtered_levels.append(level)

            return filtered_levels[:5]  # 返回最多5个支撑位

        except Exception:
            return []

    def _get_value_level(self, score: int) -> str:
        """根据评分获取价值等级"""
        if score >= 80:
            return 'EXCELLENT'
        elif score >= 60:
            return 'GOOD'
        elif score >= 40:
            return 'FAIR'
        elif score >= 20:
            return 'POOR'
        else:
            return 'VERY_POOR'

    def _generate_overall_assessment(self, trend_analysis: Dict, value_analysis: Dict, risk_assessment: Dict) -> Dict:
        """生成综合评估"""
        try:
            assessment = {
                'recommendation': 'HOLD',
                'confidence': 50,
                'key_points': [],
                'warnings': []
            }

            # 基于趋势分析
            trend_direction = trend_analysis.get('trend_direction', '中性')

            # 根据趋势方向判断（修正字符串比较错误）
            if trend_direction in ['强烈看涨', '看涨']:
                assessment['recommendation'] = 'BUY'
                assessment['confidence'] += 20
                assessment['key_points'].append(f"战略趋势：{trend_direction}")
            elif trend_direction in ['强烈看跌', '看跌']:
                assessment['recommendation'] = 'SELL'
                assessment['confidence'] += 20
                assessment['key_points'].append(f"战略趋势：{trend_direction}")

            # 基于投资价值
            value_score = value_analysis.get('investment_value_score', 0)
            if value_score > 70:
                assessment['confidence'] += 15
                assessment['key_points'].append(f"投资价值优秀({value_score}分)")
            elif value_score < 30:
                assessment['confidence'] -= 10
                assessment['warnings'].append(f"投资价值较低({value_score}分)")

            # 基于风险评估
            risk_level = risk_assessment.get('strategic_risk_level', 'UNKNOWN')
            if risk_level == 'HIGH':
                assessment['confidence'] -= 20
                assessment['warnings'].append("战略风险较高")
            elif risk_level == 'LOW':
                assessment['confidence'] += 10
                assessment['key_points'].append("战略风险较低")

            # 限制置信度范围
            assessment['confidence'] = max(0, min(100, assessment['confidence']))

            return assessment

        except Exception as e:
            return {
                'recommendation': 'HOLD',
                'confidence': 50,
                'key_points': [],
                'warnings': [f"评估生成异常: {str(e)}"]
            }

    def _generate_trend_analysis(self, current_price: float, sma_13: float, sma_26: float, sma_52: pd.Series) -> Dict[str, str]:
        """生成详细的趋势分析描述"""
        try:
            # 计算价格相对于移动平均线的位置
            price_vs_sma13_pct = ((current_price - sma_13) / sma_13) * 100
            price_vs_sma26_pct = ((current_price - sma_26) / sma_26) * 100

            # 计算移动平均线的相对关系
            sma13_vs_sma26_pct = ((sma_13 - sma_26) / sma_26) * 100

            # 获取52周移动平均线（如果有足够数据）
            sma_52_current = None
            if len(sma_52) > 0 and not pd.isna(sma_52.iloc[-1]):
                sma_52_current = sma_52.iloc[-1]

            # 趋势强度判断
            if current_price > sma_13 > sma_26:
                if price_vs_sma26_pct > 15:
                    direction = '强烈看涨'
                    description = f'当前价格{current_price:.2f}元显著高于13周均线({sma_13:.2f}元)和26周均线({sma_26:.2f}元)，价格较26周均线高出{price_vs_sma26_pct:.1f}%，显示出强劲的上升动能。'
                    reasoning = '多条移动平均线呈现多头排列，且价格大幅偏离长期均线，表明市场处于强势上涨阶段，适合长期持有。'
                elif price_vs_sma26_pct > 5:
                    direction = '看涨'
                    description = f'当前价格{current_price:.2f}元位于13周均线({sma_13:.2f}元)和26周均线({sma_26:.2f}元)之上，较26周均线高出{price_vs_sma26_pct:.1f}%，呈现稳健的上升趋势。'
                    reasoning = '移动平均线系统支撑价格上涨，虽然涨幅相对温和，但趋势稳定，适合逐步建仓。'
                else:
                    direction = '看涨'
                    description = f'当前价格{current_price:.2f}元刚好突破13周均线({sma_13:.2f}元)和26周均线({sma_26:.2f}元)，显示初步的上涨信号。'
                    reasoning = '价格刚刚站上关键均线，上涨趋势初现，但需要观察是否能够持续。'

            elif current_price < sma_13 < sma_26:
                if abs(price_vs_sma26_pct) > 15:
                    direction = '强烈看跌'
                    description = f'当前价格{current_price:.2f}元大幅低于13周均线({sma_13:.2f}元)和26周均线({sma_26:.2f}元)，较26周均线低{abs(price_vs_sma26_pct):.1f}%，显示强烈的下跌压力。'
                    reasoning = '移动平均线呈现空头排列，价格深度回调，市场情绪悲观，建议避免介入或考虑减仓。'
                elif abs(price_vs_sma26_pct) > 5:
                    direction = '看跌'
                    description = f'当前价格{current_price:.2f}元位于13周均线({sma_13:.2f}元)和26周均线({sma_26:.2f}元)下方，较26周均线低{abs(price_vs_sma26_pct):.1f}%，处于下降通道中。'
                    reasoning = '移动平均线对价格形成压制，下跌趋势明确，建议谨慎操作，等待趋势转换信号。'
                else:
                    direction = '看跌'
                    description = f'当前价格{current_price:.2f}元跌破13周均线({sma_13:.2f}元)和26周均线({sma_26:.2f}元)，显示初步的下跌信号。'
                    reasoning = '价格失守关键均线支撑，下跌风险增加，需要密切关注后续走势。'

            elif current_price > sma_13 and sma_13 < sma_26:
                direction = '中性偏涨'
                description = f'当前价格{current_price:.2f}元高于13周均线({sma_13:.2f}元)但13周均线仍低于26周均线({sma_26:.2f}元)，显示短期反弹但长期趋势尚未确立。'
                reasoning = '短期均线开始上翘，但长期均线仍处劣势，可能是反弹行情，需要观察是否能够持续突破。'

            elif current_price < sma_13 and sma_13 > sma_26:
                direction = '中性偏跌'
                description = f'当前价格{current_price:.2f}元低于13周均线({sma_13:.2f}元)但13周均线高于26周均线({sma_26:.2f}元)，显示短期调整但长期趋势仍然向上。'
                reasoning = '长期趋势依然向好，当前可能是技术性调整，可以考虑逢低布局的机会。'

            else:
                direction = '中性'
                description = f'当前价格{current_price:.2f}元与13周均线({sma_13:.2f}元)和26周均线({sma_26:.2f}元)交织运行，趋势方向不明确。'
                reasoning = '移动平均线系统混乱，市场处于震荡整理阶段，建议等待明确的方向性突破。'

            # 如果有52周均线数据，添加长期背景分析
            if sma_52_current:
                if current_price > sma_52_current:
                    description += f' 从更长期视角看，价格仍位于52周均线({sma_52_current:.2f}元)之上，长期趋势基础相对稳固。'
                else:
                    description += f' 从更长期视角看，价格已跌破52周均线({sma_52_current:.2f}元)，长期趋势面临考验。'

            return {
                'direction': direction,
                'description': description,
                'reasoning': reasoning
            }

        except Exception as e:
            return {
                'direction': '分析异常',
                'description': f'趋势分析过程中发生异常: {str(e)}',
                'reasoning': '请检查数据质量或联系技术支持'
            }

    def _describe_trend_duration(self, duration_weeks: int) -> str:
        """描述趋势持续时间"""
        if duration_weeks <= 0:
            return '趋势刚刚开始或数据不足以判断持续时间'
        elif duration_weeks <= 4:
            return f'当前趋势已持续约{duration_weeks}周，属于短期趋势，需要观察是否能够延续'
        elif duration_weeks <= 12:
            return f'当前趋势已持续约{duration_weeks}周，属于中期趋势，具有一定的稳定性'
        elif duration_weeks <= 26:
            return f'当前趋势已持续约{duration_weeks}周，属于较长期趋势，趋势稳定性较强'
        else:
            return f'当前趋势已持续约{duration_weeks}周，属于长期趋势，但需要警惕趋势反转的可能性'

    def _analyze_price_stability(self, prices: pd.Series) -> str:
        """分析价格稳定性"""
        try:
            price_volatility = prices.pct_change().std()

            if price_volatility < 0.05:
                return '价格波动极小，显示出优秀的稳定性，适合稳健型投资者长期持有'
            elif price_volatility < 0.10:
                return '价格波动较小，稳定性良好，风险相对可控'
            elif price_volatility < 0.15:
                return '价格波动适中，存在一定的投资机会但需要承担相应风险'
            elif price_volatility < 0.20:
                return '价格波动较大，投资风险偏高，需要谨慎评估风险承受能力'
            else:
                return '价格波动剧烈，投资风险很高，不适合风险厌恶型投资者'

        except Exception:
            return '价格稳定性分析异常，无法准确评估'

    def _analyze_volume_health(self, volumes: pd.Series) -> str:
        """分析成交量健康度"""
        try:
            if volumes.empty:
                return '缺少成交量数据，无法评估市场活跃度'

            # 计算近期与历史成交量对比
            volume_trend = volumes.rolling(window=4).mean()
            if len(volume_trend) >= 8:
                recent_volume = volume_trend.iloc[-4:].mean()
                historical_volume = volume_trend.iloc[-8:-4].mean()
                volume_ratio = recent_volume / historical_volume if historical_volume > 0 else 1

                if volume_ratio > 1.5:
                    return '成交量显著放大，市场关注度大幅提升，流动性优秀'
                elif volume_ratio > 1.2:
                    return '成交量温和放大，市场活跃度提升，流动性良好'
                elif volume_ratio > 0.8:
                    return '成交量保持稳定，市场活跃度正常，流动性适中'
                elif volume_ratio > 0.5:
                    return '成交量有所萎缩，市场关注度下降，流动性偏弱'
                else:
                    return '成交量大幅萎缩，市场活跃度低迷，流动性较差'
            else:
                avg_volume = volumes.mean()
                if avg_volume > volumes.median() * 1.5:
                    return '平均成交量较高，市场活跃度良好'
                else:
                    return '平均成交量一般，市场活跃度中等'

        except Exception:
            return '成交量健康度分析异常，无法准确评估'

    def _analyze_technical_health(self) -> str:
        """分析技术指标健康度"""
        try:
            if not self.indicator_calculator:
                return '技术指标计算器未初始化，无法进行技术健康度分析'

            # 这里可以调用技术指标计算器的方法
            # 由于原有方法可能返回数值，我们需要转换为文字描述

            # 简化的技术健康度分析
            prices = self.weekly_data['close']
            if len(prices) < 14:
                return '历史数据不足，无法计算完整的技术指标'

            # 基于价格趋势的简单技术健康度评估
            recent_trend = prices.iloc[-4:].mean() / prices.iloc[-8:-4].mean() if len(prices) >= 8 else 1

            if recent_trend > 1.1:
                return '技术指标显示强势特征，多项指标支持上涨'
            elif recent_trend > 1.05:
                return '技术指标显示偏强特征，整体技术面健康'
            elif recent_trend > 0.95:
                return '技术指标显示中性特征，技术面相对平衡'
            elif recent_trend > 0.9:
                return '技术指标显示偏弱特征，技术面承压'
            else:
                return '技术指标显示弱势特征，多项指标显示下跌压力'

        except Exception:
            return '技术指标健康度分析异常，无法准确评估'

    def _generate_value_assessment(self, stability: str, trend: Dict, volume: str, technical: str) -> Dict[str, str]:
        """生成综合投资价值评估"""
        try:
            trend_direction = trend.get('trend_direction', '中性')

            # 基于各因素生成综合评估
            positive_factors = []
            negative_factors = []
            neutral_factors = []

            # 分析趋势因素
            if '强烈看涨' in trend_direction or '看涨' in trend_direction:
                positive_factors.append('长期趋势向好')
            elif '强烈看跌' in trend_direction or '看跌' in trend_direction:
                negative_factors.append('长期趋势偏弱')
            else:
                neutral_factors.append('长期趋势不明确')

            # 分析稳定性因素
            if '优秀' in stability or '良好' in stability:
                positive_factors.append('价格稳定性佳')
            elif '较大' in stability or '剧烈' in stability:
                negative_factors.append('价格波动风险高')
            else:
                neutral_factors.append('价格稳定性一般')

            # 分析成交量因素
            if '优秀' in volume or '良好' in volume:
                positive_factors.append('市场流动性充足')
            elif '较差' in volume or '低迷' in volume:
                negative_factors.append('市场流动性不足')
            else:
                neutral_factors.append('市场流动性适中')

            # 分析技术因素
            if '强势' in technical or '健康' in technical:
                positive_factors.append('技术面支撑强劲')
            elif '弱势' in technical or '承压' in technical:
                negative_factors.append('技术面压力较大')
            else:
                neutral_factors.append('技术面相对平衡')

            # 生成综合结论
            if len(positive_factors) >= 3:
                conclusion = '投资价值较高'
                analysis = f'该饰品具备{len(positive_factors)}个积极因素：{", ".join(positive_factors)}。'
                reasoning = '多项指标显示积极信号，适合中长期投资配置，建议逢低布局。'
            elif len(positive_factors) >= 2 and len(negative_factors) <= 1:
                conclusion = '投资价值中等偏上'
                analysis = f'该饰品具备{len(positive_factors)}个积极因素：{", ".join(positive_factors)}。'
                if negative_factors:
                    analysis += f'但需要注意{negative_factors[0]}。'
                reasoning = '整体投资价值尚可，可以适量配置，但需要控制仓位。'
            elif len(negative_factors) >= 3:
                conclusion = '投资价值较低'
                analysis = f'该饰品存在{len(negative_factors)}个不利因素：{", ".join(negative_factors)}。'
                reasoning = '多项指标显示不利信号，不建议当前时点介入，建议观望等待更好时机。'
            elif len(negative_factors) >= 2:
                conclusion = '投资价值中等偏下'
                analysis = f'该饰品存在{len(negative_factors)}个不利因素：{", ".join(negative_factors)}。'
                reasoning = '投资风险相对较高，如需投资建议严格控制仓位并设置止损。'
            else:
                conclusion = '投资价值中等'
                analysis = '该饰品各项指标表现平平，既无明显优势也无重大劣势。'
                reasoning = '适合作为组合配置的一部分，但不建议重仓投资。'

            return {
                'conclusion': conclusion,
                'analysis': analysis,
                'reasoning': reasoning
            }

        except Exception:
            return {
                'conclusion': '评估异常',
                'analysis': '投资价值综合评估过程中发生异常',
                'reasoning': '请检查各项分析结果或联系技术支持'
            }

    def generate_investment_advice(self) -> Dict[str, Any]:
        """
        生成投资建议 - 新增方法

        Returns:
            Dict包含基于战略分析的具体建议和持有期建议
        """
        try:
            # 获取各项分析结果
            trend_analysis = self.analyze_strategic_trend()
            value_analysis = self.analyze_investment_value()
            risk_analysis = self.assess_strategic_risk()
            levels_analysis = self.calculate_key_levels()

            # 生成综合投资建议
            advice = self._generate_comprehensive_advice(
                trend_analysis, value_analysis, risk_analysis, levels_analysis
            )

            return {
                'investment_recommendation': advice['recommendation'],
                'holding_period_advice': advice['holding_period'],
                'position_sizing_advice': advice['position_sizing'],
                'entry_strategy': advice['entry_strategy'],
                'risk_management': advice['risk_management'],
                'analysis_timestamp': datetime.now()
            }

        except Exception as e:
            self.logger.error(f"投资建议生成失败: {e}")
            return {
                'investment_recommendation': '建议生成异常，请谨慎操作',
                'holding_period_advice': '无法确定持有期，建议咨询专业人士',
                'position_sizing_advice': '建议控制仓位，分散投资风险',
                'entry_strategy': '建议等待更明确的信号',
                'risk_management': '严格设置止损，控制投资风险',
                'error': str(e)
            }

    def _analyze_volatility_risk(self, prices: pd.Series) -> str:
        """分析波动率风险"""
        try:
            volatility = prices.pct_change().std() * np.sqrt(52)  # 年化波动率

            if volatility > 0.6:
                return '波动率极高，价格变动剧烈，投资风险很大，不适合风险厌恶型投资者'
            elif volatility > 0.4:
                return '波动率较高，价格波动明显，需要较强的风险承受能力'
            elif volatility > 0.25:
                return '波动率中等，价格波动在合理范围内，风险相对可控'
            elif volatility > 0.15:
                return '波动率较低，价格相对稳定，适合稳健型投资'
            else:
                return '波动率很低，价格非常稳定，风险极小但收益潜力有限'

        except Exception:
            return '波动率风险分析异常，无法准确评估'

    def _analyze_trend_risk(self) -> str:
        """分析趋势风险"""
        try:
            trend_analysis = self.analyze_strategic_trend()
            trend_direction = trend_analysis.get('trend_direction', '中性')

            if '强烈看跌' in trend_direction:
                return '趋势风险很高，处于强烈下跌趋势中，继续下跌概率大'
            elif '看跌' in trend_direction:
                return '趋势风险较高，处于下跌趋势中，需要谨慎操作'
            elif '中性' in trend_direction:
                return '趋势风险中等，方向不明确，存在双向波动风险'
            elif '看涨' in trend_direction:
                return '趋势风险较低，处于上涨趋势中，但需防范回调风险'
            elif '强烈看涨' in trend_direction:
                return '趋势风险低，处于强烈上涨趋势中，但需警惕过度乐观'
            else:
                return '趋势风险无法确定，建议等待更明确的趋势信号'

        except Exception:
            return '趋势风险分析异常，无法准确评估'

    def _analyze_liquidity_risk(self) -> str:
        """分析流动性风险"""
        try:
            volumes = self.weekly_data['volume']
            if volumes.empty:
                return '缺少成交量数据，无法评估流动性风险'

            avg_volume = volumes.mean()
            recent_volume = volumes.iloc[-4:].mean() if len(volumes) >= 4 else avg_volume

            if recent_volume < avg_volume * 0.3:
                return '流动性风险很高，成交量极度萎缩，买卖困难'
            elif recent_volume < avg_volume * 0.5:
                return '流动性风险较高，成交量偏低，可能影响交易执行'
            elif recent_volume < avg_volume * 0.8:
                return '流动性风险中等，成交量略显不足，需要注意交易时机'
            else:
                return '流动性风险较低，成交量充足，交易执行相对容易'

        except Exception:
            return '流动性风险分析异常，无法准确评估'

    def _generate_risk_assessment(self, volatility_risk: str, trend_risk: str, liquidity_risk: str) -> Dict[str, str]:
        """生成综合风险评估"""
        try:
            # 统计各类风险等级
            high_risks = []
            medium_risks = []
            low_risks = []

            # 分析波动率风险
            if '很大' in volatility_risk or '极高' in volatility_risk:
                high_risks.append('价格波动风险')
            elif '较高' in volatility_risk or '明显' in volatility_risk:
                medium_risks.append('价格波动风险')
            else:
                low_risks.append('价格波动风险')

            # 分析趋势风险
            if '很高' in trend_risk or '强烈下跌' in trend_risk:
                high_risks.append('趋势方向风险')
            elif '较高' in trend_risk or '下跌' in trend_risk or '中等' in trend_risk:
                medium_risks.append('趋势方向风险')
            else:
                low_risks.append('趋势方向风险')

            # 分析流动性风险
            if '很高' in liquidity_risk or '极度' in liquidity_risk:
                high_risks.append('流动性风险')
            elif '较高' in liquidity_risk or '偏低' in liquidity_risk:
                medium_risks.append('流动性风险')
            else:
                low_risks.append('流动性风险')

            # 生成综合风险等级
            if len(high_risks) >= 2:
                level = '高'
                description = f'该饰品存在{len(high_risks)}项高风险因素：{", ".join(high_risks)}，投资风险很大。'
                factors = f'主要风险包括：{volatility_risk}；{trend_risk}；{liquidity_risk}。'
                mitigation = '建议避免投资或严格控制仓位，设置紧密止损，密切关注市场变化。'
            elif len(high_risks) >= 1 or len(medium_risks) >= 2:
                level = '中'
                description = f'该饰品存在一定风险因素，需要谨慎评估风险承受能力。'
                factors = f'风险分析：{volatility_risk}；{trend_risk}；{liquidity_risk}。'
                mitigation = '建议适度控制仓位，设置合理止损，根据风险承受能力调整投资策略。'
            else:
                level = '低'
                description = f'该饰品整体风险相对较低，但仍需要基本的风险管理。'
                factors = f'风险状况：{volatility_risk}；{trend_risk}；{liquidity_risk}。'
                mitigation = '建议保持正常仓位管理，设置基础止损保护，定期评估投资组合。'

            return {
                'level': level,
                'description': description,
                'factors': factors,
                'mitigation': mitigation
            }

        except Exception:
            return {
                'level': '评估异常',
                'description': '风险评估过程中发生异常',
                'factors': '无法准确分析各项风险因素',
                'mitigation': '建议谨慎操作，咨询专业人士'
            }

    def _generate_key_levels_analysis(self, current_price: float, support_levels: List[float], resistance_levels: List[float]) -> Dict[str, str]:
        """生成关键价位分析"""
        try:
            # 分析支撑位
            if len(support_levels) >= 2:
                support_analysis = f'重要支撑位位于{support_levels[0]:.2f}元和{support_levels[1]:.2f}元。'
                if current_price > support_levels[0]:
                    support_analysis += f'当前价格{current_price:.2f}元位于主要支撑位之上，支撑有效。'
                else:
                    support_analysis += f'当前价格{current_price:.2f}元已跌破主要支撑位，需要关注下一支撑位{support_levels[1]:.2f}元。'
            elif len(support_levels) >= 1:
                support_analysis = f'重要支撑位位于{support_levels[0]:.2f}元。'
                if current_price > support_levels[0]:
                    support_analysis += f'当前价格{current_price:.2f}元位于支撑位之上。'
                else:
                    support_analysis += f'当前价格{current_price:.2f}元已跌破支撑位，需要谨慎。'
            else:
                support_analysis = '暂未识别到明确的支撑位，需要进一步观察价格走势。'

            # 分析阻力位
            if len(resistance_levels) >= 2:
                resistance_analysis = f'重要阻力位位于{resistance_levels[0]:.2f}元和{resistance_levels[1]:.2f}元。'
                if current_price < resistance_levels[0]:
                    resistance_analysis += f'当前价格{current_price:.2f}元位于主要阻力位下方，上涨空间受限。'
                else:
                    resistance_analysis += f'当前价格{current_price:.2f}元已突破主要阻力位，下一目标位{resistance_levels[1]:.2f}元。'
            elif len(resistance_levels) >= 1:
                resistance_analysis = f'重要阻力位位于{resistance_levels[0]:.2f}元。'
                if current_price < resistance_levels[0]:
                    resistance_analysis += f'当前价格{current_price:.2f}元位于阻力位下方。'
                else:
                    resistance_analysis += f'当前价格{current_price:.2f}元已突破阻力位。'
            else:
                resistance_analysis = '暂未识别到明确的阻力位，上涨空间相对开阔。'

            # 综合描述
            description = f'基于周K线数据分析，{support_analysis} {resistance_analysis}'

            return {
                'description': description,
                'support_analysis': support_analysis,
                'resistance_analysis': resistance_analysis
            }

        except Exception:
            return {
                'description': '关键价位分析异常，无法准确识别支撑阻力位',
                'support_analysis': '支撑位分析异常',
                'resistance_analysis': '阻力位分析异常'
            }

    def _generate_comprehensive_advice(self, trend_analysis: Dict, value_analysis: Dict,
                                     risk_analysis: Dict, levels_analysis: Dict) -> Dict[str, str]:
        """生成综合投资建议"""
        try:
            trend_direction = trend_analysis.get('trend_direction', '中性')
            value_conclusion = value_analysis.get('value_conclusion', '中等')
            risk_level = risk_analysis.get('risk_level', '中')

            # 基于趋势、价值和风险生成投资建议
            if '强烈看涨' in trend_direction and '较高' in value_conclusion and risk_level == '低':
                recommendation = '强烈推荐买入。长期趋势强劲向上，投资价值突出，风险可控，是优质的投资标的。'
                holding_period = '建议长期持有6-12个月，充分享受趋势红利。'
                position_sizing = '可以考虑较大仓位配置，建议占投资组合的15-25%。'
                entry_strategy = '可以分批建仓，在支撑位附近加仓，突破阻力位后追加投资。'

            elif '看涨' in trend_direction and ('较高' in value_conclusion or '中等偏上' in value_conclusion) and risk_level != '高':
                recommendation = '推荐买入。趋势向好，投资价值尚可，适合中长期投资。'
                holding_period = '建议持有3-6个月，根据趋势变化调整策略。'
                position_sizing = '建议中等仓位配置，占投资组合的10-15%。'
                entry_strategy = '建议在回调至支撑位时分批买入，避免追高。'

            elif '中性' in trend_direction and '中等' in value_conclusion and risk_level == '中':
                recommendation = '可以适量配置。趋势不明确但风险可控，适合作为组合配置的一部分。'
                holding_period = '建议短中期持有1-3个月，密切关注趋势变化。'
                position_sizing = '建议小仓位配置，占投资组合的5-10%。'
                entry_strategy = '建议等待明确的突破信号后再行动，或在强支撑位小量试探。'

            elif '看跌' in trend_direction or risk_level == '高':
                recommendation = '不建议买入。趋势偏弱或风险较高，建议观望等待更好时机。'
                holding_period = '如已持有，建议短期持有并寻找合适的退出时机。'
                position_sizing = '建议避免新增仓位，如需投资请严格控制在5%以下。'
                entry_strategy = '建议暂时观望，等待趋势转强或风险降低后再考虑介入。'

            else:
                recommendation = '建议谨慎操作。当前市场信号混合，建议等待更明确的投资信号。'
                holding_period = '建议根据市场变化灵活调整持有期，保持谨慎态度。'
                position_sizing = '建议控制仓位在10%以下，分散投资风险。'
                entry_strategy = '建议采用小额试探策略，根据市场反应调整后续操作。'

            # 风险管理建议
            if risk_level == '高':
                risk_management = '严格设置止损位，建议在买入价下方5-8%设置止损，密切监控市场变化。'
            elif risk_level == '中':
                risk_management = '设置合理止损位，建议在买入价下方8-12%设置止损，定期评估投资组合。'
            else:
                risk_management = '设置基础止损保护，建议在买入价下方10-15%设置止损，保持正常监控频率。'

            return {
                'recommendation': recommendation,
                'holding_period': holding_period,
                'position_sizing': position_sizing,
                'entry_strategy': entry_strategy,
                'risk_management': risk_management
            }

        except Exception:
            return {
                'recommendation': '建议生成异常，请谨慎操作并咨询专业人士',
                'holding_period': '无法确定持有期，建议根据市场情况灵活调整',
                'position_sizing': '建议严格控制仓位，分散投资风险',
                'entry_strategy': '建议等待更明确的市场信号',
                'risk_management': '严格设置止损，控制投资风险'
            }

    def execute_comprehensive_strategic_analysis(self) -> Dict[str, Any]:
        """
        执行完整的战略分析 - 主要接口方法

        Returns:
            Dict包含符合需求文档要求的完整战略分析结果
        """
        try:
            self.logger.info("开始执行完整的战略分析...")

            # 执行各项分析
            trend_analysis = self.analyze_strategic_trend()
            value_analysis = self.analyze_investment_value()
            risk_analysis = self.assess_strategic_risk()
            levels_analysis = self.calculate_key_levels()
            advice_analysis = self.generate_investment_advice()

            # 整合分析结果，符合需求文档格式
            comprehensive_result = {
                # 长期趋势方向：明确的趋势判断（强烈看涨/看涨/中性/看跌/强烈看跌）
                'long_term_trend_direction': trend_analysis.get('trend_direction', '数据不足'),
                'trend_description': trend_analysis.get('trend_description', ''),
                'trend_reasoning': trend_analysis.get('trend_reasoning', ''),

                # 投资价值评估：具体的价值分析结论和理由
                'investment_value_assessment': value_analysis.get('value_conclusion', '数据不足'),
                'value_analysis_detail': value_analysis.get('value_analysis', ''),
                'value_reasoning': value_analysis.get('value_reasoning', ''),

                # 风险等级：明确的风险级别（低/中/高）和风险因素说明
                'risk_level': risk_analysis.get('risk_level', '无法评估'),
                'risk_description': risk_analysis.get('risk_description', ''),
                'risk_factors_detail': risk_analysis.get('risk_factors', ''),
                'risk_mitigation_advice': risk_analysis.get('risk_mitigation', ''),

                # 关键价位：重要支撑位和阻力位的具体数值
                'key_levels_description': levels_analysis.get('key_levels_description', ''),
                'support_levels_analysis': levels_analysis.get('support_analysis', ''),
                'resistance_levels_analysis': levels_analysis.get('resistance_analysis', ''),
                'major_support_1': levels_analysis.get('major_support_1'),
                'major_support_2': levels_analysis.get('major_support_2'),
                'major_resistance_1': levels_analysis.get('major_resistance_1'),
                'major_resistance_2': levels_analysis.get('major_resistance_2'),

                # 投资建议：基于战略分析的具体建议和持有期建议
                'investment_recommendation': advice_analysis.get('investment_recommendation', ''),
                'holding_period_advice': advice_analysis.get('holding_period_advice', ''),
                'position_sizing_advice': advice_analysis.get('position_sizing_advice', ''),
                'entry_strategy_advice': advice_analysis.get('entry_strategy', ''),
                'risk_management_advice': advice_analysis.get('risk_management', ''),

                # 元数据
                'analysis_timestamp': datetime.now(),
                'data_quality': 'GOOD' if not self.weekly_data.empty else 'INSUFFICIENT',
                'analysis_success': True
            }

            self.logger.info("完整战略分析执行成功")
            return comprehensive_result

        except Exception as e:
            self.logger.error(f"完整战略分析执行失败: {e}")
            return {
                'long_term_trend_direction': '分析异常',
                'trend_description': f'战略分析过程中发生异常: {str(e)}',
                'trend_reasoning': '请检查数据质量或联系技术支持',
                'investment_value_assessment': '无法评估',
                'value_analysis_detail': '投资价值分析异常',
                'value_reasoning': '请检查数据质量',
                'risk_level': '高',
                'risk_description': '由于分析异常，建议谨慎操作',
                'risk_factors_detail': '分析系统异常，存在未知风险',
                'risk_mitigation_advice': '建议暂停投资，等待系统恢复',
                'key_levels_description': '无法计算关键价位',
                'support_levels_analysis': '支撑位分析异常',
                'resistance_levels_analysis': '阻力位分析异常',
                'investment_recommendation': '建议暂停投资决策',
                'holding_period_advice': '等待系统恢复后重新评估',
                'position_sizing_advice': '避免新增仓位',
                'entry_strategy_advice': '等待系统正常后再制定策略',
                'risk_management_advice': '严格控制风险，避免损失',
                'analysis_timestamp': datetime.now(),
                'data_quality': 'ERROR',
                'analysis_success': False,
                'error': str(e)
            }
