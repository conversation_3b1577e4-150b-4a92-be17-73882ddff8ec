#!/usr/bin/env python3
"""
验证依赖包是否正确安装
"""

import sys
import importlib
import pkg_resources

def check_package(package_name, required_version=None):
    """检查包是否安装并验证版本"""
    try:
        # 尝试导入包
        importlib.import_module(package_name)
        
        # 获取已安装的版本
        try:
            installed_version = pkg_resources.get_distribution(package_name).version
            print(f"✅ {package_name}: {installed_version}")
            
            if required_version and installed_version != required_version:
                print(f"⚠️  警告: {package_name} 版本不匹配 (需要: {required_version}, 已安装: {installed_version})")
                return False
                
        except pkg_resources.DistributionNotFound:
            print(f"⚠️  {package_name}: 已导入但无法获取版本信息")
            
        return True
        
    except ImportError as e:
        print(f"❌ {package_name}: 导入失败 - {e}")
        return False

def main():
    """主函数"""
    print("🔍 验证关键依赖包...")
    print("=" * 50)
    
    # 关键依赖包列表
    critical_packages = [
        ("streamlit", "1.48.1"),
        ("pandas", "2.3.1"),
        ("numpy", "2.3.2"),
        ("plotly", "5.17.0"),
        ("st_aggrid", None),  # streamlit-aggrid 导入时使用 st_aggrid
    ]
    
    all_ok = True
    
    for package, version in critical_packages:
        if not check_package(package, version):
            all_ok = False
    
    print("=" * 50)
    
    if all_ok:
        print("✅ 所有关键依赖包验证通过！")
        
        # 测试 LinkColumn 功能
        print("\n🧪 测试 LinkColumn 功能...")
        try:
            import streamlit as st
            import pandas as pd
            
            # 创建测试数据
            df = pd.DataFrame({'链接': ['https://example.com']})
            
            # 测试 LinkColumn 配置
            config = {
                "链接": st.column_config.LinkColumn(
                    "链接",
                    help="测试链接",
                    display_text=":material/open_in_new:",
                    width="small"
                )
            }
            
            print("✅ LinkColumn 功能测试通过！")
            
        except Exception as e:
            print(f"❌ LinkColumn 功能测试失败: {e}")
            all_ok = False
    else:
        print("❌ 部分依赖包验证失败，请检查安装！")
    
    return 0 if all_ok else 1

if __name__ == "__main__":
    sys.exit(main())
