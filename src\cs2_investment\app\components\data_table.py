"""
详细数据表格组件

提供饰品市场数据的完整表格展示和导出功能。
"""

import streamlit as st
import pandas as pd
from typing import Dict, Any
import io
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.app.utils.data_formatter import format_price, format_percentage, format_number


def render_detailed_data_tables(snapshot: Dict[str, Any], item_name: str = "饰品") -> None:
    """渲染详细数据表格部分"""
    if not snapshot:
        st.info("📋 暂无详细数据")
        return

    st.subheader("📋 详细数据表格")

    # 添加数据说明
    with st.expander("📖 数据说明", expanded=False):
        st.markdown("""
        **数据来源与说明：**
        - **时间基准**：所有数据基于快照时间统一计算
        - **价格数据**：显示不同时间段的价格变化情况
        - **交易数据**：基于实际成交记录统计，平均成交价 = 成交额 ÷ 成交量
        - **供需数据**：在售数量反映市场供应情况，变化率显示供需趋势
        - **热度数据**：基于市场关注度和搜索热度计算

        **⚠️ 重要提示：**
        - 数据仅供参考，不构成投资建议
        - 市场价格存在波动风险，请谨慎决策
        - 建议结合多个数据源进行综合分析
        """)

    # 使用tabs来组织不同类别的数据
    tab1, tab2, tab3, tab4 = st.tabs(["💰 价格数据", "📊 交易数据", "⚖️ 供需数据", "🔥 热度数据"])

    with tab1:
        render_price_data_table(snapshot, item_name)

    with tab2:
        render_trading_data_table(snapshot, item_name)

    with tab3:
        render_supply_data_table(snapshot, item_name)

    with tab4:
        render_popularity_data_table(snapshot, item_name)


def render_price_data_table(snapshot: Dict[str, Any], item_name: str) -> None:
    """渲染价格数据表格"""
    st.write("**💰 价格历史数据**")
    
    # 准备价格数据
    price_data = []
    
    # 当前价格
    price_data.append({
        '时间段': '当前',
        '价格': snapshot.get('current_price', 0),
        '变化百分比': 0,
        '变化金额': 0,
        '历史价格': snapshot.get('current_price', 0)
    })
    
    # 历史价格数据
    periods = [
        ('1天前', 'diff_1d', 'diff_1d_price', 'before_1d_price'),
        ('3天前', 'diff_3d', 'diff_3d_price', 'before_3d_price'),
        ('7天前', 'diff_7d', 'diff_7d_price', 'before_7d_price'),
        ('15天前', 'diff_15d', 'diff_15d_price', 'before_15d_price'),
        ('1月前', 'diff_1m', 'diff_1m_price', 'before_1m_price'),
        ('3月前', 'diff_3m', 'diff_3m_price', 'before_3m_price'),
        ('6月前', 'diff_6m', 'diff_6m_price', 'before_6m_price'),
        ('1年前', 'diff_1y', 'diff_1y_price', 'before_1y_price')
    ]
    
    for period_name, diff_key, diff_price_key, before_price_key in periods:
        diff_pct = snapshot.get(diff_key, 0)
        diff_price = snapshot.get(diff_price_key, 0)
        before_price = snapshot.get(before_price_key, 0)
        
        if before_price > 0 or diff_pct != 0 or diff_price != 0:
            price_data.append({
                '时间段': period_name,
                '价格': before_price,
                '变化百分比': diff_pct,
                '变化金额': diff_price,
                '历史价格': before_price
            })
    
    if price_data:
        # 格式化数据
        for item in price_data:
            item['价格'] = format_price(item['价格'])
            item['变化百分比'] = format_percentage(item['变化百分比']) if item['变化百分比'] != 0 else '-'
            item['变化金额'] = format_price(item['变化金额']) if item['变化金额'] != 0 else '-'
            item['历史价格'] = format_price(item['历史价格'])
        
        df = pd.DataFrame(price_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
        
        # 导出按钮
        if st.button("📥 导出价格数据", key="export_price"):
            csv_data = create_csv_download(df, f"{item_name}_价格数据")
            st.download_button(
                label="下载 CSV 文件",
                data=csv_data,
                file_name=f"{item_name}_价格数据.csv",
                mime="text/csv"
            )
    else:
        st.info("暂无价格历史数据")


def render_trading_data_table(snapshot: Dict[str, Any], item_name: str) -> None:
    """渲染交易数据表格"""
    st.write("**📊 交易历史数据**")
    
    # 准备交易数据
    trading_data = []
    
    periods = [
        ('24小时', 'trans_count_24h', 'trans_amount_24h'),
        ('48小时', 'trans_count_48h', 'trans_amount_48h'),
        ('1天', 'trans_count_1d', 'trans_amount_1d'),
        ('3天', 'trans_count_3d', 'trans_amount_3d'),
        ('7天', 'trans_count_7d', 'trans_amount_7d'),
        ('15天', 'trans_count_15d', 'trans_amount_15d'),
        ('1月', 'trans_count_1m', 'trans_amount_1m'),
        ('3月', 'trans_count_3m', 'trans_amount_3m')
    ]
    
    for period_name, count_key, amount_key in periods:
        count = snapshot.get(count_key, 0)
        amount = snapshot.get(amount_key, 0)
        
        if count > 0 or amount > 0:
            avg_price = amount / count if count > 0 else 0
            trading_data.append({
                '时间段': period_name,
                '成交量': count,
                '成交额': amount,
                '平均成交价': avg_price
            })
    
    if trading_data:
        # 格式化数据
        for item in trading_data:
            item['成交量'] = format_number(item['成交量'], '笔')
            item['成交额'] = format_price(item['成交额'])
            item['平均成交价'] = format_price(item['平均成交价'])
        
        df = pd.DataFrame(trading_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
        
        # 导出按钮
        if st.button("📥 导出交易数据", key="export_trading"):
            csv_data = create_csv_download(df, f"{item_name}_交易数据")
            st.download_button(
                label="下载 CSV 文件",
                data=csv_data,
                file_name=f"{item_name}_交易数据.csv",
                mime="text/csv"
            )
    else:
        st.info("暂无交易历史数据")


def render_supply_data_table(snapshot: Dict[str, Any], item_name: str) -> None:
    """渲染供需数据表格"""
    st.write("**⚖️ 供需历史数据**")
    
    # 准备供需数据
    supply_data = []
    
    periods = [
        ('当前', 'sell_nums', None, None),
        ('1天前', 'sell_nums_1d', 'sell_nums_1d_rate', 'sell_nums_1d_diff'),
        ('3天前', 'sell_nums_3d', 'sell_nums_3d_rate', 'sell_nums_3d_diff'),
        ('7天前', 'sell_nums_7d', 'sell_nums_7d_rate', 'sell_nums_7d_diff'),
        ('15天前', 'sell_nums_15d', 'sell_nums_15d_rate', 'sell_nums_15d_diff'),
        ('1月前', 'sell_nums_1m', 'sell_nums_1m_rate', 'sell_nums_1m_diff'),
        ('3月前', 'sell_nums_3m', 'sell_nums_3m_rate', 'sell_nums_3m_diff')
    ]
    
    for period_name, nums_key, rate_key, diff_key in periods:
        nums = snapshot.get(nums_key, 0)
        rate = snapshot.get(rate_key, 0) if rate_key else 0
        diff = snapshot.get(diff_key, 0) if diff_key else 0
        
        if nums > 0 or rate != 0 or diff != 0:
            supply_data.append({
                '时间段': period_name,
                '在售数量': nums,
                '变化率': rate,
                '变化数量': diff
            })
    
    if supply_data:
        # 格式化数据
        for item in supply_data:
            item['在售数量'] = format_number(item['在售数量'], '个')
            item['变化率'] = format_percentage(item['变化率']) if item['变化率'] != 0 else '-'
            item['变化数量'] = f"{item['变化数量']:+d}个" if item['变化数量'] != 0 else '-'
        
        df = pd.DataFrame(supply_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
        
        # 显示存世量信息
        survive_num = snapshot.get('survive_num', 0)
        if survive_num > 0:
            st.info(f"💎 **存世量**: {format_number(survive_num)}个")
        
        # 导出按钮
        if st.button("📥 导出供需数据", key="export_supply"):
            csv_data = create_csv_download(df, f"{item_name}_供需数据")
            st.download_button(
                label="下载 CSV 文件",
                data=csv_data,
                file_name=f"{item_name}_供需数据.csv",
                mime="text/csv"
            )
    else:
        st.info("暂无供需历史数据")


def render_popularity_data_table(snapshot: Dict[str, Any], item_name: str) -> None:
    """渲染热度数据表格"""
    st.write("**🔥 热度数据**")
    
    # 准备热度数据
    popularity_data = [
        {
            '指标': '热度排名',
            '数值': f"#{snapshot.get('hot_rank')}" if snapshot.get('hot_rank') else "未上榜",
            '说明': '在所有饰品中的热度排名'
        },
        {
            '指标': '热度计数',
            '数值': format_number(snapshot.get('hot_count', 0)),
            '说明': '累计关注度计数'
        },
        {
            '指标': '热度保持天数',
            '数值': f"{snapshot.get('hot_keep_days', 0)}天",
            '说明': '连续保持热度的天数'
        },
        {
            '指标': '排名变化',
            '数值': f"{snapshot.get('hot_rank_change', 0):+d}" if snapshot.get('hot_rank_change') else "无变化",
            '说明': '相比上次的排名变化'
        }
    ]
    
    df = pd.DataFrame(popularity_data)
    st.dataframe(df, use_container_width=True, hide_index=True)
    
    # 导出按钮
    if st.button("📥 导出热度数据", key="export_popularity"):
        csv_data = create_csv_download(df, f"{item_name}_热度数据")
        st.download_button(
            label="下载 CSV 文件",
            data=csv_data,
            file_name=f"{item_name}_热度数据.csv",
            mime="text/csv"
        )


def create_csv_download(df: pd.DataFrame, filename: str) -> str:
    """创建CSV下载数据"""
    output = io.StringIO()
    df.to_csv(output, index=False, encoding='utf-8-sig')  # 使用utf-8-sig支持中文
    return output.getvalue()
