"""
统一数据管理器

负责管理多时间框架的数据加载、格式转换和数据协调。
基于现有系统的数据格式，确保与现有系统的完全兼容性。
"""

import pandas as pd
import numpy as np
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from loguru import logger

from ...syncps.technical_indicator_calculator import TechnicalIndicatorCalculator

# 导入统一数据处理器
from ..utils.data_processor import StandardizedDataProcessor, DataValidator


class UnifiedDataManager:
    """统一数据管理器"""
    
    def __init__(self, item_id: str, data_base_path: str = "data/scraped_data"):
        """
        初始化统一数据管理器
        
        Args:
            item_id: 饰品ID
            data_base_path: 数据基础路径
        """
        self.item_id = item_id
        self.data_base_path = Path(data_base_path)
        self.item_data_path = self.data_base_path / item_id
        
        self.logger = logger.bind(manager=self.__class__.__name__, item_id=item_id)
        
        # 数据缓存
        self.data_cache = {}
        
        # 验证数据路径
        self._validate_data_path()
    
    def _validate_data_path(self):
        """验证数据路径"""
        if not self.item_data_path.exists():
            self.logger.warning(f"数据路径不存在: {self.item_data_path}")
        else:
            self.logger.info(f"数据路径验证成功: {self.item_data_path}")
    
    def load_multi_timeframe_data(self) -> Dict[str, Any]:
        """
        加载多时间框架数据
        
        Returns:
            Dict: 包含所有时间框架数据的字典
        """
        try:
            self.logger.info("开始加载多时间框架数据...")
            
            data_result = {
                'item_id': self.item_id,
                'load_timestamp': datetime.now(),
                'data_sources': {},
                'success': True,
                'errors': []
            }
            
            # 加载供需数据 (走势数据)
            supply_demand_data = self._load_supply_demand_data()
            data_result['data_sources']['supply_demand'] = supply_demand_data
            
            # 加载K线数据
            kline_data = self._load_kline_data()
            data_result['data_sources']['kline'] = kline_data
            
            # 数据质量检查
            quality_report = self._check_data_quality(data_result['data_sources'])
            data_result['quality_report'] = quality_report
            
            self.logger.info("多时间框架数据加载完成")
            return data_result
            
        except Exception as e:
            self.logger.error(f"多时间框架数据加载失败: {e}")
            return {
                'item_id': self.item_id,
                'load_timestamp': datetime.now(),
                'success': False,
                'error': str(e)
            }
    
    def _load_supply_demand_data(self) -> Dict[str, Any]:
        """加载供需数据"""
        supply_demand_result = {
            'success': True,
            'data': {},
            'errors': []
        }
        
        # 加载走势数据文件
        supply_demand_files = {
            '3m': '走势_3m.json',
            '6m': '走势_6m.json'
        }
        
        for timeframe, filename in supply_demand_files.items():
            file_path = self.item_data_path / filename
            
            try:
                if file_path.exists():
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    supply_demand_result['data'][timeframe] = data
                    self.logger.info(f"供需数据加载成功: {timeframe} ({len(data) if isinstance(data, list) else 1} 条记录)")
                else:
                    self.logger.warning(f"供需数据文件不存在: {filename}")
                    supply_demand_result['errors'].append(f"文件不存在: {filename}")
                    
            except Exception as e:
                self.logger.error(f"供需数据加载失败 ({timeframe}): {e}")
                supply_demand_result['errors'].append(f"{timeframe}: {str(e)}")
        
        if supply_demand_result['errors']:
            supply_demand_result['success'] = len(supply_demand_result['data']) > 0
        
        return supply_demand_result
    
    def _load_kline_data(self) -> Dict[str, Any]:
        """加载K线数据"""
        kline_result = {
            'success': True,
            'data': {},
            'errors': []
        }
        
        # K线数据文件映射
        kline_files = {
            'strategic': '周k.json',    # 战略分析 - 周K
            'execution': '时k.json',    # 执行分析 - 时K
        }

        # 处理常规K线数据
        for timeframe, filename in kline_files.items():
            file_path = self.item_data_path / filename

            try:
                if file_path.exists():
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    # 转换为DataFrame格式
                    df = self._convert_kline_to_dataframe(data, timeframe)
                    kline_result['data'][timeframe] = df

                    self.logger.info(f"K线数据加载成功: {timeframe} ({len(df)} 条记录)")
                else:
                    self.logger.warning(f"K线数据文件不存在: {filename}")
                    kline_result['errors'].append(f"文件不存在: {filename}")

            except Exception as e:
                self.logger.error(f"K线数据加载失败 ({timeframe}): {e}")
                kline_result['errors'].append(f"{timeframe}: {str(e)}")

        # 特殊处理：合并日K数据用于战术分析
        try:
            merged_daily_df = self._merge_daily_kline_data()
            if merged_daily_df is not None and not merged_daily_df.empty:
                kline_result['data']['tactical'] = merged_daily_df
                self.logger.info(f"日K数据合并成功: tactical ({len(merged_daily_df)} 条记录)")
            else:
                self.logger.warning("日K数据合并失败或数据为空")
                kline_result['errors'].append("日K数据合并失败")
        except Exception as e:
            self.logger.error(f"日K数据合并失败: {e}")
            kline_result['errors'].append(f"日K数据合并: {str(e)}")
        
        if kline_result['errors']:
            kline_result['success'] = len(kline_result['data']) > 0
        
        return kline_result
    
    def _convert_kline_to_dataframe(self, data: Any, timeframe: str) -> pd.DataFrame:
        """
        将K线数据转换为DataFrame格式
        使用统一数据处理器确保数据处理的一致性
        """
        try:
            if isinstance(data, list):
                # 使用统一数据处理器
                df = StandardizedDataProcessor.process_kline_data(data, f"{timeframe}_kline")

                # 使用数据验证器验证结果
                validation_result = DataValidator.validate_kline_dataframe(df)

                if not validation_result['is_valid']:
                    self.logger.error(f"K线数据验证失败 ({timeframe}): {validation_result['errors']}")

                if validation_result['warnings']:
                    for warning in validation_result['warnings']:
                        self.logger.warning(f"K线数据警告 ({timeframe}): {warning}")

                # 记录统计信息
                stats = validation_result.get('statistics', {})
                if stats:
                    self.logger.info(f"K线数据统计 ({timeframe}): {stats.get('total_records', 0)}条记录")

                return df
            else:
                self.logger.warning(f"K线数据格式不正确 ({timeframe}): 期望list，实际{type(data)}")
                return pd.DataFrame(columns=['datetime', 'open', 'close', 'high', 'low', 'volume', 'amount'])

        except Exception as e:
            self.logger.error(f"K线数据转换失败 ({timeframe}): {e}")
            return pd.DataFrame(columns=['datetime', 'open', 'close', 'high', 'low', 'volume', 'amount'])
    
    def _parse_datetime(self, time_value: Any) -> datetime:
        """解析时间值"""
        try:
            if isinstance(time_value, str):
                # 首先检查是否为数字字符串（时间戳）
                if time_value.isdigit():
                    try:
                        timestamp_value = int(time_value)
                        # 检查是否为毫秒时间戳
                        if timestamp_value > 1e10:
                            timestamp_seconds = timestamp_value / 1000
                        else:
                            timestamp_seconds = timestamp_value

                        # 验证时间戳是否在合理范围内（1970年到2050年）
                        min_timestamp = 0  # 1970-01-01
                        max_timestamp = 2524608000  # 2050-01-01

                        if min_timestamp <= timestamp_seconds <= max_timestamp:
                            return datetime.fromtimestamp(timestamp_seconds)
                        else:
                            self.logger.warning(f"字符串时间戳超出合理范围: {time_value}, 使用当前时间")
                            return datetime.now()
                    except (ValueError, OSError) as e:
                        self.logger.warning(f"字符串时间戳转换失败: {time_value}, 错误: {e}")
                        # 继续尝试其他格式

                # 尝试多种时间格式
                formats = [
                    '%Y-%m-%d %H:%M:%S',
                    '%Y-%m-%d',
                    '%Y/%m/%d %H:%M:%S',
                    '%Y/%m/%d',
                    '%m/%d/%Y',
                    '%d/%m/%Y'
                ]

                for fmt in formats:
                    try:
                        return datetime.strptime(time_value, fmt)
                    except ValueError:
                        continue

                # 如果都不匹配，尝试pandas的解析（但不包括纯数字字符串）
                try:
                    return pd.to_datetime(time_value)
                except:
                    self.logger.warning(f"无法解析时间字符串: {time_value}")
                    return datetime.now()

            elif isinstance(time_value, (int, float)):
                # 验证时间戳的合理性
                current_timestamp = datetime.now().timestamp()

                # 检查是否为毫秒时间戳
                if time_value > 1e10:
                    timestamp_seconds = time_value / 1000
                else:
                    timestamp_seconds = time_value

                # 验证时间戳是否在合理范围内（1970年到2050年）
                min_timestamp = 0  # 1970-01-01
                max_timestamp = 2524608000  # 2050-01-01

                if min_timestamp <= timestamp_seconds <= max_timestamp:
                    return datetime.fromtimestamp(timestamp_seconds)
                else:
                    self.logger.warning(f"时间戳超出合理范围: {time_value}, 使用当前时间")
                    return datetime.now()

            elif isinstance(time_value, datetime):
                return time_value

            else:
                # 默认返回当前时间
                self.logger.warning(f"无法识别的时间格式: {type(time_value)}, 使用当前时间")
                return datetime.now()

        except Exception as e:
            self.logger.warning(f"时间解析失败: {time_value} ({type(time_value)}), 错误: {e}, 使用当前时间")
            return datetime.now()
    
    def _check_data_quality(self, data_sources: Dict) -> Dict[str, Any]:
        """检查数据质量"""
        quality_report = {
            'overall_quality': 'GOOD',
            'issues': [],
            'recommendations': [],
            'data_coverage': {}
        }
        
        try:
            # 检查供需数据质量
            if 'supply_demand' in data_sources:
                sd_data = data_sources['supply_demand']['data']
                quality_report['data_coverage']['supply_demand'] = len(sd_data)
                
                if not sd_data:
                    quality_report['issues'].append("缺少供需数据")
                    quality_report['overall_quality'] = 'POOR'
            
            # 检查K线数据质量
            if 'kline' in data_sources:
                kline_data = data_sources['kline']['data']
                quality_report['data_coverage']['kline'] = {}
                
                for timeframe, df in kline_data.items():
                    quality_report['data_coverage']['kline'][timeframe] = len(df)
                    
                    if len(df) < 20:
                        quality_report['issues'].append(f"{timeframe}数据不足20条")
                        if quality_report['overall_quality'] == 'GOOD':
                            quality_report['overall_quality'] = 'FAIR'
            
            # 生成建议
            if quality_report['issues']:
                quality_report['recommendations'].append("建议补充缺失的数据源")
                quality_report['recommendations'].append("确保数据的时间连续性")
            
        except Exception as e:
            self.logger.error(f"数据质量检查失败: {e}")
            quality_report['overall_quality'] = 'ERROR'
            quality_report['issues'].append(f"质量检查异常: {str(e)}")
        
        return quality_report
    
    def get_data_for_timeframe(self, timeframe: str) -> Optional[pd.DataFrame]:
        """获取指定时间框架的数据"""
        try:
            data = self.load_multi_timeframe_data()
            
            if not data['success']:
                return None
            
            kline_data = data['data_sources'].get('kline', {}).get('data', {})
            return kline_data.get(timeframe)
            
        except Exception as e:
            self.logger.error(f"获取时间框架数据失败 ({timeframe}): {e}")
            return None
    
    def get_supply_demand_data(self) -> Dict[str, Any]:
        """获取供需数据"""
        try:
            data = self.load_multi_timeframe_data()
            
            if not data['success']:
                return {}
            
            return data['data_sources'].get('supply_demand', {}).get('data', {})
            
        except Exception as e:
            self.logger.error(f"获取供需数据失败: {e}")
            return {}

    def _validate_ohlc_data(self, row: Dict) -> bool:
        """
        验证OHLC数据的逻辑关系

        Args:
            row: 包含OHLC数据的字典

        Returns:
            bool: 数据是否有效
        """
        try:
            open_price = row['open']
            close_price = row['close']
            high_price = row['high']
            low_price = row['low']

            # 基本逻辑验证
            # 1. 最高价应该 >= 开盘价、收盘价、最低价
            if high_price < max(open_price, close_price, low_price):
                return False

            # 2. 最低价应该 <= 开盘价、收盘价、最高价
            if low_price > min(open_price, close_price, high_price):
                return False

            # 3. 所有价格都应该为正数
            if any(price <= 0 for price in [open_price, close_price, high_price, low_price]):
                return False

            # 4. 检查价格是否在合理范围内（避免异常值）
            prices = [open_price, close_price, high_price, low_price]
            max_price = max(prices)
            min_price = min(prices)

            # 如果最高价和最低价差异过大（超过10倍），可能是异常数据
            if max_price > min_price * 10:
                return False

            return True

        except (KeyError, TypeError, ValueError):
            return False

    def _merge_daily_kline_data(self) -> Optional[pd.DataFrame]:
        """
        合并日K数据（日k1.json + 日k2.json）

        按照源数据结构说明要求，将两个文件按时间先后顺序拼接后作为整体使用

        Returns:
            pd.DataFrame: 合并后的日K数据，如果失败返回None
        """
        try:
            daily1_path = self.item_data_path / "日k1.json"
            daily2_path = self.item_data_path / "日k2.json"

            # 检查文件是否存在
            if not daily1_path.exists():
                self.logger.warning(f"日K1数据文件不存在: {daily1_path}")
                return None
            if not daily2_path.exists():
                self.logger.warning(f"日K2数据文件不存在: {daily2_path}")
                return None

            # 加载日K1数据
            with open(daily1_path, 'r', encoding='utf-8') as f:
                daily1_raw = json.load(f)
                self.logger.info(f"日K1数据加载成功: {len(daily1_raw)} 条")

            # 加载日K2数据
            with open(daily2_path, 'r', encoding='utf-8') as f:
                daily2_raw = json.load(f)
                self.logger.info(f"日K2数据加载成功: {len(daily2_raw)} 条")

            # 使用统一数据处理器合并日K数据
            merged_df = StandardizedDataProcessor.merge_daily_kline(daily1_raw, daily2_raw)

            if merged_df.empty:
                self.logger.warning("合并后的日K数据为空")
                return None

            # 验证数据完整性
            total_expected = len(daily1_raw) + len(daily2_raw)
            actual_count = len(merged_df)

            self.logger.info(f"日K数据合并完成: 预期{total_expected}条，实际{actual_count}条")

            # 验证时间序列连续性
            if len(merged_df) > 1:
                time_diffs = merged_df['datetime'].diff().dropna()
                avg_interval = time_diffs.mean()
                self.logger.info(f"平均时间间隔: {avg_interval}")

            return merged_df

        except Exception as e:
            self.logger.error(f"日K数据合并失败: {e}")
            return None
