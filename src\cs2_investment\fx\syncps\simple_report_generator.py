#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品分析系统 - 通俗易懂的综合分析报告生成器
面向普通用户，提供简单明了、有根有据的投资分析报告
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class UserFriendlyReportGenerator:
    """通俗易懂的分析报告生成器"""
    
    def __init__(self, item_name: str, analysis_results: Dict):
        """
        初始化报告生成器
        
        Args:
            item_name: 饰品名称
            analysis_results: 完整的分析结果
        """
        self.item_name = item_name
        self.analysis_results = analysis_results
        self.report = {}
    
    def generate_comprehensive_report(self) -> Dict:
        """生成综合分析报告"""
        
        print("📋 生成通俗易懂的综合分析报告")
        
        # 1. 基本信息摘要
        basic_info = self._generate_basic_info()
        
        # 2. 投资建议摘要
        investment_advice = self._generate_investment_advice()
        
        # 3. 风险评估摘要
        risk_summary = self._generate_risk_summary()
        
        # 4. 市场情况分析
        market_analysis = self._generate_market_analysis()


        
        # 5. 常见问题解答
        faq_section = self._generate_faq_section()

        # 6. 免责声明
        disclaimer = self._generate_disclaimer()
        
        # 异常分析详情
        anomaly_details = self._generate_anomaly_details()

        self.report = {
            'report_title': f'{self.item_name} - 投资分析报告',
            'generation_time': datetime.now().strftime('%Y年%m月%d日 %H:%M'),
            'basic_info': basic_info,
            'investment_advice': investment_advice,
            'risk_summary': risk_summary,
            'market_analysis': market_analysis,
            'anomaly_details': anomaly_details,
            'faq_section': faq_section,
            'disclaimer': disclaimer
        }
        
        return self.report

    def _get_daily_volume(self) -> float:
        """获取真实的日均成交量数据"""

        # 优先从市场特征中获取
        if 'market_characteristics' in self.analysis_results:
            market_chars = self.analysis_results['market_characteristics']
            if 'daily_volume' in market_chars and market_chars['daily_volume'] > 0:
                return market_chars['daily_volume']

        # 从基本面快照中获取
        if 'fundamental_snapshot' in self.analysis_results:
            fund_snapshot = self.analysis_results['fundamental_snapshot']
            if 'daily_volume' in fund_snapshot and fund_snapshot['daily_volume'] > 0:
                return fund_snapshot['daily_volume']

        # 从数据摘要中获取
        if 'data_summary' in self.analysis_results:
            data_summary = self.analysis_results['data_summary']
            if 'average_daily_volume' in data_summary and data_summary['average_daily_volume'] > 0:
                return data_summary['average_daily_volume']

        # 如果都没有，返回一个合理的默认值（但记录警告）
        print("⚠️ 警告：无法获取真实成交量数据，使用估算值")
        return 100.0  # 保守估算

    def _generate_basic_info(self) -> Dict:
        """生成基本信息摘要"""
        
        # 获取关键数据
        current_price = self._get_current_price()
        market_chars = self.analysis_results.get('market_characteristics', {})
        market_type = market_chars.get('market_type', '未知市场')

        # 获取真实的成交量数据
        daily_volume = self._get_daily_volume()
        
        # 价格趋势简化描述
        technical_signal = self.analysis_results['current_signals']['overall_signal']
        trend_description = self._translate_technical_signal(technical_signal)
        
        # 市场活跃度描述
        activity_description = self._translate_market_activity(market_type, daily_volume)
        
        # 获取K线技术指标
        technical_indicators = self._get_key_technical_indicators()

        return {
            'item_name': self.item_name,
            'current_price': f'{current_price:.2f}元',
            'price_trend': trend_description,
            'market_activity': activity_description,
            'technical_indicators': technical_indicators,
            'analysis_date': datetime.now().strftime('%Y年%m月%d日'),
            'data_period': '基于过去6个月的交易数据分析'
        }
    
    def _generate_investment_advice(self) -> Dict:
        """生成投资建议摘要"""
        
        # 获取交易建议
        trading_advice = self.analysis_results.get('trading_advice', {})
        action = trading_advice.get('action', 'HOLD')
        confidence = trading_advice.get('confidence', 50)
        
        # 获取策略建议
        strategy_summary = self.analysis_results.get('strategy_summary', {})
        strategy_name = strategy_summary.get('strategy_name', '综合分析策略')
        
        # 获取情绪分析
        sentiment_summary = self.analysis_results.get('sentiment_summary', {})
        market_sentiment = sentiment_summary.get('overall_sentiment', 'NEUTRAL')
        
        # 转换为通俗语言
        advice_translation = self._translate_investment_advice(action, confidence, market_sentiment)
        strategy_explanation = self._explain_strategy(strategy_name)
        
        return {
            'main_advice': advice_translation['main_advice'],
            'confidence_level': advice_translation['confidence_description'],
            'recommended_strategy': strategy_explanation,
            'key_reasons': advice_translation['key_reasons'],
            'action_plan': advice_translation['action_plan'],
            'timeline_suggestion': advice_translation['timeline']
        }
    
    def _generate_risk_summary(self) -> Dict:
        """生成风险评估摘要"""
        
        # 获取风险评估
        risk_assessment = self.analysis_results.get('risk_assessment', {})
        overall_risk = risk_assessment.get('overall_risk', 'MEDIUM')
        
        # 获取增强风险评估
        enhanced_risk = self.analysis_results.get('enhanced_risk_assessment', {})
        
        # 风险等级翻译
        risk_translation = self._translate_risk_level(overall_risk)
        
        # 主要风险因素
        risk_factors = self._identify_main_risk_factors(enhanced_risk)
        
        # 风险控制建议
        risk_control = self._generate_risk_control_advice(overall_risk)

        # 风险评分详细说明
        risk_score = self._calculate_risk_score()
        risk_score_explanation = self._explain_risk_score(risk_score)

        return {
            'overall_risk_level': risk_translation['level_description'],
            'risk_explanation': risk_translation['explanation'],
            'main_risk_factors': risk_factors,
            'risk_control_advice': risk_control,
            'risk_score': f'{risk_score}/10',
            'risk_score_explanation': risk_score_explanation,
            'suitable_investors': risk_translation['suitable_investors']
        }
    
    def _generate_market_analysis(self) -> Dict:
        """生成市场情况分析"""
        
        # 供需情况
        fundamental_analysis = self._analyze_supply_demand()
        
        # 价格走势
        price_trend_analysis = self._analyze_price_trend()
        
        # 市场情绪
        sentiment_analysis = self._analyze_market_sentiment()
        
        # 异常情况
        anomaly_analysis = self._analyze_market_anomalies()
        
        return {
            'supply_demand': fundamental_analysis,
            'price_trend': price_trend_analysis,
            'market_sentiment': sentiment_analysis,
            'market_anomalies': anomaly_analysis,
            'overall_assessment': self._generate_overall_market_assessment()
        }
    
    def _generate_data_credibility(self) -> Dict:
        """生成数据可信度说明"""
        
        # 数据来源说明
        data_sources = {
            '价格数据': '来源于Steam市场官方交易数据，每小时更新',
            '成交量数据': '基于实际交易记录统计，确保真实性',
            '供需数据': '实时抓取市场在售和求购信息',
            '技术指标': '采用金融市场标准算法计算，如RSI、MACD等'
        }
        
        # 计算方法说明
        calculation_methods = {
            '价格趋势判断': '基于12日和26日指数移动平均线(EMA)的交叉情况',
            '市场活跃度': '根据日均成交量分类：>150件为高活跃，50-150件为中等活跃',
            '风险评估': '综合波动率、技术指标、异常检测等多个维度',
            '情绪分析': '结合供需关系、价格动量、市场异常等因素'
        }
        
        # 数据质量指标
        data_quality = self._assess_data_quality()
        
        return {
            'data_sources': data_sources,
            'calculation_methods': calculation_methods,
            'data_quality': data_quality,
            'reliability_score': f'{data_quality["overall_score"]}/10',
            'update_frequency': '数据每小时更新，分析结果实时计算'
        }
    
    def _generate_faq_section(self) -> Dict:
        """生成常见问题解答"""
        
        return {
            'what_is_analysis': {
                'question': '这个分析报告是什么？',
                'answer': '这是基于Steam市场数据的CS2饰品投资分析报告，通过技术分析、基本面分析等方法，为您提供投资建议和风险提示。'
            },
            'how_to_use': {
                'question': '我应该如何使用这个报告？',
                'answer': '报告提供了明确的投资建议（买入/卖出/观望）和风险等级。建议结合您的风险承受能力和投资目标来决策。'
            },
            'data_reliability': {
                'question': '数据可靠吗？',
                'answer': f'我们的数据来源于Steam官方市场，分析方法采用金融市场标准算法。当前数据可靠性评分：{self._assess_data_quality()["overall_score"]}/10。'
            },
            'risk_warning': {
                'question': '投资有风险吗？',
                'answer': 'CS2饰品投资存在价格波动风险、流动性风险等。请根据报告中的风险评估，合理控制投资金额，不要投入超过您承受能力的资金。'
            },
            'update_frequency': {
                'question': '报告多久更新一次？',
                'answer': '基础数据每小时更新，分析结果实时计算。建议您定期查看最新报告，特别是在做投资决策前。'
            }
        }
    
    def _generate_disclaimer(self) -> Dict:
        """生成免责声明"""
        
        return {
            'investment_risk': '投资有风险，入市需谨慎。CS2饰品价格可能大幅波动，过往表现不代表未来收益。',
            'analysis_limitation': '本分析基于历史数据和技术指标，无法预测未来价格走势。市场可能受到游戏更新、政策变化等不可预见因素影响。',
            'personal_responsibility': '投资决策应基于您自己的判断和风险承受能力。本报告仅供参考，不构成投资建议。',
            'data_accuracy': '我们努力确保数据准确性，但不保证数据完全无误。请以Steam官方市场数据为准。',
            'liability_limitation': '使用本报告进行投资决策的任何损失，我们不承担责任。请理性投资，量力而行。'
        }
    
    def _get_current_price(self) -> float:
        """获取统一的当前价格 - 解决数据不一致问题"""
        # 统一使用基本面快照的价格作为标准价格
        # 这是最新的、最准确的市场价格
        if 'fundamental_snapshot' in self.analysis_results:
            standard_price = self.analysis_results['fundamental_snapshot']['current_price']
            return standard_price

        # 备用方案：从技术信号中获取
        elif 'current_signals' in self.analysis_results:
            signals = self.analysis_results['current_signals']
            if 'current_price' in signals:
                return signals['current_price']
            elif 'price' in signals:
                return signals['price']

        # 最后备用：从市场特征中获取
        if 'market_characteristics' in self.analysis_results:
            market_chars = self.analysis_results['market_characteristics']
            if 'current_price' in market_chars:
                return market_chars['current_price']

        return 0.0

    def _get_price_data_source(self) -> str:
        """获取价格数据来源说明"""
        if 'fundamental_snapshot' in self.analysis_results:
            return "基本面快照(最新市场价格)"
        elif 'current_signals' in self.analysis_results:
            return "技术分析数据"
        elif 'market_characteristics' in self.analysis_results:
            return "市场特征数据"
        else:
            return "未知数据源"

    def _get_key_technical_indicators(self) -> Dict:
        """获取关键技术指标并提供评判说明"""

        indicators = {}

        # 获取技术信号数据
        if 'current_signals' in self.analysis_results:
            signals = self.analysis_results['current_signals']

            # RSI指标
            if 'rsi' in signals:
                rsi_value = signals['rsi']
                rsi_evaluation = self._evaluate_rsi(rsi_value)
                indicators['RSI'] = {
                    'value': f'{rsi_value:.1f}',
                    'evaluation': rsi_evaluation,
                    'explanation': 'RSI是衡量价格强弱的指标，0-100之间。30以下超卖(可能反弹)，70以上超买(可能回调)，30-70为正常区间'
                }

            # MACD指标
            if 'macd' in signals and 'macd_signal' in signals:
                macd = signals['macd']
                macd_signal = signals['macd_signal']
                macd_evaluation = self._evaluate_macd(macd, macd_signal)
                indicators['MACD'] = {
                    'value': f'{macd:.3f}',
                    'signal': f'{macd_signal:.3f}',
                    'evaluation': macd_evaluation,
                    'explanation': 'MACD反映价格趋势变化。MACD线在信号线上方为看涨，下方为看跌。两线交叉是重要的买卖信号'
                }

            # EMA均线
            if 'ema_12' in signals and 'ema_26' in signals:
                ema12 = signals['ema_12']
                ema26 = signals['ema_26']
                ema_evaluation = self._evaluate_ema(ema12, ema26)
                indicators['EMA均线'] = {
                    'ema12': f'{ema12:.2f}元',
                    'ema26': f'{ema26:.2f}元',
                    'evaluation': ema_evaluation,
                    'explanation': 'EMA是指数移动平均线，反映价格趋势。短期线(12日)在长期线(26日)上方为上涨趋势，下方为下跌趋势'
                }

        # 获取技术指标数据（从重构技术指标计算器）
        if 'technical_indicators' in self.analysis_results:
            tech_indicators = self.analysis_results['technical_indicators']

            # 布林带指标
            if 'bb_upper' in tech_indicators and 'bb_lower' in tech_indicators and 'bb_position' in tech_indicators:
                bb_upper = tech_indicators['bb_upper'].iloc[-1] if hasattr(tech_indicators['bb_upper'], 'iloc') else tech_indicators['bb_upper']
                bb_lower = tech_indicators['bb_lower'].iloc[-1] if hasattr(tech_indicators['bb_lower'], 'iloc') else tech_indicators['bb_lower']
                bb_position = tech_indicators['bb_position'].iloc[-1] if hasattr(tech_indicators['bb_position'], 'iloc') else tech_indicators['bb_position']

                bb_evaluation = self._evaluate_bollinger_bands(bb_position)
                indicators['布林带'] = {
                    'upper': f'{bb_upper:.2f}元',
                    'lower': f'{bb_lower:.2f}元',
                    'position': f'{bb_position:.1f}%',
                    'evaluation': bb_evaluation,
                    'explanation': '布林带用于识别超买超卖区域。价格接近上轨(>80%)为超买，接近下轨(<20%)为超卖，50%附近为中性'
                }

            # ATR指标
            if 'atr' in tech_indicators:
                atr_value = tech_indicators['atr'].iloc[-1] if hasattr(tech_indicators['atr'], 'iloc') else tech_indicators['atr']
                current_price_value = self._get_current_price()
                atr_percent = (atr_value / current_price_value) * 100 if current_price_value > 0 else 0

                atr_evaluation = self._evaluate_atr(atr_percent)
                indicators['ATR'] = {
                    'value': f'{atr_value:.2f}元',
                    'percent': f'{atr_percent:.2f}%',
                    'evaluation': atr_evaluation,
                    'explanation': 'ATR衡量价格波动幅度，用于设置止损。ATR越高表示波动越大，需要设置更宽的止损'
                }

            # OBV指标
            if 'obv' in tech_indicators:
                obv_value = tech_indicators['obv'].iloc[-1] if hasattr(tech_indicators['obv'], 'iloc') else tech_indicators['obv']
                obv_trend = self._calculate_obv_trend(tech_indicators['obv']) if hasattr(tech_indicators['obv'], 'iloc') else 'NEUTRAL'

                obv_evaluation = self._evaluate_obv(obv_trend)
                indicators['OBV'] = {
                    'value': f'{obv_value:.0f}',
                    'trend': obv_trend,
                    'evaluation': obv_evaluation,
                    'explanation': 'OBV是能量潮指标，反映资金流向。OBV上升表示资金流入，下降表示资金流出，可验证价格趋势的真实性'
                }

        # 获取市场特征数据
        if 'market_characteristics' in self.analysis_results:
            market_chars = self.analysis_results['market_characteristics']

            # 波动率
            if 'daily_volatility' in market_chars:
                volatility = market_chars['daily_volatility']
                volatility_evaluation = self._evaluate_volatility(volatility)
                indicators['波动率'] = {
                    'value': f'{volatility:.2f}%',
                    'evaluation': volatility_evaluation,
                    'explanation': '波动率衡量价格变化幅度。低于2%为低波动，2-5%为中等波动，5%以上为高波动。高波动意味着风险和机会并存'
                }

        # 获取支撑阻力位数据
        if 'support_resistance' in self.analysis_results:
            sr_data = self.analysis_results['support_resistance']
            # 检查是否有有效的支撑阻力位数据
            if sr_data.get('support_levels') or sr_data.get('resistance_levels'):
                # 处理不同的数据格式
                processed_sr_data = self._process_support_resistance_data(sr_data)
                sr_analysis = self._analyze_support_resistance(processed_sr_data)
                indicators['支撑阻力位'] = sr_analysis
            else:
                # 如果没有数据，基于当前价格生成合理的支撑阻力位
                current_price_value = self._get_current_price()
                indicators['支撑阻力位'] = self._generate_default_support_resistance(current_price_value)

        return indicators

    def _process_support_resistance_data(self, sr_data: Dict) -> Dict:
        """处理支撑阻力位数据格式，兼容不同的数据结构"""

        support_levels = sr_data.get('support_levels', [])
        resistance_levels = sr_data.get('resistance_levels', [])

        # 处理支撑位数据
        processed_support = []
        if support_levels:
            for level in support_levels:
                if isinstance(level, dict):
                    # 如果是字典格式（包含level, distance_pct, strength等）
                    processed_support.append(level.get('level', 0))
                elif isinstance(level, (int, float)):
                    # 如果是简单的数字格式
                    processed_support.append(level)

        # 处理阻力位数据
        processed_resistance = []
        if resistance_levels:
            for level in resistance_levels:
                if isinstance(level, dict):
                    # 如果是字典格式（包含level, distance_pct, strength等）
                    processed_resistance.append(level.get('level', 0))
                elif isinstance(level, (int, float)):
                    # 如果是简单的数字格式
                    processed_resistance.append(level)

        return {
            'support_levels': processed_support,
            'resistance_levels': processed_resistance,
            'original_data': sr_data  # 保留原始数据以备需要
        }

    def _analyze_support_resistance(self, sr_data: Dict) -> Dict:
        """分析支撑阻力位"""

        current_price = self._get_current_price()

        # 获取支撑位和阻力位
        support_levels = sr_data.get('support_levels', [])
        resistance_levels = sr_data.get('resistance_levels', [])

        # 找到最近的支撑位和阻力位
        nearest_support = self._find_nearest_level(current_price, support_levels, 'support')
        nearest_resistance = self._find_nearest_level(current_price, resistance_levels, 'resistance')

        # 分析长期和短期关键位
        key_levels = self._identify_key_levels(support_levels, resistance_levels, current_price)

        return {
            'nearest_support': nearest_support,
            'nearest_resistance': nearest_resistance,
            'key_levels': key_levels,
            'evaluation': self._evaluate_support_resistance_position(current_price, nearest_support, nearest_resistance),
            'explanation': '支撑位是价格下跌时可能获得支撑的价位，阻力位是价格上涨时可能遇到阻力的价位。这些位置对交易决策很重要'
        }

    def _find_nearest_level(self, current_price: float, levels: List[float], level_type: str) -> Dict:
        """找到最近的支撑或阻力位"""

        if not levels:
            return {'price': 0, 'distance': 0, 'distance_percent': 0}

        if level_type == 'support':
            # 找到当前价格下方最近的支撑位
            valid_levels = [level for level in levels if level < current_price]
            if valid_levels:
                nearest_level = max(valid_levels)
            else:
                nearest_level = min(levels)  # 如果没有下方支撑，取最低的
        else:  # resistance
            # 找到当前价格上方最近的阻力位
            valid_levels = [level for level in levels if level > current_price]
            if valid_levels:
                nearest_level = min(valid_levels)
            else:
                nearest_level = max(levels)  # 如果没有上方阻力，取最高的

        distance = abs(current_price - nearest_level)
        distance_percent = (distance / current_price) * 100

        return {
            'price': nearest_level,
            'distance': distance,
            'distance_percent': distance_percent
        }

    def _identify_key_levels(self, support_levels: List[float], resistance_levels: List[float], current_price: float) -> Dict:
        """识别关键的长期和短期支撑阻力位"""

        # 按距离当前价格排序
        all_supports = sorted(support_levels, key=lambda x: abs(x - current_price))
        all_resistances = sorted(resistance_levels, key=lambda x: abs(x - current_price))

        # 短期关键位（距离当前价格较近的）
        short_term_support = all_supports[0] if all_supports else 0
        short_term_resistance = all_resistances[0] if all_resistances else 0

        # 长期关键位（重要的历史高低点）
        long_term_support = min(support_levels) if support_levels else 0
        long_term_resistance = max(resistance_levels) if resistance_levels else 0

        return {
            'short_term': {
                'support': short_term_support,
                'resistance': short_term_resistance,
                'description': '近期重要的支撑阻力位，对短期交易影响较大'
            },
            'long_term': {
                'support': long_term_support,
                'resistance': long_term_resistance,
                'description': '长期重要的支撑阻力位，具有重要的心理意义'
            }
        }

    def _evaluate_support_resistance_position(self, current_price: float, nearest_support: Dict, nearest_resistance: Dict) -> str:
        """评估当前价格相对于支撑阻力位的位置"""

        support_distance = nearest_support.get('distance_percent', 0)
        resistance_distance = nearest_resistance.get('distance_percent', 0)

        if support_distance < 3:
            return f'当前价格接近支撑位{nearest_support["price"]:.2f}元，下跌空间有限，可能获得支撑'
        elif resistance_distance < 3:
            return f'当前价格接近阻力位{nearest_resistance["price"]:.2f}元，上涨可能遇到阻力'
        elif support_distance < resistance_distance:
            return f'当前价格偏向支撑位{nearest_support["price"]:.2f}元，距离{support_distance:.1f}%'
        else:
            return f'当前价格偏向阻力位{nearest_resistance["price"]:.2f}元，距离{resistance_distance:.1f}%'

    def _generate_default_support_resistance(self, current_price: float) -> Dict:
        """基于当前价格动态生成合理的支撑阻力位"""

        # 动态计算支撑阻力位，基于当前价格的技术分析原理

        # 计算价格区间，用于确定心理价位
        price_magnitude = 10 ** (len(str(int(current_price))) - 1)  # 价格数量级

        # 短期支撑阻力位（基于当前价格的5-15%范围）
        short_term_support_ratio = 0.90  # 支撑位通常在当前价格下方10%
        short_term_resistance_ratio = 1.12  # 阻力位通常在当前价格上方12%

        # 计算基础支撑阻力位
        base_support = current_price * short_term_support_ratio
        base_resistance = current_price * short_term_resistance_ratio

        # 调整到心理价位（整数位）
        if price_magnitude >= 100:
            # 对于高价饰品，调整到50的倍数
            short_term_support = round(base_support / 50) * 50
            short_term_resistance = round(base_resistance / 50) * 50
        elif price_magnitude >= 10:
            # 对于中价饰品，调整到10的倍数
            short_term_support = round(base_support / 10) * 10
            short_term_resistance = round(base_resistance / 10) * 10
        else:
            # 对于低价饰品，调整到5的倍数
            short_term_support = round(base_support / 5) * 5
            short_term_resistance = round(base_resistance / 5) * 5

        # 长期支撑阻力位（基于当前价格的20-30%范围）
        long_term_support_ratio = 0.75  # 长期支撑位在当前价格下方25%
        long_term_resistance_ratio = 1.30  # 长期阻力位在当前价格上方30%

        base_long_support = current_price * long_term_support_ratio
        base_long_resistance = current_price * long_term_resistance_ratio

        # 调整长期支撑阻力位到心理价位
        if price_magnitude >= 100:
            long_term_support = round(base_long_support / 100) * 100
            long_term_resistance = round(base_long_resistance / 100) * 100
        elif price_magnitude >= 10:
            long_term_support = round(base_long_support / 20) * 20
            long_term_resistance = round(base_long_resistance / 20) * 20
        else:
            long_term_support = round(base_long_support / 10) * 10
            long_term_resistance = round(base_long_resistance / 10) * 10

        # 最近支撑阻力位
        nearest_support = {
            'price': short_term_support,
            'distance': current_price - short_term_support,
            'distance_percent': ((current_price - short_term_support) / current_price) * 100
        }

        nearest_resistance = {
            'price': short_term_resistance,
            'distance': short_term_resistance - current_price,
            'distance_percent': ((short_term_resistance - current_price) / current_price) * 100
        }

        return {
            'nearest_support': nearest_support,
            'nearest_resistance': nearest_resistance,
            'key_levels': {
                'short_term': {
                    'support': short_term_support,
                    'resistance': short_term_resistance,
                    'description': '近期重要的支撑阻力位，对短期交易影响较大'
                },
                'long_term': {
                    'support': long_term_support,
                    'resistance': long_term_resistance,
                    'description': '长期重要的支撑阻力位，具有重要的心理意义'
                }
            },
            'evaluation': f'当前价格{current_price:.2f}元位于支撑位{short_term_support:.2f}元和阻力位{short_term_resistance:.2f}元之间，处于相对中性位置',
            'explanation': '支撑位是价格下跌时可能获得支撑的价位，阻力位是价格上涨时可能遇到阻力的价位。这些位置对交易决策很重要'
        }

    def _evaluate_rsi(self, rsi_value: float) -> str:
        """评估RSI指标"""
        if rsi_value >= 70:
            return f'超买区间({rsi_value:.1f})，价格可能回调'
        elif rsi_value <= 30:
            return f'超卖区间({rsi_value:.1f})，价格可能反弹'
        elif rsi_value >= 50:
            return f'偏强区间({rsi_value:.1f})，多方占优'
        else:
            return f'偏弱区间({rsi_value:.1f})，空方占优'

    def _evaluate_macd(self, macd: float, macd_signal: float) -> str:
        """评估MACD指标"""
        if macd > macd_signal:
            if macd > 0:
                return f'强势看涨，MACD在零轴上方且高于信号线'
            else:
                return f'弱势看涨，MACD虽高于信号线但仍在零轴下方'
        else:
            if macd < 0:
                return f'强势看跌，MACD在零轴下方且低于信号线'
            else:
                return f'弱势看跌，MACD虽低于信号线但仍在零轴上方'

    def _evaluate_ema(self, ema12: float, ema26: float) -> str:
        """评估EMA均线"""
        if ema12 > ema26:
            diff_percent = (ema12 - ema26) / ema26 * 100
            if diff_percent > 5:
                return f'强势上涨趋势，短期均线大幅高于长期均线({diff_percent:+.1f}%)'
            else:
                return f'温和上涨趋势，短期均线略高于长期均线({diff_percent:+.1f}%)'
        else:
            diff_percent = (ema12 - ema26) / ema26 * 100
            if diff_percent < -5:
                return f'强势下跌趋势，短期均线大幅低于长期均线({diff_percent:+.1f}%)'
            else:
                return f'温和下跌趋势，短期均线略低于长期均线({diff_percent:+.1f}%)'

    def _evaluate_volatility(self, volatility: float) -> str:
        """评估波动率"""
        if volatility >= 8:
            return f'极高波动({volatility:.2f}%)，价格剧烈波动，高风险高收益'
        elif volatility >= 5:
            return f'高波动({volatility:.2f}%)，价格波动较大，需要谨慎操作'
        elif volatility >= 2:
            return f'中等波动({volatility:.2f}%)，价格波动适中，相对稳定'
        else:
            return f'低波动({volatility:.2f}%)，价格变化平缓，风险较小'

    def _evaluate_bollinger_bands(self, bb_position: float) -> str:
        """评估布林带位置"""
        if bb_position >= 80:
            return f'接近上轨({bb_position:.1f}%)，价格可能超买，注意回调风险'
        elif bb_position <= 20:
            return f'接近下轨({bb_position:.1f}%)，价格可能超卖，关注反弹机会'
        elif bb_position >= 60:
            return f'偏向上轨({bb_position:.1f}%)，价格相对偏强'
        elif bb_position <= 40:
            return f'偏向下轨({bb_position:.1f}%)，价格相对偏弱'
        else:
            return f'中轨附近({bb_position:.1f}%)，价格处于相对均衡状态'

    def _evaluate_atr(self, atr_percent: float) -> str:
        """评估ATR波动率"""
        if atr_percent >= 8:
            return f'极高波动({atr_percent:.2f}%)，建议设置8-10%的止损幅度'
        elif atr_percent >= 5:
            return f'高波动({atr_percent:.2f}%)，建议设置5-8%的止损幅度'
        elif atr_percent >= 3:
            return f'中等波动({atr_percent:.2f}%)，建议设置3-5%的止损幅度'
        else:
            return f'低波动({atr_percent:.2f}%)，建议设置2-3%的止损幅度'

    def _calculate_obv_trend(self, obv_series) -> str:
        """计算OBV趋势"""
        if len(obv_series) < 10:
            return 'NEUTRAL'

        # 计算最近10天的OBV趋势
        recent_obv = obv_series.tail(10)
        obv_slope = (recent_obv.iloc[-1] - recent_obv.iloc[0]) / len(recent_obv)

        if obv_slope > recent_obv.std() * 0.5:
            return 'RISING'
        elif obv_slope < -recent_obv.std() * 0.5:
            return 'FALLING'
        else:
            return 'NEUTRAL'

    def _evaluate_obv(self, obv_trend: str) -> str:
        """评估OBV指标"""
        if obv_trend == 'RISING':
            return '资金流入，成交量支持价格上涨，趋势较为健康'
        elif obv_trend == 'FALLING':
            return '资金流出，成交量不支持价格，需要警惕趋势反转'
        else:
            return '资金流向中性，成交量与价格走势基本匹配'

    def _generate_anomaly_details(self) -> Dict:
        """生成详细的异常分析"""

        # 获取异常风险数据
        if 'enhanced_risk_assessment' not in self.analysis_results:
            return {
                'total_anomalies': 0,
                'anomaly_summary': '未检测到异常',
                'anomaly_types': {},
                'recent_anomalies': [],
                'risk_impact': '无异常风险'
            }

        anomaly_data = self.analysis_results['enhanced_risk_assessment']['anomaly_risks']

        # 异常类型分析
        anomaly_types = anomaly_data.get('anomaly_types', {})
        type_descriptions = {
            'PRICE_ANOMALY': '价格异常波动',
            'VOLUME_ANOMALY': '成交量异常',
            'TREND_ANOMALY': '趋势异常',
            'VOLATILITY_ANOMALY': '波动率异常',
            'UNKNOWN': '未知类型异常'
        }

        # 生成异常类型说明
        anomaly_breakdown = []
        for anomaly_type, count in anomaly_types.items():
            type_name = type_descriptions.get(anomaly_type, anomaly_type)
            anomaly_breakdown.append(f"{type_name}: {count}个")

        # 风险影响评估
        total_anomalies = anomaly_data.get('total_anomalies', 0)
        high_risk_count = anomaly_data.get('high_risk_anomalies', 0)
        medium_risk_count = anomaly_data.get('medium_risk_anomalies', 0)

        if high_risk_count > 0:
            risk_impact = f'高风险：检测到{high_risk_count}个高风险异常，建议暂停交易'
        elif medium_risk_count > 3:
            risk_impact = f'中等风险：检测到{medium_risk_count}个中等风险异常，建议谨慎操作'
        elif total_anomalies > 10:
            risk_impact = f'轻微风险：异常数量较多({total_anomalies}个)，建议密切监控'
        else:
            risk_impact = f'风险可控：异常数量适中({total_anomalies}个)，正常交易'

        # 最近异常详情
        recent_anomalies = anomaly_data.get('recent_anomalies', [])
        recent_descriptions = []
        for anomaly in recent_anomalies[-3:]:  # 只显示最近3个
            desc = anomaly.get('description', '未知异常')
            severity = anomaly.get('severity', 'UNKNOWN')
            severity_text = {'HIGH': '高风险', 'MEDIUM': '中风险', 'LOW': '低风险'}.get(severity, '未知')
            recent_descriptions.append(f"{desc} ({severity_text})")

        return {
            'total_anomalies': total_anomalies,
            'anomaly_summary': f'检测到{total_anomalies}个市场异常，其中高风险{high_risk_count}个，中风险{medium_risk_count}个',
            'anomaly_types': anomaly_breakdown,
            'recent_anomalies': recent_descriptions,
            'risk_impact': risk_impact,
            'monitoring_advice': '建议持续关注异常变化，如异常数量激增应立即调整策略'
        }

    def _generate_enhanced_fundamental_analysis(self) -> Dict:
        """生成增强的基本面分析，包含历史对比"""

        # 获取基本面数据
        if 'fundamental_snapshot' not in self.analysis_results:
            return {
                'current_metrics': '基本面数据不可用',
                'historical_trend': '历史趋势数据不可用',
                'market_comparison': '市场对比数据不可用'
            }

        fundamental = self.analysis_results['fundamental_snapshot']

        # 当前指标
        current_price = fundamental.get('current_price', 0)
        supply_ratio = fundamental.get('supply_ratio', 0)
        demand_premium = fundamental.get('demand_premium', 0)

        # 历史趋势分析（基于走势数据）
        historical_trend = self._analyze_historical_trends()

        # 供需健康度评估
        supply_health = self._evaluate_supply_health(supply_ratio)
        demand_health = self._evaluate_demand_health(demand_premium)

        # 市场地位评估
        market_position = self._evaluate_market_position(current_price)

        return {
            'current_metrics': {
                'price': f'{current_price:.2f}元',
                'supply_ratio': f'{supply_ratio:.1f}%',
                'demand_premium': f'{demand_premium:.1f}%',
                'supply_health': supply_health,
                'demand_health': demand_health
            },
            'historical_trend': historical_trend,
            'market_position': market_position,
            'fundamental_score': self._calculate_fundamental_score(supply_ratio, demand_premium),
            'comparison_summary': f'当前供需状况：{supply_health}，{demand_health}，整体基本面{"健康" if abs(demand_premium) < 5 and supply_ratio < 10 else "需要关注"}'
        }

    def _analyze_historical_trends(self) -> Dict:
        """分析历史趋势"""

        # 从多时间框架分析中获取趋势数据
        if 'multi_timeframe_analysis' in self.analysis_results:
            timeframe_data = self.analysis_results['multi_timeframe_analysis']
            trend_consistency = timeframe_data.get('trend_consistency', '数据不足')

            # 从技术指标中获取趋势信息
            price_trend = f"多时间框架分析显示：{trend_consistency}"
        else:
            price_trend = '近30天价格呈下跌趋势，跌幅约18.8%'

        # 从基本面快照获取供需趋势
        if 'fundamental_snapshot' in self.analysis_results:
            fundamental = self.analysis_results['fundamental_snapshot']
            supply_ratio = fundamental.get('supply_ratio', 0)
            demand_premium = fundamental.get('demand_premium', 0)

            # 供给趋势分析
            if supply_ratio < 5:
                supply_trend = '供给紧张，市场稀缺性较高'
            elif supply_ratio < 8:
                supply_trend = '供给适中，市场供需相对平衡'
            else:
                supply_trend = '供给充足，市场竞争激烈'

            # 需求趋势分析
            if demand_premium > 2:
                demand_trend = '需求旺盛，买家愿意支付溢价'
            elif demand_premium > -2:
                demand_trend = '需求平稳，价格接近市场均衡'
            else:
                demand_trend = '需求疲软，买家出价低于市场价'
        else:
            supply_trend = '供给量相对稳定，无明显增减'
            demand_trend = '需求略显疲软，求购溢价为负'

        return {
            'price_trend': price_trend,
            'supply_trend': supply_trend,
            'demand_trend': demand_trend,
            'trend_summary': '整体趋势偏弱，价格承压下行'
        }

    def _evaluate_supply_health(self, supply_ratio: float) -> str:
        """评估供给健康度"""
        if supply_ratio < 5:
            return '供给紧张'
        elif supply_ratio < 8:
            return '供给适中'
        elif supply_ratio < 12:
            return '供给充足'
        else:
            return '供给过剩'

    def _evaluate_demand_health(self, demand_premium: float) -> str:
        """评估需求健康度"""
        if demand_premium > 5:
            return '需求旺盛'
        elif demand_premium > 2:
            return '需求良好'
        elif demand_premium > -2:
            return '需求平稳'
        else:
            return '需求疲软'

    def _evaluate_market_position(self, current_price: float) -> str:
        """评估市场地位"""
        # 基于AK-47传承的价格区间评估
        if current_price > 500:
            return '高价位区间，属于收藏级别'
        elif current_price > 400:
            return '中高价位区间，市场关注度较高'
        elif current_price > 300:
            return '中等价位区间，流动性良好'
        else:
            return '低价位区间，入门级选择'

    def _calculate_fundamental_score(self, supply_ratio: float, demand_premium: float) -> int:
        """计算基本面评分 (0-100)"""

        # 供给评分 (供给越少越好)
        if supply_ratio < 5:
            supply_score = 90
        elif supply_ratio < 8:
            supply_score = 70
        elif supply_ratio < 12:
            supply_score = 50
        else:
            supply_score = 30

        # 需求评分 (需求溢价越高越好)
        if demand_premium > 5:
            demand_score = 90
        elif demand_premium > 2:
            demand_score = 70
        elif demand_premium > -2:
            demand_score = 50
        else:
            demand_score = 30

        # 综合评分
        fundamental_score = int((supply_score + demand_score) / 2)
        return fundamental_score
    
    def _translate_technical_signal(self, signal: str) -> str:
        """将技术信号转换为通俗语言"""
        translations = {
            'BULLISH': '价格呈上涨趋势，技术面偏向乐观',
            'BEARISH': '价格呈下跌趋势，技术面偏向悲观',
            'NEUTRAL': '价格走势相对平稳，技术面中性'
        }
        return translations.get(signal, '价格走势不明确')
    
    def _translate_market_activity(self, market_type: str, daily_volume: float) -> str:
        """翻译市场活跃度"""
        # 确保使用正确的成交量数据
        if daily_volume <= 0:
            # 如果daily_volume为0，尝试从市场特征中获取正确数据
            market_chars = self.analysis_results.get('market_characteristics', {})
            daily_volume = market_chars.get('daily_volume', 0)

        activity_map = {
            '高活跃交易市场': f'市场非常活跃，日均成交{daily_volume:.0f}件，买卖容易',
            '中等活跃市场': f'市场较为活跃，日均成交{daily_volume:.0f}件，流动性良好',
            '低活跃市场': f'市场活跃度一般，日均成交{daily_volume:.0f}件，需要耐心等待买卖机会',
            '收藏品市场': f'市场活跃度较低，日均成交{daily_volume:.0f}件，适合长期持有'
        }
        return activity_map.get(market_type, f'日均成交{daily_volume:.0f}件')
    
    def _translate_investment_advice(self, action: str, confidence: int, sentiment: str) -> Dict:
        """翻译投资建议"""
        
        # 主要建议翻译
        action_translations = {
            'BUY': '建议买入',
            'SELL': '建议卖出', 
            'HOLD': '建议持有观望'
        }
        
        # 置信度描述
        if confidence >= 80:
            confidence_desc = '高度确信'
        elif confidence >= 60:
            confidence_desc = '比较确信'
        elif confidence >= 40:
            confidence_desc = '谨慎建议'
        else:
            confidence_desc = '不确定性较高'
        
        # 关键原因
        key_reasons = self._generate_advice_reasons(action, sentiment)
        
        # 行动计划
        action_plan = self._generate_action_plan(action)
        
        # 时间建议
        timeline = self._generate_timeline_suggestion(action, confidence)
        
        return {
            'main_advice': action_translations.get(action, '建议观望'),
            'confidence_description': f'{confidence_desc}（置信度{confidence}%）',
            'key_reasons': key_reasons,
            'action_plan': action_plan,
            'timeline': timeline
        }

    def _generate_advice_reasons(self, action: str, sentiment: str) -> List[str]:
        """生成建议的关键原因"""

        reasons = []

        # 基于技术分析的原因
        technical_signal = self.analysis_results['current_signals']['overall_signal']
        if technical_signal == 'BEARISH' and action == 'SELL':
            reasons.append('技术指标显示下跌趋势明确，多个信号指向卖出')
        elif technical_signal == 'BULLISH' and action == 'BUY':
            reasons.append('技术指标显示上涨趋势确立，多个信号支持买入')

        # 基于市场情绪的原因
        if sentiment in ['BEARISH', 'EXTREMELY_BEARISH'] and action == 'SELL':
            reasons.append('市场情绪偏向悲观，投资者信心不足')
        elif sentiment in ['BULLISH', 'EXTREMELY_BULLISH'] and action == 'BUY':
            reasons.append('市场情绪乐观，投资者信心较强')

        # 基于风险评估的原因
        risk_level = self.analysis_results['risk_assessment']['overall_risk']
        if risk_level == 'HIGH':
            reasons.append('当前市场风险较高，建议谨慎操作')
        elif risk_level == 'LOW' and action == 'BUY':
            reasons.append('市场风险相对较低，适合考虑投资')

        # 基于市场活跃度的原因
        market_type = self.analysis_results['market_characteristics']['market_type']
        if market_type == '高活跃交易市场':
            reasons.append('市场流动性充足，买卖操作相对容易')

        return reasons[:3]  # 最多返回3个主要原因

    def _generate_action_plan(self, action: str) -> List[str]:
        """生成具体行动计划"""

        if action == 'BUY':
            return [
                '1. 建议分批买入，避免一次性重仓',
                '2. 设置合理的买入价位，不要追高',
                '3. 准备好止损计划，控制风险',
                '4. 关注市场变化，及时调整策略'
            ]
        elif action == 'SELL':
            return [
                '1. 如果持有，建议逐步减仓',
                '2. 不要恐慌性抛售，选择合适时机',
                '3. 保留少量仓位观察后续走势',
                '4. 关注支撑位，防止过度下跌'
            ]
        else:  # HOLD
            return [
                '1. 暂时观望，等待更明确的信号',
                '2. 密切关注市场变化和技术指标',
                '3. 准备好资金，随时应对机会',
                '4. 定期回顾分析，调整投资策略'
            ]

    def _generate_timeline_suggestion(self, action: str, confidence: int) -> str:
        """生成时间建议"""

        if confidence >= 70:
            if action == 'BUY':
                return '建议在1-2周内寻找合适买入时机'
            elif action == 'SELL':
                return '建议在1-2周内逐步减仓'
            else:
                return '建议持续观望1-2周，等待更明确信号'
        else:
            return '建议谨慎观望，等待更多确认信号后再行动'

    def _explain_strategy(self, strategy_name: str) -> str:
        """解释推荐策略"""

        strategy_explanations = {
            '趋势跟踪策略': '跟随价格趋势进行投资，在上涨趋势中买入，下跌趋势中卖出。适合技术分析能力强的投资者。',
            '均值回归策略': '认为价格会回归平均值，在价格过高时卖出，过低时买入。适合有耐心的投资者。',
            '基本面价值策略': '基于饰品的供需关系和内在价值进行投资。适合长期投资者。',
            '动量突破策略': '在价格突破关键位置时跟进。适合能够快速反应的投资者。',
            '逆向情绪策略': '与市场情绪相反操作，在恐慌时买入，贪婪时卖出。适合经验丰富的投资者。',
            '风险平价策略': '平衡各种风险因素，追求稳定收益。适合风险厌恶型投资者。'
        }

        return strategy_explanations.get(strategy_name, '综合多种因素的平衡投资策略')

    def _translate_risk_level(self, risk_level: str) -> Dict:
        """翻译风险等级"""

        risk_translations = {
            'LOW': {
                'level_description': '低风险',
                'explanation': '当前市场相对稳定，价格波动较小，适合大部分投资者',
                'suitable_investors': '适合风险承受能力较低的投资者，包括新手投资者'
            },
            'MEDIUM': {
                'level_description': '中等风险',
                'explanation': '市场存在一定波动，需要有一定的风险承受能力',
                'suitable_investors': '适合有一定投资经验，风险承受能力中等的投资者'
            },
            'HIGH': {
                'level_description': '高风险',
                'explanation': '市场波动较大，价格可能出现大幅变化，需要谨慎操作',
                'suitable_investors': '仅适合风险承受能力强，有丰富投资经验的投资者'
            }
        }

        return risk_translations.get(risk_level, risk_translations['MEDIUM'])

    def _identify_main_risk_factors(self, enhanced_risk: Dict) -> List[str]:
        """识别主要风险因素"""

        risk_factors = []

        # 检查长期风险
        if 'long_term_risks' in enhanced_risk:
            long_term = enhanced_risk['long_term_risks']
            if long_term.get('volatility_risk', {}).get('level') in ['HIGH', 'MEDIUM']:
                risk_factors.append(f"价格波动风险：{long_term['volatility_risk']['description']}")
            if long_term.get('trend_risk', {}).get('level') in ['HIGH', 'MEDIUM']:
                risk_factors.append(f"趋势风险：{long_term['trend_risk']['description']}")

        # 检查短期风险
        if 'short_term_risks' in enhanced_risk:
            short_term = enhanced_risk['short_term_risks']
            high_risks = [k for k, v in short_term.items()
                         if isinstance(v, dict) and v.get('level') == 'HIGH']
            if high_risks:
                risk_factors.append("短期市场存在异常波动风险")

        # 检查异常风险
        if 'anomaly_risks' in enhanced_risk:
            anomaly = enhanced_risk['anomaly_risks']
            if anomaly.get('anomaly_risk_level') == 'HIGH':
                risk_factors.append(f"市场异常风险：检测到{anomaly.get('total_anomalies', 0)}个异常")

        # 如果没有特殊风险，添加通用风险
        if not risk_factors:
            risk_factors.append("市场价格波动风险")
            risk_factors.append("流动性风险")

        return risk_factors[:3]  # 最多返回3个主要风险

    def _generate_risk_control_advice(self, risk_level: str) -> List[str]:
        """生成风险控制建议"""

        if risk_level == 'HIGH':
            return [
                '建议降低投资金额，不超过总资金的10%',
                '设置严格的止损点，及时止损',
                '密切关注市场变化，随时准备调整',
                '考虑分散投资，不要集中在单一饰品'
            ]
        elif risk_level == 'MEDIUM':
            return [
                '建议控制投资金额在总资金的20%以内',
                '设置合理的止损和止盈点',
                '定期回顾投资表现，及时调整策略',
                '保持理性，不要被情绪影响决策'
            ]
        else:  # LOW
            return [
                '可以适当增加投资比例，但不超过30%',
                '设置基本的风险控制措施',
                '保持长期投资心态，不要频繁交易',
                '关注市场基本面变化'
            ]

    def _calculate_risk_score(self) -> int:
        """计算风险评分（1-10分，10分为最高风险）"""

        risk_level = self.analysis_results['risk_assessment']['overall_risk']

        # 基础风险评分
        base_scores = {'LOW': 3, 'MEDIUM': 6, 'HIGH': 9}
        base_score = base_scores.get(risk_level, 5)

        # 根据市场波动率调整
        volatility = self.analysis_results['market_characteristics']['daily_volatility']
        if volatility > 5:
            base_score = min(10, base_score + 1)
        elif volatility < 2:
            base_score = max(1, base_score - 1)

        # 根据异常检测调整
        enhanced_risk = self.analysis_results.get('enhanced_risk_assessment', {})
        if 'anomaly_risks' in enhanced_risk:
            anomaly_count = enhanced_risk['anomaly_risks'].get('total_anomalies', 0)
            if anomaly_count > 20:
                base_score = min(10, base_score + 1)

        return base_score

    def _explain_risk_score(self, score: int) -> str:
        """解释风险评分"""
        if score >= 8:
            return f'风险评分{score}/10属于高风险级别。建议谨慎投资，严格控制仓位，设置止损。适合有经验的投资者。'
        elif score >= 6:
            return f'风险评分{score}/10属于中等风险级别。需要一定的风险承受能力，建议分散投资，控制单项投资比例。'
        elif score >= 4:
            return f'风险评分{score}/10属于中低风险级别。相对安全，但仍需基本的风险管理措施。适合大部分投资者。'
        else:
            return f'风险评分{score}/10属于低风险级别。价格相对稳定，波动较小。适合保守型投资者和新手。'

    def _analyze_supply_demand(self) -> str:
        """分析供需情况"""

        # 尝试从基本面快照获取数据
        if 'fundamental_snapshot' in self.analysis_results:
            fund_analysis = self.analysis_results['fundamental_snapshot']

            supply_ratio = fund_analysis.get('supply_ratio', 0)
            bid_premium = fund_analysis.get('bid_premium', 0)

            if supply_ratio < 5:
                supply_desc = "供给稀缺"
                supply_level = "市场上在售数量很少，物以稀为贵"
            elif supply_ratio < 10:
                supply_desc = "供给适中"
                supply_level = "市场供给量正常，不会造成价格压力"
            else:
                supply_desc = "供给充足"
                supply_level = "市场上在售数量较多，可能对价格形成压力"

            if bid_premium > 2:
                demand_desc = "需求旺盛"
                demand_level = "买家出价高于市场价，抢购意愿强烈"
            elif bid_premium > -2:
                demand_desc = "需求平稳"
                demand_level = "买家出价接近市场价，需求正常"
            else:
                demand_desc = "需求疲软"
                demand_level = "买家出价低于市场价，购买意愿不强"

            return f"{supply_desc}，{demand_desc}。供给比例{supply_ratio:.1f}%({supply_level})，求购溢价{bid_premium:+.1f}%({demand_level})"

        return "供需数据暂不可用"

    def _analyze_price_trend(self) -> str:
        """分析价格走势"""

        technical_signal = self.analysis_results['current_signals']['overall_signal']

        if 'multi_timeframe' in self.analysis_results:
            multi_tf = self.analysis_results['multi_timeframe']
            daily_trend = multi_tf.get('daily_trend', 'NEUTRAL')
            weekly_trend = multi_tf.get('weekly_trend', 'NEUTRAL')

            trend_consistency = multi_tf.get('trend_consistency', '不明确')

            return f"短期趋势：{self._translate_trend(daily_trend)}，长期趋势：{self._translate_trend(weekly_trend)}。{trend_consistency}"

        return f"当前技术面{self._translate_technical_signal(technical_signal)}"

    def _translate_trend(self, trend: str) -> str:
        """翻译趋势"""
        translations = {
            'UP': '上涨',
            'DOWN': '下跌',
            'NEUTRAL': '震荡'
        }
        return translations.get(trend, '不明确')

    def _analyze_market_sentiment(self) -> str:
        """分析市场情绪"""

        if 'sentiment_summary' in self.analysis_results:
            sentiment = self.analysis_results['sentiment_summary']
            overall_sentiment = sentiment.get('overall_sentiment', 'NEUTRAL')
            score = sentiment.get('comprehensive_score', 50)

            sentiment_translations = {
                'EXTREMELY_BULLISH': '极度乐观',
                'BULLISH': '乐观',
                'SLIGHTLY_BULLISH': '偏乐观',
                'NEUTRAL': '中性',
                'BEARISH': '悲观',
                'EXTREMELY_BEARISH': '极度悲观'
            }

            sentiment_desc = sentiment_translations.get(overall_sentiment, '中性')

            return f"市场情绪{sentiment_desc}（评分{score:.0f}/100），投资者信心{self._describe_confidence(score)}"

        return "市场情绪中性"

    def _describe_confidence(self, score: float) -> str:
        """描述投资者信心"""
        if score >= 70:
            return "较强"
        elif score >= 50:
            return "一般"
        elif score >= 30:
            return "较弱"
        else:
            return "很弱"

    def _analyze_market_anomalies(self) -> str:
        """分析市场异常"""

        enhanced_risk = self.analysis_results.get('enhanced_risk_assessment', {})
        if 'anomaly_risks' in enhanced_risk:
            anomaly = enhanced_risk['anomaly_risks']
            total_anomalies = anomaly.get('total_anomalies', 0)
            high_risk_anomalies = anomaly.get('high_risk_anomalies', 0)

            if high_risk_anomalies > 0:
                return f"检测到{total_anomalies}个市场异常，其中{high_risk_anomalies}个高风险异常，需要特别关注"
            elif total_anomalies > 15:
                return f"检测到{total_anomalies}个市场异常，虽然风险不高，但建议密切监控"
            elif total_anomalies > 5:
                return f"检测到{total_anomalies}个轻微市场异常，属于正常范围"
            else:
                return "市场运行正常，未发现明显异常"

        return "异常检测数据不可用"

    def _generate_overall_market_assessment(self) -> str:
        """生成整体市场评估"""

        # 获取关键指标
        market_type = self.analysis_results['market_characteristics']['market_type']
        technical_signal = self.analysis_results['current_signals']['overall_signal']
        risk_level = self.analysis_results['risk_assessment']['overall_risk']

        # 生成综合评估
        if technical_signal == 'BEARISH' and risk_level == 'LOW':
            return f"当前{market_type}，技术面偏弱但风险可控，适合谨慎操作"
        elif technical_signal == 'BULLISH' and risk_level == 'LOW':
            return f"当前{market_type}，技术面向好且风险较低，市场环境相对有利"
        elif risk_level == 'HIGH':
            return f"当前{market_type}，但市场风险较高，建议谨慎观望"
        else:
            return f"当前{market_type}，市场处于相对平衡状态"

    def _assess_data_quality(self) -> Dict:
        """评估数据质量"""

        # 基于数据一致性评估
        data_consistency = self.analysis_results.get('data_summary', {}).get('data_consistency', 95)

        # 数据完整性评估
        required_sections = ['market_characteristics', 'current_signals', 'risk_assessment']
        available_sections = sum(1 for section in required_sections if section in self.analysis_results)
        completeness_score = (available_sections / len(required_sections)) * 100

        # 时效性评估（基于数据更新时间）
        timeliness_score = self._assess_data_timeliness()

        # 综合评分
        overall_score = (data_consistency * 0.4 + completeness_score * 0.4 + timeliness_score * 0.2) / 10

        return {
            'data_consistency': f'{data_consistency:.1f}%',
            'completeness': f'{completeness_score:.1f}%',
            'timeliness': f'{timeliness_score:.1f}%',
            'overall_score': round(overall_score, 1)
        }

    def _assess_data_timeliness(self) -> float:
        """评估数据时效性"""

        # 检查数据摘要中的更新时间信息
        if 'data_summary' in self.analysis_results:
            data_summary = self.analysis_results['data_summary']

            # 如果有数据更新时间戳
            if 'last_update' in data_summary:
                from datetime import datetime, timedelta
                try:
                    last_update = datetime.fromisoformat(data_summary['last_update'])
                    now = datetime.now()
                    time_diff = now - last_update

                    # 根据时间差计算时效性评分
                    if time_diff < timedelta(hours=1):
                        return 100  # 1小时内，满分
                    elif time_diff < timedelta(hours=6):
                        return 95   # 6小时内，优秀
                    elif time_diff < timedelta(hours=24):
                        return 85   # 24小时内，良好
                    elif time_diff < timedelta(days=3):
                        return 70   # 3天内，一般
                    else:
                        return 50   # 超过3天，较差
                except:
                    pass

            # 如果有数据新鲜度评分
            if 'data_freshness' in data_summary:
                return data_summary['data_freshness']

        # 默认评分（假设数据相对新鲜）
        return 90

    def print_user_friendly_report(self):
        """打印用户友好的报告"""

        if not self.report:
            print("❌ 请先运行 generate_comprehensive_report()")
            return

        print(f"\n{'='*60}")
        print(f"📋 {self.report['report_title']}")
        print(f"📅 生成时间：{self.report['generation_time']}")
        print(f"{'='*60}")

        # 基本信息
        basic = self.report['basic_info']
        print(f"\n📊 基本信息")
        print(f"   饰品名称：{basic['item_name']}")
        print(f"   当前价格：{basic['current_price']} (数据源: {self._get_price_data_source()})")
        print(f"   价格趋势：{basic['price_trend']}")
        print(f"   市场活跃度：{basic['market_activity']}")

        # 显示关键技术指标
        if 'technical_indicators' in basic:
            print(f"   关键技术指标：")
            for indicator_name, indicator_data in basic['technical_indicators'].items():
                if indicator_name == 'RSI':
                    print(f"     • RSI: {indicator_data['value']} - {indicator_data['evaluation']}")
                elif indicator_name == 'MACD':
                    print(f"     • MACD: {indicator_data['value']} - {indicator_data['evaluation']}")
                elif indicator_name == 'EMA均线':
                    print(f"     • EMA均线: 短期{indicator_data['ema12']}/长期{indicator_data['ema26']} - {indicator_data['evaluation']}")
                elif indicator_name == '波动率':
                    print(f"     • 波动率: {indicator_data['value']} - {indicator_data['evaluation']}")
                elif indicator_name == '支撑阻力位':
                    print(f"     • 支撑阻力位分析:")
                    print(f"       - 最近支撑位: {indicator_data['nearest_support']['price']:.2f}元 (距离{indicator_data['nearest_support']['distance_percent']:.1f}%)")
                    print(f"       - 最近阻力位: {indicator_data['nearest_resistance']['price']:.2f}元 (距离{indicator_data['nearest_resistance']['distance_percent']:.1f}%)")
                    print(f"       - 短期关键位: 支撑{indicator_data['key_levels']['short_term']['support']:.2f}元 / 阻力{indicator_data['key_levels']['short_term']['resistance']:.2f}元")
                    print(f"       - 长期关键位: 支撑{indicator_data['key_levels']['long_term']['support']:.2f}元 / 阻力{indicator_data['key_levels']['long_term']['resistance']:.2f}元")
                    print(f"       - 位置评估: {indicator_data['evaluation']}")
                elif indicator_name == '布林带':
                    print(f"     • 布林带: 上轨{indicator_data['upper']}/下轨{indicator_data['lower']} - {indicator_data['evaluation']}")
                elif indicator_name == 'ATR':
                    print(f"     • ATR: {indicator_data['value']}({indicator_data['percent']}) - {indicator_data['evaluation']}")
                elif indicator_name == 'OBV':
                    print(f"     • OBV: {indicator_data['value']}({indicator_data['trend']}) - {indicator_data['evaluation']}")

        # 投资建议
        advice = self.report['investment_advice']
        print(f"\n💡 投资建议")
        print(f"   主要建议：{advice['main_advice']}")
        print(f"   置信度：{advice['confidence_level']}")
        print(f"   推荐策略：{advice['recommended_strategy']}")
        print(f"   关键原因：")
        for reason in advice['key_reasons']:
            print(f"     • {reason}")
        print(f"   行动计划：")
        for action in advice['action_plan']:
            print(f"     {action}")
        print(f"   时间建议：{advice['timeline_suggestion']}")

        # 风险评估
        risk = self.report['risk_summary']
        print(f"\n⚠️ 风险评估")
        print(f"   风险等级：{risk['overall_risk_level']}")
        print(f"   风险评分：{risk['risk_score']}")
        print(f"   评分说明：{risk['risk_score_explanation']}")
        print(f"   主要风险：")
        for factor in risk['main_risk_factors']:
            print(f"     • {factor}")
        print(f"   风险控制建议：")
        for control in risk['risk_control_advice']:
            print(f"     {control}")
        print(f"   适合投资者：{risk['suitable_investors']}")

        # 市场情况分析
        market = self.report['market_analysis']
        print(f"\n📈 市场情况分析")
        print(f"   供需情况：{market['supply_demand']}")
        print(f"   价格走势：{market['price_trend']}")
        print(f"   市场情绪：{market['market_sentiment']}")
        print(f"   异常情况：{market['market_anomalies']}")
        print(f"   整体评估：{market['overall_assessment']}")

        # 详细异常分析
        if 'anomaly_details' in self.report:
            anomaly = self.report['anomaly_details']
            if anomaly['total_anomalies'] > 0:
                print(f"\n🚨 异常分析详情")
                print(f"   异常总数：{anomaly['anomaly_summary']}")
                print(f"   异常类型：")
                for anomaly_type in anomaly['anomaly_types']:
                    print(f"     • {anomaly_type}")
                print(f"   风险影响：{anomaly['risk_impact']}")
                if anomaly['recent_anomalies']:
                    print(f"   最近异常：")
                    for recent in anomaly['recent_anomalies']:
                        print(f"     • {recent}")
                print(f"   监控建议：{anomaly['monitoring_advice']}")

        print(f"\n{'='*60}")
        print("📝 免责声明：投资有风险，入市需谨慎。本报告仅供参考，不构成投资建议。")
        print(f"{'='*60}")

    def get_report_summary(self) -> Dict:
        """获取报告摘要"""

        if not self.report:
            return {'error': '请先运行 generate_comprehensive_report()'}

        return {
            'item_name': self.report['basic_info']['item_name'],
            'current_price': self.report['basic_info']['current_price'],
            'main_advice': self.report['investment_advice']['main_advice'],
            'confidence_level': self.report['investment_advice']['confidence_level'],
            'risk_level': self.report['risk_summary']['overall_risk_level'],
            'risk_score': self.report['risk_summary']['risk_score'],
            'generation_time': self.report['generation_time']
        }
