#!/usr/bin/env python3
"""
定时任务管理页面

Streamlit页面，用于管理和监控定时任务调度器
"""

import streamlit as st
import pandas as pd
from datetime import datetime, date, timedelta
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.scheduler.back_nouse.streamlit_integration import (
    get_scheduler_status,
    stop_scheduler,
    init_scheduler_with_streamlit,
    streamlit_integration
)
from src.cs2_investment.dao.analysis_log_dao import analysis_log_dao, daily_schedule_dao
from src.cs2_investment.api.clients.api_client import api_client


def main():
    """主页面函数"""
    st.set_page_config(
        page_title="定时任务管理",
        page_icon="⏰",
        layout="wide"
    )
    
    st.title("⏰ 定时任务管理")
    st.markdown("---")
    
    # 获取调度器状态
    status = get_scheduler_status()
    
    # 显示状态概览
    show_status_overview(status)

    # 显示API服务状态
    show_api_service_status()

    # 显示控制面板
    show_control_panel(status)
    
    # 显示分析进度
    show_analysis_progress()
    
    # 显示历史记录
    show_analysis_history()


def show_status_overview(status):
    """显示状态概览"""
    st.subheader("📊 系统状态概览")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        integration_status = status.get('integration_status', 'unknown')
        if integration_status == 'running':
            st.success("🟢 集成状态: 运行中")
        elif integration_status == 'stopped':
            st.error("🔴 集成状态: 已停止")
        else:
            st.warning("🟡 集成状态: 异常")
    
    with col2:
        scheduler_status = status.get('scheduler_status', {})
        if scheduler_status and scheduler_status.get('is_running'):
            st.success("🟢 调度器: 运行中")
        else:
            st.error("🔴 调度器: 已停止")
    
    with col3:
        if scheduler_status and scheduler_status.get('regular_analysis_running'):
            st.success("🟢 常规分析: 运行中")
        else:
            st.info("⚪ 常规分析: 等待中")
    
    with col4:
        if scheduler_status and scheduler_status.get('realtime_monitor_running'):
            st.success("🟢 实时监控: 运行中")
        else:
            st.info("⚪ 实时监控: 等待中")
    
    # 显示详细配置
    if scheduler_status:
        st.markdown("### ⚙️ 配置信息")
        config_col1, config_col2, config_col3 = st.columns(3)
        
        with config_col1:
            st.metric("每日开始时间", scheduler_status.get('daily_start_time', 'N/A'))
        
        with config_col2:
            st.metric("实时监控间隔", f"{scheduler_status.get('realtime_interval_hours', 'N/A')} 小时")
        
        with config_col3:
            delay_range = scheduler_status.get('item_delay_seconds', 'N/A')
            st.metric("饰品间隔", f"{delay_range} 秒")


def show_control_panel(status):
    """显示控制面板"""
    st.subheader("🎛️ 控制面板")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🚀 启动调度器", type="primary"):
            try:
                init_scheduler_with_streamlit()
                st.success("✅ 调度器启动成功")
                st.rerun()
            except Exception as e:
                st.error(f"❌ 启动失败: {e}")
    
    with col2:
        if st.button("🛑 停止调度器", type="secondary"):
            try:
                stop_scheduler()
                st.success("✅ 调度器停止成功")
                st.rerun()
            except Exception as e:
                st.error(f"❌ 停止失败: {e}")
    
    with col3:
        if st.button("🔄 刷新状态"):
            st.rerun()


def show_analysis_progress():
    """显示分析进度"""
    st.subheader("📈 今日分析进度")
    
    today = date.today()
    
    try:
        # 获取今日进度
        progress = analysis_log_dao.get_analysis_progress('regular', today)
        daily_schedule = daily_schedule_dao.get_daily_schedule(today)
        
        if progress['total'] == 0:
            st.info("📋 今日暂无分析任务")
            return
        
        # 显示进度条
        completed_rate = progress['completed'] / progress['total'] if progress['total'] > 0 else 0
        st.progress(completed_rate, text=f"完成进度: {progress['completed']}/{progress['total']} ({completed_rate:.1%})")
        
        # 显示详细统计
        col1, col2, col3, col4, col5 = st.columns(5)
        
        with col1:
            st.metric("总任务数", progress['total'])
        
        with col2:
            st.metric("已完成", progress['completed'], delta=None)
        
        with col3:
            st.metric("运行中", progress['running'], delta=None)
        
        with col4:
            st.metric("待处理", progress['pending'], delta=None)
        
        with col5:
            st.metric("失败", progress['failed'], delta=None)
        
        # 显示每日计划信息
        if daily_schedule:
            st.markdown("### 📅 每日计划详情")
            
            plan_col1, plan_col2, plan_col3 = st.columns(3)
            
            with plan_col1:
                status_color = {
                    'pending': '🟡',
                    'running': '🟢', 
                    'completed': '✅',
                    'failed': '❌'
                }.get(daily_schedule['status'], '⚪')
                st.metric("计划状态", f"{status_color} {daily_schedule['status']}")
            
            with plan_col2:
                if daily_schedule['start_time']:
                    start_time = daily_schedule['start_time'].strftime('%H:%M:%S')
                    st.metric("开始时间", start_time)
                else:
                    st.metric("开始时间", "未开始")
            
            with plan_col3:
                if daily_schedule['end_time']:
                    end_time = daily_schedule['end_time'].strftime('%H:%M:%S')
                    st.metric("结束时间", end_time)
                else:
                    st.metric("结束时间", "进行中")
    
    except Exception as e:
        st.error(f"❌ 获取进度信息失败: {e}")


def show_analysis_history():
    """显示分析历史"""
    st.subheader("📚 分析历史")
    
    # 日期选择
    col1, col2 = st.columns(2)
    
    with col1:
        selected_date = st.date_input(
            "选择日期",
            value=date.today(),
            max_value=date.today()
        )
    
    with col2:
        analysis_type = st.selectbox(
            "分析类型",
            options=['regular', 'realtime'],
            format_func=lambda x: '常规分析' if x == 'regular' else '实时监控'
        )
    
    try:
        # 获取指定日期的分析记录
        if analysis_type == 'regular':
            # 获取常规分析记录
            progress = analysis_log_dao.get_analysis_progress(analysis_type, selected_date)
            
            if progress['total'] > 0:
                # 显示统计图表
                chart_data = pd.DataFrame({
                    '状态': ['已完成', '失败', '运行中', '待处理'],
                    '数量': [progress['completed'], progress['failed'], progress['running'], progress['pending']]
                })
                
                st.bar_chart(chart_data.set_index('状态'))
                
                # 显示详细记录表格
                st.markdown("### 📋 详细记录")
                
                # 这里可以添加更详细的记录查询和显示
                # 由于DAO方法限制，暂时显示统计信息
                record_data = {
                    '状态': ['已完成', '失败', '运行中', '待处理'],
                    '数量': [progress['completed'], progress['failed'], progress['running'], progress['pending']],
                    '百分比': [
                        f"{progress['completed']/progress['total']:.1%}",
                        f"{progress['failed']/progress['total']:.1%}",
                        f"{progress['running']/progress['total']:.1%}",
                        f"{progress['pending']/progress['total']:.1%}"
                    ]
                }
                
                df = pd.DataFrame(record_data)
                st.dataframe(df, use_container_width=True)
            
            else:
                st.info(f"📋 {selected_date} 无分析记录")
        
        else:
            st.info("📈 实时监控历史记录功能开发中...")
    
    except Exception as e:
        st.error(f"❌ 获取历史记录失败: {e}")


def show_recent_logs():
    """显示最近的日志"""
    st.subheader("📄 最近日志")
    
    try:
        # 这里可以读取日志文件并显示
        # 暂时显示占位信息
        st.info("📄 日志显示功能开发中...")
        
        # 示例日志显示
        log_placeholder = """
        2025-07-26 08:00:00 - INFO - 🚀 开始执行每日分析任务
        2025-07-26 08:00:01 - INFO - 📊 [1/100] 开始分析: AK-47 传承 (久经沙场)
        2025-07-26 08:02:15 - INFO - ✅ [1/100] 分析成功: AK-47 传承 (久经沙场) (耗时: 134.2秒)
        2025-07-26 08:02:45 - INFO - 📊 [2/100] 开始分析: 沙漠之鹰 印花集 (久经沙场)
        """
        
        st.code(log_placeholder, language="text")
    
    except Exception as e:
        st.error(f"❌ 获取日志失败: {e}")


def show_api_service_status():
    """显示API服务状态"""
    st.subheader("🔗 API服务状态")

    try:
        # 检查API服务状态
        api_status = streamlit_integration.check_api_service()

        col1, col2, col3 = st.columns(3)

        with col1:
            if api_status['api_available']:
                st.success("✅ API服务正常")
            else:
                st.error("❌ API服务不可用")

        with col2:
            st.info(f"🌐 服务地址: {api_status['api_url']}")

        with col3:
            check_time = api_status['check_time']
            st.info(f"🕐 检查时间: {check_time[:19]}")

        # 显示服务详细信息
        if api_status['api_available'] and api_status.get('service_info'):
            service_info = api_status['service_info']

            with st.expander("📋 服务详细信息"):
                info_col1, info_col2 = st.columns(2)

                with info_col1:
                    st.write("**服务名称:**", service_info.get('data', {}).get('name', 'N/A'))
                    st.write("**版本:**", service_info.get('data', {}).get('version', 'N/A'))

                with info_col2:
                    st.write("**描述:**", service_info.get('data', {}).get('description', 'N/A'))
                    features = service_info.get('data', {}).get('features', [])
                    if features:
                        st.write("**功能特性:**")
                        for feature in features:
                            st.write(f"  • {feature}")

        # 显示任务引擎统计
        if api_status['api_available']:
            try:
                engine_stats = api_client.get_engine_stats()
                if engine_stats and engine_stats.get('success'):
                    stats_data = engine_stats['data']['engine_stats']

                    with st.expander("⚙️ 任务引擎统计"):
                        stats_col1, stats_col2, stats_col3, stats_col4 = st.columns(4)

                        with stats_col1:
                            st.metric("队列大小", stats_data['queue_size'])

                        with stats_col2:
                            st.metric("运行中任务", stats_data['running_tasks_count'])

                        with stats_col3:
                            st.metric("工作线程", f"{stats_data['worker_count']}/{stats_data['max_concurrent_tasks']}")

                        with stats_col4:
                            st.metric("已处理任务", stats_data['stats']['total_processed'])

                        # 显示成功率
                        total = stats_data['stats']['total_processed']
                        successful = stats_data['stats']['successful']
                        if total > 0:
                            success_rate = successful / total * 100
                            st.metric("成功率", f"{success_rate:.1f}%")
            except Exception as e:
                st.warning(f"获取引擎统计失败: {e}")

        # 显示错误信息
        if not api_status['api_available'] and api_status.get('error'):
            st.error(f"错误详情: {api_status['error']}")

    except Exception as e:
        st.error(f"❌ 检查API服务状态失败: {e}")


if __name__ == "__main__":
    main()
