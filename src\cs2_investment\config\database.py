"""
数据库连接管理

提供数据库连接的创建、管理和配置功能。
"""

import mysql.connector
from mysql.connector import Error, pooling
from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager
from typing import Generator, Optional

# 使用统一日志系统
from ..utils.logger import get_logger
logger = get_logger(__name__)

from .settings import get_settings

# SQLAlchemy基础类
Base = declarative_base()

# 全局变量
_engine: Optional[object] = None
_session_factory: Optional[sessionmaker] = None
_connection_pool: Optional[pooling.MySQLConnectionPool] = None


def create_database_engine():
    """创建SQLAlchemy数据库引擎"""
    global _engine
    
    if _engine is None:
        settings = get_settings()
        
        _engine = create_engine(
            settings.database.url,
            poolclass=QueuePool,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,
            pool_recycle=3600,
            echo=settings.app.debug,
        )
        
        logger.info(f"数据库引擎已创建: {settings.database.host}:{settings.database.port}")
    
    return _engine


def create_session_factory():
    """创建SQLAlchemy会话工厂"""
    global _session_factory
    
    if _session_factory is None:
        engine = create_database_engine()
        _session_factory = sessionmaker(bind=engine, autoflush=False, autocommit=False)
        logger.info("数据库会话工厂已创建")
    
    return _session_factory


def create_connection_pool():
    """创建MySQL连接池"""
    global _connection_pool
    
    if _connection_pool is None:
        settings = get_settings()
        
        try:
            _connection_pool = pooling.MySQLConnectionPool(
                pool_name="cs2_investment_pool",
                pool_size=10,
                pool_reset_session=True,
                **settings.database.connection_params
            )
            logger.info("MySQL连接池已创建")
        except Error as e:
            logger.error(f"创建连接池失败: {e}")
            raise
    
    return _connection_pool


@contextmanager
def get_db_session() -> Generator:
    """获取数据库会话的上下文管理器"""
    session_factory = create_session_factory()
    session = session_factory()
    
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"数据库会话错误: {e}")
        raise
    finally:
        session.close()


@contextmanager
def get_db_connection() -> Generator:
    """获取数据库连接的上下文管理器"""
    pool = create_connection_pool()
    connection = None
    
    try:
        connection = pool.get_connection()
        yield connection
    except Error as e:
        if connection:
            connection.rollback()
        logger.error(f"数据库连接错误: {e}")
        raise
    finally:
        if connection and connection.is_connected():
            connection.close()


def test_database_connection() -> bool:
    """测试数据库连接"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            
            if result and result[0] == 1:
                logger.info("数据库连接测试成功")
                return True
            else:
                logger.error("数据库连接测试失败")
                return False
                
    except Exception as e:
        logger.error(f"数据库连接测试异常: {e}")
        return False


def create_database_if_not_exists():
    """如果数据库不存在则创建"""
    settings = get_settings()
    
    # 连接到MySQL服务器（不指定数据库）
    temp_params = settings.database.connection_params.copy()
    temp_params.pop('database', None)
    
    try:
        connection = mysql.connector.connect(**temp_params)
        cursor = connection.cursor()
        
        # 检查数据库是否存在
        cursor.execute(f"SHOW DATABASES LIKE '{settings.database.name}'")
        result = cursor.fetchone()
        
        if not result:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE {settings.database.name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            logger.info(f"数据库 {settings.database.name} 已创建")
        else:
            logger.info(f"数据库 {settings.database.name} 已存在")
        
        cursor.close()
        connection.close()
        
    except Error as e:
        logger.error(f"创建数据库失败: {e}")
        raise


def initialize_database():
    """初始化数据库"""
    try:
        # 创建数据库（如果不存在）
        create_database_if_not_exists()
        
        # 测试连接
        if test_database_connection():
            logger.info("数据库初始化成功")
            return True
        else:
            logger.error("数据库初始化失败")
            return False
            
    except Exception as e:
        logger.error(f"数据库初始化异常: {e}")
        return False


def close_database_connections():
    """关闭所有数据库连接"""
    global _engine, _session_factory, _connection_pool
    
    if _engine:
        _engine.dispose()
        _engine = None
        logger.info("SQLAlchemy引擎已关闭")
    
    if _connection_pool:
        # MySQL连接池没有直接的关闭方法，会自动管理
        _connection_pool = None
        logger.info("MySQL连接池已清理")
    
    _session_factory = None
