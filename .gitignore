# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.pyc
*.pyo
*.pyd

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
.vent
.vibedev
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# 项目特定的忽略文件
# =============================================================================

# 数据库文件
*.db
*.sqlite
*.sqlite3
data/database/
database/
*.sql.backup

# 配置文件（包含敏感信息）
config/database.yaml
config/secrets.yaml
config/api_keys.yaml
.env.local
.env.production
.env.development

# 日志文件
logs/
*.log
*.log.*
log_*.txt
steamdt_*.log
scraper_*.log
analysis_*.log

# 数据文件
data/raw/
data/processed/
data/exports/
data/backups/
data/temp/
data/cache/
*.csv
*.json.backup
*.xlsx
*.xls

# 爬虫相关
screenshots/
browser_data/
user_data/
chrome_profile/
selenium_logs/
*.har

# 分析结果和图表
charts/
reports/
analysis_results/
*.png
*.jpg
*.jpeg
*.gif
*.svg
*.pdf

# 临时文件
temp/
tmp/
*.tmp
*.temp
*.bak
*.swp
*.swo
*~

# IDE和编辑器
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
.atom/
.brackets.json
*.code-workspace
.history/
.vscode-test/

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# 缓存文件
.cache/
cache/
__pycache__/
*.pyc
*.pyo
*.pyd
.pytest_cache/
.mypy_cache/

# 备份文件
*.backup
*.bak
*.old
*_backup.*
*_old.*

# 敏感数据
api_keys.txt
passwords.txt
secrets/
credentials/
tokens/

# 大文件和二进制文件
*.zip
*.tar.gz
*.rar
*.7z
*.exe
*.msi
*.dmg

# 项目特定的数据目录
steamdt_data/
market_data/
price_history/
ranking_data/
user_data/

# Streamlit
.streamlit/secrets.toml
.streamlit/config.toml

# FastAPI
.pytest_cache/
test_*.db

# 浏览器驱动
chromedriver*
geckodriver*
msedgedriver*
operadriver*

# 机器学习模型
*.pkl
*.joblib
*.h5
*.model
models/

# 性能分析
*.prof
*.profile

data/
logs/