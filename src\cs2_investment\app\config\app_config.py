"""
Streamlit应用配置

设置页面配置、主题、导航等。
"""

import streamlit as st
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))


def setup_page_config():
    """设置页面配置"""
    st.set_page_config(
        page_title="CS2饰品投资分析系统",
        page_icon="🎮",
        layout="wide",
        initial_sidebar_state="expanded",
        menu_items={
            'Get Help': 'https://github.com/your-repo/cs2-investment',
            'Report a bug': 'https://github.com/your-repo/cs2-investment/issues',
            'About': """
            # CS2饰品投资分析系统
            
            一个专业的CS2饰品市场分析和投资决策支持系统。
            
            版本: 1.0.0
            """
        }
    )


def setup_sidebar():
    """设置侧边栏导航"""
    with st.sidebar:
        st.title("🎮 CS2投资分析")
        st.markdown("---")

        # 初始化session state中的当前页面
        if 'current_page' not in st.session_state:
            st.session_state.current_page = "首页"

        # 导航菜单 - 使用按钮形式
        st.subheader("📋 功能菜单")

        # 定义菜单分组
        menu_groups = {
            "🏠 主要功能": [
                {"name": "首页", "icon": "🏠", "key": "home", "desc": "系统概览"},
                {"name": "饰品查询", "icon": "🔍", "key": "query", "desc": "搜索饰品信息"},
                {"name": "收藏列表", "icon": "⭐", "key": "favorites", "desc": "管理收藏"},
                {"name": "持仓管理", "icon": "💼", "key": "holdings", "desc": "持仓管理"}
            ],
            "🔄 数据管理": [
                {"name": "搬砖数据", "icon": "🔄", "key": "arbitrage", "desc": "搬砖数据导入与查询"}
            ],
            "⚙️ 系统管理": [
                {"name": "配置管理", "icon": "⚙️", "key": "config", "desc": "调度器和系统配置"}
            ]
        }

        # 创建分组菜单
        for group_name, items in menu_groups.items():
            st.markdown(f"**{group_name}**")
            for item in items:
                # 检查是否为当前页面
                is_current = st.session_state.current_page == item["name"]

                if is_current:
                    st.markdown(f"""
                    <div class="menu-button menu-button-active" title="{item['desc']}">
                        {item["icon"]} {item["name"]}
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    # 创建可点击的按钮
                    if st.button(f"{item['icon']} {item['name']}",
                               key=item["key"],
                               use_container_width=True,
                               help=item["desc"]):
                        st.session_state.current_page = item["name"]
                        st.rerun()

            st.markdown("")  # 添加分组间距

        # 添加自定义CSS样式
        st.markdown("""
        <style>
        .menu-button {
            background-color: #f0f2f6;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 12px;
            margin: 4px 0;
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            width: 100%;
            color: #262730;
            text-decoration: none;
        }
        .menu-button:hover {
            background-color: #e8eaf6;
            border-color: #FF6B6B;
            transform: translateX(2px);
        }
        .menu-button-active {
            background-color: #FF6B6B;
            color: white;
            border-color: #FF6B6B;
            font-weight: bold;
        }
        .menu-button-active:hover {
            background-color: #ff5252;
        }
        </style>
        """, unsafe_allow_html=True)

        page = st.session_state.current_page
        st.markdown("---")
        
        # 显示当前页面信息
        if st.session_state.current_page != "首页":
            st.info(f"📍 当前页面: {st.session_state.current_page}")

        st.markdown("---")

        # 快速统计信息
        st.subheader("📊 快速统计")

        # 这里可以添加一些实时统计信息
        col1, col2 = st.columns(2)
        with col1:
            st.metric("今日更新", "1,234", "↗️ 5%")
        with col2:
            st.metric("热门饰品", "89", "🔥")

        st.markdown("---")

        # 定时任务状态
        st.subheader("⚙️ 系统状态")
        try:
            from src.cs2_investment.app.services.app_scheduler import show_scheduler_status
            show_scheduler_status()
        except Exception as e:
            st.error("🔴 定时更新: 加载失败")

        st.markdown("---")

        # 快捷操作
        st.subheader("🚀 快捷操作")

        col1, col2 = st.columns(2)
        with col1:
            if st.button("🔄 刷新数据", use_container_width=True):
                st.success("数据刷新中...")
                st.rerun()

        with col2:
            if st.button("📊 查看报告", use_container_width=True):
                st.session_state.current_page = "投资筛选"
                st.rerun()

        st.markdown("---")

        # 系统信息
        with st.expander("ℹ️ 系统信息"):
            st.write("**数据库**: MySQL 8.0")
            st.write("**更新频率**: 每2小时")
            st.write("**数据源**: SteamDT")
            st.write("**版本**: v1.0.0")
            st.write("**架构**: 微服务架构")
    
    return page


def get_theme_config():
    """获取主题配置"""
    return {
        'primaryColor': '#FF6B6B',
        'backgroundColor': '#FFFFFF',
        'secondaryBackgroundColor': '#F0F2F6',
        'textColor': '#262730',
        'font': 'sans serif'
    }
